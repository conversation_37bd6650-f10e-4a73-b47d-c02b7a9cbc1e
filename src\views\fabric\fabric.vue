<template>
  <div class="fabric">
    <Instance v-if="currentTab==='instance'" />
    <router-view v-else/>
  </div>
</template>

<script>
import Instance from './instance/index.vue'
export default {
  name: 'fabric',
  components: {
    Instance
  },
  data () {
    return {
      excludeArr: []
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
