<template>
  <div class="page">
    <div class="content">
      <div>
        <span class="back"  @click="goback">返回</span>
        <span>{{detail.ChannelName}}</span>
      </div>
      <div class="title">
        <div class="left">
          <!-- <span class="infotext" @click="goDetail">通道信息</span>
          <img :src="arrowIcon" class="icon">
          <span class="infotext"  @click="goChannelMgr">{{detail.ChannelName}}通道详情</span> -->
          <img :src="infoIcon" class="infoIcon">
          <span class="infotext">节点信息</span>
        </div>
      </div>
      <!-- 通道成员信息 -->
      <SpaceLayout top="0" paddingX="0" paddingY="0">
        <div slot="padding">
          <div class="table-wrapper">
            <el-row class="nav-box">
              <el-col :span="5"><div class="">节点名称</div></el-col>
              <el-col :span="6"><div class="">组织名称</div></el-col>
              <el-col :span="10"><div class="">域名</div></el-col>
              <el-col :span="3"><div class="">版本</div></el-col>
            </el-row>
            <div class="none" v-if="memberArray.length == 0">
              <i class="el-icon-loading" v-if="paddingText == '数据请求中...'"></i>
              <!-- <svg-icon icon-class="table-empty" v-else/> -->
              {{paddingText}}
            </div>
            <div class="nan-item" v-for="(item,memberindex) in listData" :key="memberindex">
              <el-row  class="nav-box">
                <el-col :span="5">
                    <div class="">
                      <span>
                        <el-popover trigger="hover" placement="top">
                          <p>{{item.Peer}}</p>
                          <div slot="reference" class="name-wrapper">
                            <span class="text" slot="reference">{{getName(item.Peer)}}</span>
                          </div>
                        </el-popover>
                    </span>
                    </div>
                  </el-col>
                <el-col :span="6"><div class=""><span>{{item.Name}}</span></div></el-col>
                <el-col :span="10"><div class=""><span>{{item.Name +'.'+item.Peer+'.fabric.com'}}</span></div></el-col>
                <el-col :span="3"><div class="">{{channelObject.FabricVersion}}</div></el-col>
              </el-row>
            </div>
          </div>
          <Page
          :total="total"
          :current.sync="pageIndex"
          @on-change="handleCurrentChange"
          :page-size="pageSize"
          :page-size-opts="[10,20,40,60,100]"
          show-total show-elevator show-sizer
          @on-page-size-change="handleSizeChange"
          style="text-align:right;"/>
          <!-- <pagination
              @toHandleSizeChange="handleSizeChange"
              @toHandleCurrentChange="handleCurrentChange"
              @toJumpFirstPage="jumpFirstPage"
              @toJumpLastPage="jumpLastPage"
              :fTotal="total"
              :fBtnStartDisabled="btnStartDisabled"
              :fBtnEndDisabled="btnEndDisabled"
              :fPageIndex="pageIndex"
              :fZys="zys"
              >
          </pagination> -->
        </div>
      </SpaceLayout>
    </div>
    <!-- <countDown v-if="isShowIcon" :state="countState" :countTime="countTime" :text="countText" @getCountDown="getCountDown"></countDown> -->
  </div>
</template>

<script>
import{getChannelPeerList,}from '@/api/baascore/channelMgr'
import SpaceLayout from '@/components/SpaceLayout'
import {forbidden} from "@/utils/index.js";
import { mapGetters } from 'vuex'
export default {
  mixins: [forbidden],
  props: {
    detail:{
      type: Object,
      default() {
          return {}
      }
    }
  },
  components: {
    SpaceLayout
  },
  data() {
    return {
      countState:'',
      countTime:2,
      countText:'',
      isShowIcon:false,
      arrowIcon:require('@/assets/chainManage_images/overview/arrow.png'),
      infoIcon: require("@/assets/chainManage_images/overview/infoIcon.png"),
      channelObject:{},//
      memberArray:[],//成员信息
      total:0,
      btnStartDisabled: false, //用来判断首页尾页按钮是否禁用
      btnEndDisabled: false, //用来判断首页尾页按钮是否禁用
      zys: 0,
      pageIndex: 1,
      pageSize:10,
      paddingText:'数据请求中...',
      listData:[],
      // detail:{
      //   ChannelName: "",
      //   ServiceId: "",
      // },
    }
  },
  computed: {
    ...mapGetters(['chainItem']),
    getName(name) {
      return function(name) {
        if(name.indexOf('-') != -1) {
          var strList = name.split('-')
          name = strList[0] +'-' + strList[1]
          return name
        }else {
          return name
        }
      }
    },
  },
  watch : {
    // chainItem: {
    //   handler(newvalue, oldvalue) {
    //     if (newvalue.Id != oldvalue.Id) {
    //       this.$router.push({
    //         path:'/chainManage/channelMgr'
    //       })
    //     }
    //   },
    //   deep: true,
    // },
  },
  mounted () {
    //this.detail = JSON.parse(sessionStorage.getItem('detail'))
    document.body.scrollTop = 0;
    document.documentElement.scrollTop = 0;
    this.getChannelPeerList()
  },
  methods: {
    getCountDown(type) {
      this.isShowIcon = false
    },
    getListData() {
      // this.zys = Math.ceil(this.total/ this.pageSize); //获取总页数
      // this.pageIndex > this.zys ? this.pageIndex = this.zys : '';
      //  // 调用混入方法判断首页尾页按钮禁用的方法
      // this.forbidden(this.zys, this.pageIndex);
      this.listData = this.memberArray.slice((this.pageIndex -1) * this.pageSize, this.pageSize * this.pageIndex)
    },
    goback() {
      this.$emit('getisShowMember',false)
    },
    goChannelMgr() {
      this.$emit('getisShowMember',false)
    },
    goDetail() {
      this.$emit('getIsShowDetial',false)
    },
    getListData() {
      this.listData = this.memberArray.slice((this.pageIndex -1) * this.pageSize, this.pageSize * this.pageIndex)
    },
    //通道里的节点列表
    getChannelPeerList() {
      var ServiceId = this.detail.ServiceId
      var ChannelName = this.detail.ChannelName
      var params = {
        ServiceId,
        ChannelName
      }
      getChannelPeerList(params).then(res =>{
        if(res.code == 200 ) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if(res.data && res.data.Orgs) {
            this.channelObject = res.data
            var data = res.data.Orgs
            var arr = []
            var obj = {}
            data.forEach(item =>{
              if(item.Name && item.Peers && item.Peers.length > 0) {
                item.Peers.forEach((citem,cindex) =>{
                  obj = {
                    Name:item.Name,
                    Peer : item.Peers[cindex]
                  }
                  arr.push(obj)
                })
              }
            })
            this.memberArray = arr
            this.getListData()
            this.total = this.memberArray.length
            if(this.total == 0) {
              this.paddingText = '暂无数据'
            }
            this.zys = Math.ceil(this.total / this.pageSize); //获取总页数
          } else {
            this.paddingText = '暂无数据'
          }
        }
        if(res.code != 200) {
          this.paddingText = '暂无数据'
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize  = val
      this.getListData()
    },
    handleCurrentChange(val) {
      this.pageIndex = val
      this.getListData()
    },
    // 首页按钮
    jumpFirstPage(val) {
      this.pageIndex = val;
      this.handleCurrentChange(val);
    },
    // 尾页按钮
    jumpLastPage(val) {
      this.pageIndex = val;
      this.handleCurrentChange(this.pageIndex);
    },
  }
}
</script>

<style rel="stylesheet/less" lang="less" scoped>
@import "../../../../styles/common/modal.less";
.page {
  width: 100%;
  height: 100%;
  //margin-top:-20px;
  .content {
    //padding-bottom: 50px;
    .title {
      margin: 20px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .infotext {
        // font-size: 22px;
        font-size: 14px;
        color: #333333;
        vertical-align: middle;
        cursor: pointer;
      }
      .icon {
        width: 22px;
        height: 22px;
        margin: 0 24px;
      }
      .name {
        // font-size: 20px;
        font-size: 14px;
        font-weight: bold;
        color: #333333;
        cursor: default;
      }
      .left {
        display: flex;
        align-items: center;
        .infoIcon {
          width: 3px;
          height: 14px;
          margin-right:5px;
          vertical-align: middle;
        }
        .infotext {
          // font-size: 22px;
          font-size: 14px;
          color: #333333;
          vertical-align: middle;
        }
      }

    }
    .none {
      text-align: center;
      color: #666;
      // font-size: 18px;
      font-size: 14px;
    }
    .nav-box{
      color: #999999;
      // font-size: 18px;
      font-size: 14px;
      box-sizing: border-box;
      /*margin-top:20px;*/
    }
    .nav-box /deep/ .el-col{
      text-align: center;
      color: #999;
      // font-size: 18px;
      font-size: 14px;
      padding:0px 0;
      line-height: 36px;
    }
    .nav-box .el-col div span{
      /*cursor: default;*/
    }
    .nan-item .nav-box:hover{
      -moz-box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6; -webkit-box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6; box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6;
    }
    .nan-item .nav-box{
        // font-size: 16px;
        font-size: 14px;
        box-sizing: border-box;
        background: #fff;
        display: flex;
        align-items: center;
    }
    .nan-item .nav-box /deep/ .el-col {
      color: #666666;
    }
    .nan-item .nav-box .el-col div span.call {
      display: inline-block;
      width: 102px;
      height: 36px;
      border-radius: 4px;
      text-align: center;
      line-height: 36px;
      background: #00ADA2;
      // font-size: 16px;
      font-size: 14px;
      color: #FFFFFF;
    }
    .nan-item .nav-box .el-col div span.text {
      display: block;
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
      /*padding: 0 40px;*/
    }
    .nan-item .nav-box .el-col div span.textLeft {
      /*padding-left: 36px;*/
    }
    .pagination {
      /*margin-top: 16px;*/
      // display: flex;
      // justify-content: flex-end;
      // align-items: center;
    }
  }
  .ivu-page{
    margin-top:10px;
  }

}
</style>
