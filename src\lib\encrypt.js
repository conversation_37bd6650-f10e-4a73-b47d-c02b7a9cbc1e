import { JSEncrypt } from 'jsencrypt'
import { localRead } from '@/lib/util'
//  加密
export const encryptedData = (data, publicKey) => {

  // 新建JSEncrypt对象
  publicKey = publicKey ? publicKey : sessionStorage.getItem('RSA_PUBLIC_KEY')
  publicKey = `-----BEGIN PUBLIC KEY-----${publicKey}-----END PUBLIC KEY-----`

  let encryptor = new JSEncrypt();
  // 设置公钥
  encryptor.setPublicKey(publicKey);
  // 加密数据
  return encryptor.encrypt(data);
}
// 解密
export const decryptData = (data, privateKey) => {
  privateKey = privateKey ? privateKey : sessionStorage.getItem('RSA_PRIVATE_KEY')
  privateKey = `-----BEGIN RSA PRIVATE KEY-----${privateKey}-----END RSA PRIVATE KEY-----`
  // 新建JSEncrypt对象
  let decrypt = new JSEncrypt();
  // 设置私钥
  decrypt.setPrivateKey(privateKey);
  // 解密数据
  return decrypt.decrypt(data);
}