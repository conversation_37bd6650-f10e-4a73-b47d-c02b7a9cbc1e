<template>
  <div class="multilink-details">
    <div style="width：auto;margin-left: 10px;">
      <Card style="width: auto;">
        <!-- <p class="info-title" style="margin:10px 0 15px 10px;"><span class="bs">基本信息</span></p> -->
        <div class="info-title addflex">
          <div>
            <div class="bs"></div>
            <span>基本信息</span>
          </div>
          <div class="btns">
            <Button ghost type="primary" @click="clickHyperion" :disabled="hasEditPermission">Hyperion 配置</Button>
            <Button ghost type="primary" @click="clickElas" :disabled="hasEditPermission">Elasticsearch 配置</Button>
            <!-- <Button ghost type="primary" @click="clickSuite">开发套件配置</Button> -->
          </div>
        </div>
        <ul>
          <li>链名称：{{ arrDetails.chainName }}</li>
          <li>链版本：{{ arrDetails.engineType+' '+ arrDetails.chainVersion }}</li>
          <li>开发架构链ID：{{ arrDetails.eosChainId }}</li>
          <li v-show="ownership=='管控链'">链版本：{{ arrDetails.isGm==='1'?'国密版':'企业版' }}</li>
          <li>合约升级：{{ arrDetails.isUpgradeContract==='1'?'是':'否' }}</li>
          <li>所有权：{{ arrDetails.ownership }}</li>
          <li>主子链：{{ arrDetails.chainSource==='SUB_CHAIN'?'省子链':'集团主链' }}</li>
          <!-- <div v-if="chaindetail.chainSource==='MAIN_CHAIN'">
          <li >链归属公司：{{ chaindetail.companyName }}</li>
          </div> -->
          <div v-if="arrDetails.chainSource==='SUB_CHAIN'">
            <!-- <li v-if="chaindetail.companyName===null">链归属公司：</li> -->
            <li>链归属公司：{{ arrDetails.companyName==null?'': arrDetails.companyName}}</li>
          </div>

          <!-- <li v-show="chaindetail.chainSource==='SUB_CHAIN'">链归属公司：{{ chaindetail.companyName }}</li> -->
          <li>审核列表：{{ arrDetails.auditList?arrDetails.auditList.map(val => val.auditValue).join(','):'' }}</li>
          <li>状态：<span>{{ arrDetails.status }}</span></li>
          <li style="word-break:break-all;white-space: pre-wrap;">描述：{{ arrDetails.chainBrief }}</li>
        </ul>
      </Card>
    </div>

    <Card style="margin:20px 5px 10px 10px;">
      <div class="node" style="width：auto;margin:10px 5px 0px 10px;">
        <div class="info-title initialization">
          <div>
            <span class="bs"></span><span>节点</span>
          </div>
          <div>
            <Button ghost type="success" @click="add" icon="md-add" style="margin-top:-5px;" :disabled="hasEditPermission">新增节点</Button>
            <Button ghost type="success" @click="initialization" :disabled="disabledinitial" style="margin-top:-5px;margin-left: 10px;" v-show="ownership=='管控链'&& arrDetails.isInit!=='1'">初始化</Button>
          </div>
        </div>
        <Table :columns="columns" :data="tableData" style="margin-top:20px;">
          <template slot-scope="{ row, index }" slot="action">
            <!-- <Button v-show="row.statusKey!=='ABANDON'" size="small" @click="editDetails(index)" style="margin-right:5px;" :disabled="row.ipDesensitizeOpen==='1'" :style="row.ipDesensitizeOpen==='1'?'color:#c5c8ce;border:1px solid #c5c8ce;':'color:#3D73EF;border:1px solid #3D73EF;'">编辑</Button> -->
            <Button v-show="row.statusKey!=='ABANDON'" size="small" :disabled="hasEditPermission" @click="editDetails(index,row)" :style="buttonStyle">编辑</Button>

            <Poptip confirm title="确认删除吗?" @on-ok="deletedData(row)" cancel-text="取消" transfer>
              <Button size="small" :style="row.statusKey=='ABANDON'&&row.nodeType!=='出块节点'&&ownership=='管控链'?'border: 1px solid #3D73EF;color: #3D73EF':'display: none;'">删除</Button>
            </Poptip>

            <!-- <Button size="small" :style="row.nodeStatus=='ENABLE'?'':'color:#c5c8ce;border:1px solid #c5c8ce;margin-left:5px;'" @click="chongqi(row)">重启</Button> -->
            <!-- <Button size="small" v-if="row.nodeStatus==='ENABLE'&&ownership=='管控链'?" style="'border: 1px solid #3D73EF;color: #3D73EF;margin-left:5px;'" @click="restartNode(row)">重启</Button> -->
            <Button size="small" v-if="ownership=='管控链'?row.nodeStatus!=='ENABLE'?true:false:false" style="border: 1px solid #3D73EF;color: #3D73EF;margin-left:5px;" @click="restartNode(row)">重启</Button>
          </template>

        </Table>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[5,10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;margin:10px 0;" />
      </div>
    </Card>
    <div v-show="ownership=='纳管链'">
      <Card style="margin:20px 5px 30px 10px;" v-if="isUpgradeContract=='0'">
        <div class="account" style="margin:10px 5px 30px 10px;">
          <div class="info-title">
            <span class="bs"></span><span>链账户</span>
            <Button class="btn" type="success" ghost @click="addAccount" icon="md-add" :disabled="hasEditPermission" style="float:right;margin-top:-5px;">新增管理链账户</Button>
          </div>
          <edit-table-mul :columns="accountColums" v-model="accountTableData" :key="transferKey" style="margin-top:20px;"></edit-table-mul>
        </div>
      </Card>
    </div>
    <Card style="margin:20px 5px 30px 10px;">
      <Tenant :tenantVisibility="arrDetails.tenantVisibility" :chainId="arrDetails.chainId" :chainName="arrDetails.chainName"></Tenant>
    </Card>
    <Modal :draggable="true" v-model="modal" width="580" :title="formItem.alertTitle" :z-index="1000" sticky :mask-closable="false" @on-cancel="cancel('formItem')">
      <Form ref="formItem" :rules="formItemRule" :model="formItem" :label-width="130">
        <FormItem label="节点名称：" prop="nodeNameg" v-if="ownership=='管控链'">
          <Input placeholder="请输入节点名称" style="width:400px;vertical-align:baseline;" v-model="formItem.nodeNameg" :disabled="formItem.alertTitle==='修改链节点信息'?true:false" />
        </FormItem>
        <FormItem label="节点名称：" prop="nodeNamen" v-else>
          <Input placeholder="请输入节点名称" style="width:400px;vertical-align:baseline;" v-model="formItem.nodeNamen" />
        </FormItem>
        <FormItem label="节点类型：" prop="nodeType">
          <Select v-model="formItem.nodeType" placeholder="请选择节点类型" style="width:200px;" :disabled="ownership=='管控链'&&formItem.alertTitle==='修改链节点信息'?true:false">
            <Option v-for="item in nodeTypeList" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem label="主机IP：" prop="nodeAddressg" v-if="ownership=='管控链'">
          <Select v-model="formItem.nodeAddressg" placeholder="请选择主机IP" style="width:200px;" :disabled="formItem.alertTitle==='修改链节点信息'?true:false">
            <Option v-for="item in iplist" :value="item.ip" :key="item.id">{{ item.ip }}</Option>
          </Select>
        </FormItem>
        <FormItem label="IP：" prop="nodeAddressn" v-else>
          <Input placeholder="请输入IP地址" style="width:400px;vertical-align:baseline;" v-model="formItem.nodeAddressn" />
        </FormItem>

        <div>
          <FormItem label="API端口：" prop="nodeApiPort">
            <Input placeholder="请输入API端口地址" maxlength="5" show-word-limit style="width:400px;vertical-align:baseline;" v-model="formItem.nodeApiPort" />
          </FormItem>
          <FormItem label="P2P端口：">
            <Input placeholder="请输入P2P端口地址" maxlength="5" show-word-limit style="width:400px;vertical-align:baseline;" v-model="formItem.nodeP2pPort" />
          </FormItem>
        </div>
        <!-- <div v-else>
          <FormItem label="API端口：" prop="nodeApiPort">
            <Input placeholder="请输入API端口地址" style="width:400px;vertical-align:baseline;" show-word-limit maxlength="5" v-model="formItem.nodeApiPort" />
          </FormItem>
          <FormItem label="P2P端口：">
            <Input placeholder="请输入P2P端口地址" style="width:400px;vertical-align:baseline;" show-word-limit maxlength="5" v-model="formItem.nodeP2pPort" />
          </FormItem>
        </div> -->

        <FormItem label="地理位置：" class="mandatory">
          <Select v-model="formItem.provinceCode" filterable clearable style="width:160px;" placeholder="请选择所在省" @on-open-change="selectmethods" @on-select="itemprovinces" @on-clear="clearcode">
            <Option v-for="item in cityList" :value="item.code" :key="item.name">{{ item.name }}</Option>
          </Select>
          <Select v-model="formItem.cityCode" filterable clearable style="width:160px;" placeholder="请选择所在市" :disabled="disabled" @on-select="itemcity" @on-clear="clearCity">
            <Option v-for="item in cityList1" :value="item.code" :key="item.name">{{ item.name }}</Option>
          </Select>
          <!-- <Input type="textarea" style="width:400px;vertical-align:baseline;"  v-model="formItem.location" :maxlength="128" show-word-limit :autosize="{minRows: 3,maxRows: 5}" placeholder="请输入位置信息"/> -->
        </FormItem>
        <FormItem label="磁盘挂载目录：" prop="diskdir">
          <Input placeholder="请输入磁盘挂载目录" style="width:400px;vertical-align:baseline;" v-model="formItem.diskdir" />
        </FormItem>
        <FormItem v-if="formItem.status&&ownership=='管控链'" label="启用状态：" prop="status">
          <Select v-model="formItem.status" placeholder="选择运行状态" style="width:200px;">
            <Option v-for="item in statusListg" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem v-else-if="formItem.status&&ownership=='纳管链'" label="启用状态：" prop="status">
          <Select v-model="formItem.status" placeholder="选择运行状态" style="width:200px;">
            <Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <!-- <FormItem label="运行状态：" v-if="formItem.statusYun" prop="statusYun">
          <p>{{formItem.statusYun=== 'ENABLE' ? '正常' : '异常'}}</p>
        </FormItem> -->
      </Form>
      <div slot="footer">
        <Button type="text" @click="cancel('formItem')">取消</Button>
        <Button type="primary" @click="ok('formItem')" :loading="loading" v-if="formItem.status!=='弃用'">
          <span v-if="!loading">确定</span>
          <span v-else>Loading...</span>
        </Button>
        <Button type="primary" @click="actionAbandon" :loading="loading" v-else>
          <span v-if="!loading">确定</span>
          <span v-else>Loading...</span>
        </Button>
      </div>
    </Modal>
    <Modal :draggable="true" v-model="accountModal" width="700" :title="accountItem.alertTitle" :z-index="1000" sticky :mask-closable="false">
      <Form ref="accountItem" :rules="accountItemRule" :model="accountItem" :label-width="150">
        <FormItem label="管理链账户名称：" prop="manageAccountName">
          <Tooltip max-width="200" content="5-12位,仅包含{a-z,1-5,.},且.不能在最前和最后" style="margin-left: -18px;">
            <Icon type="md-help-circle" style="font-size:16px;" />
          </Tooltip>
          <Input placeholder="管理链账户名称" style="width:250px;vertical-align:baseline;" v-model="accountItem.manageAccountName" :disabled="disabledInput" />
        </FormItem>
        <FormItem v-if="ownerKeyShow" label="owner权限私钥：" prop="ownerPrivateKey">
          <Input type="password" placeholder="owner权限私钥" style="width:500px;vertical-align:baseline;" v-model="accountItem.ownerPrivateKey">
          <i class="ri-eye-close-line" slot="suffix" @click="handleOwnerKey"></i>
          </Input>
        </FormItem>
        <FormItem v-else label="owner权限私钥：" prop="ownerPrivateKey">
          <Input type="text" placeholder="owner权限私钥" style="width:500px;vertical-align:baseline;" v-model="accountItem.ownerPrivateKey">
          <i class="ri-eye-line" slot="suffix" @click="handleOwnerKey"></i>
          </Input>
        </FormItem>
        <FormItem v-if="activeKeyShow" label="active权限私钥：" prop="activePrivateKey">
          <Input type="password" placeholder="active权限私钥" style="width:500px;vertical-align:baseline;" v-model="accountItem.activePrivateKey">
          <i class="ri-eye-close-line" slot="suffix" @click="handleActiveKey"></i>
          </Input>
        </FormItem>
        <FormItem v-else label="active权限私钥：" prop="activePrivateKey">
          <Input type="text" placeholder="active权限私钥" style="width:500px;vertical-align:baseline;" v-model="accountItem.activePrivateKey">
          <i class="ri-eye-line" slot="suffix" @click="handleActiveKey"></i>
          </Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="cancelAccount">取消</Button>
        <Button type="primary" @click="okAccount">确定</Button>
      </div>
    </Modal>
    <!-- 初始化弹框 -->
    <Modal v-model="creatmodal" title="初始化操作确认" @on-ok="creatok">
      <p style="margin: 3%;">每个区块链网络仅可执行一次初始化操作，请确认已完成所有出块节点的创建，并继续执行初始化操作。</p>
    </Modal>
    <!-- 弃用弹框 -->
    <Modal v-model="abandonmodal" title="操作确认" @on-ok="abandonok">
      <p style="margin: 3%;text-align: center;">弃用后该节点无法恢复，请确认是否弃用？</p>
    </Modal>
    <hyperion-modal ref="hyperionModal" :chainId="`${arrDetails.chainId}`" />
    <elasticsearch-modal ref="elasticsearchModal" :chainId="`${arrDetails.chainId}`" />

    <!-- <suite-modal ref="suiteModal" :chainId="`${arrDetails.chainId}`" /> -->
  </div>
</template>

<script>
import HyperionModal from './hyperionModal.vue'
import ElasticsearchModal from './elasticsearchModal.vue'
// import suiteModal from './suiteModal.vue'
import { getMultiLinkDetails, reviseMultiChainNode, addMultiChainNode, addManageAccount, deleteManageAccount, getNodeList, manageAccountInfo } from '@/api/data'
import { getserchList, getNodeDetails, getconfig } from '@/api/contract'
import EditTableMul from '_c/edit-table-mul'
import Tenant from './tenant'
import { searchKey, getKey, changeKey } from './tool'
import { statusList, nodeTypeList, statusListg } from './typeList'
import { isNumber, isAccount, isNodeName } from '../../../lib/check'
import { initnetWork, nodelablegetList, addCmeosNode, createNetwork, updateNetwork, restartCmeosNode, deleteCmeosNode } from '@/api/arrange'
import { encryptedData, decryptData } from '@/lib/encrypt'
import { localRead } from '@/lib/util'
export default {
  name: 'multilink_details',
  components: {
    EditTableMul,
    Tenant,
    HyperionModal,
    ElasticsearchModal
    // suiteModal
  },
  data () {
    const validateValue = (rule, value, callback) => {
      if (!isNumber(value)) {
        callback(new Error('请输入数字'))
      } else {
        callback()
      }
    }
    const validateAccount = (rule, value, callback) => {
      if (!isAccount(value)) {
        callback(new Error('仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字'))
      } else {
        callback()
      }
    }
    const validateNodename = (rule, value, callback) => {
      var reg = /^([a-z])$/
      if (!reg.test(value.slice(0, 1))) {
        callback(new Error('请输入以小写字母开头的节点名称'))
      } else if (!isNodeName(value)) {
        callback(new Error('仅包含:{a-z,1-9,-},且-不能在开头或结尾,长度限制20'))
      } else {
        callback()
      }
    }
    // const validateIpvalue = (rule, value, callback) => {
    //   if (!isIpNumber(value)) {
    //     callback(new Error('请输入正确的IP'))
    //   } else {
    //     callback()
    //   }
    // }
    const validateName = (rule, value, callback) => {
      let reg = /^[/]/
      if (!reg.test(value.slice(0, 1))) {
        callback(new Error('请输入以“/xx/xx/xx....”的格式路径'))
      } else {
        callback()
      }
    }
    return {
      abandonmodal: false,
      statusListg: [],
      creatmodal: false,
      initia: false,
      ownership: this.$route.params.ownership ? this.$route.params.ownership : localStorage.getItem('ownership'),
      disabledinitial: true, // 初始化禁用
      chaindetail: {},
      disabled: true,
      loading: false,
      tablePageParam: {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      },
      disabledInput: false,
      modal: false,
      accountModal: false,
      ownerKeyShow: true,
      activeKeyShow: true,
      transferKey: 0,
      eosChainId: this.$route.params.eosChainId ? this.$route.params.eosChainId : '',
      chainId: this.$route.params.chainId ? this.$route.params.chainId : '',
      arrDetails: {},
      tableData: [],
      accountTableData: [],
      reviseTableData: [],
      formItem: {
        alertTitle: '新增节点',
        chainId: '',
        nodeId: '',
        nodeNameg: '',
        nodeNamen: '',
        nodeAddressg: '',
        nodeAddressn: '',
        nodeApiPort: '',
        nodeType: '',
        status: '',
        nodeP2pPort: '',
        location: '',
        nodeTypeKey: '',
        statusKey: '',
        provinceCode: null, // 省code
        cityCode: null, // 市code
        provinceName: '', // 省名称
        cityName: '', // 市名称
        diskdir: '', // 磁盘目录
        statusYun: '',
        oldownerPrivateKey: '',
        oldactivePrivateKey: ''
      },
      accountItem: {
        alertTitle: '新增管理链账户',
        chainId: '',
        manageAccountName: '',
        ownerPrivateKey: '',
        activePrivateKey: ''
      },
      manageAccountName: '',
      formItemRule: {
        nodeNameg: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateNodename }
        ],
        nodeNamen: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nodeAddressg: [
          { required: true, message: '请选择一项', trigger: 'change' }
        ],
        nodeAddressn: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nodeApiPort: [{ required: true, message: '不能为空,只能为数字', trigger: 'blur' },
        { required: true, trigger: 'blur', validator: validateValue }
        ],
        nodeP2pPort: [{ required: true, trigger: 'blur', validator: validateValue }],
        nodeType: [{ required: true, message: '请选择一项', trigger: 'change' }],
        status: [{ required: true, message: '请选择一项', trigger: 'blur' }],
        diskdir: [{ required: true, message: '请输入磁盘挂载目录', trigger: 'blur' },
        { max: 200, message: '请输入200字符以内名称', trigger: 'blur' },
        { required: true, trigger: 'blur', validator: validateName }]
      },
      accountItemRule: {
        manageAccountName: [
          { required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
          { max: 12, message: '不能多于12位', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateAccount }
        ],
        ownerPrivateKey: [{ required: true, message: '不能为空', trigger: 'blur' },
          // { type: 'string', pattern: /^[a-zA-Z0-9]{51}$/, message: '格式有误,长度必须为51位,a-zA-Z0-9', trigger: 'blur' }
        ],
        activePrivateKey: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      statusList: [],
      nodeTypeList: [],
      columns: [
        // { key: 'nodeId', title: '链节点id', sortable: true },
        { key: 'nodeName', title: '节点名称' },
        { key: 'nodeType', title: '节点类型' },
        { key: 'nodeAddress', title: 'IP' },
        { key: 'nodeApiPort', title: 'API端口' },
        { key: 'location', title: '地址', tooltip: true },
        {
          key: 'status',
          title: '启用状态',
          filters: [
            {
              label: '启用',
              value: 'ENABLE'
            },
            {
              label: '已下线',
              value: 'DISABLE'
            }
          ],
          filterMultiple: false,
          filterRemote: (value, row) => {
            console.log(value[0]);
            this.statusFilter = value[0] || '';
            this.getTableData(this.arrDetails.chainId);
          },
          render: (h, params) => {
            const color = params.row.statusKey === 'ENABLE' ? '#15AD31' : '#C7C7C7'
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, params.row.status)
          }
        },
        {
          key: 'nodeStatus',
          title: '运行状态',
          filters: [
            {
              label: '正常',
              value: 'ENABLE'
            },
            {
              label: '异常',
              value: 'DISABLE'
            }
          ],
          filterMultiple: false,
          filterRemote: (value, row) => {
            console.log(value[0]);
            this.statusNode = value[0] || '';
            this.getTableData(this.arrDetails.chainId);
          },
          render: (h, params) => {
            const color = params.row.nodeStatus === 'ENABLE' ? '#15AD31' : '#FF4D4F'
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, params.row.nodeStatus === 'ENABLE' ? '正常' : '异常')
          }
        },
        {
          slot: 'action',
          title: '操作',
          minWidth: 120
          // render: (h, params) => {
          //   return h('div', [
          //     h('Button', {
          //       props: { type: 'text', size: 'small', disabled: params.row.ipDesensitizeOpen === '1' },
          //       style: { marginRight: '8px', color: params.row.ipDesensitizeOpen === '1' ? '#c5c8ce' : '#3D73EF', border: params.row.ipDesensitizeOpen === '1' ? '1px solid #c5c8ce' : '1px solid #3D73EF' },
          //       on: {
          //         click: () => {
          //           this.editDetails(params.index)
          //         }
          //       }
          //     }, '编辑'),
          //     h(
          //       'Poptip',
          //       {
          //         props: {
          //           transfer: true,
          //           placement: 'top-end',
          //           confirm: true,
          //           title: '确认删除吗?',
          //           'ok-text': '确认',
          //           'cancel-text': '取消'
          //         },
          //         on: {
          //           'on-ok': () => {
          //             this.deletedData(params)
          //           }
          //         }
          //       },
          //       [
          //         h(
          //           'Button', {
          //             props: { type: 'text', size: 'small' },
          //             style: { marginRight: '8px',
          //               color: '#3D73EF',
          //               border: '1px solid #3D73EF',
          //               display: params.row.nodeType === '出块节点' ? 'none' : 'block'
          //             }
          //           }, '删除'
          //         )
          //       ]
          //     ),
          //     h('Button', {
          //       props: { type: 'text', size: 'small' },
          //       style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
          //       on: {
          //         click: () => {
          //           this.chongqi(params)
          //         }
          //       }
          //     }, '重启')
          //   ]

          //   )
          // }
        }
      ],
      accountColums: [
        // { key: 'chainId', title: '目标链Id' },
        { key: 'accountName', title: '管理链账户名称' },
        { key: 'ownerPrivateKey', title: 'owner权限私钥' },
        { key: 'activePrivateKey', title: 'active权限私钥' },
        {
          key: 'action',
          title: '操作',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                style: this.buttonStyle,
                on: {
                  click: () => {
                    this.editAccount(params.index)
                  }
                }
              }, '编辑'),
              h('Poptip', {
                props: {
                  confirm: true,
                  transfer: true,
                  title: '确定删除管理链账户[' + this.accountTableData[params.index].accountName + ']吗?'
                },
                on: {
                  'on-ok': () => {
                    this.editTableIndex = -1
                    // 调用删除方法
                    this.deleteAccount(params.index)
                  },
                  'on-cancel': () => {
                  }
                }
              }, [
                h('Button', {
                  class: { 'btnFA5151': true },
                  props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                  style: this.buttonStyle1,

                  on: {
                    click: () => {
                    }
                  }
                }, '删除')
              ]
              )
            ])
          }
        }
      ],
      cityList: [], // 省数组
      cityList1: [], // 市数组
      isUpgradeContract: '',
      iplist: [],
      statusNode: '',

      statusFilter: '',
      pkeyone: '',
      pkeytwo: '',
      targetWin: '',
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  // watch: {
  //   'arrDetails.isInit' (newvalue, oldvalue) {
  //     console.log(newvalue, oldvalue)
  //   }
  // },
  methods: {
    actionAbandon () {
      this.abandonmodal = true
    },
    abandonok () {
      this.ok('formItem')
    },
    // 删除节点
    deletedData (row) {
      deleteCmeosNode(row).then(res => {
        if (res.code === '00000') {
          this.msgInfo('success', res.message, true)
          this.getTableData(this.arrDetails.chainId)
        } else {
          this.msgInfo('error', res.message, true)
        }
      })
    },
    // 重启节点
    restartNode (row) {
      // console.log(row)
      restartCmeosNode(this.arrDetails.chainId, row).then(res => {
        if (res.code === '00000') {
          this.msgInfo('success', res.message, true)
          this.getTableData(this.arrDetails.chainId)
        } else {
          this.msgInfo('error', res.message, true)
        }
      })
    },
    // 节点ip列表
    nodelist () {
      nodelablegetList().then((res) => {
        this.iplist = res.data
      })
    },
    // 初始化
    initialization () {
      this.creatmodal = true
    },
    creatok () {
      this.disabledinitial = true
      this.initia = true
      // this.isshow = false
      this.msgInfo('success', '开始初始化，稍后可在链管理页面->日志中查看结果', true)
      initnetWork(this.arrDetails.chainId).then((res) => {
        this.getInfo(this.eosChainId)
        if (res.code === '00000') {
          // this.msgInfo('success', res.message, true)

        } else {
          this.msgInfo('error', res.message, true)
        }
      })
    },
    // 省清空
    clearcode () {
      this.formItem.provinceCode = null
      this.formItem.cityCode = null
      this.formItem.provinceName = ''
      this.formItem.cityName = ''
      this.disabled = true
    },
    // 市清空
    clearCity () {
      this.formItem.cityName = ''
      this.formItem.cityCode = null
    },
    getselectOptions () { // 获取省下来选项列表
      let listdata = {
        pcode: 100000,
        name: ''
      }
      getserchList(listdata).then((res) => {
        this.cityList = res.data
      })
    },
    async getCityOptions (params) { // 获取市下拉列表
      let { data } = await getserchList(params)
      this.cityList1 = data
      this.formItem.cityName = this.cityList1[0].name
      this.formItem.cityCode = this.cityList1[0].code
      return data
    },
    //
    selectmethods () {
      // console.log(this.provinces)

      if (this.formItem.provinceCode !== null) {
        this.disabled = false
      }
    },
    // 选择省份item
    itemprovinces (e) {
      // console.log(e)
      this.formItem.provinceCode = e.value // 省code
      this.formItem.provinceName = e.label// 省名称
      let listdata = {
        pcode: e.value,
        name: ''
      }
      // getserchList(listdata).then((res) => {
      //   this.cityList1 = res.data
      // })
      this.getCityOptions(listdata)
    },
    //  // 选择市item
    itemcity (e) {
      // console.log(e)
      this.formItem.cityCode = e.value // 市code
      this.formItem.cityName = e.label// 市名称
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData(this.arrDetails.chainId)
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData(this.arrDetails.chainId)
    },
    init () {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.formItem = {
        alertTitle: '新增节点',
        chainId: '',
        nodeId: '',
        nodeNameg: '',
        nodeNamen: '',
        nodeAddressg: '',
        nodeAddressn: '',
        nodeApiPort: '',
        nodeType: '',
        status: '',
        nodeP2pPort: '',
        location: '',
        provinceCode: null, // 省code
        cityCode: null, // 市code
        provinceName: '', // 省名称
        cityName: '', // 市名称
        diskdir: '',
        statusYun: ''
      }
      this.loading = false
      this.disabled = true
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    tipInfo (res) {
      if (res.code === '00000') {
        this.msgInfo('success', res.message, true)
        this.getInfo(this.eosChainId)
        this.modal = false
        this.accountModal = false
        this.ownerKeyShow = true
        this.activeKeyShow = true
        if (this.formItem.alertTitle === '新增节点' && this.arrDetails.isInit !== '1' && this.initia === false) {
          this.disabledinitial = false
        } else {
          this.disabledinitial = true
        }
      } else {
        this.msgInfo('error', res.message, true)
      }
    },
    getTableData (chainId) {
      getNodeList(this.tablePageParam, chainId, this.statusFilter, this.statusNode).then(res => {
        if (res.code === '00000') {
          this.tableData = res.data.records
          if (this.tableData.length !== 0 && this.arrDetails.isInit !== '1') {
            this.disabledinitial = false
          }
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          ++this.transferKey
        } else {
          this.msgInfo('error', res.message, true)
          this.reback()
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
        this.reback()
      })
    },
    getInfo (eosChainId) {
      getMultiLinkDetails(eosChainId).then(res => {
        if (res.code === '00000') {
          this.arrDetails = res.data
          localStorage.setItem('ownership', this.arrDetails.ownership)
          this.getTableData(this.arrDetails.chainId)
          // 深拷贝数组
          this.reviseTableData = JSON.parse(JSON.stringify(res.data.manageAccountList))
          this.accountTableData = changeKey(res.data.manageAccountList)
          this.isUpgradeContract = res.data.isUpgradeContract
          ++this.transferKey
        } else {
          this.msgInfo('error', res.message, true)
          this.reback()
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
        this.reback()
      })
    },
    async editDetails (index, row) {
      // console.log(Boolean(this.formItem.statusYun))
      // this.$nextTick(() => {
      //   this.$refs['formItem'].resetFields()
      // })
      console.log(row);
      let res = await getNodeDetails(this.arrDetails.chainId, row.nodeId)
      if (res.code == '00000') {
        this.formItem = {
          alertTitle: '修改链节点信息',
          chainId: this.arrDetails.chainId,
          nodeId: `${this.tableData[index].nodeId}`,
          nodeNameg: `${this.tableData[index].nodeName}`,
          nodeAddressg: `${this.tableData[index].nodeAddress}`,
          nodeNamen: `${this.tableData[index].nodeName}`,
          nodeAddressn: res.data.nodeAddress,
          nodeApiPort: `${this.tableData[index].nodeApiPort}`,
          nodeType: `${this.tableData[index].nodeType}`,
          status: `${this.tableData[index].status}`,
          nodeP2pPort: `${this.tableData[index].nodeP2pPort || ''}`,
          location: `${this.tableData[index].location}`,
          provinceCode: `${this.tableData[index].provinceCode || null}`, // 省code
          cityCode: `${this.tableData[index].cityCode || null}`, // 市code
          provinceName: `${this.tableData[index].provinceName || ''}`, // 省名称
          cityName: `${this.tableData[index].cityName || ''}`, // 市名称
          diskdir: `${this.tableData[index].diskDir || ''}`,
          statusYun: `${this.tableData[index].nodeStatus || ''}` // 运行状态
        }
      } else {
        this.msgInfo('error', res.message, true)
      }
      this.loading = false
      this.modal = true

      if (this.formItem.provinceName) { // 如果省级有默认值那么获取市级列表
        this.disabled = false
        let params = { // 获取市级列表
          pcode: this.formItem.provinceCode,
          name: ''
        }
        // getserchList(listdata).then((res) => {
        //   this.cityList1 = res.data
        // })
        await this.getCityOptions(params) // 市级列表获取成功
        // 如果市级有默认值 给市级赋默认值
        const { cityName, cityCode } = this.tableData[index]
        this.formItem.cityName = `${cityName || ''}` // 当后台返回 无效值时返回空
        this.formItem.cityCode = `${cityCode || null}` // 市code
      }
    },
    ok (name) {
      // console.log('提交')
      this.$refs[name].validate((valid) => {
        if (valid) {
          // if (this.formItem.provinceName !== '' && this.formItem.cityName === '') {
          //   this.formItem.cityName = this.cityList1[0].name
          //   this.formItem.cityCode = this.cityList1[0].code
          // }
          // console.log('form', this.formItem)
          // if (this.tableData.length === 0 && this.formItem.nodeName != 'eosio') {
          //   this.msgInfo('error', '首次创建节点名称必须为eosio', true)
          // } else {

          if (this.ownership === '管控链' && this.tableData.length == 0 && this.formItem.nodeType != '出块节点') {
            this.msgInfo('error', '链的第一个节点必须为出块节点', true)
          } else if ((this.arrDetails.isInit === '1' || this.initia === true) && this.formItem.nodeType === '出块节点' && this.formItem.alertTitle === '新增节点') {
            this.msgInfo('error', '已完成初始化，无法新建出块节点', true)
          } else if (this.formItem.cityName === '' || this.formItem.provinceName === '' || this.formItem.provinceCode == null || this.formItem.cityCode == 'null') {
            this.msgInfo('error', '省份和城市不能为空', true)
          } else {
            if (this.formItem.nodeP2pPort && !isNumber(this.formItem.nodeP2pPort)) {
              this.msgInfo('error', 'P2P端口必须为数字', true)
            } else {

              this.formItem.location = this.formItem.provinceName + this.formItem.cityName
              this.formItem.nodeTypeKey = searchKey(this.formItem.nodeType, this.nodeTypeList)
              if (this.ownership === '管控链') {
                this.formItem.statusKey = searchKey(this.formItem.status, this.statusListg)
              } else {
                this.formItem.statusKey = searchKey(this.formItem.status, this.statusList)
              }

              // console.log('编辑', this.formItem)
              // 为了判断首次初始化失败后 还可以再次操作初始化
              if (this.tableData.length == 0 && this.ownership === '管控链' && this.arrDetails.networkStatus == null) {
                // this.getcreateNetork(this.chainId)
                let infodata = {
                  chainId: this.chainId,
                  status: 'ENABLE'
                }


                createNetwork(infodata).then((res) => {
                  console.log(res);
                  if (res.code === '00000') {
                    this.loading = true
                    this.getInfo(this.eosChainId)
                    console.log(this.formItem.cityCode);
                    addCmeosNode(this.formItem).then(res => {
                      this.tipInfo(res)
                      this.loading = false
                    }).catch(error => {
                      this.msgInfo('error', error.message, true)
                      this.loading = false
                    })
                  } else {
                    this.msgInfo('error', res.message, true)
                  }
                })
              } else if (this.tableData.length == 0 && this.ownership === '管控链') {
                addCmeosNode(this.formItem).then(res => {
                  this.tipInfo(res)
                  this.loading = false
                }).catch(error => {
                  this.msgInfo('error', error.message, true)
                  this.loading = false
                })
              }
              if (this.formItem.nodeId) {
                this.loading = true
                if (this.ownership === '管控链') {
                  updateNetwork(this.formItem).then(res => {
                    this.tipInfo(res)
                    this.loading = false
                  }).catch(error => {
                    this.msgInfo('error', error.message, true)
                    this.loading = false
                  })
                } else {
                  reviseMultiChainNode(this.formItem).then(res => {
                    this.tipInfo(res)
                    this.loading = false
                  }).catch(error => {
                    this.msgInfo('error', error.message, true)
                    this.loading = false
                  })
                }
              } else {
                // console.log('新增', this.formItem)
                this.formItem.chainId = this.arrDetails.chainId
                if (this.ownership === '管控链' && this.tableData.length > 0) {
                  this.loading = true
                  addCmeosNode(this.formItem).then(res => {
                    this.tipInfo(res)
                    this.loading = false
                  }).catch(error => {
                    this.msgInfo('error', error.message, true)
                    this.loading = false
                  })
                } else if (this.ownership === '纳管链') {
                  this.loading = true
                  addMultiChainNode(this.formItem).then(res => {
                    this.tipInfo(res)
                    this.loading = false
                  }).catch(error => {
                    this.msgInfo('error', error.message, true)
                    this.loading = false
                  })
                }
              }
            }
          }
          // }
        } else {
          this.msgInfo('error', '存在字段校验不符合规范，请检查！', true)
        }
      })
      // this.init()
      // this.$refs.formItem.resetFields()
    },
    cancel (name) {
      this.init()
      this.modal = false
    },
    add (name) {

      // console.log(Boolean(this.formItem.statusYun))
      // console.log(localStorage.getItem('ownership'))
      this.init()
      this.modal = true
    },
    reback () {
      this.$router.push({
        name: 'multilink_admin'
      })
      // this.$emit('handleTabRemove', this.$route.name, event)
    },
    //新增链账户
    addAccount () {
      this.$nextTick(() => {
        this.$refs['accountItem'].resetFields()
      })
      this.accountItem.alertTitle = '新增管理链账户'
      this.disabledInput = false
      this.accountModal = true
    },
    //确定新增链账户
    okAccount () {
      this.$refs['accountItem'].validate((valid) => {
        if (valid) {
          const regex = /^[a-zA-Z0-9]{51}$/
          if (this.oldownerPrivateKey == this.accountItem.ownerPrivateKey && this.oldactivePrivateKey == this.accountItem.activePrivateKey) {
            this.msgInfo('error', '私钥无修改', true)
            return
          }
          if (!regex.test(this.accountItem.ownerPrivateKey)) {
            this.msgInfo('error', 'owner权限私钥格式有误,长度必须为51位,a-zA-Z0-9', true)
            return
          }

          this.accountItem.chainId = this.arrDetails.chainId
          let obj = {
            ownerPrivateKey: encryptedData(this.accountItem.ownerPrivateKey, this.$store.state.publicKey),
            activePrivateKey: encryptedData(this.accountItem.activePrivateKey, this.$store.state.publicKey),
            accountName: this.accountItem.accountName,
            chainId: this.arrDetails.chainId,
            manageAccountName: this.accountItem.manageAccountName
          }
          console.log('this.accountItem', this.accountItem)



          addManageAccount(obj).then(res => {
            if (res.code == 'A0314') {
              console.log('打开金库查看详情')
              this.showBanktwo(localStorage.getItem("upsertManageAccountEncrypt"))//传入编辑参数
            } else {
              console.log('新增编辑(正常逻辑)', res.data)
              this.tipInfo(res)
            }

          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        } else {
          this.msgInfo('error', '存在字段校验不符合规范，请检查！', true)
        }
      })
    },
    //金库确认编辑/新增
    okAccounttwo () {
      this.$refs['accountItem'].validate((valid) => {
        if (valid) {
          const regex = /^[a-zA-Z0-9]{51}$/
          if (this.oldownerPrivateKey == this.accountItem.ownerPrivateKey && this.oldactivePrivateKey == this.accountItem.activePrivateKey) {
            this.msgInfo('error', '私钥无修改', true)
            return
          }
          if (!regex.test(this.accountItem.ownerPrivateKey)) {
            this.msgInfo('error', 'owner权限私钥格式有误,长度必须为51位,a-zA-Z0-9', true)
            return
          }

          this.accountItem.chainId = this.arrDetails.chainId
          let obj = {
            ownerPrivateKey: encryptedData(this.accountItem.ownerPrivateKey, this.$store.state.publicKey),
            activePrivateKey: encryptedData(this.accountItem.activePrivateKey, this.$store.state.publicKey),
            accountName: this.accountItem.accountName,
            chainId: this.arrDetails.chainId,
            manageAccountName: this.accountItem.manageAccountName
          }
          console.log('this.accountItem', this.accountItem)



          addManageAccount(obj).then(res => {
            // if (res.code == 'A0314') {
            //   console.log('打开金库查看详情')
            //   this.showBanktwo(localStorage.getItem("upsertManageAccountEncrypt"))//传入编辑参数
            // } else {
            //   console.log('新增编辑(正常逻辑)', res.data)
              this.tipInfo(res)
            // }

          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        } else {
          this.msgInfo('error', '存在字段校验不符合规范，请检查！', true)
        }
      })
    },
    cancelAccount () {
      this.accountModal = false
      this.ownerKeyShow = true
      this.activeKeyShow = true
    },

    //获取key
    getkeyonefn () {
      getconfig('DEFAULT_WEB_KEY1').then((res) => {
        this.pkeyone = res.data.value
      })
    },
    getkeytwofn () {
      getconfig('DEFAULT_WEB_KEY2').then((res) => {
        this.pkeytwo = res.data.value
      })
    },
    //编辑链账户
    editAccount (index) {
      console.log(this.accountTableData[index].accountName, '链名称')
      this.manageAccountName = this.accountTableData[index].accountName //记录合约名称
      this.getmanageAccountInfo(index)
    },

    //获取金库
    getmanageAccountInfo (index, treasuryToken = null) {
      let obj = {
        'eosChainId': this.arrDetails.eosChainId,
        'accountName': this.manageAccountName,
        "treasuryToken": treasuryToken
      }
      manageAccountInfo(obj).then(res => {
        console.log(res, 'lllllll')
        if (res.code == 'A0314') {
          console.log('打开金库')
          this.showBank(localStorage.getItem("manageAccountInfoEncrypt"))//传入详情参数
        } else {
          console.log('编辑正常逻辑', res.data)
          this.openEidtModal(res)
          // this.openModal(index)
        }
      }).catch(error => {

      })
    },
    //验证完金库打开编辑弹窗
    openEidtModal (res) {
      this.$nextTick(() => {
        this.$refs['accountItem'].resetFields()

        let privateKey = this.pkeyone + this.pkeytwo
        console.log(privateKey, 1111)
        console.log(decryptData(res.data.ownerPrivateKey, privateKey), 222222)
        this.accountItem = {
          alertTitle: '修改链账户信息',
          chainId: this.arrDetails.chainId,
          manageAccountName: this.manageAccountName,
          ownerPrivateKey: decryptData(res.data.ownerPrivateKey, privateKey),
          activePrivateKey: decryptData(res.data.activePrivateKey, privateKey)
        }
      })
      this.accountModal = true
      this.disabledInput = true
    },
    //正常打开弹窗逻辑
    openModal (index) {
      this.$nextTick(() => {
        this.$refs['accountItem'].resetFields()
      })
      console.log(this.accountTableData, ';;;;;;;;;;;;;;;;;;;;;;;;;;;;;')
      this.accountModal = true
      var list = getKey(this.accountTableData[index].accountName, this.reviseTableData)
      this.oldownerPrivateKey = list.ownerPrivateKey
      this.oldactivePrivateKey = list.activePrivateKey
      console.log(decryptData(list.ownerPrivateKey), decryptData(list.activePrivateKey), '解密秘钥')
      this.accountItem = {
        alertTitle: '修改链账户信息',
        chainId: this.arrDetails.chainId,
        manageAccountName: `${this.accountTableData[index].accountName}`,
        ownerPrivateKey: list.ownerPrivateKey,
        activePrivateKey: list.activePrivateKey
      }
      this.disabledInput = true
    },







    //查看详情（金库弹窗）
    showBank (params) {

      var iWidth = 700; //模态窗口宽度
      var iHeight = 450;//模态窗口高度
      var iTop = (window.screen.height - iHeight - 100) / 2;
      var iLeft = (window.screen.width - iWidth) / 2;
      var winOption = 'height=' + iHeight + ',innerHeight=' + iHeight + ',width=' + iWidth + ',innerWidth=' + iWidth + ',top=' + iTop + ',left=' + iLeft + ',toolbar=no,menubar=no,scrollbars=no,resizeable=no,location=no,status=no';

      var obj = new Object();
      obj.operCode = params//!!!!区分详情  编辑!!!!!
      obj.mainLoginName = "";
      obj.subLoginName = localStorage.getItem("userNameEncrypt")
      obj.appCode = "JTNGCMBAAS";
      obj.sessionId = sessionStorage.getItem("session");
      obj.serverIp = window.location.host.split(":")[0]
      obj.serverPort = window.location.port;
      obj.checkSessionUrl = "/";
      obj.svcNum = "";
      obj.operContent = "";

      // 移除旧的事件监听器，防止重复绑定
      if (window.removeEventListener) {
        window.removeEventListener('message', this.receiveMsg, false);
      }

      //增加浏览器的判断，ie走if原有逻辑，非ie走else逻辑,通过遮罩层实现。
      var a1 = navigator.userAgent;
      var yesIE = a1.search(/Trident/i);
      if (window.ActiveXObject || window.attachEvent || yesIE > 0) { //IE
        // var returnValue = window.showModalDialog("b.html?id=" + new Date(), obj, "dialogHeight:" + iHeight + "px; dialogWidth:" + iWidth + "px; toolbar:no; menubar:no;  titlebar:no; scrollbars:yes; resizable:no; location:no; status:no;left:" + iLeft + "px;top:" + iTop + "px;");
        var me = new Object();
        // me.data = returnValue;
        this.receiveMsg(me);
      } else {  //非IE
        this.openWindowWithPostRequest(iWidth, iHeight, iTop, iLeft, winOption, obj);
        if (window.addEventListener) {
          //为window注册message事件并绑定监听函数
          window.addEventListener('message', this.receiveMsg, false);
        } else {
          window.attachEvent('message', this.receiveMsg);
        }
      }
    },
    //非IE
    openWindowWithPostRequest (iWidth, iHeight, iTop, iLeft, winOption, obj) {
      var winName = "sWindow";
      var winURL = "http://api.it4a.cmit.cmcc:7081/uac/web3/jsp/goldbank/goldbank3!goldBankIframeAction.action";//应用侧对应后台服务action
      var form = document.createElement("form");
      form.setAttribute("method", "post");
      form.setAttribute("action", winURL);
      form.setAttribute("target", winName);
      for (var i in obj) {
        if (obj.hasOwnProperty(i)) {
          var input = document.createElement('input');
          input.type = 'hidden';
          input.name = i;
          input.value = obj[i];
          form.appendChild(input);
        }
      }
      document.body.appendChild(form);
      //打开地址，刚开始时，打开一个不存在的地址，这样才有返回值
      // 将window.open的结果存储在普通变量中，而不是Vue的响应式属性中
      const targetWindow = window.open("", winName, winOption);
      form.target = winName;
      form.submit();
      document.body.removeChild(form);
      if (window.focus && targetWindow) {
        targetWindow.focus();
      }
      // 将引用存储在this上，但使用非响应式方式
      this._targetWin = targetWindow;
    },
    //接收返回值后处理函数
    receiveMsg (e) {
      console.log(e, 'e');
      // returnValue = e.data;

      // 如果没有数据或数据为空对象，可能是窗口刚刚打开，不做处理
      if (!e.data || (typeof e.data === 'object' && Object.keys(e.data).length === 0)) {
        return;
      }

      var dataStatus = typeof e.data === 'string' ? e.data.split("#") : []
      const statusMessageMap = {
        '-3': { message: '金库应急开启中，允许业务继续访问', allowAccess: true },
        '-2': { message: '金库场景或元业务未开启，允许业务继续访问', allowAccess: true },
        '-1': { message: '直接关闭窗口，未申请审批，不允许业务继续访问', allowAccess: false },
        '1': { message: '审批通过，允许业务继续访问', allowAccess: true },
        '0': { message: '审批不通过，不允许业务继续访问', allowAccess: false },
        '2': { message: '超时，允许业务继续访问', allowAccess: true },
        '3': { message: '超时，不允许业务继续访问', allowAccess: false },
        '4': { message: '出现错误或异常（包括数据异常），不允许业务继续访问', allowAccess: false },
        '5': { message: '未配置策略，允许业务继续访问', allowAccess: true },
        '6': { message: '未配置策略，不允许继续访问', allowAccess: false }
      };
      const status = dataStatus[0];
      const statusInfo = statusMessageMap[status];
      console.log(statusInfo)
      if (statusInfo) {
        if (statusInfo.allowAccess) {
          // 允许业务继续访问，继续请求接口
          // 这里调用你继续请求接口的函数
          let obj = {
            'eosChainId': this.arrDetails.eosChainId,
            'accountName': this.manageAccountName,
            "treasuryToken": e.data
          }
          console.log(obj, '获取传参')
          manageAccountInfo(obj).then(res => {
            console.log(res, '获取金库参数111')
            if (res.code === '00000') {
              this.openEidtModal(res)
            } else if (res.code == 'A0314') {
              this.showBank(localStorage.getItem("manageAccountInfoEncrypt"))
            } else if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        } else {
          // 不允许业务继续访问，弹出提示框
          this.msgInfo('warning', statusInfo.message, true);
        }

        // 只有在收到有效响应时才关闭窗口
        if (this._targetWin) {
          try {
            this._targetWin.close();
            if (typeof closeDiv === 'function') {
              closeDiv(); //关闭遮罩层
            }
          } catch (err) {
            console.error('关闭窗口时出错:', err);
          }
          // 清除引用
          this._targetWin = null;
        }
      }
    },




    //新增编辑（金库弹窗）
    showBanktwo (params) {
      var iWidth = 700; //模态窗口宽度
      var iHeight = 450;//模态窗口高度
      var iTop = (window.screen.height - iHeight - 100) / 2;
      var iLeft = (window.screen.width - iWidth) / 2;
      var winOption = 'height=' + iHeight + ',innerHeight=' + iHeight + ',width=' + iWidth + ',innerWidth=' + iWidth + ',top=' + iTop + ',left=' + iLeft + ',toolbar=no,menubar=no,scrollbars=no,resizeable=no,location=no,status=no';

      var obj = new Object();
      obj.operCode = params//!!!!区分详情  编辑!!!!!
      obj.mainLoginName = "";
      obj.subLoginName = localStorage.getItem("userNameEncrypt")
      obj.appCode = "JTNGCMBAAS";
      obj.sessionId = sessionStorage.getItem("session");
      obj.serverIp = window.location.host.split(":")[0]
      obj.serverPort = window.location.port;
      obj.checkSessionUrl = "/";
      obj.svcNum = "";
      obj.operContent = "";

      // 移除旧的事件监听器，防止重复绑定
      if (window.removeEventListener) {
        window.removeEventListener('message', this.receiveMsgtwo, false);
      }

      //增加浏览器的判断，ie走if原有逻辑，非ie走else逻辑,通过遮罩层实现。
      var a1 = navigator.userAgent;
      var yesIE = a1.search(/Trident/i);
      if (window.ActiveXObject || window.attachEvent || yesIE > 0) { //IE
        // var returnValue = window.showModalDialog("b.html?id=" + new Date(), obj, "dialogHeight:" + iHeight + "px; dialogWidth:" + iWidth + "px; toolbar:no; menubar:no;  titlebar:no; scrollbars:yes; resizable:no; location:no; status:no;left:" + iLeft + "px;top:" + iTop + "px;");
        var me = new Object();
        // me.data = returnValue;
        this.receiveMsgtwo(me);
      } else {  //非IE
        this.openWindowWithPostRequesttwo(iWidth, iHeight, iTop, iLeft, winOption, obj);
        if (window.addEventListener) {
          //为window注册message事件并绑定监听函数
          window.addEventListener('message', this.receiveMsgtwo, false);
        } else {
          window.attachEvent('message', this.receiveMsgtwo);
        }
      }
    },
    //非IE
    openWindowWithPostRequesttwo (iWidth, iHeight, iTop, iLeft, winOption, obj) {
      var winName = "sWindow";
      var winURL = "http://api.it4a.cmit.cmcc:7081/uac/web3/jsp/goldbank/goldbank3!goldBankIframeAction.action";//应用侧对应后台服务action
      var form = document.createElement("form");
      form.setAttribute("method", "post");
      form.setAttribute("action", winURL);
      form.setAttribute("target", winName);
      for (var i in obj) {
        if (obj.hasOwnProperty(i)) {
          var input = document.createElement('input');
          input.type = 'hidden';
          input.name = i;
          input.value = obj[i];
          form.appendChild(input);
        }
      }
      document.body.appendChild(form);
      //打开地址，刚开始时，打开一个不存在的地址，这样才有返回值
      // 将window.open的结果存储在普通变量中，而不是Vue的响应式属性中
      const targetWindow = window.open("", winName, winOption);
      form.target = winName;
      form.submit();
      document.body.removeChild(form);
      if (window.focus && targetWindow) {
        targetWindow.focus();
      }
      // 将引用存储在this上，但使用非响应式方式
      this._targetWin = targetWindow;
    },
    //接收返回值后处理函数
    receiveMsgtwo (e) {
      console.log(e, 'e');
      // returnValue = e.data;

      // 如果没有数据或数据为空对象，可能是窗口刚刚打开，不做处理
      if (!e.data || (typeof e.data === 'object' && Object.keys(e.data).length === 0)) {
        return;
      }

      var dataStatus = typeof e.data === 'string' ? e.data.split("#") : []
      const statusMessageMap = {
        '-3': { message: '金库应急开启中，允许业务继续访问', allowAccess: true },
        '-2': { message: '金库场景或元业务未开启，允许业务继续访问', allowAccess: true },
        '-1': { message: '直接关闭窗口，未申请审批，不允许业务继续访问', allowAccess: false },
        '1': { message: '审批通过，允许业务继续访问', allowAccess: true },
        '0': { message: '审批不通过，不允许业务继续访问', allowAccess: false },
        '2': { message: '超时，允许业务继续访问', allowAccess: true },
        '3': { message: '超时，不允许业务继续访问', allowAccess: false },
        '4': { message: '出现错误或异常（包括数据异常），不允许业务继续访问', allowAccess: false },
        '5': { message: '未配置策略，允许业务继续访问', allowAccess: true },
        '6': { message: '未配置策略，不允许继续访问', allowAccess: false }
      };
      const status = dataStatus[0];
      const statusInfo = statusMessageMap[status];
      console.log(statusInfo)
      if (statusInfo) {
        if (statusInfo.allowAccess) {
          // 允许业务继续访问，继续请求接口
          // 这里调用你继续请求接口的函数

          let obj = {
            ownerPrivateKey: encryptedData(this.accountItem.ownerPrivateKey, this.$store.state.publicKey),
            activePrivateKey: encryptedData(this.accountItem.activePrivateKey, this.$store.state.publicKey),
            accountName: this.accountItem.accountName,
            chainId: this.arrDetails.chainId,
            manageAccountName: this.accountItem.manageAccountName
          }
          addManageAccount(obj).then(res => {
            console.log(res, '获取金库参数222')
            if (res.code === '00000') {
              this.okAccounttwo()
            } else if (res.code == 'A0314') {
              this.showBanktwo(localStorage.getItem("upsertManageAccountEncrypt"))
            } else if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })

        } else {
          // 不允许业务继续访问，弹出提示框
          this.msgInfo('warning', statusInfo.message, true);
        }

        // 只有在收到有效响应时才关闭窗口
        if (this._targetWin) {
          try {
            this._targetWin.close();
            if (typeof closeDiv === 'function') {
              closeDiv(); //关闭遮罩层
            }
          } catch (err) {
            console.error('关闭窗口时出错:', err);
          }
          // 清除引用
          this._targetWin = null;
        }
      }
    },





















    handleOwnerKey () {
      this.ownerKeyShow = !this.ownerKeyShow
    },
    handleActiveKey () {
      this.activeKeyShow = !this.activeKeyShow
    },
    addClass (value) {
      return value === '启用' ? 'status_style_blue' : 'status_style'
    },
    deleteAccount (index) {
      // console.log('index', index, this.arrDetails.chainId, this.accountTableData[index].accountName)
      deleteManageAccount(this.arrDetails.chainId, this.accountTableData[index].accountName).then(res => {
        this.tipInfo(res)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    clickHyperion () {
      // this.$refs.hyperionModal.visible = true
      this.$refs.hyperionModal.getHyperioncConfigQuery(this.arrDetails.chainId)
    },
    clickElas () {
      // this.$refs.elasticsearchModal.visible = true
      this.$refs.elasticsearchModal.getConfigQuery(this.arrDetails.chainId)
    }
    // clickSuite () {
    //   // console.log(111)
    //   // this.$refs.hyperionModal.visible = true
    //   this.$refs.suiteModal.getSuiteConfigQuery(this.arrDetails.chainId)
    // }
  },
  created () {
    // 组件创建时就去请求下来列表  就赋值一次就行
    this.getselectOptions()// 获取下拉选项列表 省份
    this.getkeyonefn()
    this.getkeytwofn()
  },
  watch: {
    '$route.params.eosChainId' (val) {
      if (val) {
        this.getInfo(val)
      }
    }
    // modal1 (val) {
    //   console.log(val)
    //   // console.log(oldvalu)
    // }
    // if (this.provinces !== '') {
    //   this.disabled = false
    // } else if (this.model1 === '') {
    //   this.disabled = true
    // }

  },
  mounted () {
    this.statusList = statusList
    this.statusListg = statusListg
    this.nodeTypeList = nodeTypeList
    if (this.eosChainId) {
      this.getInfo(this.eosChainId)
    }
    this.nodelist() // 查询ip
    // this.$Message.config({
    //   top: 250,
    //   duration: 2
    // })
    this.chaindetail = this.$route.params
    // console.log(this.chaindetail)
  },
  computed: {

    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin || this.userPermission.isTenantAdmin || this.userPermission.isTenant
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }

    },
    buttonStyle1 () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#FA5151',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#FA5151'}`
      }

    },
  },
  // beforeRouteEnter (to, from, next) {
  //   // 在渲染该组件的对应路由被 confirm 前调用
  //   // 不！能！获取组件实例 `this`
  //   // 因为当钩子执行前，组件实例还没被创建
  //   if (from.name) {
  //     // console.log('from.name:', from.name)
  //     next()
  //   } else {
  //     next('/multilink_admin')
  //   }
  // }
  // beforeRouteLeave (to, from, next) {
  //   // 导航离开该组件的对应路由时调用
  //   // 可以访问组件实例 `this`
  //   // this.reback()
  //   // this.$emit('handleTabRemove', this.$route.name, event)
  //   // this.tabRemove(this.$route.name, event)
  //   // const state = {
  //   //   tabList: JSON.parse(localRead('tabList') || '[]')
  //   // }

  //   // const getTabListToLocal = state.tabList.filter(item => {
  //   //   return item.name !== 'multilink_details'
  //   // }).map(item => {
  //   //   return {
  //   //     name: item.name,
  //   //     path: item.path,
  //   //     meta: item.meta,
  //   //     params: item.params,
  //   //     query: item.query
  //   //   }
  //   // })

  //   next()
  // }
}
</script>

<style lang="less" scoped>
.mandatory {
  /deep/.ivu-form-item-label::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}
.ivu-select-input[disabled] {
  &:hover {
    &::after {
      content: "请先选择";
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-radius: 3px;
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}
ul,
li {
  padding-left: 7px;
  margin: 0;
  list-style: none;
}
li {
  font-size: 14px;
  margin-bottom: 5px;
  .status_style_blue {
    width: 100px;
    height: 30px;
    padding: 3px 15px;
    color: #ffffff;
    background-color: #2d8cf0;
    box-shadow: #c2bdbd 0px 0px 5px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
  }
  .status_style {
    width: 100px;
    height: 30px;
    padding: 3px 15px;
    color: #ffffff;
    background-color: #c2bdbd;
    box-shadow: #c2bdbd 0px 0px 5px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
  }
}
span {
  padding-left: 6px;
}
.info-title {
  font-size: 16px;
  font-weight: bold;
  vertical-align: middle;
  height: 18px;
  font-family: "Microsoft YaHei";
  line-height: 18px;
  color: #333333;
  margin: 10px 0 25px 0px;
  &.addflex {
    display: flex;
    justify-content: space-between;
    .btns {
      button {
        margin-right: 20px;
      }
    }
  }
  &.initialization {
    display: flex;
    justify-content: space-between;
  }
}
.bs {
  float: left;
  width: 6px;
  height: 18px;
  background: #19c3a0;
  opacity: 1;
  border-radius: 3px;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.btnFA5151:hover {
  background-color: #fa5151 !important;
}
/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
</style>
