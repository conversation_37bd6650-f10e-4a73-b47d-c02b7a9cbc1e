<template>
  <div class="overview_page">
    <!-- <SpaceLayout bottom="20"> -->
      <div class="content">
      <div class="content_nav">
        <div class="child_nav">
          <!-- :to="path + '/' + item.path" -->
          <span class="title"  @click="getSelectName(index,item)" v-for="(item,index) in childrenRouter" :key="item.name" :class="{'router-link-active':index == selectIndex,last:index == childrenRouter.length - 1}">
            <span v-if="item.meta && item.meta.title">
              {{
              item.meta.title
              }}
            </span>
            <span class="activeup"></span>
          </span>
        </div>
        <div class="left">
          <!-- <div>{{chainItem.Name}}</div> -->
          <!-- <el-select
            v-model="chainItem"
            placeholder="请选择"
            value-key="Id"
            @change="getSelectChainItem"
            popper-class="el-option"
          >
            <el-option :label="item.Name" :value="item" v-for="item in chainList" :key="item.Id"></el-option>
          </el-select>-->
          <!-- <el-button type="primary" class="blue-btn btn-lf" @click="goGuide">一键建链</el-button> -->
        </div>
      </div>
      <div class="line"></div>
      <div>
       <!-- <router-view :chainItem="chainItem" :isClick="isClick" v-if="isRouterAlive"></router-view>-->
      </div>
    </div>
    <!-- </SpaceLayout> -->
  </div>
</template>

<script>
import SpaceLayout from "@/components/SpaceLayout";
export default {
  name: 'selectChain',
  components: {
    SpaceLayout
  },
  data() {
    return {
      isClick: false, //是否点击
      isRouterAlive: true,
      loginoutImageURL: '',
      path: "",
      childrenRouter: [],
      chainList: [],
      chainItem: {},
      curChain: {},
      selectIndex:0,
    };
  },
  mounted() {
    this.chainItem = JSON.parse(sessionStorage.getItem("chainItem"))
    this.getChildrenRouter();
  },
  computed: {
  },
  methods: {
    getSelectName(index,item) {
      // var list = this.breadcrumbList
      // if(item.name != "channelMgr") {
      //   list = list.slice(0,2)
      //   this.$store.dispatch("getBreadcrumbList", list).then(() =>{
      //     this.selectIndex = index
      //     this.$emit('getShowItem',index)
      //   })
      // } else {
      //   this.selectIndex = index
      //   this.$emit('getShowItem',index)
      // }
      this.selectIndex = index
      this.$emit('getShowItem',index)
    },
    //获取服务列表
    // getChainList() {
    //   let chainItem = JSON.parse(sessionStorage.getItem('chainItem'))
    //   getChainList().then((res) => {
    //     if (res.status == 200) {
    //       if (res.data) {
    //         this.chainList = res.data;
    //         if(chainItem && chainItem.Id) {
    //           this.chainItem = JSON.parse(sessionStorage.getItem('chainItem'))
    //           // this.$store.commit('setChainStatus', 'doing')
    //           // this.$emit('getStatus',true)
    //         } else {
    //           this.chainItem = res.data[0];
    //           // this.$emit('getStatus',true)
    //           // this.$store.commit('setChainStatus', 'doing')//none是成功什么都不做
    //         }
    //         this.$store.dispatch("getChainItem", this.chainItem)
    //       } else {
    //         this.$router.push({
    //           path: "/guide/guide",
    //         });
    //       }
    //     } else {
    //       this.$message.error("数据获取失败，请重新加载！");
    //     }
    //   });
    // },
    // getSelectChainItem(value) {
    //   // let idx=Math.round(Math.random() * 2)
    //   // let list=['fail','success','doing']
    //   // this.$emit('getStatus',true)
    //   // this.$store.commit('setChainStatus', list[idx])
    //   this.$store.dispatch("getChainItem", value).then(() => {
    //     this.chainItem = value;
    //     this.isClick = true;
    //   });
    // },
    goGuide() {
      this.$router.push({
        path: "/guide/step",
      });
    },
    getChildrenRouter() {
      // var path = this.$route.path;
      // path = path.split("/");
      // this.path = "/" + path[1];
      // var routers = this.addRouters;
      // routers.forEach((item) => {
      //   if (item.path == "/chainManage") {
      //     if (item.children && item.children.length > 0) {
      //       this.childrenRouter = item.children
      //     }
      //   }
      // });
      this.childrenRouter = [
        {
          meta:{
            title:'概览'
          }
        },
        {
          meta:{
            title:'通道管理'
          }
        },
        {
          meta:{
            title:'合约管理'
          }
        },
        {
          meta:{
            title:'组织节点'
          }
        },/*{
          meta:{
            title:'安全监控'
          }
        }*/
      ]
      var list = [{meta:{title: "安全监控"}}]
       if(this.chainItem.TemplateType == 'KAFKA_CMRI') {
         this.childrenRouter = this.childrenRouter.concat(list)
       }
    }
  }
};
</script>
<style>
  .el-select-dropdown__item{
    color:#333;
    /* font-size:17px; */
    font-size: 14px;
  }
</style>
<style rel="stylesheet/less" lang="less" scoped>
  @import "../../../styles/common/select.less";
.overview_page {
  width: 100%;
  .content {
    // margin-bottom: 28px;
    .content_nav {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      span {
        color: #666666;
        // font-size: 18px;
        font-size: 14px;
      }
      .left {
        display: flex;
        align-items: center;
        .btn {
          margin-left: 20px;
          width: 120px;
          background: #337DFF;
          color: #ffffff;
          // font-size: 18px;
          font-size: 14px;
        }
      }

      .child_nav {
        display: flex;
        .title {
          cursor: pointer;
          width: 96px;
          height: 40px;
          background: #F1F4FA;
          border: 1Px solid #D9D9D9;
          border-radius: 3px 3px 0 0;
          border-radius: 3px 3px 0px 0px;
          margin-right: 7px;
          text-align: center;
          line-height:40px;
          position: relative;
          border-bottom: 0;
          bottom: -1Px;
          span {
            color: #555555;
            // font-size: 20px;
            font-size: 14px;
          }
          &:hover {
            span {
              color: #337DFF !important;
            }
          }
        }
        .router-link-active {
          background: #FFFFFF;
          border-bottom: 1Px solid #fff;
          bottom: -1Px;
          height: 41px;
          span {
            color: #337DFF  !important;
          }
        }
      }
    }
    .line {
      width: 100%;
      height: 1Px;
      background:#D9D9D9;
    }
    .child {
      margin-top: 36px;
    }
  }
}

.left /deep/ .el-input--medium .el-input__inner {
  /*width: 180px !important;*/
  height: 46px !important;
  /*font-size: 20px;*/
}
.left /deep/ .el-button {
  /*height: 48px !important;*/
  /*font-size: 20px;*/
  /*font-weight: bold;*/
}
</style>
