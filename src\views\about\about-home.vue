<template>
  <div class="market-home">
    <keep-alive>
      <div class="market-home-item">
        <!-- <div class="contract-title">
          <b>智能合约</b>
          <Button icon="ios-arrow-forward" size="small" @click="isContractFlag=false">合约广场</Button>
        </div> -->
        <!-- <Button class="right-button" icon="ios-arrow-forward" size="small" @click="changeFlag(false)">合约广场</Button> -->
        <Tabs :value="name1">

          <TabPane label="迭代版本说明" name="name1">
            <IterationDescription />
          </TabPane>
          <TabPane label="帮助文档" name="name2">
            <OperationManual />
          </TabPane>
          <TabPane label="常见问题解答" name="name3">
            <ProblemSolving />
          </TabPane>
          <TabPane label="关于我们" name="name4">
            <AboutUs />
          </TabPane>
        </Tabs>
      </div>
    </keep-alive>
  </div>
</template>
<script>
import AboutUs from './about-us'
import OperationManual from './operation-manual'
import IterationDescription from './iteration-description'
import ProblemSolving from './problem-solving'
export default {
  // name: 'has-approval',
  components: {
    AboutUs,
    OperationManual,
    IterationDescription,
    ProblemSolving
  },
  data () {
    return {
      name1: this.$route.params.name1 ? this.$route.params.name1 : 'name1'
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {

  },
  mounted () {

  },

}
</script>

<style lang="less" scoped>
.market-home {
  .market-home-item {
    position: relative;
    // .contract-title{
    //   display: flex;
    //   align-items: center;
    //   b{
    //     margin-right:10px;
    //   }
    // }
  }
  //
  .right-button {
    position: absolute;
    right: 16px;
    top: 4px;
    z-index: 10;
  }
}
</style>
