<template>
  <div class="layout-wrapper">
    <Layout class="layout-outer">
      <Sider :width="200" collapsible hide-trigger reverse-arrow v-model="collapsed" class="sider-outer">
        <side-menu @on-parent-select="parentSelect" :collapsed="collapsed" :list="routers"></side-menu>
      </Sider>
      <Layout>
        <Header class="header-wrapper">
          <!-- <Icon :class="triggerClasses" @click.native="handleCollapsed" type="md-menu" :size="28"/> -->
          <div style="float:left;">
            <Breadcrumb>
              <Icon :class="triggerClasses" @click.native="handleCollapsed" type="md-menu" :size="20" />
              <BreadcrumbItem to="" v-for="(item, index) in routeSplitArray" :key="item.name+Math.random()"><span @click="goRouter(item.name)" class="click-to" v-if="((routeSplitArray.length >2 && findList.indexOf(item.name) <=-1) || (item.name === 'user_index')||(item.name === 'market')||(item.name === 'ipfs_index'))">{{ item.meta.title }}</span><span v-else>{{ item.meta.title  }}</span></BreadcrumbItem>
              <!-- <BreadcrumbItem to="">{{ this.$router.history.current.matched[0].meta.title }}</BreadcrumbItem>
              <BreadcrumbItem to="" v-if="this.$router.history.current.matched[1].meta.title">{{ this.$router.history.current.matched[1].meta.title }}</BreadcrumbItem>
              <BreadcrumbItem to="" v-if="this.$route.meta.title !== this.$router.history.current.matched[1].meta.title">{{ this.$route.meta.title }}</BreadcrumbItem> -->
            </Breadcrumb>
          </div>
          <!-- <Button type="dashed" @click.stop="openHistory" circle style="cursor:pointer;margin-left:20px">平台操作手册</Button> -->
          <div style="float:right;">
            <!-- <div class="layout-img-container" @click="handleScreen" title="运营总览视图" v-show="screenShow">
              <SatisfactionSurvey :type="1"  ref="satisfactionSurvey" />
            </div> -->
            <!-- <div class="layout-img-container" v-show="yunyingsrcshow" @click="handleyunyingScreen" title="生产运营驾驶舱">

              <SatisfactionSurvey :type="2" ref="satisfactionSurvey" />
            </div> -->
            <Tooltip v-show="yunyingsrcshow" content="生产运行驾驶舱" placement="bottom-end" :transfer="false" class="tooltip-style">
              <div @click="handleyunyingScreen" style="position: relative;top: 3px;">
                <SatisfactionSurvey :type="2" ref="satisfactionSurvey" />
              </div>
            </Tooltip>

            <!-- <Tooltip content="请先到配置管理配置跳转客服的地址，配置键为：CUSTOMER_FROM_URL" max-width="200" placement="bottom-end" :transfer="false" class="tooltip-style" v-if="customerdis">
              <Button style="border:none;width:45px;background:none" disabled>
                <img src="../assets/image/gdxz.png" alt="" style="margin:-2 12px 0 0;width:16px" @click="customer" />
              </Button>
            </Tooltip> -->
            <!-- <Tooltip content="中移链大屏" placement="bottom-end" :transfer="false" class="tooltip-style" v-else>
              <Icon :class="triggerClasses" @click.native="handleScreen" type="md-desktop" :size="20" />
            </Tooltip> -->
            <!-- <Tooltip content="客服" placement="bottom-end" :transfer="false" class="tooltip-style" v-else>
              <img src="../assets/image/gdzc.png" alt="" style="margin:0 12px 0 0;cursor:pointer" @click="customer" />
            </Tooltip> -->

            <Tooltip content="关于我们" placement="bottom-end" :transfer="false" class="tooltip-style">
              <!-- <Icon type="md-people" :size="18" class="full-screen" style="margin:0 12px 0 0;cursor:pointer" @click="modalAbout1" /> -->
              <img src="../assets/image/guanyu.png" alt="" style="margin:0 12px 0 12px;cursor:pointer" @click="getmodalAbout1" />
            </Tooltip>
            <Tooltip content="全屏显示" placement="bottom-end" :transfer="false" class="tooltip-style">
              <Icon type="md-qr-scanner" :size="18" class="full-screen" style="margin:0 12px 0 0;cursor:pointer" @click="fullScreen()" />
            </Tooltip>
            <div class="layout-img-container">
              <Tooltip content="工单管理" placement="bottom" :transfer="false" class="tooltip-style">
                <img class="layout-img" @click="goRouterWorkOrderNew('new_workorder')" :src="workorderIcon">
              </Tooltip>
            </div>
            <!-- <Poptip v-else placement="bottom-end" trigger="click"  width="400" v-model="visible">
               <Tooltip content="工单管理" placement="bottom-end" :transfer="false">
              <div class="layout-img-container"><img class="layout-img" :src="workorderIcons"></div>
               </Tooltip>
              <div class="work-api" slot="content">
                <div class="work-tip-main">
                  <div class="work-tip" v-for="item in workorders" :key="item.orderId+Math.random()">
                    <p class="work-time">{{item.reportTime}}</p>
                    <p :style="styleBr">{{item.title}} <span class="btn" @click="goRouter('workorder_detail', item.orderId )">详情 ></span></p>
                  </div>
                </div>
                <p class="p-btn"><span @click="goRouterWorkOrderNew('new_workorder')">新建工单</span></p>
              </div>
            </Poptip> -->

            <Tooltip content="用户通知" placement="bottom-end" :transfer="false" class="tooltip-style">
              <div class="layout-img-container">
                <img class="layout-img" @click="openDrawer" :src="noticeDotImg" v-if="waitRead === 1 || waitDeal === 1">
                <img class="layout-img" @click="openDrawer" :src="noticeImg" v-else>
              </div>
            </Tooltip>
            <Icon type="md-remove" style="transform:rotate(90deg)" :size="18" />
            <Dropdown>
              <Avatar :src="faceUrl || faceUrls" shape="circle" icon="ios-person" style="margin:12px" />
              <span>{{ userLoginId || userLoginIds }}</span>
              <DropdownMenu slot="list">
                <DropdownItem @click.native="handleUserInfo">进入个人中心</DropdownItem>
                <DropdownItem @click.native="handleCM">关于CMBaaS</DropdownItem>
                <DropdownItem @click.native="handleLogout">退出登录</DropdownItem>
              </DropdownMenu>
            </Dropdown>
            <Select class="select-style1" v-show="isAdmin" @on-change="changetenant" @on-clear="cleartenant" v-model="$store.state.tenantId" placeholder="请选择租户" clearable style="width:170px;margin-left:10px;">
              <Option v-for="item in userListData" :value="item.id" :key="item.id">{{ item.tenantName }}</Option>
            </Select>
          </div>
        </Header>
        <Content class="content-con">
          <div class="view-tab">
            <Tabs type="card" @on-click="handleClickTab" :value="getTabNameByRoute($route)">
              <TabPane :label="labelRender(item)" :name="getTabNameByRoute(item)" v-for="(item, index) in tabList" :key="`tabNav${index}`"></TabPane>
              <div slot="extra" style="padding:6px 15px 5px 5px;">
                <Dropdown :transfer="true">
                  <Icon type="ios-close-circle-outline" size="20" />
                  <DropdownMenu slot="list">
                    <DropdownItem @click.native="closeAll">关闭所有</DropdownItem>
                    <DropdownItem @click.native="closeOther">关闭其它</DropdownItem>
                  </DropdownMenu>
                </Dropdown>
              </div>
            </Tabs>
          </div>

          <div class="view-box" style="margin: 0 12px;">
            <Card shadow class="page-card">
              <!-- <keep-alive :include="includeArr"> 切换tab会导致页面无法重新加载-->
              <router-view @handleTabRemove='handleTabRemove' />
              <!-- </keep-alive> -->
            </Card>
          </div>
        </Content>
        <Footer>
          <p class="footer">{{ versionData.version  }}</p>
          <p class="footer">{{ versionData.copyright }}</p>
        </Footer>
      </Layout>
    </Layout>
    
    <!-- AI助手图标 -->
    <div class="ai-assistant"
         :class="{ 'ai-active': aiHovered }"
         v-if="aiAssistantVisible"
         @mouseenter="handleAiMouseEnter"
         @mouseleave="handleAiMouseLeave">
      <Poptip placement="left" width='300' trigger="hover">
         <img class="renou" :src="aiHovered ? aiRenouImg : aiRenouImg_p" alt="AI助手"  @click="getAiConfigAndOpen" />
        <div slot="content">
          <p class="p_elail">
            <span class="img_elail"><img src="../assets/kefu/Email.png" alt=""></span>
            <span class="info row" style="margin-left:10px">{{servizioinfo.CUSTOMER_SERVICE_EMAIL}}</span>
          </p>
          <Divider />
          <p class="p_elail">
            <span class="img_elail"><img src="../assets/kefu/phone.png" alt=""></span>
            <span class="info row" style="margin-left:10px">{{servizioinfo.CUSTOMER_SERVICE_PHONE}}</span>
          </p>
          <p class="p_elail" style="margin-top: 3px;">
            <span class="info">时间：{{servizioinfo.CUSTOMER_SERVICE_TIME}}</span>
          </p>
        </div>
      </Poptip>
    </div>
    
    <!-- AI助手iframe弹窗 -->
    <div v-if="aiModalVisible" class="custom-modal-container">
      <div class="custom-modal">
        <div class="custom-modal-header">
          <span>AI智能助手</span>
          <Icon type="md-close" @click="aiModalVisible = false" class="close-icon" />
        </div>
        <div class="custom-modal-body">
          <iframe :src="aiIframeUrl" frameborder="0" class="ai-iframe"></iframe>
        </div>
      </div>
    </div>
    
    <Drawer v-model="drawerVisible" width="350" :mask-closable="true" :closable="false" height="150">
      <div class="login_header">
        <div @click="clickCur" :class="{active:cur===0}" class="login_header_1">
          <Badge dot :count="waitDeal">
            <span>待办</span>
          </Badge>
        </div>
        <div @click="clickCur" :class="{active:cur===1}" class="login_header_2" style="">
          <Badge dot :count="waitRead">
            <span>待阅</span>
          </Badge>
        </div>
        <div class="login_header_3" :class="[allCheck === true ? 'img-style-none' : '']" v-show="cur===1" @click="clickAll">
          <Tooltip content="标记当前待阅为已读" placement="left">
            <img :class="[allCheck === true ? 'img-style-none' : 'img-style']" :src="allCheck === true ? allImgDot : allImg">
          </Tooltip>
        </div>
        <!-- <div class="login_header_3" v-show="cur===0">
          <img :src="allImgDot">
        </div> -->
      </div>
      <div v-show="cur===0">
        <DrawerList ref="drawChild" @closeDrawer=closeDrawer @getWaitDeal=getWaitDeal :cur="0"></DrawerList>
      </div>
      <div v-show="cur===1">
        <ReadDrawerList ref="readDrawChild" @closeDrawer=closeDrawer @getWaitRead=getWaitRead @getAllCheck=getAllCheck :cur="1"></ReadDrawerList>
      </div>
      <div class="drawer-footer">
        <Button type="primary" @click="closeDrawer">关闭</Button>
      </div>
    </Drawer>
    <!-- <p style="position: fixed;bottom: 0;left: 50%;color: #A5A4BF;font-size:12px;">Copyright中移动信息技术有限公司</p> -->
    <!-- 关于我们 -->
    <!-- <Modal v-model="modalAbout1" title="关于我们" :footer-hide='true' width="900">
      <ul class="aboutText">
        <li>
          <p class="aboutTitle">产品简介：</p>
          <p>CMBaaS（中国移动区块链服务平台）是中国移动的一个区块链网络项目，用来为省专公司内部提供区块链服务。</p>
        </li>
        <li>
          <p class="aboutTitle">版本号：</p>
          <p>{{version}}</p>
        </li>
        <li>
          <p class="aboutTitle">版权所有 ：</p>
          <p>Copyright © 2022 中移信息技术有限公司</p>
        </li>
      </ul>
    </Modal> -->
  </div>
</template>

<script>
import SideMenu from '_c/side-menu'
// import drawer from '_c/drawer.vue'
import screenfull from 'screenfull'
import { mapState, mapActions } from 'vuex' // mapMutations,
import { getTabNameByRoute, getRouteById, localRead, localSave } from '@/lib/util'
import { unreadList, userLogout, getTenantList, getTenantListId } from '@/api/data'
import { userPermission } from '@/api/user'
import workorderIcon from '@/assets/selfIcons/workorder.png'
import workorderIcons from '@/assets/selfIcons/workorders.png'
import DrawerList from '_c/drawer-list'
import ReadDrawerList from '_c/read-drawer-list'
import SatisfactionSurvey from '_c/satisfaction-survey'
import { version } from '../../static/config.json'
import { customerfrom, getconfig, getCheckRole } from '@/api/contract'
import { encryptedData, decryptData } from '@/lib/encrypt'
import aiRenouImg from '@/assets/ai/renou.png'
import aiRenouImg_p from '@/assets/ai/renou_p.png'
import HttpRequest from '@/lib/axios'
import { getConfigMap } from '@/api/contract'

// 创建axios实例
const axios = new HttpRequest()

export default {
  components: {
    SideMenu,
    DrawerList,
    ReadDrawerList,
    SatisfactionSurvey
  },
  data () {
    return {
      modalAbout1: false,
      customerdis: false,
      customerUrl: '',
      inputvalue: '', // 查询条件
      tablePageParam: { pagetotal: 0, pageSize: 10, pageIndex: 1 },
      id: 1,
      userListData: [],
      version: '', // 版本号
      originArr: [
        // 'browser_index',
        'contract_table',
        'chain_table',
        // 'tenant_table',
        'tenant_admin',
        'user_admin',
        'multilink_admin',
        'survival_direct',
        'node_management',
        'blockchain_network',
        'chain_index',
        'template_table',
        'contract_index'
        // 'ipfs_network'
      ],
      includeArr: [
        // 'browser_index',
        'contract_table',
        'chain_table',
        // 'tenant_table',
        'tenant_admin',
        'user_admin',
        'multilink_admin',
        'survival_direct',
        'node_management',
        'blockchain_network',
        'chain_index',
        'template_table',
        'contract_index'
        // 'ipfs_network'
      ],
      styleBr: {
        wordWrap: 'break-word',
        wordBreak: 'break-all',
        whiteSpace: 'pre-wrap !important'
      },
      workorderIcon, // 图标
      workorderIcons, // 图标
      carouselValue: 0,
      fullscreen: false,
      pageParam: {
        pageSize: 100,
        pageIndex: 1,
        orders: [{
          asc: true,
          column: 'id'
        }]
      },
      visible: false, // Poptip关闭
      workorders: [], // 工单
      isListFlag: false,
      // 是否打开历史记录栏目
      isShowHistory: false,
      drawerVisible: false,
      chainIdList: [],
      chainIdmodel: '',
      collapsed: false,
      getTabNameByRoute,
      userLoginIds: localRead('userLoginId'),
      faceUrls: localRead('faceUrl'),
      cur: 0,
      noticeImg: require('@/assets/notice/notice.png'),
      noticeDotImg: require('@/assets/notice/notice-dot.png'),
      allImg: require('@/assets/notice/all.png'),
      allImgDot: require('@/assets/notice/all-dot.png'),
      waitDeal: 0,
      waitRead: 0,
      allCheck: false,
      findList: ['approvel_admin', 'admin_index', 'storageevidence', 'home','menu_block_chain_manage','menu_system_manage','menu_group_auth_manage','menu_business_approve_manage','menu_log_manage'],
      timeR: null,
      routerSce: '',
      screenShow: false,
      yunyingsrc: '',
      routerSceshow: false,
      yunyingsrcshow: false,
      versionData: {

      },
      // AI助手相关数据
      aiRenouImg, // AI人偶图片
      aiRenouImg_p, // AI人偶趴着的图片
      aiModalVisible: false, // AI弹窗是否可见
      aiAssistantVisible: false, // AI助手图标是否可见
      aiHovered: false, // AI助手是否被悬停
      aiTimer: null, // AI助手定时器
      aiIframeUrl: '', // AI iframe的URL，将在点击时动态获取
      privateKey: '', // 用于解密的私钥
      servizioinfo: {}, // 客服信息
    }
  },
  computed: {
    modalAbout () {
      // 从 Vuex 的 state 中获取 modalAbout 状态
      return this.$store.state.modalAbout;
    },
    isAdmin () {
      if (localStorage.getItem('roleId') == 1) {
        return true
      } else {
        return false
      }
    },
    triggerClasses () {
      return [
        'trigger-icon',
        this.collapsed ? 'rotate' : ''
      ]
    },
    routeSplitArray () { // 后续面包屑路由跳转逻辑开发
      let dealObj = {}
      this.$router.history.current.matched.forEach(element => {
        if (element.meta.title === this.$route.meta.title) {
          dealObj[element.meta.title] = element
        } else {
          dealObj[element.meta.title] = element
        }
      })
      return Object.values(dealObj)
    },
    workordersChanges () {
      if (this.$store.state.tabNav.orderId.indexOf('new') !== -1) {
        return this.unreadList()
      }

      const filterArr = this.workorders.filter(val => val.orderId === this.$store.state.tabNav.orderId)
      if (filterArr.length > 0) {
        return this.unreadList()
      }
      return []
    },
    ...mapState({
      tabList: state => state.tabNav.tabList.filter(item => {
        return item.path !== '/'
      }),
      routers: state => state.router.routers.filter(item => {
        // console.log('routers==>', item)
        // if (item.name === 'home') {
        //   // console.log('routers==>', item.children)
        //   item.children[4].children.splice(1, 3)
        //   item.children[5].children.splice(1, 1)
        // } else if (item.name === 'admin_index')item.children[0].children.splice(1, 1)
        return item.path !== '/' && item.path !== '*' && item.name !== 'login' && item.name !== 'register' && item.name !== 'forget_pwd' && item.name !== 'reset_pwd'
      }),
      userLoginId: state => state.user.userLoginId,
      faceUrl: state => state.user.faceUrl
    }),
    paramsVersion () {
      return window.sessionStorage.getItem('params')
    }
  },
  methods: {
    // 每秒检查一次
    // 关于我们
    getmodalAbout1 () {
      this.$router.push({
        name: 'about_index',
        params: { name1: 'name4' }
      })
    },
    ...mapActions([
      'logout',
      'handleRemove',
      'updateOrderId',
      'resetRouterState'
    ]),
    customer () {
      window.open(this.customerUrl)
    },
    // 设置选中的租户
    setSelectedTenant (tenantId) {
      if (tenantId) {
        this.$store.commit('SET_TENANTUUID', tenantId)
      }
    },
    // 获取租户列表
    gettenantlist () {
      getTenantList().then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.userListData = res.data
          // 如果有传入的租户ID，设置选中状态
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })


      //  回显租户
      getTenantListId().then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          // 如果有传入的租户ID，设置选中状态
          if (res.data) {
            this.setSelectedTenant(res.data.tenantId)
          }

        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },

    // 选择租户
    changetenant (val) {
      this.$store.commit('SET_TENANTUUID', val)
      // console.log("1",val,this.$store.state.tenantUuid)
    },
    // 清空租户
    cleartenant (val) {
      this.$store.commit('SET_TENANTUUID', val)
      // console.log("1",val,this.$store.state.tenantUuid)
    },
    // 打开手册
    openHistory () {
      alert('开启操作手册')
      this.isShowHistory = true
    },
    // 点击全屏展示
    fullScreen () {
      if (!screenfull.isEnabled) {
        this.$Message.warning('当前暂不支持全屏功能，请与管理员联系！')
        return
      }
      screenfull.toggle()
    },
    // 保存系统公钥
    savepublicKey (val) {
      this.$store.commit('SAVE_PUBLICKEY', val)
    },
    //保存公钥
    getpublicKey () {
      let name = 'RSA_PUBLIC_KEY'
      getconfig(name).then((res) => {
        sessionStorage.setItem('RSA_PUBLIC_KEY', res.data.value)
        localSave('publicKey', res.data.value)
        this.savepublicKey(decryptData(res.data.value))
      })
    },
    // 监听关闭事件
    close () {
      alert('关闭操作手册')
      this.isShowHistory = false
    },
    footerOk () {
      console.log('footerok')
    },
    footerCal () {
      this.isShowHistory = false
    },
    handleLogout () {
      sessionStorage.setItem('isLog', true)
      sessionStorage.removeItem('detailFrom')
      sessionStorage.removeItem('fromName')
      sessionStorage.removeItem('recordCurrent')
      userLogout().then(res => {
        this.initUser(res)
      }).catch(() => {
        this.initUser()
      })
    },
    initUser (res) {
      this.logout()
      this.resetRouterState()
      this.$store.commit('REMOVE_ALLTAB')

      // 4A来源跳转到退出页面
      if (res.data.userSource == 1) {
        this.$router.push({
          name: 'Logout',
          params: {
            userSource: 1
          }
        })
      } else { // 否则跳登录页面
        this.$router.push({
          name: 'login'
        })
      }
    },
    handleCollapsed () {
      this.collapsed = !this.collapsed
    },
    handleClickTab (id) {
      let route = getRouteById(id)
      // console.log(getRouteById(id))
      this.$router.push(route)
    },
    handleTabRemove (id, event) {
      event.stopPropagation()
      // console.log(id, event,'pppp',this.$route)
      // const dealArr = JSON.parse(JSON.stringify(this.originArr))
      if (this.includeArr.includes(id)) {
        this.includeArr = this.includeArr.filter(val => val !== id)
      }
      // console.log(this.includeArr,id,'sss')
      this.handleRemove({
        id,
        $route: this.$route
      }).then(nextRoute => {
        this.$router.push(nextRoute)
      })
    },
    // 中移大屏跳转
    handleScreen () {
      if (this.routerSce) {
        let url = this.routerSce + localRead('token')
        window.open(url)
        // this.$router.push(url)
      }
    },
    //跳转运营大屏
    handleyunyingScreen () {
      if (this.yunyingsrc) {
        let url = this.yunyingsrc + localRead('token')
        window.open(url)
        // this.$router.push(url)
      }
    },
    parentSelect (name) {
      if (this.originArr.includes(name) && !this.includeArr.includes(name)) {
        this.includeArr.push(name)
      }
    },
    labelRender (item) {
      // console.log(item.meta.title)
      if (item.meta.title !== 'Dashboard') {
        return h => {
          return (
            <div>
              <span>{item.meta.title}</span>

              <icon nativeOn-click={this.handleTabRemove.bind(this, getTabNameByRoute(item))} type="md-close-circle" style="line-height:10px;"></icon>
            </div>
          )
        }
      } else {
        return h => {
          return (
            <div>
              <span>{item.meta.title}</span>
            </div>
          )
        }
      }
    },
    handleUserInfo () {
      this.$router.push({
        name: 'user_info'
      })
    },
    handleCM () {
      this.$router.push({
        name: 'about_index'
      })
    },
    handleIpfs () {
      this.$router.push({
        name: 'ipfs_network'
      })
    },
    goRouterWorkOrderNew (name) {
      const menuDetail = Array.from(this.$router.history.current.matched).map(val => val.meta.title)
      this.$router.push({
        name,
        params: {
          menuDetail
        }
      })
    },
    // 路由跳转
    goRouter (name, orderId) {
      if (orderId) {
        this.workorders = this.workorders.filter(val => val.orderId !== orderId)
        this.updateOrderId(orderId)
        if (this.workorders.length > 0) {
          this.isListFlag = true
        } else {
          this.isListFlag = false
        }
        this.$router.push({
          name,
          params: {
            orderId: orderId
          }
        })
      } else if (name) {
        if (name === 'user_index') {
          this.handleUserInfo()
        } else if (name === 'ipfs_index') {
          this.handleIpfs()
        } else {
          this.$router.push({
            name
          })
        }
        //   }// else {
        // //   if (name === 'user_index') {
        // //     this.handleUserInfo()
        // //   } else {
        // //     this.$router.push({
        // //       name
        // //     })
        // //   }
        // // }
      }

      this.visible = false
    },
    // 初始化查询未读表单
    unreadList () {
      unreadList().then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.workorders = res.data
          if (this.workorders.length > 0) {
            this.isListFlag = true
          } else {
            this.isListFlag = false
          }
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({ background: true, closable: closable, content: content })
    },
    openDrawer () {
      this.$refs.drawChild.initPageParam()
      this.$refs.readDrawChild.initPageParam()
      this.cur = 0
      this.drawerVisible = true
      this.$refs.drawChild.getWaitDeal()
      this.$refs.readDrawChild.getWaitRead()
    },
    closeDrawer () {
      this.$refs.drawChild.initPageParam()
      this.$refs.readDrawChild.initPageParam()
      this.drawerVisible = false
      this.cur = 0
    },
    clickCur () {
      // console.log('点击了')
      this.cur = this.cur === 0 ? 1 : 0
      if (this.cur === 0) {
        this.$refs.drawChild.initPageParam()
        this.$refs.drawChild.getWaitDeal()
      } else {
        this.$refs.readDrawChild.initPageParam()
        this.$refs.readDrawChild.getWaitRead()
      }
    },
    getWaitRead (val) {
      this.waitRead = val
    },
    getWaitDeal (val) {
      this.waitDeal = val
    },
    clickAll () {
      this.$refs.readDrawChild.clickAll()
    },
    getAllCheck (newVal) {
      this.allCheck = newVal
    },
    closeAll () {
      if (this.$route.name === 'dashboard') {
        this.closeOther()
      } else if (this.$store.state.tabNav.tabList.length > 1 || this.$route.name !== 'dashboard') {
        this.$store.commit('REMOVE_ALLTAB')
        this.$router.push({
          name: 'dashboard'
        })
      }
    },
    closeOther () {
      if ((this.$store.state.tabNav.tabList.length >= 2 && this.$route.name !== 'dashboard') || this.$route.name === 'dashboard') {
        this.$store.commit('REMOVE_OTHER', this.$route.name)
      }
    },
    // AI助手相关方法
    openAiModal() {
      this.aiModalVisible = true;
    },

    // 处理AI助手鼠标进入事件
    handleAiMouseEnter() {
      this.aiHovered = true;
      // 清除之前的定时器
      if (this.aiTimer) {
        clearTimeout(this.aiTimer);
        this.aiTimer = null;
      }
    },

    // 处理AI助手鼠标离开事件
    handleAiMouseLeave() {
      // 设置10秒后回到原状态
      this.aiTimer = setTimeout(() => {
        this.aiHovered = false;
        this.aiTimer = null;
      }, 6000);
    },

    // 获取AI配置并打开弹窗
    async getAiConfigAndOpen() {
      // 首先检查当前用户是否有权限使用AI助手
      if (!this.aiAssistantVisible) {
        // this.msgInfo('warning', '您当前没有使用AI助手的权限', true);
        return;
      }
      //  this.openAiModal();

      try {
        // 获取跳转地址
        const jumpRes = await getconfig('SHEN_TONG_CONFIG_JUMP');
        if (!jumpRes.data || !jumpRes.data.value) {
          this.msgInfo('warning', '获取AI助手跳转地址失败', true);
          return;
        }
        const jumpUrl = decryptData(jumpRes.data.value, this.privateKey);
        console.log('跳转链接:', jumpUrl);

        // 获取token的请求连接
        const loginRes = await getconfig('SHEN_TONG_CONFIG_LOGIN');
        if (!loginRes.data || !loginRes.data.value) {
          this.msgInfo('warning', '获取AI助手登录参数失败', true);
          return;
        }
        const loginParam = decryptData(loginRes.data.value, this.privateKey);
        console.log('获取token链接:', loginParam);

        // 使用Axios发送请求获取token
        const tokenRes = await axios.request({
          url: loginParam,
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        console.log('AI助手token响应:', tokenRes);

        if (!tokenRes.data) {
          this.msgInfo('warning', '获取AI助手token失败', true);
          return;
        }

        let token = '';
        console.log('准备提取token，响应数据类型:', tokenRes.data, '数据:', tokenRes.data);
        // 根据图片展示的响应格式
        token = tokenRes.data;
        console.log('提取到token(方式1):', token);

        if (!token) {
          this.msgInfo('warning', '无法从响应中提取AI助手token', true);
          console.log('AI token响应:', tokenRes.data);
          return;
        }

        // 拼接完整URL，带上token参数
        this.aiIframeUrl = jumpUrl + token;
        // 打开弹窗
        this.openAiModal();

      } catch (error) {
        this.msgInfo('error', '获取AI助手配置出错: ' + error, true);
      }
    },
  },

  mounted () {


    // 判断用户角色控制按钮权限

    // userPermission().then(res => {
    //   if (res.data) {
    //     localSave('userPermission', JSON.stringify(res.data))
    //   }
    // }).catch((error) => {
    //   this.msgInfo('error', error.message, true)
    // })

    this.getpublicKey()
    getCheckRole().then(res => {
      if (res.data) {
        this.screenShow = true
      } else {
        this.screenShow = false
      }
    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })
    // console.log(1111111111);
    let nameIde = 'JUMP_TO_DAPING'
    //运维视图
    getconfig(nameIde).then((res) => {
      console.log(res)
      if (res.data) {
        this.routerSceshow = true
        if (res.data.value) {
          this.routerSce = res.data.value
        }
      } else {
        this.routerSceshow = false
      }
    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })
    //运营视图
    getconfig('OVERVIEW_YUNYING_OPEN_URL').then((res) => {
      if (res.data) {
        this.yunyingsrcshow = true
        if (res.data.value) {
          this.yunyingsrc = res.data.value
        }
      } else {
        this.yunyingsrcshow = false
        console.log(res)
      }
    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })

    console.log(window)
    // let custdata = {
    //   name: 'CUSTOMER_FROM_URL',
    //   status: 'ENABLE'
    // }
    // customerfrom(custdata).then(res => {
    //   if (res) {
    //     this.customerUrl = res
    //   } else {
    //     this.customerdis = true
    //   }
    // })
    if (localStorage.getItem('roleId') == 1) {
      this.gettenantlist()

      // 示例：如果需要根据ID选中某个租户，可以这样调用
      // this.setSelectedTenant('租户ID')
    }
    this.cleartenant()
    // this.isShowHistory = true
    this.unreadList()

    if (!this.includeArr.includes('contract_table')) {
      this.includeArr.push('contract_table')
    }
    // console.log(this.includeArr)
    // 引入版本号

    this.version = version
    const storedVersionData = JSON.parse(sessionStorage.getItem('versionData')) || {};
    this.versionData = {
      version: storedVersionData.version || '产品版本：1.5.0',
      copyright: storedVersionData.copyright || 'Copyright © 2022 中移信息技术有限公司'
    };

    // 获取解密用的私钥
    getconfig('DEFAULT_WEB_KEY1').then((res) => {
      if (res.data && res.data.value) {
        let pkeyone = res.data.value;
        
        getconfig('DEFAULT_WEB_KEY2').then((res2) => {
          if (res2.data && res2.data.value) {
            let pkeytwo = res2.data.value;
            this.privateKey = pkeyone + pkeytwo;
          }
        }).catch((error) => {
          this.msgInfo('error', '获取解密私钥(2)出错: ' + error.message, true);
        });
      }
    }).catch((error) => {
      this.msgInfo('error', '获取解密私钥(1)出错: ' + error.message, true);
    });
    
    // 获取AI助手是否开启的配置
    getconfig('SHEN_TONG_OPEN').then((res) => {
      if (res.data && res.data.value) {
        const configValue = res.data.value;
        // 情况1：全部开放
        if (configValue === 'true') {
          this.aiAssistantVisible = true;
        } 
        // 情况2：全部关闭
        else if (configValue === 'false') {
          this.aiAssistantVisible = false;
        } 
        // 情况3：按用户名判断
        else {
         
          // 获取当前用户名
          const currentUserName = this.userLoginId || this.userLoginIds;
        
          // 根据逗号分隔配置值，得到允许使用的用户列表
          const allowedUsers = configValue.split(',').map(name => name.trim());
          // 判断当前用户是否在允许列表中
          this.aiAssistantVisible = allowedUsers.includes(currentUserName);
          console.log(currentUserName,'configValue',configValue,this.aiAssistantVisible)
        }
      } else {
        this.aiAssistantVisible = false;
      }
    }).catch((error) => {
      this.msgInfo('error', error.message, true);
      this.aiAssistantVisible = false;
    })
    
    // 获取客服信息
    let info = {
      configNames: ['CUSTOMER_SERVICE_EMAIL', 'CUSTOMER_SERVICE_PHONE', 'CUSTOMER_SERVICE_TIME']
    }
    getConfigMap(info).then(res => {
      this.servizioinfo = res.data
    }).catch((error) => {
      this.msgInfo('error', error.message, true);
    })

  },
  beforeDestroy() {
    // 清理AI助手定时器
    if (this.aiTimer) {
      clearTimeout(this.aiTimer);
      this.aiTimer = null;
    }
  },
  beforeRouteLeave (to, from, next) {
    if (from.name === 'dashboard' && to.name === 'contract_table' && to.params.modal) {
      this.includeArr = this.includeArr.filter(val => val !== 'contract_table')
    } else {
      if (this.originArr.includes(to.name) && !this.includeArr.includes(to.name)) {
        this.includeArr.push(to.name)
      }
    }
    next()
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      // 通过 `vm` 访问组件实例
      if (vm.originArr.includes(to.name) && !vm.includeArr.includes(to.name)) {
        vm.includeArr.push(to.name)
      }
      // console.log(to.name, 'this.includeArr====beforeRouteEnter', vm.includeArr)
      // next()
    })
  },
  watch: {
    modalAbout (newVal) {
      if (newVal === true) {
        this.modalAbout1 = true;
      } else {
        this.modalAbout1 = false;
      }
    }
  }
}
</script>

<style lang="less" scoped>
.circle-icon {
  width: 6px;
  height: 6px;
  display: inline-block;
  background: #ed4014;
  border-radius: 50%;
  vertical-align: middle;
  position: relative;
  top: -1px;
}
.work-api {
  .work-tip-main {
    box-sizing: border-box;
    padding: 10px 0;
    border-bottom: 1px solid #e8eaec;
    .work-tip {
      p {
        color: #43425d;
      }
      .btn {
        margin-left: 20px;
        color: #3d73ef;
        opacity: 0.8;
        &:hover {
          opacity: 1;
        }
      }
    }
  }
  .p-btn {
    margin-top: 10px;
    span {
      color: #3d73ef;
      opacity: 0.8;
      &:hover {
        opacity: 1;
      }
    }
  }
}
.layout-wrapper {
  height: 100%;
  .ivu-layout {
    background-color: #f2f6fd;
  }
  .layout-outer {
    height: 100%;
    .ivu-layout-header {
      height: 56px;
      line-height: 56px;
    }
    .header-wrapper {
      display: inline-block;
      background: #fff;
      box-shadow: 0 1px 1px 1px rgba(0, 0, 0, 0.1);
      padding: 0 22px;

      .trigger-icon {
        padding-right: 10px;
        padding-top: 7px;
        margin-right: 5px;
        cursor: pointer;
        color: #0c0c0c;
        transition: transform 0.3s ease;
        &.rotate {
          transform: rotateZ(-90deg);
          transition: transform 0.3s ease;
        }
      }
      // .rotate{
      //   // vertical-align:0 !important;
      //   vertical-align: 0.035em;
      // }
    }
    .sider-outer {
      height: 100%;
      overflow-x: hidden;
      overflow-y: scroll;
      // margin-right: -2px;
      // background-color:#111945;
      background: url("../assets/img/sider-bg.png");
      background-size: cover;
      background-repeat: no-repeat;
      .ivu-layout-sider-children {
        margin-right: -20px;
        overflow-y: scroll;
        overflow-x: hidden;
      }
    }
    .sider-outer::-webkit-scrollbar {
      width: 2px;
    }
    .content-con {
      padding: 0;
      .ivu-tabs-bar {
        margin-bottom: 0;
      }
    }
    .page-card {
      min-height: ~"calc(100vh - 140px)";
    }
  }
  .time {
    font-size: 14px;
    font-weight: bold;
  }
  .content {
    padding-left: 5px;
  }
}
/deep/.ivu-menu-dark.ivu-menu-vertical
  .ivu-menu-item-active:not(.ivu-menu-submenu) {
  color: #fff;
  background: #2d8cf0;
}
/deep/.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item:hover {
  background: transparent;
}
/deep/.ivu-menu-dark.ivu-menu-vertical
  .ivu-menu-item-active:not(.ivu-menu-submenu),
.ivu-menu-dark.ivu-menu-vertical
  .ivu-menu-item-active:not(.ivu-menu-submenu):hover {
  background: #2d8cf0;
}
/deep/.view-tab {
  .ivu-tabs-nav-scroll {
    height: 38px;
    background: #dfe4f8;
    opacity: 0.7;
  }
  .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
    border-radius: 0;
    background: #fff;
  }
  .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active {
    border-top: 1px solid #3399ff;
    background: #f2f6fd;
  }
  .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active:before {
    content: "";
    display: block;
    width: 100%;
    height: 1px;
    background: #3399ff;
    position: absolute;
    top: 0;
    left: 0;
  }
}
.layout-img-container {
  display: inline-block;
  //height: 56px;
  vertical-align: sub;
  margin-left: 1px;
  margin-right: 12px;
  .layout-img {
    display: inline-flex;
    width: 16px;
    height: 16px;
    position: relative;
    bottom: 1px;
    cursor: pointer;
  }
}
.layout-img:hover {
  opacity: 0.6;
}
/deep/.ivu-card-shadow {
  box-shadow: none;
}
/deep/.ivu-card:hover {
  box-shadow: none !important;
  .ivu-card.ivu-card-shadow:hover {
    box-shadow: none !important;
  }
}

/* AI助手样式 */
.ai-assistant {
  position: fixed;
  right: -40px; /* 向右偏移，只露出一半 */
  bottom: 60px;
  z-index: 1000;
  cursor: pointer;
  transition: all 0.3s ease;

  &.ai-active {
    right: 20px; /* 激活时完全显示 */
    transform: scale(1.05);
  }
}
.renou{
  width: 80px;
  height: 80px;
  border-radius: 50%;
  transition: all 0.3s ease;
}
.ai-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.custom-modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-modal {
  width: 90%;
  height: 80%;
  min-width: 400px;
  min-height: 300px;
  max-width: 95vw;
  max-height: 95vh;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  resize: both;
  overflow: auto;
}

.custom-modal-header {
  height: 50px;
  line-height: 50px;
  padding: 0 16px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.close-icon {
  cursor: pointer;
  font-size: 20px;
  &:hover {
    color: #2d8cf0;
  }
}

.custom-modal-body {
  flex: 1;
  overflow: hidden;
}

.drawer-footer {
  //border-top: 0.1px solid #ddd;
  display: flex;
  justify-content: flex-end;
  bottom: 10px;
  position: fixed;
  right: 20px;
}
.login_header {
  height: 50px;
  font-size: 16px;
  border-bottom: 1px solid #e2e2e5;
  margin: 0px -16px 0px -16px;
}
.login_header_1 {
  text-align: center;
  margin-right: 20px;
  cursor: pointer;
  width: 50px;
  display: inline-block;
  margin-left: 16px;
  color: #0c0c0c;
}
.login_header_2 {
  text-align: center;
  cursor: pointer;
  padding-top: 15px;
  width: 50px;
  display: inline-block;
  color: #0c0c0c;
}
.login_header_3 {
  float: right;
  padding: 15px 30px 0 0;
}
.active {
  color: #3d73ef;
  padding-bottom: 8px;
  border-bottom: 3px solid #3d73ef;
  cursor: pointer;
}
/deep/.ivu-badge {
  display: inline-flex;
}
.img-style {
  cursor: pointer;
}
.img-style-none {
  cursor: default;
  pointer-events: none;
}
.img-style:hover {
  opacity: 0.6;
}
.full-screen:hover {
  opacity: 0.6;
}
.tooltip-style {
  /deep/.ivu-tooltip-popper {
    cursor: default;
    top: 40px !important;
  }
}
.footer {
  font: 12px Microsoft YaHei;
  text-align: center;
  color: #a5a4bf;
}
.ivu-layout-footer {
  padding: 5px 10px 5px 10px;
}

.click-to {
  cursor: pointer;
}
.click-to:hover {
  color: #3399ff;
}
.aboutText {
  li {
    display: flex;
    margin: 20px 0;
    .aboutTitle {
      text-align-last: justify;
      width: 80px;
    }
  }
}
.tags {
  text-align: center;
  color: #fff;
  text-shadow: 0px 0px 0.572917vw #1310b9;
  font-family: "zhankugaoduan";
  font-size: 1.041667vw;
  font-style: normal;
  font-weight: 400;
  line-height: 1.302083vw;
  white-space: nowrap;
  font-size: 14px;
  display: inline-flex;

  height: 16px;
  position: relative;
  bottom: 1px;
  cursor: pointer;
}

/* AI客服样式 */
.p_elail {
  display: flex;
  .img_elail {
    width: 30px;
    height: 30px;
    display: inline-block;
    img {
      width: 100%;
    }
  }
  .info {
    color: #6190ff;
    line-height: 30px;
    font-size: 15px;
  }
}
.row {
  white-space: pre-wrap;
  display: inline-block;
  word-wrap: break-word;
  width: 80%;
}
.ivu-divider-horizontal{
  margin:5px 0;
}
</style>
