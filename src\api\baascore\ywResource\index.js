import axios from '../../index'
const BASEURL = '/cmbaas/portal/fabric/CommonAPI'
// 集群资源 //无用
export function getClusterMonitor (query) {
  return axios.request({
    url: '/baasmonitor/clusterMonitor/getClusterMonitor',
    method: 'get',
    params: query
  })
}

/* // 选择主机
export function getClusterHostInfoList(query) {
    return axios.request({
        url: BASEURL + '/CommonAPI?msgType=getClusterHostInfoList',
        method: 'get',
        params: query
    })
} */

// 主机资源 无用
export function getHostNodeMonitor (query) {
  return axios.request({
    url: '/baasmonitor/clusterMonitor/getHostNodeMonitor',
    method: 'get',
    params: query
  })
}

// 链节点资源选择节点
export function getChainNodeNameList (query) {
  return axios.request({
    url: BASEURL + '?msgType=chainOrg%23getChainNodeNameList',
    method: 'get',
    params: query
  })
}

// 链节点
export function getChainPodList (query) {
  return axios.request({
    url: BASEURL + '?msgType=chainOrg%23getChainPodList',
    method: 'get',
    params: query
  })
}

// 链节点
/*
export function getChainContainerMonitor(query) {
    return axios.request({
        url: BASEURL + '/CommonAPI?msgType=getChainContainerMonitor',
        method: 'get',
        params: query
    })
} */
