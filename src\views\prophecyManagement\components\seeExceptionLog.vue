<!--
  aturun
  异常日志
  2021/10/21

-->
<template>
  <el-dialog
      class="dialog_sty see_exception_log"
      title="异常日志"
      :visible.sync="Visible"
      width="700px"
      destroy-on-close
      :close-on-click-modal="false"
      @opened="open">
    <div class="dialog_content">
      {{textInfo.exceptionDesc}}
    </div>
  </el-dialog>
</template>

<script>
import * as api from "../api";

export default {
  name: "seeExceptionLog",
  components: {},
  props:[],
  data() {
    return {
      Visible:false,
      form:{
      },
      textInfo:{},
      rules: {
        name: [
          {required: true, message: '请输入用户名称', trigger: 'blur'},
        ],
      }
    }
  },
  watch: {},
  created() {

  },
  mounted() {
  },
  methods: {
    open(){
      api.getExceptionLogDetails(
          {logId:this.form.logId}
      ).then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.textInfo=res.result
      })
    }
  },

}
</script>

<style lang="less" scoped>
.see_exception_log{
  .dialog_content{
    height: 400px;
    overflow: scroll;
    span{
      white-space:nowrap;
    }
  }
}
</style>
