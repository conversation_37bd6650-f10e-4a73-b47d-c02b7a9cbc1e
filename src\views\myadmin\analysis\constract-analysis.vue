<template>
  <div class="analysis">
    <Row style="padding:20px" class="selectStyle">
      <Col>
      <Select :class="className" @on-open-change="selectClassName" v-model="chainId" placeholder="选择目标链" @on-change="changeChainId" style="width:280px;">
        <Option v-for="item in chainIdList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
        <Option :value="chainId" :label="chainId" :disabled="true" v-if="pageParam.pageSize < pageParam.pageTotal">
          <span @mouseover="handleReachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="cursor:pointer;margin-left:5px;"></span>
        </Option>
      </Select>
      </Col>
    </Row>
    <Divider></Divider>
    <Row style="padding:20px" class="selectStyle">
      <Col span="12"><span class="time-scope">{{pieTitle}}</span></Col>
      <Col span="5">
      <span class="time-scope">时间范围</span>
      <Select v-model="timeScope1" style="width:80px;" @on-change="changeTimeScope1">
        <Option v-for="item in timeScopeList" :value="item.key" :key="item.key">{{ item.value }}</Option>
      </Select>
      </Col>
    </Row>
    <Row>
      <div class="left-area">
        <Pie :pieData="pieData1" pieHeight="40vh" pieWidth="70vh" :type="1" v-if="pieData1 && pieData1.length > 0" @hoverEvent="clickChange"></Pie>
        <div v-else class="data-none">
          <img :src="imagesurl">
          <p class="title-none" style="">暂无数据</p>
        </div>
      </div>
      <div class="right-area">
        <Polylines :polylineData="polylineData" :polylineXaxis="polylineXaxis" :legendData="polylineLegend" polylineHeight="40vh" polylineWidth="86vh" v-if="polylineData && polylineData.length > 0"></Polylines>
        <div v-else class="data-none">
          <img :src="imagesurl">
          <p class="title-none" style="">暂无数据</p>
        </div>
      </div>
    </Row>
    <Row style="padding:20px" class="selectStyle">
      <Col span="4" class="selectStyle2">
      <Select v-model="contractType" style="width:165px;" class="border-none" @on-change="changeContractType">
        <Option v-for="item in contractTypeList" :value="item.key" :key="item.key">{{ item.value + commomName }}</Option>
      </Select>
      </Col>
      <Col span="5" offset="2">
      <span class="time-scope">时间范围</span>
      <Select v-model="timeScope2" style="width:80px;" @on-change="changeTimeScope2">
        <Option v-for="item in timeScopeList" :value="item.key" :key="item.key">{{ item.value }}</Option>
      </Select>
      </Col>
      <Col span="4" offset="1" class="selectStyle2">
      <Select v-model="contractName" style="width:155px;" class="border-none" @on-change="changeContractName(contractName)">
        <Option v-for="item in pieData2" :value="item.name" :key="item.name">{{ item.name + '调用量' }}</Option>
      </Select>
      </Col>
      <Col span="5" offset="3">
      <span class="time-scope">时间范围</span>
      <Select v-model="timeScope3" style="width:80px;" @on-change="changeTimeScope3">
        <Option v-for="item in timeScopeList" :value="item.key" :key="item.key">{{ item.value }}</Option>
      </Select>
      </Col>
    </Row>
    <Row>
      <div class="left-area">
        <Pie :pieData="pieData2" @hoverEvent="change($event)" :type="2" pieHeight="40vh" pieWidth="70vh" v-if="pieData2 && pieData2.length > 0"></Pie>
        <div v-else class="data-none">
          <img :src="imagesurl">
          <p class="title-none" style="">暂无数据</p>
        </div>
      </div>
      <div class="right-area">
        <Areas :areasData="areasData" :areasXaxis="areasXaxis" :contractName="contractName + '调用量'" areasHeight="40vh" areasWidth="86vh" v-if="areasData && areasData.length > 0"></Areas>
        <div v-else class="data-none">
          <img :src="imagesurl">
          <p class="title-none" style="">暂无数据</p>
        </div>
      </div>
    </Row>
  </div>
</template>

<script>
import { getChainIdList, getAllContractTypeAnalysis, getHistoryAnalysis, getContractTypeAnalysis, getAccountNameAnalysis, getTimeScope } from '@/api/data'
import Pie from '_c/pie'
import Areas from '_c/areas'
import Polylines from '_c/polylines'
import { mapGetters } from 'vuex'
export default {
  name: 'constract-analysis',
  components: {
    Pie,
    Areas,
    Polylines
  },
  data () {
    return {
      imgUrl: require('@/assets/img/arrow.png'),
      imagesurl: require('@/assets/img/null.png'),
      pieData1: [],
      pieData2: [],
      polylineData: [],
      saveData: [],
      polylineLegend: [],
      polylineXaxis: [],
      areasData: [],
      areasXaxis: [],
      pieTitle: '各能力合约调用量',
      chainIdList: [],
      chainId: 0,
      pageParam: { pageTotal: 0, pageSize: 5, pageIndex: 1 },
      scollshow: false,
      useNameDisable: false,
      timeScope1: '',
      timeScope2: '',
      timeScope3: '',
      timeScopeList: [],
      contractType: '',
      contractTypeDesc: '',
      contractTypeList: [],
      contractName: '',
      accountName: '',
      contractNameList: [],
      commomName: '合约调用量',
      className: 'select-style1',
      clickName: ''
    }
  },
  computed: {
    ...mapGetters(['getDict'])
  },
  methods: {
    selectClassName () {
      this.className = this.className === 'select-style1' ? 'select-style2' : 'select-style1'
    },
    changeContractType () {
      this.getContractTypeAnalysisList()
    },
    changeChainId () {
      this.getAllContractTypeAnalysisList()
      this.getHistoryAnalysisList()
      this.getContractTypeAnalysisList()
    },
    changeTimeScope1 () {
      this.timeScope2 = this.timeScope1
      this.getAllContractTypeAnalysisList()
      this.getHistoryAnalysisList()
      this.changeTimeScope2()
    },
    changeTimeScope2 () {
      this.timeScope3 = this.timeScope2
      this.getContractTypeAnalysisList()
    },
    changeTimeScope3 () {
      if (this.pieData2.length > 0) {
        this.getAccountNameAnalysisList()
      } else {
        this.msgInfo('warning', '当前能力下的链账户列表为空，查询无结果！', true)
      }
    },
    changeContractName (item) {
      if (item) {
        for (var i in this.pieData2) {
          if (this.pieData2[i].name === item) {
            this.contractNameList = this.pieData2[i].nameList
          }
        }
        this.getAccountNameAnalysisList()
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    handleReachBottom () {
      this.pageParam.pageSize += 5
      this.getChainList(true)
    },
    change (data) {
      this.contractName = data.name
      this.contractNameList = data.nameList
      this.timeScope3 = this.timeScope2
      this.getAccountNameAnalysisList()
    },
    clickChange (data) {
      this.transferPolylineData(data)
      this.transferContractTypeDesc(data.name)
      this.timeScope2 = this.timeScope1
      this.timeScope3 = this.timeScope2
      this.getContractTypeAnalysisList()
    },
    // 能力类型
    getContractTypeList () {
      // vuex的实现方式
      this.$store.dispatch('getOptions', 'CONTRACT_TYPE')
        .then((result) => {
          if (Object.prototype.toString.call(result) === '[object Boolean]') {
            this.contractTypeList = this.getDict.CONTRACT_TYPE
          } else {
            this.contractTypeList = result
          }
          if (this.contractTypeList.length > 0 && this.contractTypeList[0].value) {
            this.contractType = this.contractTypeList[0].key
            this.getContractTypeAnalysisList()
          }
        })
        .catch(error => {
          this.msgInfo('error', error.message, true)
        })
      // getContractType().then(res => {
      //   if (res.code === '00000') {
      //     this.contractTypeList = res.data
      //     if (this.contractTypeList[0].contractType) {
      //       this.contractType = this.contractTypeList[0].contractType
      //       this.getContractTypeAnalysisList()
      //     }
      //   } else {
      //     this.msgInfo('error', res.message, true)
      //   }
      // }).catch(error => {
      //   this.msgInfo('error', error.message, true)
      // })
    },
    // 链列表
    getChainList (tag) {
      getChainIdList(this.pageParam).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.chainIdList = res.data.records
          this.pageParam = {
            pageTotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          if (this.chainIdList.length > 0) {
            this.chainId = this.chainIdList[0].chainId
            if (!tag) {
              this.getTimeScopeList()
            }
          }
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询各能力合约调用量统计
    getAllContractTypeAnalysisList () {
      getAllContractTypeAnalysis(this.chainId, this.timeScope1).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          if (res.data.callerList) {
            this.pieData1 = res.data.callerList
          } else {
            this.pieData1 = []
          }
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询各能力合约调用量历史数据
    getHistoryAnalysisList () {
      getHistoryAnalysis(this.chainId, this.timeScope1).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          if (res.data) {
            this.polylineData = res.data.series
            this.saveData = res.data.series
            this.polylineXaxis = res.data.xaxis
            this.polylineLegend = res.data.legend
          }
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询某能力合约调用量统计
    getContractTypeAnalysisList () {
      getContractTypeAnalysis(this.chainId, this.contractType, this.timeScope2).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          if (res.data.callerList) {
            this.pieData2 = res.data.callerList
            if (this.pieData2[0].name) {
              this.contractName = this.pieData2[0].name
              this.contractNameList = this.pieData2[0].nameList
              this.getAccountNameAnalysisList()
            }
          } else {
            this.pieData2 = []
            this.contractName = ''
            this.contractNameList = []
            this.areasData = []
          }
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询某能力下的某个合约调用量历史数据
    getAccountNameAnalysisList () {
      getAccountNameAnalysis(this.chainId, this.contractNameList, this.timeScope3).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.areasData = []
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.areasData = res.data.series
          this.areasXaxis = res.data.xaxis
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询所有时间范围
    getTimeScopeList () {
      // vuex的实现方式
      this.$store.dispatch('getOptions', 'TIME_SCOPE')
        .then((result) => {
          if (Object.prototype.toString.call(result) === '[object Boolean]') {
            this.timeScopeList = this.getDict.TIME_SCOPE
          } else {
            this.timeScopeList = result
          }
          if (this.timeScopeList && this.timeScopeList[0].value) {
            this.timeScope1 = this.timeScopeList[0].key
            this.timeScope2 = this.timeScopeList[0].key
            this.timeScope3 = this.timeScopeList[0].key
            this.getHistoryAnalysisList()
            this.getAllContractTypeAnalysisList()
            this.getContractTypeList()
          }
        })
        .catch(err => { console.log(err, 'err') })
      // 过去的
      // getTimeScope().then(res => {
      //   if (res.code !== '00000') this.msgInfo('warning', res.message, true)
      //   else {
      //     this.timeScopeList = res.data
      //     if (this.timeScopeList[0].timeScopeDesc) {
      //       this.timeScope1 = this.timeScopeList[0].timeScope
      //       this.timeScope2 = this.timeScopeList[0].timeScope
      //       this.timeScope3 = this.timeScopeList[0].timeScope
      //       this.getHistoryAnalysisList()
      //       this.getAllContractTypeAnalysisList()
      //       this.getContractTypeList()
      //     }
      //   }
      // }).catch(error => {
      //   this.msgInfo('error', error.message, true)
      // })
    },
    transferContractTypeDesc (val) {
      for (var item in this.contractTypeList) {
        if (this.contractTypeList[item].contractTypeDesc === val) {
          this.contractType = this.contractTypeList[item].contractType
        }
      }
    },
    transferPolylineData (data) {
      if (data.selected) {
        for (var item in this.saveData) {
          if (this.saveData[item].name === data.name) {
            this.polylineData = []
            this.polylineData.push(this.saveData[item])
          }
        }
      } else {
        this.polylineData = this.saveData
      }
      this.clickName = data.name
    }
  },
  watch: {
    pieData1: {
      handler (newVal) {
        this.pieData1 = newVal
      },
      deep: true,
      immediate: false
    },
    pieData2: {
      handler (newVal) {
        this.pieData2 = newVal
      },
      deep: true,
      immediate: false
    },
    polylineData: {
      handler (newVal, oldVal) {
        this.polylineData = newVal
      },
      deep: true,
      immediate: false
    },
    areasData: {
      handler (newVal, oldVal) {
        this.areaData = newVal
      },
      deep: true,
      immediate: false
    }
  },
  mounted () {
    this.getChainList()
    // this.$Message.config({
    //   top: 250,
    //   duration: 2
    // })
  },
  destroyed () {
    clearInterval(this.timer)
  }
}
</script>
<style lang="less" scoped>
.analysis {
  width: 100%;
  height: 100%;
  .commom {
    text-align: center;
    display: table;
  }
  .middle-center {
    margin: 0 auto;
    vertical-align: middle;
  }
  .data-none {
    padding: 40px;
    .title-none {
      color: #adadad;
      font-size: 8px;
    }
  }
  .left-area {
    float: left;
    .commom;
    width: 45%;
    height: 50%;
    .select-area1 {
      .middle-center;
      margin-top: 10px;
    }
    /deep/.pie {
      .middle-center;
    }
  }
  .selectStyle {
    /deep/.ivu-select-selection {
      border: 1px solid #4b98eb;
    }
  }
  .time-scope {
    margin-right: 5px;
    font-weight: bold;
    font-size: 14px;
  }
  .right-area {
    float: right;
    height: 50%;
    padding-left: 10px;
    .commom;
    width: 55%;
    /deep/.polyline {
      .middle-center;
    }
    /deep/.areas {
      .middle-center;
    }
    .select-area2 {
      .middle-center;
    }
  }

  .selectStyle2 {
    /deep/.ivu-select-selection {
      border: 1px solid #4b98eb;
    }
    .border-none {
      /deep/.ivu-select-selection {
        border: none;
      }
    }
  }

  /deep/.ivu-select-item-disabled:hover {
    cursor: pointer;
  }
  /deep/.ivu-select-input {
    color: #57a3f3;
  }

  /deep/.ivu-icon-ios-arrow-down:before {
    color: #57a3f3;
  }
  /deep/.ivu-select {
    text-align: left;
  }
}
/deep/.ivu-divider-horizontal {
  margin: 0;
}
/deep/.select-style1 {
  .ivu-select-arrow {
    padding: 9px;
    margin-right: -9px;
    background-color: #57a3f3;
    color: #fff;
    border-radius: 0 5px 5px 0;
    transition: none;
  }
  .ivu-icon-ios-arrow-down:before {
    color: #fff;
  }
}
/deep/.select-style2 {
  .ivu-select-arrow {
    padding: 9px;
    margin-right: -9px;
    background-color: #57a3f3;
    color: #fff;
    border-radius: 5px 0px 0px 5px;
    transition: none;
  }
  .ivu-icon-ios-arrow-down:before {
    color: #fff;
  }
}
</style>
