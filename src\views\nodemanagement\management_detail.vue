<template>
  <div class="management_box">
    <div class="management_top">
      <h2>节点信息</h2>
      <Row class="top_center">
        <Col span="">
        节点名称：<span>{{ this.detailslist.nodeName }}</span></Col>
        <Col span="">所属用户：<span>{{ this.detailslist.userLoginId }}</span></Col>
        <Col span="">内网IP：<span>{{ this.detailslist.networkAddress }}</span></Col>
        <Col span="">
        外网IP：<span>{{ this.detailslist.nodeAddress }}</span></Col>
      </Row>
    </div>
    <div class="management_middle">
      <Row class="middle_center">
        <Col span="4"> 节点配置初始资源：</Col>
        <Col span="5">内存：<span>{{ this.detailslist.nodeMemory }}Mi</span></Col>
        <Col span="5">硬盘：<span>{{ this.detailslist.nodeDisk }}Gi</span></Col>
        <Col span="5">
        CPU：<span>{{ this.detailslist.nodeCpu }}m</span></Col>
      </Row>
      <Row class="middle_center">
        <Col span="4"> 节点消耗资源情况：</Col>
        <Col span="5">
        内存：
        <span style="color: red" v-if="this.consume.netUsed > 60">{{ this.consume.netUsed }}%</span>
        <span style="color: green" v-else>{{ this.consume.netUsed }}%</span>
        </Col>
        <Col span="5">
        硬盘：
        <span style="color: red" v-if="this.consume.storageUsed > 60">{{ this.consume.storageUsed }}%</span>
        <span style="color: green" v-else>{{ this.consume.storageUsed }}%</span>
        </Col>
        <Col span="5">
        CPU：
        <span style="color: red" v-if="this.consume.cpuUsed > 80">{{ this.consume.cpuUsed }}%
          <Icon type="md-arrow-up" />
        </span>
        <span style="color: green" v-else>{{ this.consume.cpuUsed }}%</span>
        </Col>
      </Row>
    </div>
    <div class="management_bottom">
      <Row type="flex" justify="center" class="code-row-bg">
        <Col span="5"> 区块高度 </Col>
        <Col span="5">已处理交易情况</Col>
        <Col span="6">区块产生速度</Col>
      </Row>
      <Row type="flex" justify="center" class="code-row-bg raius">
        <Col span="5">
        <div class="bottom_radius tow">{{ this.detailslist.blockNum }}</div>
        </Col>
        <Col span="5">
        <div class="bottom_radius tow">{{ this.dataCount }}</div>
        </Col>
        <Col span="6">
        <div class="bottom_radius sthree">{{ this.consume.netUsed=='0'?this.consume.storageUsed=='0'?this.consume.cpuUsed=='0'?'0':'2':'2':'2'}}/S</div>
        </Col>
      </Row>
    </div>
    <div class="cz_table">
      <Table :columns="historyColumns" :data="historyData"></Table>
      <Page :total="dataCount" :page-size="PageParam.pageSize" :page-size-opts="[10, 20, 40, 60, 100]" show-sizer show-total show-elevator class="paging" @on-change="changepage" style="text-align: right" @on-page-size-change="pageSizeChange"></Page>
    </div>
  </div>
</template>
<script>
import { nodeManagement, Managementconsume, Transaction } from '@/api/data'

export default {
  data () {
    return {
      content: {},
      detailid: this.$route.params.content.id
        ? this.$route.params.content.id
        : '',
      // 节点详情
      detailslist: {},
      consume: {},
      // 分页
      PageParam: {
        pageIndex: 1,
        pageSize: 10
      },
      dataCount: 0,
      historyColumns: [
        {
          title: '序号',
          type: 'index',
          width: 300
        },
        {
          title: '访问IP',
          key: 'clientIp'
        },
        {
          title: '请求时间',
          key: 'requestTime'
        }
      ],
      historyData: []
    }
  },
  methods: {
    changepage (index) {
      // 改变页码时触发
      // console.log(index);
      this.PageParam.pageIndex = index
      this.details() // 获取表格列表
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      // 当前页条数
      this.PageParam.pageSize = size
      this.details() // 获取表格列表
    },
    //
    details (detailid) {
      //
      let nodeData = {
        id: detailid,
        pageParam: this.PageParam // 分页
      }
      // 节点信息
      nodeManagement(nodeData).then((res) => {
        this.detailslist = res.data.records[0]
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
      // 节点消耗资源情况
      Managementconsume(this.detailid).then((res) => {
        const { code } = res
        if (code === '00000') {
          this.consume = res.data
          // console.log(this.consume);
        } else {
          console.log('数据获取异常：', res)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
      // 详情信息交易列表
      let transaction = {
        nodeId: detailid,
        pageParam: this.PageParam
      }
      Transaction(transaction).then((res) => {
        // console.log(res.data.total);
        this.historyData = res.data.records
        this.dataCount = res.data.total
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    reback () {
      this.$router.push({
        name: 'node_management'
      })
      // this.$emit('handleTabRemove', this.$route.name, event)
    }
  },
  mounted () {
    if (this.detailid) {
      this.details(this.detailid)
    } else {
      this.reback()
    }
  },
  beforeRouteEnter (to, from, next) {
    // 在渲染该组件的对应路由被 confirm 前调用
    // 不！能！获取组件实例 `this`
    // 因为当钩子执行前，组件实例还没被创建
    if (from.name) {
      // console.log('from.name:', from.name)
      next()
    } else {
      next('/node_management')
    }
  }
}
</script>

<style lang="less" scope>
.management_box {
  font-size: 18px;
  font-weight: 550;
}

.management_top {
  border-bottom: 1px solid #bfbfbf;
  .top_center {
    display: flex;
    flex-flow: row wrap;
    margin-left: 3%;
    margin-top: 1%;
    margin-bottom: 3%;
    .ivu-col {
      margin-right: 2%;
    }
  }
}

.management_middle {
  border-bottom: 1px solid #bfbfbf;
  margin-bottom: 4%;
  .middle_center {
    margin-bottom: 3%;
    margin-top: 3%;
    margin-left: 3%;
  }
}

.management_bottom {
  .bottom_radius {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    text-align: center;
    line-height: 100px;
  }
  .code-row-bg {
    margin-top: 3%;
    margin-left: 2%;
  }

  .one {
    border: 1px solid rgb(135, 225, 236);
  }
  .tow {
    border: 1px solid rgb(99, 250, 85);
  }
  .sthree {
    border: 1px solid rgb(114, 220, 247);
  }
}
.raius {
  margin-left: 4%;
}
.cz_table {
  margin-top: 3%;
}
</style>
