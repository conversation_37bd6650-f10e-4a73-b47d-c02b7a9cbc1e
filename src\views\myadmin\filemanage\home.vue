<template>
  <div class="home">
    <keep-alive>
      <div class="home-item">
        <!-- <div class="contract-title">
          <b>智能合约</b>
          <Button icon="ios-arrow-forward" size="small" @click="isContractFlag=false">合约广场</Button>
        </div> -->
        <!-- <Button class="right-button" icon="ios-arrow-forward" size="small" @click="changeFlag(false)">合约广场</Button> -->
        <Tabs :value="name" @on-click="tabsFun">
          <TabPane label="文档中心" name="name1">
            <DocumentCenter />
          </TabPane>
          <TabPane label="附件管理" name="name2">
            <MyAttachment ref='serviceCallRanking' />
          </TabPane>
        </Tabs>
      </div>
    </keep-alive>
  </div>
</template>

<script>
import DocumentCenter from './table'
import MyAttachment from './attachment'
export default {
  name: 'contract_home',
  components: {
    DocumentCenter,
    MyAttachment
  },
  data () {
    return {
      name: this.$route.params.tabs || 'name1',
      sonData: ''
    }
  },
  computed: {
    currentTab () {
      return this.$route.params
    }
  },
  methods: {
    tabsFun (e) {
      if (e === 'name2') {
        this.$refs.serviceCallRanking.tabsFun(e)
      }
    }
    // changeFlag (flag) {
    //   this.$router.push({
    //     name: 'contract_area'
    //   })
    // }
  },
  mounted () {
    // console.log(this.$route.params)
  }
}
</script>

<style lang="less" scoped>
.home {
  .home-item {
    position: relative;
    // .contract-title{
    //   display: flex;
    //   align-items: center;
    //   b{
    //     margin-right:10px;
    //   }
    // }
  }
  //
  .right-button {
    position: absolute;
    right: 16px;
    top: 4px;
    z-index: 10;
  }
}
</style>
