<template>
  <div class="menuadmin">
    <Split v-model="split1">
      <div slot="left" class="leftmenu">
        <b style="line-height: 30px; margin: 0 5px 10px">菜单/接口列表</b><br />
        <Input style="width: 80%" placeholder="请输入菜单/接口描述" v-model="inputvalue" @keyup.enter.native="searchDetail()">
        <Icon type="ios-search" slot="suffix" @click="searchDetail()" />
        </Input>
        <p>
          <Button type="info" ghost @click="packupMenu">一键收起</Button>
          <!-- <Button type="primary" ghost @click="drawerValue=true">添加</Button> -->
          <Poptip confirm :title="
              checkChangeArr.length === 0 ? '请选择待删除资源!' : '确认删除吗?'
            " @on-ok="removesBatch">
            <Button type="error" ghost style="margin: 10px 5px" :disabled="hasEditPermission">批量删除</Button>
          </Poptip>
        </p>
        <Tree :key="transferKeyTree" ref="sss" class="tree-render" :data="dataTree" show-checkbox :render="renderContent" @on-select-change="handleSelectChange" @on-check-change="handleCheckChange"></Tree>
      </div>
      <div slot="right" class="rightmenu">
        <b style="line-height: 30px; margin: 0 5px 10px">{{
          menuDetail.isAdd ? "新增配置信息" : "编辑配置信息"
        }}</b><br />
        <div :key="transferKey">
          <!-- <div> -->
          <FormSingle style="" ref="childMethod" @getMenu="getMenu" :pastDetail="JSON.stringify(menuDetail)"></FormSingle>
          <!-- {{JSON.stringify(menuDetail)}} -->
        </div>
      </div>
    </Split>
    <!-- <Drawer
      title="新增菜单/接口配置"
      v-model="drawerValue"
      width="720"
      :mask-closable="false"
      :styles="styles"
    >
      <FormSingle style="" ref="childMethod"></FormSingle>
    </Drawer> -->
  </div>
</template>
<script>
import { getMenuInfo, getMenuDetail, deleteMenuInfo } from '@/api/data'
import FormSingle from '_c/form-single'
import { localRead } from '@/lib/util'
export default {
  name: 'menu_admin',
  components: {
    FormSingle
  },
  data () {
    return {
      parentList: {}, // 获取父id数组
      split1: 0.4,
      transferKey: 0,
      transferKeyTree: 0,
      inputvalue: '',
      drawerValue: false,
      contextData: null,
      styles: {
        height: 'calc(100% - 55px)',
        overflow: 'auto',
        paddingBottom: '53px',
        position: 'static'
      },
      dataTree: [
        {
          title: 'CMBaaS',
          // parentName: '/',
          expand: true,
          render: (h, { root, node, data }) => {
            return h(
              'span',
              {
                style: { display: 'inline-block', width: '100%' }
              },
              [
                h('span', data.title),
                h(
                  'span',
                  {
                    style: {
                      display: 'inline-block',
                      float: 'right',
                      marginRight: '32px'
                    }
                  },
                  [
                    h('Button', {
                      props: Object.assign({}, this.buttonProps, {
                        icon: 'ios-add',
                        type: 'primary',
                        disabled: this.hasEditPermission
                      }),
                      style: { width: '56px' },
                      on: {
                        click: (e) => {
                          e.stopPropagation()
                          this.append(data)
                        }
                      }
                    })
                  ]
                )
              ]
            )
          },
          children: []
        }
      ],
      buttonProps: { type: 'default', size: 'small' },
      menuDetail: {
        resourceId: '',
        resourceName: '',
        title: '',
        resourceType: 'MENU',
        url: '',
        httpMethod: '',
        parentId: [],
        parentName: '/',
        status: 'DISABLE',
        seq: ''
      },
      checkChangeArr: [],
      userPermission: JSON.parse(localRead('userPermission')),
      resourceId: ''
    }
  },
  computed: {

    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
  methods: {
    init () {
      this.menuDetail = {
        resourceId: '',
        resourceName: '',
        title: '',
        resourceType: 'MENU',
        url: '',
        httpMethod: '',
        parentId: [0],
        parentName: '/',
        status: 'DISABLE',
        seq: ''
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    /**
     * @获取树形列表数据
     * @params parentId 控制二次刷新是否默认展开
     */
    getMenu (resourceType, parentId) {
      getMenuInfo(resourceType)
        .then((res) => {
          // console.log('getMenuInfo===>', res)
          if (res.code !== '00000') {
            this.msgInfo('warning', res.message, true)
          } else {
            this.buildParentList(res.data)
            if (parentId !== undefined) {
              if (typeof parentId === 'number') {
                this.dataTree[0].children = this.handleExpand(
                  res.data,
                  this.getParentIdArr(parentId)
                )
              } else {
                this.dataTree[0].children = this.handleExpand(
                  res.data,
                  parentId
                )
              }
            } else {
              this.dataTree[0].children = res.data
            }
          }
          // if (res.code !== "00000") this.msgInfo("warning", res.message, true);
          // else this.dataTree[0].children = this.handleExpand(res.data, 304);
          // // else this.dataTree[0].children = this.handleExpand(res.data)
          // this.buildParentList(res.data); //获取父id数组
        })
        .catch((error) => {
          console.log('getMenuInfo.error===>', error)
          this.msgInfo('error', error.message, true)
        })
    },
    handleExpand (data, parentIdArr) {
      data.map((item) => {
        if (parentIdArr.indexOf(item.resourceId) >= 0) {
          item.expand = true
        } else {
          item.expand = false
        }
        if (item.children && item.children.length !== 0) {
          this.handleExpandChildren(item.children, parentIdArr)
        }
      })
      return data
    },
    handleExpandChildren (data, parentIdArr) {
      data.map((item) => {
        if (parentIdArr.indexOf(item.resourceId) >= 0) {
          item.expand = true
        } else {
          item.expand = false
        }
        if (item.children && item.children.length !== 0) {
          this.handleExpandChildren(item.children, parentIdArr)
        }
      })
    },

    searchDetail () {
      let info = {
        "resourceId": this.resourceId,
        "queryParam": this.inputvalue
      }
      // if (resourceValue) {
      getMenuDetail(info)
        .then((res) => {
          // console.log('getMenuDetail===>', res)
          if (res.code !== '00000') {
            this.msgInfo('warning', res.message, true)
          } else {
            this.resourceId = ''
            // this.inputvalue = ''
            this.menuDetail = res.data
            // this.menuDetail.parentId =[2,12]
            this.menuDetail.parentId = this.getParentIdArr(
              res.data.resourceId
            )
          }
        })
        .catch((error) => {
          console.log('getMenuDetail.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      // } else this.msgInfo('warning', '内容为空!')
    },
    deleteMenu (resourceIdList) {
      // console.log(resourceIdList)
      deleteMenuInfo(resourceIdList)
        .then((res) => {
          // console.log('deleteMenuInfo===>', res)
          if (res.code !== '00000') this.msgInfo('warning', res.message, true)
          else this.msgInfo('success', res.message)
          this.getMenu('ALL', resourceIdList[0])
          this.init()
        })
        .catch((error) => {
          console.log('deleteMenuInfo.error===>', error)
          this.msgInfo('error', error.message, true)
        })
    },
    packupMenu () {
      if (this.dataTree[0]) this.dataTree[0].expand = false
      this.loopPackup(this.dataTree[0])
    },
    loopPackup (val) {
      let that = val.children
      // console.log(val.title)
      if (that) {
        for (let i = 0; i < that.length; i++) {
          that[i].expand = false
          this.loopPackup(that[i])
        }
      } else return false
    },
    handleSelectChange (arr, now) {
      console.log('handleSelectChange===', now)
      if (now.resourceName) {
        this.resourceId = now.resourceId
        this.searchDetail()
      }
      else {
        this.init()
        ++this.transferKey
      }
    },
    handleCheckChange (arr, now) {
      // console.log('handleCheckChange===', arr, JSON.stringify(now), now.resourceName)
      this.checkChangeArr = arr
    },
    renderContent (h, { root, node, data }) {
      return h(
        'span',
        {
          style: { display: 'inline-block', width: '100%' }
        },
        [
          h('span', data.title),
          h(
            'span',
            {
              style: {
                display: 'inline-block',
                float: 'right',
                marginRight: '32px'
              }
            },
            [
              h('Button', {
                props: Object.assign({}, this.buttonProps, { icon: 'ios-add', disabled: this.hasEditPermission }),
                style: {
                  marginRight: '8px',
                  display: data.resourceType === 'API' ? 'none' : 'black'
                },
                on: {
                  click: (e) => {
                    e.stopPropagation()
                    this.append(data)
                  }
                }
              }),
              // h('Button', {
              //   props: Object.assign({}, this.buttonProps, {
              //     icon: 'ios-trash-outline'
              //   }),
              //   on: {
              //     click: () => {
              //       this.remove(root, node, data)
              //     }
              //   }
              // })
              h('Poptip', {
                props: {
                  confirm: true,
                  transfer: true,
                  title: '确认删除吗?',

                },
                on: {
                  'on-ok': () => {
                    this.remove(root, node, data)
                  }
                }
              },
                [h('Button', {
                  props: Object.assign({}, this.buttonProps, { icon: 'ios-trash-outline', disabled: this.hasEditPermission })
                })])
            ]
          )
        ]
      )
    },
    // 新增
    append (data) {
      this.msgInfo('info', '请在右侧填写详细信息并提交')
      const children = data.children || []
      this.init()
      if (data.children && data.children.length !== 0) {
        this.menuDetail.seq = data.children[data.children.length - 1].seq + 1
      } else {
        this.menuDetail.seq = 1
      }
      // this.menuDetail.parentId =[2,12]
      this.menuDetail.parentId = [
        ...this.getParentIdArr(data.resourceId),
        data.resourceId || data.nodeKey
      ]
      this.menuDetail.isAdd = true
      this.menuDetail.resourceType = 'MENU'
      // children.push(this.menuDetail);
      this.$set(data, 'children', children)
    },
    remove (root, node, data) {
      const parentKey = root.find((el) => el === node).parent
      const parent = root.find((el) => el.nodeKey === parentKey).node
      const index = parent.children.indexOf(data)
      parent.children.splice(index, 1)
      if (data.resourceId) this.deleteMenu([data.resourceId])
    },
    removesBatch () {
      if (this.checkChangeArr.length === 0) {
        //  this.$Message.warning('请选择待删除资源!');
        return false
      }
      let listArr = []
      for (let j = 0; j < this.checkChangeArr.length; j++) {
        listArr.push(this.checkChangeArr[j].resourceId)
      }
      this.deleteMenu(listArr)
      // location.reload();
      // this.getMenu('ALL')
      // ++this.transferKeyTree
      // ++this.transferKey
    },
    getKeyTree (data) {
      this.transferKeyTree = data
    },
    /**
     * 递归获取parentIdArr
     */
    buildParentList (arr) {
      arr.forEach((g) => {
        if (g.parentId !== undefined) {
          let pid = g['parentId']
          let oid = g['resourceId']
          this.parentList[oid] = pid
        }
        if (g.children !== undefined) {
          if (g['children']) {
            this.buildParentList(g['children'])
          }
        }
      })
    },
    getParentIdArr (idx) {
      let arr = []
      let parentList = this.parentList
      function findParent (idx) {
        if (parentList[idx] !== undefined) {
          let pid = parentList[idx]
          // console.log(pid)
          arr.push(pid)
          findParent(pid)
        }
      }
      findParent(idx)
      return arr.reverse()
    }
  },
  watch: {
    menuDetail: {
      handler () {
        ++this.transferKey
      },
      deep: true,
      immediate: false
    }
  },
  mounted () {
    this.getMenu('ALL')
  }
}
</script>
<style lang="less" scoped>
/deep/.tree-render .ivu-tree-title {
  width: 92%;
}
// /deep/.ivu-tree-title-selected {background-color: transparent;}
.menuadmin {
  min-height: 600px;
}
.leftmenu {
  margin-right: -8px;
  height: 600px;
  overflow-x: hidden;
  overflow-y: auto;
  border-right: 1px solid #d9d9d9;
}
.rightmenu {
  margin-left: 20px;
}
</style>
