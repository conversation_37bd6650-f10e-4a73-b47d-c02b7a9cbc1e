<template>
  <div class="userBox">
    <card style="height:90px;margin-top:-10px;">
      <div style="z-index:0;position:relative;">
        <Avatar :src="userInfo.faceUrl" icon="ios-person" class="ava" style="" size="60" @click.native="reviseAvatar" />
        <p class="ava-title" style="z-index:999;" @click="reviseAvatar"><span style="margin-left:6px;">更换头像</span></p>
        <p style="font-size:20px;font-weight:bold;">{{ userLoginId }}</p>
        <p style="font-size:10px;padding-top:5px;">
          <span style="margin-right:10px;" class="role-title" v-show="tenantName">{{ tenantName }}</span>
          <span class="role-title" v-show="roleName">{{ roleName }}</span>
        </p>
      </div>
    </card>
    <Row class="userInfo">
      <Col span="4" style="position:relative;" class="link-line">
      <ul>
        <li @click="selectPage(0)" v-show="userSource" :class="{active:changeCur===0}"><span class="li-title">我的信息</span></li>
        <li @click="selectPage(1)" v-show="userSource" :class="{active:changeCur===1}"><span class="li-title">修改密码</span></li>
        <li @click="selectPage(2)" :class="{active:changeCur===2}"><span class="li-title">我的租户</span></li>
        <li @click="selectPage(3)" :class="{active:changeCur===3}"><span class="li-title">通知管理</span></li>
        <li @click="selectPage(4)" :class="{active:changeCur===4}"><span class="li-title">工单管理</span></li>
      </ul>
      </Col>
      <Col span="20" class="link">
      <div class="content" v-show="changeCur===0" style="height:auto;">
        <li style="cursor:default"><span class="title">我的信息</span></li>
        <Form ref="userInfo" :rules="userInfoRule" :model="userInfo" label-position="top" style="padding:20px 0 0 10px;">
          <FormItem label="账号" prop="account">
            <Input v-model="userInfo.account" style="width:230px;" disabled></Input>
          </FormItem>
          <FormItem label="真实姓名" prop="realName">
            <Input v-model="userInfo.realName" style="width:230px;"></Input>
          </FormItem>
          <FormItem label="所属组织">
            <Input v-model="userInfo.organization" style="width:230px;"></Input>
            <span style="color:red;margin-left:5px;" v-show="userInfo.organization && userInfo.organization.length > 32">不能超过32位</span>
          </FormItem>
          <FormItem label="手机号码">
            <Input v-model="userInfo.phone" style="width:230px;" disabled></Input>
            <span class="reviseFont" @click="revisePhone">修改手机号</span>
          </FormItem>
          <FormItem label="邮箱地址">
            <Input v-model="userInfo.email" style="width:230px;" disabled></Input>
            <span class="reviseFont" @click="reviseEmail">修改邮箱地址</span>
          </FormItem>
          <FormItem>
            <p style="font-size:13px;padding-bottom:13px;">上次登陆：{{userInfo.lastLoginTime}}</p>
            <Button type="primary" @click="handleInfoSubmit('userInfo')" style="font-size:14px;" :disabled="getSaveFlag">保存</Button>
            <Button @click="handleInfoCancel('userInfo')" style="margin-left: 8px;font-size:14px;">取消</Button>
          </FormItem>
        </Form>
      </div>
      <revisePwd ref="pwd" @select="selectPage" v-show="changeCur===1" :expireTime="userInfo.expireTime" style="height:auto;"></revisePwd>
      <telnetInfo ref="telnet" v-show="changeCur===2" style="height:auto;"></telnetInfo>
      <noticeMsg ref="notice" v-show="changeCur===3" @selectPage="selectPage" tyle="height:auto;" />
      <workOrderMng v-show="changeCur===4" style="height:auto;"></workOrderMng>
      </Col>
    </Row>
    <Modal draggable v-model="phoneModal" :title="revisePhoneModal === true ? '更换手机号' : '修改手机号'" width="350" sticky :mask-closable="false">
      <div :style="revisePhoneModal === true ? 'display:none' : ''" style="padding-top:10px;margin-bottom:-5px;">
        <p style="font-size:13px;">请使用手机<span>{{userInfo.phone.substr(0,3)+'****'+userInfo.phone.substr(7)}}</span>验证身份</p>
        <Form ref="phoneCode" :rules="phoneCodeRule" :model="userInfo">
          <FormItem prop="phoneCode">
            <div style="display: flex;align-items: center;padding-top:8px;">
              <Input type="text" style="width:200px;" v-model="userInfo.phoneCode" placeholder="请输入验证码" />
              <Button style="margin-left:8px;padding:2px;width:100px;height:32px;" :disabled="!!cooling" @click="getPhoneCaptcha(userInfo.phone, 'CHECK_PHONENUMBER')"> {{ cooling ? "重新发送(" + cooling + "s)" : " 获取验证码 " }}</Button>
            </div>
          </FormItem>
        </Form>
      </div>
      <div :style="revisePhoneModal === true ? '' : 'display:none'">
        <Form ref="newPhoneCode" :rules="newPhoneCodeRule" :model="userInfo">
          <FormItem prop="newPhone">
            <Input type="text" style="width:308px;" v-model="userInfo.newPhone" placeholder="请输入手机号" />
          </FormItem>
          <FormItem prop="newPhoneCode">
            <div style="display: flex;align-items: center;padding-top:8px;">
              <Input type="text" style="width:200px;" v-model="userInfo.newPhoneCode" placeholder="请输入验证码" />
              <Button style="margin-left:8px;padding:2px;width:100px;height:32px;" :disabled="!!cooling" @click="getPhoneCaptcha(userInfo.newPhone, 'MODIFY_PHONENUMBER')"> {{ cooling ? "重新发送(" + cooling + "s)" : " 获取验证码 " }}</Button>
            </div>
          </FormItem>
        </Form>
      </div>
      <div slot="footer" :style="revisePhoneModal === true ? 'display:none' : ''">
        <Button @click="phoneCancel">取消</Button>
        <Button type="primary" @click="phoneNext">下一步</Button>
      </div>
      <div slot="footer" :style="revisePhoneModal === true ? '' : 'display:none'">
        <Button @click="phoneCancel">取消</Button>
        <Button type="primary" @click="phoneOk">确认</Button>
      </div>
    </Modal>
    <Modal draggable v-model="emailModal" :title="reviseEmailModal === true ? '更换邮箱地址' : '验证身份'" width="350" sticky :mask-closable="false">
      <div :style="reviseEmailModal === true ? 'display:none' : ''" style="padding-top:8px;margin-bottom:-5px;">
        <p style="font-size:13px;">请使用邮箱<span>{{userInfo.email}}</span>验证身份</p>
        <Form ref="emailCode" :rules="emailCodeRule" :model="userInfo">
          <FormItem prop="emailCode">
            <div style="display: flex;align-items: center;padding-top:8px;">
              <Input type="text" style="width:200px;" v-model="userInfo.emailCode" placeholder="请输入验证码">
              </Input>
              <Button style="margin-left:8px;padding:2px;width:100px;height:32px;" :disabled="!!cooling" @click="getEmailCaptcha(userInfo.email, 'CHECK_EMAILADDRESS')"> {{ cooling ? "重新发送(" + cooling + "s)" : " 获取验证码 " }}</Button>
            </div>
          </FormItem>
        </Form>
      </div>
      <div :style="reviseEmailModal === true ? '' : 'display:none'">
        <Form ref="newEmailCode" :rules="newEmailCodeRule" :model="userInfo">
          <FormItem prop="newEmail">
            <Input type="text" style="width:308px;" v-model="userInfo.newEmail" placeholder="请输入邮箱地址" />
          </FormItem>
          <FormItem prop="newEmailCode">
            <div style="display: flex;align-items: center;padding-top:8px;">
              <Input type="text" style="width:200px;" v-model="userInfo.newEmailCode" placeholder="请输入验证码">
              </Input>
              <Button style="margin-left:8px;padding:2px;width:100px;height:32px;" :disabled="!!cooling" @click="getEmailCaptcha(userInfo.newEmail, 'MODIFY_EMAILADDRESS')"> {{ cooling ? "重新发送(" + cooling + "s)" : " 获取验证码 " }}</Button>
            </div>
          </FormItem>
        </Form>
      </div>
      <div slot="footer" :style="reviseEmailModal === true ? 'display:none' : ''">
        <Button @click="emailCancel">取消</Button>
        <Button type="primary" @click="emailNext">下一步</Button>
      </div>
      <div slot="footer" :style="reviseEmailModal === true ? '' : 'display:none'">
        <Button @click="emailCancel">取消</Button>
        <Button type="primary" @click="emailOk">确认</Button>
      </div>
    </Modal>
    <!-- 更换头像 -->
    <Modal draggable v-model="avatarModal" title="更换头像" width="450" sticky :mask-closable="false">
      <div style="padding:20px;margin-bottom:-5px;height:160px;text-align:center; overflow:hidden;">
        <div :style="imageUrl ? 'float:left;width:50%;border-right: 1px solid #ccc9c9;' :''">
          <Avatar :src="userInfo.faceUrl" icon="ios-person" shape="circle" size="60"></Avatar>
        </div>
        <div class="upload" v-show="imageUrl">
          <Avatar :src="imageUrl" shape="circle" size="60" style="background-color: #87d068"></Avatar>
        </div>
        <Upload action="" :format="['png','jpg','jpeg']" :max-size="2048" :on-exceeded-size="handleMaxSize" :on-format-error="handleFormatError" :before-upload="handleUpload" style="padding-top:20px;">
          <Button icon="ios-cloud-upload-outline"><span style="font-size:10px;">上传头像</span></Button>
        </Upload>
        <p style="font-size:6px;">图片要求：不大于2M，200*200像素以内，类型仅为png、jpg、jpeg</p>
      </div>
      <div slot="footer">
        <Button @click="uploadCancel">取消</Button>
        <Button type="primary" @click="uploadOk" :disabled="!imageUrl">更换</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { uploadImageFile, checkUserLoginId, getUserInfo, requestVerificationCode, checkVerificationCode, updateUserInfo } from '@/api/data'
import RevisePwd from '../userinfo/revise-pwd.vue'
import TelnetInfo from '../userinfo/telnet-info.vue'
import WorkOrderMng from '../userinfo/workorder-mng.vue'
import { isEmail, isPhoneNumber, isLoginId, isRealName } from '@/lib/check'
import { setToken, localSave, localRead } from '@/lib/util'
import { loginSysConfig } from '@/api/user'
import { mapActions } from 'vuex'
// import ManagementMsg from './management-msg.vue'
import noticeMsg from './notice-msg.vue'
export default {
  name: 'user_info',
  components: {
    RevisePwd,
    TelnetInfo,
    WorkOrderMng,
    noticeMsg
  },
  data () {
    const validateEmail = (rule, value, callback) => {
      if (!isEmail(value)) {
        callback(new Error('邮箱格式不正确'))
      } else {
        callback()
      }
    }

    const validateRealName = (rule, value, callback) => {
      if (!isRealName(value)) {
        callback(new Error('只允许中英文、数字， 且不能为纯数字'))
      } else {
        callback()
      }
    }

    const validateAccount = (rule, value, callback) => {
      if (!isLoginId(value)) {
        callback(new Error('请输入中英文、数字、下划线'))
      } else if (value !== this.userLoginId) {
        checkUserLoginId(value).then(res => {
          if (res.code === '00000') {
            callback()
          } else {
            callback(new Error(res.message))
          }
        }).catch(error => {
          callback(new Error(error.message))
        })
      } else {
        callback()
      }
    }
    const validateEmailCode = (rule, value, callback) => {
      if (!(/^\d{6}$/.test(value))) {
        callback(new Error('请输入6位数字'))
      } else {
        callback()
      }
    }
    const validatePhone = (rule, value, callback) => {
      if (!isPhoneNumber(value)) {
        callback(new Error('手机号格式不支持'))
      } else {
        callback()
      }
    }
    return {
      tenantName: localStorage.getItem('tenantName') ? localStorage.getItem('tenantName') : '',
      avatarModal: false,
      display: 'display:none',
      nullUrl: require('@/assets/img/null.png'),
      phoneModal: false,
      revisePhoneModal: false,
      emailModal: false,
      reviseEmailModal: false,
      cooling: 0,
      cur: 1,
      changeCur: localRead('userSource') == 1 ? 2 : 0,
      imageUrl: null,
      imageFile: null,
      uploatFlag: false,
      loadingStatus: false,
      userLoginId: '',
      organization: '',
      roleName: '',
      listLoading: true,
      contentLoading: false,
      messageContent: '',
      showingMsgItem: {},
      checkFlag: true,
      isNoAuthFalg: true, // // true显示正常，（没调用auth），false相反
      userInfo: {
        userId: '',
        account: '',
        realName: '',
        roleName: '',
        organization: '',
        phone: '',
        status: '',
        phoneCode: '',
        newPhone: '',
        newPhoneCode: '',
        email: '',
        emailCode: '',
        newEmail: '',
        newEmailCode: '',
        lastLoginTime: '',
        faceUrl: null,
        expireTime: ''
      },
      hisUserInfo: {
        account: '',
        realName: '',
        organization: '',
        phone: '',
        email: ''
      },
      userInfoRule: {
        account: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, max: 15, message: '长度不能超过15', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateAccount }
        ],
        realName: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, max: 15, message: '长度不能超过15', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateRealName }
        ],
        organization: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, max: 30, message: '长度不能超过30', trigger: 'blur' }
        ]
      },
      emailCodeRule: {
        emailCode: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateEmailCode }
        ]
      },
      newEmailCodeRule: {
        newEmail: [{ required: true, trigger: 'blur', validator: validateEmail }],
        newEmailCode: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateEmailCode }
        ]
      },
      phoneCodeRule: {
        phoneCode: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateEmailCode }
        ]
      },
      newPhoneCodeRule: {
        newPhoneCode: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateEmailCode }
        ],
        newPhone: [
          { required: true, pattern: /^[0-9]{11}$/, message: '手机号应为11位数字', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validatePhone }
        ]
      }
    }
  },
  computed: {
    userSource () {
      if (localRead('userSource') == 1) {
        return false
      } else {
        return true
      }
    },
    getSaveFlag () {
      var flag = false
      if (this.userInfo.organization && this.userInfo.organization.length > 32) {
        flag = false
      } else {
        flag = true
      }
      if (this.userInfo.account && this.userInfo.realName && flag && this.userInfo.phone && this.userInfo.email) {
        if (this.isEqual(this.userInfo.account, this.hisUserInfo.account) || this.isEqual(this.userInfo.realName, this.hisUserInfo.realName) || this.isEqual(this.userInfo.organization, this.hisUserInfo.organization) || this.isEqual(this.userInfo.phone, this.hisUserInfo.phone) || this.isEqual(this.userInfo.email, this.hisUserInfo.email)) {
          if (isLoginId(this.userInfo.account) && isRealName(this.userInfo.realName)) {
            return false
          } else {
            return true
          }
        } else {
          return true
        }
      } else {
        return true
      }
    },
    changeCur () {
      return this.cur
    }
  },
  methods: {
    isEqual (value1, value2) {
      if (value1 === value2) {
        return false
      } else {
        return true
      }
    },
    ...mapActions([
      'updateUserLoginId',
      'updateFaceUrl',
      'updateCur'
    ]),
    selectPage (value) {
      this.cur = value
      this.changeCur = value
      this.updateCur(value)
      if (value === 1) {
        this.$refs.pwd.initPwd()
      } else if (value === 0) {
        this.init()
        this.getUserInfoData(localRead('userLoginId'))
      } else if (value === 2) {
        this.$refs.telnet.initTelnetInfo()
      } else if (value === 3) {
        this.$refs.notice.changeTabs(this.$store.state.user.tas || 1)
      }
    },
    init () {
      this.$nextTick(() => {
        this.$refs['userInfo'].resetFields()
      })
    },
    handleSelect (name) {
      this.currentMessageType = name
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    handleUpload (file) {
      // var name = file.name.substring(file.name.indexOf('.') + 1)
      var name = file.name.split('.')
      if (name[name.length - 1] === 'jpg' || name[name.length - 1] === 'jpeg' || name[name.length - 1] === 'png') {
        this.imageFile = file
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          const _base64 = reader.result
          this.imageUrl = _base64 // 将_base64赋值给图片的src，实现图片预览
        }
      } else {
        this.msgInfo('warning', `不符合上传的文件类型`, true)
      }
    },
    // 修改手机号
    revisePhone () {
      this.phoneModal = true
      this.revisePhoneModal = false
      this.cooling = 0
      this.$nextTick(() => {
        this.$refs['phoneCode'].resetFields()
        this.$refs['newPhoneCode'].resetFields()
      })
    },
    getPhoneCaptcha (phone, type) {
      this.requestCode(this.userLoginId, null, phone, type)
    },
    phoneNext () {
      this.$refs['phoneCode'].validate((valid) => {
        if (valid) {
          checkVerificationCode(this.userLoginId, null, this.userInfo.phone, this.userInfo.phoneCode, 'CHECK_PHONENUMBER').then(res => {
            if (res.code === '00000') {
              this.revisePhoneModal = true
              this.cooling = 0
              this.msgInfo('success', res.message, true)
            } else {
              this.msgInfo('error', res.message, true)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        }
      })
    },
    phoneCancel () {
      this.phoneModal = false
      this.revisePhoneModal = false
    },
    phoneOk () {
      this.$refs['newPhoneCode'].validate((valid) => {
        if (valid) {
          checkVerificationCode(this.userLoginId, null, this.userInfo.newPhone, this.userInfo.phoneCode, 'MODIFY_PHONENUMBER').then(res => {
            if (res.code === '00000') {
              this.msgInfo('success', res.message + ',手机号修改成功', true)
              this.revisePhoneModal = false
              this.phoneModal = false
              this.userInfo.phone = this.userInfo.newPhone
            } else {
              this.msgInfo('error', res.message, true)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        }
      })
    },
    // 修改邮箱
    reviseEmail () {
      this.cooling = 0
      this.emailModal = true
      this.reviseEmailModal = false
      this.$nextTick(() => {
        this.$refs['emailCode'].resetFields()
        this.$refs['newEmailCode'].resetFields()
      })
    },
    emailNext () {
      this.$refs['emailCode'].validate((valid) => {
        if (valid) {
          checkVerificationCode(this.userLoginId, this.userInfo.email, null, this.userInfo.emailCode, 'CHECK_EMAILADDRESS').then(res => {
            if (res.code === '00000') {
              this.msgInfo('success', res.message, true)
              this.reviseEmailModal = true
              this.cooling = 0
            } else {
              this.msgInfo('error', res.message, true)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        }
      })
    },
    getEmailCaptcha (email, type) {
      // this.msgInfo('warning', '邮箱验证接口暂未开通', true)
      this.requestCode(this.userLoginId, email, null, type)
    },
    emailCancel () {
      this.emailModal = false
      this.reviseEmailModal = false
    },
    emailOk () {
      this.$refs['newEmailCode'].validate((valid) => {
        if (valid) {
          checkVerificationCode(this.userLoginId, this.userInfo.newEmail, null, this.userInfo.emailCode, 'MODIFY_EMAILADDRESS').then(res => {
            if (res.code === '00000') {
              this.msgInfo('success', res.message + '邮箱修改成功', true)
              this.reviseEmailModal = false
              this.emailModal = false
              this.userInfo.email = this.userInfo.newEmail
            } else {
              this.msgInfo('error', res.message, true)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        }
      })
    },
    handleInfoCancel () {
      // 刷新最新数据
      this.init()
      this.getUserInfoData(this.userLoginId)
    },
    handleInfoSubmit () {
      this.$refs['userInfo'].validate((valid) => {
        if (valid) {
          updateUserInfo(this.userInfo.userId, this.userInfo.account, this.userInfo.email, this.userInfo.realName, this.userInfo.phone, this.userInfo.organization).then(res => {
            if (res.code === '00000') {
              this.msgInfo('success', res.message, true)
              setToken(res.data.token)
              localSave('userLoginId', res.data.userLoginId)
              localSave('token', res.data.token)
              this.updateUserLoginId(res.data.userLoginId)
              this.getUserInfoData(res.data.userLoginId)
            } else {
              this.msgInfo('error', res.message, true)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        }
      })
    },
    avatarShow () {
      this.display = 'display:block'
    },
    avatarHidden () {
      this.display = 'display:none'
    },
    reviseAvatar () {
      this.avatarModal = true
    },
    uploadCancel () {
      this.avatarModal = false
      this.imageUrl = null
    },
    uploadOk () {
      if (this.imageFile) {
        uploadImageFile(this.imageFile).then(res => {
          if (res.code === '00000') {
            this.msgInfo('success', res.message, true)
            var img = res.data.faceUrl
            this.$http.get(img).then(res => {
              if (res.status === 200) {
                localSave(res.data.faceUrl)
                this.updateFaceUrl(img)
                this.userInfo.faceUrl = img
              }
            }).catch(error => {
              console.log(error.message + ',图片获取失败！！')
            })
            this.avatarModal = false
            this.imageUrl = null
          } else {
            this.msgInfo('error', res.message, true)
          }
        }).catch(error => {
          this.msgInfo('error', error.message, true)
        })
      }
    },
    handleMaxSize (file) {
      this.msgInfo('warning', '文件' + file.name + '太大,不能超过 2M.', true)
      return false
    },
    handleFormatError (file) {
      // this.msgInfo('warning', file.name + '图片格式不正确,只能为jpg、png、jpeg', true)
      return false
    },
    // 获取用户信息
    getUserInfoData (userLoginId) {
      getUserInfo(userLoginId).then(res => {
        this.userInfo.userId = res.data.userId
        this.userInfo.account = res.data.userLoginId
        this.userInfo.realName = res.data.realName
        this.userInfo.email = res.data.email
        this.userInfo.roleName = res.data.roleName
        this.userInfo.organization = res.data.organization
        this.userInfo.phone = res.data.phoneNumber
        this.userInfo.status = res.data.status
        this.userInfo.lastLoginTime = res.data.lastLoginTime
        this.userInfo.expireTime = res.data.expireTime
        var facePicHash = res.data.facePicHash
        if (facePicHash) {
          this.$http.get(facePicHash).then(res => {
            if (res.status === 200) {
              this.userInfo.faceUrl = facePicHash
            }
          }).catch(error => {
            console.log(error.message + ',图片获取失败！！')
          })
        }
        this.userLoginId = res.data.userLoginId
        this.organization = res.data.organization
        this.roleName = res.data.roleName
        // 保存原有值
        this.hisUserInfo.account = res.data.userLoginId
        this.hisUserInfo.realName = res.data.realName
        this.hisUserInfo.email = res.data.email
        this.hisUserInfo.organization = res.data.organization
        this.hisUserInfo.phone = res.data.phoneNumber
      }).catch(error => {
        // console.log('res:', error)
        this.msgInfo('error', error.message, true)
      })
    },
    requestCode (val1, val2, val3, val4) {
      requestVerificationCode(val1, val2, val3, val4).then(res => {
        if (res.code === '00000') {
          this.msgInfo('success', res.message, true)
          this.cooling = 60
          let coolingInter = setInterval(() => {
            if (this.cooling > 0) {
              this.cooling--
            } else {
              clearInterval(coolingInter)
            }
          }, 1000)
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getSystem () {
      loginSysConfig().then(res => {
        // console.log('loginSysConfig===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          // console.log('this.$route.params.cur',this.$route.params.cur,'this.$store.state.user.cur',this.$store.state.user.cur,'this.cur',this.cur)
          //   if (res.data.showUserInfo && res.data.showUserInfo === '1') {
          //     this.isNoAuthFalg = true
          //     // 正常情况下
          //     this.cur = this.$route.params.cur || this.$store.state.user.cur || 0
          //   } else {
          //     this.isNoAuthFalg = false
          //     this.cur = this.$route.params.cur && this.$route.params.cur > 1 ? this.$route.params.cur : 2
          //     this.selectPage(this.cur)
          //   }
        }
      }).catch(error => {
        console.log('loginSysConfig.error===>', error)
      })
    }
  },
  mounted () {
    this.getSystem()
    this.getUserInfoData(localRead('userLoginId'))
    // this.uploadList = this.$refs.upload.fileList
    // 路由跳转过来非cur=0时
    // 记录前一次离开这个页面的记录
    this.cur = localRead('userSource') == 1 ? 2 : 0

    this.cur = this.$route.params.cur || this.$store.state.user.cur || 0
    console.log(this.$route.params.cur);

    // 如果有传入cur参数，切换到对应的标签页
    if (this.$route.params.cur) {
      this.selectPage(parseInt(this.$route.params.cur))
    }
    // this.getUserInfoData(localRead('userLoginId'))
  },
  watch: {
    'changeCur' (val) {

    }
  }
}
</script>
<style lang="less" scoped>
.userBox {
  width: calc(100% + 32px);
  //height:100vh;
  background-color: #f2f6fd;
  margin: -16px;
  // padding-bottom:15px;
  .userInfo {
    max-height: 170vh;
    margin-top: 12px;
    border-radius: 3px;
    background-color: #fff;
  }
  .link-line {
    min-height: calc(100vh - 276px);
    background-color: #fff;
  }
  .demo-upload-list {
    display: inline-block;
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    border: 1px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    margin-right: 4px;
  }
  .upload img {
    height: 50px;
    border-radius: 50%;
  }
  .demo-upload-list-cover {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.6);
  }
  .demo-upload-list:hover .demo-upload-list-cover {
    display: block;
  }
  .demo-upload-list-cover i {
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    margin: 0 2px;
  }

  // 内容
  .reviseFont {
    font-size: 13px;
    color: #3f7dff;
    margin-left: 8px;
    .click;
  }
  .click {
    cursor: pointer;
  }
  ul,
  li {
    list-style: none;
    margin-top: 20px;
  }
  li {
    .click;
    font-size: 16px;
    padding: 6px 12px 6px 12px;
  }
  .title {
    font-weight: bold;
    cursor: default;
    vertical-align: middle;
    display: table-cell;
    font-size: 16px;
  }
  .li-title {
    padding-left: 10px;
  }

  .active {
    color: #3f7dff;
    //padding-bottom: 10px;
    border-right: solid #3f7dff 4px;
    height: 34px;
    font-weight: bold;
    z-index: 999;
    background-color: #eef4ff;
  }
  .link {
    // width: 5px;
    // height: 100%;
    background-color: #fff;
    border-left: solid #eeeef0 2px;
    padding-left: 9px;
    margin-left: -1px;
  }
  /deep/.ivu-modal-header-inner {
    font-size: 14px !important;
    font-weight: bold;
  }
  /deep/.ivu-input {
    font-size: 14px !important;
  }
  .ivu-btn {
    font-size: 13px;
  }
  img {
    height: auto;
    //width:100%;
  }
  .role-title {
    color: #2d8cf0;
    font-size: 8px;
    background: #eff2f8;
    border: #3f7dff solid 1px;
    padding: 5px;
    border-radius: 3px;
  }
  .ava-title {
    float: left;
    position: absolute;
    line-height: 60px;
    font-size: 8px;
    cursor: pointer;
    color: #fff;
    height: 60px;
    width: 60px;
    background: rgb(173, 173, 172);
    border-radius: 50%;
    opacity: 0.6;
    display: none;
  }
  .ava {
    float: left;
    margin-right: 20px;
    cursor: pointer;
  }
  .ava:hover + .ava-title,
  .ava-title:hover {
    display: block;
    opacity: 0.6;
  }
  /deep/label {
    font-weight: 400;
  }
}
</style>
