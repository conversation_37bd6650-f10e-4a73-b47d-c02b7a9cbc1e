export const engineTypeList = [{
    value: 'eos引擎',
    key: 'EOS'
  },
  // {
  //   value: 'bos引擎',
  //   key: 'BOS'
  // }
]
export const statusList = [{
    value: '启用',
    key: 'ENABLE'
  },
  {
    value: '关闭',
    key: 'DISABLE'
  }
]
export const auditList = [{
    key: 'true',
    value: '需要'
  },
  {
    key: 'false',
    value: '不需要'
  }
]
export const ownershipList = [{
    value: '纳管链',
    key: 'MANAGED_CHAIN'
  },
  {
    value: '管控链',
    key: 'CONTROL_CHAIN'
  }
]
export const nodeTypeList = [{
    key: 'BLOCK_PRODUCER',
    value: '出块节点'
  },
  {
    key: 'BUSINESS_NODE',
    value: '业务节点'
  },
  {
    key: 'SHARE_NODE',
    value: '分享节点'
  }
]
export const watchTypeList = [{
    key: 'ALL_VISIBLE',
    value: '全部租户可见'
  },
  {
    key: 'PARTIALLY_VISIBLE',
    value: '部分租户可见'
  },
  {
    key: 'ALL_INVISIBLE',
    value: '全部租户不可见'
  }
]