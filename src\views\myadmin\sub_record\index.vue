<template>
  <div class="subrecord_index">
    <keep-alive v-if="currentTab === 'transaction_commit'">
    <SubrecordTable />
    </keep-alive>
    <keep-alive :exclude="excludeArr" v-else>
    <router-view />
    </keep-alive>
  </div>
</template>

<script>
import SubrecordTable from './sub_record_table.vue'
export default {
  name: 'subrecord_index',
  components: {
    SubrecordTable
  },
  data () {
    return {
      excludeArr: ['new_record']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
