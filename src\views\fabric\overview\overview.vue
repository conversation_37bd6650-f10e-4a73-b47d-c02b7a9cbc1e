<template>
  <div class="overview_page">
    <!--<SelectChain @getStatus="getStatus"></Select<PERSON>hain>-->
    <!--<Prompt v-if="!chainStatus" type="fail"></Prompt>-->
    <template>
        <ul class="imglist">
          <!-- @click="goPage(item)" -->
          <li v-for="item in imageList" :key="item.name">
            <img :src="item.liImgUrl" class="liImage" />
            <img :src="item.iconImgUrl" class="iconImage" />
            <span class="number">{{ item.number }}</span>
            <span class="name">
              {{ item.name }}
            </span>
          </li>
        </ul>
      <!-- <div class="info">
        <div class="infoTitle">
          <img :src="infoIcon" class="infoIcon" />
          <span class="infotext">链配信息</span>
        </div>
        <div class="infoList">
          <div class="infoItem" v-for="item in infoImageList" :key="item.name">
            <img :src="item.imageUrl" class="infoItemImage" />
            <div class="textWrap">
              <div class="name">{{ item.name }}</div>
              <div class="text" v-if="item.name == '集群名称'">
                {{ item.text }}
              </div>
              <div class="text" v-if="item.name == '共识方式'">
                {{ getName(item.text) }}
              </div>
              <div class="text" v-if="item.name == '创建时间'">
                {{ dateFormat("YYYY-mm-dd", item.text) }}
              </div>
              <div
                class="text"
                v-if="item.name == '运行状态'"
                :class="{
                  active: item.text == '0',
                  restart: item.text == '1',
                  close: item.text == '2'
                }"
              >
                {{ getChainStatus(item.text) }}
              </div>
              <div class="text" v-if="item.name == '加密方式'">
                {{ item.text == "SM2" ? "国密" : "ECDSA" }}
              </div>
            </div>
          </div>
        </div>
      </div> -->
      <!-- <div class="table info">
        <div class="infoTitle">
          <img :src="infoIcon" class="infoIcon" />
          <span class="infotext">节点信息</span>
        </div>
        <el-row class="nav-box">
          <el-col :span="3"><div class="">序号</div></el-col>
          <el-col :span="4"><div class="">名称</div></el-col>
          <el-col :span="3"><div class="">角色</div></el-col>
          <el-col :span="4"><div class="">组织</div></el-col>
          <el-col :span="3"><div class="">主机</div></el-col>
          <el-col :span="3"><div class="">重启次数</div></el-col>
          <el-col :span="4"><div class="">运行状态</div></el-col>
        </el-row>
        <div class="nan-item" v-for="(orderdata, index) in listData" :key="index">
          <el-row class="nav-box">
            <el-col :span="3" v-if="index < 9"
              ><div class="">
                <span>{{ "00" + (index + 1) }}</span>
              </div></el-col
            >
            <el-col :span="3" v-else
              ><div class="">
                <span>{{ "0" + (index + 1) }}</span>
              </div></el-col
            >
            <el-col :span="4">
              <div class="">
                <span>
                  <el-popover trigger="hover" placement="top">
                    <p>{{ orderdata.Name }}</p>
                    <div slot="reference" class="name-wrapper">
                      <span slot="reference">{{
                        checkName(orderdata.Name)
                      }}</span>
                    </div>
                  </el-popover>
                </span>
              </div>
            </el-col>
            <el-col :span="3"
              ><div class="">
                <span>{{ orderdata.Type }}</span>
              </div></el-col
            >
            <el-col :span="4"
              ><div class="">
                <span>{{ orderdata.GroupName }}</span>
              </div></el-col
            >
            <el-col :span="3"
              ><div class="">
                <span>{{ orderdata.NodeName }}</span>
              </div></el-col
            >
            <el-col :span="3"
              ><div class="">
                <span>{{ orderdata.RestartCount }}</span>
              </div></el-col
            >
            <el-col :span="4"
              ><div class="">
                <span
                  class="status"
                  :class="{
                    active: orderdata.Status == '0',
                    restart: orderdata.Status == '1',
                    close: orderdata.Status == '2'
                  }"
                  >{{ getNodeStatus(orderdata.Status) }}</span
                >
              </div></el-col
            >
          </el-row>
        </div>
        <div class="pagination">
          <pagination
            @toHandleSizeChange="handleSizeChange"
            @toHandleCurrentChange="handleCurrentChange"
            @toJumpFirstPage="jumpFirstPage"
            @toJumpLastPage="jumpLastPage"
            :fTotal="total"
            :fBtnStartDisabled="btnStartDisabled"
            :fBtnEndDisabled="btnEndDisabled"
            :fPageIndex="pageIndex"
            :fZys="zys"
          >
          </pagination>
        </div>
      </div> -->
      <!-- 链性能资源 -->
      <div>
        <div class="info" ref="everyWid">
          <div class="infoTitle">
            <img :src="infoIcon" class="infoIcon" />
            <span class="infotext">链性能资源</span>
          </div>
        </div>
        <!-- 折线图 -->
        <div class="zxList" v-show="noL == true">
          <template v-for="(item, index) in ljList">
            <div class="zxModule" :key="index">
              <div :id="item.id" class="zxSty"></div>
            </div>
          </template>
        </div>
        <div class="none" v-show="noL == false" ref="noThing">
          <i class="el-icon-loading" v-if="zxtPaddingText == '数据请求中...'"></i>
          <!-- <svg-icon icon-class="table-empty" v-else/> -->
          {{ zxtPaddingText }}
        </div>
      </div>
      <Resource></Resource>
    </template>
    <!-- <countDown v-if="isShowIcon" :state="countState" :countTime="countTime" :text="countText" @getCountDown="getCountDown"></countDown> -->
  </div>
</template>

<script>

import {
  getChainStatistics,
  getChainState,
  getChainMonitor,
} from "@/api/baascore/overview";
import {
  getChainPodList,getChainNodeNameList
} from "@/api/baascore/ywResource";
import echarts from "echarts";
import {forbidden} from "@/utils/index.js";
import zxt from "@/utils/zxmixin";
import { getCurrentDate,diffNumber } from "@/utils/validate";
import SelectChain from '../compontents/selectChain'
import Resource from '@/views/fabric/resource/index'
export default {
  mixins: [forbidden, zxt],
  // 组件部分
  components: {
    SelectChain,
    Resource,
  },
  data() {
    return {
      chainStatus:true,
      countState:'',
      countTime:2,
      countText:'',
      isShowIcon:false,
      zxtPaddingText: "数据请求中...",
      btnStartDisabled: false, //用来判断首页尾页按钮是否禁用
      btnEndDisabled: false, //用来判断首页尾页按钮是否禁用
      zys: 0,
      pageIndex: 1,
      imageList: [
        {
          liImgUrl: require("@/assets/chainManage_images/overview/libg1.png"),
          iconImgUrl: require("@/assets/chainManage_images/overview/icon1.png"),
          number: "",
          name: "节点数量"
        },
        {
          liImgUrl: require("@/assets/chainManage_images/overview/libg2.png"),
          iconImgUrl: require("@/assets/chainManage_images/overview/icon2.png"),
          number: "",
          name: "通道数量"
        },
        {
          liImgUrl: require("@/assets/chainManage_images/overview/libg3.png"),
          iconImgUrl: require("@/assets/chainManage_images/overview/icon3.png"),
          number: "",
          name: "合约数量"
        },
        {
          liImgUrl: require("@/assets/chainManage_images/overview/libg4.png"),
          iconImgUrl: require("@/assets/chainManage_images/overview/icon4.png"),
          number: "",
          name: "区块高度"
        },
        {
          liImgUrl: require("@/assets/chainManage_images/overview/libg5.png"),
          iconImgUrl: require("@/assets/chainManage_images/overview/icon5.png"),
          number: "",
          name: "交易数量"
        }
      ],
      infoImageList: [
        {
          imageUrl: require("@/assets/chainManage_images/overview/info1.png"),
          name: "集群名称",
          text: ""
        },
        {
          imageUrl: require("@/assets/chainManage_images/overview/info2.png"),
          name: "共识方式",
          text: ""
        },
        {
          imageUrl: require("@/assets/chainManage_images/overview/info3.png"),
          name: "加密方式",
          text: ""
        },
        {
          imageUrl: require("@/assets/chainManage_images/overview/info4.png"),
          name: "创建时间",
          text: ""
        },
        {
          imageUrl: require("@/assets/chainManage_images/overview/info5.png"),
          name: "运行状态",
          text: ""
        }
      ],
      infoIcon: require("@/assets/chainManage_images/overview/infoIcon.png"),
      tableData: [],
      curChain: {},
      channeListLength: 0,
      peerLength: 0,
      chainState: "",
      total: 0,
      chainInfo: {},
      pageSize: 10,
      pageIndex: 1,
      listData: [],
      ljCpuList: [], //链节点资源Cpu使用率
      ljXCpulist: [], //链节点资源X轴数据
      tooltipList:[],
      ljNcList: [], //链节点资源内存使用率
      ljXNclist: [], //链节点资源内存X轴数据
      ljCcList: [], //链节点资源存储使用率
      ljXCclist: [], //链节点资源存储X轴数据
      ljUpdateList: [], //链节点资源上传使用率
      ljXUplist: [], //链节点资源上传X轴数据
      ljDownLoadList: [], //链节点资源下载使用率
      ljXDolist: [], //链节点资源下载X轴数据
      ncMax: "", //获取内存使用率单位
      ccMax: "", //获取存储使用量单位
      scMax: "", //获取上传单位
      xzMax: "", //获取下载单位
      // 链节点资源
      ljList: [
        {
          id: "zxCpu",
          unit: "(单位：%)"
        },
        {
          id: "zxNc",
          unit: "(单位：%)"
        },
        {
          id: "zxCc",
          unit: "(单位：kb/s)"
        },
        {
          id: "zxSc",
          unit: "(单位：kb/s)"
        }
      ],
      ljdList: [], //所有的链节点
      noL: false, //没有链的时候
      parentClientWidth: 0,

    };
  },
  watch: {
    // chainItem: {
    //   handler(newvalue, oldvalue) {
    //     this.chainInfo = JSON.parse(sessionStorage.getItem("chainItem"));
    //     if (newvalue.Id) {
    //       //获取链状态
    //       this.xzMax=this.scMax='';
    //       this.getChainState();
    //       //  所有节点列表
    //       this.ChainNodeNameList();
    //     }
    //     if (!this.chainInfo) {
    //       this.$router.push({
    //         path: "/guide/guide"
    //       });
    //     }
    //   },
    //   deep: true
    // }
  },
  mounted() {
    // if (this.chainStatus) {
      //this.overview_btn_add = this.elements['overview:btn_add']
      window.addEventListener('resize',this.initEcharts,false)
      this.chainInfo = JSON.parse(sessionStorage.getItem("chainItem"));
    // }
    if (this.chainInfo) {
      //获取链状态
      this.xzMax=this.scMax='';
      //this.getChainState();
      //  所有节点列表
      this.ChainNodeNameList();
    }
  },
  computed: {
    checkName(name) {
      return function(name) {
        if (name.indexOf("-") != -1) {
          var strList = name.split("-");
          name = strList[0] + "-" + strList[1];
          return name;
        } else {
          return name;
        }
      };
    },
    getName(name) {
      return function(name) {
        if (name.indexOf("_") != -1) {
          var strList = name.split("_");
          name = strList[0];
          return name;
        } else {
          return name;
        }
      };
    },
    getNodeStatus(status) {
      return function(status) {
        if (status == "0") {
          return "运行中";
        } else if (status == "1") {
          return "等待中";
        } else if (status == "2") {
          return "不可用";
        }
      };
    },
    getChainStatus(status) {
      return function(status) {
        if (status == "0") {
          return "运行中";
        } else if (status == "1") {
          return "启动中";
        } else if (status == "2") {
          return "故障";
        }
      };
    },

    dateFormat(fmt, date) {
      return function(fmt, date) {
        date = new Date(date);
        let ret;
        const opt = {
          "Y+": date.getFullYear().toString(), // 年
          "m+": (date.getMonth() + 1).toString(), // 月
          "d+": date.getDate().toString(), // 日
          "H+": date.getHours().toString(), // 时
          "M+": date.getMinutes().toString(), // 分
          "S+": date.getSeconds().toString() // 秒
          // 有其他格式化字符需求可以继续添加，必须转化成字符串
        };
        for (let k in opt) {
          ret = new RegExp("(" + k + ")").exec(fmt);
          if (ret) {
            fmt = fmt.replace(
              ret[1],
              ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
            );
          }
        }
        return fmt;
      };
    }
  },
  methods: {
    // getStatus(v) {
    //   console.log('type', v)
    //   this.chainStatus=v
    // },
    initEcharts() {
      setTimeout(() =>{
        this.parentClientWidth =  this.$refs.everyWid.clientWidth / 2 - (this.$refs.everyWid.clientWidth / 2) * 0.08;
        let setWid = {
          width: this.parentClientWidth
        };
        echarts.init(document.getElementById("zxCpu")).resize(setWid);
        echarts.init(document.getElementById("zxNc")).resize(setWid);
        echarts.init(document.getElementById("zxCc")).resize(setWid);
        echarts.init(document.getElementById("zxSc")).resize(setWid);
      },300)
    },
    getCountDown(type) {
      this.isShowIcon = false
    },
    // handleSizeChange(val) {
    //   this.pageSize = val;
    //   this.getListData();
    // },
    // handleCurrentChange(val) {
    //   this.pageIndex = val;
    //   this.getListData();
    // },
    // // 首页按钮
    // jumpFirstPage(val) {
    //   this.pageIndex = val;
    //   this.handleCurrentChange(val);
    // },
    // // 尾页按钮
    // jumpLastPage(val) {
    //   this.pageIndex = val;
    //   this.handleCurrentChange(this.pageIndex);
    // },
    // getListData() {
    //   this.zys = Math.ceil(this.total / this.pageSize); //获取总页数
    //   this.forbidden(this.zys, this.pageIndex);
    //   this.listData = this.tableData.slice(
    //     (this.pageIndex - 1) * this.pageSize,
    //     this.pageSize * this.pageIndex
    //   );
    // },
    //点击跳转页面
    goPage(page) {
      var name = page.name;
      switch (name) {
        case "节点数量":
          this.$router.push({
            path: "/chainManage/orgNodes"
          });
          break;
        case "通道数量":
          this.$router.push({
            path: "/chainManage/channelMgr"
          });
          break;
        case "合约数量":
          this.$router.push({
            path: "/chainManage/chaincodeMgr"
          });
          break;
      }
    },
    //获取链状态
    getChainState() {
      var query = {
        ServiceId: this.chainInfo.Id
      };
      getChainState(query).then(res => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          this.chainState = res.data;
          //获取链信息
          //this.getChainInfo();
        } else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");

        }
      });
    },
    //查询链统计信息
    getChainStatistics() {
      var query = {
        ServiceId: this.chainInfo.Id
      };
      getChainStatistics(query).then(res => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          var data = res.data;
          this.imageList.forEach(item => {
            switch (item.name) {
              case "节点数量":
                item.number = this.ljdList.length;
                // item.number = this.peerLength;
                break;
              case "通道数量":
                item.number = data.Channel;
                // sessionStorage.setItem('channeListLength',this.channeListLength)
                break;
              case "合约数量":
                item.number = diffNumber(data.ChainCode);
                break;
              case "区块高度":
                item.number = diffNumber(data.Block);
                break;
              case "交易数量":
                item.number = diffNumber(data.Trans);
                break;
            }
          });
        } else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      });
    },
    //获取链信息
    getChainInfo() {
      var chainInfo = this.chainInfo;
      this.infoImageList.forEach(item => {
        switch (item.name) {
          case "集群名称":
            item.text = chainInfo.Cluster;
            break;
          case "共识方式":
            item.text = chainInfo.TemplateType;
            break;
          case "加密方式":
            item.text = chainInfo.CryptoType;
            break;
          case "创建时间":
            item.text = chainInfo.CreateTime;
            break;
          case "运行状态":
            item.text = this.chainState;
            break;
        }
      });
    },
    //获取节点信息
    getChainPodList() {
      var query = {
        ServiceId: this.chainInfo.Id
      };
      getChainPodList(query).then(res => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          var data = res.data;
          var arr = [];
          data.forEach(item => {
            if (item.GroupName && item.NodeInfo && item.NodeInfo.length > 0) {
              item.NodeInfo.map(citem => {
                citem.GroupName = item.GroupName;
              });
              arr = arr.concat(item.NodeInfo);
            }
          });
          this.tableData = arr;
          this.total = this.tableData.length;
          this.peerLength = this.tableData.length;
          // this.getListData();
          //查询链统计信息
         // this.getChainStatistics();
        } else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      });
    },
    //  所有节点列表
    ChainNodeNameList() {
      let parms = {
        ServiceId: this.chainInfo.Id
      };
      this.xzMax=this.scMax='';
      getChainNodeNameList(parms).then(res => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          this.ljdList = [];
          res.data.forEach((item, index) => {
            this.ljdList.push(item.NodeName);
          });
          //查询链统计信息
          this.getChainStatistics();
          // 链节点资源数据查询
          this.ChainMonitor();
        } else {
          this.noL = false;
          this.zxtPaddingText = "暂无数据";
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      });
    },
    // 链节点资源数据查询
    ChainMonitor() {
      let parms = {
        ClusterId: this.chainInfo.Cluster,
        ServiceName: this.chainInfo.Name,
        NodeName: this.ljdList.join(),
        StartTime: Date.parse(getCurrentDate(1))
          .toString()
          .substring(0, 10),
        EndTime: Date.parse(getCurrentDate(0))
          .toString()
          .substring(0, 10)
      };
      getChainMonitor(parms).then(res => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if (res.data && res.data.Result) {
            this.noL = true;
            res.data.Result.forEach((item, index) => {
              if(item.Metric && item.Metric.Job) {
                let GetTitleName = item.Metric.Job;
                this.filterZx(GetTitleName, item);
              }
            });
            // v-if第一次失败后面加载不出来，
            if (document.getElementById("zxCpu")) {
              this.initEcharts()
            }
            this.ljdChart();
          } else {
            this.noL = false;
            this.zxtPaddingText = "暂无数据";
          }
        } else {
          this.noL = false;
          this.zxtPaddingText = "暂无数据";
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      });
    },
  },
  destroyed () {
    window.removeEventListener('resize',this.initEcharts)
  }
};
</script>
<style>
  /*重置select 字体颜色 hover 背景色*/
  .el-select-dropdown__item.selected{
    color:#337DFF;
    font-weight:normal;
  }
  .el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
    background-color: #F5F8FF;
  }

</style>
<style rel="stylesheet/less" lang="less" scoped>
  /*@import "src/styles/common/selectG.css";*/
  @import "../../../styles/common/select.less";

  .overview_page {
  width: 100%;
  height: 100%;
  //padding-bottom: 80px;
  overflow: hidden;
  // width: 90%;
  // min-height: 100%;
  // margin: auto;
  // padding-top: 30px;
  .imglist {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin:20px 0 10px;
    li {
      width: 19%;
      position: relative;
      height: 120px;
      .liImage {
        width: 100%;
        height:120px;
      }
      .iconImage {
        // width: 35%;
        // position: absolute;
        // top: 17%;
        // left: 5%;
        width: 70px;
        height: 70px;
        position: absolute;
        top: 25px;
        left: 16px;
      }
      .number {
        position: absolute;
        top: 25px;
        right:26px;
        // font-size: 58px;
        font-size: 32px;
        color: #ffffff;
      }
      .name {
        position: absolute;
        bottom: 25px;
        right: 26px;
        // font-size: 20px;
        font-size: 14px;
        color: #ffffff;
      }
    }
  }
  .info {
    width: 100%;
    margin-top: 20px;
    .infoTitle {
      // font-size: 22px;
      font-size: 14px;
      .infoIcon {
        width: 3px;
        height: 14px;
        margin-right:5px;
        vertical-align: middle;
      }
      .infotext {
        color: #333333;
        vertical-align: middle;
      }
    }
    .infoList {
      width: 100%;
      background: #ffffff;
      border: 2px solid rgba(228, 227, 227, 0.22);
      box-shadow: 0px 4px 10px 0px rgba(218, 218, 218, 0.17);
      border-radius: 3px;
      margin-top: 30px;
      padding: 24px 12px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      .infoItem {
        width: 19%;
        display: flex;
        align-items: center;
        .infoItemImage {
          width: 35%;
          margin-right: 18px;
        }
        .textWrap {
          word-break: break-all;
          .name {
            // font-size: 16px;
            font-size: 14px;
            color: #999999;
          }
          .text {
            margin-top: 16px;
            // font-size: 20px;
            font-size: 14px;
            color: #666666;
          }
          .active {
            color: #00ada2;
          }
          .restart {
            color: #1973cc;
          }
          .close {
            color: #e15500;
          }
        }
      }
    }
  }
  .table {
    margin-top: 30px;
    .nav-box {
      color: #999999;
      // font-size: 18px;
      font-size: 14px;
      box-sizing: border-box;
      //background: #F3F4FD;
      /*margin-top: 20px;*/
    }
    .nav-box /deep/ .el-col {
      text-align: center;
      color: #666666;
      // font-size: 18px;
      font-size: 14px;
      padding: 10px 0;
      box-sizing: border-box;
    }
    .nav-box .el-col div span {
      // margin:0 3px;
      /*cursor: default;*/
    }
    .nan-item .nav-box:hover {
      -moz-box-shadow: 2px 2px 6px #bbd1e6, -2px -2px 6px #bbd1e6;
      -webkit-box-shadow: 2px 2px 6px #bbd1e6, -2px -2px 6px #bbd1e6;
      box-shadow: 2px 2px 6px #bbd1e6, -2px -2px 6px #bbd1e6;
    }
    .nan-item .nav-box {
      color: #666666;
      // font-size: 16px;
      box-sizing: border-box;
      background: #fff;
      display: flex;
      align-items: center;
    }
    .nan-item .nav-box .el-col div span.status {
      display: inline-block;
      width: 102px;
      height: 36px;
      border-radius: 4px;
      text-align: center;
      line-height: 36px;
    }
    .nan-item .nav-box .el-col div span.active {
      color: #00ada2;
      background: rgba(9, 223, 192, 0.2);
    }
    .nan-item .nav-box .el-col div span.restart {
      color: #1973cc;
      background: rgba(25, 115, 204, 0.2);
    }
    .nan-item .nav-box .el-col div span.close {
      color: #e15500;
      background: rgba(255, 85, 0, 0.2);
    }
  }
  .pagination {
    /*margin-top: 16px;*/
    // display: flex;
    // justify-content: flex-end;
    // align-items: center;
  }
  // 折线图
  .zxList {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .zxModule {
      width: 48.5%;
      height: 290px;
      padding: 20px 20px;
      background: #ffffff;
      box-shadow: 0px 0px 20px 0px rgba(218, 218, 218, 0.6);
      border-radius: 4px;
      margin: 20px 4px 10px;
      position: relative;
      .zxSty {
        width: 100%;
        height: 290px;
      }
      .timeList {
        position: absolute;
        bottom: 18px;
        width: 80%;
        margin-left: 12%;
        display: flex;
        justify-content: space-between;
        font-size: 12px;

        font-weight: 400;
        color: #666666;
      }
    }
  }
  .none {
    // font-size: 18px;
    text-align: center;
    line-height: 65px;
    color: #666666;
    background: #fff;
    margin: 30px 0px 0px;
  }
  .content {
    .content_nav {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      span {
        color: #666666;
        // font-size: 18px;
      }
      .left {
        display: flex;
        align-items: center;
        .btn {
          margin-left: 20px;
          width: 120px;
          background: #337DFF;
          color: #ffffff;
        }
      }

      .child_nav {
        position: relative;
        a {
          margin-right: 40px;
        }
        .router-link-active {
          position: relative;
          .activeup {
            position: absolute;
            bottom: -18px;
            left: -2px;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #1973cc, #1973cc);
            z-index: 2;
          }
        }
      }
    }
    .line {
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, #1973cc, #1973cc, #55cec7);
      opacity: 0.25;
      margin-top: 18px;
    }
    .child {
      margin-top: 36px;
    }
  }


}
.left /deep/ .el-input--medium .el-input__inner {
  /*width: 180px !important;*/
  height: 48px !important;
  /*font-size: 20px;*/
}
.left /deep/ .el-button {
  /*height: 48px !important;*/
  /*font-size: 20px;*/
  /*font-weight: bold;*/
}
</style>
