<template>
  <div>
    <div class="forgetpwd">
      <h1>
        <img :src="logoUrl"><br>
        <!-- <b>CMBaaS</b> -->
        <span style="font-size:18px;">中移动CMBaaS —— 同心共筑，"链"接未来</span>
      </h1>
      <card style="width:440px">
        <div style="text-align:center;margin-top:10px;">
          <span style="font-size:20px;font-weight: bold;">重置密码</span><br />
          <Avatar size="small" style="background:#2d8cf0;margin:25px 5px;">1</Avatar>
          <span>邮箱验证</span>
          <span style="color:#E5E5E5;"> ———————————— </span>
          <Avatar size="small" style="margin-right:5px;">2</Avatar>
          <span style="color:#a5a5a6;">设置新密码</span>
        </div>
        <div style="margin:10px 19px 0px;">
          <Form ref="formData" :model="formData" :rules="formDataRule">
            <FormItem prop="userLoginId">
              <Input prefix="ios-contact" placeholder="平台用户名" style="width:370px;" v-model="formData.userLoginId" />
            </FormItem>
            <FormItem prop="email">
              <Input prefix="md-mail" placeholder="邮箱" style="width:370px;opacity:.9;" v-model="formData.email">
              </Input>
            </FormItem>
            <FormItem prop="emailCode">
              <div style="display: flex;align-items: center;">
                <Input prefix="md-checkmark-circle" type="text" style="width:274px" v-model="formData.emailCode" placeholder="请输入验证码">
                </Input>
                <Button type="primary" style="margin-left:8px;padding:2px;width:86px;height:30px;" :disabled="!!cooling" @click="getCaptcha"> {{ cooling ? "重新发送(" + cooling + ")" : " 发送验证码 " }}</Button>
              </div>
            </FormItem>
            <FormItem>
              <Button type="primary" @click="handleNext" style="width:370px;">下一步</Button>
              <p style="padding-top:10px;;text-align:right">
                <router-link to="/login">返回登录</router-link>
              </p>
            </FormItem>
          </Form>
        </div>
      </card>
    </div>
    <div class="footer-copyright">
      <p style="margin-left:30%">{{ versionData.version  }}</p>
      <span>{{ versionData.copyright }}</span>
    </div>
    <canvas class="cash" id="" :style='"background:url("+images+") no-repeat;background-size:100% 100%;"'></canvas>
  </div>
</template>

<script>
import { isEmail, isNumber } from '../lib/check'
import { checkVerificationCode, requestVerificationCode } from '@/api/data'
import { newDate } from '../../static/config.json'
export default {
  name: 'forget_pwd',
  data () {
    const validateEmail = (rule, value, callback) => {
      if (!isEmail(value)) {
        callback(new Error('邮箱格式不正确'))
      } else {
        callback()
      }
    }
    const validateEmailCode = (rule, value, callback) => {
      if (!(/^\d{6}$/.test(value))) {
        callback(new Error('请输入6位数字'))
      } else {
        callback()
      }
    }
    return {
      cooling: 0,
      captcha: '',
      // emailPrefix: '@chinamobile.com',
      formData: {
        email: '',
        emailCode: '',
        userLoginId: ''
      },
      formDataRule: {
        email: [
          { required: true, trigger: 'blur', validator: validateEmail }
        ],
        userLoginId: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, max: 15, message: '长度不能超过15', trigger: 'blur' }
        ],
        emailCode: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateEmailCode }
        ]
      },
      images: require('@/assets/img/bg1.png'),
      newDate: '',
      logoUrl: ''
    }
  },
  mounted () {
    this.newDate = newDate
    // starsNest()
    this.$nextTick(() => {
      this.$refs['formData'].resetFields()
    })
    this.init()
    // this.$Message.config({
    //   top: 300,
    //   duration: 2
    // })
    const storedVersionData = JSON.parse(sessionStorage.getItem('versionData')) || {};
    this.versionData = {
      version: storedVersionData.version || '产品版本：1.5.0',
      copyright: storedVersionData.copyright || 'Copyright © 2022 中移信息技术有限公司',

    };
    this.logoUrl = storedVersionData.logoUrl || '/data/iconLogin.png';
  },
  methods: {
    // ...mapActions([
    //   'register'
    // ]),
    init () {
      this.formData = {
        email: '',
        emailCode: '',
        userLoginId: ''
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    handleNext () {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          checkVerificationCode(this.formData.userLoginId, this.formData.email, null, this.formData.emailCode, 'RESET_PASSWORD').then(res => {
            if (res.code === '00000') {
              // console.log('res:', res)
              this.msgInfo('success', res.message, true)
              this.$router.push({
                name: 'reset_pwd',
                params: {
                  userLoginId: this.formData.userLoginId,
                  email: this.formData.email
                }
              })
            } else {
              // console.log('error:', res.message)
              this.msgInfo('error', res.message, true)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        }
      })
    },
    getCaptcha () {
      // this.msgInfo('warning', '邮箱验证接口暂未开通', true)
      if (this.formData.email && this.formData.userLoginId) {
        requestVerificationCode(this.formData.userLoginId, this.formData.email, null, 'RESET_PASSWORD').then(res => {
          if (res.code === '00000') {
            this.msgInfo('success', res.message, true)
            this.cooling = 60
            let coolingInter = setInterval(() => {
              if (this.cooling > 0) {
                this.cooling--
              } else {
                clearInterval(coolingInter)
              }
            }, 1000)
          } else {
            this.msgInfo('error', res.message, true)
          }
        }).catch(error => {
          this.msgInfo('error', error.message, true)
        })
      } else {
        this.msgInfo('error', '请先填写用户名以及邮箱', true)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.cash {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000c17;
}
div.forgetpwd {
  display: grid;
  position: absolute;
  left: 50%;
  top: 46%;
  transform: translate(-50%, -50%);
  z-index: 100;
  opacity: 0.8;
  h1 {
    margin: 8px;
    b {
      text-align: center;
      margin: 0 10px;
      color: #fff;
    }
    span {
      font-size: small;
      color: #ccc;
    }
  }
  input {
    //margin-bottom: 10px;
    opacity: 0.9;
  }
  .line {
    height: 1px;
    width: 133px;
    background: rgba(255, 255, 255, 1);
    border: none;
    border-top: 1px solid #555555;
  }
  /deep/.ivu-card-body {
    padding: 10px 16px 0 16px;
  }
}
</style>
