<template>
  <div>
    <div style="width：auto;margin-left: 10px;">
      <Card style="width: auto;">
        <!-- <p class="info-title" style="margin:10px 0 15px 10px;"><span class="bs">基本信息</span></p> -->
        <div class="info-title addflex">
          <div>
            <div class="bs"></div>
            <span>基础信息</span>
          </div>
        </div>
        <Form ref="formValidate" :model="formValidate" :label-width="140">
          <FormItem label="项目名称：">
            <p class="descClass">{{projectName}}</p>
          </FormItem>
          <FormItem label="项目描述：" prop="desc">
            <p class="descClass">{{projectBrief}}</p>
          </FormItem>
          <FormItem label="上传文件：">
            <RadioGroup v-model="formValidate.radio" @on-change='getGroup'>
              <Radio label="文档上传">文档/图片上传</Radio>
              <Radio label="压缩包上传">压缩包上传</Radio>
              <Radio label="视频上传">视频上传</Radio>
            </RadioGroup>
          </FormItem>
        </Form>
        <div>
          <!-- 文档 -->
          <p class="basetext" style="margin-bottom:10px" v-if="formValidate.radio==='文档上传'">
            <Upload multiple type="drag" action="" :before-upload="handleSecurity" :accept="'.jpg,.png,.jpeg,.doc,.docx,.txt,.pdf,.xlsx,.xls,.JPG,.PNG,.JPEG,.DOC,.DOCX,.TXT,.PDF,.XLSX,.XLS'" :format="['jpg','png','jpeg','doc','docx','txt','pdf','xlsx','xls','JPG','PNG','JPEG','DOC','DOCX','TXT','PDF','XLSX','XLS']">
              <div style="padding: 10px 0" v-if="this.fileStatus.file===false">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <h4>支持拖拽上传文件</h4><br />
                <p style="color: #aaa;font-size: 12px;">支持doc,docx,txt,pdf,xlsx,xls格式的文件或jpg,png,jpeg格式的图片上传</p>
              </div>
              <div style="padding: 20px 0" v-else>
                <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
                <h4>完成文件上传</h4>
                <p style="color: color: #3399ff;font-size: 15px;">{{ securityFile? securityFile : '' }}</p>
              </div>
            </Upload>
          </p>
          <!-- 压缩包 -->
          <p class="basetext" style="margin-bottom:10px" v-if="formValidate.radio==='压缩包上传'">
            <Upload multiple type="drag" action="" :before-upload="handleSecurity" :accept="'.zip,.tar,.ZIP,.TAR'" :format="['zip','tar','ZIP','TAR']">
              <div style="padding: 10px 0" v-if="this.fileStatus.zip===false">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <h4>支持拖拽上传文件</h4><br />
                <p style="color: #aaa;font-size: 12px;">支持zip,tar格式的压缩包上传</p>
              </div>
              <div style="padding: 20px 0" v-else>
                <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
                <h4>完成文件上传</h4>
                <p style="color: color: #3399ff;font-size: 15px;">{{securityFileTar? securityFileTar : ''  }}</p>
              </div>
            </Upload>
          </p>
          <!-- 视频 -->
          <p class="basetext" style="margin-bottom:10px" v-if="formValidate.radio==='视频上传'">
            <Upload multiple type="drag" action="" :before-upload="handleSecurity" :accept="'.mp4,.avi,.mov,.vmw,.flv,.wemb,.mkv,.mpeg,MP4,AVI,MOV,VMW,FLV,WEMB,MKV,MPEG'" :format="['mp4','avi','mov','vmw','flv','wemb','mkv','mpeg','AVI','MOV','VMW','FLV','WEMB','MKV','MPEG']">
              <div style="padding: 10px 0" v-if="this.fileStatus.video===false">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <h4>支持拖拽上传文件</h4><br />
                <p style="color: #aaa;font-size: 12px;">支持mp4,avi,mov,vmw,flv,wemb,mkv,mpeg,MP4,AVI,MOV,VMW,FLV,WEMB,MKV,MPEG格式的视频文件上传</p>
              </div>
              <div style="padding: 20px 0" v-else>
                <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
                <h4>完成文件上传</h4>
                <p style="color: color: #3399ff;font-size: 15px;">{{securityFileMp4? securityFileMp4 : '' }}</p>
              </div>
            </Upload>
          </p>

        </div>
        <div style="margin-left: 46%;margin-top: 3%;">
          <Button @click="handleReset('formValidate')" :disabled="hasEditPermission">删除</Button>
          <Button type="primary" @click="handleSubmit('formValidate')" :disabled="hasEditPermission" :loading='fileLoading' style="margin-left: 8px">提交上传</Button>
        </div>
      </Card>
    </div>
    <Card style="margin:20px 5px 10px 10px;">
      <div class="node" style="width：auto;margin:10px 5px 0px 10px;">
        <div>
          <edit-table-mul :columns="columns" v-model="tableData" :key="transferKey" style="margin-top:20px;"></edit-table-mul>
        </div>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[5,10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;margin:10px 0;" />
      </div>
    </Card>

  </div>
</template>
<script>
import EditTableMul from '_c/edit-table-mul'
import { fileUploadmanyFile, getFileListDetail, nodeList, getFileDetete, getFileDown } from '@/api/contract'
import loginVue from '../../login.vue'
import { localRead } from '@/lib/util'
export default {
  name: 'file_detail',
  components: {
    EditTableMul
  },
  data () {

    return {
      fileStatus: {
        file: false,
        zip: false,
        video: false,
      },

      addModal: false,
      transferKey: 0,
      formValidate: {
        radio: '文档上传'
      },
      columns: [
        { key: 'fileName', title: '文档名称' },
        { key: 'fileType', title: '格式', tooltip: true },
        { key: 'updateTime', title: '上传时间' },
        {
          key: 'action',
          title: '操作',
          minWidth: 120,
          render: (h, params) => {
            return h('div', [
              h(
                'Poptip',
                {
                  props: {
                    transfer: true,
                    placement: 'top-end',
                    confirm: true,
                    title: '确认删除吗?',
                    'ok-text': '确认',
                    'cancel-text': '取消'
                  },
                  on: {
                    'on-ok': () => {
                      this.deletedData(params.row)
                    }
                  }
                },
                [
                  h(
                    'Button', {
                    props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                    style: this.buttonStyle,
                  }, '删除'
                  )
                ]
              ),
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    this.DownFile(params.row)
                  }
                }
              }, '下载')
            ]

            )
          }
        }
      ],
      tableData: [],
      tablePageParam: {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      },

      noderow: {},
      resous: {},
      fileSize: '',
      fileSizeTar: '',
      fileSizeMp4: '',
      securityFile: '',
      securityFileTar: '',
      securityFileMp4: '',
      fileId: '',
      projectName: '',
      projectBrief: '',
      fileType: '',
      fileSizeAll: '',//文件大小
      fileLoading: false,
      userPermission: JSON.parse(localRead('userPermission')),

    }
  },
  methods: {

    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    DownFile (hash) {
      getFileDown(hash.fileHash).then(res => {
        var blob = new Blob([res])
        var downloadElement = document.createElement('a')
        var href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = hash.fileName
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)


      }).catch(error => {

        this.msgInfo('error', '该角色暂未配置资源或已禁用', true)
      })
    },


    // 删除
    deletedData (row) {
      let deteleId = {
        id: row.id
      }
      getFileDetete(deteleId).then(res => {
        if (res.code === '00000') {
          this.getDetailList()
          this.msgInfo('success', res.message, true)
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
      // this.tableData.splice(index, 1)
    },



    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getDetailList()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getDetailList()
    },

    // 上传
    handleSecurity (file) {
      this.fileSizeAll = file.size
      let name = file.name.toLowerCase().split('.').pop()
      if (this.formValidate.radio === '文档上传') {
        if (name === 'doc' || name === 'docx' || name === 'txt' || name === 'pdf' || name === 'xlsx' || name === 'xls' || name === 'jpg' || name === 'png' || name === 'jpeg') {
          this.fileStatus.file = true
          this.fileSize = file
          this.securityFile = file.name
        } else {
          this.msgInfo('error', '请上传doc,docx,txt,pdf,xlsx,xls格式的文件或jpg,png,jpeg格式的图片上传', true)
        }
      } else if (this.formValidate.radio === '压缩包上传') {
        if (name === 'zip' || name === 'tar') {
          this.fileStatus.zip = true
          this.fileSizeTar = file
          this.securityFileTar = file.name

        } else {
          this.msgInfo('error', '请上传zip,tar格式的文件', true)
        }
      } else {
        if (name === 'mp4' || name === 'avi' || name === 'mov' || name === 'vmw' || name === 'flv' || name === 'wemb' || name === 'mkv' || name === 'mpeg') {
          this.fileStatus.video = true
          this.fileSizeMp4 = file
          this.securityFileMp4 = file.name
        } else {
          this.msgInfo('error', '请上传mp4,avi,mov,vmw,flv,wemb,mkv,mpeg,MP4,AVI,MOV,VMW,FLV,WEMB,MKV,MPEG格式的文件', true)
        }
      }

      return false
    },
    getGroup (e) {
      this.securityFile = ''
      this.fileSize = ''
      this.fileStatus.file = false
      this.fileStatus.zip = false
      this.securityFileTar = ''
      this.fileSizeTar = ''
      this.fileStatus.video = false
      this.securityFileMp4 = ''
      this.fileSizeMp4 = ''
      this.fileSizeAll = ''
    },
    handleSubmit (name) {

      this.$refs[name].validate((valid) => {
        this.fileType = this.formValidate.radio === '文档上传' ? this.fileSize : this.formValidate.radio === '压缩包上传' ? this.fileSizeTar : this.fileSizeMp4
        if (this.fileType) {
          if (this.fileSizeAll <= 500 * 1024 * 1024) {
            this.fileLoading = true
            fileUploadmanyFile(this.fileId, this.fileType).then(res => {
              if (res.code === '00000') {
                this.msgInfo('success', res.message, true)
                this.securityFile = ''
                this.fileSize = ''
                this.fileStatus.file = false
                this.fileStatus.zip = false
                this.securityFileTar = ''
                this.fileSizeTar = ''
                this.fileStatus.video = false
                this.securityFileMp4 = ''
                this.fileSizeMp4 = ''
                this.getDetailList()
                this.fileLoading = false
              } else {
                this.msgInfo('error', res.message, true)
                this.fileLoading = false
              }
            }).catch(error => {
              this.msgInfo('error', error.message, true)
              this.fileLoading = false
            })
          } else {
            this.msgInfo('warning', '请上传大小为500M以内的文件', true)

          }

        }

      })
    },
    handleReset () {
      this.securityFile = ''
      this.fileSize = ''
      this.fileStatus.file = false
      this.fileStatus.zip = false
      this.securityFileTar = ''
      this.fileSizeTar = ''
      this.fileStatus.video = false
      this.securityFileMp4 = ''
      this.fileSizeMp4 = ''
      this.fileSizeAll = ''
      this.fileLoading = false
      // this.$router.push({
      //   name: 'file_manage'
      // })
    },
    getDetailList () {
      let data = {
        id: this.fileId,
        pageIndex: this.tablePageParam.pageIndex,
        pageSize: this.tablePageParam.pageSize,
      }
      getFileListDetail(data).then(res => {
        if (res.code === '00000') {
          this.tableData = res.data.records
          this.tablePageParam.pagetotal = res.data.total
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  mounted () {

    this.fileId = this.$route.params.id
    let filename = JSON.parse(sessionStorage.getItem('fileName'))
    this.projectName = filename.projectName
    this.projectBrief = filename.projectBrief
    this.getDetailList()

  },
  computed: {


    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }

    },
  },

}
</script>

<style lang="less" scoped>
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
/deep/.ivu-input-type-textarea {
  height: 200px;
}
.bs {
  float: left;
  width: 6px;
  height: 18px;
  background: #19c3a0;
  opacity: 1;
  border-radius: 3px;
}
span {
  padding-left: 6px;
}
.info-title {
  font-size: 16px;
  font-weight: bold;
  vertical-align: middle;
  height: 18px;
  font-family: "Microsoft YaHei";
  line-height: 18px;
  color: #333333;
  margin: 10px 0 25px 0px;
  // &.addflex {
  //   display: flex;
  //   justify-content: space-between;
  //   // .btns {
  //   //   button {
  //   //     margin-right: 20px;
  //   //   }
  //   // }
  // }
}
.basetext {
  padding-top: 20px;
  span {
    text-align: left;
    margin: 0 26px;
    line-height: 20px;
    word-break: break-all;
  }
}
.descClass {
  width: 800px;
  word-wrap: break-word;
}
</style>
