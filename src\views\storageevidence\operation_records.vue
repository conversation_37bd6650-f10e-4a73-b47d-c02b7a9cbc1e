<template>
  <!-- 存证操作记录 -->
  <div>
    <div class="cz_header">
      <div class="cz_ss">
        <Select v-model="model3" style="width: 100px" placeholder="平台名称">
          <Option v-for="item in cityList" :value="item.value" :key="item.value" @click.native="click_value(item.value)">{{ item.label }}</Option>
        </Select>
        <Input placeholder="输入信息" v-model="search_value" @on-search="input_value(search_value)" />
      </div>

      <Row>
        <div class="sl_timout">请求时间</div>
        <Col span="12">
        <DatePicker format="yyyy-MM-dd" v-model="daterange" type="daterange" placement="bottom-end" placeholder="开始日期~结束日期" style="width: 200px" @on-change="timeout_click"></DatePicker>
        </Col>
      </Row>
      <div class="operation">
        <Button type="primary" icon="ios-search" @click.native="input_value">搜索</Button>
        <Button type="primary" ghost icon="ios-sync" @click.native="reset">重置</Button>
      </div>
    </div>
    <!-- table -->

    <div class="cz_table">
      <Table :columns="historyColumns" :data="historyData"></Table>
      <Page :total="dataCount" :page-size="PageParam.pageSize" :page-size-opts="[10, 20, 40, 60, 100]" show-sizer show-total show-elevator class="paging" @on-change="changepage" style="text-align: right" @on-page-size-change="pageSizeChange"></Page>
    </div>
  </div>
</template>
<script>
import { operation } from '@/api/data'

export default {
  name: 'operation_records',
  data () {
    return {
      // 初始化信息总条数
      dataCount: 0,
      // 表头
      historyColumns: [
        {
          title: '序号',
          type: 'index'
        },
        {
          title: '平台名称',
          key: 'platformName'
        },
        {
          title: '登陆用户名',
          key: 'userLoginId'
        },
        {
          title: '用户姓名',
          key: 'realName'
        },
        {
          title: '组织名称',
          key: 'organization'
        },
        {
          title: '用户请求真实Url',
          key: 'requestUrl',
          render: (h, params) => {
            if (!params.row.requestUrl) {
              return ''
            }
            let text = params.row.requestUrl
            if (text.length > 15) {
              text = text.substr(0, 15) + '...'
            }
            return h(
              'Tooltip',
              {
                props: {
                  maxWidth: 200,
                  content: params.row.requestUrl,
                  placement: 'bottom',
                  transfer: true
                }
              },
              text
            )
          }
        },
        {
          title: '请求参数',
          key: 'requestInfo',
          width: 180,
          render: (h, params) => {
            if (!params.row.requestInfo) {
              return ''
            }
            let text = params.row.requestInfo
            if (text.length > 15) {
              text = text.substr(0, 10) + '...'
            }
            return h(
              'Tooltip',
              {
                props: {
                  maxWidth: 150,
                  content: params.row.requestInfo,
                  placement: 'bottom',
                  transfer: true
                }
              },
              text
            )
          }
        },
        {
          title: '返回参数',
          key: 'responseInfo',
          render: (h, params) => {
            if (!params.row.responseInfo) {
              return ''
            }
            let text = params.row.responseInfo
            if (text.length > 15) {
              text = text.substr(0, 15) + '...'
            }
            return h(
              'Tooltip',
              {
                props: {
                  maxWidth: 200,
                  content: params.row.responseInfo,
                  placement: 'bottom',
                  transfer: true
                }
              },
              text
            )
          }
        },
        {
          title: '请求时间',
          key: 'requestTime'
        },
        {
          title: '存证状态',
          key: 'status',
          width: 120
        }
      ],
      daterange: '', // 清空的时间
      // 上链开始时间
      beginTime: '',
      // 上链结束时间
      endTime: '',
      historyData: [],
      model3: '',
      // 上链时间
      chain_time: [],
      // 输入框存证号
      search_mark: '',
      // 输入框存证号
      search_name: '',
      // 下拉框
      lable: '平台名称',
      search_value: '',
      // 分页
      PageParam: {
        pageIndex: 1,
        pageSize: 10
      },
      // 下拉框
      cityList: [
        {
          value: '平台名称',
          label: '平台名称'
        },
        {
          value: '登录用户名',
          label: '登录用户名'
        }
      ]
    }
  },
  methods: {
    changepage (index) {
      // 改变页码时触发
      // console.log(index);
      this.PageParam.pageIndex = index
      this.getRecords() // 获取表格列表
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      // 当前页条数
      this.PageParam.pageSize = size
      this.getRecords() // 获取表格列表
    },

    // 下拉存证
    click_value (e) {
      this.lable = e
      this.search_mark = ''
      this.search_name = ''
    },
    // 输入框值
    input_value () {
      if (this.lable === '平台名称') {
        this.search_mark = this.search_value
        this.search_name = ''
      } else if (this.lable === '登录用户名') {
        this.search_name = this.search_value
        this.search_mark = ''
      }
      this.getRecords() // 获取表格列表
    },
    // 获取时间
    timeout_click (e) {
      this.beginTime = e[0]
      this.endTime = e[1]
    },
    // 重置事件
    reset () {
      this.search_value = ''
      this.daterange = ''
      this.beginTime = ''
      this.endTime = ''
      this.search_mark = ''
      this.search_name = ''
      this.getRecords()
    },
    // 请求的方法
    getRecords () {
      let params = {
        platformName: this.search_mark, // 平台名称
        userLoginId: this.search_name, // 登录用户名
        beginTime: this.beginTime,
        endTime: this.endTime,
        pageParam: this.PageParam // 分页
      }
      operation(params).then((res) => {
        const { code, data } = res
        if (code === '00000') {
          const { records } = res.data
          // 类型 对应关系
          let typeRale = {
            1: '存证中',
            2: '成功',
            3: '失败'
          }
          let list = records.map((item) => {
            return {
              ...item,
              status: typeRale[item.status]
            }
          })
          this.historyData = list
          this.dataCount = data.total
        } else {
          // console.log('数据获取异常：', res)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  created () {
    this.getRecords() // 获取表格列表
  }
}
</script>

<style lang="less" scoped>
.ivu-card-body {
  .ivu-input-wrapper {
    line-height: 34px;
  }

  .ivu-select-single .ivu-select-selection {
    background: red;
  }
}
.cz_header {
  display: flex;
  margin-top: 10px;
  align-content: center;
  .sl_timout {
    border: 1px solid #dcdee2;
    height: 33px;
    padding: 5px 8px;
    text-align: center;
  }
  .cz_sltimout {
    width: 50%;
  }
  .cz_ss {
    display: flex;
  }
  .ivu-row {
    display: flex;
    flex-flow: row wrap;
    margin-left: 5%;
  }
}

// table
.cz_table {
  margin-top: 2% !important;
}
.operation {
  margin-left: 10px;
}
.ivu-btn-primary {
  margin-right: 10px;
}
</style>
