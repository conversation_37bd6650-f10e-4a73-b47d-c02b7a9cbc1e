<template>
  <!-- 用户日志 -->
  <div>
    <div class="cz_headerT">

      <div class="cz_header item">
        <div>
          <Input placeholder="请输入用户名称" v-model="search_value" @on-enter="uservalue" style="width:120px" />
        </div>
        <Row class="cz-dis">
          <div class="sl_timout">应用类型</div>
          <Col span="12">
          <Select v-model="applicationType" style="width: 100px" placeholder="请选择">
            <Option v-for="item in applicationTypeList" :value="item.value" :key="item.value" @click.native="get_Type(item.value)">{{ item.label }}</Option>
          </Select>
          </Col>
        </Row>
        <Row class="cz-dis">
          <div class="sl_timout">操作级别</div>
          <Col span="12">
          <Select v-model="operation" style="width: 100px" placeholder="全部">
            <Option v-for="item in operationList" :value="item.value" :key="item.value" @click.native="getOperation(item.value)">{{ item.label }}</Option>
          </Select>
          </Col>
        </Row>
        <!--  -->
        <Row class="cz-dis">
          <div class="sl_timout">日志时间</div>
          <DatePicker format="yyyy-MM-dd" type="daterange" placement="bottom-end" v-model="daterange" :editable='false' placeholder="开始日期~结束日期" style="width: 200px" @on-change="timeout_click"></DatePicker>
          <!-- <DatePicker type="datetimerange" format="yyyy-MM-dd HH:mm:ss" placeholder="Select date and time(Excluding seconds)" style="width: 300px"></DatePicker> -->
          <!-- <DatePicker format="yyyy-MM-dd HH:mm:ss" type="datetimerange" placement="bottom-end" v-model="daterange" :editable='false' placeholder="开始日期~结束日期" style="width:315px" @on-change="timeout_click"></DatePicker> -->
          <Button type="primary" icon="ios-search" @click.native="information">查询</Button>
          <Button type="primary" ghost icon="md-sync" @click.native="reset">重置</Button>
        </Row>
      </div>
      <Row class="cz-dis">
        <Button type="success" ghost @click.native="derive" class="dao">导出</Button>
      </Row>

    </div>
    <!-- table -->
    <div class="item">
      <edit-table-mul :columns="historyColumns" v-model="historyData"></edit-table-mul>
      <Page :total="dataCount" :page-size="tablePageParam.pageSize" :current.sync="tablePageParam.pageIndex" show-sizer show-total show-elevator class="paging" @on-change="changepage" style="text-align: right;margin-top: 10px;" @on-page-size-change="pageSizeChange"></Page>
    </div>
  </div>
</template>
<script>
import { application, exportApplication } from '@/api/data.js'
import EditTableMul from '_c/edit-table-mul'
export default {
  name: 'survival_log',
  components: {
    EditTableMul
  },
  data () {
    return {
      // 初始化信息总条数
      dataCount: 0,
      // 存证应用类型
      applicationType: '',
      applicationTypeList: [
        {
          value: 'CMBAAS',
          label: 'CMBAAS'
        },
        {
          value: '中国移动CMBaaS平台',
          label: '中国移动CMBaaS平台'
        }
      ],
      // 操作级别
      operation: '',
      operationList: [
        {
          value: 1,
          label: '一般'
        },
        {
          value: 2,
          label: '重要'
        },
        {
          value: 3,
          label: '敏感'
        },
        {
          value: 4,
          label: '警告'
        },
        {
          value: 5,
          label: '严重'
        }
      ],
      // 上链开始时间
      beginTime: '',
      // 上链结束时间
      endTime: '',
      // 输入框
      search_value: '',
      // 下拉框
      lable: '',
      // 日期
      daterange: '',
      // 分页
      tablePageParam: { pageIndex: 1, pageSize: 10 },
      //   table 表头
      historyColumns: [
        {
          title: '序号',
          type: 'index',
          width: 140
        },
        {
          title: '用户',
          key: 'userLoginId'
        },
        {
          title: '应用类型',
          key: 'applicationName'
        },
        {
          title: '操作类型',
          key: 'opTypeDesc'
        },
        {
          title: '操作结果',
          key: 'result'
        },
        {
          title: '操作级别',
          key: 'opLevelId'
        },
        {
          title: '操作时间',
          key: 'opTime',
          width: '180'
        }

      ],
      historyData: []
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, top: 400 }) },
    changepage (index) {
      this.tablePageParam.pageIndex = index // 当前页
      this.getapplication()
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      this.tablePageParam.pageSize = size
      this.getapplication()
    },
    // 重置事件
    reset () {
      this.search_value = ''
      this.daterange = ''
      this.beginTime = ''
      this.endTime = ''
      this.applicationType = ''
      this.operation = ''
      this.tablePageParam = { pageIndex: 1, pageSize: 10 }
      this.getapplication()
    },
    // 回车事件
    uservalue () {
      this.getapplication()
    },
    // 导出
    derive () {
      let deriveData = {
        userLoginId: this.search_value, // 用户id
        beginTime: this.beginTime ? this.beginTime + '00:00:00' : '', // 开始时间
        endTime: this.endTime ? this.endTime + '23:59:59' : '', // 结束时间
        applicationName: this.applicationType, // 应用类型
        opLevelId: this.operation, // 操作级别
        opType: ''
      }
      exportApplication(deriveData).then(res => {
        let reader = new FileReader();
        reader.readAsText(res);
        reader.onload = () => {
          try {
            let jsonData = JSON.parse(reader.result);
            this.msgInfo('error', jsonData.message, true)
          } catch (e) {
            let blob = new Blob([res])
            let downloadElement = document.createElement('a')
            let href = window.URL.createObjectURL(blob)
            downloadElement.href = href
            downloadElement.download = '用户行为记录.xls'
            document.body.appendChild(downloadElement)
            downloadElement.click()
            document.body.removeChild(downloadElement)
            window.URL.revokeObjectURL(href)
          }

        };
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 输入框值
    information () {
      this.search_value = this.search_value
      this.getapplication()
    },
    // 上链时间
    timeout_click (e) {
      this.beginTime = e[0]
      this.endTime = e[1]
    },
    // 应用类型
    get_Type (e) {
      this.applicationType = e
    },
    // 操作级别
    getOperation (e) {
      this.operation = e
    },
    // 请求方法
    getapplication () {
      let userdata = {
        userLoginId: this.search_value, // 用户id
        pageParam: this.tablePageParam, // 分页
        beginTime: this.beginTime ? this.beginTime + ' 00:00:00' : '', // 开始时间
        endTime: this.endTime ? this.endTime + ' 23:59:59' : '', // 结束时间
        applicationName: this.applicationType, // 应用类型
        opLevelId: this.operation // 操作级别
      }
      application(userdata).then((res) => {
        res.data.records.map((item) => {
          item.result === 0 ? (item.result = '成功') : (item.result = '失败')
          switch (item.opLevelId) {
            case 1:
              item.opLevelId = '一般'
              break
            case 2:
              item.opLevelId = '重要'
              break
            case 3:
              item.opLevelId = '敏感'
              break
            case 4:
              item.opLevelId = '警告'
              break
            case 5:
              item.opLevelId = '严重'
              break
            default:
          }
        })
        this.dataCount = res.data.total
        this.historyData = res.data.records
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
    //
  },
  created () {
    this.getapplication()
  }
}
</script>

<style lang="less" scoped>
// .dao {
//   display: flex;
//   text-align: right;
// }
.ivu-card-body {
  padding-top: 50px;
  .ivu-input-wrapper {
    line-height: 34px;
  }

  .ivu-select-single .ivu-select-selection {
    background: red;
  }
}

.cz_headerT {
  display: flex;
  justify-content: space-between;
  padding-top: 10px;
  margin-bottom: 10px;
}
.cz_header {
  display: flex;
  // justify-content: space-between;
  .sl_timout {
    height: 33px;
    padding: 5px 3px;
    text-align: center;
    border-radius: 4px;
  }
  .cz-dis {
    margin-right: 5px;
  }
}

// table
.cz_table {
  margin-top: 2% !important;
}
.cz_gjz {
  height: 33px;
  line-height: 32px;
  width: 99px;
  text-align: center;
  border: 1px solid;
  background: #2d8cf0;
  color: #fff;
}
.s_type {
  margin-right: 3%;
}
.ivu-btn-primary {
  margin-left: 7px;
}
.ivu-col-span-12 {
  display: block;
  flex: 0 0 41%;
  max-width: 50%;
}
.btn_title1 {
  width: 110px;
  height: 33px;
  text-align: center;
  line-height: 33px;
  display: inline-block;
}
</style>
