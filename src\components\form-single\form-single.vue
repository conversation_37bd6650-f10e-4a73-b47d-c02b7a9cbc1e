<template>
  <Form ref="formItem" :label-width="120" style="line-height:0" class="single-wrap" :rules="ruleValidate" :model="menuDetail" hide-required-mark>
    <input type="hidden" id="pastDetail" :value="pastDetail" />
    <FormItem label="类型：">
      <RadioGroup v-model="menuDetail.resourceType" @change.native="showapi = menuDetail.resourceType === 'MENU' ? false : true">
        <Radio label="MENU">菜单</Radio>
        <Radio label="API">接口</Radio>
      </RadioGroup>
    </FormItem>
    <FormItem label="父级菜单：">
      <!-- <p v-show="showparent">
        {{menuDetail.parentName}}
        <a @click="showparent = false">更改</a>
      </p>
      {{menuDetail.parentId}}
      <Cascader v-show="!showparent" v-model="menuDetail.parentId" :data="dataCascader" trigger="hover" filterable change-on-select></Cascader> -->
      <Cascader v-model="menuDetail.parentId" :data="dataCascader" trigger="hover" filterable change-on-select></Cascader>
    </FormItem>
    <FormItem label="顺序号：">
      <Tooltip max-width="200" content="菜单或接口可通过顺序号调整显示顺序" style="margin-left:-17px;">
        <Icon type="md-help-circle" style="font-size:16px;" />
      </Tooltip>
      <Input type="number" :placeholder="'建议输入顺序号：'+ menuDetail.orderSeq" v-model="menuDetail.seq" />
    </FormItem>
    <FormItem label="菜单/接口名称：" prop="resourceName">
      <Input type="text" placeholder="最多输入32个字符" v-model="menuDetail.resourceName" maxlength="33" />
    </FormItem>
    <FormItem label="菜单/接口描述：" prop="title">
      <Input type="text" placeholder="最多输入64个字符，作为侧边菜单栏显示" v-model="menuDetail.title" maxlength="65" />
    </FormItem>
    <FormItem label="接口地址：" v-show="showapi" prop="url">
      <Input type="text" placeholder="如：/test" v-model="menuDetail.url" />
    </FormItem>
    <FormItem label="接口请求方式：" v-show="showapi">
      <RadioGroup v-model="menuDetail.httpMethod">
        <Radio label="PUT">PUT</Radio>
        <Radio label="GET">GET</Radio>
        <Radio label="POST">POST</Radio>
        <Radio label="DELETE">DELETE</Radio>
      </RadioGroup>
    </FormItem>
    <FormItem label="菜单/接口状态：">
      <i-switch v-model="switchStatus" size="large">
        <span slot="open">启用</span>
        <span slot="close">禁用</span>
      </i-switch>
    </FormItem>
    <FormItem style="margin:50px auto 0;text-align:center;">
      <Button v-if="this.menuDetail.isAdd" @click.native="handleSubmit" icon="md-arrow-round-forward" type="primary">提交</Button>
      <Poptip v-else confirm :title="`确认修改吗?`" class="popup-wrap" @on-ok="handleSubmit">
        <Button icon="md-arrow-round-forward" type="primary" :disabled="hasEditPermission">提交</Button>
      </Poptip>
      <!-- <Button @click="handleReset" style="margin-left: 8px">重置</Button> -->
    </FormItem>
  </Form>
</template>

<script>
import { getMenuInfo, addMenuInfo } from '@/api/data'
import { localRead } from '@/lib/util'
export default {
  name: 'FormSingle',
  props: ['pastDetail'],
  data () {
    const validateNumber = (rule, value, callback) => {
      if ((/^\d+$/.test(value))) {
        callback(new Error('不能为纯数字'))
      } else {
        callback()
      }
    }
    return {
      transferKeyTree: 0,
      modal: false,
      showapi: false,
      showparent: false,
      switchStatus: false,
      dataCascader: [],
      ruleValidate: {
        resourceName: [
          { required: true, message: '请输入菜单/接口名称!', trigger: 'blur' },
          { max: 32, message: '最多输入32个字符，当前超出1个字符!', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '请输入菜单/接口描述!', trigger: 'blur' },
          { max: 64, message: '最多输入64个字符，当前超出1个字符!', trigger: 'blur' },
          { required: true, message: '不能为纯数字', trigger: 'blur', validator: validateNumber }
        ],
        url: [
          { pattern: /^\//, message: "必须以'/'开始!", trigger: 'blur' }
        ]
      },
      userPermission: JSON.parse(localRead('userPermission')),
      menuDetail: { 'resourceId': '', 'resourceName': '', 'title': '', 'resourceType': 'MENU', 'url': '', 'httpMethod': '', 'parentId': [], 'parentName': '/', 'status': 'DISABLE', 'seq': '' }
    }
  },
  computed: {

    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    getMenu (resourceType) {
      getMenuInfo(resourceType).then(res => {
        if (res.code !== '00000') this.msgInfo('warning', res.message, true)
        else {
          this.dataCascader = [{
            resourceId: 0,
            title: 'CMBaaS',
            children: this.handleDataCascader(res.data)
          }]
        }
        // window.arrList = res.data
      }).catch(error => {
        console.log('getMenuInfo.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    handleDataCascader (arr) {
      function handleData (arr) {
        arr.map(item => {
          item.label = item.label ? item.label : item.title
          item.value = item.value ? item.value : item.resourceId
          if (item.children) {
            handleData(item.children)
          }
          return item
        })
        return arr
      }
      return handleData(arr)
    },
    // changeParent (labels, selectedData) {
    //   console.log('changeParent===', labels, selectedData)
    //   // this.menuDetail.parentId = labels
    // },
    handleSubmit () {
      this.$refs.formItem.validate((valid) => {
        if (valid) {
          if (this.menuDetail.resourceName) {
            let menuDetail = { ...this.menuDetail }
            if (typeof (menuDetail.parentId) === 'object') menuDetail.parentId = menuDetail.parentId[menuDetail.parentId.length - 1]
            addMenuInfo(menuDetail).then(res => {
              if (res.code !== '00000') this.msgInfo('warning', res.message, true)
              else {
                this.msgInfo('success', res.message)
                this.$emit('getMenu', 'ALL', this.menuDetail.resourceId || this.menuDetail.parentId)
                // location.reload()
              }
            }).catch(error => {
              console.log('addMenuInfo.error===>', error)
              this.msgInfo('error', error.message, true)
            })
          }
        } else {
          this.$Message.error('请按要求填写完整!')
        }
      })
    }
  },
  watch: {
    dataCascader: {
      handler (parentId) {
        if (this.dataCascader !== []) {
          for (let i = 0; i < this.dataCascader.length; i++) {
            this.dataCascader[i].value = this.dataCascader[i].resourceId
            this.dataCascader[i].label = this.dataCascader[i].title
            this.dataCascader[i].expand = ''
            if (this.dataCascader[i].children) {
              for (let j = 0; j < this.dataCascader[i].children.length; j++) {
                this.dataCascader[i].children[j].value = this.dataCascader[i].children[j].resourceId
                this.dataCascader[i].children[j].label = this.dataCascader[i].children[j].title
                this.dataCascader[i].children[j].expand = ''
                if (this.dataCascader[i].children[j].children) {
                  for (let z = 0; z < this.dataCascader[i].children[j].children.length; z++) {
                    this.dataCascader[i].children[j].children[z].value = this.dataCascader[i].children[j].children[z].resourceId
                    this.dataCascader[i].children[j].children[z].label = this.dataCascader[i].children[j].children[z].title
                    this.dataCascader[i].children[j].children[z].expand = ''
                  }
                }
              }
            }
          }
        }
      },
      deep: true,
      immediate: false
    },
    switchStatus: {
      handler () {
        this.menuDetail.orderSeq = this.menuDetail.orderSeq ? this.menuDetail.orderSeq : this.menuDetail.seq
        if (this.switchStatus) this.menuDetail.status = 'ENABLE'
        else this.menuDetail.status = 'DISABLE'
      },
      deep: true,
      immediate: false
    },
    menuDetail: {
      handler (status) {
        this.menuDetail.orderSeq = this.menuDetail.orderSeq ? this.menuDetail.orderSeq : this.menuDetail.seq || 1
        this.menuDetail.seq = this.menuDetail.seq ? this.menuDetail.seq : 1
        if (this.menuDetail.status === 'ENABLE') this.switchStatus = true
        else if (this.menuDetail.status === 'DISABLE') this.switchStatus = false
        // if (this.menuDetail.parentName) this.showparent = true
        // else this.showparent = false
      },
      deep: true,
      immediate: true
    }
  },
  mounted () {
    let pastDetail = JSON.parse(document.getElementById('pastDetail').value)
    if (pastDetail) this.menuDetail = pastDetail
    // if (pastDetail) this.menuDetail.parentId = [pastDetail.parentId]
    if (this.menuDetail.parentName && this.menuDetail.resourceType === 'API') {
      this.showapi = true
      this.showparent = true
    }
    this.getMenu('MENU')
  }
}
</script>

<style lang="less">
.single-wrap {
  .popup-wrap {
    .ivu-poptip-body {
      display: flex !important;
    }
  }
}
</style>
