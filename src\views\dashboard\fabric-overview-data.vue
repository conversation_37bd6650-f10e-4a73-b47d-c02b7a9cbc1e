<template>
  <div ref="imgSizeBox">
    <Card :padding="0">
      <p class="title"><span class="bs"></span>平台概览</p>
      <Row :gutter="24" style="padding-top:10px;margin-left:5px;padding-bottom:10px">
        <i-col :lg="4" :xs="12" :md="6" style="padding-left:0;padding-right:0">
          <div class="content-con">
            <img class="imgs" :src="back1" ref="imgSize" id="imgs" @load="imgload">
            <div class="content-title">
              <p class="count-title" :style="inforCardData[0].params.unit ? 'margin-top:-20px;' : ''">{{ inforCardData[0].title }}({{inforCardData[0].unitVal || inforCardData[0].unit}})</p>
              <span class="count-to-number">{{inforCardData[0].count}}</span>
              <div class="tip" :style="getWidth(inforCardData[0].params.val)" v-if="inforCardData[0].params.unit">
                <div class="inner" style="background-color:#34cab4">
                </div>
                <div class="inner1">
                  <span>{{inforCardData[0].params.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </i-col>
        <i-col :lg="4" :xs="12" :md="6" style="padding-left:0;padding-right:0">
          <div class="content-con">
            <img class="imgs" :src="back2">
            <div class="content-title">
              <p class="count-title" :style="inforCardData[1].params.unit ? 'margin-top:-20px;' : ''">{{ inforCardData[1].title }}({{inforCardData[1].unitVal ||inforCardData[1].unit}})</p>
              <span class="count-to-number">{{inforCardData[1].count}}</span>
              <div class="tip" :style="getWidth(inforCardData[1].params.val)" v-if="inforCardData[1].params.unit">
                <div class="inner" style="background-color:#2c68c9">
                </div>
                <div class="inner1">
                  <span>{{inforCardData[1].params.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </i-col>
        <i-col :lg="4" :xs="12" :md="6" style="padding-left:0;padding-right:0">
          <div class="content-con">
            <img class="imgs" :src="back3">
            <div class="content-title">
              <p class="count-title" :style="inforCardData[2].params.unit ? 'margin-top:-20px;' : ''">{{ inforCardData[2].title }}({{inforCardData[2].unitVal ||inforCardData[2].unit}})</p>
              <span class="count-to-number">{{inforCardData[2].count}}</span>
              <div class="tip" :style="getWidth(inforCardData[2].params.val)" v-if="inforCardData[2].params.unit">
                <div class="inner" style="background-color:#66698e">
                </div>
                <div class="inner1">
                  <span>{{inforCardData[2].params.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </i-col>
        <i-col :lg="4" :xs="12" :md="6" style="padding-left:0;padding-right:0">
          <div class="content-con">
            <img class="imgs" :src="back4">
            <div class="content-title">
              <p class="count-title" :style="inforCardData[3].params.unit ? 'margin-top:-20px;' : ''">{{ inforCardData[3].title }}({{inforCardData[3].unitVal ||inforCardData[3].unit}})</p>
              <span class="count-to-number">{{inforCardData[3].count}}</span>
              <div class="tip" :style="getWidth(inforCardData[3].params.val)" v-if="inforCardData[3].params.unit">
                <div class="inner" style="background-color:#92a0c1">
                </div>
                <div class="inner1">
                  <span>{{inforCardData[3].params.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </i-col>
        <i-col :lg="4" :xs="12" :md="6" style="padding-left:0;padding-right:0">
          <div class="content-con">
            <img class="imgs" :src="back5">
            <div class="content-title">
              <p class="count-title" :style="inforCardData[4].params.unit ? 'margin-top:-20px;' : ''">{{ inforCardData[4].title }}({{inforCardData[4].unitVal ||inforCardData[4].unit}})</p>
              <span class="count-to-number">{{ inforCardData[4].count }}</span>
              <div class="tip" :style="getWidth(inforCardData[4].params.val)" v-if="inforCardData[4].params.unit">
                <div class="inner" style="background-color:#fe8b4c">
                </div>
                <div class="inner1">
                  <span>{{inforCardData[4].params.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </i-col>
        <i-col :lg="4" :xs="12" :md="6" style="padding-left:0">
          <div class="content-con">
            <img class="imgs" :src="back6">
            <div class="content-title">
              <p class="count-title" :style="inforCardData[5].params.unit ? 'margin-top:-20px;' : ''">{{ inforCardData[5].title }}({{inforCardData[5].unitVal ||inforCardData[5].unit}})</p>
              <span class="count-to-number">{{inforCardData[5].count}}</span>
              <div class="tip" :style="getWidth(inforCardData[5].params.val)" v-if="inforCardData[5].params.unit">
                <div class="inner" style="background-color:#fe8b4c">
                </div>
                <div class="inner1">
                  <span>{{inforCardData[5].params.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </i-col>
      </Row>
    </Card>
  </div>
</template>

<script>
import { getAllChainStatistic } from '@/api/baascore/dashboard'
import { transferData, transferVal } from '@/lib/transformUnit'
export default {
  name: 'overview-data',
  components: {
  },
  data () {
    return {
      timer: null,
      left: 36,
      color: '',
      back1: require('@/assets/img/dashboard/lian.png'),
      back2: require('@/assets/img/dashboard/zhangben.png'),
      back3: require('@/assets/img/dashboard/jiedian.png'),
      back4: require('@/assets/img/dashboard/zhinengheyue.png'),
      back5: require('@/assets/img/dashboard/jiaoyi.png'),
      back6: require('@/assets/img/dashboard/yonghu.png'),
      inforCardData: [
        { title: '链', count: 0, unit: '条', unitVal: '', params: { unit: '', val: 0 } },
        { title: '账本', count: 0, unit: '个', unitVal: '', params: { unit: '', val: 0 } },
        { title: '节点数', count: 0, unit: '个', unitVal: '', params: { unit: '', val: 0 } },
        { title: '智能合约', count: 0, unit: '个', unitVal: '', params: { unit: '', val: 0 } },
        { title: '交易数', count: 0, unit: '条', unitVal: '', params: { unit: '', val: 0 } },
        { title: '用户数', count: 0, unit: '个', unitVal: '', params: { unit: '', val: 0 } }
      ]
    }
  },
  computed: {
  },
  methods: {
    imgload () {
      if (this.$refs.imgSize) {
        this.$emit('getHeight', this.$refs.imgSize.offsetHeight)
      }
    },
    getWidth (val) {
      return `width: ${1.2 + val}vw`
    },
    getInfoCardData (count, i) {
      this.inforCardData[i].count = transferVal(count).value
      this.inforCardData[i].unitVal = transferVal(count).unit + this.inforCardData[i].unit
      this.inforCardData[i].params.unit = transferData(this.inforCardData[i].count).unit
      this.inforCardData[i].params.val = transferData(this.inforCardData[i].count).val
    },
    getBackOver1 (value) {
      this.back1 = require('@/assets/img/dashboard/bg-1s.png')
    },
    getBackOut1 () {
      this.back1 = require('@/assets/img/dashboard/bg-1.png')
    },
    getBackOver2 (value) {
      this.back2 = require('@/assets/img/dashboard/bg-6s.png')
    },
    getBackOut2 () {
      this.back2 = require('@/assets/img/dashboard/bg-6.png')
    },
    getBackOver3 () {
      this.back3 = require('@/assets/img/dashboard/bg-2s.png')
    },
    getBackOut3 () {
      this.back3 = require('@/assets/img/dashboard/bg-2.png')
    },
    getBackOver4 (value) {
      this.back4 = require('@/assets/img/dashboard/bg-4s.png')
    },
    getBackOut4 () {
      this.back4 = require('@/assets/img/dashboard/bg-4.png')
    },
    getBackOver5 (value) {
      this.back5 = require('@/assets/img/dashboard/bg-3s.png')
    },
    getBackOut5 () {
      this.back5 = require('@/assets/img/dashboard/bg-3.png')
    },
    getBackOver6 (value) {
      this.back6 = require('@/assets/img/dashboard/bg-5s.png')
    },
    getBackOut6 () {
      this.back6 = require('@/assets/img/dashboard/bg-5.png')
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    // 查询平台概览数据
    getDashboardData () {
      getAllChainStatistic().then(res => {
        if (res.code == 200) {
          this.getInfoCardData(res.data.chainCount, 0)
          this.getInfoCardData(res.data.channelCount, 1)
          this.getInfoCardData(res.data.chainNodeCount, 2)
          this.getInfoCardData(res.data.contractCount, 3)
          this.getInfoCardData(res.data.transactionCount, 4)
          this.getInfoCardData(res.data.userCount, 5)
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  created () {
  },
  mounted () {
    this.getDashboardData()
    window.onresize = () => {
      this.imgload()
    }
  }
}
</script>

<style lang="less" scoped>
.contract-more {
  border-left: 4px solid #19c3a0;
}
.title {
  height: 18px;
  font-weight: bold;
  text-indent: 4px;
  line-height: 18px;
  font-size: 16px;
  font-family: "Microsoft YaHei";
  font-weight: bold;
  color: #333333;
  opacity: 1;
  margin-left: 30px;
  margin-top: 30px;
}
.bs {
  float: left;
  width: 6px;
  height: 18px;
  background: #19c3a0;
  opacity: 1;
  border-radius: 3px;
}
.common {
  float: left;
  display: table;
  text-align: center;
}
.size {
  width: 100%;
  height: 100%;
}
.middle-center {
  vertical-align: middle;
}
/deep/.ivu-col {
  display: flex;
  justify-content: center;
}

.content-con {
  float: left;
  display: flex;
  justify-content: center;
  .middle-center;
  text-align: center;
  position: relative;
  .content-title {
    z-index: 2;
    text-align: left;
    position: absolute;
    color: black;
    top: 60%;
    left: 57%;
    transform: translate(-25%, -50%);
    .count-title {
      font-size: 1vw;
      // font-family: 'Microsoft YaHei';
      // font-weight: 400;
      color: black;
      // text-shadow: -1px -1px 1px rgba(103, 67, 10, 0.25);
      opacity: 0.8;
    }
    .count-to-number {
      font-size: 2.5vw;
      font-family: "D-DIN";
      font-weight: normal;
    }
    .tip {
      width: 90px;
      height: 1px;
      border-top: 1px solid #e2e1e1;
      margin-top: -0.5vw;
      opacity: 0.7;
      .inner {
        background-color: #f37441;
        width: 5px;
        height: 6px;
        border: 1px solid #e2e1e1;
        position: relative;
        left: 1vw;
        top: -4px;
        transform: rotate(45deg);
        border-right: 0px;
        border-bottom: 0px;
      }
      .inner1 {
        margin-top: -8px;
        font-size: 8px;
        transform: scale(0.8);
        color: #e2e1e1;
        margin-left: 0.4vw;
      }
    }
  }
}
.imgs {
  width: auto;
  height: auto;
  max-width: 90%;
  // max-height:90%;
  //max-width:100%;
}
.ivu-card-shadow {
  box-shadow: none !important;
}
/deep/.ivu-card:hover {
  box-shadow: none !important;
  .ivu-card.ivu-card-shadow:hover {
    box-shadow: none !important;
  }
}
</style>
