<!--
   信源管理
   Aturun
-->
<template>
    <div class="source_management">
      <div class="content-top">
        <div class="content-top-lift-title">信源管理</div>
        <div class="top_op">
          <div class="content-top-lift">
            <div class="content-top-bottom-left">
              <div class="top_text" style="width: 80px;">信源类型:</div>
              <el-select v-model="entityIn.providerType" clearable placeholder="请选择" @change="getOracleProviderList(true)">
                <el-option label="EOS" value="EOS"></el-option>
                <el-option label="http" value="HTTP"></el-option>
                <el-option label="数据库" value="DB"></el-option>
              </el-select>
              <div class="top_text" style="width: 80px;">创建时间:</div>
              <el-date-picker
                v-model="entityIn.startTime"
                type="datetime"
                placeholder="选择日期">
              </el-date-picker>
              <div class="top_text top_text-content">到</div>
              <el-date-picker
                v-model="entityIn.endTime"
                type="datetime"
                placeholder="选择日期">
              </el-date-picker>
              <div class="top-right-input icon-search_suffix">
                <el-input
                  placeholder="可输入信源名称查询"
                  v-model="entityIn.providerName"
                  @keyup.enter.native="getOracleProviderList">
                  <i slot="suffix" class="el-icon-search" @click="getOracleProviderList"></i>
                </el-input>
              </div>
              <el-button type="primary" icon="el-icon-search" @click="getOracleProviderList(true)">查 询</el-button>

            </div>
          </div>
          <div class="content-top-right">
            <div class="top-right-button">
              <el-button icon="el-icon-plus" @click="newConsumer">新建信源账户</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="content-body">
        <el-table
            :data="tableData"
            style="width: 100%"
            height="520px"
            stripe>
          <el-table-column
              label="信源名称"
              width="120px">
            <template slot-scope="scope">
              <div v-if="scope.row.providerName.length<=12">{{scope.row.providerName}}</div>
              <el-popover v-else trigger="hover" placement="top">
                <div class="dsfdg" >{{ scope.row.providerName }}</div>
                <div slot="reference" class="name-wrapper">
                  {{ scope.row.providerName }}
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
              label="信源类型" width="230px">
            <template slot-scope="scope">
              <div>{{ProviderType[scope.row.providerType]}}</div>
            </template>
          </el-table-column>
          <el-table-column
              label="取数模式">
            <template slot-scope="scope">
              <div v-if="scope.row.fetchType=='0'">定时</div>
              <div v-if="scope.row.fetchType=='1'">请求</div>
            </template>
          </el-table-column>
          <el-table-column
              prop="cycleType"
              label="取数周期类型"
              width="120px">
            <template slot-scope="scope">
              <div v-if="scope.row.cycleType=='3'">天</div>
              <div v-else-if="scope.row.cycleType=='2'">小时</div>
              <div v-else-if="scope.row.cycleType=='1'">分钟</div>
              <div v-else>/</div>
            </template>
          </el-table-column>
          <el-table-column
              prop="cycleTime"
              label="取数周期">
            <template slot-scope="scope">
              <div >{{scope.row.cycleTime||'/'}}</div>
            </template>
          </el-table-column>
<!--          <el-table-column-->
<!--              label="状态"-->
<!--              width="150">-->
<!--            <template slot-scope="scope">-->
<!--                <div slot="reference" class="name-wrapper" v-if="scope.row.providerStatus==0">-->
<!--                  <el-tag size="medium" class="tag_succ" v-if="scope.row.regStatus=='true'">注册成功</el-tag>-->
<!--                  <el-tag size="medium" class="tag_err" v-else-if="scope.row.regStatus=='false'">注册失败</el-tag>-->
<!--                  <el-tag size="medium" class="tag_war" v-else>已注销</el-tag>-->
<!--                </div>-->
<!--              <div slot="reference" class="name-wrapper" v-else>-->
<!--                <el-tag size="medium" class="tag_err">已删除</el-tag>-->
<!--              </div>-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column
            width="220px"
              label="业务描述">
            <template slot-scope="scope">
                         <div v-if="scope.row.memo.length<=12">{{scope.row.memo}}</div>
              <el-popover v-else trigger="hover" placement="top">
                <div class="dsfdg" >{{ scope.row.memo }}</div>
                <div slot="reference" class="name-wrapper">
                  {{ scope.row.memo }}
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
              label="创建时间"
              width="220px"
              >
            <template slot-scope="scope">
             {{setDates(scope.row.createTime)}}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="450">
            <template slot-scope="scope">
              <el-button
                  size="mini"
                  @click="handlelook(scope.$index, scope.row)">查看</el-button>

              <el-button
                  size="mini"
                  @click="handleDelete(scope.$index, scope.row)">删除</el-button>
              <el-button
                  size="mini"
                  @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              <el-button
                style="width: 80px;"
                  size="mini"
                  @click="handleRegister(scope.$index, scope.row)" >编辑注册</el-button>
<!--              <el-button-->
<!--                size="mini"-->
<!--                @click="handleCancellation(scope.$index, scope.row)">注销</el-button>-->
            </template>
          </el-table-column>
        </el-table>
        <div class="block table_pag">
          <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="entityIn.rows"
              background
              layout="total, prev, pager, next, sizes, jumper"
              :total="total">
          </el-pagination>
        </div>
      </div>
      <newSourceAccount ref="newSourceAccountRef" @Refresh="Refresh"></newSourceAccount>
      <EditRegistration ref="EditRegistrationRef" @Refresh="Refresh"></EditRegistration>
    </div>
</template>

<script>
import * as api from './api'
import newSourceAccount from './components/newSourceAccount'
import EditRegistration from './components/EditRegistration'
import {getFormatDates} from '../../utils/atuUtils.js'
    export default {
      components:{
        newSourceAccount,
        EditRegistration
      },
        data(){
            return {
              currentPage: 1,
              input:'',
              tableData: [],
              total:null,
              entityIn:{
                "endTime": "",
                "filter": {},
                "order": "",
                "page": 1,
                "providerName": "",
                providerType:"",
                "rows": 10,
                "sort": "",
                "startTime": ""
              },
              ProviderType:{
                EOS:'链(CMBAAS内部跨链交互)',
                HTTP:'http',
                DB:'数据库'
              }
            }
        },
        created(){
        this.getOracleProviderList()
        },
        mounted(){
          // console.log(this.entityIn)
        },
      methods: {
        setProviderType(key){

        },
        setDates(val){
          return getFormatDates(val,'yyyy-mm-dd MM:mm:ss')
        },
        newConsumer(){
          this.$refs.newSourceAccountRef.operationState=0
          this.$refs.newSourceAccountRef.Visible=true
        },
        Refresh(){
          this.getOracleProviderList()
        },
        //获取数据用户列表
        getOracleProviderList(search=false){
          console.log(this.entityIn)
          if(this.entityIn.startTime&&this.entityIn.endTime){
            if(new Date(this.entityIn.startTime).getTime()>=new Date(this.entityIn.endTime).getTime()){
              return this.$message.warning('结束时间不能小于开始时间！')
            }
          }

          if(this.entityIn.startTime){
            this.entityIn.startTime = this.setDates(this.entityIn.startTime)
          }
          if(this.entityIn.endTime){
            this.entityIn.endTime = this.setDates(this.entityIn.endTime)
          }
          if(search){
            this.entityIn.page=1
            this.currentPage = 1
          }
          api.getOracleProviderList(
              this.entityIn
          ).then(res=>{
            if(res.code!=0) return this.$message.warning(res.msg)
            this.tableData=res.result.rows
            this.total=res.result.total
          })
        },
        handleEdit(index, row) {
          this.$refs.newSourceAccountRef.operationState=1
          this.$refs.newSourceAccountRef.providerId=row.providerId
          this.$refs.newSourceAccountRef.Visible=true
        },

         /**
         * @Description: TODO 删除信源
         * @Author: Aturun
         * @param  index row
         * @return
         * @Date: 2021/12/1  09:57
         */
        handleDelete(index, row) {
          this.$confirm(`此操作将永久删除${row.providerName}, 是否继续?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            showClose:false,
            type:'warning'
          }).then(() => {
            api.delOracleProvider(row.providerId).then(res=>{
              if(res.code!=0) return this.$message.warning(res.msg)
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.Refresh()
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
        },
        handleCancellation(index, row) {
          this.$confirm('此操作将注销该信源, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            showClose:false
          }).then(() => {
            api.logOutProvider(row.providerId).then(res=>{
              if(res.code!=0) return this.$message.warning(res.msg)
              this.$message({
                type: 'success',
                message: '注销成功!'
              });
              this.Refresh()
            })
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消注销'
            });
          });
        },
        handleRegister(index, row) {
          if(row.providerId||row.providerId==0){
            this.$refs.EditRegistrationRef.providerId = row.providerId
            this.$refs.EditRegistrationRef.Visible = true
          }else {
            this.$message.warning('列表providerId主键不存在')
          }
        },
        handlelook(index, row) {
          this.$refs.newSourceAccountRef.operationState=2
          this.$refs.newSourceAccountRef.providerId=row.providerId
          this.$refs.newSourceAccountRef.Visible=true
        },
        handleSizeChange(val) {
          this.entityIn.rows =val
          this.getOracleProviderList()
        },
        handleCurrentChange(val) {
          this.entityIn.page = val
          this.currentPage =val
          this.getOracleProviderList()
        }
      },
    }
</script>

<style lang="less" scoped>
.source_management{
  margin: 16px 14px;
  background: #ffffff;
  height: 706px;
  .content-top{
    .content-top-lift-title{
      padding-left: 30px;
      font-size: 18px;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }
    .icon-search_suffix{
      margin-left: 10px;
      margin-right: 10px;
    }
    .top_op{
      display: flex;
      justify-content: space-between;
    }
    .content-top-right{
      display: flex;
      padding: 10px 20px 0 0;
      .top-right-input{
        margin-right: 12px;
        .el-button{

        }
      }
      .top-right-button{
        .el-button{

        }
      }
    }
    .content-top-lift{
      padding: 0 0 0 23px;
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      color: #333333;
      opacity: 1;
      .content-top-bottom-left{
        padding-top: 10px;
        display: flex;

      }
    }
  }
  .content-body{
    margin: 11px 17px 0 16px;
  }
  .table_pag{
    margin: 12px 16px 0 0;
    display: flex;
    justify-content: flex-end;
  }
}
</style>



