<template>
  <div class="subrecord_index">
    <keep-alive v-if="currentTab === 'file_manage'">
      <SubrecordTable />
    </keep-alive>
    <router-view />
  </div>
</template>

<script>
import SubrecordTable from './home.vue'
export default {
  name: 'file_manage',
  components: {
    SubrecordTable
  },
  data () {
    return {
      excludeArr: ['file_manage']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () { }
}
</script>
