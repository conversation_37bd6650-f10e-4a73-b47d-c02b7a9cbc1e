<template>
  <div class="map-wrap">
    <div class="map-title">
      <div class="eos">
        节点地图
      </div>
    </div>
    <div ref="mapbox" class="box" id="map"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { geoCoordMap } from './geoCoorMap'
import { getChainNodes } from '@/api/baascore/dashboard'
const getNodeNumber = function (province, mapDataList = []) {
  let provinceNode = ''
  mapDataList.forEach((item) => {
    if (item.province === province) {
      provinceNode = item.provinceNode
    }
  })
  return provinceNode || 0
}

let getNodeNumList = function (mapDataList) {
  return [
    // 配置各省节点数，显示不用颜色
    { name: '北京', value: getNodeNumber('北京', mapDataList) },
    { name: '天津', value: getNodeNumber('天津', mapDataList) },
    { name: '上海', value: getNodeNumber('上海', mapDataList) },
    { name: '重庆', value: getNodeNumber('重庆', mapDataList) },
    { name: '河北', value: getNodeNumber('河北', mapDataList) },
    { name: '河南', value: getNodeNumber('河南', mapDataList) },
    { name: '云南', value: getNodeNumber('云南', mapDataList) },
    { name: '辽宁', value: getNodeNumber('辽宁', mapDataList) },
    { name: '黑龙江', value: getNodeNumber('黑龙江', mapDataList) },
    { name: '湖南', value: getNodeNumber('湖南', mapDataList) },
    { name: '安徽', value: getNodeNumber('安徽', mapDataList) },
    { name: '山东', value: getNodeNumber('山东', mapDataList) },
    { name: '新疆', value: getNodeNumber('新疆', mapDataList) },
    { name: '江苏', value: getNodeNumber('江苏', mapDataList) },
    { name: '浙江', value: getNodeNumber('浙江', mapDataList) },
    { name: '江西', value: getNodeNumber('江西', mapDataList) },
    { name: '湖北', value: getNodeNumber('湖北', mapDataList) },
    { name: '广西', value: getNodeNumber('广西', mapDataList) },
    { name: '甘肃', value: getNodeNumber('甘肃', mapDataList) },
    { name: '山西', value: getNodeNumber('山西', mapDataList) },
    { name: '内蒙古', value: getNodeNumber('内蒙古', mapDataList) },
    { name: '陕西', value: getNodeNumber('陕西', mapDataList) },
    { name: '吉林', value: getNodeNumber('吉林', mapDataList) },
    { name: '福建', value: getNodeNumber('福建', mapDataList) },
    { name: '贵州', value: getNodeNumber('贵州', mapDataList) },
    { name: '广东', value: getNodeNumber('广东', mapDataList) },
    { name: '青海', value: getNodeNumber('青海', mapDataList) },
    { name: '西藏', value: getNodeNumber('西藏', mapDataList) },
    { name: '四川', value: getNodeNumber('四川', mapDataList) },
    { name: '宁夏', value: getNodeNumber('宁夏', mapDataList) },
    { name: '海南', value: getNodeNumber('海南', mapDataList) },
    { name: '台湾', value: getNodeNumber('台湾', mapDataList) },
    { name: '香港', value: getNodeNumber('香港', mapDataList) },
    { name: '澳门', value: getNodeNumber('澳门', mapDataList) },
    { name: '南海诸岛', value: getNodeNumber('南海诸岛', mapDataList) }
  ]
}
const convertData = function (data) {
  var res = []
  for (var i = 0; i < data.length; i++) {
    var geoCoord = geoCoordMap[data[i].province]
    if (geoCoord) {
      let cityArr = []
      data[i].cityList &&
        data[i].cityList.map((city) => {
          cityArr.push({ city: city.city, cityNode: city.cityNode })
        })

      res.push({
        name: data[i].province,
        value: geoCoord.concat(data[i].province, data[i].provinceNode, [
          cityArr
        ])
      })
    }
  }
  return res
}
const option = {
  title: {
    text: '省节点数',
    x: 'center',
    textStyle: {
      color: '#11173D',
      fontSize: 14
    },
    subtextStyle: {},
    left: '0',
    bottom: '100vw'
  },
  geo: {
    map: 'china',
    show: true,
    roam: true,
    zoom: 1.22,
    label: {
      normal: {
        show: false
      },
      emphasis: {
        show: false
      }
    },
    itemStyle: {
      // 模块color
      normal: {
        areaColor: '#D3E0FF',
        borderColor: '#c0bfbf',
        shadowColor: '#D2ECF7'
      },
      emphasis: {
      }
    }
  },

  series: [
    {
      label: {
        normal: {
          formatter: '{b}',
          position: 'right',
          show: false
        },
        emphasis: {
          show: false
        }
      },
      itemStyle: {
        normal: {
          color: '#3D73EF',
          cursor: 'default'
        }
      },
      name: 'light',
      type: 'scatter',
      coordinateSystem: 'geo'
    },
    {
      type: 'map',
      map: 'china',
      geoIndex: 0,
      aspectScale: 1,
      showLegendSymbol: false,
      label: {
        normal: {
          show: false
        },
        emphasis: {
          show: false,
          textStyle: {
            color: '#fff'
          }
        }
      },
      roam: false,
      itemStyle: {
        normal: {
          areaColor: '#031525',
          borderColor: '#FFFFFF'
        },
        emphasis: {
          areaColor: '#2B91B7'
        }
      },

      animation: false,
      data: []
    },
    {
      name: '节点信息',
      type: 'effectScatter',
      coordinateSystem: 'geo',
      symbolSize: 7,
      showEffectOn: 'render',
      rippleEffect: {
        brushType: 'stroke'
      },
      hoverAnimation: true,
      label: {
        normal: {
          formatter: '{b}',
          position: 'right',
          color: 'red',
          show: false
        },
        emphasis: {
          show: false
        }
      },
      itemStyle: {
        // 节点color
        normal: {
          color: '#fff',
          borderColor: '#71A3FF',
          borderWidth: 3,
          shadowSpread: 3,
          shadowBlur: 2,
          shadowColor: '#eee',
          rippleEffect: {
            // 涟漪特效
            period: 1, // 特效动画时长
            scale: 4, // 波纹的最大缩放比例
            brushType: 'stroke' // 波纹的绘制方式：stroke | fill
          }
        },
        emphasis: {
          show: false,
          borderColor: '#19C3A0'
        }
      },
      zlevel: 1
    }
  ],

  visualMap: {
    type: 'piecewise',
    title: '',
    show: true,
    min: 0,
    max: 200,
    left: 'left',
    top: 'bottom',
    calculable: false,
    seriesIndex: [1],
    orient: '', // horizontal
    inRange: {
      color: ['#D6E2FF', '#2967FF'] // 颜色
    },

    textStyle: {
      fontSize: '1.1vw'
    },
    splitNumber: 4, // 对于连续型数据，自动平均切分成几段。默认为5段
    selectedMode: false, // 取消图例上的点击事件
    pieces: [
      { min: 0, max: 10 },
      { min: 10, max: 50 },
      { min: 50, max: 100 },
      { min: 100 }
    ]
  },
  tooltip: {
    trigger: 'item',
    backgroundColor: '#4F80F1',
    formatter: function (params) {
      if (typeof params.value[2] === 'undefined') {
        return ''
      } else {
        // console.log(params)
        return `
          <div style="font-size: 14px;margin:5px 10px;width:125px">${params.data.name}节点数：${params.value[3] || 0}</div>
          ${params.value[4].map((city) => {
          return `<div style="font-size:11px;margin:5px 10px">${city.city}：${city.cityNode}</div>`
        }).join('')}`
      }
    }
  }
}
export default {
  name: 'NodeMap',
  data () {
    return {
      tooltipShow: false,
      timer: null,
      nodeData: []
    }
  },
  mounted () {

    let that = this
    that.mychart = echarts.init(that.$refs.mapbox) // 获取mapbox盒子
    this.getEosMapData().then((data) => {
      let list = getNodeNumList(data)
      option.series[1].data = list
      that.mychart.setOption(option)
    })
  },
  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    getEosMapData () {
      let that = this
      return new Promise((resolve, reject) => {
        getChainNodes().then((res) => {
          if (res.code == 200) {
            option.series[0].data = convertData(res.data)
            option.series[2].data = convertData(res.data)
            that.mychart.setOption(option, true)
            window.addEventListener('resize', () => {
              that.mychart.resize()
            })
            resolve(res.data)
          } else {
            that.msgInfo('error', res.message, true)
            resolve([])
          }
        }).catch((error) => {
          resolve([])
          that.msgInfo('error', error.message, true)
        })
      })
    }
  },
  watch: {
  }
}
</script>

<style lang="less" scoped>
// .map-title {
// text-align: center;
// padding-top: 45px;
// .eos {
//   display: inline-block;
//   width: 120px;
//   color: #838383;
//   background-color: #f1f5f5;
//   padding: 8px;
//   font-size: 14px;
//   border-radius: 4px;
//   cursor: pointer;
// }
// .fabric {
//   background-color: #3d73ef;
//   color: #fff;
//   // /cursor:not-allowed;
// }
// }

.map-title {
  margin: 10px 0 0 20px;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: calc(50% - 7.5px);
    left: 0;
    width: 6px;
    height: 16px;
    background: #19c3a0;
    opacity: 1;
    border-radius: 3px;
  }
  .eos {
    margin-left: 10px;
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    line-height: 21px;
    color: #333333;
    opacity: 1;
  }
}
.box {
  width: 60vh;
  height: 53vh;
  margin: auto;
  color: #a3a3df;
  cursor: default;
}
</style>
