<template>
  <div class="multilink_index">
    <keep-alive v-if="currentTab === 'blockchain_network'">
    <NetworkTable />
    </keep-alive>
    <keep-alive :exclude="excludeArr" v-else>
    <router-view />
    </keep-alive>
  </div>
</template>

<script>
import NetworkTable from './network-table.vue'
export default {
  name: 'network_index',
  components: {
    NetworkTable
  },
  data () {
    return {
      excludeArr: ['network-details']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>

<style lang="less" scoped>
.multilink_index{
 //
}
</style>
