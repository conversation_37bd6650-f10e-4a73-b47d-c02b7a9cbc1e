<template>
  <div class="oderapprovel">
    <Tabs :value="name" @on-click="clickTab">
      <TabPane label="待审批" name="name1">
        <p style="text-align:right; margin:10px 10px 15px 0px;">
          <Input class="width-input" style="vertical-align:baseline;" placeholder="请输入工单标题" v-model="title1" @keyup.enter="searchList" @keyup.enter.native="searchList">
          <Icon type="ios-search" slot="suffix" @click="searchList" />
          </Input>
        </p>
        <edit-table-mul :columns="columns" v-model="tableData"></edit-table-mul>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px" />
      </TabPane>
      <TabPane label="已审批" name="name2">
        <p style="text-align:right; margin:10px 10px 15px 0px;">
          <Input class="width-input" style="vertical-align:baseline;" placeholder="请输入工单标题" v-model="title2" @keyup.enter="searchList2" @keyup.enter.native="searchList2">
          <Icon type="ios-search" slot="suffix" @click="searchList2" />
          </Input>
        </p>
        <edit-table-mul :columns="columns2" v-model="tableData2"></edit-table-mul>
        <Page :total="tablePageParam2.pagetotal" :current.sync="tablePageParam2.pageIndex" @on-change="pageChange2" :page-size="tablePageParam2.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange2" style="text-align:right;line-height:40px" />
      </TabPane>
    </Tabs>

    <Modal v-model="modalAppro" :title="appModal" width="900" :draggable=true sticky :mask-closable="false">
      <Card dis-hover>
        <p class="title bs"> 问题信息 </p>
        <span>问题位置：{{ arrDetails.menuFunction }}</span><br>
        <span>标题：{{ arrDetails.title }}</span><br>
        <span :style="styleBr">描述：{{ arrDetails.brief }}</span><br>
        <span :style="styleBr">调整期望：{{ arrDetails.expectResult }}</span><br>
        <span v-if="arrDetails.isImg === true">附件：<br>
          <img :style="{width:changeWidth}" @click="changeWidthFn" :src="fileAddr" />
        </span>
        <div class="p-img" v-if="arrDetails.isImg !== true">附件：<br>
          <span @click="downloadFile(arrDetails.fileId, arrDetails.fileName)"><a>{{arrDetails.fileName}}</a></span>
        </div>
      </Card>
      <Card style="margin-top:5px; overflow:hidden" dis-hover v-if="arrDetails.reportUser">
        <p class="title bs"> 填报人信息 </p>
        <p>填报人：<span>{{ arrDetails.reportUser.reportUserName }}</span></p>
        <p>所属租户：<span>{{ arrDetails.tenantName }}</span></p>
        <p>平台角色：<span>{{ arrDetails .reportUser.reportRoleName}}</span></p>
        <p>填报日期：<span>{{ arrDetails.reportTime }}</span></p>
        <p>邮箱地址：<span>{{ arrDetails.reportUser.reportEmail }}</span></p>
        <p>联系电话：<span>{{ arrDetails.reportUser.reportPhoneNumber }}</span></p>
        <p>替他人填报：<span>{{ arrDetails.reportType==='FOR_OTHERS'?'是':'否' }}</span></p>
      </Card>
      <Card style="margin-top:5px; overflow:hidden" dis-hover v-if="arrDetails.reportType === 'FOR_OTHERS'">
        <p class="title bs"> 申请人信息 </p>
        <p>填报人：<span>{{ arrDetails.applyUser.applyUserName }}</span></p>
        <p>所属租户：<span>{{ arrDetails.tenantName }}</span></p>
        <p>平台角色：<span>{{ arrDetails .applyUser.applyRoleName}}</span></p>
        <p>邮箱地址：<span>{{ arrDetails.applyUser.applyEmail }}</span></p>
        <p>联系电话：<span>{{ arrDetails.applyUser.applyPhoneNumber }}</span></p>
      </Card>
      <div slot="footer">
        <Card shadow :bordered="false">
          <p class="title" style="text-align:left;">处理情况</p>
          <i-Form :model="formItem" :rules="formItemRule" :label-width="80" ref="formItem" style="text-align:left;">
            <!-- <FormItem style="margin:1px;padding:1px;" label="处理说明" prop="auditRemark">
            <Input  v-model="auditRemark"  maxlength="200" show-word-limit type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="说明（选填）"/>
        </FormItem> -->
            <FormItem style="padding:20px 0 10px 0;" label="处理说明" prop="auditRemark" v-show="formItem.approStatus==='PROCESSED_FAILED'">
              <Input v-model="formItem.auditRemark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="说明" :maxlength="30" show-word-limit></Input>
            </FormItem>
            <FormItem style="padding:20px 0 10px 0;" label="处理说明" v-show="formItem.approStatus!=='PROCESSED_FAILED'">
              <Input v-model="formItem.auditRemark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="说明（选填）" :maxlength="30" show-word-limit></Input>
            </FormItem>
            <FormItem style="margin:5px;padding:5px;" label="处理结果" prop="approStatus">
              <RadioGroup v-model="formItem.approStatus">
                <Radio label="PROCESSED_SUCCESS">处理成功</Radio>
                <Radio label="PROCESSED_FAILED">处理失败</Radio>
              </RadioGroup>
              <p style="font-size:10px; color: #A5A4BF;">*处理结果将以短信形式通知用户</p>
            </FormItem>
          </i-Form>
          <Button style="margin-top: -28px;" type="primary" @click="ok('formItem')">提交</Button>
          <Button style="margin-top: -28px;" type="default" @click="cancelApp('formItem')">取消</Button>
        </Card>
      </div>
    </Modal>

    <Modal v-model="modalDetail" :title="appModal" width="900" :draggable=true sticky :mask-closable="false">
      <Card dis-hover>
        <p class="title bs"> 问题信息 </p>
        <span>问题位置：{{ arrDetails.menuFunction }}</span><br>
        <span>标题：{{ arrDetails.title }}</span><br>
        <span :style="styleBr">描述：{{ arrDetails.brief }}</span><br>
        <span :style="styleBr">调整期望：{{ arrDetails.expectResult }}</span><br>
        <span v-if="arrDetails.isImg === true">附件：<br>
          <img :style="{width:changeWidth}" @click="changeWidthFn" :src="fileAddr" />
        </span>
        <div class="p-img" v-if="arrDetails.isImg !== true">附件：<br>
          <span @click="downloadFile(arrDetails.fileId, arrDetails.fileName)"><a>{{arrDetails.fileName}}</a></span>
        </div>
      </Card>
      <Card style="margin-top:5px; overflow:hidden" dis-hover v-if="arrDetails.reportUser">
        <p class="title bs"> 填报人信息 </p>
        <p>填报人：<span>{{ arrDetails.reportUser.reportUserName }}</span></p>
        <p>所属租户：<span>{{ arrDetails.tenantName }}</span></p>
        <p>平台角色：<span>{{ arrDetails .reportUser.reportRoleName}}</span></p>
        <p>填报日期：<span>{{ arrDetails.reportTime }}</span></p>
        <p>邮箱地址：<span>{{ arrDetails.reportUser.reportEmail }}</span></p>
        <p>联系电话：<span>{{ arrDetails.reportUser.reportPhoneNumber }}</span></p>
        <p>替他人填报：<span>{{ arrDetails.reportType==='FOR_OTHERS'?'是':'否' }}</span></p>
      </Card>
      <Card style="margin-top:5px; overflow:hidden" dis-hover v-if="arrDetails.reportType === 'FOR_OTHERS'">
        <p class="title bs"> 申请人信息 </p>
        <p>填报人：<span>{{ arrDetails.applyUser.applyUserName }}</span></p>
        <p>所属租户：<span>{{ arrDetails.tenantName }}</span></p>
        <p>平台角色：<span>{{ arrDetails .applyUser.applyRoleName}}</span></p>
        <p>邮箱地址：<span>{{ arrDetails.applyUser.applyEmail }}</span></p>
        <p>联系电话：<span>{{ arrDetails.applyUser.applyPhoneNumber }}</span></p>
      </Card>
      <div slot="footer" class="bg1" :bordered="false" style="background-color:#F5F6FA" v-show="arrDetails.orderStatus === 'PROCESSED_SUCCESS'">
        <p class="title" style="text-align:left;">处理情况</p>
        <div class="divS">
          <p style="text-align:left; margin-left:30px; margin-top:10px;">结果:<span class="resultS">处理成功</span></p>
          <p style="text-align:left; margin-left:30px; margin-top:5px;">说明:<span>{{ arrDetails.auditRemark }}</span></p>
        </div>
        <Button type="default" @click="cancelDet('formItem')">返回</Button>
      </div>

      <div slot="footer" class="bg2" :bordered="false" style="background-color:#F5F6FA" v-show="arrDetails.orderStatus !== 'PROCESSED_SUCCESS'">
        <p class="title" style="text-align:left;">处理情况</p>
        <div class="divS">
          <p style="text-align:left; margin-left:30px; margin-top:10px;">结果:<span class="resultF"> 处理失败 </span></p>
          <p style="text-align:left; margin-left:30px; margin-top:5px;">说明:<span>{{ arrDetails.auditRemark }}</span></p>
        </div>
        <Button type="default" @click="cancelDet('formItem')">返回</Button>
      </div>
    </Modal>

  </div>
</template>

<script>
import { getOrderList, orderApprovel, getOrderDetailNew, downloadAnnounceFile } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import { localRead } from '@/lib/util'
export default {
  name: 'workorder-approvel',
  components: {
    EditTableMul
  },
  data () {
    return {
      name: this.$route.params.tabs || 'name1',
      styleBr: {
        wordWrap: 'break-word',
        wordBreak: 'break-all',
        whiteSpace: 'pre-wrap !important'
      },
      modalDetail: false,
      modalAppro: false,
      changeWidth: '100px',
      title1: '',
      title2: '',
      orderStatus: '',
      orderId: '',
      formItem: {
        auditRemark: '',
        approStatus: 'APPROVED'
      },
      formItemRule: {
        approStatus: [{ required: true, message: '请选择处理结果！', trigger: 'change' }],
        auditRemark: [{ required: true, message: '请填写处理说明！', trigger: 'blur' }]
      },
      appModal: '工单审批',
      arrDetails: {},
      statusStr: '',
      statusStr2: '',
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        { key: 'title', title: '标题', minWidth: 300, tooltip: true },
        { key: 'tenantName', title: '租户', tooltip: true },
        { key: 'reportRoleName', title: '角色', tooltip: true },
        { key: 'reportUserName', title: '填报人', tooltip: true },
        { key: 'reportTime', title: '填报时间', minWidth: 50, tooltip: true },
        {
          key: 'orderStatus',
          title: '状态',
          minWidth: 120,
          render: (h, params) => {
            const row = params.row
            const color = row.orderStatus === 'UNAPPROVED' ? 'primary' : row.orderStatus === 'PROCESSED_SUCCESS' ? 'success' : 'error'
            const text = row.orderStatus === 'UNAPPROVED' ? '待审核' : row.orderStatus === 'PROCESSED_SUCCESS' ? '处理成功' : '处理失败'
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, text)
          }
        },
        {
          key: 'action',
          title: '操作',
          // fixed: 'right',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                style: this.buttonStyle,
                on: {
                  click: () => {
                    this.ApproDetails(params.index)
                  }
                }
              }, '审批')

            ])
          }
        }
      ],
      tablePageParam2: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      columns2: [
        { key: 'title', title: '标题', minWidth: 300, tooltip: true },
        { key: 'tenantName', title: '租户', tooltip: true },
        { key: 'reportRoleName', title: '角色', tooltip: true },
        { key: 'reportUserName', title: '填报人', tooltip: true },
        { key: 'reportTime', title: '填报时间', minWidth: 50, tooltip: true },
        {
          key: 'orderStatus',
          title: '状态',
          minWidth: 120,
          render: (h, params) => {
            const row = params.row
            const color = row.orderStatus === 'UNAPPROVED' ? 'primary' : row.orderStatus === 'PROCESSED_SUCCESS' ? 'success' : 'error'
            const text = row.orderStatus === 'UNAPPROVED' ? '待审核' : row.orderStatus === 'PROCESSED_SUCCESS' ? '处理成功' : '处理失败'
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, text)
          }
        },
        {
          key: 'action',
          title: '操作',
          // fixed: 'right',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    this.showDetails(params.index)
                  }
                }
              }, '详情')

            ])
          }
        }
      ],
      tableData: [],
      tableData2: [],
      fileAddr: '',
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  methods: {
    clickTab (name) {
      if (name === 'name1') {
        this.title1 = ''
        this.tablePageParam = {
          pagetotal: 0,
          pageSize: 10,
          pageIndex: 1
        }
        this.getTableData()
      }
    },
    downloadFile (fileId, fileName) {
      downloadAnnounceFile(fileId).then(res => {
        var blob = new Blob([res])
        var downloadElement = document.createElement('a')
        var href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = fileName
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      }).catch(error => {
        this.msgInfo('error', error, true)
      })
    },
    changeWidthFn () {
      if (this.changeWidth === '100px') {
        this.changeWidth = '600px'
      } else {
        this.changeWidth = '100px'
      }
    },
    init () {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.formItem = {
        auditRemark: '',
        approStatus: ''
      }

      this.title1 = ''
      this.title2 = ''
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content,
        duration: 8
      })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    pageChange2 (index) {
      this.tablePageParam2.pageIndex = index
      this.getTableData2()
    },
    pageSizeChange2 (index) {
      this.tablePageParam2.pageSize = index
      this.getTableData2()
    },
    showDetails (index) {
      // console.log('进入查看详情弹窗函数')
      this.orderStatus = ''
      this.changeWidth = '100px' // 初始化宽度
      getOrderDetailNew(this.tableData2[index].orderId).then(res => {
        if (res.code === '00000') {
          this.modalDetail = true
          this.arrDetails = res.data
          this.orderId = res.data.orderId
          if (res.data.isImg) {
            downloadAnnounceFile(res.data.fileId).then(res => {
              console.log(res);
              var blob = new Blob([res])
              this.fileAddr = window.URL.createObjectURL(blob)
            }).catch(error => {
              this.msgInfo('error', error, true)
            })
          }
          if (res.data.orderStatus === 'PROCESSED_SUCCESS') {
            this.orderStatus = '处理成功'
          } else {
            this.orderStatus = '处理失败'
          }
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else this.msgInfo('info', res.message)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },

    getTableData () {
      // 查询待审核状态的数据
      this.statusStr = 'UNAPPROVED'
      const params = {
        title: this.title1,
        auditStatus: this.statusStr,
        pageParam: this.tablePageParam
      }

      getOrderList(params).then(res => {
        if (res.code === '00000') {
          // console.log('工单列表查询：getTableData===>', res)
          this.tableData = res.data.records
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        // console.log('getTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    getTableData2 () {
      // 查询已审核的所有数据
      this.statusStr2 = 'APPROVED'
      const params = {
        title: this.title2,
        auditStatus: this.statusStr2,
        pageParam: this.tablePageParam2
      }
      getOrderList(params).then(res => {
        if (res.code === '00000') {
          // console.log('getTableData===>', res)
          this.tableData2 = res.data.records
          this.tablePageParam2 = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        // console.log('getTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    searchList () { this.getTableData() },
    searchList2 () { this.getTableData2() },

    // 审批弹窗
    ApproDetails (index) {
      // console.log('进入弹窗函数')
      // 状态和审批描述置空(X掉弹窗后，置空上一次的操作。)
      this.changeWidth = '100px' // 初始化宽度
      this.formItem.approStatus = ''
      this.formItem.auditRemark = ''

      getOrderDetailNew(this.tableData[index].orderId).then(res => {
        if (res.code === '00000') {
          this.modalAppro = true
          this.arrDetails = res.data
          this.orderId = res.data.orderId
          if (res.data.isImg) {
            downloadAnnounceFile(res.data.fileId).then(res => {
              console.log(res);
              var blob = new Blob([res])
              this.fileAddr = window.URL.createObjectURL(blob)
            }).catch(error => {
              this.msgInfo('error', error, true)
            })
          }
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else this.msgInfo('info', res.message)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    ok (name) {
      this.$refs[name].validate((valid) => {
        if (!this.formItem.approStatus) {
          // this.msgInfo('warning', '请选择是否同意！', true)
        } else {
          orderApprovel(this.orderId, this.formItem.approStatus, this.formItem.auditRemark).then(res => {
            // console.log('addChainApproval===>', res)
            // this.msgInfo('info', res.message)
            this.tipInfo(res)
          }).catch(error => {
            // console.log('contractApprovel.error===>', error)
            this.msgInfo('error', error.message, true)
          })
          // 审批一条后，状态和审批描述置空
          this.formItem.approStatus = ''
          this.formItem.auditRemark = ''
        }
      })
    },
    tipInfo (res) {
      if (res.code === '00000') {
        this.msgInfo('success', res.message, true)
        this.getTableData()
        this.getTableData2()
        this.modalAppro = false
      } else {
        // console.log('tipInfo-error:', res.message)
        this.msgInfo('error', res.message, true)
      }
    },
    cancelApp (name) {
      this.init()
      this.modalAppro = false
      // 用户操作清空，状态和审批描述置空
      this.formItem.approStatus = ''
      this.formItem.auditRemark = ''
    },
    cancelDet (name) {
      this.init()
      this.modalDetail = false
    }
  },
  computed: {


    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }

    },
  },
  watch: {
    tableData: {
      handler (newVal) {
        //
      },
      deep: true,
      immediate: false
    }
  },
  mounted () {
    this.getTableData()
    this.getTableData2()
  }
}
</script>

<style lang="less" scoped>
 .width-input{
    width:15vw;
    min-width:200px;
  }
.p-img {
  display: flex;
  span {
    color: #3d73ef;
    opacity: 0.8;
    &:hover {
      opacity: 1;
    }
  }
}
input {
  margin: 0 0 10px;
}
button.btn {
  position: absolute;
  right: 10px;
  margin: 0 10px;
}
.bt1 {
  margin-right: 10px;
}
.search-title {
  font-size: 12px;
}

.title {
  font-weight: bold;
  font-size: 16px;
}
.bs {
  text-indent: 10px;
  line-height: 15px;
  border-left: 5px solid #3d73ef;
  margin-bottom: 15px;
}
.bg1 {
  position: relative;
  background-repeat: no-repeat;
  background: top right no-repeat;
  background-image: url("../../../assets/img/processed_success.png");
}
.bg2 {
  //以下是右下角图片设置
  position: relative;
  background-repeat: no-repeat;
  background: top right no-repeat;
  width: 100%;
  height: 140px;
  background-image: url("../../../assets/img/processed_failed.png");
}
.resultS {
  margin-top: 30px;
  font-weight: bold;
  font-size: 18px;
  color: #52c7aa;
  margin-left: 5px;
}

.resultF {
  margin-top: 20px;
  font-weight: bold;
  font-size: 18px;
  color: #ef7d68;
  margin-left: 5px;
}
.divS {
  margin-top: 10px;
  margin-left: 30px;
  width: 500px;
  height: 70px;
  background-color: rgb(255, 255, 255);
}
/deep/.ivu-modal-footer {
  /* border-top: 1px solid #e8eaec; */
  /* padding: 12px 18px 12px 18px; */
  text-align: right;
  background-color: #f5f6fa;
}
/deep/.ivu-modal > .ivu-modal-content > .ivu-modal-body {
  max-height: 50vh;
  overflow: auto;
}
/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
</style>
