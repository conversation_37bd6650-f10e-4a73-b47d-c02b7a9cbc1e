// 常见问题解决
<template>
  <div class="problem-solving-wrap">
    <div class="title-wrap">
      <span>常见问题解答</span>
      <Button v-if="roleId == '1'" class="btn" type="success" icon="md-add" ghost @click="problemHandle(false)">新增问题</Button>
    </div>
    <div class="analog-wrap">
      <div v-if="tableData.length === 0" class="empty">暂无数据</div>
      <div v-for="(item, index) in tableData" :key="item.id" class="table-item">
        <div class="table-l">
          <div style="white-space:pre-line">{{ sortIndex + index + 1 }}、{{ item.problemName }}</div>
          <div style="white-space:pre-line">{{ item.problemAnswer }}</div>
        </div>
        <div class="table-r" v-if="roleId == '1'">
          <Button size="small" @click="problemHandle(item)" class="btn">编辑</Button>
          <Divider type="vertical" />
          <Poptip transfer placement="top-end" confirm title="确认删除吗?" @on-ok="deleteOk(item)" ok-text="确认" cancel-text="取消">
            <Button size="small" class="btn">删除</Button>
          </Poptip>
        </div>
      </div>
    </div>
    <div class="page-wrap">
      <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[5, 8, 10]" show-total show-elevator show-sizer @on-page-size-change="sizeChange" style="text-align: right" :transfer='true' />
    </div>
    <ProblemSolvingModules ref="problemSolvingModules" :rowData="rowData" @commonProblemList="commonProblemList" />
  </div>
</template>

<script>
import ProblemSolvingModules from './modules/problem-solving-modules.vue'
import { commonProblemList, delCommonProblem } from '@/api/data'
import { localRead } from '@/lib/util'
export default {
  components: {
    ProblemSolvingModules
  },
  data () {
    return {
      rowData: 'add',
      sortIndex: 0,
      roleId: localRead('roleId'),
      tablePageParam: {
        pagetotal: 0,
        pageIndex: 1,
        pageSize: 5
      },
      tableData: []
    }
  },
  methods: {
    problemHandle (row) {
      this.rowData = row || 'add'
      this.$refs.problemSolvingModules.visible = true
    },
    /**
     * 排序
     */
    getIndex () {
      this.sortIndex = (this.tablePageParam.pageIndex - 1) * this.tablePageParam.pageSize
    },
    /**
     * 确认删除
     */
    deleteOk (row) {
      delCommonProblem({ id: row.id })
        .then((res) => {
          if (res.code === '00000') {
            this.msgInfo('success', res.message)
            // 删除成功重新获取当前页数据
            this.commonProblemList()
          } else {
            this.msgInfo('error', res.message)
          }
        })
        .catch((error) => {
          this.msgInfo('error', error.message)
        })
    },
    /**
     * 每页条数改变
     */
    sizeChange (size) {
      this.tablePageParam.pageSize = size
      this.commonProblemList()
    },
    /**
     * 页码改变
     */
    pageChange (pageIndex) {
      this.tablePageParam.pageIndex = pageIndex
      this.commonProblemList()
      // console.log('pageIndex', pageIndex)
    },
    /**
     * 获取数据列表
     */
    commonProblemList () {
      let params = {
        pageSize: this.tablePageParam.pageSize,
        pageIndex: this.tablePageParam.pageIndex
      }
      commonProblemList(params)
        .then((res) => {
          if (res.code === '00000') {
            this.tablePageParam = {
              pagetotal: res.data.total,
              pageIndex: res.data.current,
              pageSize: res.data.size
            }
            this.getIndex()
            this.tableData = res.data.records || []
          } else {
            this.tableData = []
            this.tablePageParam.pagetotal = 0
            this.msgInfo('error', res.message)
          }
        })
        .catch((error) => {
          this.tableData = []
          this.tablePageParam.pagetotal = 0
          this.msgInfo('error', error.message)
        })
    },

    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    }
  },
  created () {
    this.commonProblemList()
  }
}
</script>
<style lang="less" scoped>
.problem-solving-wrap {
  margin: 20px;
  .title-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    & > span {
      font-size: 18px;
      font-weight: bold;
    }
  }
  .analog-wrap {
    margin-top: 20px;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 20px 50px 20px 20px;
    .table-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:not(:nth-last-child(1)) {
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
      }
      &:not(:first-child) {
        margin-top: 20px;
      }
      .table-l {
        div:nth-child(1) {
          font-size: 15px;
          font-weight: bold;
        }
        div:nth-child(2) {
          margin-top: 5px;
          font-size: 14px;
          color: #999;
        }
      }
      .table-r {
        flex: 0 0 120px;
        margin-left: 30px;
        .btn {
          color: #3d73ef;
          border: 1px solid #3d73ef;
        }
      }
    }
  }
  .page-wrap {
    margin-top: 10px;
  }
  .empty {
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgb(81, 90, 110);
  }
}
</style>
