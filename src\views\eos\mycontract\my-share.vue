<template>
  <div class="contract">
    <p style="text-align:right;">
      <!-- <b style="float:left;margin: 10px;">智能合约</b> -->
      <Input style="width:250px;vertical-align:baseline;" placeholder="可输入合约名称或应用名称查询"  v-model="queryName" @keyup.enter="searchList" @keyup.enter.native="searchList">
         <Icon type="ios-search" slot="suffix" @click="searchList"/>
      </Input>
      <!-- <Button type="primary" @click="searchList" icon="ios-search">查询</Button> -->
    </p>
    <div class="share-flex" v-if="tableData.length>0">
      <Card   class="share-card" :bordered="false" v-for="(item,index) in tableData" :key="index">
          <div class="share-card-item" style="text-align:left">
              <h3>{{item.contractReadableName}}（合约链账户：{{item.contractAccountName}}）</h3>
              <p>合约名称：{{item.contractName}}</p>
              <p>链名称：{{item.chainName}}</p>
              <p v-if="item.contractBrief.length>=14">
                <span class="label-span">应用简介：</span>
                <Tooltip max-width="200" transfer="true"  placement="bottom-start" :content="item.contractBrief">
                    <span class="p-span">{{item.contractBrief}}</span>
                </Tooltip>
              </p>
              <p v-else>应用简介：{{item.contractBrief}}</p>
              <p :style="{color:fontStyleObj[item.shareStatusKey]}"><span class="circle"></span>{{item.shareStatus}}</p>
              <div class="btn-div">
                <span class="btn-left"><a  @click="getContract(item)" :disabled="item.shareStatusKey!=='APPROVED'">调整</a></span>
                <span  class="btn-right"><a  @click="dropContract(item)" :disabled="item.shareStatusKey === 'REMOVED' || item.shareStatusKey === 'UNAPPROVED' || item.shareStatusKey === 'REJECT'">下架</a></span>
              </div>
          </div>
      </Card>
    </div>
     <div class="share-flex-none" v-else>
        <img class="imgs" :src="imagesurl"/>
        <p class="contract-none">暂无我共享的合约</p>
      </div>

    <!-- <edit-table-mul style="margin: 10px 0;" :key="transferKey" :columns="columns" v-model="tableData"></edit-table-mul> -->
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[6,12,30,60,120]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;"/>
    <Modal
      v-model="modal"
       width="700"
      title="合约共享详情"
      @on-cancel="modal=false"
      class="eg-class"
      >
      <div class="opera-col">
        <Button icon="ios-add"  type="primary"  @click="addTenant">添加租户</Button>
          <Input placeholder="请输入租户名称" class="input-search" v-model="noTenantName" @keyup.enter.native="searchNoTenantName">
            <Icon type="ios-search" slot="suffix" @click="searchNoTenantName"/>
          </Input>
      </div>
      <Table
        :columns="detailColums"
        :data="detailTableData"
        stripe
        :show-header="true"
          max-height="300">
        </Table>
      <Page
        :total="detailPageParam.pagetotal"
        :current.sync="detailPageParam.pageIndex"
        @on-change="detailPageChange"
        :page-size="detailPageParam.pageSize"
        :page-size-opts="[5,10,20,40,60,100]"
        show-total show-elevator show-sizer
        @on-page-size-change="detailPageSizeChange"
        style="text-align:right;padding-top:15px;"/>
      <div slot="footer">
        <Button type="primary" @click="modal=false">关闭</Button>
      </div>
    </Modal>
     <Modal
      v-model="addModal"
       width="700"
      title="请选择要共享的租户"
      :show-header="false"
      @on-cancel="addModal=false"
      >
      <SearchTablePage :transProps="transProps" :flag="addModal" :contractAccountId="`${contractAccountId}`" @getSelectionData="getSelectionDataFn" />
     <div slot="footer">
        <Button @click="addModal=false">取消</Button>
        <Button type="primary" @click="shareRequest">确定</Button>
      </div>
     </Modal>
  </div>
</template>

<script>
import { findSharedContractTenantList, shareList, addSharedContractTenantList, cancelContract, countShared, shareDelete } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import SearchTablePage from '_c/search-table-page'
export default {
  name: 'my_share',
  components: {
    EditTableMul,
    SearchTablePage
  },
  data () {
    return {
      imagesurl: require('@/assets/img/null.png'),
      noTenantName: '',
      modal: false,
      addModal: false,
      contractAccountId: '',
      transProps: {},
      fontStyleObj: {
        APPROVED: '#15AD31', // 绿色
        REJECT: '#FA5151 ', // 红色
        UNAPPROVED: '#FFC300', // 橘色
        OFF_THE_SHELF: '#eee'
      },
      // shareStatusDict: {
      //   UNAPPROVED: '待审批',
      //   审批通过: '审批通过',
      //   REJECT: '审批拒绝',
      //   OFF_THE_SHELF: '下架' },
      detailColums: [
        // {
        //   type: 'selection',
        //   width: 35,
        //   align: 'center'
        // },
        { key: 'tenantName', title: '租户名称' },
        { key: 'tenantBrief', title: '租户描述', minWidth: 100, tooltip: true },
        { key: 'createTime', title: '配置时间', minWidth: 100, tooltip: true },
        { key: 'action',
          title: '操作',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: { click: () => {
                  this.$Modal.confirm({
                    content: `确定删除该租户吗？`,
                    cancelText: '取消',
                    okText: '确定',
                    onOk: () => {
                      this.deleteTenant(params.index)
                      // 确定后提示
                    },
                    onCancel: () => {
                      // this.$Message.info('Clicked cancel')
                    }
                  })
                }
                }
              }, '删除')
            ])
          }
        }
      ],
      shareItem: { shareTenantList: [] },
      detailTableData: [],
      detailPageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      queryName: '',
      tablePageParam: { pagetotal: 0, pageSize: 6, pageIndex: 1 },
      tableData: []
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    detailPageChange (index) {
      this.detailPageParam.pageIndex = index
      this.getChainTenantList(this.detailPageParam, 1)
    },
    detailPageSizeChange (index) {
      this.detailPageParam.pageSize = index
      this.getChainTenantList(this.detailPageParam, 1)
    },
    getChainTenantList (key) {
      const params = {
        tenantName: this.noTenantName,
        shareRecordId: this.transProps.shareRecordId,
        pageParam: this.detailPageParam
      }
      findSharedContractTenantList(params).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          }
          if (!(key && key === 'delete')) {
            this.msgInfo('warning', res.message, true)
          }
          this.detailTableData = []
          this.detailPageParam = {
            pagetotal: 0,
            pageSize: 10,
            pageIndex: 1
          }
        } else {
          this.detailTableData = res.data.records
          this.detailPageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        }
      }).catch(error => {
        // console.log('findSharedContractTenantList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    dropContract (item) {
      // console.log('item:', item)
      countShared(item.shareRecordId).then(res => {
        if (res.code === '00000') {
          this.$Modal.confirm({
            content: `下架后，${res.data}个租户将无法调用您的共享合约，确定继续吗？`,
            cancelText: '取消',
            okText: '确定',
            onOk: () => {
              this.cancelContract(item)
              // 确定后提示
            },
            onCancel: () => {
              // this.$Message.info('Clicked cancel')
            }
          })
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(err => {
        this.msgInfo('error', err.message, true)
      })
    },
    // 合约下架
    cancelContract (item) {
      cancelContract(item.shareRecordId).then(res => {
        if (res.code === '00000') {
          this.msgInfo('success', '已下架该合约，共享租户不能再调用', true)
          this.getTableData()
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(err => {
        this.msgInfo('error', err.message, true)
      })
    },
    getContract (item) {
      this.modal = true
      this.noTenantName = ''
      this.contractAccountId = item.contractAccountId
      this.transProps = item
      this.getChainTenantList()
    },
    addTenant () {
      this.addModal = true
    },
    shareRequest () {
      if (this.shareItem.shareTenantList.length === 0) {
        this.msgInfo('warning', '请选择共享租户', true)
        return
      }
      const params = {
        shareRecordId: this.transProps.shareRecordId,
        tenantList: this.shareItem.shareTenantList
      }
      addSharedContractTenantList(params).then(res => {
        if (res.code === '00000') {
          this.addModal = false
          this.$Modal.success({
            content: '<p>' + res.message + '</p>',
            onOk: () => {
              this.getChainTenantList()
            }
          })
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else {
          this.msgInfo('warning', res.message, true)
        }
      }).catch(error => {
        console.log('addSharedContractTenantList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    getSelectionDataFn (data) {
      this.shareItem.shareTenantList = data.map(val => {
        return val.tenantId
      })
    },
    searchNoTenantName () {
      this.getChainTenantList(this.noSelectPageParam, 1)
    },
    // 删除租户
    deleteTenant (index) {
      // console.log('===============', index, this.detailTableData[index])
      shareDelete(this.transProps.shareRecordId, this.detailTableData[index].tenantId).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.msgInfo('success', res.message, true)
          this.getChainTenantList('delete')
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getTableData () {
      const params = {
        queryName: this.queryName,
        queryType: 'MY_SHARED_CONTRACT', // 我共享的合约
        pageParam: this.tablePageParam
      }
      shareList(params).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          // console.log('res.data.records:', res.data.records)
          this.tableData = res.data.records
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        }
      }).catch(error => {
        console.log('findSharedContractTenantList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    searchList () { this.getTableData() }

  },
  mounted () {
    this.getTableData()
  },
  deactivated () {
  },
  activated () {
    this.getTableData()
  }
}
</script>

<style lang="less" scoped>
.opera-col{
  text-align: right;
  margin-bottom: 20px;
  .input-search{
    width:250px;
    margin-left:10px;
    vertical-align:middle;
  }
}
.share-flex{
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  margin-bottom: 20px;
  .share-card{
    width:calc((100% - 20px)/3);
    margin-right:10px;
    margin-bottom:10px;
    &:nth-of-type(3n){
      margin-right:0;
    }
    h3{
      margin-bottom: 15px;
      height:40px;
    }
    p{
      line-height: 14px;
      min-height:40px;
      display: flex;
      width: 100%;
      overflow: hidden;
      align-items: center;
      .label-span{
        display: inline-block;
        min-width: 70px;
      }
      .p-span{
        display: inline-block;
        width:calc(100% - 90px);
        max-width: calc((100vw - 320px)/3);
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap;
      }
    }
    .circle{
      border:5px solid;
      border-radius: 5px;
      margin-right: 10px;
    }
    .btn-div{
      span{
        border-top:1px solid #dcdee2;
        width:50%;
        text-align: center;
        display: inline-block;
        line-height: 36px;
        height: 36px;
      }
    }
    .btn-left{
      border-right: 1px solid #dcdee2;
    }
  }
}
.share-flex-none{
  text-align: center;
  min-height:60vh;
  img {
    height:40vh;
    max-width: 80%;
    max-height: 80%;
  }

  .contract-none {
    font-size: 8px;
    color: #d4d3d3;
    margin-top: -5px;
    position: relative;
  }
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61,115,239,.8);
  color: #fff!important;
}
/deep/.ivu-btn-text:active{
  background-color: #3D73EF;
}
</style>
