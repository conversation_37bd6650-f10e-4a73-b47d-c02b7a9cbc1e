let baseURL = process.env.NODE_ENV === 'production'
  ? '/'
  : 'http://10.0.42.61:19102'
// : 'http://192.168.1.38:8900'//接口地址
//  :  'http://10.248.201.71:32387' // 一测地址
fetch('/static/config.json').then(async (response) => {
  if (response.status === 200) {
    let config = null
    if (response.headers.get('content-type') === 'application/json') {
      config = await response.json()
    } else {
      config = await response.text()
    }
    if (config.baseURL) {
      baseURL = config.baseURL
      localStorage.setItem('baseURL', baseURL)
    }
  }
})

export {
  baseURL
}
