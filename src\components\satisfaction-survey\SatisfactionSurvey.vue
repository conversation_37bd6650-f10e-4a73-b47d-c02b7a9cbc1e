<template>
  <!-- <Poptip placement="bottom" class="poptip-diy" ref="poptip">
      <div class="carousel-wrap">
        <div class="window-box">
          <div class="carousel-content" :class="{ active: active }" @click.once="qnUrlClose">
            <img src="../../assets/icons/questionnaire.jpg" alt="">
            <img src="../../assets/icons/questionnaire.jpg" alt="">
            <img src="../../assets/icons/questionnaire.jpg" alt="">
          </div>
        </div>
      </div>
    <div slot="content">
      <div class="satisfaction-survey-wrap" v-if="url !== 'null'">
        <img
          :src="url"
          alt="问卷调查"
        />
      </div>
      <div v-else class="empty">暂无调查问卷</div>
    </div>
  </Poptip> -->

  <Poptip placement="bottom" class="poptip-diy" ref="poptip">
    <div class="carousel-wrap" v-if="type==1">
      <div class="window-box">
        <div class="carousel-content" :class="{ active: active }" @click.once="qnUrlClose">
          <img src="../../assets/icons/zonglan.png" alt="">
          <img src="../../assets/icons/zonglan.png" alt="">
          <img src="../../assets/icons/zonglan.png" alt="">
        </div>
      </div>
    </div>
    <div class="carousel-wraptwo" v-if="type==2">
      <div class="window-boxtwo">
        <div class="carousel-content" :class="{ active: active }" @click.once="qnUrlClose">
          <!-- <img src="../../assets/icons/duowei.png" alt=""> -->
          <img src="../../assets/icons/yunxing.png" alt="">
          <!-- <img src="../../assets/icons/duowei.png" alt="">
          <img src="../../assets/icons/duowei.png" alt=""> -->
        </div>
      </div>
    </div>
    <!-- <div slot="content">
      <div class="satisfaction-survey-wrap" v-if="url !== 'null'">
        <img :src="url" alt="总览视图" />
      </div>
      <div v-else class="empty">暂无总览视图</div>
    </div> -->
  </Poptip>
</template>

<script>
import { localRead, localSave } from '@/lib/util'
export default {
  props: {
    type: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      url: localRead('qnUrl'),
      active: false
    }
  },
  created () {
  },
  mounted () {
    if (localRead('isShowQn') === 'true') {
      this.$refs.poptip.visible = true
    }
  },
  methods: {
    qnUrlClose () {
      localSave('isShowQn', 'false')
    }
  }
}
</script>

<style lang="less" scoped>
@keyframes mymove {
  0% {
    top: 0px;
  }
  40% {
    top: 0px;
  }
  50% {
    top: -18px;
  }
  90% {
    top: -18px;
  }
  100% {
    top: -36px;
  }
}
.poptip-diy {
  /deep/.ivu-poptip-popper {
    // top: 40px!important;
  }
}

.carousel-wrap {
  cursor: pointer;
  height: 100%;
  width: 56px;
  display: flex;
  justify-content: center;
  align-items: center;

  .window-box {
    position: relative;
    height: 18px;
    width: 56px;
    overflow: hidden;
    .carousel-content {
      // animation: mymove 10s linear 1s infinite normal;
      position: absolute;
      top: 0px;
      left: 0;
      display: flex;
      flex-direction: column;
      img {
        height: 18px;
      }
    }
  }
}

.carousel-wraptwo {
  cursor: pointer;
  height: 100%;
  width: 80px;
  display: flex;
  justify-content: center;
  align-items: center;

  .window-boxtwo {
    position: relative;
    height: 18px;
    width: 80px;
    overflow: hidden;
    .carousel-content {
      // animation: mymove 10s linear 1s infinite normal;
      position: absolute;
      top: 0px;
      left: 0;
      display: flex;
      flex-direction: column;
      img {
        height: 18px;
      }
    }
  }
}
.empty {
  text-align: center;
  background-color: #5c70f5;
  padding: 5px;
  margin: 5px;
  box-shadow: 0 4px 4px -3px rgba(0, 0, 0, 25%);
  border-radius: 2px;
  color: #fff;
}
/deep/.ivu-poptip-popper {
  padding: 0;
}
/deep/.ivu-poptip-arrow {
  display: none;
}
/deep/.ivu-poptip-inner {
  background-color: transparent;
  box-shadow: none;
}

.satisfaction-survey-wrap {
  z-index: 99;
  padding: 0;
  overflow: hidden;
  overflow: hidden;
  img {
    width: 435px;
  }
}
/deep/.ivu-poptip-body {
  padding: 0;
}
.satisfaction-no {
  text-align: center;
  padding: 10px;
  color: #fff;
  background-color: #5c70f5;
}
</style>
