<template>
  <div class="tenantdetails">
    <Divider orientation="left">基础信息</Divider>
    <p slot="content" class="basetext">
      <span>租户唯一编号：{{ arrDetails.tenantUuid }}</span><br>
      <span>租户名称：{{ arrDetails.tenantName }}</span><br>
      <span>创建时间：{{ arrDetails.createTime }}</span><br>
      <span>描述：{{ arrDetails.tenantBrief }}</span><br>
    </p>
    <Divider orientation="left">分配用户</Divider>
    <div>
      <Transfer :key="transferKey" :data="leftUnassigned" :target-keys="rightAssigned" :list-style="listStyle" :render-format="render" :titles="['未分配用户列表','已分配用户列表']" :operations="['移除用户','分配用户']" filterable filter-placeholder="请输入用户名进行搜索" @on-change="handleChange">
      </Transfer>
    </div>

    <Modal v-model="modal" title="分配角色" @on-ok="ok" @on-cancel="cancel" :mask-closable="false">
      选择角色：<Select v-model="addrole.roleId" placeholder="请选择" style="width:500px;">
        <Option v-for="item in roleList" :value="item.roleId" :key="item.roleLevel">{{ item.roleName }}</Option>
      </Select>
    </Modal>
  </div>
</template>
<script>
import { getTenantDetailsf, getAssigned, getUnassigned, getTenantRoleList, getTenantUser, getTenantUser1 } from '@/api/data'
import { isChinese } from '@/lib/check'
import { localRead } from '@/lib/util'
export default {
  data () {
    return {
      name: 'tenant_details',
      modal: false,
      transferKey: 0,
      tenantId: this.$route.params.tenantId ? this.$route.params.tenantId : '',
      addrole: {
        userIdList: [],
        roleId: '',
        newTargetKeys: []
      },
      arrDetails: {},
      pageParam: {
        pageSize: 1000,
        pageIndex: 1
        // ,orders: [{ asc: true, column: 'id' }]
      },
      roleList: [],
      assigned: [],
      unassigned: [],
      leftUnassigned: [],
      rightAssigned: [],
      max: 250,
      listStyle: { minWidth: `250px`, width: '30vw', height: '300px', marginBottom: '10px', marginTop: '10px' },
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  computed: {
    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
  methods: {
    init () { this.addrole = { userIdList: [], roleId: '', newTargetKeys: [] } },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    ok () {
      const parans = {
        userIdList: this.addrole.userIdList,
        roleId: this.addrole.roleId
      }
      getTenantUser(this.tenantId, parans, 'POST').then(res => {
        if (res.code === '00000') {
          this.initFun()
          this.rightAssigned = this.addrole.newTargetKeys
        } else if (res.code === 'A0209') {
          let html = res.message + '：'
          let data = res.data.errorList
          for (let i = 0; i < data.length; i++) {
            html += data[i].errorMsg + ' '
          }
          alert(html)
          this.initFun()
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else this.msgInfo('warning', res.message, true)
      }).catch(error => {
        console.log('getTenantUser.error===>', error)
        this.msgInfo('error', error.message, true)
      })
      this.init()
    },
    cancel () { this.init() },
    initFun () {
      getUnassigned(this.tenantId, this.pageParam).then(res => {
        // console.log('getUnassigned===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else this.unassigned = res.data.records
        getAssigned(this.tenantId, this.pageParam).then(res => {
          // console.log('getAssigned===>', res)
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else this.assigned = res.data.records
        }).catch(error => {
          console.log('getAssigned.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }).catch(error => {
        console.log('getUnassigned.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    getUnassignedList () {
      if (this.unassigned.length !== 0 || this.assigned.length !== 0) {
        let mockArr = []
        let mockData = []
        if (this.assigned.length !== 0) mockData = this.unassigned.concat(this.assigned)
        else mockData = this.unassigned
        // console.log('mockData===>', mockData)
        for (let i = 0; i < mockData.length; i++) {
          mockArr.push({
            'key': JSON.stringify(i),
            'label': mockData[i].userLoginId, // '不能为number'
            'userId': JSON.parse(mockData[i].userId),
            'userLoginId': mockData[i].userLoginId,
            'realName': mockData[i].realName,
            'email': mockData[i].email,
            'phoneNumber': mockData[i].phoneNumber,
            'roleId': mockData[i].roleId ? mockData[i].roleId : '',
            'roleName': mockData[i].roleName ? mockData[i].roleName : '',
            'disabled': mockData[i].disabled ? mockData[i].disabled : (this.hasEditPermission ? true : false)
          })
        }
        return mockArr
      }
    },
    getAssignedList () {
      if (this.assigned.length !== 0) {
        let mockArr = []
        mockArr = this.getUnassignedList().filter((item) => item.roleId !== '').map(item => item.key)
        return mockArr
      }
    },
    handleChange (newTargetKeys, index, targetKeys) {
      // console.log('handleChange===>', newTargetKeys, index, targetKeys)
      let userIdList = []
      if (this.tenantId && index === 'right') {
        if (targetKeys.length > 10) this.msgInfo('warning', '最大分配数暂不能超过10个！', true)
        else {
          for (let i = 0; i < targetKeys.length; i++) userIdList.push(this.leftUnassigned[targetKeys[i]].userId)
          // console.log(userIdList)
          this.addrole.userIdList = userIdList
          this.addrole.newTargetKeys = newTargetKeys
          this.modal = true
        }
      } else if (this.tenantId && index === 'left') { // delect
        for (let j = 0; j < targetKeys.length; j++) userIdList.push(this.leftUnassigned[targetKeys[j]].userId)
        const params = {
          userIdList,
          roleId: ''
        }
        getTenantUser1(this.tenantId, params).then(res => {
          if (res.code === '00000') {
            this.initFun()
            this.rightAssigned = newTargetKeys
          } else if (res.code === 'A0211') {
            this.$Modal.confirm({
              title: '提示',
              content: '<p>租户管理员已全部移除，是否移除剩余用户</p>',
              onOk: () => {
                const paramsDellAll = {
                  deleteAll: 1
                }
                getTenantUser1(this.tenantId, paramsDellAll).then(res => {
                  if (res.code === '00000') {
                    this.initFun()
                    this.rightAssigned = newTargetKeys
                  } else {
                    this.msgInfo('warning', res.message, true)
                  }
                })
              },
              onCancel: () => {
                this.initFun()
                this.rightAssigned = newTargetKeys
              }
            })
          } else if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        }).catch(error => {
          console.log('getTenantUser.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }
    },
    render (item) {
      // console.log('render===>', item)
      const msg = item.realName + ' - ' + item.userLoginId
      const msg1 = ' - ' + item.phoneNumber + ' - ' + item.email
      let width = msg.length * 10
      if (this.getNum(msg) < 15) {
        width = msg.length * 11
      } else {
        width = this.getNum(msg) >= 25 ? msg.length * 16 : msg.length * 14
      }
      if (width > this.max) {
        this.max = width
        this.$set(this.listStyle, 'min-Width', `${this.max}px`)
      }
      return `<span style="width:${width}px">${msg}</span><br/><span style="margin-left:30px;">${msg1}</span>`
    },
    getNum (msg) {
      let num = 0
      for (let i in msg) {
        if (isChinese(msg[i])) {
          num++
        }
      }
      return num
    },
    tenantIdChange () {
      getTenantDetailsf(this.tenantId).then(res => {
        // console.log('getTenantDetailsf===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else this.arrDetails = res.data
      }).catch(error => {
        console.log('getTenantDetailsf.error===>', error)
        this.msgInfo('error', error.message, true)
      })
      getTenantRoleList(this.tenantId).then(res => {
        // console.log('getTenantRoleList===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else this.roleList = res.data
      }).catch(error => {
        console.log('getTenantRoleList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
      this.initFun()
    }
  },
  watch: {
    assigned: {
      handler (newVal) {
        this.leftUnassigned = this.getUnassignedList()
        this.rightAssigned = this.getAssignedList()
      },
      deep: true,
      immediate: false
    },
    leftUnassigned: {
      handler (newVal) {
        ++this.transferKey
      },
      deep: true,
      immediate: false
    },
    '$route.params.tenantId' (val) {
      if (val) {
        this.tenantId = val
        this.tenantIdChange()
      }
    }
  },
  created () {
    if (this.tenantId) {
      getTenantDetailsf(this.tenantId).then(res => {
        // console.log('getTenantDetailsf===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else this.arrDetails = res.data
      }).catch(error => {
        console.log('getTenantDetailsf.error===>', error)
        this.msgInfo('error', error.message, true)
      })
      getTenantRoleList(this.tenantId).then(res => {
        // console.log('getTenantRoleList===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else this.roleList = res.data
      }).catch(error => {
        console.log('getTenantRoleList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
      this.initFun()
    }
  },
  mounted () {
    // console.log(this.max, 'this.maxmounted')
    this.$nextTick(() => {
      // console.log(this.max, 'this.maxmounted--nextTick')
    })
  }
}
</script>
<style lang="less" scoped>
.tenantdetails {
  .basetext {
    span {
      text-align: left;
      margin: 0 30px;
      line-height: 30px;
    }
  }
}
/* 添加一个全屏覆盖的半透明层以阻止点击 */
.disabled-overlay {
  position: relative;
}
.disabled-overlay::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.4); /* 半透明背景色 */
  z-index: 9; /* 确保覆盖层位于组件之上但不遮挡其他重要元素 */
  cursor: not-allowed; /* 更改鼠标指针为禁止图标 */
}
</style>
