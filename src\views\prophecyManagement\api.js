import {
    getAction,
    deleteAction,
    putAction,
    postAction,
    httpAction,
    uploadAction,
    getFileStreamAction,
    downloadFile,
    getArraybufferAction,
    postFileStreamAction
} from '../../api/oracle/manage'

// 示例
// const 方法名 = (params) => httpAction(url, parameter, method);
// const 方法名 = (params) => deleteAction(url, params);
// const 方法名 = (params) => postAction(url, params);
// const 方法名 = (params) => putAction(url, params);



// 消费者用户管理表api文档

export const addDataConsumer = (params) => postAction('/CMBaaSApi/OracleDataConsumer/addDataConsumer', params);//添加数据用户信息表
export const delDataConsumer = (params) => postAction('/CMBaaSApi/OracleDataConsumer/delDataConsumer', params);//删除数据用户信息表
export const getDataConsumerDetails = (params) => postAction('/CMBaaSApi/OracleDataConsumer/getDataConsumerDetails', params);//获取数据用户信息表表详情
export const getDataConsumerList = (params) => postAction('/CMBaaSApi/OracleDataConsumer/getDataConsumerList', params);//获取数据用户列表
export const updateDataConsumer = (params) => postAction('/CMBaaSApi/OracleDataConsumer/updateDataConsumer', params);//编辑数据用户信息表表
export const getHttpContractList = (params) => postAction('/eos/contract/list', params);//查询智能合约列表
export const getHttpContractListTwo = (params) => postAction('/cd/contract/list', params);//查询智能合约列表



// 信源管理api文档

export const addProvider = (params) => postAction('/CMBaaSApi/OracleProvider/addProvider', params);//添加信源
export const getOracleProviderDetails = (params) => getAction(`/CMBaaSApi/OracleProvider/getOracleProviderDetails/${params}`,null);//获取数信源表详情
export const updateOracleProvider = (params) => postAction('/CMBaaSApi/OracleProvider/updateOracleProvider', params);//编辑数信源表
export const getOracleProviderList = (params) => postAction('/CMBaaSApi/OracleProvider/getOracleProviderList', params);//获取数据用户列表
export const getProviderUserList = (params) => postAction('/CMBaaSApi/OracleProvider/getProviderUserList', params);//获取信源用户下拉列表
export const getContractList = (params) => postAction('/CMBaaSApi/Assembly/getContractList', params);//获取指定信源链合约列表
export const getNodeList = (params) => postAction('/CMBaaSApi/Assembly/getNodeList', params);//获取可用信源链列表
export const regAgain = (params) => getAction(`/CMBaaSApi/OracleProvider/regAgain/${params}`,null );//获取可用信源链列表
export const httpReq = (params) => postAction('/CMBaaSApi/OracleProvider/httpReq',params );//HTTP测试
export const logOutProvider = (params) => getAction(`/CMBaaSApi/OracleProvider/logOutProvider/${params}`,null );//注销信源
export const delOracleProvider = (params) => getAction(`/CMBaaSApi/OracleProvider/delOracleProvider/${params}`,null );//删除信源
export const logoutTemp = (params) => postAction('/CMBaaSApi/OracleProvider/logoutTemp',params );//信源模板注销
export const getOracleTempList = (params) => getAction(`/CMBaaSApi/OracleProvider/getOracleTempList/${params}`,null );//
export const regAgainTemp = (params) => postAction('/CMBaaSApi/OracleProvider/regAgainTemp',params );//信源重新注册模板
export const addTemp = (params) => postAction('/CMBaaSApi/OracleProvider/addTemp',params );//
// export const getOracleTempList = (params) => getAction(`/CMBaaSApi/OracleProvider/getOracleTempList/${params}`,null );//
export const getProviderRegisterList = (params) => getAction(`/CMBaaSApi/OracleProvider/getProviderRegisterList/${params}`,null );//
export const getEosList = (params) => postAction('/eos/multi/chain/list',params );//查询所属链
export const getEosUserList = (chainId,params) => postAction(`/eos/chain/${chainId}/account/list`,params );//普通链
export const contractmessage = (chainId,chainAccountId,params) => getAction(`/cd/${chainId}/${chainAccountId}/contractmessage`,params );//普通链
export const databaseReq = (params) => postAction('/CMBaaSApi/OracleProvider/databaseReq',params );//DB测试
export const eosReq = (params) => postAction('/CMBaaSApi/OracleProvider/eosReq',params );//EOS测试
export const delProviderTemplate = (params) => postAction('/CMBaaSApi/OracleProvider/delProviderTemplate',params );//信源模板批量删除
export const getVersion = (contractId) => getAction(`/cd/contract/${contractId}`,null );//普通链



// 预言机模板api文档

export const addMachineTemp = (params) => postAction('/CMBaaSApi/OracleMachineTemp/addMachineTemp', params);//添加预言机模板
export const delMachineTemp = (params) => postAction('/CMBaaSApi/OracleMachineTemp/delMachineTemp', params);//删除预言机模板
export const getMachineTempDetails = (params) => postAction('/CMBaaSApi/OracleMachineTemp/getMachineTempDetails', params);//获取预言机模板详情
export const getMachineTempList = (params) => postAction('/CMBaaSApi/OracleMachineTemp/getMachineTempList', params);//获取预言机模板列表
export const updateMachineTemp = (params) => postAction('/CMBaaSApi/OracleMachineTemp/updateMachineTemp', params);//编辑预言机模板0
export const updateDescription = (params) => putAction('/CMBaaSApi/OracleMachineTemp/updateDescription', params);//编辑预言机模板1



// 预言机列表api文档

export const addOracleMachine = (params) => postAction('/CMBaaSApi/OracleMachine/addOracleMachine', params);//添加预言机
export const delOracleMachine = (params) => postAction('/CMBaaSApi/OracleMachine/delOracleMachine', params);//删除预言机
export const getOracleMachineDetails = (params) => postAction('/CMBaaSApi/OracleMachine/getOracleMachineDetails', params);//获取预言机详情
export const getOracleMachineList = (params) => postAction('/CMBaaSApi/OracleMachine/getOracleMachineList', params);//获取预言机列表
export const offlineOracleMachine = (params) => postAction('/CMBaaSApi/OracleMachine/offlineOracleMachine', params);//预言机启用，禁用
export const updateOracleMachine = (params) => postAction('/CMBaaSApi/OracleMachine/updateOracleMachine', params);//编辑预言机
export const getConsList = (params) => postAction('/CMBaaSApi/OracleMachine/getConsList', params);//消费者用户列表
export const getProList = (params) => postAction('/CMBaaSApi/OracleMachine/getProList', params);//信源用户列表
export const OracleMachineTest = (params) => getAction(`/CMBaaSApi/OracleMachine/test/${params}`,null );//预言机http测试



// 交易记录表api文档

export const getBusinessRecordDetails = (params) => postAction('/CMBaaSApi/OracleBusRec/getBusRecDetails', params);//获取交易记录表详情
export const getBusinessRecordList = (params) => postAction('/CMBaaSApi/OracleBusRec/getBusRecList', params);//获取交易记录表列表






// 请求记录表api文档

export const getReqRecDetails = (params) => postAction('/CMBaaSApi/OracleReqRec/getReqRecDetails', params);//获取交易记录表详情
export const getReqRecList = (params) => postAction('/CMBaaSApi/OracleReqRec/getReqRecList', params);//获取请求记录表列表
// 异常日志表api文档

export const getExceptionLogDetails = (params) => postAction('/CMBaaSApi/OracleExcepLog/getExceptionLogDetails', params);//获取异常日志表详情
export const getExceptionLogList = (params) => postAction('/CMBaaSApi/OracleExcepLog/getExceptionLogList', params);//获取异常日志表列表


//下拉列表

export const getHttpChainList = (params) => postAction('/CMBaaSApi/Universal/getHttpChainList', params);//获取第三方链列表接口
export const getHttpUserList = (params) => postAction('/CMBaaSApi/Universal/getHttpUserList', params);//获取第三方链上用户列表

export const getOracleNameList = (params) => getAction('/CMBaaSApi/OracleMachine/getOracleNameList',params );//HTTP测试
export const getFieldDef = (params) => getAction('/CMBaaSApi/OracleBusRec/getFieldDef',params );//获取字段的定义
export const getReqFieldDef = (params) => getAction('/CMBaaSApi/OracleReqRec/getFieldDef',params );//获取字段的定义

