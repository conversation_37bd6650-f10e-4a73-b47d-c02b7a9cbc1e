// base color
@font-family: "<PERSON>o",
sans-serif;
@default: #000;
@default-white: #FFF;
@color: #303133;
@bg-color: #EFF1F4;
@divider: #E5E5E5;

//border

@border: 1Px solid #E9EBEF;


// colors
@primary: #337DFF;
@primary-hover: #5392FF;
@primary-focus: #125DE1;

@primary-disabled-font:#BBBBBB;
@primary-disabled-border:#D9D9D9;
@primary-disabled-background: #F5F5F5;
@success-font: #10C038;
@error-font: #F04134;
@warning-font: #FE983D;

// sub button
@sub-font: #555;
@sub-focus-background:#E1ECFF;

// font
@font-title: 18px;
@tip-font-color: #BBB;
@selector-font-primary: rgba(51, 125, 255, 0.9);
@font20:20px;
@font1:16px;//一号字体
@font2:14px;//二号字体

// bview table component style
@table-header-cell-bg: rgba(233,235,239,0.5);
@table-header-cell-text: #031129;
@table-body-cell-hover-bg: #F5F8FF;
@table-body-cell-text: #333;
@table-cell-border-color: #E9EBEF;

// input
@input-height: 32px;
@input-radius:2px;
@input-disabled: #F5F5F5;
@input-border: #DCDFE6;

// tip
@tip-title-panel-height: 60px;

// order
@price-panel-height: 80px;
@padding-top: 20px;
@padding-bottom: 20px;

// scroll
@scroll-border-radius: 4px;
@scroll-color: #D8D8D8;
@scroll-width: 8px;

:export {
  orderTitleHeight: @tip-title-panel-height;
}
