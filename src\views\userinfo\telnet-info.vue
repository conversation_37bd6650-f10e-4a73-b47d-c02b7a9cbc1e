<template>
<div>
    <li><span class="title">租户情况</span></li>
    <li>我的租户：{{tenantName}}</li>
    <li style="padding-bottom:15px;">租户成员<span style="margin-left:5px;">(共<span style="font-weight:bold">{{total}}</span>位用户)</span></li>
    <edit-table-mul :columns="columns" v-model="telnetTableData" :key="transferKey" :height="getHeight" style="margin: 10px;"></edit-table-mul>
    <p style="text-align:center;padding-top:8px;padding-bottom:10px">
      <!-- <span style="font-size:12px;color:#000;margin-right:6px;">共{{total}}位用户</span> -->
      <span style="font-size:12px;color:#57a3f3;margin-left:2px;cursor:pointer;" @click="getMore" :style="getHidden">
        <img :src="imgUrl" style="cursor:pointer;" @click="getMore">更多
      </span>
    </p>
</div>
</template>
<script>
import { tenantList } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
export default {
  components: {
    EditTableMul
  },
  data () {
    return {
      imgUrl: require('@/assets/img/arrow.png'),
      transferKey: 0,
      tenantName: localStorage.getItem('tenantName') ? localStorage.getItem('tenantName') : '',
      columns: [
        { key: 'facePic',
          title: '头像',
          render: (h, params) => {
            return h('div', [
              h('Avatar', {
                props: { size: '40',
                  shape: 'circle',
                  icon: 'ios-person',
                  src: this.telnetTableData[params.index].facePic },
                on: {
                }
              }, '')
            ])
          }
        },
        { key: 'userLoginId', title: '组员名称' },
        { key: 'email', title: '邮箱' },
        { key: 'roleName', title: '角色' }
      ],
      total: 0,
      count: 0,
      telnetTableData: [],
      pagetotal: 0,
      pageParam: {
        pageIndex: 1,
        pageSize: 10
      },
      moreFlag: false,
      pages: 0
    }
  },
  computed: {
    getHidden () {
      if (this.count < this.total) {
        return ''
      } else {
        return 'display:none'
      }
    },
    getHeight: function (value) {
      if (this.telnetTableData.length === 0) {
        return 90
      } else if (this.telnetTableData.length > 10) {
        return 90 + 48 * 9
      }
    }
  },
  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    getTenentList (flag) {
      tenantList(this.pageParam).then(res => {
        if (res.code === '00000') {
          if (res.data && res.data.records) {
            if (!flag) {
              this.telnetTableData = res.data.records
            } else {
              this.telnetTableData.push.apply(this.telnetTableData, res.data.records)
            }
            // this.tenantName = res.data.records[0].tenantName
            this.total = res.data.total
            this.count = this.telnetTableData.length
            this.pages = res.data.pages
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getMore () {
      if (this.pageParam.pageIndex < this.pages) {
        this.pageParam.pageIndex += 1
        this.getTenentList(!this.moreFlag)
      }
    },
    initTelnetInfo () {
      this.pageParam = {
        pageIndex: 1,
        pageSize: 10
      }
      this.telnetTableData = []
      this.getTenentList(this.moreFlag)
    }
  },
  mounted () {
    // this.uploadList = this.$refs.upload.fileList
    // this.$Message.config({
    //   top: 250,
    //   duration: 2
    // })
    this.getTenentList(this.moreFlag)
  }
}
</script>
<style lang="less" scoped>
.click {
    cursor: pointer;
  }
ul,li{ list-style:none;margin-top:20px;}
    li {
    // .click;
    font-size:14px;
    padding:5px 10px;
    }
    .title{
      vertical-align: middle;
      display: table-cell;
      font-weight: bold;
      font-size:16px;
      margin-top: 0px;
      cursor: default;
    }
 .active {
        color: #3f7dff;
        //padding-bottom: 10px;
        border-right: solid #3f7dff 3px ;
        height:28px;
        font-weight: bold;
        z-index: 999;
        background-color: #eef4ff;
      }
.link{
  // width: 5px;
  height: 100%;
  border-left: solid #e4e4e6 2px;
  padding-left:9px;
  margin-left:-2px;
}
/deep/.ivu-form-item-label{
  font-size:14px !important;
}
/deep/.ivu-modal-header-inner{
  font-size:14px !important;
  font-weight: bold;
}
/deep/.ivu-input{
  font-size:14px !important;
}
.ivu-btn{
  font-size:14px;
}
/deep/.ivu-table-cell{
  font-size:14px !important;
}
/deep/.ivu-table:before{
  height: 0;
}
</style>
