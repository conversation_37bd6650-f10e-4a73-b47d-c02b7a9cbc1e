<template>
  <div class="management_index">
  <keep-alive v-if="currentTab==='node_management'">
    <Management  />
  </keep-alive>
  <keep-alive :exclude="excludeArr" v-else>
  <router-view />
  </keep-alive>
  </div>
</template>

<script>
import Management from './management.vue'
export default {
  name: 'management_index',
  components: {
    Management
  },
  data () {
    return {
      //
      excludeArr: ['management_detail', 'new_recor']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
