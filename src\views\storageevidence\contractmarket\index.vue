<template>
  <div class="contract_market">
    <keep-alive>
    <ShelvesContract v-if="currentTab==='contract_market'" />
  </keep-alive>
  <router-view v-if="currentTab!=='contract_market'"/>
  </div>
</template>

<script>
import ShelvesContract from './market-home.vue'
export default {
  name: 'contract_market',
  components: {
    ShelvesContract
  },
  data () {
    return {
      // excludeArr: ['tem_modify']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
