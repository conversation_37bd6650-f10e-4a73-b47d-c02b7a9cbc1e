<template>
  <div>
    <div style="padding: 10px 0 20px 0">
          <span>选择要共享的租户</span>
          <Input placeholder="请输入租户名称" class="input-search" style="width:250px;margin-left:305px;vertical-align:middle;" v-model="tenantName" @keyup.enter.native="searchTenantName">
            <Icon type="ios-search" slot="suffix" @click="searchTenantName"/>
          </Input>
        </div>
        <div style="width:100%;">

          <Table
            :columns="selectColums"
            :data="selectTableData"
            stripe
            ref="selection"
            :show-header="true"
            @on-select="onSelect"
            @on-select-all="onSelectAll"
            @on-select-cancel="onSelectCancel"
            @on-select-all-cancel="onSelectAllCancel" max-height="300"></Table>
          <Page
            :total="selectPageParam.pagetotal"
            :current.sync="selectPageParam.pageIndex"
            @on-change="noSelectPageChange"
            :page-size="selectPageParam.pageSize"
            :page-size-opts="[5,10,20,40,60,100]"
            show-total show-elevator show-sizer
            @on-page-size-change="noSelectPageSizeChange"
            style="text-align:right;padding-top:15px;"/>
        </div>
  </div>
</template>
<script>
import { shareableTenantList } from '@/api/data'
export default {
  name: 'Search-Table-Page',
  components: {
  },
  props: {
    // 以后扩展
    // selectColums: {
    //   type: Array,
    //   default: () => []
    // },
    // render: { // 调用的接口方法
    //   type: Function,
    //   default: () => {}
    // }
    contractAccountId: { // 共享的合约链账户
      type: String,
      default: ''
    },
    transProps: { // 共享的合约链账户
      type: Object,
      default: () => {}
    },
    flag: { // 是否进入组件重新加载
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      tenantName: '',
      selectColums: [
        {
          type: 'selection',
          width: 35,
          align: 'center'
        },
        { key: 'tenantName', title: '租户名称' },
        { key: 'tenantBrief', title: '租户描述', minWidth: 300, tooltip: true }
      ],
      selectPageParam: {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      },
      selectTableData: [],
      selectRecords: [],
      saveFindDatas: {} // 存储查询出来的数据
    }
  },
  watch: {
    flag: {
      handler (newVal, oldVal) {
        this.flag = newVal
        this.selectPageParam = {
          pagetotal: 0,
          pageSize: 5,
          pageIndex: 1
        }
        this.selectRecords = []
        this.saveFindDatas = {}
        this.tenantName = ''
        this.selectTableData = []
        if (this.flag) { // 打开时才能调用
          if (this.contractAccountId) {
            this.getChainTenantList(this.selectPageParam)
          }
        }
      },
      deep: true,
      immediate: false
    },
    contractAccountId: {
      handler (newVal, oldVal) {
        this.contractAccountId = newVal
        this.selectPageParam = {
          pagetotal: 0,
          pageSize: 5,
          pageIndex: 1
        }
        this.selectRecords = []
        this.tenantName = ''
        this.saveFindDatas = {}
        this.selectTableData = []
        if (this.contractAccountId) { this.getChainTenantList(this.selectPageParam) }
      },
      deep: true,
      immediate: false
    }
  },
  mounted () {
    this.selectTableData = []
    // this.getChainTenantList(this.selectPageParam)
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    searchTenantName () {
      this.getChainTenantList(this.selectPageParam, 1)
    },
    noSelectPageChange (index) {
      this.selectPageParam.pageIndex = index
      this.getChainTenantList(this.selectPageParam, 1)
    },
    noSelectPageSizeChange (index) {
      this.selectPageParam.pageSize = index
      this.getChainTenantList(this.selectPageParam, 1)
    },
    onSelectAll (selection) {
      selection.forEach(item => {
        this.selectRecords.push(item.tenantId)
      })
      this.selectRecords = Array.from(new Set(this.selectRecords))
      this.emitSelection()
    },
    onSelectAllCancel (selection) {
      this.selectTableData.forEach(item => {
        this.selectRecords.forEach((e, index) => {
          if (item.tenantId === e) {
            this.selectRecords.splice(index, 1)
            this.emitSelection()
          }
        })
      })
    },
    emitSelection () {
      let transDatasParent = Object.values(this.saveFindDatas)
      let transDatas = transDatasParent.reduce((item, val) => item.concat(val), [])
      let data = transDatas.filter(val => this.selectRecords.includes(val.tenantId))
      this.$emit('getSelectionData', data)
    },
    getChainTenantList (pageParam, i = 0) {
      if (!this.contractAccountId) {
        this.msgInfo('warning', '请先选择共享的合约链账户！', true)
        return
      }
      const params = {
        tenantName: this.tenantName,
        chainId: this.transProps.chainId,
        contractAccountId: this.transProps.contractAccountId,
        uploadVersion: this.transProps.contractVersion || this.transProps.uploadVersion,
        pageParam: pageParam
      }
      shareableTenantList(params).then(res => {
      // getChainTenant(pageParam, this.contractAccountId, this.visibleState[i], this.tenantName).then(res => {
        if (res.code === '00000') {
          if (res.data && i === 0) {
            this.selectTableData = res.data.records ? res.data.records : []
            this.selectPageParam = {
              pagetotal: res.data.total,
              pageSize: res.data.size,
              pageIndex: res.data.current
            }
          } else if (res.data && i === 1) {
            this.selectTableData = res.data.records ? res.data.records : []
            this.selectPageParam = {
              pagetotal: res.data.total,
              pageSize: res.data.size,
              pageIndex: res.data.current
            }
            this.handleTableChecked(this.selectTableData)
          }
          this.saveFindDatas[this.selectPageParam.pageIndex] = this.selectTableData
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    onSelect (selection, row) {
      this.selectRecords.push(row.tenantId)
      this.emitSelection()
      this.selectTableData.forEach(item => {
        if (item.tenantId === row.tenantId) {
          item['_checked'] = true
        }
      })
    },
    onSelectCancel (selection, row) {
      this.selectRecords.forEach((item, index) => {
        if (row.tenantId === item) {
          this.selectRecords.splice(index, 1)
          this.emitSelection()
        }
      })
    },
    handleTableChecked (datas) {
      this.selectRecords.forEach(item => { // 判断本页数据状态
        datas.forEach(e => {
          if (item === e.tenantId) {
            e['_checked'] = true
          }
        })
      })
    }
  }
}
</script>
