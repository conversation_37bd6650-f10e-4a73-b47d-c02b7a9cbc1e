<template>
  <div class="user-log">
    <p class="title" style="">
      <span class="title-1">{{userLoginId}}的日志列表</span>
      <span class="search-1">
      <!-- <Icon type="ios-medical" style="color:red;margin-right:-4px;"/> -->
      <DatePicker :value="searchData.timePeriod" v-model="searchData.timePeriod" :options="options" format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="开始日期 - 结束日期" style="width: 200px;margin:0 10px;" @on-change="search" @keyup.enter.native="search" @on-clear="clear"></DatePicker>
      <Input placeholder="请输入操作类型/响应码" style="width:250px;vertical-align:baseline;" v-model="searchData.query" suffix="ios-search" @keyup.enter.native="search">
        <Icon type="ios-search" slot="suffix" @click="search"/>
      </Input>
      </span>
    </p>
    <edit-table-mul :columns="columns" v-model="logData" :key="transferKey" style="margin:0 10px;"></edit-table-mul>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;margin:10px 0;"/>
    <Modal
    v-model="jsonModal"
    width="800"
    :title="'查看json'"
    footer-hide
    scrollable
    :mask-closable="false">
      <Row style="margin:10px 10px 10px 30px;">
        <Col span="11">
          <p class="json-title">请求入参json</p>
          <div class="json-content">
            <div v-if="requestInfo">
              <json-viewer :value="requestInfo" :copyable="copyable"></json-viewer>
            </div>
          </div>
        </Col>
        <Col span="11" offset="1">
          <p class="json-title">响应信息json</p>
          <div class="json-content" >
            <div v-if="responseInfo">
              <json-viewer :value="responseInfo" :copyable="copyable"></json-viewer>
            </div>
          </div>
        </Col>
      </Row>
    </Modal>
  </div>
</template>

<script>
import EditTableMul from '_c/edit-table-mul'
import JsonViewer from 'vue-json-viewer'
import { getLogList } from '@/api/data'
export default {
  name: 'user_log',
  components: {
    EditTableMul,
    JsonViewer
  },
  data () {
    return {
      copyable: { copyText: '复制', copiedText: '已复制' },
      userLoginId: '',
      jsonModal: false,
      searchData: {
        timePeriod: [],
        query: '',
        startTime: '',
        endTime: ''
      },
      options: {
        disabledDate (date) {
          return (date && date.valueOf() > Date.now())
        }
      },
      transferKey: 0,
      logData: [],
      typeList: [
        { key: 'patternUrl', value: 'URL（系统格式）' },
        { key: 'requestUrl', value: 'URL（真实）' }
      ],
      requestInfo: null,
      responseInfo: null,
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        { key: 'operateTypeDesc', title: '操作类型' },
        { key: 'clientIp', title: '请求IP', tooltip: true },
        { key: 'userAgent', title: '浏览器信息', tooltip: true },
        { key: 'patternUrl',
          title: 'URL（系统格式）',
          tooltip: true,
          minWidth: 30,
          renderHeader: (h, params) => {
            return this.renderSelectTableColumn(h, params)
          }
        },
        { key: 'resultCode', title: '响应码' },
        { key: 'requestTime', title: '请求时间' },
        { key: 'action',
          title: '请求/响应json',
          minWidth: 30,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    this.getJson(params.index)
                  }
                }
              }, '查看')
            ])
          }
        }
      ]
    }
  },
  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getUserLogList()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getUserLogList()
    },
    clear () {
      this.searchData.startTime = ''
      this.searchData.endTime = ''
    },
    search () {
      if (this.searchData.timePeriod[0] && this.searchData.timePeriod[1]) {
        var time1 = this.searchData.timePeriod[0]
        var time2 = this.searchData.timePeriod[1]
        this.searchData.startTime = time1 && this.transferDate(time1)
        this.searchData.endTime = time2 && this.transferDate(time2)
      } else {
        this.searchData.startTime = ''
        this.searchData.endTime = ''
      }
      this.getUserLogList()
    },
    reset () {
      this.searchData.endTime = this.transferDate(new Date())
      let lw = new Date(new Date() - 1000 * 60 * 60 * 24 * 1)
      this.searchData.startTime = this.transferDate(lw)
      this.searchData.timePeriod = [this.searchData.startTime, this.searchData.endTime]
    },
    transferDate (date) {
      if (date) {
        let lastY = date.getFullYear()
        let lastM = date.getMonth() + 1
        let lastD = date.getDate()
        return lastY + '-' + (lastM < 10 ? '0' + lastM : lastM) + '-' + (lastD < 10 ? '0' + lastD : lastD)
      } else {
        return ''
      }
    },
    getJson (index) {
      this.requestInfo = this.logData[index].requestInfo ? this.logData[index].requestInfo : null
      this.responseInfo = this.logData[index].responseInfo ? JSON.parse(this.logData[index].responseInfo) : null
      this.jsonModal = true
    },
    getKey (value) {
      var key = ''
      this.typeList.forEach(e => {
        if (e.value === value) {
          key = e.key
        }
      })
      return key
    },
    getUserLogList () {
      getLogList(this.userLoginId, this.searchData, this.tablePageParam).then(res => {
        if (res.code !== '00000') {
          this.msgInfo('warning', res.message, true)
        } else {
          // console.log('res.data:', res.data)
          if (res.data) {
            this.logData = res.data.records ? res.data.records : []
            this.tablePageParam = {
              pagetotal: res.data.total,
              pageSize: res.data.size,
              pageIndex: res.data.current
            }
            ++this.transferKey
          }
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    renderSelectTableColumn (h, params) {
      let edit
      let options = this.typeList.map(v => {
        return h('Option', {
          props: {
            value: v.value
          }
        }, v.value)
      })
      edit = [h('Select', {
        props: {
          value: params.column.title,
          placement: 'bottom-end',
          transfer: true
        },
        class: { 'select-style': true },
        style: { marginLeft: '-8px', marginRight: '8px' },
        on: {
          input: (val) => {
            params.column.title = val
            if (this.getKey(val)) {
              params.column.key = this.getKey(val)
            } else {
              this.msgInfo('error', val + '列不存在,请检查！', true)
            }
          }
        }
      }, options)]
      return h('div', [edit])
    }
  },
  mounted () {
    this.userLoginId = this.$route.params.userLoginId ? this.$route.params.userLoginId : ''
    if (this.userLoginId) {
      this.reset()
      this.getUserLogList(this.userLoginId)
    } 
    // this.$Message.config({
    //   top: 250,
    //   duration: 2
    // })
  },
  beforeRouteEnter (to, from, next) {
    if (from.name) {
      next()
    } else {
      next('/user_admin')
    }
  }
}
</script>

<style lang="less" scoped>
.user-log{
  .title{
    margin:10px 10px 25px 0px;
  }
  .title-1{
    font-size: 16px;
    font-weight: bold;
    margin:0 10px;
  }
  .search-1{
    float:right;
  }
}
.json-title{
  font-weight: bold;
  margin-bottom: 10px;
}
.json-content{
  height:500px;
  overflow-y:auto;
  overflow-x:hidden;
  border: 1px solid rgb(211, 211, 214);
}
.json-content::-webkit-scrollbar{
    width : 5px;  /*高宽分别对应横竖滚动条的尺寸*/
    min-height: 1px;
}
.json-content::-webkit-scrollbar-thumb{
    border-radius   : 10px;
    background-color: rgb(135, 158, 235);
}
/deep/.select-style >.ivu-select-selection{
  border: none;
  background-color: transparent;
  box-shadow: none;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61,115,239,.8);
  color: #fff!important;
}
/deep/.ivu-btn-text:active{
  background-color: #3D73EF;
}
</style>
