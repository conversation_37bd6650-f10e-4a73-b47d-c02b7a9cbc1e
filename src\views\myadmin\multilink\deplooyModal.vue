<template>
  <div>
    <Modal :draggable="true" v-model="modal" width="700" :title="formItem.alertTitle" :z-index="1000" sticky :mask-closable="false" @on-cancel="cancel">
      <Form ref="formItem" :rules="formItemRule" :model="formItem" :label-width="150">
        <FormItem label="链名称：" prop="chainName">
          <Input placeholder="请输入" style="width: 500px; vertical-align: baseline" v-model="formItem.chainName" />
        </FormItem>
        <FormItem label="开发架构类型：" prop="engineTypeKey">
          <Tooltip max-width="200" content="CMEOS：EOS移动增强版，支持C++和JavaScript智能合约。" style="margin-left: -18px">
            <Icon type="md-help-circle" style="font-size: 16px" />
          </Tooltip>
          <Select v-model="formItem.engineTypeKey" placeholder="选择开发架构类型" style="width: 200px" :disabled="formItem.alertTitle=='修改管控链'">
            <Option value="CMEOS">CMEOS</Option>
          </Select>
        </FormItem>
        <FormItem label="链版本：" prop="chainVersion">
          <Select v-model="formItem.chainVersion" style="width:200px" placeholder="请选择链版本" :disabled="formItem.alertTitle=='修改管控链'">
            <Option v-for="item in deplooyList" :value="item.key" :key="item.key">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem label="初始化公钥：" prop="publickey" v-if="formItem.alertTitle=='一键部署'">
          <Input placeholder="请输入" style="width: 500px; vertical-align: baseline" v-model="formItem.publickey" maxlength="53" show-word-limit />
        </FormItem>
        <FormItem label="初始化私钥：" prop="privatekey" v-if="formItem.alertTitle=='一键部署'">
          <Input placeholder="请输入" style="width: 500px; vertical-align: baseline" v-model="formItem.privatekey" maxlength="51" show-word-limit />
        </FormItem>
        <FormItem label="合约升级：" prop="contractup">
          <Select v-model="formItem.contractup" style="width:200px" placeholder="请选择是否升级合约">
            <Option v-for="item in contractupList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="链所有权：" prop="ownership">
          <Select v-model="formItem.ownership" style="width: 200px" :disabled="formItem.alertTitle=='修改管控链'">
            <Option value="CONTROL_CHAIN">管控链</Option>
          </Select>
        </FormItem>
        <FormItem label="主子链：" prop="subChain">
          <Select v-model="formItem.subChain" placeholder="选择主子链" style="width: 200px" @on-select="onSelect">
            <Option v-for="item in subChainList" :value="item.value" :key="item.value">{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="链归属公司：" prop="company" v-if="subtrue">
          <Select v-model="formItem.company" placeholder="请选择链归属公司" style="width: 200px" filterable>
            <Option v-for="item in selectList" :value="item.id" :key="item.id">{{item.companyName}}</Option>
          </Select>
        </FormItem>
        <FormItem label="历史数据导出方式：" prop="expType">
          <Select v-model="formItem.expType" placeholder="选择历史数据导出方式" style="width: 200px">
            <Option v-for="item in expTypeList" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem label="审核列表：" prop="" class="mandatory">
          <Tooltip max-width="200" content="可选择租户需要人工审核的操作，为空系统自动审核" style="margin-left: -18px">
            <Icon type="md-help-circle" style="font-size: 16px" />
          </Tooltip>
          <Select v-model="formItem.needAudited" multiple placeholder="选择审核列表(多选)" style="width: 200px">
            <Option v-for="item in auditList" :value="item.key" :key="item.key">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem v-if="formItem.status" label="状态：" prop="status">
          <Select v-model="formItem.status" placeholder="选择运行状态" style="width: 200px; margin: 0 5px">
            <Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem label="主要业务描述：">
          <Input type="textarea" style="width: 500px" :maxlength="128" show-word-limit v-model="formItem.chainBrief" :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请填写链的主要业务描述" />
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="primary" ghost @click="cancel('formItem')">取消</Button>
        <Button type="primary" @click="ok('formItem')" :loading="loadingbtn">
          {{formItem.alertTitle=='一键部署'?'一键部署':'确定'}}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { engineTypeList, statusList, ownershipList, expTypeList, deplooyList } from './typeList'
import { addBlockChain } from '@/api/arrange'
import { searchKey } from './tool'
import { tenantcompanyList } from '@/api/contract'
import { reviseMultiChain } from '@/api/data'
export default {
  data () {
    return {
      loadingbtn: false,
      subtrue: false,
      modal: false,
      formItem: {
        alertTitle: '一键部署',
        chainId: '',
        chainName: '',
        // eosChainId: '',
        chainBrief: '',
        ownership: '',
        needAudited: [],
        status: '',
        engineTypeKey: '',
        ownershipKey: '',
        needAuditedKey: '',
        statusKey: '',
        expType: '',
        subChain: '',
        company: '',
        contractup: '',
        chainVersion: '', // 链版本
        publickey: '', // 公钥
        privatekey: ''// 私钥
      },
      formItemRule: {
        chainName: [{ required: true, message: '不能为空', trigger: 'blur' }, { max: 16, message: '不能多于16位', trigger: 'blur' }],
        // eosChainId: [
        //   { required: true, message: '不能为空', trigger: 'blur' },
        //   {
        //     type: 'string',
        //     pattern: /^[a-z0-9]{64}$/,
        //     message: '格式有误,长度必须为64位,a-z0-9',
        //     trigger: 'blur'
        //   }
        // ],
        engineTypeKey: [
          { required: true, message: '请选择一项', trigger: 'change' }
        ],
        ownership: [
          { required: true, message: '请选择一项', trigger: 'change' }
        ],
        chainBrief: [
          {
            required: true,
            message: '请填写链的主要业务描述',
            trigger: 'blur'
          }
        ],
        company: [{ required: true, message: '请选择链归属公司', trigger: 'change', type: 'number' }],
        status: [{ required: true, message: '请选择一项', trigger: 'change' }],
        expType: [{ required: true, message: '请选择一项', trigger: 'change' }],
        subChain: [
          { required: true, message: '请选择主子链', trigger: 'change' }
        ],
        contractup: [
          { required: true, message: '请选择是否升级合约', trigger: 'change' }
        ],
        publickey: [{ required: true, message: '不能为空', trigger: 'blur' }],
        privatekey: [{ required: true, message: '不能为空', trigger: 'blur' }],
        chainVersion: [{ required: true, message: '请选择一项', trigger: 'change' }]
      },
      deplooyList: [],
      statusList: [],
      auditList: [],
      ownershipList: [],
      engineTypeList: [],
      expTypeList: [],
      selectList: [], // 归属公司
      subChainList: [{ value: 'MAIN_CHAIN', name: '集团主链' }, { value: 'SUB_CHAIN', name: '省子链' }], // 主子链
      contractupList: [{
        value: '1',
        label: '是'
      },
      {
        value: '0',
        label: '否'
      }]

    }
  },
  computed: {
    auditLists () {
      return this.$store.state.dict.options.AUDIT_TYPE || []
    }
  },
  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    init () {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.formItem = {
        alertTitle: '一键部署',
        chainId: '',
        chainName: '',
        eosChainId: '',
        chainBrief: '',
        ownership: '',
        needAudited: [],
        status: '',
        engineTypeKey: '',
        ownershipKey: '',
        needAuditedKey: '',
        statusKey: '',
        expType: '',
        subChain: '',
        company: '',
        contractup: '',
        chainVersion: '', // 链版本
        publickey: '', // 公钥
        privatekey: ''// 私钥

      }
    },
    clickmodal (msg, info) {
      console.log(msg, info)
      this.modal = true
      if (msg === '修改管控链') {
        this.formItem = {
          alertTitle: '修改管控链',
          chainName: info.chainName, // 链名称
          eosChainId: info.eosChainId,
          chainBrief: info.chainBrief, // 链描述
          chainVersion: info.isGm, // 链版本
          contractup: info.isUpgradeContract === '是' ? '1' : '0', // 合约升级
          engineTypeKey: info.engineTypeKey, // 开发架构类型
          ownership: 'CONTROL_CHAIN', // 所有权
          // ownershipKey: 'CONTROL_CHAIN',
          needAudited: info.auditList.map((val) => val.auditKey), // 审核列表
          status: info.status,
          expType: info.expType, // 导出方式
          subChain: info.chainSource, // 主子链
          company: info.companyId, // 公司id
          chainId: info.chainId

        }
        this.subtrue = info.chainSource !== 'MAIN_CHAIN'
      } else {
        this.init()
      }
      this.attribution()
      this.$store
        .dispatch('getOptions', 'AUDIT_TYPE')
        .then((result) => {
          if (Object.prototype.toString.call(result) === '[object Boolean]') {
            this.auditList = this.auditLists
            // console.log(this.auditList)
          } else {
            this.auditList = result
          }
        })
        .catch((err) => {
          // console.log(err, 'err')
          this.msgInfo('error', err.message, true)
        })
    },

    cancel (name) {
      this.init()
      this.modal = false
      this.subtrue = false
      this.loadingbtn = false
    },
    ok (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.formItem.subChain === 'SUB_CHAIN' && this.formItem.company === '') {
            this.msgInfo('error', '请选择链归属公司', true)
          } else {
            // this.formItem.ownershipKey = searchKey(
            //   this.formItem.ownership,
            //   this.ownershipList
            // )
            this.formItem.auditList = this.formItem.needAudited || []
            let item = {
              chainName: this.formItem.chainName, // 链名称
              engineTypeKey: 'CMEOS', // 开发架构类型
              isGm: this.formItem.chainVersion,
              //= == '国密版' ? 1 : 0, // 链版本  国密:0是 1否
              isUpgradeContract: this.formItem.contractup, // 合约升级
              // ownership: this.formItem.ownership, // 链所有权
              ownershipKey: this.formItem.ownership, // 所有权
              chainSource: this.formItem.subChain, // 主子链
              companyId: this.formItem.company, // 公司id
              expType: searchKey(this.formItem.expType, this.expTypeList), // 导出方式
              auditList: this.formItem.auditList, // 审核列表
              chainBrief: this.formItem.chainBrief, // 链描述
              initializationStatus: 0 // 是否初始化

              // initKey: this.formItem.initialize, // 初始化私钥

            }
            this.loadingbtn = true
            if (this.formItem.alertTitle === '一键部署') {
              addBlockChain(item).then((res) => {
                if (res.code === '00000') {
                  this.msgInfo('success', res.message, true)
                  this.init()
                  setTimeout(() => {
                    // console.log(new Date().getTime())
                    this.$parent.getTableData()
                  }, 3000)
                  this.modal = false
                  this.loadingbtn = false
                } else {
                  // console.log('tipInfo-error:', res.message)
                  this.msgInfo('error', res.message, true)
                  this.loadingbtn = false
                }
              })
            } else {
              this.formItem.statusKey = searchKey(
                this.formItem.status,
                this.statusList
              )
              let item = {
                auditList: this.formItem.auditList,
                chainBrief: this.formItem.chainBrief,
                chainId: this.formItem.chainId,
                chainName: this.formItem.chainName,
                engineTypeKey: 'CMEOS',
                eosChainId: this.formItem.eosChainId,
                expType: searchKey(this.formItem.expType, this.expTypeList),
                ownershipKey: 'CONTROL_CHAIN', // this.formItem.ownershipKey,
                statusKey: this.formItem.statusKey,
                chainSource: this.formItem.subChain, // 主子链
                companyId: this.formItem.company, // 公司id
                isUpgradeContract: this.formItem.contractup
              }
              reviseMultiChain(item).then((res) => {
                if (res.code === '00000') {
                  this.msgInfo('success', res.message, true)
                  this.init()
                  this.$parent.getTableData()
                  this.modal = false
                  this.loadingbtn = false
                } else {
                  // console.log('tipInfo-error:', res.message)
                  this.msgInfo('error', res.message, true)
                  this.loadingbtn = false
                }
              })
            }
          }
        } else {
          this.msgInfo('error', '存在字段校验不符合规范，请检查！', true)
        }
      })
    },
    onSelect (value) {
      if (value.value === 'SUB_CHAIN') {
        this.subtrue = true
      } else if (value.value === 'MAIN_CHAIN') {
        this.subtrue = false
        this.formItem.company = ''
      }
    },
    // 归属公司
    attribution () {
      let listcompany = {
        companyName: ''
      }
      tenantcompanyList(listcompany).then(res => {
        if (res.code === '00000') {
          this.selectList = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    }

  },
  created () {
    this.deplooyList = deplooyList
    this.engineTypeList = engineTypeList
    this.statusList = statusList
    this.ownershipList = ownershipList
    this.expTypeList = expTypeList
  }
}
</script>

<style lang="less" scoped>
.mandatory {
  /deep/.ivu-form-item-label::before {
    content: " ";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    // font-family: SimSun;
    // font-size: 14px;
    // color: #ed4014;
  }
}
</style>
