<template>
  <div class="pieBox">
    <div ref="pie" class="pie" :style="{width:pieWidth,height: pieHeight}" v-if="type==1"></div>
    <div ref="pie2" class="pie" :style="{width:pieWidth,height: pieHeight}" v-if="type==2"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
const option = {
  title: {
    text: '',
    textStyle: { color: '#3c4858', fontSize: 14 },
    left: 'left'
  },
  color: ['#57a3f3', '#9fe6b8', '#808695', '#efd306', '#ff9f7f', '#9fe6b8', '#52C7AA', '#8378ea'],
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',
    right: '12vw',
    align: 'left',
    itemWidth: 10,
    itemHeight: 10,
    type: 'scroll',
    y: 'center',
    icon: 'circle',
    textStyle: { fontSize: '60%', lineHeight: 10 }
  },
  series: [
    {
      type: 'pie',
      radius: '60%',
      selectedMode: 'single',
      selectedOffset: 15,
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      itemStyle: {
        normal: {
          shadowColor: 'rgba(0,0,0,0.4)',
          shadowBlur: 10,
          label: {
            show: true,
            textStyle: { color: '#3c4858', fontSize: '80%', lineHeight: 14 },
            formatter: function (val) {
              return val.name + '\n' + val.value
            }
          },
          labelLine: {
            show: true
          }
        },
        emphasis: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
          textColor: '#000'
        }
      }
    }
  ],
  animation: true,
  animationDelay: 100,
  animationThreshold: 250,
  animationDuration: 1000,
  animationDurationUpdate: 1000,
  animationEasing: 'ExponentialOut',
  // animationEasingUpdate: 'ExponentialOut',
  hoverLayerThreshold: 2000,
  hoverAnimationDuration: 2000

}
export default {
  name: 'pie',
  props: {
    pieData: {
      type: Array,
      default () {
        return []
      }
    },
    pieHeight: {
      type: String,
      default: '360px'
    },
    pieWidth: {
      type: String,
      default: '500px'
    },
    pieTitle: {
      type: String,
      default: ''
    },
    type: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      clickName: ''
    }
  },
  methods: {
    initPieData () {
      option.title.text = this.pieTitle
      option.series[0].data = this.pieData
      this.mychart.on('click', params => {
        if (params.data.nameList) {
          this.$emit('hoverEvent', params.data)
        } else {
          if (params.data.selected && params.data.name === this.clickName) {
            params.data.selected = false
          } else {
            params.data.selected = true
          }
          this.$emit('hoverEvent', params.data)
        }
        this.clickName = params.data.name
      })
      this.mychart.setOption(option, true)
      window.addEventListener('resize', () => {
        this.mychart.resize()
      })
    },
    transferData (val) {
      for (var item in this.pieData) {
        if (this.pieData[item].name === val.name) {
          this.pieData[item].selected = true
        }
      }
    }
  },
  mounted () {
    if (this.type === 1) {
      this.mychart = echarts.init(this.$refs.pie)
    } else {
      this.mychart = echarts.init(this.$refs.pie2)
    }
    this.mychart.clear()
    this.mychart.off('click')
    this.initPieData()
  },
  watch: {
    pieData: {
      handler (newVal, oldVal) {
        this.pieData = newVal
        this.mychart.off('click')
        this.initPieData()
      },
      deep: true,
      immediate: false
    }
  }
}
</script>
<style lang="less" scoped>

</style>
