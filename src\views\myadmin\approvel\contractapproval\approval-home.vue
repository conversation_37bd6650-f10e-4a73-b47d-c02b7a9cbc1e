<template>
  <div class="market-home">
    <keep-alive>
      <div class="market-home-item">
        <!-- <div class="contract-title">
          <b>智能合约</b>
          <Button icon="ios-arrow-forward" size="small" @click="isContractFlag=false">合约广场</Button>
        </div> -->
        <!-- <Button class="right-button" icon="ios-arrow-forward" size="small" @click="changeFlag(false)">合约广场</Button> -->
        <Tabs value="name1" @on-click="clickTab">
          <TabPane label="待审批" name="name1"   >
            <PendingApproval/>
          </TabPane>
          <TabPane label="已审批" name="name2" >
            <HasApproval />
          </TabPane>
        </Tabs>
      </div>
    </keep-alive>
  </div>
</template>
<script>
import PendingApproval from './pending-approval.vue'
import HasApproval from './has-approval.vue'
import { PendingList } from '@/api/data'
export default {
  // name: 'has-approval',
  components: {
    PendingApproval,
    HasApproval
  },
  data () {
    return {
      tablePageParam: {
        pageSize: 10,
        pageIndex: 1
      },
      approvaltype: 'approved',
      inputvalue: ''
      // name: ''
      // name: this.$route.name.tbs || 'name1'
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    clickTab (e) {
      // console.log(e)
      if (e === 'name1') {
        let pendingList = {
          contractBagName: this.inputvalue,
          pageParam: this.tablePageParam
        }
        PendingList(pendingList).then(res => {
          // console.log(res, '待审批')
        })
      } else if (e === 'name2') {
        let pendingList = {
          approvalType: this.approvaltype,
          contractBagName: this.inputvalue,
          pageParam: this.tablePageParam
        }
        PendingList(pendingList).then(res => {
          // console.log(res, '已审批')
        })
      }
    }

  },
  mounted () {

  }
}
</script>

<style lang="less" scoped>
.market-home{
  .market-home-item{
    position: relative;
    // .contract-title{
    //   display: flex;
    //   align-items: center;
    //   b{
    //     margin-right:10px;
    //   }
    // }
  }
 //
 .right-button{
   position: absolute;
   right: 16px;
   top:4px;
   z-index: 10;
 }
}
</style>
