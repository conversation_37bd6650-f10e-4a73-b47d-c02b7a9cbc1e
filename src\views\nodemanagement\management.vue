<template>

  <div>
    <div class="cz_header">
      <div class="cz_ss">
        <!-- 所属用户 -->
        <div class="ss_name">
          <!-- <Select v-model="model3" style="width: 100px;margin-right:5px;" placeholder="所属用户">
            <Option
              v-for="item in chainName"
              :value="item.value"
              :key="item.value"
              @click.native="click_value(item.value)"
              >{{ item.label }}</Option
            >
          </Select>
          <Input placeholder="输入信息" v-model="search_value"></Input> -->
           <Input placeholder="输入信息" v-model="search_value">
            <template #prepend>
              <Select v-model="model3" style="width: 100px;margin-right:5px;" placeholder="所属用户">
               <Option
                v-for="item in chainName"
                :value="item.value"
                :key="item.value"
                @click.native="click_value(item.value)"
              >{{ item.label }}</Option
            >
          </Select>
            </template>
        </Input>
        </div>
        <!-- IP -->
        <div class="net_ip">
          <!-- <Select v-model="model2" style="width: 100px;margin-right:5px;" placeholder="内网IP">
            <Option
              v-for="item in networkIP"
              :value="item.value"
              :key="item.value"
              @click.native="click_value2(item.value)"
              >{{ item.label }}</Option
            >
          </Select>
          <Input placeholder="输入信息" v-model="search_valueip" /> -->
          <Input placeholder="输入信息" v-model="search_valueip">
            <template #prepend>
              <Select v-model="model2" style="width: 100px;margin-right:5px;" placeholder="内网IP">
            <Option
              v-for="item in networkIP"
              :value="item.value"
              :key="item.value"
              @click.native="click_value2(item.value)"
              >{{ item.label }}</Option
            >
          </Select>
            </template>
        </Input>
        </div>
        <!-- 所属区块链网络 -->
        <div class="net_work">
          <Input
            placeholder="输入所属区块链网络"
            v-model="chainetwork"
            class="auto"
          />
        </div>
        <!-- 按钮 -->
        <div class="btn">
          <Button
            type="primary"
            icon="ios-search"
            style="margin: 0 10px"
            @click.native="serch_btn"
            >查询</Button
          >
          <Button type="primary" icon="md-sync" ghost @click="reset"
            >重置</Button
          >
        </div>
      </div>
    </div>
    <!-- table -->
    <div class="cz_table">
      <edit-table-mul
        :columns="historyColumns"
        v-model="historyData"
      ></edit-table-mul>
      <Page
        :total="PageParam.pagetotal"
        :page-size="PageParam.pageSize"
        :current.sync="PageParam.pageIndex"
        :page-size-opts="[10, 20, 40, 60, 100]"
        show-sizer
        show-total
        show-elevator
        class="paging"
        :key="transferKey"
        @on-change="changepage"
        style="text-align: right"
        @on-page-size-change="pageSizeChange"
      ></Page>
    </div>

    <!-- 弹框 -->
    <Modal v-model="record" title="所属用户" width="65%">
      <edit-table-mul :columns="columns1" v-model="data1"></edit-table-mul>
    </Modal>
  </div>
</template>
<script>
import { nodeManagement, Belonginguser } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
export default {
  name: 'node_management',
  components: {
    EditTableMul
  },
  data () {
    return {
      ajaxHistoryData: [],
      // 每页显示多少条
      transferKey: 0,
      record: false,

      columns1: [
        {
          title: '姓名',
          key: 'realName',
          tooltip: true
        },
        {
          title: '账号',
          key: 'userLoginId'
        },
        {
          title: '注册邮箱',
          key: 'email',
          tooltip: true
        },
        {
          title: '组织',
          key: 'organization',
          width: 180
        },
        {
          title: '手机号',
          key: 'phoneNumber'
        },
        {
          title: '角色',
          key: 'roleName'
        }
      ],
      data1: [],
      historyColumns: [
        {
          title: '序号',
          type: 'index',
          width: 80
        },
        {
          title: '节点名称',
          key: 'nodeName',
          tooltip: true
        },
        {
          title: '内网IP',
          key: 'networkAddress',
          width: 140
        },
        {
          title: '外网IP',
          key: 'nodeAddress',
          width: 140
        },
        {
          title: '是否在线',
          key: 'online'
        },
        {
          title: '所属用户',
          key: 'userLoginId',
          tooltip: true,
          render: (h, params) => {
            return h('div', [
              h(
                'a',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF'
                    // border: "1px solid #3D73EF"
                  },
                  on: {
                    click: () => {
                      // this.$router.push("management_detail");
                      this.sur_records(params.row.userLoginId)
                    }
                  }
                },
                params.row.userLoginId
              )
            ])
          }
        },
        {
          title: '所属区块链网络',
          key: 'chainName',
          width: 140,
          tooltip: true
        },
        {
          title: '角色',
          key: 'roleStatus'
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    size: 'small'
                  },
                  style: {
                    marginRight: '5px', color: '#3D73EF', border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      // this.$router.push("management_detail");
                      this.surDetails(params.index)
                    }
                  }
                },
                '详情'
              ),
              h(
                'Button',
                {
                  props: {
                    size: 'small'
                  },
                  style: {
                    marginRight: '5px', color: '#3D73EF', border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      // this.$router.push("/new_recor");
                      this.recordDetails(params.index)
                    }
                  }
                },
                '交易'
              )
            ])
          }
        }
      ],
      historyData: [],
      model3: '所属用户',
      model2: '内网IP',
      model3Count: null,

      // 输入框所属用户
      search_mark: '',
      // 输入框节点名称
      search_name: '',
      // 内网IP
      intranet_ip: '',
      // 外网IP
      extranet_ip: '',
      chainetwork: '',
      network: '',
      // 下拉框
      lable: '所属用户',
      iplable: '内网IP',
      search_value: '',
      search_valueip: '',
      // 分页
      PageParam: {
        pageIndex: 1,
        pageSize: 10,
        pagetotal: 0
      },
      // 下拉框
      chainName: [
        {
          value: '所属用户',
          label: '所属用户'
        },
        {
          value: '节点名称',
          label: '节点名称'
        }
      ],
      networkIP: [
        {
          value: '内网IP',
          label: '内网IP'
        },
        {
          value: '外网IP',
          label: '外网IP'
        }
      ],
      blockChain: [
        {
          value: '所属区块链网络',
          label: '所属区块链网络'
        }
      ]
    }
  },
  methods: {
    changepage (index) {
      // 改变页码时触发
      this.PageParam.pageIndex = index
      this.getTablist() // 获取表格列表
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      // 当前页条数
      this.PageParam.pageSize = size
      this.getTablist() // 获取表格列表
    },
    sur_records (e) {
      this.record = true
      Belonginguser(e).then(res => {
        this.data1 = [res.data]
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    ok () {
      this.$Message.info('点击了确定')
    },
    cancel () {
      this.$Message.info('点击了取消')
    },
    // 详情
    surDetails (index) {
      this.$router.push({
        name: 'management_detail',
        params: {
          managementId: index,
          content: this.historyData[index]
        }
      })
    },
    // 交易
    recordDetails (index) {
      this.$router.push({
        name: 'new_recor',
        params: {
          managementId: index,
          content: this.historyData[index]
        }
      })
    },
    // 下拉所属用户
    click_value (e) {
      this.lable = e
      this.search_mark = ''
      this.search_name = ''
    },
    click_value2 (e) {
      this.iplable = e
      this.intranet_ip = ''
      this.extranet_ip = ''
    },

    // 输入框值
    serch_btn (e) {
      if (this.lable === '所属用户') {
        this.search_mark = this.search_value
        this.search_name = ''
      } else if (this.lable === '节点名称') {
        this.search_name = this.search_value
        this.search_mark = ''
      }
      if (this.iplable === '内网IP') {
        this.intranet_ip = this.search_valueip
        this.extranet_ip = ''
      } else if (this.iplable === '外网IP') {
        this.intranet_ip = ''
        this.extranet_ip = this.search_valueip
      }
      this.getTablist() // 获取表格列表
    },
    // 重制
    reset () {
      this.search_valueip = ''
      this.chainetwork = ''
      this.search_value = ''
      this.search_mark = ''
      this.model3 = '所属用户'
      this.model2 = '内网IP'
      this.search_name = ''
      this.intranet_ip = ''
      this.extranet_ip = ''
      this.getTablist()
    },
    // 请求的方法
    getTablist () {
      let nodeData = {
        id: '',
        pageParam: this.PageParam, // 分页
        nodeName: this.search_name, // 节点名称
        belongUserId: this.search_mark, // 所属用户
        networkAddress: this.intranet_ip, // 内网IP
        nodeAddress: this.extranet_ip, // 外网IP
        chainName: this.chainetwork
      }
      nodeManagement(nodeData).then(res => {
        const { records } = res.data
        let recostate = {
          0: '共识节点',
          1: '业务节点'
        }
        let onlinete = {
          0: '不在线',
          1: '在线'
        }
        let lists = records.map(item => {
          return {
            ...item,
            roleStatus: recostate[item.roleStatus],
            online: onlinete[item.online]
          }
        })
        this.historyData = lists
        this.PageParam = {
          pagetotal: res.data.total,
          pageSize: res.data.size,
          pageIndex: res.data.current
        }
        ++this.transferKey
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  created () {
    // this.userLoginId = localStorage.getItem("userLoginId");
    this.getTablist() // 获取表格列表
  }
}
</script>

<style lang="less" scoped>
.ivu-card-body {
  .ivu-input-wrapper {
    // line-height: 34px;
  }

  .ivu-select-single .ivu-select-selection {
    background: red;
  }
}
.cz_header {
  display: flex;
  margin-top: 10px;
  justify-content: space-between;
  .sl_timout {
    border: 1px solid #dcdee2;
    height: 33px;
    padding: 5px 8px;
    text-align: center;
  }
  .cz_sltimout {
    width: 50%;
  }
  .cz_ss {
    display: flex;
  }
  .s_type {
    display: flex;
    .s_title {
      border: 1px solid #dcdee2;
      height: 33px;
      padding: 5px 8px;
      text-align: center;
    }
  }
}

// table
.cz_table {
  margin-top: 2% !important;
}
// 搜索
.net_ip {
  display: flex;
  margin-right: 5px;
}
.ss_name {
  display: flex;
  margin-right: 5px;
}
.net_work {
  display: flex;
  margin-right:5px;
  .auto {
    width: auto !important;
  }
}
.btn {
  display: flex;
}
</style>
