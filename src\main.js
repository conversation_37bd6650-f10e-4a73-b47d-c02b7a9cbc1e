import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import Bus from './lib/bus'
import iview from 'view-design'
import 'view-design/dist/styles/iview.css'
import '@/assets/font/iconfont.js'
import '@/assets/font/iconfont.css'
import IconFont from '_c/icon-font'
import IconSvg from '_c/icon-svg'
import { baseURL } from '@/config'
import axios from 'axios'
import '@/styles/index.less' // global css
import '@/styles/message.less' // Message css
import 'remixicon/fonts/remixicon.css'
import VueFullscreen from 'vue-fullscreen'

Vue.use(VueFullscreen)
Object.defineProperty(Vue.prototype, '$http', { value: axios }) // 把axios 加入到Vue对象里面
// 防止重复提交指令
const preventReClick = Vue.directive('preventReClick', {
  inserted (el, binding) {
    el.addEventListener('click', () => {
      if (!el.disabled) {
        el.disabled = true
        setTimeout(() => {
          el.disabled = false
        }, binding.value || 1000)
      }
    })
  }
})
import { setVueClickGlobalThrottle } from '@/lib/util.js'

setVueClickGlobalThrottle(Vue) // 将所有click 进行节流处理
Vue.use(preventReClick)
// if (process.env.NODE_ENV !== 'production') require('./mock')
// console.log(baseURL)

Vue.prototype.$baseURL = baseURL
Vue.config.productionTip = false
Vue.prototype.$bus = Bus
Vue.component('icon-font', IconFont)
Vue.component('icon-svg', IconSvg)
Vue.use(iview)

Vue.prototype.$Message.config({
  top: 250,
  duration: 3
})

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')