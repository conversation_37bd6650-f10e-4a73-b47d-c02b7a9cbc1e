<template>
  <div class="workorder-new">
    <Form ref="workOrder" :rules="workOrderRule" :model="userInfo" :label-width="240" style="padding:10px 0 0 10px;font-size:12px;">
      <h3>问题信息</h3>
      <FormItem label="问题位置" prop="menuFunction">
        <!-- <p v-show="showparent" style="width:410px;">
        {{menuDetail.parentName}}
        <a @click="showparent = false">更改</a>
      </p> -->
        <Cascader v-model="menuDetail.parentId" @on-change="onChangeCascader" :data="dataCascader" trigger="hover" filterable change-on-select style="width:410px;"></Cascader>
      </FormItem>
      <FormItem label="标题" prop="title">
        <Input type="text" v-model="userInfo.title" style="width:410px;" maxlength="30" />
      </FormItem>
      <FormItem label="问题描述" prop="brief">
        <Input v-model="userInfo.brief" maxlength="200" show-word-limit type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="请详细描述问题" style="width:410px;" />
      </FormItem>
      <FormItem label="调整期望" prop="expectResult">
        <Input v-model="userInfo.expectResult" maxlength="200" show-word-limit type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="请调整期望结果" style="width:410px;" />
      </FormItem>
      <FormItem label="附件">
        <p style="font-size:13px">{{"最多一个附件，每个大小不超过"+maxsize / 1024+"M，支持格式：png，jpg，jpeg，xls，xlsx，doc，docx，pdf，mov，rmvb，mp4，avi"}}</p>
        <Upload action="" :max-size="maxsize" :before-upload="handleUpload" :on-exceeded-size="handleMaxSize">
          <!-- <Icon type="md-attach" style="transform:rotate(45deg);color:#57a3f3;" /><span class="click" style="color:#57a3f3;">添加附件</span>
          <span class="size-color">{{'(单文件最大'+ maxsize / 1024 +'M)'}}</span> -->
          <Button icon="ios-cloud-upload-outline">上传附件</Button>
        </Upload>
        <div v-show="uploadFiles.length > 0 ? true : false" style="font-size:8px;background-color: #abccf052;padding:5px 0 0 5px;" v-for="(item, i) in uploadFiles" :key="i">
          <div class="upload-file">
            <Icon type="md-list-box" style="color:#57a3f3;margin-top:-5px;" size="18" />
            {{item.fileName}}
            <span style="color:#bdbbbb;margin-left:5px;">({{getFileSize(item.fileSize)}})</span>
            &nbsp;&nbsp;&nbsp;
            <span style="color:#57a3f3;cursor:pointer;font-size:8px;" @click="fileDel(item.fileId)">删除</span>
          </div>
        </div>
      </FormItem>
      <Divider />
      <h3>填报人信息</h3>
      <FormItem label="填报人" prop="reportUserIdName">
        <Input v-model="userInfo.reportUserIdName" style="width:410px;" disabled />
      </FormItem>
      <FormItem label="所属租户" prop="tenantId">
        <Select v-model="userInfo.tenantId" filterable style="width:410px;">
          <Option v-for="item in tenantIdList" :value="`${item.tenantId}`" :key="item.tenantId+Math.random()">{{ item.tenantName }}</Option>
        </Select>
      </FormItem>
      <FormItem label="平台角色" prop="reportRoleName">
        <Input v-model="userInfo.reportRoleName" style="width:410px;" disabled />
      </FormItem>
      <!-- <FormItem  label="填报日期" prop="password" >
        <Input v-model="userInfo.password" style="width:410px;" />
    </FormItem> -->
      <FormItem label="邮箱地址" prop="reportEmail">
        <Input v-model="userInfo.reportEmail" style="width:410px;" />
      </FormItem>
      <FormItem label="联系电话" prop="reportPhoneNumber">
        <Input maxlength="11" v-model="userInfo.reportPhoneNumber" style="width:410px;" />
        <span style="margin-left:5px">处理结果将以短信形式通知您，<span class="text" @click="downDoc">具体服务内容可参考客户服务SLA</span> </span>
      </FormItem>
      <FormItem label="替他人填报" prop="reportType">
        <RadioGroup v-model="userInfo.reportType">
          <Radio label="MYSELF">否</Radio>
          <Radio label="FOR_OTHERS">是</Radio>
        </RadioGroup>
      </FormItem>
      <div v-if="userInfo.reportType==='FOR_OTHERS'">
        <Divider />
        <h3>申请人信息</h3>
        <FormItem label="申请人" prop="applyUserId">
          <Select v-model="userInfo.applyUserId" filterable style="width:410px;">
            <Option v-for="item in personList" :value="`${item.userId}`" :key="item.userId+Math.random()">{{ item.userName }}</Option>
          </Select>
        </FormItem>
        <FormItem label="所属租户" prop="tenantId">
          <Select v-model="userInfo.tenantId" filterable style="width:410px;" disabled>
            <Option v-for="item in tenantIdList" :value="`${item.tenantId}`" :key="item.tenantId+Math.random()">{{ item.tenantName }}</Option>
          </Select>
        </FormItem>
        <FormItem label="平台角色" prop="applyRoleName">
          <Input v-model="userInfo.applyRoleName" style="width:410px;" disabled />
        </FormItem>
        <!-- <FormItem  label="填报日期" prop="password" >
          <Input v-model="userInfo.password" style="width:410px;">
          </Input>
      </FormItem> -->
        <FormItem label="邮箱地址" prop="applyEmail">
          <Input v-model="userInfo.applyEmail" style="width:410px;" />
        </FormItem>
        <FormItem label="联系电话" prop="applyPhoneNumber">
          <Input maxlength="11" v-model="userInfo.applyPhoneNumber" style="width:410px;" />
        </FormItem>
      </div>
      <FormItem style="text-align:center;width:650px;">
        <Button type="primary" @click="handleSubmit" style="font-size:13px;" :disabled="!getSaveFlag">提交</Button>
        <Button @click="handleCancel" style="margin-left: 8px;font-size:13px;">取消</Button>
      </FormItem>
    </Form>
  </div>
</template>
<script>
import { orderNew, getMenuInfo, getTenantIdList, getUserIdList, uploadAnnounceFile, getUserInfo } from '@/api/data'
import { localRead } from '@/lib/util'
import { isPhoneNumber, isEmail } from '@/lib/check'
import { mapActions } from 'vuex' // mapMutations,
export default {
  data () {
    const validateEmail = (rule, value, callback) => {
      if (!isEmail(value)) {
        callback(new Error('邮箱格式不正确'))
      } else {
        callback()
      }
    }
    const validatePhone = (rule, value, callback) => {
      if (!isPhoneNumber(value)) {
        callback(new Error('手机号格式不支持'))
      } else {
        callback()
      }
    }
    return {
      timer: null,
      saveFlag: true,
      flag: true,
      userInfo: {
        'title': '',
        'brief': '',
        'expectResult': '',
        'fileId': '',
        'menuFunction': '',
        'tenantId': '',
        'reportType': 'MYSELF',
        'reportUserId': '',
        reportUserIdName: '',
        'reportRoleName': '',
        'reportEmail': '',
        'reportPhoneNumber': ''
      },
      workOrderRule: {
        title: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, max: 64, message: '长度不能超过64', trigger: 'blur' }
        ],
        brief: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        expectResult: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        menuFunction: [
          { required: true, trigger: 'blur' }
        ],
        tenantId: [
          { required: true, message: '不能为空11', trigger: 'blur' }
        ],
        reportUserIdName: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        reportRoleName: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        reportEmail: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateEmail }
        ],
        reportPhoneNumber: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validatePhone }
        ],
        applyUserId: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        applyRoleName: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        applyEmail: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateEmail }
        ],
        applyPhoneNumber: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validatePhone }
        ]
      },
      showparent: false,
      switchStatus: false,
      personList: [], // 申请人
      tenantIdList: [], // 租户
      dataCascader: [],
      uploadFiles: [], // 显示已上传的文件
      menuDetail: {
        'resourceId': '',
        'resourceName': '',
        'title': '',
        'resourceType': 'MENU',
        'url': '',
        'httpMethod': '',
        'parentId': [],
        'parentName': '/',
        'status': 'DISABLE',
        'seq': ''
      },
      maxsize: localStorage.getItem('MAX_FILE_SIZE') ? JSON.parse(localStorage.getItem('MAX_FILE_SIZE')) : 2048
    }
  },
  computed: {
    getSaveFlag () {
      let flag = true
      const unJoin = ['fileId']
      for (let item in this.userInfo) {
        if (!this.userInfo[item] && !unJoin.includes(item)) {
          flag = false
        }
      }
      if (this.userInfo.reportType !== 'MYSELF') {
        if (this.userInfo.applyUserId && this.userInfo.applyEmail && this.userInfo.applyPhoneNumber) {
          flag = true
        } else {
          flag = false
        }
      }
      return flag
    }
  },
  methods: {
    ...mapActions([
      'updateOrderId',
      'updateTablePageParam'
    ]),
    // 下载
    downDoc () {
      window.location.href = '/中国移动中移链CMBaaS产品客户服务SLA协议.docx'
    },
    onChangeCascader (value) {
      this.userInfo.menuFunction = `${value && value.join(',')}`
      this.userInfo.title = `${value && value.join('页面-')}功能的调整工单`
    },
    handleSubmit () {
      this.$refs['workOrder'].validate((valid) => {
        if (valid) {
          let params = {
            'title': this.userInfo.title,
            'brief': this.userInfo.brief,
            'expectResult': this.userInfo.expectResult,
            'fileId': this.userInfo.fileId,
            'fileHash': this.userInfo.fileHash,
            'menuFunction': this.userInfo.menuFunction,
            'tenantId': Number(this.userInfo.tenantId),
            'reportType': this.userInfo.reportType,
            'reportUser': {
              'reportUserId': Number(this.userInfo.reportUserId),
              // reportUserIdName: this.userInfo.reportUserIdName,
              'reportRoleName': this.userInfo.reportRoleName,
              'reportEmail': this.userInfo.reportEmail,
              'reportPhoneNumber': this.userInfo.reportPhoneNumber
            }
          }
          if (this.userInfo.reportType === 'FOR_OTHERS') {
            params = {
              'title': this.userInfo.title,
              'brief': this.userInfo.brief,
              'expectResult': this.userInfo.expectResult,
              'fileId': this.userInfo.fileId,
              'fileHash': this.userInfo.fileHash,
              'menuFunction': this.userInfo.menuFunction,
              'tenantId': Number(this.userInfo.tenantId),
              'reportType': this.userInfo.reportType,
              'reportUser': {
                'reportUserId': Number(this.userInfo.reportUserId),
                'reportRoleName': this.userInfo.reportRoleName,
                'reportEmail': this.userInfo.reportEmail,
                'reportPhoneNumber': this.userInfo.reportPhoneNumber
              },
              'applyUser': {
                'applyUserId': Number(this.userInfo.applyUserId),
                'applyRoleName': this.userInfo.applyRoleName,
                'applyEmail': this.userInfo.applyEmail,
                'applyPhoneNumber': this.userInfo.applyPhoneNumber
              }
            }
          }
          orderNew(params).then((res) => {
            if (res.code === '00000') {
              this.msgInfo('success', res.message + '即将跳转到工单管理界面', true)
              this.updateOrderId(`new${Math.random()}`)
              this.timer = setTimeout(() => {
                this.userInfo = {}
                // 初始化分页的值
                const transParams = { pagetotal: 0, pageSize: 10, pageIndex: 1 }
                this.updateTablePageParam(transParams)
                this.$router.push({
                  name: 'user_info',
                  params: { cur: 4 }
                })
              }, 2 * 1000)
              // this.$Spin.show({
              //   render: (h) => {
              //     return h('div', [
              //       h('Icon', {
              //         'class': 'demo-spin-icon-load',
              //         props: {
              //           type: 'ios-loading',
              //           size: 18
              //         }
              //       }),
              //       h('div', res.message + '即将跳转到工单管理界面')
              //     ])
              //   }
              // })
              // setTimeout(() => {
              //   this.$router.push({
              //     name: 'user_info',
              //     params: { cur: 4 }
              //   })
              //   this.$Spin.hide()
              // }, 3000)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        }
      })
    },
    handleCancel () {
      this.$router.push({
        name: 'user_info',
        params: { cur: 4 }
      })
    },
    handleShow () {
      this.flag = !this.flag
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    // 获取用户信息
    getUserInfoData (userLoginId) {
      // localSave('userLoginId', 'longch')
      getUserInfo(userLoginId).then(res => {
        this.userInfo.reportUserId = res.data.userId
        this.userInfo.reportUserIdName = res.data.realName
        this.userInfo.reportEmail = res.data.email
        this.userInfo.reportPhoneNumber = res.data.phoneNumber
        this.getUserIdList(res.data.userId)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询用户对应租户角色列表接口
    getUserIdList (userId) {
      getUserIdList(userId).then(res => {
        if (res.code !== '00000') this.msgInfo('warning', res.message, true)
        else {
          this.tenantIdList = res.data.tenantRoles
          this.userInfo.tenantId = `${this.tenantIdList[0].tenantId}`
          this.userInfo.reportRoleName = this.tenantIdList[0].roleName
          this.userInfo.applyRoleName = this.tenantIdList[0].roleName
          this.getPersonList(this.userInfo.tenantId)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询租户对应用户角色列表接口
    getPersonList (tenantId) {
      getTenantIdList(tenantId).then(res => {
        if (res.code !== '00000') this.msgInfo('warning', res.message, true)
        else this.personList = res.data.userRoles
      }).catch(error => {
        // console.log('error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },

    getMenu (resourceType) {
      getMenuInfo(resourceType).then(res => {
        if (res.code !== '00000') this.msgInfo('warning', res.message, true)
        else this.dataCascader = res.data
      }).catch(error => {
        // console.log('getMenuInfo.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },

    handleMaxSize (file) {
      this.msgInfo('warning', '文件' + file.name + '太大,不能超过' + this.maxsize / 1024 + 'M', true)
      return false
    },
    handleUpload (file) {
      let result = false
      if (!result) {
        if (file.size / 1024 / 1024 <= this.maxsize / 1024) {
          let types = ['png', 'jpg', 'jpeg', 'xls', 'xlsx', 'doc', 'docx', 'pdf', 'mov', 'rmvb', 'mp4', 'avi']
          let typeString = file.type.split('/')[1]
          let flag = false
          types.forEach(val => {
            if ((typeString && typeString.indexOf(val) !== -1) || file.name.indexOf(`.${val}`) !== -1) {
              flag = true
            }
          })
          if (!flag) {
            this.msgInfo('error', `不符合上传的文件类型`, true)
            return
          }
          var fileObj = {}
          uploadAnnounceFile('ANNOUNCE', file).then(res => {
            if (res.code === '00000') {
              fileObj.fileName = file.name
              fileObj.file = file
              fileObj.fileSize = file.size
              fileObj.fileId = res.data.fileId
              fileObj.fileHash = res.data.fileHash
              this.userInfo.fileId = res.data.fileId
              this.userInfo.fileHash = res.data.fileHash
              this.uploadFiles.splice(0, 1, fileObj)
            } else {
              this.msgInfo('error', res.message, true)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        }
      } else {
        this.msgInfo('warning', '文件' + file.name + '已上传', true)
      }
    },
    getFileSize (value) {
      if (value < 1024) {
        return value + 'B'
      } else if (value / (1024 * 1024) < 1) {
        return (value / 1024).toFixed(2) + 'KB'
      } else {
        return (value / 1024 / 1024).toFixed(2) + 'MB'
      }
    },
    fileDel (fileId) {
      this.uploadFiles = this.uploadFiles.filter(item => item.fileId !== fileId)
    }
  },
  watch: {
    dataCascader: {
      handler (parentId) {
        if (this.dataCascader !== []) {
          for (let i = 0; i < this.dataCascader.length; i++) {
            // this.dataCascader[i].value = this.dataCascader[i].resourceId
            this.dataCascader[i].value = this.dataCascader[i].title
            this.dataCascader[i].label = this.dataCascader[i].title
            this.dataCascader[i].expand = ''
            if (this.dataCascader[i].children) {
              for (let j = 0; j < this.dataCascader[i].children.length; j++) {
                this.dataCascader[i].children[j].value = this.dataCascader[i].children[j].title
                this.dataCascader[i].children[j].label = this.dataCascader[i].children[j].title
                this.dataCascader[i].children[j].expand = ''
                if (this.dataCascader[i].children[j].children) {
                  for (let z = 0; z < this.dataCascader[i].children[j].children.length; z++) {
                    this.dataCascader[i].children[j].children[z].value = this.dataCascader[i].children[j].children[z].title
                    this.dataCascader[i].children[j].children[z].label = this.dataCascader[i].children[j].children[z].title
                    this.dataCascader[i].children[j].children[z].expand = ''
                  }
                }
              }
            }
          }
          // console.log(this.dataCascader)
        }
      },
      deep: true,
      immediate: false
    }
  },
  mounted () {
    // this.uploadList = this.$refs.upload.fileList
    this.$Message.config({
      top: 250,
      duration: 3
    })
    this.getUserInfoData(localRead('userLoginId'))
    this.getMenu('MENU')
    if (this.$route.params.menuDetail) {
      this.menuDetail.parentId = this.$route.params.menuDetail
      this.onChangeCascader(this.menuDetail.parentId)
    }
  },
  destroyed () {
    clearTimeout(this.timer)
  }
}
</script>
<style lang="less" scoped>
.text {
  cursor: pointer;
}
// 内容
.workorder-new {
  width: 100%;
  h3 {
    height: 26px;
    line-height: 26px;
    margin: 0 auto;
    text-align: left;
    border-left: 6px solid #3d73ef;
    padding-left: 10px;
    margin-bottom: 5px;
  }
}
/deep/.ivu-form-item-label {
  font-size: 13px !important;
}
/deep/.ivu-modal-header-inner {
  font-size: 14px !important;
  font-weight: bold;
}
/deep/.ivu-input {
  font-size: 13px !important;
}
.ivu-btn {
  font-size: 13px;
}
</style>
