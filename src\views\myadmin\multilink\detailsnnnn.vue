<template>
  <div class="multilink-details">
    <div style="width：auto;margin-left: 10px;">
      <Card style="width: auto;">
        <!-- <p class="info-title" style="margin:10px 0 15px 10px;"><span class="bs">基本信息</span></p> -->
        <div class="info-title addflex">
          <div>
            <div class="bs"></div>
            <span>基本信息</span>
          </div>
          <div class="btns">
            <Button ghost type="primary" @click="clickHyperion">Hyperion 配置</Button>
            <Button ghost type="primary" @click="clickElas">Elasticsearch 配置</Button>
            <!-- <Button ghost type="primary" @click="clickSuite">开发套件配置</Button> -->
          </div>
        </div>
        <ul>
          <li>链名称：{{ arrDetails.chainName }}</li>
          <li>开发架构类型：{{ arrDetails.engineType }}</li>
          <li>开发架构链ID：{{ arrDetails.eosChainId }}</li>
          <li>合约升级：{{ arrDetails.isUpgradeContract==='1'?'是':'否' }}</li>
          <li>所有权：{{ arrDetails.ownership }}</li>
          <li>主子链：{{ arrDetails.chainSource==='SUB_CHAIN'?'省子链':'集团主链' }}</li>
          <!-- <div v-if="chaindetail.chainSource==='MAIN_CHAIN'">
          <li >链归属公司：{{ chaindetail.companyName }}</li>
          </div> -->
          <div v-if="arrDetails.chainSource==='SUB_CHAIN'">
            <!-- <li v-if="chaindetail.companyName===null">链归属公司：</li> -->
            <li>链归属公司：{{ arrDetails.companyName==null?'': arrDetails.companyName}}</li>
          </div>

          <!-- <li v-show="chaindetail.chainSource==='SUB_CHAIN'">链归属公司：{{ chaindetail.companyName }}</li> -->
          <li>审核列表：{{ arrDetails.auditList?arrDetails.auditList.map(val => val.auditValue).join(','):'' }}</li>
          <li>状态：<span>{{ arrDetails.status }}</span></li>
          <li style="word-break:break-all;white-space: pre-wrap;">描述：{{ arrDetails.chainBrief }}</li>
        </ul>
      </Card>
    </div>

    <Card style="margin:20px 5px 10px 10px;">
      <div class="node" style="width：auto;margin:10px 5px 0px 10px;">
        <div class="info-title">
          <span class="bs"></span><span>节点</span>
          <Button ghost type="success" @click="add" icon="md-add" style="float:right;margin-top:-5px;">新增节点</Button>
        </div>
        <edit-table-mul :columns="columns" v-model="tableData" :key="transferKey" style="margin-top:20px;"></edit-table-mul>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[5,10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;margin:10px 0;" />
      </div>
    </Card>
    <Card style="margin:20px 5px 30px 10px;" v-if="isUpgradeContract=='0'">
      <div class="account" style="margin:10px 5px 30px 10px;">
        <div class="info-title">
          <span class="bs"></span><span>链账户</span>
          <Button class="btn" type="success" ghost @click="addAccount" icon="md-add" style="float:right;margin-top:-5px;">新增管理链账户</Button>
        </div>
        <edit-table-mul :columns="accountColums" v-model="accountTableData" :key="transferKey" style="margin-top:20px;"></edit-table-mul>
      </div>
    </Card>
    <Card style="margin:20px 5px 30px 10px;">
      <Tenant :tenantVisibility="arrDetails.tenantVisibility" :chainId="arrDetails.chainId" :chainName="arrDetails.chainName"></Tenant>
    </Card>
    <Modal :draggable="true" v-model="modal" width="580" :title="formItem.alertTitle" :z-index="1000" sticky :mask-closable="false" @on-cancel="cancel('formItem')">
      <Form ref="formItem" :rules="formItemRule" :model="formItem" :label-width="130">
        <FormItem label="节点名称：" prop="nodeName">
          <Input placeholder="请输入节点名称" style="width:400px;vertical-align:baseline;" v-model="formItem.nodeName" />
        </FormItem>
        <FormItem label="节点类型：" prop="nodeType">
          <Select v-model="formItem.nodeType" placeholder="请选择节点类型" style="width:200px;">
            <Option v-for="item in nodeTypeList" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem label="IP：" prop="nodeAddress">
          <Input placeholder="请输入IP地址" style="width:400px;vertical-align:baseline;" v-model="formItem.nodeAddress" />
        </FormItem>
        <FormItem label="API端口：" prop="nodeApiPort">
          <Input placeholder="请输入API端口地址" style="width:400px;vertical-align:baseline;" v-model="formItem.nodeApiPort" />
        </FormItem>
        <FormItem label="P2P端口：">
          <Input placeholder="请输入P2P端口地址" style="width:400px;vertical-align:baseline;" v-model="formItem.nodeP2pPort" />
        </FormItem>
        <FormItem label="地理位置：" class="mandatory">
          <Select v-model="formItem.provinceCode" filterable clearable style="width:160px;" placeholder="请选择所在省" @on-open-change="selectmethods" @on-select="itemprovinces" @on-clear="clearcode">
            <Option v-for="item in cityList" :value="item.code" :key="item.name">{{ item.name }}</Option>
          </Select>
          <Select v-model="formItem.cityCode" filterable clearable style="width:160px;" placeholder="请选择所在市" :disabled="disabled" @on-select="itemcity" @on-clear="clearCity">
            <Option v-for="item in cityList1" :value="item.code" :key="item.name">{{ item.name }}</Option>
          </Select>
          <!-- <Input type="textarea" style="width:400px;vertical-align:baseline;"  v-model="formItem.location" :maxlength="128" show-word-limit :autosize="{minRows: 3,maxRows: 5}" placeholder="请输入位置信息"/> -->
        </FormItem>
        <FormItem label="磁盘挂载目录：" prop="diskdir">
          <Input placeholder="请输入磁盘挂载目录" style="width:400px;vertical-align:baseline;" v-model="formItem.diskdir" />
        </FormItem>
        <FormItem v-if="formItem.status" label="启用状态：" prop="status">
          <Select v-model="formItem.status" placeholder="选择运行状态" style="width:200px;">
            <Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem label="运行状态：" v-if="formItem.statusYun" prop="statusYun">
          <p>{{formItem.statusYun=== 'ENABLE' ? '正常' : '异常'}}</p>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="cancel('formItem')">取消</Button>
        <Button type="primary" @click="ok('formItem')" :loading="loading">
          <span v-if="!loading">确定</span>
          <span v-else>Loading...</span>
        </Button>
      </div>
    </Modal>
    <Modal :draggable="true" v-model="accountModal" width="700" :title="accountItem.alertTitle" :z-index="1000" sticky :mask-closable="false">
      <Form ref="accountItem" :rules="accountItemRule" :model="accountItem" :label-width="150">
        <FormItem label="管理链账户名称：" prop="manageAccountName">
          <Tooltip max-width="200" content="5-12位,仅包含{a-z,1-5,.},且.不能在最前和最后" style="margin-left: -18px;">
            <Icon type="md-help-circle" style="font-size:16px;" />
          </Tooltip>
          <Input placeholder="管理链账户名称" style="width:250px;vertical-align:baseline;" v-model="accountItem.manageAccountName" :disabled="disabledInput" />
        </FormItem>
        <FormItem v-if="ownerKeyShow" label="owner权限私钥：" prop="ownerPrivateKey">
          <Input type="password" placeholder="owner权限私钥" style="width:500px;vertical-align:baseline;" v-model="accountItem.ownerPrivateKey">
          <i class="ri-eye-close-line" slot="suffix" @click="handleOwnerKey"></i>
          </Input>
        </FormItem>
        <FormItem v-else label="owner权限私钥：" prop="ownerPrivateKey">
          <Input type="text" placeholder="owner权限私钥" style="width:500px;vertical-align:baseline;" v-model="accountItem.ownerPrivateKey">
          <i class="ri-eye-line" slot="suffix" @click="handleOwnerKey"></i>
          </Input>
        </FormItem>
        <FormItem v-if="activeKeyShow" label="active权限私钥：" prop="activePrivateKey">
          <Input type="password" placeholder="active权限私钥" style="width:500px;vertical-align:baseline;" v-model="accountItem.activePrivateKey">
          <i class="ri-eye-close-line" slot="suffix" @click="handleActiveKey"></i>
          </Input>
        </FormItem>
        <FormItem v-else label="active权限私钥：" prop="activePrivateKey">
          <Input type="text" placeholder="active权限私钥" style="width:500px;vertical-align:baseline;" v-model="accountItem.activePrivateKey">
          <i class="ri-eye-line" slot="suffix" @click="handleActiveKey"></i>
          </Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="cancelAccount">取消</Button>
        <Button type="primary" @click="okAccount">确定</Button>
      </div>
    </Modal>
    <hyperion-modal ref="hyperionModal" :chainId="`${arrDetails.chainId}`" />
    <elasticsearch-modal ref="elasticsearchModal" :chainId="`${arrDetails.chainId}`" />
    <!-- <suite-modal ref="suiteModal" :chainId="`${arrDetails.chainId}`" /> -->
  </div>
</template>

<script>
import HyperionModal from './hyperionModal.vue'
import ElasticsearchModal from './elasticsearchModal.vue'
// import suiteModal from './suiteModal.vue'
import { getMultiLinkDetails, reviseMultiChainNode, addMultiChainNode, addManageAccount, deleteManageAccount, getNodeList } from '@/api/data'
import { getserchList } from '@/api/contract'
import EditTableMul from '_c/edit-table-mul'
import Tenant from './tenant'
import { searchKey, getKey, changeKey } from './tool'
import { statusList, nodeTypeList } from './typeList'
import { isNumber, isAccount } from '../../../lib/check'
export default {
  name: 'multilink_details',
  components: {
    EditTableMul,
    Tenant,
    HyperionModal,
    ElasticsearchModal
    // suiteModal
  },
  data () {
    const validateValue = (rule, value, callback) => {
      if (!isNumber(value)) {
        callback(new Error('请输入数字'))
      } else {
        callback()
      }
    }
    const validateAccount = (rule, value, callback) => {
      if (!isAccount(value)) {
        callback(new Error('仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字'))
      } else {
        callback()
      }
    }
    // const validateIpvalue = (rule, value, callback) => {
    //   if (!isIpNumber(value)) {
    //     callback(new Error('请输入正确的IP'))
    //   } else {
    //     callback()
    //   }
    // }
    const validateName = (rule, value, callback) => {
      let reg = /^[/]/
      if (!reg.test(value.slice(0, 1))) {
        callback(new Error('请输入以“/xx/xx/xx....”的格式路径'))
      } else {
        callback()
      }
    }
    return {
      chaindetail: {},
      disabled: true,
      loading: false,
      tablePageParam: {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      },
      disabledInput: false,
      modal: false,
      accountModal: false,
      ownerKeyShow: true,
      activeKeyShow: true,
      transferKey: 0,
      eosChainId: this.$route.params.eosChainId ? this.$route.params.eosChainId : '',
      arrDetails: {},
      tableData: [],
      accountTableData: [],
      reviseTableData: [],
      formItem: {
        alertTitle: '新增节点',
        chainId: '',
        nodeId: '',
        nodeName: '',
        nodeAddress: '',
        nodeApiPort: '',
        nodeType: '',
        status: '',
        nodeP2pPort: '',
        location: '',
        nodeTypeKey: '',
        statusKey: '',
        provinceCode: null, // 省code
        cityCode: null, // 市code
        provinceName: '', // 省名称
        cityName: '', // 市名称
        diskdir: '', // 磁盘目录
        statusYun: ''
      },
      accountItem: {
        alertTitle: '新增管理链账户',
        chainId: '',
        manageAccountName: '',
        ownerPrivateKey: '',
        activePrivateKey: ''
      },
      formItemRule: {
        nodeName: [{ required: true, message: '不能为空', trigger: 'blur' }],
        nodeAddress: [{ required: true, message: '不能为空', trigger: 'blur' }
          //  { required: true, trigger: 'blur', validator: validateIpvalue }
        ],
        nodeApiPort: [{ required: true, message: '不能为空,只能为数字', trigger: 'blur' },
        { required: true, trigger: 'blur', validator: validateValue }
        ],
        nodeP2pPort: [{ required: true, trigger: 'blur', validator: validateValue }],
        nodeType: [{ required: true, message: '请选择一项', trigger: 'change' }],
        status: [{ required: true, message: '请选择一项', trigger: 'blur' }],
        diskdir: [{ required: true, message: '请输入磁盘挂载目录', trigger: 'blur' },
        { max: 200, message: '请输入200字符以内名称', trigger: 'blur' },
        { required: true, trigger: 'blur', validator: validateName }]
      },
      accountItemRule: {
        manageAccountName: [
          { required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
          { max: 12, message: '不能多于12位', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateAccount }
        ],
        ownerPrivateKey: [{ required: true, message: '不能为空', trigger: 'blur' },
        { type: 'string', pattern: /^[a-zA-Z0-9]{51}$/, message: '格式有误,长度必须为51位,a-zA-Z0-9', trigger: 'blur' }
        ],
        activePrivateKey: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },
      statusList: [],
      nodeTypeList: [],
      columns: [
        // { key: 'nodeId', title: '链节点id', sortable: true },
        { key: 'nodeName', title: '节点名称' },
        { key: 'nodeType', title: '节点类型' },
        { key: 'nodeAddress', title: 'IP' },
        { key: 'nodeApiPort', title: 'API端口' },
        { key: 'location', title: '地址', tooltip: true },
        {
          key: 'status',
          title: '启用状态',
          render: (h, params) => {
            const color = params.row.statusKey === 'ENABLE' ? '#15AD31' : '#C7C7C7'
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, params.row.status)
          }
        },
        {
          key: 'nodeStatus',
          title: '运行状态',
          render: (h, params) => {
            const color = params.row.nodeStatus === 'ENABLE' ? '#15AD31' : '#FF4D4F'
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, params.row.nodeStatus === 'ENABLE' ? '正常' : '异常')
          }
        },
        {
          key: 'action',
          title: '操作',
          render: (h, params) => {
            console.log(params.row);
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small', disabled: params.row.ipDesensitizeOpen === '1' },
                style: { marginRight: '8px', color: params.row.ipDesensitizeOpen === '1' ? '#c5c8ce' : '#3D73EF', border: params.row.ipDesensitizeOpen === '1' ? '1px solid #c5c8ce' : '1px solid #3D73EF' },
                on: {
                  click: () => {
                    this.editDetails(params.index)
                  }
                }
              }, '编辑')]
            )
          }
        }
      ],
      accountColums: [
        // { key: 'chainId', title: '目标链Id' },
        { key: 'accountName', title: '管理链账户名称' },
        { key: 'ownerPrivateKey', title: 'owner权限私钥' },
        { key: 'activePrivateKey', title: 'active权限私钥' },
        {
          key: 'action',
          title: '操作',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    this.editAccount(params.index)
                  }
                }
              }, '编辑'),
              h('Poptip', {
                props: {
                  confirm: true,
                  transfer: true,
                  title: '确定删除管理链账户[' + this.accountTableData[params.index].accountName + ']吗?'
                },
                on: {
                  'on-ok': () => {
                    this.editTableIndex = -1
                    // 调用删除方法
                    this.deleteAccount(params.index)
                  },
                  'on-cancel': () => {
                  }
                }
              }, [
                h('Button', {
                  class: { 'btnFA5151': true },
                  props: { size: 'small', type: 'text' },
                  style: { marginRight: '8px', color: '#FA5151', border: '1px solid #FA5151' },
                  on: {
                    click: () => {
                    }
                  }
                }, '删除')
              ]
              )
            ])
          }
        }
      ],
      cityList: [], // 省数组
      cityList1: [], // 市数组
      isUpgradeContract: ''
    }
  },
  methods: {
    // 省清空
    clearcode () {
      this.formItem.provinceCode = null
      this.formItem.cityCode = null
      this.formItem.provinceName = ''
      this.formItem.cityName = ''
      this.disabled = true
    },
    // 市清空
    clearCity () {
      this.formItem.cityName = ''
      this.formItem.cityCode = null
    },
    getselectOptions () { // 获取省下来选项列表
      let listdata = {
        pcode: 100000,
        name: ''
      }
      getserchList(listdata).then((res) => {
        this.cityList = res.data
      })
    },
    async getCityOptions (params) { // 获取市下拉列表
      let { data } = await getserchList(params)
      this.cityList1 = data
      this.formItem.cityName = this.cityList1[0].name
      this.formItem.cityCode = this.cityList1[0].code
      return data
    },
    //
    selectmethods () {
      // console.log(this.provinces)

      if (this.formItem.provinceCode !== null) {
        this.disabled = false
      }
    },
    // 选择省份item
    itemprovinces (e) {
      // console.log(e)
      this.formItem.provinceCode = e.value // 省code
      this.formItem.provinceName = e.label// 省名称
      let listdata = {
        pcode: e.value,
        name: ''
      }
      // getserchList(listdata).then((res) => {
      //   this.cityList1 = res.data
      // })
      this.getCityOptions(listdata)
    },
    //  // 选择市item
    itemcity (e) {
      // console.log(e)
      this.formItem.cityCode = e.value // 市code
      this.formItem.cityName = e.label// 市名称
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData(this.arrDetails.chainId)
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData(this.arrDetails.chainId)
    },
    init () {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.formItem = {
        alertTitle: '新增节点',
        chainId: '',
        nodeId: '',
        nodeName: '',
        nodeAddress: '',
        nodeApiPort: '',
        nodeType: '',
        status: '',
        nodeP2pPort: '',
        location: '',
        provinceCode: null, // 省code
        cityCode: null, // 市code
        provinceName: '', // 省名称
        cityName: '', // 市名称
        diskdir: '',
        statusYun: ''
      }
      this.loading = false
      this.disabled = true
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    tipInfo (res) {
      if (res.code === '00000') {
        this.msgInfo('success', res.message, true)
        this.getInfo(this.eosChainId)
        this.modal = false
        this.accountModal = false
        this.ownerKeyShow = true
        this.activeKeyShow = true
      } else {
        this.msgInfo('error', res.message, true)
      }
    },
    getTableData (chainId) {
      getNodeList(this.tablePageParam, chainId).then(res => {
        if (res.code === '00000') {
          this.tableData = res.data.records
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          ++this.transferKey
        } else {
          this.msgInfo('error', res.message, true)
          this.reback()
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
        this.reback()
      })
    },
    getInfo (eosChainId) {
      getMultiLinkDetails(eosChainId).then(res => {
        if (res.code === '00000') {
          this.arrDetails = res.data
          this.getTableData(this.arrDetails.chainId)
          // 深拷贝数组
          this.reviseTableData = JSON.parse(JSON.stringify(res.data.manageAccountList))
          this.accountTableData = changeKey(res.data.manageAccountList)
          this.isUpgradeContract = res.data.isUpgradeContract
          ++this.transferKey
        } else {
          this.msgInfo('error', res.message, true)
          this.reback()
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
        this.reback()
      })
    },
    async editDetails (index) {
      // this.$nextTick(() => {
      //   this.$refs['formItem'].resetFields()
      // })
      this.loading = false
      this.modal = true
      this.formItem = {
        alertTitle: '修改链节点信息',
        chainId: this.arrDetails.chainId,
        nodeId: `${this.tableData[index].nodeId}`,
        nodeName: `${this.tableData[index].nodeName}`,
        nodeAddress: `${this.tableData[index].nodeAddress}`,
        nodeApiPort: `${this.tableData[index].nodeApiPort}`,
        nodeType: `${this.tableData[index].nodeType}`,
        status: `${this.tableData[index].status}`,
        nodeP2pPort: `${this.tableData[index].nodeP2pPort || ''}`,
        location: `${this.tableData[index].location}`,
        provinceCode: `${this.tableData[index].provinceCode || null}`, // 省code
        cityCode: `${this.tableData[index].cityCode || null}`, // 市code
        provinceName: `${this.tableData[index].provinceName || ''}`, // 省名称
        cityName: `${this.tableData[index].cityName || ''}`, // 市名称
        diskdir: `${this.tableData[index].diskDir || ''}`,
        statusYun: `${this.tableData[index].nodeStatus || ''}`,//运行状态
      }
      if (this.formItem.provinceName) { // 如果省级有默认值那么获取市级列表
        this.disabled = false
        let params = { // 获取市级列表
          pcode: this.formItem.provinceCode,
          name: ''
        }
        // getserchList(listdata).then((res) => {
        //   this.cityList1 = res.data
        // })
        await this.getCityOptions(params) // 市级列表获取成功
        // 如果市级有默认值 给市级赋默认值
        const { cityName, cityCode } = this.tableData[index]
        this.formItem.cityName = `${cityName || ''}` // 当后台返回 无效值时返回空
        this.formItem.cityCode = `${cityCode || null}` // 市code
      }
    },
    ok (name) {
      // console.log('提交')
      this.$refs[name].validate((valid) => {
        if (valid) {
          // if (this.formItem.provinceName !== '' && this.formItem.cityName === '') {
          //   this.formItem.cityName = this.cityList1[0].name
          //   this.formItem.cityCode = this.cityList1[0].code
          // }
          // console.log('form', this.formItem)
          if (this.formItem.cityName === '' || this.formItem.provinceName === '' || this.formItem.provinceCode == null || this.formItem.cityCode == 'null') {
            this.msgInfo('error', '省份和城市不能为空', true)
          } else {
            if (this.formItem.nodeP2pPort && !isNumber(this.formItem.nodeP2pPort)) {
              this.msgInfo('error', 'P2P端口必须为数字', true)
            } else {
              if (this.formItem.cityCode !== null && this.formItem.provinceCode !== null) {
                this.formItem.cityCode = Number(this.formItem.cityCode)
                this.formItem.provinceCode = Number(this.formItem.provinceCode)
              }

              this.formItem.location = this.formItem.provinceName + this.formItem.cityName
              this.formItem.nodeTypeKey = searchKey(this.formItem.nodeType, this.nodeTypeList)
              this.formItem.statusKey = searchKey(this.formItem.status, this.statusList)
              this.loading = true
              // console.log('编辑', this.formItem)
              if (this.formItem.nodeId) {
                reviseMultiChainNode(this.formItem).then(res => {
                  this.tipInfo(res)
                  this.loading = false
                }).catch(error => {
                  this.msgInfo('error', error.message, true)
                  this.loading = false
                })
              } else {
                // console.log('新增', this.formItem)
                this.formItem.chainId = this.arrDetails.chainId
                addMultiChainNode(this.formItem).then(res => {
                  this.tipInfo(res)
                  this.loading = false
                }).catch(error => {
                  this.msgInfo('error', error.message, true)
                  this.loading = false
                })
              }
            }
          }
        } else {
          this.msgInfo('error', '存在字段校验不符合规范，请检查！', true)
        }
      })
      // this.init()
      // this.$refs.formItem.resetFields()
    },
    cancel (name) {
      this.init()
      this.modal = false
    },
    add (name) {
      this.init()
      this.modal = true
    },
    reback () {
      this.$router.push({
        name: 'multilink_admin'
      })
      // this.$emit('handleTabRemove', this.$route.name, event)
    },
    addAccount () {
      this.$nextTick(() => {
        this.$refs['accountItem'].resetFields()
      })
      this.accountItem.alertTitle = '新增管理链账户'
      this.disabledInput = false
      this.accountModal = true
    },
    okAccount () {
      this.$refs['accountItem'].validate((valid) => {
        if (valid) {
          this.accountItem.chainId = this.arrDetails.chainId
          addManageAccount(this.accountItem).then(res => {
            this.tipInfo(res)
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        } else {
          this.msgInfo('error', '存在字段校验不符合规范，请检查！', true)
        }
      })
    },
    cancelAccount () {
      this.accountModal = false
      this.ownerKeyShow = true
      this.activeKeyShow = true
    },
    editAccount (index) {
      this.$nextTick(() => {
        this.$refs['accountItem'].resetFields()
      })
      this.accountModal = true
      var list = getKey(this.accountTableData[index].accountName, this.reviseTableData)
      this.accountItem = {
        alertTitle: '修改链账户信息',
        chainId: this.arrDetails.chainId,
        manageAccountName: `${this.accountTableData[index].accountName}`,
        ownerPrivateKey: list.ownerPrivateKey,
        activePrivateKey: list.activePrivateKey
      }
      this.disabledInput = true
    },
    handleOwnerKey () {
      this.ownerKeyShow = !this.ownerKeyShow
    },
    handleActiveKey () {
      this.activeKeyShow = !this.activeKeyShow
    },
    addClass (value) {
      return value === '启用' ? 'status_style_blue' : 'status_style'
    },
    deleteAccount (index) {
      // console.log('index', index, this.arrDetails.chainId, this.accountTableData[index].accountName)
      deleteManageAccount(this.arrDetails.chainId, this.accountTableData[index].accountName).then(res => {
        this.tipInfo(res)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    clickHyperion () {
      // this.$refs.hyperionModal.visible = true
      this.$refs.hyperionModal.getHyperioncConfigQuery(this.arrDetails.chainId)
    },
    clickElas () {
      // this.$refs.elasticsearchModal.visible = true
      this.$refs.elasticsearchModal.getConfigQuery(this.arrDetails.chainId)
    }
    // clickSuite () {
    //   // console.log(111)
    //   // this.$refs.hyperionModal.visible = true
    //   this.$refs.suiteModal.getSuiteConfigQuery(this.arrDetails.chainId)
    // }
  },
  created () {
    // 组件创建时就去请求下来列表  就赋值一次就行
    this.getselectOptions()// 获取下拉选项列表 省份
  },
  watch: {
    '$route.params.eosChainId' (val) {
      if (val) {
        this.getInfo(val)
      }
    }
    // modal1 (val) {
    //   console.log(val)
    //   // console.log(oldvalu)
    // }
    // if (this.provinces !== '') {
    //   this.disabled = false
    // } else if (this.model1 === '') {
    //   this.disabled = true
    // }

  },
  mounted () {
    this.statusList = statusList
    this.nodeTypeList = nodeTypeList
    if (this.eosChainId) {
      this.getInfo(this.eosChainId)
    }
    // this.$Message.config({
    //   top: 250,
    //   duration: 2
    // })
    this.chaindetail = this.$route.params
    // console.log(this.chaindetail)
  }
  // beforeRouteEnter (to, from, next) {
  //   // 在渲染该组件的对应路由被 confirm 前调用
  //   // 不！能！获取组件实例 `this`
  //   // 因为当钩子执行前，组件实例还没被创建
  //   if (from.name) {
  //     // console.log('from.name:', from.name)
  //     next()
  //   } else {
  //     next('/multilink_admin')
  //   }
  // }
  // beforeRouteLeave (to, from, next) {
  //   // 导航离开该组件的对应路由时调用
  //   // 可以访问组件实例 `this`
  //   // this.reback()
  //   // this.$emit('handleTabRemove', this.$route.name, event)
  //   // this.tabRemove(this.$route.name, event)
  //   // const state = {
  //   //   tabList: JSON.parse(localRead('tabList') || '[]')
  //   // }

  //   // const getTabListToLocal = state.tabList.filter(item => {
  //   //   return item.name !== 'multilink_details'
  //   // }).map(item => {
  //   //   return {
  //   //     name: item.name,
  //   //     path: item.path,
  //   //     meta: item.meta,
  //   //     params: item.params,
  //   //     query: item.query
  //   //   }
  //   // })

  //   next()
  // }
}
</script>

<style lang="less" scoped>
.mandatory {
  /deep/.ivu-form-item-label::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}
.ivu-select-input[disabled] {
  &:hover {
    &::after {
      content: "请先选择";
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-radius: 3px;
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}
ul,
li {
  padding-left: 7px;
  margin: 0;
  list-style: none;
}
li {
  font-size: 14px;
  margin-bottom: 5px;
  .status_style_blue {
    width: 100px;
    height: 30px;
    padding: 3px 15px;
    color: #ffffff;
    background-color: #2d8cf0;
    box-shadow: #c2bdbd 0px 0px 5px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
  }
  .status_style {
    width: 100px;
    height: 30px;
    padding: 3px 15px;
    color: #ffffff;
    background-color: #c2bdbd;
    box-shadow: #c2bdbd 0px 0px 5px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
  }
}
span {
  padding-left: 6px;
}
.info-title {
  font-size: 16px;
  font-weight: bold;
  vertical-align: middle;
  height: 18px;
  font-family: "Microsoft YaHei";
  line-height: 18px;
  color: #333333;
  margin: 10px 0 25px 0px;
  &.addflex {
    display: flex;
    justify-content: space-between;
    .btns {
      button {
        margin-right: 20px;
      }
    }
  }
}
.bs {
  float: left;
  width: 6px;
  height: 18px;
  background: #19c3a0;
  opacity: 1;
  border-radius: 3px;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.btnFA5151:hover {
  background-color: #fa5151 !important;
}
/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
</style>
