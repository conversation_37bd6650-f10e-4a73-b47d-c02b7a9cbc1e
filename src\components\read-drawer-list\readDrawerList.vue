<template>
  <div style="height:100%;width:100%">
    <List id="noticeScroll" style="max-height:calc(100vh - 140px);overflow-y:auto;padding-top:10px;" v-show="approvalList.length>0">
      <ListItem v-for="(item, i) in approvalList" :key="i">
        <ListItemMeta>
          <template slot="avatar">
            <img :class="{opc: item.processStatus}" :src="infoImg">
          </template>
          <template slot="title">
            <div :class="{opc: item.processStatus}">
              <div :class="item.arrowShow === true ? 'otherProductItem' : 'otherProductItem1'" ref="contentAbstractDom" @click="clickTo($event, item)" v-html="item.noticeMessage">
                <!-- <p v-html="item.noticeContent"></p> -->
              </div>
              <Icon :type="item.arrowShow === true ? 'ios-arrow-down':'ios-arrow-up'" class="icon" @click="clickOpen(item, i)" v-show="item.isOpen" />
            </div>
          </template>
          <template slot="description">
            <div style="float:left">{{item.createTime}}</div>
            <div style="float:right;padding-right:5px;">
              <Checkbox v-model="item.processStatus" @on-change="changeCheck(item)" :disabled="item.processStatus">已读</Checkbox>
            </div>
          </template>
        </ListItemMeta>
      </ListItem>
    </List>
    <div class="data-none" v-show="approvalList && approvalList.length === 0">
      <img :src="imagesUrl">
      <p class="title-none" style="">暂时没有收到待阅通知</p>
    </div>
    <p style="text-align:center;" v-if="this.count < this.total && this.pages > this.pageParam.pageIndex">
      <span style="font-size:8px;color:#57a3f3;cursor:pointer;" @click="getMore">
        更多<img :src="moreUrl" style="cursor:pointer;margin-left:2px;" @click="getMore">
      </span>
    </p>
  </div>
</template>
<script>
import { noticeManage, read } from '@/api/data'
import { mapActions } from 'vuex'
export default {
  name: 'readDrawList',
  data () {
    return {
      moreUrl: require('@/assets/img/arrow.png'),
      imagesUrl: require('@/assets/img/null.png'),
      infoImg: require('@/assets/notice/info.png'),
      single: false,
      showMore: false,
      isOpen: false,
      isShowMoreBtn: false,
      arrowType: 'ios-arrow-down',
      isOpc: false,
      approvalList: [],
      pageParam: {
        pageSize: 8,
        pageIndex: 1
      },
      count: 0,
      total: 0,
      pages: 0,
      showRed: 0,
      noticeIdList: []
    }
  },
  computed: {
    height () {
      return this.isOpen ? 'auto' : 50 + 'px'
    },
    getHidden () {
      if (this.count < this.total) {
        return ''
      } else {
        return 'display:none'
      }
    }
  },
  methods: {
    ...mapActions([
      'updateCur',
      'updateFlag'
    ]),
    getMore () {
      // if (this.noticeIdList.length > 0) {
      //   this.pageParam = {
      //     pageSize: 10,
      //     pageIndex: 1
      //   }
      //   this.noticeIdList = []
      //   this.getWaitRead()
      // } else {
      //   this.pageParam.pageIndex += 1
      //   this.getWaitRead(true)
      // }
      if (this.pageParam.pageIndex < this.pages) {
        this.pageParam.pageIndex += 1
        this.getWaitRead(true)
        const scrollDiv = document.getElementById('noticeScroll')
        scrollDiv.scrollTo({
          top: scrollDiv.scrollHeight,
          behavior: 'smooth'
        })
      }
    },
    getReadList () {
      this.noticeIdList = []
      if (this.approvalList.length > 0) {
        for (var item in this.approvalList) {
          if (!this.approvalList[item].processStatus) {
            this.approvalList[item].processStatus = true
            this.noticeIdList.push(this.approvalList[item].noticeId)
          }
        }
        if (this.noticeIdList.length === 0) {
          this.msgInfo('info', '您已勾选当前所有', true)
        } else {
          this.postRead(this.noticeIdList)
        }
      }
    },
    postRead (list, flag) {
      read(list).then(res => {
        if (res.code === '00000') {
          if (flag) {
            this.$router.go(0)
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    clickTo (e, item) {
      if (e.target.localName.toLowerCase() === 'span') {
        if (this.routerTo(item)) {
          if (!item.processStatus) {
            this.noticeIdList = []
            this.noticeIdList.push(item.noticeId)
            item.processStatus = true
            this.postRead(this.noticeIdList)
          }
          this.$emit('closeDrawer')
          this.noticeIdList = []
        }
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    clickOpen (item, i) {
      item.arrowShow = !item.arrowShow
      this.$set(this.approvalList, i, item)
    },
    transferRead (list) {
      for (var i in list) {
        if (list[i].processStatus === 'UNREAD') {
          list[i].processStatus = false
        } else if (list[i].processStatus === 'READ') {
          list[i].processStatus = true
        }
      }
    },
    changeCheck (item) {
      this.noticeIdList = []
      this.noticeIdList.push(item.noticeId)
      this.postRead(this.noticeIdList)
    },
    clickAll () {
      this.getReadList()
    },
    getWaitRead (flag) {
      noticeManage('WAIT_READ', this.pageParam).then(res => {
        if (res.code === '00000') {
          if (flag) {
            this.approvalList.push.apply(this.approvalList, res.data.records)
          } else {
            this.approvalList = res.data.records
          }
          this.count = this.approvalList.length
          this.total = res.data.total
          this.pages = res.data.pages
          this.transferRead(this.approvalList)
          this.showRed = this.count > 0 ? this.approvalList[0].showRed : 0
          this.$emit('getWaitRead', this.showRed)
          this.$nextTick(() => {
            for (var item in this.approvalList) {
              let offsetHeight = this.$refs.contentAbstractDom[item].offsetHeight
              if (offsetHeight > 50) {
                this.approvalList[item].arrowShow = true
                this.approvalList[item].isOpen = true
              } else {
                if (this.approvalList[item].isOpen === undefined) {
                  this.approvalList[item].isOpen = false
                }
              }
              this.$set(this.approvalList, item, this.approvalList[item])
            }
          })
        } else {
          this.approvalList = []
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    routerTo (item) {
      var jsonObj = item.linkParams ? JSON.parse(item.linkParams) : {}
      console.log('item:', jsonObj)

      // 链管理详情页
      if (item.bizType === 'NODE_ABNORMAL') {
        this.$router.push({
          name: 'multilink_details',
          params: {
            eosChainId: jsonObj.chainId,

          }
        })
        //  链账户资源管理
      } else if (item.bizType === 'ACCOUNT_RESULT') {
        this.$router.push({
          name: 'chain_table',
          query: {
            chainId: jsonObj.chainId,
            chainName: jsonObj.chainName,
            chainAccountName: jsonObj.chainAccountName
          }
        })
        // 合约部署审批
      } else if (item.bizType === 'DEPLOY_RESULT') {
        if (this.$route.path === '/contract_table') {
          if (!item.processStatus) {
            this.noticeIdList = []
            this.noticeIdList.push(item.noticeId)
            item.processStatus = true
            this.postRead(this.noticeIdList, true)
          } else {
            this.$router.go(0)
          }
        } else {
          this.$router.push({
            name: 'contract_table',
            params: {
              contractId: jsonObj.contractId
            }
          })
        }

        // 工单审批
      } else if (item.bizType === 'ORDER_RESULT') {
        this.$router.push({
          name: 'workorder_detail',
          params: {
            orderId: jsonObj.orderId
          }
        })
        // 所属租户变更
      } else if (item.bizType === 'TENANT_USER_UPDATE') {
        this.updateCur(2)
        this.$router.push({
          name: 'user_info',
          params: {
            cur: 2
          }
        })
        // 租户成员变动
      } else if (item.bizType === 'OWNER_TENANT_UPDATE') {
        this.$router.push({
          name: 'tenant_details',
          params: {
            tenantId: jsonObj.tenantId
          }
        })
      } else if (item.bizType === 'PASSWORD_RESET' || item.bizType === 'PASSWORD_MODIFY' || item.bizType === 'SHARE_OFF' || item.bizType === 'SHARE_UNBIND' || item.bizType === 'SHARE_DELETE_TENANT') {
        return false
      } else if (item.bizType === 'SHARE_RESULT') {
        this.$router.push({
          name: 'contract_details',
          params: {
            contractId: jsonObj.contractId
          }
        })
      } else if (item.bizType === 'SHARE_RECEIVE') {
        this.updateFlag(false)
        this.$router.push({
          name: 'contract_area',
          params: {
            isContractFlag: false
          }
        })
      } else if (item.bizType === 'SHARE_UPDATE') {
        this.$router.push({
          name: 'sharecontract_details',
          params: {
            shareRecordId: jsonObj.shareRecordId,
            receivedTenantId: item.receivedTenantId
          }
        })
        // 个人中心待阅
      } else if (item.bizType === 'CONTRACT_MARKET_ON' || item.bizType === 'CONTRACT_MARKET_OFF' || item.bizType === 'MARKET_OFF_SUCCESS' || item.bizType === 'MARKET_OFF_FAILED' || item.bizType === 'MARKET_REON_SUCCESS' || item.bizType === 'MARKET_REON_FAILED' || item.bizType === 'CONTRACT_APP_ON' || item.bizType === 'CONTRACT_APP_OFF') {
        this.$router.push({
          name: 'contract_market',
          params: {
            tabs: 'name2'
          }
        })
        // 链账户资源分配待阅
      } else if (item.bizType === 'ACCOUNT_ASSIGNED') {
        this.$router.push({
          name: 'chain_table'
        })
      } else {
        this.msgInfo('warning', item.bizType + '没找到匹配路由', true)
        return false
      }
      return true
    },
    initPageParam () {
      this.pageParam = {
        pageSize: 8,
        pageIndex: 1
      }
      this.count = 0
      this.total = 0
      this.pages = 0
    }
  },
  mounted () {
    this.getWaitRead()
  },
  // 注意以下代码不可以加
  // activated () {
  //   this.getWaitRead()
  // },

  watch: {
    cur: {
      handler (newVal, oldVal) {
        this.cur = newVal
      },
      deep: true,
      immediate: false
    },
    approvalList: {
      handler (newVal, oldVal) {
        var flag = true
        for (var i in newVal) {
          if (!newVal[i].processStatus) {
            flag = false
            break
          }
        }
        this.$emit('getAllCheck', flag)
      },
      deep: true,
      immediate: false
    }
  }
}
</script>

<style lang="less" scoped>
.opc {
  opacity: 0.5;
}
img {
  margin: 0 auto;
}
.otherProductItem {
  cursor: default;
  width: 260px;
  line-height: 25px;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  padding-right: 9px;
  margin-top: -5px;
  margin-left: -5px;
}
.otherProductItem1 {
  cursor: default;
  width: 260px;
  line-height: 25px;
  padding-right: 8px;
  margin-top: -5px;
}
.icon {
  position: absolute;
  width: 32px;
  right: 6px;
  margin-top: -15px;
}
.data-none {
  position: relative;
  text-align: center;
  vertical-align: middle;
  margin: 0 auto;
  padding-top: 50px;
  .title-none {
    font-size: 8px;
    color: #bdbbbb;
  }
}
</style>
