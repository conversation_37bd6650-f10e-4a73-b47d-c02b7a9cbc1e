<template>
  <div>
    <Row v-show="approvalList.length>0" class="overflow">
      <Timeline>
        <TimelineItem v-for="(item, i) in approvalList" :key="i">
          <p class="title-3">
            <span>{{getTimePeriod(item.createTime)}}</span>
            <span class="publishDate">{{item.createTime}}</span>
          </p>
          <div :class="item.arrowShow ? 'otherProductItem' : 'otherProductItem1'" ref="abstractDom" @click="clickTo($event, item)" v-html="item.noticeMessage">
          </div>
        </TimelineItem>
      </Timeline>
    </Row>
    <!-- <Row :style="approvalList.length===0?'display:none':'display:block'">
                </Row> -->
    <div class="data-noned" v-show="approvalList && approvalList.length === 0">
      <img style="max-width: 75%;max-height: 75%;" :src="imagesurl">
      <p class="title-none" style="">暂无工作待办</p>
    </div>
    <Row :style="approvalList.length===0?'display:none':'display:block'">
      <Button icon="md-add" style="margin:5px 10px 0 10px;width:95%;height:4vh;position:relative;color:#519FF2;border:1px solid #519FF2" type="default" @click="Workdo">创建工作待办</Button>
    </Row>
  </div>
</template>
<script>
import { noticeManage } from '@/api/data'
import { mapActions } from 'vuex'
export default {
  data () {
    return {
      imagesurl: require('@/assets/img/null.png'),
      pageParam: {
        pageSize: 8,
        pageIndex: 1
      },
      approvalList: []
    }
  },
  methods: {
    getWaitDeal (flag) {
      noticeManage('WAIT_DEAL', this.pageParam).then(res => {
        console.log(res)
        if (res.code === '00000') {
          if (flag) {
            this.approvalList.push.apply(this.approvalList, res.data.records)
          } else {
            this.approvalList = res.data.records
          }
          // this.count = this.approvalList.length
          // this.total = res.data.total
          // this.pages = res.data.pages
          // this.showRed = this.count > 0 ? this.approvalList[0].showRed : 0
          // this.$emit('getWaitDeal', this.showRed)
          // this.$nextTick(() => {
          //   for (var item in this.approvalList) {
          //     let offsetHeight = this.$refs.abstractDom[item].offsetHeight
          //     if (offsetHeight > 50) {
          //       this.approvalList[item].arrowShow = true
          //       this.approvalList[item].isOpen = true
          //     } else {
          //       if (this.approvalList[item].isOpen === undefined) {
          //         this.approvalList[item].isOpen = false
          //       }
          //     }
          //     this.$set(this.approvalList, item, this.approvalList[item])
          //   }
          // })
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getDay (value) {
      var dt = new Date(value.replace(/-/g, '/'))
      dt.setMonth(dt.getMonth() + 1)
      dt.setDate(0)
      return dt.getDate()
    },
    getTimePeriod (value) {
      var dateBegin = new Date(value.replace(/-/g, '/'))
      var maxDate = this.getDay(value)
      var dateEnd = new Date()
      var diff = dateEnd - dateBegin
      var meg = ''
      if (diff / 1000 / 60 < 1) {
        meg = Math.floor(diff / 1000) + '秒前'
      } else if (diff / 1000 / 60 >= 1 && diff / 1000 / 3600 < 1) {
        meg = Math.floor(diff / 1000 / 60) + '分钟前'
      } else if (diff / 1000 / 3600 >= 1 && diff / 1000 / (3600 * 24) < 1) {
        meg = Math.floor(diff / 1000 / 3600) + '小时前'
      } else if (diff / 1000 / (3600 * 24) >= 1) {
        if (dateEnd.getFullYear() - dateBegin.getFullYear() >= 1) {
          if (dateEnd.getFullYear() - dateBegin.getFullYear() === 1 && dateEnd.getMonth() < dateBegin.getMonth()) {
            meg = dateEnd.getMonth() + 12 - dateBegin.getMonth() + '个月前'
          } else {
            meg = dateEnd.getFullYear() - dateBegin.getFullYear() + '年前'
          }
        } else if (dateEnd.getMonth() - dateBegin.getMonth() >= 1) {
          if (dateEnd.getMonth() - dateBegin.getMonth() === 1 && dateEnd.getDate() < dateBegin.getDate()) {
            meg = dateEnd.getDate() + maxDate - dateBegin.getDate() + '天前'
          } else {
            meg = dateEnd.getMonth() - dateBegin.getMonth() + '个月前'
          }
        } else {
          meg = dateEnd.getDate() - dateBegin.getDate() + '天前'
        }
      }
      return meg
    },
    ...mapActions([
      'updateCur'
    ]),
    Workdo () {
      this.updateCur(3)
      this.$router.push({
        name: 'user_info',
        params: {
          cur: 3
        }
      })
    },
    clickTo (e, item) {
      if (e.target.localName.toLowerCase() === 'span') {
        if (this.routerTo(item)) {
          this.$emit('closeDrawer')
        }
      }
    },
    routerTo (item) {
      // 链账户审批
      if (item.bizType === 'ACCOUNT_AUDIT') {
        this.$router.push({
          name: 'chain-approvel'
        })
        // 合约部署审批
      } else if (item.bizType === 'DEPLOY_AUDIT') {
        if (this.$route.path === '/contract-approvel') {
          this.$router.go(0)
        } else {
          this.$router.push({
            name: 'contract-approvel',
            params: {
              tabs: 'name1'
            }
          })
        }

        // 工单审批
      } else if (item.bizType === 'ORDER_AUDIT') {
        this.$router.push({
          name: 'workorder-approvel'
        })
        // 密码过期
      } else if (item.bizType === 'PASSWORD_EXPIRE') {
        this.updateCur(1)
        this.$router.push({
          name: 'user_info',
          params: {
            cur: 1
          }
        })
      } else if (item.bizType === 'SHARE_AUDIT') {
        // console.log('item:', item)
        this.$router.push({
          name: 'sharecontract-approvel',
          params: {
            tabs: 'name1'
          }
        })
        // 链账户资源管理
      } else if (item.bizType === 'ACCOUNT_UNASSIGNED') {
        // console.log('item:', item)
        this.$router.push({
          name: 'token_admin'
        })
        // 个人中心待办
      } else if (item.bizType === 'CONTRACT_APP_ON' || item.bizType === 'MARKET_AUDIT' || item.bizType === 'MARKET_AUDIT_OFF' || item.bizType === 'MARKET_AUDIT_REON') {
        this.$router.push({
          name: 'shelves_approval'
        })
      } else {
        this.msgInfo('warning', item.bizType + '没找到匹配路由', true)
        return false
      }
      return true
    }
  },
  mounted () {
    this.getWaitDeal()
  }
}
</script>

<style lang="less" scoped>
/deep/.ivu-timeline-item-tail {
  border-left: 1px dashed #e8eaec;
}
/deep/.ivu-timeline-item-head {
  width: 11px;
  height: 11px;
  border: 3px solid #7ba0f5;
  border-radius: 50%;
  opacity: 0.6;
  background-color: #e0e6f3;
  z-index: 999;
}
/deep/.ivu-timeline-item-content {
  padding: 0px 0px 0px 20px;
}
::-webkit-scrollbar {
  width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
.overflow {
  overflow-y: scroll;
  height: calc(15vh + 10px);
}
.work {
  height: calc(32vh + 10px);
  width: 100%;
  border: 2px solid #f5f4f4;
  padding-top: 16px;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 6px;
  /deep/.ivu-row {
    width: 100%;
  }
  .ivu-timeline {
    width: 100%;
  }
  .data-noned {
    position: relative;
    text-align: center;
    vertical-align: middle;
    margin: 0 auto;
    width: 65%;
    // padding-top: 50px;
    .title-none {
      font-size: 8px;
      color: #bdbbbb;
    }
  }
}
.ivu-timeline {
  padding: 16px;
}
.time {
  font-size: 14px;
  font-weight: bold;
}
.content {
  padding-left: 5px;
}
/deep/.ivu-btn-dashed {
  color: #57a3f3;
  border-color: #57a3f3;
}
.title-3 {
  font-size: 12px;
  color: #9b9b9b;
  // margin-top:-15px;
  // margin-left:13px;
  font-family: "Microsoft YaHei";
  font-weight: 400;
  .publishDate {
    float: right;
    margin-right: 7%;
  }
}
.otherProductItem {
  cursor: default;
  width: 210px;
  line-height: 25px;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  padding-right: 9px;
  margin-top: -5px;
  margin-left: -5px;
}
.otherProductItem1 {
  cursor: default;
  width: 210px;
  line-height: 25px;
  padding-right: 8px;
  margin-top: -5px;
  margin-left: -5px;
}
</style>
