<template>
  <div>
    <div class="cz_header">
      <div class="cz_ss">
        <div class="cz_hao">
          <!-- <Select v-model="model3" style="width: 100px" placeholder="存证号">
            <Option
              v-for="item in cityList"
              :value="item.value"
              :key="item.value"
              @click.native="click_value(item.value)"
              >{{ item.label }}</Option
            >
          </Select> -->
          <span class="btn_title1">交易名称</span>
          <Input placeholder="输入信息" v-model="search_value" class="title_one" />
        </div>
        <div class="s_type">
           <span class="btn_title">交易类型</span>
          <Select v-model="model3" style="width: 100px" placeholder="全部">
            <Option
              v-for="item in cityList1"
              :value="item.value"
              :key="item.value"
              @click.native="click_type(item.value)"
              >{{ item.label }}</Option
            >
          </Select>
        </div>
        <div class="sl_time">
           <span class="btn_title">交易时间</span>
          <DatePicker
            format="yyyy-MM-dd"
            type="daterange"
            placement="bottom-end"
            :editable='false'
            placeholder="开始日期~结束日期"
            style="width: 220px"
            @on-change="timeout_click"
            v-model="time"
          ></DatePicker>
        </div>
        <div class="b_search">
          <Button
            type="primary"
            class="btn_search"
            icon="ios-search"
            @click.native="input_value"
            >搜索</Button
          >
          <Button type="primary" ghost icon="md-sync" @click.native="reset"
            >重置</Button
          >
        </div>
      </div>
      <Button type="success" ghost @click="go" icon="md-add">新增交易</Button>
    </div>
    <!-- table -->
    <div class="cz_table">
      <edit-table-mul
        :columns="historyColumns"
        v-model="historyData"
      ></edit-table-mul>
      <Page
        :total="dataCount"
        :page-size="PageParam.pageSize"
        :page-size-opts="[10, 20, 40, 60, 100]"
        show-sizer
        show-total
        show-elevator
        class="paging"
        @on-change="changepage"
        style="text-align: right"
        @on-page-size-change="pageSizeChange"
      ></Page>
    </div>
    <!-- 记录 -->
    <Modal v-model="record" title="存证记录" width="65%">
      <edit-table-mul :columns="columns1" v-model="data1"></edit-table-mul>
    </Modal>
  </div>
</template>
<script>
import { subSearch } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
export default {
  name: 'transaction_commit',
  components: {
    EditTableMul
  },
  data () {
    return {
      ajaxHistoryData: [],
      // 初始化信息总条数
      dataCount: 0,
      // 每页显示多少条
      // pageSize: 10,
      option_value: '全部',
      record: false,
      time: '',
      columns1: [
        {
          title: '数据来源',
          key: 'otherPlatforms'
        },
        {
          title: '操作方',
          key: 'userLoginId'
        },
        // {
        //   title: "用户姓名",
        //   key: "realName",
        //   tooltip: true,
        // },
        {
          title: '组织名称',
          key: 'organization'
        },
        {
          title: '存证时间',
          key: 'createTime',
          width: 180
        },
        {
          title: '存证状态',
          key: 'status'
        }
      ],
      data1: [],
      historyColumns: [
        {
          title: '交易Id',
          key: 'trxId',
          tooltip: true
        },
        {
          title: '交易名称',
          key: 'title',
          tooltip: true
        },
        {
          title: '交易类型',
          key: 'type'
        },
        {
          title: '交易时间',
          key: 'confirmTime'
        }

        // {
        //   title: "操作",
        //   key: "action",
        //   width: 150,
        //   align: "center",
        //   render: (h, params) => {
        //     return h("div", [
        //       h(
        //         "Button",
        //         {
        //           props: {
        //             type: "text",
        //             size: "small",
        //           },
        //           style: {
        //             marginRight: "8px",
        //             color: "#3D73EF",
        //             border: "1px solid #3D73EF",
        //           },
        //           on: {
        //             click: () => {
        //               this.surDetails(params.index);
        //             },
        //           },
        //         },
        //         "详情"
        //       ),
        //       h(
        //         "Button",
        //         {
        //           props: {
        //             type: "text",
        //             size: "small",
        //           },
        //           style: {
        //             marginRight: "8px",
        //             color: "#3D73EF",
        //             border: "1px solid #3D73EF",
        //           },
        //           on: {
        //             click: () => {
        //               this.sur_records(params.index);
        //             },
        //           },
        //         },
        //         "记录"
        //       ),
        //     ]);
        //   },
        // },
      ],
      historyData: [],
      model3: '',
      model3Count: null,
      sur_rec: [],
      // 上链时间
      chain_time: '',
      // 输入框存证号
      search_mark: '',
      // 输入框存证号
      search_name: '',
      // 下拉框
      lable: '存证号',
      search_value: '',
      // 分页
      PageParam: {
        pageIndex: 1,
        pageSize: 10
      },
      // 下拉框
      cityList: [
        {
          value: '存证号',
          label: '存证号'
        },
        {
          value: '存证名称',
          label: '存证名称'
        }
      ],
      // 存证类型下拉框
      cityList1: [
        {
          value: '全部',
          label: '全部'
        },
        {
          value: '内容',
          label: '内容'
        },
        {
          value: '文件',
          label: '文件'
        }
      ]
    }
  },
  methods: {
    changepage (index) {
      // 改变页码时触发
      this.PageParam.pageIndex = index
      this.getTablist() // 获取表格列表
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      // 当前页条数
      this.PageParam.pageSize = size
      this.getTablist() // 获取表格列表
    },
    //
    go () {
      this.$router.push({ path: '/new_record' })
    },
    // 详情
    // surDetails(index) {
    //   this.$router.push({
    //     name: "survival_details",
    //     params: {
    //       survivalId: index,
    //       content: this.historyData[index],
    //     },
    //   });
    // },
    // 记录
    // sur_records(index) {
    //   const surIndex = {
    //     code: this.sur_rec[index].code,
    //   };
    //   this.record = true;
    //   recordGet(surIndex).then((res) => {
    //     let statusR = {
    //       0: "提交存证",
    //       1: "数据上链中",
    //       2: "交易确认",
    //       3: "交易失败",
    //       4: "数据签署",
    //     };
    //     let Platforms = {
    //       1: "CMBaaS",
    //       2: "智慧中台",
    //     };
    //     let lists = res.data.map((item) => {
    //       return {
    //         ...item,
    //         status: statusR[item.status],
    //         otherPlatforms: Platforms[item.platformId],
    //         userLoginId: item.status === 0 ? item.userLoginId : "system",
    //       };
    //     });
    //     this.data1 = lists;
    //   });
    // },
    ok () {
      this.record = false
    },
    cancel () {
      this.record = false
    },
    // 重置
    reset () {
      this.search_value = ''
      this.model3 = ''
      this.time = ''
      this.model3Count = null
      this.chain_time = []
      this.search_mark = ''
      this.search_name = ''
      this.getTablist()
    },
    // 下拉存证
    // click_value(e) {
    //   this.lable = e;
    //   this.search_mark = "";
    //   this.search_name = "";
    // },
    // 下拉类型
    click_type (e) {
      this.model3 = e
      switch (e) {
        case '全部':
          this.model3Count = null
          break
        case '文件':
          this.model3Count = '0'
          break
        case '内容':
          this.model3Count = '1'
          break
        default:
          break
      }
      if (e === '全部') this.model3 = ''
    },
    // 输入框值
    input_value () {
      //   if (this.lable === "存证号") {
      //     this.search_mark = this.search_value;
      //     this.search_name = "";
      //   } else if (this.lable === "存证名称") {
      //     this.search_name = this.search_value;
      //     this.search_mark = "";
      //   }
      this.getTablist() // 获取表格列表
    },
    timeout_click (e) {
      this.chain_time = e
    },
    // 请求的方法
    getTablist () {
      let params = {
        // code: this.search_mark, // 存证号
        title: this.search_value, // 名称
        type: this.model3Count, // 存证类型
        status: '',
        startTime: this.chain_time[0],
        endTime: this.chain_time[1],
        operatorUserId: '',
        pageParam: this.PageParam, // 分页
        platformId: 1, // 用户登录ID
        userLoginId: this.userLoginId
      }

      subSearch(params).then((res) => {
        const { code, data } = res
        this.sur_rec = res.data.records
        if (code === '00000') {
          const { records } = res.data
          // 类型 对应关系
          let typeRale = {
            0: '文件',
            1: '内容'
          }
          let statusRale = {
            0: '提交存证',
            1: '数据上链中',
            2: '交易确认',
            3: '交易失败',
            4: '数据签署'
          }
          let list = records.map((item) => {
            return {
              ...item,
              type: typeRale[item.type],
              status: statusRale[item.status]
            }
          })

          this.historyData = list
          this.dataCount = data.total
        } else {
          console.log('数据获取异常：', res)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  created () {
    this.userLoginId = localStorage.getItem('userLoginId')
    this.getTablist() // 获取表格列表
  },
  activated () {
    /** 执行页面数据刷新的方法 */
    // reload();
    this.getTablist()
  }
}
</script>

<style lang="less" scoped>
.ivu-card-body {
  .ivu-input-wrapper {
    line-height: 34px;
  }
}
.cz_header {
  display: flex;
  margin-top: 10px;
  justify-content: space-between;

  .cz_ss {
    display: flex;
  }
  .s_type {
    display: flex;
  }
}
.b_search {
  display: flex;
}
.cz_hao,
.s_type,
.sl_time {
  margin-right: 3%;
}
.cz_hao {
  display: flex;
}
.sl_time {
  display: flex;
}
.btn_search {
  margin-right: 10px;
}
// table
.cz_table {
  margin-top: 2% !important;
}
button.btn {
  position: absolute;
  right: 10px;
  margin: 0 10px;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
.btn_title{
width: 80px;
height: 33px;
text-align: center;
line-height: 33px;
display: inline-block;
}
.btn_title1{
  width: 110px;
height: 33px;
text-align: center;
line-height: 33px;
display: inline-block;
}
</style>
