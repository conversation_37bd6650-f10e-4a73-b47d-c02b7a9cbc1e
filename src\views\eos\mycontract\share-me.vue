<template>
  <div class="contract">
    <p style="text-align:right;">
      <!-- <b style="float:left;margin: 10px;">智能合约</b> -->
      <span>发布租户：</span>
       <Select v-model="shareTenantId" clearable placeholder="全部" style="width:100px;text-align:left;margin-right:10px" label-in-value  @on-change="searchList">
        <Option v-for="item in shareTenantIdList" :value="item.tenantId" :key="item.tenantId">{{item.tenantName}}</Option>
      </Select>
      <Input style="width:250px;vertical-align:baseline;" placeholder="可输入合约名称或应用名称查询"  v-model="queryName" @keyup.enter="searchList" @keyup.enter.native="searchList">
         <Icon type="ios-search" slot="suffix" @click="searchList"/>
      </Input>
      <!-- <Button type="primary" @click="searchList" icon="ios-search">查询</Button> -->
    </p>
    <div class="share-flex" v-if="tableData.length>0">
      <Card class="share-card"  :bordered="false" v-for="(item,index) in tableData" :key="index">
          <div class="share-card-item" style="text-align:left">
              <h3>{{item.contractReadableName}}（合约链账户：{{item.contractAccountName}}）</h3>
              <p>合约名称：{{item.contractName}}</p>
              <p>链名称：{{item.chainName}}</p>
              <p v-if="item.contractBrief.length>=14">
                <span class="label-span">应用简介：</span>
                <Tooltip max-width="200" transfer="true"  placement="bottom-start" :content="item.contractBrief">
                    <span class="p-span">{{item.contractBrief}}</span>
                </Tooltip>
              </p>
              <p v-else>应用简介：{{item.contractBrief}}</p>
              <p>发布租户：{{item.shareTenantName}}</p>
              <p>接收租户：{{item.receivedTenantName}}</p>
              <Button v-if="item.receiveStatus==='UNRECEIVED'" type="primary" style="height:35px;" long @click="getContract(item)">获取</Button>
              <Button v-else-if="item.receiveStatus==='RECEIVED'" style="height:35px;" long @click="showDetails(item)">已获取，查看详情</Button>
          </div>
      </Card>
    </div>
    <div class="share-flex-none" v-else>
        <img class="imgs" :src="imagesurl"/>
        <p class="contract-none">暂无共享给我的合约</p>
      </div>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[6,12,30,60,120]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;"/>

  </div>
</template>

<script>
import { getContractPower, shareList, contractShareObtain, shareTenantList } from '@/api/data'
export default {
  name: 'my_share',
  components: {},
  data () {
    return {
      shareTenantId: '',
      shareTenantIdList: [],
      imagesurl: require('@/assets/img/null.png'),
      transId: '', // 去详情时的contractId
      chainId: this.$route.params.chainId ? this.$route.params.chainId : '',
      queryName: '',
      tablePageParam: { pagetotal: 0, pageSize: 6, pageIndex: 1 },
      tableData: []
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    getContract (item) {
      // 获取操作
      const params = {
        shareRecordId: item.shareRecordId,
        receivedTenantId: item.receivedTenantId
      }
      contractShareObtain(params).then(res => {
        if (res.code === '00000') {
          this.$Modal.confirm({
            content: `已成功获取合约${item.contractName}，可在【智能合约】页面的“获取的共享合约”分类下查看。`,
            cancelText: '继续获取',
            okText: '查看详情',
            onOk: () => {
              // 查看详情
              this.goList()
              this.showDetails(item)
            },
            onCancel: () => {
              this.goList()
              // this.$Message.info('Clicked cancel')
            }
          })
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        console.log('contractShareObtain.error===>', error)
      })
    },
    async okRequest () {
      getContractPower(this.arrContract).then(res => {
        if (res.code !== '00000') {
          this.msgInfo('error', res.message, true)
        } else {
          this.transId = res.data.contractId
          this.showSuccess()
        }
      }).catch(error => {
        console.log('getContractPower.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    // 去往列表
    goList () {
      this.getTableData()
    },
    // 获取租户列表
    getShareTenantList () {
      const params = {
        bizType: 'CONTRACT_SHARE'
      }
      shareTenantList(params).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.shareTenantIdList = res.data
        }
      }).catch(error => {
        console.log('shareTenantId.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    getTableData () {
      const params = {
        shareTenantId: this.shareTenantId,
        queryName: this.queryName,
        queryType: 'SHARED_ME_CONTRACT',
        pageParam: this.tablePageParam
      }
      shareList(params).then(res => {
        // console.log('shareList===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.tableData = res.data.records
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        }
      }).catch(error => {
        console.log('shareList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    searchList () { this.getTableData() },
    showDetails (item) {
      this.$router.push({
        name: 'sharecontract_details',
        params: { shareRecordId: `${item.shareRecordId}`,
          receivedTenantId: `${item.receivedTenantId}`,
          languageType: `${item.languageType}` }
      })
    }
  },
  mounted () {
    this.getShareTenantList()
    this.getTableData()
  },
  deactivated () {
  },
  activated () {
    this.getShareTenantList()
    this.getTableData()
  }
}
</script>

<style lang="less" scoped>
.share-flex{
  min-height:60vh;
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  margin-bottom: 20px;
  .share-card{
    width:calc((100% - 20px)/3);
    margin-right:10px;
    margin-bottom:10px;
    &:nth-of-type(3n){
      margin-right:0;
    }
    h3{
      margin-bottom: 15px;
      height:40px;
    }
    p{
      line-height: 14px;
      min-height:40px;
      display: flex;
      overflow: hidden;
      align-items: center;
       .label-span{
        display: inline-block;
        min-width: 70px;
      }
      .p-span{
        display: inline-block;
        width:calc(100% - 90px);
        max-width: calc((100vw - 320px)/3);
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap;
      }
    }
  }
}
.share-flex-none{
  text-align: center;
  min-height:60vh;
  img {
    height:40vh;
    max-width: 80%;
    max-height: 80%;
  }

  .contract-none {
    font-size: 8px;
    color: #d4d3d3;
    margin-top: -5px;
    position: relative;
  }
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61,115,239,.8);
  color: #fff!important;
}
/deep/.ivu-btn-text:active{
  background-color: #3D73EF;
}
</style>
