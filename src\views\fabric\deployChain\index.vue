<template>
  <div class="page">
    <CreatHeader :isDeploy4='isDeploy4' :isDeploy3='isDeploy3' :isDeploy2='isDeploy2' :isAllSuccess='isAllSuccess'></CreatHeader>
    <div class="content">
      <div class="base">
        <div class="title">
          <img :src="infoIcon" class="image">
          <span class="text">环境部署</span>
        </div>
        <div class="soloForm" v-if="chainType == 'SOLO_CMRI'">
          <!-- <div class="list">
            <span class="label">镜像完整性检测：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:mockForm.Mirroring == '',reslove:mockForm.Mirroring == 'success',reject:(mockForm.Mirroring != 'success' && mockForm.Mirroring != '')}">{{mockForm.Mirroring | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(mockForm.Mirroring)" class="image" v-else>
          </div> -->
          <!-- <div class="list">
            <span class="label">资源检测(占用 &lt;70%)：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:mockForm.Resource == '',reslove:mockForm.Resource == 'success',reject:(mockForm.Resource != 'success' && mockForm.Resource != '')}">{{mockForm.Resource | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(mockForm.Resource)" class="image" v-else>
          </div> -->
          <div class="list">
            <span class="label">基本环境部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:soloForm.Env == '',reslove:soloForm.Env == 'success',reject:(soloForm.Env != 'success' && soloForm.Env != '')}">{{soloForm.Env | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(soloForm.Env)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">CA节点部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:soloForm.Ca == '',reslove:soloForm.Ca == 'success',reject:(soloForm.Ca != 'success' && soloForm.Ca != '')}">{{soloForm.Ca | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(soloForm.Ca)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Orderer节点部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:soloForm.Order == '',reslove:soloForm.Order == 'success',reject:(soloForm.Order != 'success' && soloForm.Order != '')}">{{soloForm.Order | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(soloForm.Order)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Peer节点部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:soloForm.Peers == '',reslove:soloForm.Peers == 'success',reject:(soloForm.Peers != 'success' && soloForm.Peers != '')}">{{soloForm.Peers | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(soloForm.Peers)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Rest接口部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:soloForm.Rest == '',reslove:soloForm.Rest == 'success',reject:(soloForm.Rest != 'success' && soloForm.Rest != '')}">{{soloForm.Rest | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(soloForm.Rest)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Mysql数据库部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:soloForm.Mysql == '',reslove:soloForm.Mysql == 'success',reject:(soloForm.Mysql != 'success' && soloForm.Mysql != '')}">{{soloForm.Mysql | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(soloForm.Mysql)" class="image" v-else>
          </div>
          <!-- <div class="list">
            <span class="label">节点可信检测：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:mockForm.Node == '',reslove:mockForm.Node == 'success',reject:(mockForm.Node != 'success' && mockForm.Node != '')}">{{mockForm.Node | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(mockForm.Node)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">端口可信检测：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:mockForm.Prot == '',reslove:mockForm.Prot == 'success',reject:(mockForm.Prot != 'success' && mockForm.Prot != '')}">{{mockForm.Prot | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(mockForm.Prot)" class="image" v-else>
          </div> -->
          <div class="list">
            <span class="label">安全检测：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:soloForm.Explorer == '',reslove:soloForm.Explorer == 'success',reject:(soloForm.Explorer != 'success' && soloForm.Explorer != '')}">{{soloForm.Explorer | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(soloForm.Explorer)" class="image" v-else>
          </div>
          <div class="hint" v-if="isShowNetWork">网络部署失败，请检查您的网络或返回上一步重新设置网络。</div>
        </div>
        <div class="soloForm" v-if="chainType == 'KAFKA_CMRI'">
          <!-- <div class="list">
            <span class="label">镜像完整性检测：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:mockForm.Mirroring == '',reslove:mockForm.Mirroring == 'success',reject:(mockForm.Mirroring != 'success' && mockForm.Mirroring != '')}">{{mockForm.Mirroring | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(mockForm.Mirroring)" class="image" v-else>
          </div> -->
          <!-- <div class="list">
            <span class="label">资源检测(占用 &lt;70%)：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:mockForm.Resource == '',reslove:mockForm.Resource == 'success',reject:(mockForm.Resource != 'success' && mockForm.Resource != '')}">{{mockForm.Resource | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(mockForm.Resource)" class="image" v-else>
          </div> -->
          <div class="list">
            <span class="label">基本环境部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:KafkaForm.Env == '',reslove:KafkaForm.Env == 'success',reject:(KafkaForm.Env != 'success' && KafkaForm.Env != '')}">{{KafkaForm.Env | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(KafkaForm.Env)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Zookeeper部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:KafkaForm.Zookeepers == '',reslove:KafkaForm.Zookeepers == 'success',reject:(KafkaForm.Zookeepers != 'success' && KafkaForm.Zookeepers != '')}">{{KafkaForm.Zookeepers | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(KafkaForm.Zookeepers)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Kafka部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:KafkaForm.Kafkas == '',reslove:KafkaForm.Kafkas == 'success',reject:(KafkaForm.Kafkas != 'success' && KafkaForm.Kafkas != '')}">{{KafkaForm.Kafkas | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(KafkaForm.Kafkas)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">CA节点部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:KafkaForm.Cas == '',reslove:KafkaForm.Cas == 'success',reject:(KafkaForm.Cas != 'success' && KafkaForm.Cas != '')}">{{KafkaForm.Cas | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(KafkaForm.Cas)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Orderer节点部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:KafkaForm.Orders == '',reslove:KafkaForm.Orders == 'success',reject:(KafkaForm.Orders != 'success' && KafkaForm.Orders != '')}">{{KafkaForm.Orders | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(KafkaForm.Orders)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Peer节点部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:KafkaForm.Peers == '',reslove:KafkaForm.Peers == 'success',reject:(KafkaForm.Peers != 'success' && KafkaForm.Peers != '')}">{{KafkaForm.Peers | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(KafkaForm.Peers)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Rest接口部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:KafkaForm.Rest == '',reslove:KafkaForm.Rest == 'success',reject:(KafkaForm.Rest != 'success' && KafkaForm.Rest != '')}">{{KafkaForm.Rest | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(KafkaForm.Rest)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Mysql数据库部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:KafkaForm.Mysql == '',reslove:KafkaForm.Mysql == 'success',reject:(KafkaForm.Mysql != 'success' && KafkaForm.Mysql != '')}">{{KafkaForm.Mysql | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(KafkaForm.Mysql)" class="image" v-else>
          </div>
          <!-- <div class="list">
            <span class="label">节点可信检测：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:mockForm.Node == '',reslove:mockForm.Node == 'success',reject:(mockForm.Node != 'success' && mockForm.Node != '')}">{{mockForm.Node | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(mockForm.Node)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">端口可信检测：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:mockForm.Prot == '',reslove:mockForm.Prot == 'success',reject:(mockForm.Prot != 'success' && mockForm.Prot != '')}">{{mockForm.Prot | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(mockForm.Prot)" class="image" v-else>
          </div> -->
          <div class="list">
            <span class="label">安全检测：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:KafkaForm.Explorer == '',reslove:KafkaForm.Explorer == 'success',reject:(KafkaForm.Explorer != 'success' && KafkaForm.Explorer != '')}">{{KafkaForm.Explorer | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(KafkaForm.Explorer)" class="image" v-else>
          </div>
          <div class="hint" v-if="isShowNetWork">网络部署失败，请检查您的网络或返回上一步重新设置网络。</div>
        </div>
        <div class="soloForm" v-if="chainType == 'BFT_CMRI'">
          <!-- <div class="list">
            <span class="label">镜像完整性检测：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:mockForm.Mirroring == '',reslove:mockForm.Mirroring == 'success',reject:(mockForm.Mirroring != 'success' && mockForm.Mirroring != '')}">{{mockForm.Mirroring | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(mockForm.Mirroring)" class="image" v-else>
          </div> -->
          <!-- <div class="list">
            <span class="label">资源检测(占用 &lt;70%)：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:mockForm.Resource == '',reslove:mockForm.Resource == 'success',reject:(mockForm.Resource != 'success' && mockForm.Resource != '')}">{{mockForm.Resource | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(mockForm.Resource)" class="image" v-else>
          </div> -->
          <div class="list">
            <span class="label">基本环境部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:bftForm.Env == '',reslove:bftForm.Env == 'success',reject:(bftForm.Env != 'success' && bftForm.Env != '')}">{{bftForm.Env | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(bftForm.Env)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">BFT部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:bftForm.Bfts == '',reslove:bftForm.Bfts == 'success',reject:(bftForm.Bfts != 'success' && bftForm.Bfts != '')}">{{bftForm.Bfts | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(bftForm.Bfts)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">CA节点部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:bftForm.Cas == '',reslove:bftForm.Cas == 'success',reject:(bftForm.Cas != 'success' && bftForm.Cas != '')}">{{bftForm.Cas | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(bftForm.Cas)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Orderer节点部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:bftForm.Orders == '',reslove:bftForm.Orders == 'success',reject:(bftForm.Orders != 'success' && bftForm.Orders != '')}">{{bftForm.Orders | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(bftForm.Orders)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Peer节点部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:bftForm.Peers == '',reslove:bftForm.Peers == 'success',reject:(bftForm.Peers != 'success' && bftForm.Peers != '')}">{{bftForm.Peers | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(bftForm.Peers)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Rest接口部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:bftForm.Rest == '',reslove:bftForm.Rest == 'success',reject:(bftForm.Rest != 'success' && bftForm.Rest != '')}">{{bftForm.Rest | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(bftForm.Rest)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">Mysql数据库部署：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:bftForm.Mysql == '',reslove:bftForm.Mysql == 'success',reject:(bftForm.Mysql != 'success' && bftForm.Mysql != '')}">{{bftForm.Mysql | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(bftForm.Mysql)" class="image" v-else>
          </div>
          <!-- <div class="list">
            <span class="label">节点可信检测：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:mockForm.Node == '',reslove:mockForm.Node == 'success',reject:(mockForm.Node != 'success' && mockForm.Node != '')}">{{mockForm.Node | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(mockForm.Node)" class="image" v-else>
          </div>
          <div class="list">
            <span class="label">端口可信检测：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:mockForm.Prot == '',reslove:mockForm.Prot == 'success',reject:(mockForm.Prot != 'success' && mockForm.Prot != '')}">{{mockForm.Prot | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(mockForm.Prot)" class="image" v-else>
          </div> -->
          <div class="list">
            <span class="label">安全检测：</span>
            <span class="status reject" v-if="isShowErrow">失败</span>
            <span class="status" v-else :class="{pending:bftForm.Explorer == '',reslove:bftForm.Explorer == 'success',reject:(bftForm.Explorer != 'success' && bftForm.Explorer != '')}">{{bftForm.Explorer | getStatusText}}</span>
            <img :src="rejectImgUrl" class="image" v-if="isShowErrow">
            <img :src="getStatus(bftForm.Explorer)" class="image" v-else>
          </div>
          <div class="hint" v-if="isShowNetWork">网络部署失败，请检查您的网络或返回上一步重新设置网络。</div>
        </div>
      </div>
    </div>
    <div class="btn-wrap" v-if="isShowFinish">
      <el-button plain v-if="isShowCreat" @click="goCreate">继续创建</el-button>
      <el-button plain v-else @click="goBack">上一步</el-button>
      <el-button type="primary" class="handle-btn blue-btn" @click="goFinish">返回实例管理</el-button>
    </div>
    <!-- <div v-if="loading" class="BoxLoading" v-loading="loading" element-loading-text="部署中"></div> -->
    <!-- <countDown v-if="isShowIcon" :state="countState" :countTime="countTime" :text="countText" @getCountDown="getCountDown"></countDown> -->
  </div>
</template>

<script>
import {getDeployChainProgress} from '@/api/baascore/creatChain'
import {getClusterList,checkChainLimit} from "@/api/baascore/overview";
import CreatHeader from '../creatChain/compontents/header'
export default {
  components: {
    CreatHeader
  },
  data() {
    return {
      countState:'',
      countTime:2,
      countText:'',
      isShowIcon:false,
      chainName:'',
      chainType:'',
      infoIcon:require('@/assets/chainManage_images/overview/infoIcon.png'),
      pendingImgUrl:require('@/assets/chainManage_images/overview/pending.gif'),
      resloveImgUrl:require('@/assets/chainManage_images/overview/reslove.png'),
      rejectImgUrl:require('@/assets/chainManage_images/overview/reject .png'),
      soloForm:{
        Env:'',
        Ca:'',
        Order:'',
        Peers:'',
        Rest:'',
        Mysql:'',
        Explorer:'',
      },
      KafkaForm :{
        Env:'',
        Cas:'',
        Orders:'',
        Peers:'',
        Rest:'',
        Mysql:'',
        Explorer:'',
        Zookeepers:'',
        Kafkas:'',
      },
      bftForm :{
        Env:'',
        Cas:'',
        Bfts:'',
        Orders:'',
        Peers:'',
        Rest:'',
        Mysql:'',
        Explorer:'',
      },
      // mockForm:{
      //   Prot:'',
      //   Node:'',
      //   Resource:'',
      //   Mirroring:'success'
      // },
      ServiceId:'',
      isShowErrow:false,
      isShowNetWork:false,
      isGoBack:false,
      isShowFinish:false,
      isAllSuccess:false,
      isShowCreat:false,
      loading:true,
      isDeploy2:true,
      isDeploy3:true,
      isDeploy4:false,
      timer:null
    };
  },

  mounted() {
    var that = this
    that.chainName = sessionStorage.getItem('params')
    that.chainType = sessionStorage.getItem('chainType') || 'SOLO_CMRI'
    that.ServiceId = sessionStorage.getItem('ServiceId')
    // this.$route.meta.title = that.chainName
    if(this.chainType && this.ServiceId) {
      this.getDeployChainProgress()
    }
    else {
      this.$router.replace({
        path:'/guide/step'
      })
    }
    // setTimeout(() =>{
    //   this.$set(this.mockForm,'Resource','success')
    // },1000)
    // setTimeout(() =>{
    //   this.$set(this.mockForm,'Node','success')
    // },5000)
    // setTimeout(() =>{
    //   this.$set(this.mockForm,'Prot','success')
    // },7000)
  },
  methods:{
    getCountDown(type) {
      this.isShowIcon = false
    },
    goCreate() {
      sessionStorage.clear()
      getClusterList().then(res =>{
        if(res.code == 200 && res.data) {
          var params = {
            ClusterName:res.data[0].ClusterName
          }
          checkChainLimit(params).then(res =>{
            if(res.code == 200) {
              this.$router.replace({
                path:'/guide/step'
              })
            }else{
              this.$message.warning('当前资源不足，无法创建实例。')
            }
          })
        }else {
          this.$message.error('数据获取失败，请重新加载！')
        }
      })
    },
    getStatus(type) {
      if(type == '') {
         return  this.pendingImgUrl
      } else if(type == 'success') {
        return this.resloveImgUrl;
      } else {
        return this.rejectImgUrl
      }
    },
    getIsAllSuccess(obj) {
      var isOK = null
      for(var key in obj) {
        if(obj[key] == '') {
          isOK = true
        }
      }
      if(isOK) {
        this.isShowFinish = false
        this.timer =  setTimeout(() =>{
          this.getDeployChainProgress()
        },1000)

      } else {
        for(var key in obj) {
          if(obj[key] != 'success') {
            this.isShowNetWork = true
          }
        }
        this.isDeploy4 = true
        this.isShowFinish = true
        this.isGoBack = false
        this.isAllSuccess = true
        this.isShowCreat = true
        this.loading = false
        clearTimeout(this.timer)
      }
    },
    getDeployChainProgress() {
      var query = {
        ServiceId:this.ServiceId,
      }
      var chainType = this.chainType
      getDeployChainProgress(query).then(res =>{
        if(res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if(chainType == 'SOLO_CMRI') {
            this.soloForm = res.data
          }
          if(chainType == 'KAFKA_CMRI') {
            this.KafkaForm = res.data
          }
          if(chainType == 'BFT_CMRI') {
            this.bftForm = res.data
          }
          var obj = res.data
          this.getIsAllSuccess(obj)
        } else {
          this.loading = false
          this.isShowErrow = true
          this.isShowNetWork = true
          this.isDeploy4 = true
          this.isGoBack = true
          this.isShowFinish = true
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '部署失败，请检查网络！'
          this.$message.error('部署失败，请检查网络！')
        }
      }).catch((error) =>{
        this.loading = false
        this.isShowNetWork = true
        this.isDeploy4 = true
        this.isShowErrow = true
        this.isGoBack = true
        this.isShowFinish = true
        /*if (error.code == 'ECONNABORTED' && error.message.indexOf('timeout')!=-1) {
          Message.error('请求超时，请检查网络环境')
        }else {
          Message.error('网络异常,请检查网络')
        }*/
        // this.$router.replace({
        //   path:'/'
        // })
      })
    },
    goBack() {
      if(this.isGoBack) {
        this.$router.go(-1)
      }
    },
    goFinish() {
      if(this.isShowFinish) {
        sessionStorage.clear()
        this.$router.push({
          path:'/instance'
        })
      }
    }
  },
  filters:{
    getStatusText(type) {
      if(type == '') {
        return '进行中';
      } else if(type == 'success') {
        return '完成'
      }else {
        return '失败'
      }
    }
  },
  destroyed() {
    clearTimeout(this.timer)
    this.timer = null
  },
}
</script>

<style lang="less" scoped>
.page {
  width: 100%;
  min-height: 100%;
  .content {
    width: 100%;
    .base {
      margin-top: 26px;
      .title {
        .image {
          width: 3px;
          height: 14px;
          vertical-align: middle;
          margin-right: 5px;
        }
        .text {
          // font-size: 20px;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #333333;
          vertical-align: middle;
        }
      }
      .soloForm {
        margin: 26px auto 0px;
        padding: 50px 230px;
        width: 100%;
        text-align: center;
        background: #fff;
        // background-image: url('../../../assets/chainManage_images/overview/deployBg.png');
        // background-repeat: no-repeat;
        // background-size: 100% 100%;
        .list {
          display: flex;
          align-items: center;
          justify-content: center;
          // font-size: 17px;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          margin-bottom: 20px;
          .label {
            width: 200px;
            text-align: right;
            color: #333333;
          }
          .status {
            width: 60px;
            margin:0 40px;
            &.pending {
              color: #F2C94C;
            }
            &.reslove {
              color: #00ADA2;
            }
            &.reject  {
              color: #F04134;
            }
          }
          .image {
            width: 28px;
            height: 28px;
          }
        }
        .hint {
          // font-size: 17px;
          font-size: 14px;
          color: #F04134;
          margin-top: 36px;
        }
      }
    }
  }
  .btn-wrap {
    background: #fff;
    //margin:66px auto;
    text-align: center;
    display: flex;
    justify-content: center;
    padding-bottom: 50px;
  }
  .el-button.el-button--primary {
    background-color: #3D73EF;
    border-color: #3D73EF;
  }
  .last-step {
    width: 210px;
    height: 64px;
    background: #fff;
    border: 3px solid #E7ECEF;
    border-radius: 4px;
    // font-size: 22px;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #666;
    line-height: 58px;
    cursor: pointer;
    margin-right: 180px;
  }
  .next-step {
    width: 192px;
    height: 64px;
    background: #337DFF;
    border: 2px solid;
    border-radius: 4px;
    // font-size: 22px;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 58px;
    cursor: pointer;
  }
}
.BoxLoading{
  position:fixed;
  top:0;
  right:0;
  bottom:0;
  left:0;
  z-index: 10000
}
.BoxLoading /deep/ .el-loading-mask{
  background: rgba(0, 0, 0, 0.2) !important;
}
.BoxLoading /deep/ .el-loading-spinner {
  top:32%;
}
.BoxLoading /deep/ .el-loading-spinner .circular{
  width:60px !important;
  height: 60px !important;
}
.BoxLoading /deep/ .el-loading-spinner .path{
  stroke:#fff !important;
}
.BoxLoading /deep/ .el-loading-spinner .el-loading-text{
  color:#fff !important;
  // font-size:24px;
  font-size: 14px;
  font-weight:bold;
}
</style>
