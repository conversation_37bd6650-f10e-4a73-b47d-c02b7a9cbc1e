FROM node:14-buster as builder
WORKDIR /code
COPY . /code/
RUN npm config set registry "https://registry.npmmirror.com" \
  && npm cache clean --force \
  && npm install @vue/cli -g \
  && npm install @vue/cli-service-global -g \
  && npm install \
  && npm run build

FROM openresty/openresty:alpine
COPY --from=builder /code/dist /usr/share/nginx/html
COPY nginx.conf /usr/local/openresty/nginx/conf/nginx.conf


