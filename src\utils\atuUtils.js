/**
 * 存储localStorage
 */
export const setStore = (name, content) => {
    if (!name) return;
    if (typeof content !== 'string') {
        content = JSON.stringify(content);
    }
    window.localStorage.setItem(name, content);
}

/**
 * 获取localStorage
 */
export const getStore = name => {
    if (!name) return;
    var value = window.localStorage.getItem(name);
    if (value !== null) {
        try {
            value = JSON.parse(value);
        } catch (e) {
            value = value;
        }
    }
    return value;
}
/**
 * <AUTHOR>
 * @Description //TODO
 * @Date  2021/8/2 15:31
 * @Param 获取url上的参数
 * @return
 **/
export const getUrlParam = (sUrl, sKey) => {
    var result = {};
    sUrl.replace(/(\w+)=(\w+)(?=[&|#])/g, function (ele, key, val) {
        if (!result[key]) {
            result[key] = val;
        } else {
            var temp = result[key];
            result[key] = [].concat(temp, val);
        }
    })
    if (!sKey) {
        return result;
    } else {
        return result[sKey] || '';
    }
}


/**
 * 删除localStorage
 */
export const removeStore = name => {
    if (!name) return;
    window.localStorage.removeItem(name);
}


/**
 * 返回随机数
 * @param {Number} min 最小
 * @param {Number} max 最大
 * @returns {Number}
 */
export const getRandomNumber = (min,max)=>{
    return Math.floor(Math.random() * (max - min + 1) + min)
}

/**
 * 两个数组交集
 * @param {Array} arrOne 最小
 * @param {Array} arrtwo 最大
 * @returns {Array}
 */
export const getIntersection = (arrOne,arrtwo)=>{
    return arrOne.filter(item=>new Set(arrtwo).has(item))
}

/**
 * 清楚空元素
 * @param {Array} arr 操作数组
 * @returns {Array}
 */
export const deleteArrayNullEle = (arr)=>{
    let _arr = arr

    for(var i = 0;i<_arr.length;i++){
        if(_arr[i]==''||_arr[i]==null||typeof(_arr[i])==undefined){
            _arr.splice(i,1);
            i=i-1;
        }
    }
    return _arr
}

/**
 * 读取base64
 */
export const  readFile = file => {
    console.log(file)
    //var file = this.files[0];
    //判断是否是图片类型
    if (!/image\/\w+/.test(file.raw.type)) {
        alert("只能选择图片");
        return false;
    }
    var reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function (e) {
        var filedata = {
            filename: file.name,
            filebase64: e.target.result
        }
        alert(e.target.result)
    }
}

   export const setItem=(name, content) => {
        if (!name) return;
        if (typeof content !== 'string') {
            content = JSON.stringify(content);
        }
        window.sessionStorage.setItem(name, content);
    };

export const getItem=(name)=> {
        if (!name) return;
        return window.sessionStorage.getItem(name);
    };

export const removeItem=(name) =>{
        if (!name) return;
        window.sessionStorage.removeItem(name);
    };




/**
 * 返回年月日字符串
 * @param {string} date 具体日期变量
 * @param {string} dateType 返回格式
 * @returns {string}
 */
export function getFormatDates(date, dateType='yyyy-mm-dd MM:mm:ss') {
  if (!date) return ' '
    let dateObj = new Date(date)
    let month = dateObj.getMonth() + 1
    let strDate = dateObj.getDate()
    let hours = dateObj.getHours()
    let minutes = dateObj.getMinutes()
    let seconds = dateObj.getSeconds()
    if (month >= 1 && month <= 9) {
        month = '0' + month
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = '0' + strDate
    }
    if (hours >= 0 && hours <= 9) {
        hours = '0' + hours
    }
    if (minutes >= 0 && minutes <= 9) {
        minutes = '0' + minutes
    }
    if (seconds >= 0 && seconds <= 9) {
        seconds = '0' + seconds
    }

    let dateText = dateObj.getFullYear() + '年' + (dateObj.getMonth() + 1) + '月' + dateObj.getDate() + '日'
    if (dateType == 'yyyy-mm-dd') {
        dateText = dateObj.getFullYear() + '-' + (dateObj.getMonth() + 1) + '-' + dateObj.getDate()
    }
    if (dateType == 'yyyy/mm/dd') {
        dateText = dateObj.getFullYear() + '/' + (dateObj.getMonth() + 1) + '/' + dateObj.getDate()
    }
    if (dateType == 'yyyy/mm/dd MM:mm') {
        dateText = dateObj.getFullYear() + '/' + (dateObj.getMonth() + 1) + '/' + dateObj.getDate() + ' ' + hours + ':' + minutes
    }
    if (dateType == 'yyyy/mm/dd MM:mm:ss') {
        dateText = dateObj.getFullYear() + '/' + (dateObj.getMonth() + 1) + '/' + dateObj.getDate() + ' ' + hours + ':' + minutes + ':' + seconds
    }
    if (dateType == 'yyyy.mm.dd') {
        dateText = dateObj.getFullYear() + '.' + (dateObj.getMonth() + 1) + '.' + dateObj.getDate()
    }
    if (dateType == 'YYYY-MM-DD') {
        dateText = dateObj.getFullYear() + '-' + month + '-' + strDate
    }
    if (dateType == 'yyyy-mm-dd MM:mm:ss') {
        dateText = dateObj.getFullYear() + '-' + month + '-' + strDate + ' ' + hours + ':' + minutes + ':' + seconds
    }

    if (dateType == 'mm-dd MM:mm:ss') {
        dateText = month + '-' + strDate + ' ' + hours + ':' + minutes + ':' + seconds
    }
    if (dateType == 'yyyy-mm-dd HH:mm') {
        dateText = dateObj.getFullYear() + '-' + month + '-' + strDate + ' ' + hours + ':' + minutes
    }
    if (dateType == 'yyyy年mm月dd日 MM:mm:ss') {
        dateText = dateObj.getFullYear() + '年' + month + '月' + strDate + '日' + ' ' + hours + ':' + minutes + ':' + seconds
    }
    if (dateType == 'yyyymmddMMmmss') {
        dateText = dateObj.getFullYear() + month + strDate + hours + minutes + seconds
    }
    if (dateType == 'yyyy-mm-dd 23:59:59') {
        dateText = dateObj.getFullYear() + '-' + (dateObj.getMonth() + 1) + '-' + dateObj.getDate() + ' ' + 23 + ':' + 59 + ':' + 59
    }
    return dateText
}







































