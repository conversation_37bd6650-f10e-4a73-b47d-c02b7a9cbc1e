<template>
  <div class="chainMaker">
    <ChainMaker v-if="currentTab==='chainMaker_index'" />
    <router-view v-else/>
  </div>
</template>

<script>
import ChainMaker from './chainMaker.vue'
export default {
  name: 'chainMaker_index',
  components: {
    ChainMaker
  },
  data () {
    return {
      excludeArr: []
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
