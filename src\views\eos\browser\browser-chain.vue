<template>
<div class="chain">
  <div style="padding:20px 0px 40px 10px;">
    <div style="float:left;">
        <Select filterable :class="className" @on-open-change="selectClassName" v-model="chain.chainId" placeholder="选择目标链" @on-change="changeChain" style="width:280px">
            <Option v-for="item in chainIdList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
            <Option :value="chain.chainId" :label="chain.chainId"  :disabled="true" v-if="pageParam.pageIndex < pages && chainIdList.length>0" style="text-align:center">
              <span @mouseover="handleReachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
            </Option>
            <Option :value="chain.chainId" :label="chain.chainId"  :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
              <span style="font-size:8px;">已加载全部</span>
            </Option>
          </Select>
      </div>
      <div style="float:right;">
          <Input
            prefix="ios-search"
              type="text"
              style="width:400px;margin-right:-1px;"
              v-model="searchData"
              placeholder="区块高度/交易哈希/账户名称"
              @keyup.enter.native="browserBlur(searchData)">
          </Input>
          <Button type="primary" style="padding:2px;width:75px;height:31px;" @click="browserBlur(searchData)"> 搜索</Button>
      </div>
  </div>
  <div v-show="!isShow">
    <Card class="back" style="margin-top:10px;" :bordered="false">
      <div class="title">
        <div class="bs"></div>
        <div>账户信息</div>
      </div>
      <!-- <p class="title bs">账户信息</p> -->
      <p class="title-1">账户名称:<span class="title-3">{{ accountName }}</span></p>
      <p class="title-1">创建者:<span class="title-2">{{ creator }}</span></p>
      <p class="title-1">创建时间:<span class="title-2">{{ created }}</span></p>
      <Row style="background:#FBFBFD;padding:10px;">
        <Col span="7">
          <div>
            <p style="font-family:PingFangSC-Regular;font-size:14px; color:#A5A4BF">
              CPU
            </p>
            <P><span style="color:#52C7AA;font-size:28px;">{{ cpuUsed }}/{{ cpuMax }}</span></P>
            <i-Progress :percent="cpup1"  stroke-color="#52C7AA"  hide-info />
          </div>
        </Col>
        <Col span="7" offset="1">
          <div>
            <p style="font-family:PingFangSC-Regular;font-size:14px; color:#A5A4BF">
              RAM
            </p>
            <P><span style="color:#6A65F8;font-size:28px;" >{{ ramUsage }}/{{ ramQuota }}</span></P>
            <i-Progress :percent="ramp2"  stroke-color="#6A65F8"   hide-info />
          </div>
        </Col>
        <Col span="7" offset="1">
          <div>
              <p style="font-family:PingFangSC-Regular;font-size:14px; color:#A5A4BF">
                NET
              </p>
              <P><span style="color:#FFBC24;font-size:28px;" >{{ netUsed }}/{{ netMax }}</span></P>
              <i-Progress :percent="netp3" class="netp3Style" stroke-color="#FFBC24" hide-info />
          </div>
        </Col>
      </Row>
    </Card>
    <Card style="margin-top:20px;">
      <!-- <p class="title bs" >权限和公钥</p> -->
      <div class="title">
        <div class="bs"></div>
        <div>权限和公钥</div>
      </div>
      <Row style="margin-top:5px;width:100%;">
        <Col span="4">
          <Card :bordered="false" shadow>
            <p slot="title" style="font-size:13px;">权限</p>
            <p style="font-size:13px;"> <span > owner </span >
              <Avatar style="background:white; color:#515a6e" />
            </p>
            <p style="font-size:13px;"> <span >active </span >
              <Avatar style="background:white; color:#515a6e" />
            </p>
          </Card>
        </Col>
        <Col span="18">
          <Card :bordered="false" shadow>
            <p slot="title" style="font-size:13px;">公钥</p>
            <p style="font-size:13px;"><span >{{ ownerKey }}</span>
              <!-- <img class="imgs" :src="imagesurl1">            -->
              <Avatar  v-show="ownerKey !== '' && openflag === 0" style="background:white; color:#515a6e" @click.native="displayKey1" >
                <i class="ri-eye-close-line"></i>
              </Avatar>
              <Avatar v-show="ownerKey !== '' && openflag !== 0" style="background:white; color:#515a6e" @click.native="displayKey1" >
                <i class="ri-eye-line"></i>
              </Avatar>
            </p>
            <p style="font-size:13px;"><span >{{ activeKey }}</span>
              <Avatar v-show="activeKey !== '' && openflag2 === 0" style="background:white; color:#515a6e" @click.native="displayKey2">
              <i class="ri-eye-close-line"></i>
              </Avatar>
              <Avatar v-show="activeKey !== '' && openflag2 !== 0" style="background:white; color:#515a6e" @click.native="displayKey2">
              <i class="ri-eye-line"></i>
              </Avatar>
            </p>
          </Card>
        </Col>
      </Row>
    </Card>
    <Card style="margin-top:20px;">
      <div class="title"><div class="bs"></div><div>操作</div></div>
      <edit-table-mul :columns="columns" v-model="tableData" :key="transferKey" style="margin:2px;font-size:13px" :height="getHeight"></edit-table-mul>
      <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;"/>
    </Card>
    </div>
    <div v-show="isShow" class="show-style">
      <img class="imgs" :src="showUrl">
      <p class="msg-style">{{showMsg}}</p>
    </div>
</div>
</template>
<script>
import 'remixicon/fonts/remixicon.css'
import { getAccountData, getActionData, getChainIdList } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import { isBlockNum, isAccount, isTrxId, isStar } from '@/lib/check'
import { transform, transformCpu } from '@/lib/transformUnit'
import { mapActions } from 'vuex'
import JsonViewer from 'vue-json-viewer'
export default {
  name: 'browser_chain',
  components: {
    EditTableMul,
    JsonViewer
  },
  data () {
    return {
      copyable: { copyText: '复制', copiedText: '已复制' },
      isShow: false,
      showUrl: require('@/assets/img/null.png'),
      showMsg: '',
      searchBack: '',
      chain: JSON.parse(this.$route.query.chain) || {
        chainId: 0,
        chainName: ''
      },
      actionsValue: '',
      searchData: '',
      hisPath: 'browser_index',
      goFlag: true,
      totalActions: 0,
      totalPages: 0,
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      ownerKey: '**********',
      openflag: 0,
      openflag2: 0,
      activeKey: '**********',
      transferKey: 0,
      ownerPublicKey: '',
      activePublicKey: '',
      created: '',
      creator: '',
      amount: '',
      cpuUsed: 0,
      cpuMax: 0,
      netUsed: 0,
      netMax: 0,
      ramUsage: 0,
      ramQuota: 0,
      cpup1: 10,
      ramp2: 40,
      netp3: 20,
      accountName: this.$route.query.accountName ? this.$route.query.accountName : '',
      columns: [
        { key: 'actionTime', title: '时间' },
        { key: 'actionName', title: '名称' },
        { key: 'contract', title: '合约账户' },
        { key: 'authorization', title: '授权信息' },
        {
          title: '数据',
          key: 'data',
          minWidth: 200,
          render: (h, params) => {
            if (params.row.data) {
              return h('div', [
                h(JsonViewer, {
                  props: {
                    value: params.row.data,
                    copyable: this.copyable,
                    expandDepth: 0
                  },
                  style: { background: 'transparent' }
                })
              ])
            }
          }
        // { key: 'data',
        //   title: '数据',
        //   width: 500,
        //   className: 'tools-ellipsis',
        //   // tooltip: true, // 只有超过展示长度的数据，用弹窗提示
        //   // tooltipMaxWidth: 500
        //   // 以下代码不管数据长短都弹提示窗
        //   // ellipsis: true,
        //   render: (h, params) => {
        //     let texts = ''
        //     let dataStr = ''
        //     dataStr = JSON.stringify(params.row.data)
        //     if (dataStr !== null) {
        //       if (dataStr.length > 100) {
        //         texts = dataStr.substring(0, 100) + '...'
        //       } else {
        //         texts = dataStr
        //       }
        //     }
        //     return dataStr.length < 55 ? h('span', dataStr) : h('Tooltip', {
        //       props: {
        //         maxWidth: 400,
        //         transfer: true,
        //         transferClassName: 'tools-scroll-auto'
        //       }
        //     }, [
        //       texts,
        //       h('span', {
        //         slot: 'content',
        //         style: {
        //           whiteSpace: 'normal',
        //           wordBreak: 'break-all'
        //         }
        //       }, dataStr)
        //     ])
        //   }
        }
      ],
      tableData: [],
      className: 'select-style1',
      pageParam: { pageTotal: 0, pageSize: 60, pageIndex: 1 },
      pages: 0,
      chainIdList: [],
      imgUrl: require('@/assets/img/arrow.png'),
      size: 0,
      routeChain: JSON.parse(this.$route.query.chain) || {
        chainId: 0,
        chainName: ''
      }
    }
  },
  computed: {
    getHeight: function (value) {
      if (this.tableData.length === 0) {
        return 90
      } else if (this.tableData.length < 5) {
        return 90 + 48 * (this.tableData.length - 1)
      } else {
        return 90 + 48 * 5
      }
    }
  },
  methods: {
    ...mapActions([
      'updateChain'
    ]),
    browserBlur (val) {
      if (isBlockNum(val)) {
        this.$router.push({
          name: 'browser_block',
          query: {
            blockNum: val,
            chain: JSON.stringify(this.chain),
            tag: true
          }
        })
      } else if (isAccount(val)) {
        this.goFlag = false
        this.accountName = val
        this.getChainAccountDatas(val)
      } else if (isTrxId(val)) {
        this.$router.push({
          name: 'browser_trade',
          query: {
            trxId: val,
            chain: JSON.stringify(this.chain),
            tag: true
          }
        })
      } else {
        if (val === '') {
          this.msgInfo('warning', '未输入任何查询信息，请检查！', true)
          this.showMsg = '未输入任何查询信息，请检查！'
        } else {
          this.msgInfo('warning', '输入信息有误，请检查！', true)
          this.showMsg = '输入信息有误，请检查！'
        }
      }
    },
    // 获取账户信息
    getChainAccountDatas (value) {
      this.actionsValue = value
      getAccountData(this.chain.chainId, value, 10, 1).then(res => {
        // console.log('进入获取账户信息程序main:value===>', value)
        if (res.code === '00000') {
          this.isShow = false
          this.accountName = res.data.accountName
          this.created = res.data.created
          this.creator = res.data.creator
          this.amount = res.data.amount ? res.data.amount : 0
          this.cpuUsed = res.data.cpuUsed > 0 ? res.data.cpuUsed : 0
          this.cpuMax = res.data.cpuMax > 0 ? res.data.cpuMax : 0
          this.netUsed = res.data.netUsed > 0 ? res.data.netUsed : 0
          this.netMax = res.data.netMax > 0 ? res.data.netMax : 0
          this.ramUsage = res.data.ramUsage > 0 ? res.data.ramUsage : 0
          this.ramQuota = res.data.ramQuota > 0 ? res.data.ramQuota : 0
          this.ownerPublicKey = res.data.ownerPublicKey === null ? '' : res.data.ownerPublicKey
          this.activePublicKey = res.data.activePublicKey === null ? '' : res.data.activePublicKey
          this.totalActions = res.data.totalActions
          ++this.transferKey
          // 转成***
          let tmp = ''
          var i = 0
          var n = this.ownerPublicKey.length
          for (i = 0; i < n; i++) {
            tmp += '*'
          }
          this.ownerKey = tmp

          let atmp = ''
          var j = 0
          var m = this.activePublicKey.length
          for (j = 0; j < m; j++) {
            atmp += '*'
          }
          this.activeKey = atmp

          // cpu等单个数值的比例
          this.cpup1 = 0
          this.ramp2 = 0
          this.netp3 = 0
          if (this.cpuMax) {
            this.cpup1 = (this.cpuUsed / this.cpuMax) * 100
          }
          if (this.ramQuota) {
            this.ramp2 = (this.ramUsage / this.ramQuota) * 100
          }
          if (this.netMax) {
            this.netp3 = (this.netUsed / this.netMax) * 100
          }

          // 单位换算
          this.cpuUsed = transformCpu(this.cpuUsed)
          this.cpuMax = transformCpu(this.cpuMax)
          this.netUsed = transform(this.netUsed)
          this.netMax = transform(this.netMax)
          this.ramUsage = transform(this.ramUsage)
          this.ramQuota = transform(this.ramQuota)
          // 清空actions列表
          this.tableData = []
          this.tablePageParam.pagetotal = 0
          // 获取action信息
          if (this.totalActions) {
            getActionData(this.chain.chainId, value, this.totalActions, 10, 1).then(resaction => {
              if (resaction.code === '00000') {
                this.tableData = resaction.data.actions
                this.tablePageParam.pagetotal = resaction.data.total
                // console.log('获取actions数据===>this.tableData', this.tableData)
              } else {
                this.msgInfo('error', resaction.message, true)
              }
            }).catch(erroraction => {
              this.msgInfo('error', erroraction.message, true)
            })
          }
        } else {
          this.isShow = true
          if (res.code === 'A1002') {
            this.msgInfo('error', res.message, true)
            this.showMsg = '账户[' + value + ']，' + '该账户信息在[' + this.chain.chainName + ']上不存在！'
          } else {
            this.msgInfo('error', res.message, true)
            this.showMsg = '查询账户[' + value + ']，' + res.message
          }
        }
      }).catch(error => {
        this.isShow = true
        this.showMsg = error.message
        this.msgInfo('error', error.message, true)
      })
    },

    pageChange (index) {
      this.tablePageParam.pageIndex = index
      getActionData(this.chain.chainId, this.actionsValue, this.totalActions, this.tablePageParam.pageSize, this.tablePageParam.pageIndex).then(resaction => {
        // console.log('pageChange===>this.tablePageParam.pageIndex', this.tablePageParam.pageIndex)
        if (resaction.code === '00000') {
          this.tableData = resaction.data.actions
          this.tablePageParam.pagetotal = resaction.data.total
          // console.log('pageChange===>this.tableData', this.tableData)
        } else {
          this.msgInfo('error', resaction.message, true)
        }
      }).catch(erroraction => {
        this.msgInfo('error', erroraction.message, true)
      })
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      // console.log('pageSizeChange===>this.tablePageParam.pageSize', this.tablePageParam.pageSize)
      getActionData(this.chain.chainId, this.actionsValue, this.totalActions, this.tablePageParam.pageSize, this.tablePageParam.pageIndex).then(resaction => {
        if (resaction.code === '00000') {
          this.tableData = resaction.data.actions
          this.tablePageParam.pagetotal = resaction.data.total
          // console.log('pageSizeChange===>this.tableData', this.tableData)
        } else {
          this.msgInfo('error', resaction.message, true)
        }
      }).catch(erroraction => {
        this.msgInfo('error', erroraction.message, true)
      })
    },

    displayKey1 () {
      if (isStar(this.ownerKey)) {
        this.ownerKey = this.ownerPublicKey
        this.openflag = 1
      } else {
        let tmp = ''
        var i = 0
        var n = this.ownerPublicKey.length
        for (i = 0; i < n; i++) {
          tmp += '*'
        }
        this.ownerKey = tmp
        this.openflag = 0
      }
    },
    displayKey2 () {
      if (isStar(this.activeKey)) {
        this.activeKey = this.activePublicKey
        this.openflag2 = 1
      } else {
        let tmp = ''
        var i = 0
        var n = this.activePublicKey.length
        for (i = 0; i < n; i++) {
          tmp += '*'
        }
        this.activeKey = tmp
        this.openflag2 = 0
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    reback () {
      this.$router.push({
        name: 'browser_index'
      })
    },
    getChain (data) {
      this.chain = data
      if (this.accountName === '') {
        this.browserBlur(this.searchData)
      } else {
        this.browserBlur(this.accountName)
      }
    },
    selectClassName () {
      this.className = this.className === 'select-style1' ? 'select-style2' : 'select-style1'
    },
    handleReachBottom () {
      if (this.pageParam.pageIndex < this.pages) {
        // this.pageParam.pageSize += 3
        this.pageParam.pageIndex += 1
        // this.updatePageSize(this.pageParam.pageSize)
        this.getChainList(true)
      }
    },
    getChainList (flag) {
      getChainIdList(this.pageParam).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          if (flag) {
            let index = res.data.records.findIndex(item => {
              if (item.chainId === this.routeChain.chainId) {
                return true
              }
            })
            if (index !== -1) {
              res.data.records.splice(index, 1)
            }
            this.chainIdList.push.apply(this.chainIdList, res.data.records)
          } else {
            this.chainIdList = res.data.records && res.data.records.length > 0 ? res.data.records : []
            if (this.chain.chainId === 0 && this.chainIdList[0].chainId) {
              this.chain.chainId = this.chainIdList[0].chainId
              this.chain.chainName = this.chainIdList[0].chainName
            } else {
              this.pushChainId()
            }
          }
          this.pageParam = {
            pageTotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          this.size = this.chainIdList.length
          this.pages = res.data.pages
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    pushChainId () {
      let flag = false
      for (let i in this.chainIdList) {
        if (this.chainIdList[i].chainId === this.routeChain.chainId) {
          flag = true
          break
        }
      }
      if (!flag) {
        this.chainIdList.push(this.routeChain)
      }
    },
    changeChain (val) {
      if (this.chain.chainId !== 0) {
        this.getChainName(this.chain.chainId)
        this.getChainAccountDatas(this.accountName)
      }
    },
    getChainName (value) {
      for (let item in this.chainIdList) {
        if (this.chainIdList[item].chainId === value) {
          this.chain.chainName = this.chainIdList[item].chainName
        }
      }
    }
  },
  mounted () {
    if (this.$route.query.accountName && this.$route.query.chain) {
      if (this.$route.query.tag) {
        this.searchData = this.$route.query.accountName + ''
      }
      this.getChainList()
      this.getChainAccountDatas(this.$route.query.accountName)
    } else {
      this.reback()
    }
  },
  beforeRouteEnter (to, from, next) {
    if (from.name) {
      next(vm => {
        vm.hisPath = from.name
      })
    } else {
      next('/browser_index')
    }
  },
  destroyed () {
    this.updateChain(this.chain)
  }
}
</script>
<style lang="less">
.tools-ellipsis{
  .ivu-tooltip-rel{
    width: 485px !important;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.tools-scroll-auto{
  .ivu-tooltip-inner-with-width{
  max-height: 80vh !important;
  overflow: auto!important;
  }
}
</style>

<style lang="less" scoped>
.size{
  font-weight: bold;
  font-size:15px;
}

.chain{
  height:100%;
  .title{
    .size;
    height:18px;;
    font-family: 'Microsoft YaHei';
    line-height: 18px;
    color: #333333;
    vertical-align: middle;
    margin:10px 0 15px 10px;
  }
  .bs{
    float:left;
    width: 6px;
    height: 18px;
    background: #19C3A0;
    opacity: 1;
    border-radius: 3px;
    margin-right:6px;
}

  .title-1{
   font-size:13px;
   font-weight:bold;
   margin:5px 0;
   padding-left:11px;
    .title-2{
      margin-left:5px;
      font-weight:100;
    }
    .title-3{
      margin-left:5px;
      font-size:20px;
    }
  }
}
.tradeTy{
     margin-right: 30px;
     padding-top: 20px;
}
.wordSt{
  padding-left: 10px;
}
.title{
  font-family:PingFangSC-Regular;
}
.netp3Style{
  stroke-color: #FFBC24;

}
.back{
    background-image:url('../../../assets/img/browser/block.png');
    background-repeat:no-repeat;
    background-size:100% 100%;
  }
.show-style{
  display: table;
  text-align: center;
  vertical-align: middle;
  margin:0 auto;
  position: relative;
  padding:8%;
  .msg-style{
    color:#b7b8b9;
    font-size:12px;
  }
}
</style>
<style lang="less" scoped>
/deep/.select-style1{
   .ivu-select-arrow{
     padding:9px;
     margin-right:-9px;
     background-color: #57a3f3;
     color: #fff;
     border-radius: 0 5px 5px 0;
     transition: none;
   }
   .ivu-icon-ios-arrow-down:before{
     color:#fff;
   }
}
/deep/.select-style2{
   .ivu-select-arrow{
     padding:9px;
     margin-right:-9px;
     background-color: #57a3f3;
     color: #fff;
     border-radius: 5px 0px 0px 5px;
     transition: none;
   }
   .ivu-icon-ios-arrow-down:before{
     color:#fff;
   }
}
/deep/.ivu-input{
  border-radius: 4px 0 0 4px;
}
/deep/.ivu-btn{
   border-radius: 0 4px 4px 0;
}
</style>
