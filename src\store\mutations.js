const mutations = {
  SET_APP_NAME (state, params) {
    state.appName = params
  },
  SET_APP_VERSION (state) {
    // vue.set(state, 'appVersion', 'v2.0')
    state.appVersion = 'v2.0'
  },
  SET_STATE_VALUE (state, value) {
    state.stateValue = value
  },
  SET_TENANTUUID (state, value) {
    state.tenantId = value
  },
  SAVE_PUBLICKEY (state, value) {
    state.publicKey = value
  },
  SAVE_MODALABOUT (state, value) {
    console.log(value);
    state.modalAbout = value
  }
}
export default mutations
