import axios from './index'
// 数据推送列表
export const datapushList = (params) => {
  return axios.request({
    url: 'cmbaas/scale/scaleScheduledTask/listScalesScheduled',
    data: params,
    method: 'POST'
  })
}
export const newdataPush = (params) => {
  return axios.request({
    url: 'cmbaas/scale/scalePlatformResource/listScalePlatformResource',
    data: params,
    method: 'POST'
  })
}
export const newpushfrom = (params) => {
  return axios.request({
    url: 'cmbaas/scale/scaleScheduledTask/saveScalesScheduled',
    data: params,
    method: 'POST'
  })
}
export const updatepushfrom = (params) => {
  return axios.request({
    url: 'cmbaas/scale/scaleScheduledTask/updateScalesScheduled',
    data: params,
    method: 'POST'
  })
}
export const updateStatus = (params) => {
  return axios.request({
    url: 'cmbaas/scale/scaleScheduledTask/updateStatus',
    data: params,
    method: 'POST'
  })
}
// 数据采集新增
export const newsavefrom = (params) => {
  return axios.request({
    url: 'cmbaas/scale/scaleScheduledTask/saveScalesScheduled',
    data: params,
    method: 'POST'
  })
}
// 数据采集编辑
export const updateScalesfrom = (params) => {
  return axios.request({
    url: 'cmbaas/scale/scaleScheduledTask/updateScalesScheduled',
    data: params,
    method: 'POST'
  })
}
// 交易列表查询
export const listToDayScaleTransaction = (params) => {
  return axios.request({
    url: 'cmbaas/scale/scaleTransaction/listToDayScaleTransaction',
    data: params,
    method: 'POST'
  })
}
// 交易查询
export const getScalesByTransactionId = (id) => {
  return axios.request({
    url: 'cmbaas/scale/scaleTransaction/getScalesByTransactionId?transactionId=' + id,
    method: 'GET'
  })
}
// 用户资源列表查询
export const userlist = (params) => {
  return axios.request({
    url: 'cmbaas/scale/scaleTransactionResourceShare/listScaleTransactionResourceShare',
    data: params,
    method: 'POST'
  })
}
// 查询资源总量
export const getScaleTransactionResourceShare = () => {
  return axios.request({
    url: 'cmbaas/scale/scaleTransactionResource/getScaleTransactionResourceShare',
    method: 'GET'
  })
}

// 分配资源
export const getUserResourceList = (params) => {
  return axios.request({
    url: 'cmbaas/scale/scaleTransactionResourceShare/saveScaleTransactionResourceShare',
    data: params,
    method: 'POST'
  })
}

// 新增数据服务配置

export const getNewServeResourceList = (params) => {
  return axios.request({
    url: 'cmbaas/scale/scalePlatformResource/saveScalePlatformResource',
    data: params,
    method: 'POST'
  })
}
// 编辑数据服务配置
export const getUpdateServeResourceList = (params) => {
  return axios.request({
    url: 'cmbaas/scale/scalePlatformResource/updateScalePlatformResource',
    data: params,
    method: 'POST'
  })
}
// 删除数据服务配置
export const getDeleteServeResourceList = (data) => {
  return axios.request({
    url: 'cmbaas/scale/scalePlatformResource/deleteScalePlatformResource?id=' + data,
    method: 'GET'
  })
}

// 租户查询
export const getTenantTableData = (pageParam, tenantName = "") => {
  return axios.request({
    url: "/cmbaas/portal/tenant/list",
    data: {
      tenantName,
      pageParam,
    },
    method: "POST",
  });
};
// 链账户
export const getChainIdList = (pageParam, searchItem = {}) => {
  return axios.request({
    url: "/cmbaas/chain/eos/multi/chain/list",
    data: {
      statusKey: searchItem.status !== undefined ? searchItem.status : "ENABLE",
      chooseTenantId: searchItem.chooseTenantId,
      pageParam,
    },
    method: "POST",
  });
};

// 查询租户的已分配用户列表
export const getAssigned = (
  tenantId,
  pageParam,
  userName = "",
  fullQuery = 1,
  skipVerify = 0
) => {
  return axios.request({
    url: "/cmbaas/portal/tenant/assigned/list/" + JSON.parse(tenantId),
    data: {
      userName,
      fullQuery,
      skipVerify,
      pageParam,
    },
    method: "POST",
  });
};

//  合约链账户列表

export const getContractList = (tenantId) => {
  return axios.request({
    url: "/cmbaas/chain/eos/chain/listChainAccountByChainId?chainId=" + JSON.parse(tenantId),
    data: {},
    method: "get",
  });
};

// 查询已部署的合约信息
export const getContractInfo = (chainId, chainAccountId) => {
  return axios.request({
    url:
      "/cmbaas/chain/eos/chain/" +
      JSON.parse(chainId) +
      "/" +
      JSON.parse(chainAccountId) +
      "/contractInfo",
    method: "GET",
  });
};

// 服务资源列表

// 链账户
export const getServerList = (data) => {
  return axios.request({
    url: "/cmbaas/scale/scaleInvokeLog/countResourceUseTotal",
    data: data,
    method: "POST",
  });
};

// cron表达式判断

export const getCron = (data) => {
  return axios.request({
    url: "/cmbaas/scale/scaleScheduledTask/validCronExpression?cronExpression=" + data,
    data: data,
    method: "GET",
  });
};
