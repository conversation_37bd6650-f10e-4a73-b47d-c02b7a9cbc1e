<template>
  <div class="tissue_box">
    <!-- <SelectChain></SelectChain> -->
    <div class="btn-raw">
      <!-- <div v-if="channeListLength != 0"> -->
      <!-- <div v-if="navList.length < 5" class="add-tissue-btn" @click="addFun()">添加组织</div> -->
      <!--<el-button type="primary" class="blue-btn"  @click="addFun(channeListLength)">添加组织</el-button>-->
      <!-- <el-button plain  @click="delFun()">删除区块链</el-button> -->
    </div>
    <div class="all-wrapper">
      <div class="onTopNav flex">
        <div class="nav-menu flex-align-item">
          <div
            v-for="(item, idx) in navList"
            :key="idx"
            :class="isNavActive == idx ? 'active' : ''"
            @click="navClick(idx, item.GroupNames)"
          >
            {{ item.GroupNames }}
          </div>
        </div>
      </div>
      <!--<div v-if="navList.length==0" class="none"><svg-icon icon-class="table-empty" />暂无数据</div>-->
      <div class="none" v-if="navList.length == 0">
        <i class="el-icon-loading" v-if="paddingText == '数据请求中...'"></i>
        <!-- <svg-icon icon-class="table-empty" v-else/> -->
        {{ paddingText }}
      </div>
      <div
        v-for="(msg, idx) in navList"
        :key="idx"
        v-show="isNavActive == idx"
        ref="tabs"
      >
        <div class="order">
          <div
            v-for="(data, ind) in msg.CountInfo"
            :key="ind"
            class="nodeBox_margin"
          >
            <div v-if="msg[ind].length > 0 && ind == 'orderer'">
              <div v-if="ind == 'orderer'" class="flex flex-align-item">
                <div class="node-title">{{ ind }}节点</div>
              </div>
              <SpaceLayout top="0" paddingX="0">
                <div slot="padding">
                  <div class="node-list-nav table-wrapper">
                    <el-row v-if="ind == 'orderer'" class="nav-box">
                      <el-col :span="6"><div class="">名称</div></el-col>
                      <el-col :span="3"><div class="">版本</div></el-col>
                      <el-col :span="3"><div class="">创建时间</div></el-col>
                      <el-col :span="3"><div class="">主机</div></el-col>
                      <!--<el-col :span="3"><div class="">外部访问</div></el-col>-->
                      <el-col :span="2"><div class="">重启次数</div></el-col>
                      <el-col :span="3"><div class="">状态</div></el-col>
                      <el-col :span="4"><div class="">操作</div></el-col>
                    </el-row>
                  </div>
                  <div class="tissue-table" v-for="(dataMsg, indx) in msg[ind]" :key="indx">
                    <table-order-peer
                      :orderdata="dataMsg"
                      :operationArrs="operationArrs"
                      :operationArr="operationArr"
                      @editData="editData"
                    ></table-order-peer>
                  </div>
                </div>
              </SpaceLayout>
            </div>
          </div>
        </div>
        <div class="otherone">
          <div
            v-for="(data, ind) in msg.CountInfo"
            :key="ind"
            class="nodeBox_margin"
          >
            <div v-if="msg[ind].length > 0 && ind != 'peer'&& ind != 'orderer'">
              <div
                v-if="ind != 'peer' && ind != 'orderer'"
                class="flex flex-align-item"
              >
                <div class="node-title">{{ ind }}节点</div>
              </div>
              <SpaceLayout paddingX="0">
                <div slot="padding">
                  <div class="node-list-nav table-wrapper">
                    <el-row v-if="ind != 'peer' && ind != 'orderer'" class="nav-box">
                      <el-col :span="6"><div class="">名称</div></el-col>
                      <el-col :span="3"><div class="">版本</div></el-col>
                      <el-col :span="3"><div class="">创建时间</div></el-col>
                      <el-col :span="3"><div class="">主机</div></el-col>
                     <!-- <el-col :span="3"><div class="">外部访问</div></el-col>-->
                      <el-col :span="2"><div class="">重启次数</div></el-col>
                      <el-col :span="3"><div class="">状态</div></el-col>
                      <el-col :span="4"><div class="">操作</div></el-col>
                    </el-row>
                  </div>
                  <div class="tissue-table" v-for="(dataMsg, indx) in msg[ind]" :key="indx">
                    <tissue-table
                      :orderdata="dataMsg"
                      :operationArrs="operationArrs"
                      :operationArr="operationArr"
                      @editData="editData"
                    ></tissue-table>
                  </div>
                </div>
              </SpaceLayout>
            </div>
          </div>
        </div>
        <div class="peer">
          <div
            v-for="(data, ind) in msg.CountInfo"
            :key="ind"
            class="nodeBox_margin"
          >
            <!-- <div v-if="msg[ind].length > 0 && ind == 'peer'"> -->
            <div v-if="ind == 'peer'">
              <div v-if="ind == 'peer'" class="flex flex-align-item">
                <div class="node-title">{{ ind }}节点</div>
                <!--<el-button plain v-if="ind == 'peer'-->
                           <!--@click="addNode(msg[ind])">添加节点</el-button>-->
                <!-- <div v-if="ind == 'peer' && msg[ind].length <= 3" class="add-node" @click="addNode(msg[ind])">添加节点</div> -->
              </div>
              <SpaceLayout paddingX="0">
                <div slot="padding">
                  <div class="node-list-nav table-wrapper">
                    <el-row v-if="ind == 'peer'" class="nav-box">
                      <el-col :span="6"><div class="">名称</div></el-col>
                      <el-col :span="3"><div class="">版本</div></el-col>
                      <el-col :span="3"><div class="">创建时间</div></el-col>
                      <el-col :span="3"><div class="">主机</div></el-col>
                    <!--  <el-col :span="3"><div class="">外部访问</div></el-col>-->
                      <el-col :span="2"><div class="">重启次数</div></el-col>
                      <el-col :span="3"><div class="">状态</div></el-col>
                      <el-col :span="4"><div class="">操作</div></el-col>
                    </el-row>
                  </div>
                  <div class="tissue-table" v-for="(dataMsg, indx) in msg[ind]" :key="indx">
                    <table-order-peer
                      :orderdata="dataMsg"
                      :operationArrs="operationArrs"
                      :operationArr="operationArr"
                      @editData="editData"
                    ></table-order-peer>
                  </div>
                </div>
              </SpaceLayout>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 添加组织弹框 -->
    <transition name="fade">
      <div v-if="isAlert" class="alertBox">
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">添加组织
              <i class="el-icon-cursor el-icon-close fr" @click="closeFun()"></i>
            </div>
          </div>
          <div class="alert_box">
            <div class="tissue_name">组织名称：{{ alertTissueName }}</div>
            <el-form ref="form" :model="form" label-width="140px">
              <div class="selectBox">
                <div class="labelBox">Peer节点数量：</div>
                <div class="flex_sy">
                  <el-select
                    v-model="form.nodeNum"
                    placeholder="Peer节点数量："
                    @change="selectNodeNum(form.nodeNum, '02')"
                  >
                    <el-option
                      v-for="(msg, idx) in elOption"
                      :key="idx"
                      :label="msg.label"
                      :value="msg.value"
                    ></el-option>
                  </el-select>
                </div>
                <div class="start">*</div>
              </div>
              <div class="errorMsg" v-if="msgShow">请选择节点数量</div>
              <!-- <el-form-item label="Peer节点数量：" :required="true">
                            <el-select v-model="form.nodeNum" placeholder="Peer节点数量：" @change="selectNodeNum(form.nodeNum,'02')">
                                <el-option v-for="(msg,idx) in elOption" :key="idx" :label="msg.label" :value="msg.value"></el-option>
                            </el-select>
                        </el-form-item> -->
              <div
                v-for="(msg, idx) in NodeDeploy"
                :key="idx"
                class="selectBox"
              >
                <div class="labelBox">{{ checkName(msg.name) }}：</div>
                <div class="flex_sy">
                  <el-select
                    v-model="msg.value"
                    @change="getOptionVal(msg, idx)"
                  >
                    <el-option
                      v-for="(msg, idx) in selectOption"
                      :key="idx"
                      :label="msg.Name"
                      :value="msg.Name"
                    ></el-option>
                  </el-select>
                </div>
                <div class="start">*</div>
              </div>
            </el-form>
          </div>
           <div class="btn-row">
             <Button type="primary" class="sure-btn" @click="tissueDeploy('01')">部署</Button>
            <Button type="text" class="border-btn" @click="closeFun()">取消 </Button>
          </div>
        </div>
      </div>
    </transition>
    <!-- 编辑节点弹窗 -->
    <transition name="fade">
      <div class="alertBox" v-if="isEditAlert">
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">
              编辑节点
              <i class="el-icon-cursor el-icon-close fr" @click="editCloseFun()"></i>
            </div>
          </div>
          <div class="flex nodeContent">
            <div class="nodeName">节点名称</div>
            <div class="setSite">主机设置</div>
            <div class="start"></div>
          </div>
          <div class="alert_box">
            <el-form ref="form" :model="form" :rules="rules">
              <div class="selectBox">
<!--                <div class="flex-align-item view" style="color:#333">-->
<!--                  <div class="tit">节点名称</div>-->
<!--                  <div class="titValue">主机设置</div>-->
<!--                </div>-->
                <div class="evertModule">
                   <el-form-item  :label="`${checkName(tissueName)}：`" prop="upName" label-width="150px">

                      <el-select v-model="form.NodeName">
                        <el-option
                          v-for="(msg, idx) in selectOption"
                          :key="idx"
                          :label="msg.Name"
                          :value="msg.Name"
                        ></el-option>
                      </el-select>
                    </el-form-item>



                </div>
<!--                <div class="labelBox labelBoxs">-->
<!--                  {{ checkName(tissueName) }}:-->
<!--                </div>-->
<!--                <div class="flex_sy">-->
<!--                  <el-select v-model="form.NodeName">-->
<!--                    <el-option-->
<!--                      v-for="(msg, idx) in selectOption"-->
<!--                      :key="idx"-->
<!--                      :label="msg.Name"-->
<!--                      :value="msg.Name"-->
<!--                    ></el-option>-->
<!--                  </el-select>-->
<!--                </div>-->
<!--                <div class="start">*</div>-->
              </div>
               <!-- <el-form-item :label="tissueName">
                            <el-select v-model="form.NodeName">
                                <el-option v-for="(msg,idx) in selectOption" :key="idx" :label="msg.Name" :value="msg.Name"></el-option>
                            </el-select>
                        </el-form-item> -->
            </el-form>
          </div>
          <div class="btn-row">
            <Button type="primary" class="sure-btn" @click="editDeploy()">部署</Button>
            <Button type="text" class="border-btn" @click="editCloseFun()" >取消</Button >
          </div>
        </div>
      </div>
    </transition>
      <!-- 删除节点弹窗 -->
    <transition name="fade">
      <div v-if="isDelete" class="alertBox confirmBox">
        <div class="addTissue">
          <div class="delText">
            <i class="el-icon-warning"></i>
            您确定要删除节点 {{ checkName(delNodeName) }} 吗？
          </div>
          <div class="confirmBottom">
            <Button type="primary" class="sure-btn" @click="affirm()">确定</Button>
            <Button type="text" class="border-btn" @click="deleteClose()">取消</Button>
          </div>
        </div>
      </div>
    </transition>
   <!-- 删除区块链弹窗 -->
    <transition name="fade">
      <div v-if="isdeltissue" class="alertBox confirmBox">
        <div class="addTissue">
          <div class="delText">
            <i class="el-icon-warning"></i>您确定要删除区块链 {{ checkName(delNodeName) }} 吗？
          </div>
          <div class="confirmBottom">
            <Button type="priamry" class="sure-btn" @click="deltissueaffirm()">确定</Button>
            <Button type="text" class="border-btn" @click="isdeleteClose()">取消</Button>
          </div>
        </div>
      </div>
    </transition>
 <!-- 添加节点弹框 -->
    <transition name="fade">
      <div v-if="isNode" class="alertBox">
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">添加节点
              <i class="el-icon-cursor el-icon-close fr" @click="closeNode()"></i>
            </div>
          </div>
          <div class="alert_box">
            <div class="tissue_name">组织名称：{{ addOrgName }}</div>
            <el-form ref="form" :model="form">
              <div :class="NodeDeployArrNum? 'selectBox selectBox_margin_none' : 'selectBox'">
                <div class="labelBox labelBoxs">Peer节点数量：</div>
                <div class="flex_sy">
                  <el-select
                    v-model="form.nodeNum"
                    placeholder="Peer节点数量："
                    @change="selectNodeNum(form.nodeNum, '01')"
                  >
                    <el-option
                      v-for="(msg, idx) in elOption"
                      :key="idx"
                      :label="msg.label"
                      :value="msg.value"
                    ></el-option>
                  </el-select>
                </div>
                <div class="start">*</div>
              </div>
              <div class="selectBox selectBox_margin_none sel_margin">
                <div class="labelBox labelBoxs"></div>
                <div class="flex_sy">
                  <div v-if="NodeDeployArrNum" class="alertText">您设置的节点数量已超出可添加范围。</div>
                  <div v-if="addJd" class="alertText">请选择节点数量</div>
                </div>
              </div>
              <!-- <div v-if="NodeDeployArrNum" class="alertText">您设置的节点数量超出可添加范围。</div> -->
              <div  v-for="(msg, idx) in NodeDeployArr" :key="idx" class="selectBox">
                <div class="labelBox">{{ checkName(msg.name) }}：</div>
                <div class="flex_sy">
                  <el-select
                    v-model="msg.value"
                    @change="getOptionVal(msg, idx)"
                  >
                    <el-option v-for="(msg, idx) in selectOption" :key="idx" :label="msg.Name"  :value="msg.Name"  ></el-option>
                  </el-select>
                </div>
                <div class="start">*</div>
              </div>
            </el-form>
          </div>
          <div class="btn-row">
            <Button type="primary" class="sure-btn" @click="tissueDeploy('02')">部署</Button>
            <Button type="text" class="border-btn" @click="closeNode()">取消 </Button>
          </div>
        </div>
      </div>
    </transition>
       <!-- 查看日志弹框 -->
   <transition name="fade">
      <div class="alertBox" v-if="isShowLog">
        <div class="addTissue" style="width:1000px;max-width:1000px">
          <div class="alertTop">
            <div class="tit">节点日志
              <i class="el-icon-cursor el-icon-close fr" @click="closeNode()"></i>
            </div>
          </div>
          <div class="alert_box fbIpt" ref="log">
            <div class="selectBox" >
              <div class="" v-for="(log, index) in logList" :key="index">
                <span>{{ log }}</span>
              </div>
            </div>
          </div>
          <div class="btn-row">
            <Button class="sure-btn blue-btn" @click="refresh()">刷新</Button>
          </div>
        </div>
      </div>
    </transition>
    <div
      v-if="loading"
      class="BoxLoading"
      v-loading="loading"
      :element-loading-text="text"
    ></div>
  </div>
</template>
<script>
  import {
    addOrgPeerDefaulDeploy,
    prepareForAddPeer,
    addOrg,
    updateDeployNode,
    deletePeer,
    addPeer,
    deleteChain,
    getFabricNodeLogs
  } from "@/api/baascore/tissue";
  import {
    getChainPodList,getChainNodeNameList
  } from "@/api/baascore/ywResource";
  import {getChannelList} from '@/api/baascore/agreement';
  import {getK8SNodeList} from '@/api/baascore/creatChain'
  import tissueTable from "./components/table";
  import TableOrderPeer from "./components/tableOrderPeer";
  import { calcCostMixin } from "@/utils/mixin";
  import SelectChain from '../compontents/selectChain'
  import SpaceLayout from "@/components/SpaceLayout";

  export default {
    //inject: ["reload"],
    mixins: [calcCostMixin],
    components: {
      tissueTable,
      TableOrderPeer,
      SelectChain,
      SpaceLayout
    },
    data() {
      return {
        countState:'',
        countTime:2,
        countText:'',
        isShowIcon:false,
        closeImg: require('@/assets/image/close.png'),
        msgShow: false, //添加组织未选择错误信息提示
        addJd: false, //添加节点未选择错误信息提示
        text: "加载中",
        loading: false,
        isdeltissue: false,
        DelClusterName: "",
        channeListLength: "",
        navList: [],
        isNavActive: 0,
        orderMsg: [],
        form: {
          nodeNum: "",
          NodeName: "",
          nodename2: "",
          nodename3: "",
          nodename4: "",
          masterVal: ""
        },
        isAlert: false,
        elOption: [
          {
            label: 1,
            value: 1
          },
          {
            label: 2,
            value: 2
          },
          {
            label: 3,
            value: 3
          },
          {
            label: 4,
            value: 4
          }
        ],
        isEditAlert: false,
        alertTissueName: "",
        tissueName: "",
        operationArrs: [],
        operationArr: [],
        isDelete: false,
        delNodeName: "",

        NodeDeploy: [], //
        ClusterName: "",
        ServiceId: "", //
        ClusterId: "",
        selectOption: [], //添加组织下拉option
        optionArr: [],
        Name: "",
        delMsg: "",

        // 添加节点
        isNode: false,
        addOrgName: "",
        navTopName: "",
        NodeDeployArr: [],
        NodeDeployArrNum: false,
        nodeleng: [],
        arrs: [],
        setTime: "",
        isReload: false,
        navLen: "",
        alreadyNode: [],
        nodeNameList:[],
        logList:[],
        logData:{},
        isShowLog:false,
        paddingText:"数据请求中...",
        rules: {
          upName: [
            { required: true, message: " ", trigger: "change" }
          ]
        },
      };
    },
    watch: {
      // chainItem: {
      //   handler(newvalue, oldvalue) {
      //     this.chainInfo = JSON.parse(sessionStorage.getItem("chainItem"));
      //     this.getChannelList(this.chainInfo.Id);
      //     this.ServiceId = this.chainInfo.Id;
      //     this.DelClusterName = this.chainInfo.Name;
      //     this.isNavActive = 0;
      //     this.getChainPodLists(this.chainInfo.Id);
      //   },
      //   deep: true
      // }
    },
    updated() {
      // this.getChainPodLists(this.ServiceId)
    },
    computed: {
    },
    mounted() {
      // this.operationArrs.push({name: "编辑",code: "01"},{name:'查看日志', code:'04'})
      // this.operationArr.push({name: "编辑",code: "02"},{name:'查看日志', code:'04'})
      this.operationArrs.push({name:'查看日志', code:'04'})
      this.operationArr.push({name:'查看日志', code:'04'})
      //this.operationArr.push({name: "删除", code: "03"})
      this.chainInfo = JSON.parse(sessionStorage.getItem("chainItem"));
      this.getChannelList(this.chainInfo.Id);
      this.ServiceId = this.chainInfo.Id;
      this.DelClusterName = this.chainInfo.Name;
      this.getChainPodLists(this.chainInfo.Id);
      this.ClusterName = this.chainInfo.Cluster;
      this.ClusterId = this.chainInfo.Cluster;
      // this.addOrgPeerDefaulDeploy(curChain.ServiceId,curChain.ClusterName)
    },
    methods: {
      getCountDown(type) {
        this.isShowIcon = false
      },
      //刷新日志
      refresh() {
        var params = this.logData
        getFabricNodeLogs(params).then(res =>{
          if(res.code == 200) {
            // this.isShowIcon = true
            // this.countState = 'success'
            // this.countText = '请求成功！'
            const dom=this.$refs.log;
            if(res.data) {
              this.logList = res.data
              this.logList = this.logList.reverse()
              this.isShowLog = true;
              if(dom){
                dom.scrollTop = 0;
              }
            }

          }else {
            // this.isShowIcon = true
            // this.countState = 'error'
            // this.countText = '数据获取失败，请重新加载！'
            this.$message.error("数据获取失败，请重新加载！");
          }
        })
      },
      getChainNodeNameList(id) {
        var query = {
          ServiceId: id
        };
        getChainNodeNameList(query).then(res =>{
          if(res.code == 200 && res.data) {
            this.nodeNameList = res.data
            // this.isShowIcon = true
            // this.countState = 'success'
            // this.countText = '请求成功！'
          } else {
            // this.isShowIcon = true
            // this.countState = 'error'
            // this.countText = '数据获取失败，请重新加载！'
            this.$message.error('数据获取失败，请重新加载！');
          }
        })
      },
      // 查询通道数量
      getChannelList(id) {
        this.channeListLength = 0;
        var query = {
          ServiceId: id
        };
        getChannelList(query).then(res => {
          if (res.code == 200) {
            // this.isShowIcon = true
            // this.countState = 'success'
            // this.countText = '请求成功！'
            if (res.data && res.data.Channels) {
              this.channeListLength = res.data.Channels.length;
            } else {
              this.channeListLength = 0;
            }
          } else {
            // this.isShowIcon = true
            // this.countState = 'error'
            // this.countText = '数据获取失败，请重新加载！'
            this.$message.error('数据获取失败，请重新加载！');
          }
        });
      },
      // 1、顶部导航切换事件
      navClick(idx, name) {
        this.navTopName = name;
        this.isNavActive = idx;
        this.code = idx;
      },
      delFun() {
        this.isdeltissue = true;
      },
      // 添加组织
      addFun(count) {
        /*if (count == 0) {
          // this.isShowIcon = true
          // this.countState = 'full'
          // this.countText = '无法添加组织，请先前往“链通道管理”创建通道！'
          this.$message.warning("无法添加组织，请先前往“链通道管理”创建通道！");
        } else {
          if(this.navList.length >= 5) {
            // this.isShowIcon = true
            // this.countState = 'full'
            // this.countText = '组织数量已满，无法添加！'
            this.$message.warning('组织数量已满，无法添加！')
          } else {
            this.isAlert = true
          }
        }*/
        if(this.navList.length >= 5) {
          // this.isShowIcon = true
          // this.countState = 'full'
          // this.countText = '组织数量已满，无法添加！'
          this.$message.warning('组织数量已满，无法添加！')
        } else {
          this.isAlert = true
        }
      },
      // 关闭弹窗
      closeFun() {
        this.NodeDeploy = [];
        this.form.nodeNum = "";
        this.alertTissueName = "";
        this.isAlert = false;
        this.msgShow = false
        this.optionArr = []
      },
      // 节点数量
      selectNodeNum(id, name) {
        this.msgShow = false;
        this.addJd=false;
        this.form.nodeNum = id;
        this.addOrgPeerDefaulDeploy(id, name);
      },
      // 子组件传过来的值
      editData(msg, code) {
        // 公共节点 的order 节点编辑
        if (code == "01" || code == "02") {
          this.tissueName = msg.Name;
          this.form.NodeName = msg.NodeName;
          this.Name = msg.Name;
          this.isEditAlert = true;
          this.getK8SNodeList()
        } else if (code == "03") {
          this.delMsg = msg;
          this.delNodeName = msg.Name;
          this.isDelete = true;
        }else if(code == '04') {
          var PodName = msg.PodName
          var params = {
            //chainName:this.DelClusterName,
            chainId: this.ServiceId,
            fabricNodeName:PodName,
            lineNum:1500,
            clusterName:this.ClusterId,
          }
          this.logData = params
          getFabricNodeLogs(params).then(res =>{
            if(res.code == 200) {
              // this.isShowIcon = true
              // this.countState = 'success'
              // this.countText = '请求成功！'
              if(res.data)
                this.logList = res.data
                this.logList = this.logList.reverse()
                this.isShowLog = true
            } else {
              // this.isShowIcon = true
              // this.countState = 'error'
              // this.countText = '获取节点日志失败！'
              this.$message.error('获取节点日志失败！');
            }
          })
        }
      },
      editCloseFun() {
        this.isEditAlert = false;
      },
      // 确认删除该节点吗？
      affirm() {
        this.deletePeer();
      },
      deleteClose() {
        this.isDelete = false;
      },
      isdeleteClose() {
        this.isdeltissue = false;
      },
      // 部署
      tissueDeploy(type) {
        if (type == "01") {
          this.addOrg(); // 添加组织
        } else if (type == "02") {
          this.addPeer();
        }
      },
      // 请求
      //获取链状态
      getChainPodLists(id) {
        // this.loading = true
        // this.text = '加载中...'
        this.navList = [];
        var query = {
          ServiceId: id
        };
        getChainPodList(query).then(res => {
          if (res.code == 200) {
            // this.isShowIcon = true
            // this.countState = 'success'
            // this.countText = '请求成功！'
            // 处理tab切换展示
            let s = 0;
            for (var i in res.data) {
              res.data[i].code = s++;
              res.data[i].GroupName == "Commons"
                ? (res.data[i].GroupNames = "公共节点")
                : (res.data[i].GroupNames = res.data[i].GroupName);
            }
            if (this.isReload) {
              if (res.data.length >= this.navLen.length) {
                this.navList = res.data;
                this.isReload = false;
              }
            } else {
              this.navList = res.data;
            }
            if (this.navList.length===0){
              this.paddingText="暂无数据"
            }
            // this.navList = res.data
            // 处理公共组织
            for (var k in this.navList) {
              this.navList[k].orderer = [];
              this.navList[k].kafka = [];
              this.navList[k].zookeeper = [];
              this.navList[k].bft = [];
              this.navList[k].ca = [];
              this.navList[k].peer = [];
              for (var m in this.navList[k].NodeInfo) {
                if (this.navList[k].NodeInfo[m].Type == "orderer") {
                  this.navList[k].orderer.push(this.navList[k].NodeInfo[m]);
                } else if (this.navList[k].NodeInfo[m].Type == "kafka") {
                  this.navList[k].kafka.push(this.navList[k].NodeInfo[m]);
                } else if (this.navList[k].NodeInfo[m].Type == "zookeeper") {
                  this.navList[k].zookeeper.push(this.navList[k].NodeInfo[m]);
                } else if (this.navList[k].NodeInfo[m].Type == "bft") {
                  this.navList[k].bft.push(this.navList[k].NodeInfo[m]);
                } else if (this.navList[k].NodeInfo[m].Type == "ca") {
                  this.navList[k].ca.push(this.navList[k].NodeInfo[m]);
                } else if (
                  this.navList[k].NodeInfo[m].Type == "peer"
                ) {
                  this.navList[k].peer.push(this.navList[k].NodeInfo[m]);
                }
              }
            }
            // this.loading = false
          } else {
            // this.isShowIcon = true
            // this.countState = 'error'
            // this.countText = '数据获取失败，请重新加载！'
            this.paddingText="暂无数据"
            this.$message.error('数据获取失败，请重新加载！');
          }
        });
      },
      // 添加组织列表 请求
      addOrgPeerDefaulDeploy(count, name) {
        let msg = {
          // msgType: "addOrgPeerDefaulDeploy",
          // params: {
          ServiceId: this.ServiceId,
          ClusterName: this.ClusterName,
          Orgs: [
            {
              Name: name == "01" ? this.addOrgName : "",
              PeerCount: count
            }
          ]
          // }
        };
        if (name == "02") {
          addOrgPeerDefaulDeploy(msg)
            .then(res => {
              if (res.code == 200) {
                // this.isShowIcon = true
                // this.countState = 'success'
                // this.countText = '请求成功！'
                this.alertTissueName = res.data.DeployInfo.PeerOrgs[0].Name;
                this.NodeDeploy = [];
                // 处理select返回值的数据
                for (var i in res.data.NodeDeploy) {
                  for (var k in res.data.NodeDeploy[i]) {
                    let obj = {};
                    obj.name = k;
                    obj.value = res.data.NodeDeploy[i][k];
                    this.NodeDeploy.push(obj);
                  }
                }
                this.optionArr = this.NodeDeploy;
              } else {
                //this.NodeDeployArrNum = true;
                // this.isShowIcon = true
                // this.countState = 'error'
                // this.countText = '数据获取失败，请重新加载！'
                this.$message.error("数据获取失败，请重新加载！");
              }
            })
          // .catch(() => {
          //   this.$message.error("网络异常,请检查网络");
          // });
        } else {
          if (this.alreadyNode.length + count <= 4) {
            this.NodeDeployArrNum = false;
            prepareForAddPeer(msg)
              .then(res => {
                if (res.code == 200) {
                  // this.isShowIcon = true
                  // this.countState = 'success'
                  // this.countText = '请求成功！'
                  let Dep = this.NodeDeployArr;
                  this.arrs = [];
                  for (var i in res.data.NodeDeploy) {
                    for (var k in res.data.NodeDeploy[i]) {
                      let obj = {};
                      obj.name = k;
                      obj.value = res.data.NodeDeploy[i][k];
                      this.arrs.push(obj);
                    }
                  }
                  // this.NodeDeployArr = [...this.nodeleng,...this.arrs]
                  this.NodeDeployArr = this.arrs;
                } else {
                  // this.isShowIcon = true
                  // this.countState = 'error'
                  // this.countText = '数据获取失败，请重新加载！'
                  this.$message.error("数据获取失败，请重新加载！");
                }
              })
            // .catch(() => {
            //   this.$message.error("网络异常,请检查网络");
            // });
          } else {
            this.NodeDeployArr = [];
            this.NodeDeployArrNum = true;
          }
        }
      },
      // 添加组织下拉数据
      getK8SNodeList() {
        var query = {
          ClusterId: this.ClusterId
        };
        getK8SNodeList(query).then(res => {
          if (res.code == 200) {
            // this.isShowIcon = true
            // this.countState = 'success'
            // this.countText = '请求成功！'
            this.selectOption = res.data.Hosts;
          } else {
            // this.isShowIcon = true
            // this.countState = 'error'
            // this.countText = '数据获取失败，请重新加载！'
            this.$message.error('数据获取失败，请重新加载！');
          }
        });
      },

      // 选中下拉数据
      getOptionVal(val, idx) {},

      // 请求 ~ 组织部署按钮
      addOrg() {
        if (this.optionArr.length > 0) {
          this.loading = true;
          this.text = "部署中...";
          let optionFilter = this.optionArr;
          let obj = {};
          for (var item in optionFilter) {
            obj[optionFilter[item].name] = optionFilter[item].value;
          }

          let SpecsData = this.optionArr;
          let arr = [];
          for (var i in SpecsData) {
            var o = {};
            o.Hostname = SpecsData[i].name;
            arr.push(o);
          }
          let msg = {
            // msgType: "addOrg",
            // params: {
            ServiceId: this.ServiceId,
            DeployInfo: {
              OrdererOrgs: null,
              PeerOrgs: [
                {
                  Name: this.alertTissueName,
                  Domain: this.alertTissueName + ".fabric.com",
                  Specs: arr,
                  Users: {
                    Count: 1
                  }
                }
              ]
            },
            NodeDeploy: {
              [this.alertTissueName]: obj
            }
            // }
          };
          addOrg(msg)
            .then(res => {
              // this.loading = false
              if (res.code == 200) {
                // setTimeout(() => {
                //     this.$message({
                //         message: '部署成功',
                //         type: 'success'
                //     });
                //     this.isAlert = false
                //     this.loading = false
                //     this.reload()
                //     this.getChainPodLists(this.ServiceId)
                // }, 3000);
                // this.$message({
                //     message: '部署成功',
                //     type: 'success'
                // });
                // this.loading = false
                // this.isAlert = false
                // setTimeout(() => {
                //     this.reload()
                //     this.getChainPodLists(this.ServiceId)
                // },500)
                // const navLen = this.navList
                this.navLen = this.navList;
                this.isReload = true;
                this.setTime = setInterval(() => {
                  if (this.navList.length <= this.navLen.length) {
                    this.getChainPodLists(this.ServiceId);
                  } else {
                    clearInterval(this.setTime);
                    this.setTime = null;
                    this.$message.success("部署成功！")
                    // this.isShowIcon = true
                    // this.countState = 'success'
                    // this.countText = '部署成功！'
                    this.isAlert = false;
                    this.loading = false;
                    this.NodeDeploy = [];
                    this.form.nodeNum = "";
                  }
                }, 1500);
              } else {
                this.loading = false;
                // this.isShowIcon = true
                // this.countState = 'error'
                // this.countText = '部署失败，请重新部署！'
                this.$message.error("部署失败，请重新部署！");
              }
            })
            .catch(err => {
              this.loading = false;
              //this.$message.error("网络异常，请检查网络");
            });
        } else {
          this.msgShow = true;
          // Message.error("请选择节点数量");
        }
      },
      // 请求 ~ 节点部署按钮
      addPeer() {
        if (this.NodeDeployArr.length > 0) {
          if (this.arrs.length > 0) {
            this.loading = true;
            this.text = "部署中...";
            let num = this.isNavActive;
            let optionFilter = this.arrs;
            let obj = {};
            for (var item in optionFilter) {
              obj[optionFilter[item].name] = optionFilter[item].value;
            }
            let SpecsData = this.arrs;
            let arr = [];
            for (var i in SpecsData) {
              var o = {};
              o.Hostname = SpecsData[i].name;
              arr.push(o);
            }
            let msg = {
              // msgType: "addPeer",
              //params: {
              ServiceId: this.ServiceId,
              DeployInfo: {
                OrdererOrgs: null,
                PeerOrgs: [
                  {
                    Name: this.addOrgName,
                    Domain: this.addOrgName + ".fabric.com",
                    Specs: arr,
                    Users: {
                      Count: 1
                    }
                  }
                ]
              },
              NodeDeploy: {
                [this.addOrgName]: obj
              }
              // }
            };
            addPeer(msg).then(res => {
              // this.loading = false
              if (res.code == 200) {
                // this.isShowIcon = true
                // this.countState = 'success'
                // this.countText = '部署成功！'
                this.$message.success('部署成功！')
                this.loading = false;
                this.isNode = false;
                // this.reload()
                this.isNavActive = num;
                this.closeNode()
                this.getChainPodLists(this.ServiceId);
              } else {
                this.loading = false;
                // this.isShowIcon = true
                // this.countState = 'error'
                // this.countText = '部署失败，请重新部署！'
                this.$message.error("部署失败，请重新部署！");
              }
            }).catch(err => {
              this.loading = false;
              //this.$message.error("网络异常,请检查网络");
            });
          } else {
            this.$message.warning("请添加新的节点。");
          }
        }else{
          this.addJd=true;
        }
      },

      // 编辑部署按钮
      editDeploy() {
        this.loading = true;
        this.text = "部署中...";
        let msg = {
          //msgType: "updateDeployNode",
          // params: {
          ServiceId: this.ServiceId,
          DeployName: this.Name,
          NodeName: this.form.NodeName
          //}
        };
        updateDeployNode(msg).then(res => {
          this.loading = false;
          if (res.code == 200) {
            // this.isShowIcon = true
            // this.countState = 'success'
            // this.countText = '修改成功！'
            //this.loading = false
            this.$message.success('修改成功！')
            this.isEditAlert = false;
            this.getChainPodLists(this.ServiceId);
          } else {
            // this.isShowIcon = true
            // this.countState = 'error'
            // this.countText = '修改失败，请重试！'
            this.$message.error("修改失败，请重试！");
          }
        });
      },

      // 删除节点接口
      deletePeer() {
        this.loading = true;
        this.text = "删除中...";
        let active = this.isNavActive;
        let msg = {
          //msgType: "deletePeer",
          //params: {
          ServiceId: this.ServiceId,
          Org: this.delMsg.OrgName,
          PeerName: this.delMsg.Name
          //}
        };
        deletePeer(msg)
          .then(res => {
            this.loading = false;
            if (res.code == 200) {
              this.getChainPodLists(this.ServiceId)
              // this.isShowIcon = true
              // this.countState = 'success'
              // this.countText = '删除成功！'
              this.$message.success('删除成功！')
              this.isDelete = false;
              this.isNavActive = active;
            } else {
              this.loading = false;
              // this.isShowIcon = true
              // this.countState = 'error'
              // this.countText = '删除失败，请重试！'
              this.$message.error("删除失败，请重试！");
            }
          })
          .catch(err => {
            // console.lofg(err)
            if (err) {
              this.loading = false;
              //this.$message.error("网络异常,请检查网络");
            }
          });
      },

      // 添加节点按钮
      addNode(msg) {
        this.alreadyNode = msg;
        if (msg.length == 4) {
          // this.isShowIcon = true
          // this.countState = 'full'
          // this.countText = '成员节点数量已满，无法添加！'
          this.$message.warning("成员节点数量已满，无法添加！");
        } else {
          this.NodeDeployArr = [];
          this.addOrgName = this.navTopName;
          this.isNode = true;
        }
        // 不展示已有节点
        // this.NodeDeployArr = []
        // this.addOrgName = this.navTopName
        // 展示已有节点
        // this.NodeDeployArr = []
        // this.addOrgName = this.navTopName
        // for(var k in msg){
        //     let obj = {}
        //     obj.name = msg[k].Name
        //     obj.value = msg[k].NodeName
        //     this.NodeDeployArr.push(obj)
        // }
        // var nodeLen = JSON.parse(JSON.stringify(this.NodeDeployArr));
        // this.nodeleng = nodeLen
        // this.isNode = true
      },
      // 关闭节点
      closeNode() {
        this.isNode = false;
        this.NodeDeployArrNum = false;
        this.form.nodeNum = "";
        this.addJd = false;
        this.isShowLog = false
      },
      // 删除组织
      deltissueaffirm() {
        this.deleteChain();
      },
      // 删除链
      deleteChain() {
        this.loading = true;
        this.text = "删除中...";
        let msg = {
          //msgType: "deleteChain",
          // params: {
          ServiceId: this.ServiceId
          //  }
        };
        deleteChain(msg).then(res => {
          this.loading = false;
          if (res.code == 200) {
            // this.isShowIcon = true
            // this.countState = 'success'
            // this.countText = '删除成功！'
            this.$message.success('删除成功！')
            this.loading = false;
            this.isdeltissue = false;
            // sessionStorage.removeItem("curChain");
            sessionStorage.removeItem("chainItem");
            // this.reload()
            this.$router.push({
              path: "/instance"
            });
          } else {
            this.loading = false;
            // this.isShowIcon = true
            // this.countState = 'error'
            // this.countText = '删除失败，请重试！'
            //sessionStorage.removeItem("chainItem");
            this.$message.error("删除失败，请重试！");
          }
        }).catch(err => {
          this.loading = false;
          //this.$message.error("网络异常,请检查网络");
        });
      }
    }
  };
</script>
<style lang="less" scoped>
@import "../../../styles/common/modal.less";
  .tissue_box {
    //padding-bottom: 80px;
  }
  .onTopNav {
    width: calc(100% + 30px);
    margin-left: -15px;
    padding: 0 20px;
    border-bottom: 1Px solid #D9D9D9;
    border-top: 2PX solid transparent;
    height: 44px;
    background: #fff;
  }
  .nav-menu div {
    padding: 12px 31px;
    color: #555;
    cursor: pointer;
    // font-size: 20px;
    font-size: 14px;
  }
  .nav-menu .active {
    color: #337DFF;
    //border-bottom: 2PX solid #337DFF;
    // font-size: 20px;
    font-size: 14px;
    position: relative;
    &::before {
      position: absolute;
      content: '';
      width: 100%;
      height: 2px;
      background: #337DFF;
      left: 0px;
      bottom: 1Px;
    }
  }
  .add-tissue-btn {
    width: 120px;
    height: 46px;
    // font-size: 18px;
    font-size: 14px;
    font-weight: 400;
    color: #ffffff;
    text-align: center;
    line-height: 46px;
    cursor: pointer;
    background: #337DFF;
    border-radius: 4px;
  }

  .closeIcon {
    cursor: pointer;
  }
  .alert_box .tissue_name {
    color: #00ada2;
    // font-size: 18px;
    font-size: 14px;
    margin: 60px 0 43px 36px;
  }
  .alert_box.log {
    padding: 50px 60px 0 60px;
    height: 550px;
    overflow-x: hidden;
    overflow-y: auto;
    word-wrap:break-word;
  }
  .log::-webkit-scrollbar {/*滚动条整体样式*/
    height: 70px;     /*高宽分别对应横竖滚动条的尺寸*/
  }
  .log::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
    border-radius: 2px;
    background: #BFBFBF;
    border: 1Px solid #E7ECEF;
  }
  .log-btn {
    text-align: center;
    margin-top: 40px;
    margin-bottom: 50px;
    span {
      display: inline-block;
      width: 120px;
      height: 46px;
      border: 2px solid #1F82E8;
      border-radius: 4px;
      line-height: 46px;
      // font-size: 18px;
      font-size: 14px;
      color: #1F82E8;
      cursor: pointer;
    }
  }
  .alert_box.log span {
    width: 100%;
    display: inline-block;
    // font-size: 18px;
    font-size: 14px;
    color: #333;
    margin-bottom: 12px;
    line-height: 28px;
  }
  .alert_box /deep/ .el-select {
    width: 100%;
    height: 56px;
  }

  .btn-row {
    /*margin: 50px 100px 60px;*/
  }
  /*.delText {*/
  /*  font-size: 20px;*/
  /*  color: #333333;*/
  /*  margin-top: 60px;*/
  /*  text-align: center;*/
  /*}*/
  .add-node {
    width: 120px;
    height: 46px;
    border: 2px solid #1f82e8;
    border-radius: 4px;
    color: #1f82e8;
    // font-size: 18px;
    font-size: 14px;
    text-align: center;
    line-height: 46px;
    cursor: pointer;
  }
  .nodeBox_margin {
    margin-top: 20px;
  }

  .setSite {
    flex: 1;
    /*text-align: center;*/
    // font-size: 17px;
    font-size: 14px;
    color: #333333;
  }
  .node-title {
    // font-size: 22px;
    font-size: 14px;
    color: #333;
    font-family: Microsoft YaHei;
    font-weight: 400;
    // margin-top:43px;
  }
  .node-title::before {
    content: "";
    display: inline-block;
    width: 3px;
    height: 14px;
    background:  #337DFF;
    /*border-radius: 2px;*/
    margin-right: 6px;
    transform: translateY(3px);
  }
  .node-list-nav .nav-box {
    color: #999999;
    // font-size: 18px;
    font-size: 14px;
    box-sizing: border-box;
    /*margin-top: 20px;*/
  }
  .node-list-nav .nav-box /deep/ .el-col {
    text-align: center;
    color: #999999;
    // font-size: 18px;
    font-size: 14px;
    /*padding: 10px 0 10px !important;*/
  }
  // .node-list-nav .nan-item .nav-box:hover{
  //     border: 2px solid rgba(31, 130, 232, 0.33);
  //     -moz-box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6; -webkit-box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6; box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6;
  // }
  // .node-list-nav .nan-item .nav-box{
  //     background: #fff;
  //     margin-top:10px;
  //     border-radius: 4px;
  //     overflow:hidden;
  // }
  // .node-list-nav .nan-item .status span{
  //     background:rgba(9, 223, 192, 0.2);
  //     color:#00ADA2;
  //     padding:10px 26px;
  //     border-radius: 4px;
  //     cursor: default;
  // }
  // .node-list-nav .nan-item .edit span{
  //     background:#00ADA2;
  //     color:#fff;
  // }
  // .node-list-nav .nan-item .wait span{
  //     background:rgba(253, 187, 45, 0.2);
  //     color:#FCB827;
  // }
  // .node-list-nav .nan-item .waring span{
  //     background: rgba(255, 0, 43,0.4);
  //     color:#fff;
  // }
  // .node-list-nav .nav-box .el-col div span{
  //     padding:10px 30px;
  //     border-radius: 4px;
  //     margin:0 3px;
  // }
  // .node-list-nav .nav-box .el-col .operaBtn span:nth-child(1){
  //     background:#00ADA2;
  //     color:#fff;
  //     cursor: pointer;
  // }
  // .node-list-nav .nav-box .el-col .operaBtn span:nth-child(2){
  //     background:#A7BFE8;
  //     color:#fff;
  //     cursor: pointer;
  // }
  .selectBox {
    /*display: flex;*/
    /*margin-top: 38px;*/
    /*padding: 0 60px 0 60px;*/
    /*align-items: center;*/
  }
  .labelBox {
    // min-width: 100px;
    width: 160px;
    // padding:0 30px 0 0;
    text-align: center;
    // font-size: 18px;
    font-size: 14px;
  }
  // .labelBoxs{
  //    width: 150px;
  // }
  .alertText {
    color: #fc4f1d;
    // font-size: 16px;
    font-size: 14px;
    text-align: left;
    // margin-right:168px;
    margin: 12px 0;
  }
  .delTissue {
    background: #a7bfe8;
    color: #fff;
    margin-left: 20px;
  }
  .nodeContent {
    padding: 16px 16px 0;
  }
  .nodeContent .nodeName {
    width: 120px;
    text-align: right;
    font-size: 14px;
    margin-right: 30px;
  }
  .flex_sy {
    flex: 1;
  }
  .BoxLoading {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10000;
  }
  .BoxLoading /deep/ .el-loading-mask {
    background: rgba(0, 0, 0, 0.2) !important;
  }
  .BoxLoading /deep/ .el-loading-spinner .circular {
    width: 60px !important;
    height: 60px !important;
  }
  .BoxLoading /deep/ .el-loading-spinner .path {
    stroke: #fff !important;
  }
  .BoxLoading /deep/ .el-loading-spinner .el-loading-text {
    color: #fff !important;
    // font-size: 24px;

    font-weight: bold;
  }
  .start {
    // font-size: 40px;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #ff3a4c;
    margin-left: 21px;
    margin-top: 11px;
  }
  .selectBox_margin_none {
    margin-bottom: 0;
    margin-top: 0px;
  }
  .sel_margin {
    padding-right: 86px;
  }
  // 修改弹窗样式
  .errorMsg {
    width: 100%;
    color: #f56c6c;
    // font-size: 16px;
    font-size: 14px;
    margin-top: 10px;
    text-align: left;
    text-indent: 220px;
  }
  .btn-raw{
    margin-bottom: 20px;
  }
  .none{
    // font-size: 18px;
    font-size: 14px;
    text-align: center;
    line-height: 65px;
    color: #666666;
    background: #fff;
  }
  .all-wrapper{
    background: #fff;
    padding:0 0 10px;
  }
</style>
