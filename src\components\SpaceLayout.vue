<template>
  <div class="padding" :style="styleAll">
    <slot name="padding"></slot>
  </div>
</template>

<script>
  export default {
    name: 'SpaceLayout',
    props: {
      top: {
        default: 0
      },
      bottom: {
        default: 0
      },
      paddingX: {
        default: 30
      },
      paddingY: {
        default: 20
      }
    },
    computed: {
      styleAll() {
        return {
          padding: `${this.paddingY}px ${this.paddingX}px`,
          'margin-top': `${this.top}px`,
          'margin-bottom': `${this.bottom}px`
        }
      }
    }
  }
</script>

<style scoped>
  .padding {
    /*padding: 30px 40px;*/
    background: #fff;
  }
</style>
