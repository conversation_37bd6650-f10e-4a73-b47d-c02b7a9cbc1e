<template>
  <div>
    <!-- 上架合约模板 -->
    <Modal v-model="putaway" :title="describeTitleModal" width=840>
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80">
        <FormItem label="合约语言">
          <p>{{this.languagetype}}</p>
        </FormItem>
        <FormItem label="模板类型" prop="name">
          <Input v-model="formValidate.name" placeholder="请填写模板类型" />
        </FormItem>
        <FormItem label="适用场景" prop="scenario">
          <Input v-model="formValidate.scenario" placeholder="请填写适用场景" :maxlength="60" show-word-limit />
        </FormItem>
        <FormItem label="描述" prop="describe">
          <Input v-model="formValidate.describe" placeholder="请填写描述内容" type="textarea" :maxlength="200" show-word-limit :autosize="{minRows: 3,maxRows: 5}" />
        </FormItem>
        <FormItem label="接口描述" prop="interfacedescribe" class="mandatory">
          <p style="float-right">
            <Button type="success" ghost @click="addInfo" icon="md-add">新增</Button>
          </p>
          <div>
            <edit-table-mul border :columns="modalDescribe" v-model="modaldescribeData"></edit-table-mul>
          </div>

        </FormItem>
        <FormItem label="版本信息" prop="versioninfo" class="mandatory">
          <div>
            <edit-table-mul border ref="selection" :columns="VersionTitle" v-model="VersionData"></edit-table-mul>
          </div>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="cancelShare">取消</Button>
        <Button type="primary" @click="okShare('formValidate')" :loading='Loadstatus'>确定</Button>
      </div>
    </Modal>
    <!-- 接口描述新增、修改弹框 -->
    <Modal v-model="newdescribe" :title="describeTitle" footer-hide>
      <Form ref="newfrom" :rules="newfromValidate" :model="newfrom" :label-width="80">
        <FormItem label="函数名" prop="funcName">
          <Input v-model="newfrom.funcName" placeholder="请填写函数名" />
        </FormItem>
        <FormItem label="参数" prop="parameter">
          <Input v-model="newfrom.parameter" placeholder="请填写参数" />
        </FormItem>
        <FormItem label="简介" prop="description">
          <Input v-model="newfrom.description" placeholder="请填写简介" />
        </FormItem>
      </Form>
      <div class="newdescribe">
        <Button type="primary" @click="describeOk('newfrom')">确定</Button>
        <Button @click="FormInfoCancel" style="margin-left: 8px">取消</Button>
      </div>
    </Modal>
    <!-- 查看源码 -->
    <Modal v-model="chaincode" title="查询合约链码" width='900px'>
      <p style="margin-bottom:20px">上传版本号：{{this.title}}</p>
      <div v-if="isSingleCpp=='0'">
        <Layout>
          <Sider hide-trigger :style="{background: '#fff'}">
            <Menu theme="light" width="auto" :open-names="['1']">
              <Submenu :name="key" v-for="(item,key) in cppsTitle" :key="key">
                <template slot="title">
                  <Icon type="ios-folder"></Icon>
                  {{key}}
                </template>
                <MenuItem :name="items.fileName" v-for="(items,i) in item" :key="i" @click.native="clickCpps(items.content)">{{items.fileName}}</MenuItem>
              </Submenu>
            </Menu>
          </Sider>
          <Content :style="{padding: ' 0 24px', minHeight: '280px', background: '#fff'}">
            <p>
              <textarea class="textarea-style" v-html="cppContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
            </p>
          </Content>
        </Layout>

      </div>
      <div v-else>
        <Collapse simple accordion v-if="this.formValidate.languagetype === 'C++'">
          <Panel :name="transferKey1" v-if="transferKey1">
            {{transferKey1}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.cppcentent.fileContent" readonly @scroll="handScroll($event, 'cpp')"></textarea>
            </p>
          </Panel>
          <Panel :name="item" v-for="(item,index) in filesHpp" :key='index' v-show="filesHpp.length">
            {{item}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.hppcentent.fileContent" readonly @scroll="handScroll($event, 'hpp')"></textarea>
            </p>
          </Panel>

        </Collapse>
        <Collapse simple accordion v-else-if="this.formValidate.languagetype === 'JS'">
          <Panel :name="transferKey1" v-if="transferKey1">
            {{transferKey1}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.jscentent.fileContent" readonly @scroll="handScroll($event, 'js')"></textarea>
            </p>
          </Panel>
          <Panel :name="fileName" v-if="fileName">
            {{fileName}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.abicentent.fileContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
            </p>
          </Panel>
        </Collapse>
        <Collapse simple accordion v-else>
          <Panel :name="fileName" v-if="fileName">
            {{fileName}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.gocentent.fileContent" readonly @scroll="handScroll($event, 'go')"></textarea>
            </p>
          </Panel>

        </Collapse>
      </div>
    </Modal>
  </div>
</template>

<script>
import { getChaincode } from '@/api/data'
import { getStencils, getShelvesTem } from '@/api/contract'
import { TemContractName } from '../../../lib/check'
import EditTableMul from '_c/edit-table-mul'
export default {
  name: 'putaway',
  components: {
    EditTableMul
  },
  data () {
    const validateContractName = (rule, value, callback) => {
      let reg = /^[_a-zA-Z]/

      if (!reg.test(value.slice(0, 1))) {
        callback(new Error('只能以英文及下划线开头'))
      }
      if (!TemContractName(value)) {
        callback(new Error('支持英文和数字，特殊符号只能有英文句号.和英文_'))
      } else {
        callback()
      }
    }
    return {
      languagetype: '',
      putaway: false,
      chaincode: false, // 查询合约链码弹框
      newdescribe: false, // 接口描述新增、修改弹框
      size: 50 * 1024 * 1024,
      AllList: [], // 选中的列表
      Loadstatus: false, // 按钮状态
      // 新建合约类型from表单
      formValidate: {
        name: '',
        scenario: '',
        describe: '',
        languagetype: '',
        chainType: '',
        contractId: ''
      },
      // from表单校验
      ruleValidate: {
        name: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
          { max: 32, message: '不能多于32位', trigger: 'blur' },
          { trigger: 'blur', validator: validateContractName }

        ],
        //

        scenario: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
          { max: 60, message: '不能多于60位', trigger: 'blur' }
          // { trigger: 'blur', validator: validatemiaoshu }
        ],
        describe: [
          {
            required: true,
            max: 200,
            message: '不能为空且长度不能超过200位',
            trigger: 'blur'
          }
        ]

      },
      // 接口描述
      modalDescribe: [

        {
          title: '函数名（name）',
          key: 'funcName',
          tooltip: true
        },
        {
          title: '参数（inputs）',
          key: 'parameter',
          tooltip: true
        },
        {
          title: '简介（description）',
          key: 'description',
          width: 175,
          tooltip: true
        },
        {
          title: '操作',
          key: 'action',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.descriptEdit(params)
                    }
                  }
                },
                '修改'
              ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.describeRemove(params)
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      modaldescribeData: [], // 接口描述数组
      //
      newfromValidate: {
        funcName: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur'
          }
        ]
      },
      // 模板上传数组
      transferKey1: '',
      fileName: '',
      describeTitleModal: '上架合约模板', // 修改或新增 标题
      describeStatus: '',
      describeTitle: '修改', // 修改或新增 标题
      // 接口描述的新增/修改表单
      newfrom: {
        funcName: '',
        parameter: '',
        description: '',
        index: null // 用于替换当前修改的行
      },
      // 版本信息
      VersionTitle: [],
      VersionTitleC: [
        {

          type: 'checkBox',
          align: 'center',
          width: 60,
          render: (h, params) => {
            // console.log(params.row.uploadVersion)
            return h('div', [
              h('Checkbox', {
                props: {
                  value: params.row.checkBox,
                  disabled: params.row.contractMarketStatus === '已上架'
                },
                on: {
                  'on-change': (e) => {
                    // console.log(this.VersionData[params.index].contractMarketStatus)
                    this.VersionData.forEach((items) => {
                      this.$set(items, 'checkBox', false)
                    })
                    this.VersionData[params.index].checkBox = e
                    if (e) {
                      this.AllList.splice(0, 1, params.row.uploadVersion)

                    } else {
                      this.AllList.length = []
                    }

                    // if (e) {
                    //   this.describeStatus = this.VersionData[params.index].contractMarketStatus
                    // } else {
                    //   this.describeStatus = ''
                    // }
                  }
                }
              })
            ])
          }
        },
        {
          title: '版本号',
          tooltip: true,
          key: 'uploadVersion'

        },
        {
          title: 'cpp文件名',
          key: 'cppFileName',
          tooltip: true
        },
        {
          title: 'hpp文件名',
          key: 'hppFileNames',
          tooltip: true,
          render: (h, params) => {
            return h('div', params.row.hppFileNames.join(','))
          }
        },
        {
          title: '上架状态',
          key: 'contractMarketStatus',
          tooltip: true
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      VersionTitleJ: [
        {

          type: 'checkBox',
          align: 'center',
          width: 60,
          render: (h, params) => {
            return h('div', [
              h('Checkbox', {
                props: {
                  value: params.row.checkBox,
                  disabled: params.row.contractMarketStatus === '已上架'
                },
                on: {
                  'on-change': (e) => {
                    this.isSingleCpp1 = params.row.isSingleCpp
                    // console.log(this.VersionData[params.index])
                    this.VersionData.forEach((items) => {
                      this.$set(items, 'checkBox', false)
                    })
                    this.VersionData[params.index].checkBox = e
                    if (e) {
                      this.AllList.splice(0, 1, params.row.uploadVersion)
                    } else {
                      this.AllList = []
                    }
                  }
                }
              })
            ])
          }
        },
        {
          title: '版本号',
          tooltip: true,
          key: 'uploadVersion'

        },
        {
          title: 'JavaScript文件名',
          key: 'jsFileName',
          tooltip: true,
          width: 150
        },
        {
          title: 'abi文件名',
          key: 'abiFileName',
          tooltip: true
          // render: (h, params) => {
          //   return h('div', params.row.hppFileNames.join(','))
          // }
        },
        {
          title: '上架状态',
          key: 'contractMarketStatus',
          tooltip: true
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      VersionTitleGo: [
        {
          type: 'checkBox',
          align: 'center',
          width: 60,
          render: (h, params) => {
            return h('div', [
              h('Checkbox', {
                props: {
                  value: params.row.checkBox,
                  disabled: params.row.contractMarketStatus === '已上架'
                },
                on: {
                  'on-change': (e) => {
                    this.isSingleCpp1 = params.row.isSingleCpp
                    // console.log(this.VersionData[params.index])
                    this.VersionData.forEach((items) => {
                      this.$set(items, 'checkBox', false)
                    })
                    this.VersionData[params.index].checkBox = e
                    if (e) {
                      this.AllList.splice(0, 1, params.row.uploadVersion)
                    } else {
                      this.AllList = []
                    }
                  }
                }
              })
            ])
          }
        },
        {
          title: '版本号',
          tooltip: true,
          key: 'uploadVersion'

        },
        // {
        //   title: '文件名',
        //   key: 'cppFileName',
        //   tooltip: true
        // },
        {
          title: '文件名',
          key: 'goFileNames'
          // tooltip: true,
          // render: (h, params) => {
          //   return h('div', params.row.hppFileNames.join(','))
          // }
        },
        {
          title: '上架状态',
          key: 'contractMarketStatus',
          tooltip: true
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      VersionData: [],
      CollContent: { cppcentent: {}, hppcentent: {}, jscentent: {}, abicentent: {}, gocentent: {} },
      filesHpp: [],
      title: '',// 查看文件源码标题
      isSingleCpp: '',
      isSingleCpp1: '',
      cppContent: '请选择要看的源码文件',
      cppsTitle: '',
    }
  },

  mounted () {

  },

  methods: {
    // transSize (size) { // 传入 字节 返回兆
    //   // 字节转兆  1m = 1024k 1k = 1024b
    //   // return size / 1024 / 1024
    //   return size * 1024 * 1024
    // },
    // 上传
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    okShare (formValidate) {
      this.$refs[formValidate].validate((valid) => {
        if (valid) {
          // console.log(this.AllList.length)
          if (this.AllList.length === 0) {
            this.msgInfo('warning', '请勾选版本信息！', true)
          } else if (this.modaldescribeData.length === 0) {
            this.msgInfo('warning', '接口描述不能为空！', true)
          } else {
            let fromList = {
              // contractName: this.basisinfo.contractName,
              contractId: this.formValidate.contractId, // 合约id
              contractType: this.formValidate.name, // 模板类型
              chainType: this.formValidate.chaincity, // 链类型
              languageType: this.formValidate.languagetype, // 语言类型
              description: this.formValidate.describe, // 描述场景
              scene: this.formValidate.scenario, // 适用场景
              selectVersion: this.AllList, // 版本信息,
              descriptionList: this.modaldescribeData, // 接口描述列表
              isSingleCpp: this.isSingleCpp1

            }
            getStencils(fromList).then(res => {
              this.Loadstatus = true
              if (res.code !== '00000') { this.Loadstatus = false; this.putaway = false; this.msgInfo('warning', res.message, true) } else {
                this.msgInfo('success', res.message, true)
                this.putaway = false
                this.Loadstatus = false
                this.$parent.getTableList()
              }
            }).catch(error => {
              this.putaway = false
              this.Loadstatus = false
              this.msgInfo('error', error.message, true)
            })
          }
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },
    cancelShare () {
      this.putaway = false
    },

    // 初始化接口描述新增/修改弹框input框数据
    init () {
      // 清空上架合约模板表格
      this.$nextTick(() => {
        this.$refs['formValidate'].resetFields()
      })
      // 清空新增接口描述表格
      this.$nextTick(() => {
        this.$refs['newfrom'].resetFields()
      })
    },
    // 接口描述 弹框 (修改、新增) 确定按钮
    describeOk () {
      this.$refs['newfrom'].validate((valid) => {
        // console.log(valid)
        if (valid) { // 检验通过
          this.newdescribe = false
          const { index, ...info } = this.newfrom
          if (this.describeTitle === '修改') {
            // 保存修改信息
            // console.log(info)
            this.modaldescribeData.splice(index, 1, info) // 替换当前项
          } else {
            // 保存新增
            this.modaldescribeData.push(info)
            // console.log(this.data1)
          }
          // this.initFormInfo()
          // 关闭弹框
        } else { // 校验失败
          // console.log(this.newdescribe)
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
      // console.log('点击确定按钮')
    },
    // 修改信息 打开弹框
    descriptEdit (info) {
      let { row } = info
      this.newdescribe = true
      this.describeTitle = '修改'
      // console.log(info)
      // console.log(this.newfrom)
      this.newfrom.parameter = row.parameter
      this.newfrom.funcName = row.funcName
      this.newfrom.description = row.description
      this.newfrom.index = info.index
    },
    // 打开新增弹框
    addInfo () {
      this.newdescribe = true
      this.describeTitle = '新增'
      // 清空新增接口描述表格
      this.$nextTick(() => {
        this.$refs['newfrom'].resetFields()
      })
    }, // 接口描述列表删除
    describeRemove (info) {
      this.modaldescribeData.splice(info.index, 1)
    },
    // 接口描述 弹框取消清空表单
    FormInfoCancel () {
      this.newdescribe = false
      // this.init()
    },
    // getSelectAll (list) {
    //   console.log(list.row.checkBox)
    //   if (list.row.checkBox) {
    //     this.AllList.splice(0, 1, list.row.uploadVersion)
    //   }
    //   console.log(this.AllList)
    //   // this.AllList = list.map(item => item.uploadVersion)
    //   // // this.AllList.push(list)
    // },
    // 点击文件源码
    fileModal (params) {
      console.log(params)
      this.chaincode = true
      this.title = params.row.uploadVersion
      this.codeData = {
        contractId: params.row.contractId,
        uploadVersion: params.row.uploadVersion
      }
      if (params.row.fileContent) {
        this.cppsTitle = params.row.fileContent
      }
      this.isSingleCpp = params.row.isSingleCpp
      if (this.formValidate.languagetype === 'C++') {
        this.transferKey1 = params.row.cppFileName
        this.filesHpp = params.row.hppFileNames
        this.getCode(params.row.cppFileName, 'cpp')
        if (params.row.hppFileNames && params.row.hppFileNames.length > 0) {
          params.row.hppFileNames.forEach(val => this.getNewCode(val, 'hpp'))
        }
      } else if (this.formValidate.languagetype === 'JS') {
        this.transferKey1 = params.row.jsFileName
        this.fileName = params.row.abiFileName
        this.getCode(params.row.jsFileName, 'js')
        this.getNewCode(params.row.abiFileName, 'abi')
      } else {
        this.fileName = params.row.goFileName
        this.getgoCode(params.row.goFileName, 'go')
      }
    },
    getCode (fileName) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          if (this.formValidate.languagetype === 'C++') {
            this.CollContent.cppcentent = res.data
          } else {
            this.CollContent.jscentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    getNewCode (fileName, val) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          if (this.formValidate.languagetype === 'C++') {
            this.CollContent.hppcentent = res.data
          } else {
            this.CollContent.abicentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    getgoCode (fileName) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          this.CollContent.gocentent = res.data
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 点击折叠面板事件
    // colldata (key) {
    //   if (key[0]) {
    //     this.codeData.fileName = key[0]
    //     getChaincode(this.codeData).then(res => {
    //       if (res.code === '00000') {
    //         this.CollContent = res.data
    //       }
    //     }).catch((error) => {
    //       this.msgInfo('error', error.message, true)
    //     })
    //   }
    // },
    // 滚动
    handScroll (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    gainSound (row) {
      this.formValidate.contractId = row.contractId
      this.formValidate.chaincity = row.chainType
      this.formValidate.languagetype = row.languageType === 'JavaScript' ? 'JS' : row.languageType
      this.languagetype = row.languageType
      this.VersionTitle = row.languageType === 'C++' ? this.VersionTitleC : row.languageType === 'JavaScript' ? this.VersionTitleJ : this.VersionTitleGo
      getShelvesTem(row.contractId).then(res => {
        let statusdata = {
          1: '已上架',
          2: '未上架'
        }
        let versionData = res.data.map(item => {
          return {
            ...item,
            hppFileNames: item.hppFileNames ? item.hppFileNames : [],
            contractMarketStatus: statusdata[item.contractMarketStatus]
          }
        })
        this.VersionData = versionData
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    clickCpps (value) {
      this.cppContent = value

    }

  }
}
</script>

<style scoped lang="less">
/deep/.ivu-menu-submenu-title {
  background: #fff !important;
}
/deep/.ivu-menu,
.ivu-menu-dark,
.ivu-menu-vertical,
.ivu-menu-opened,
.ivu-menu-submenu-title {
  background: #fff !important;
}
.ivu-menu-vertical.ivu-menu-light:after {
  background: #fff;
}
// 接口描述 取消按钮
.newdescribe {
  margin-left: 38%;
}

.mandatory {
  /deep/.ivu-form-item-label::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
  p {
    margin-bottom: 15px;
  }
}
//滚动条
.textarea-style {
  width: 100%;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
</style>
