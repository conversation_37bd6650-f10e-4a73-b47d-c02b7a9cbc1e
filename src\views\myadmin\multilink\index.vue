<template>
  <div class="multilink_index">
    <keep-alive v-if="currentTab === 'multilink_admin'">
    <Multilinkadmin />
    </keep-alive>
    <keep-alive :exclude="excludeArr" v-else>
    <router-view />
    </keep-alive>
  </div>
</template>

<script>
import Multilinkadmin from './admin.vue'
export default {
  name: 'multilink_index',
  components: {
    Multilinkadmin
  },
  data () {
    return {
      excludeArr: ['multilink_details']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>

<style lang="less" scoped>
.multilink_index{
 //
}
</style>
