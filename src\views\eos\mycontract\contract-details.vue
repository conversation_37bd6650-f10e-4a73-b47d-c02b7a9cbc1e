<template>
  <div class="contract">
    <Collapse v-model="panelValue" simple name="mainpanel">
      <Panel name="1" style="background:#ECEFFC;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        合约基础信息
        <p slot="content" class="basetext" style="display: flex;">
          <span style="width: 50%;display: block;">合约名称：{{ arrDetails.contractName }}</span>
          <span style="width: 50%;display: block;">创建时间：{{ arrDetails.createTime }}</span>
        </p>
        <p slot="content" class="basetext" style="display: flex;">
          <span style="width: 50%;display: block;">应用名称：{{ arrDetails.contractReadableName }}</span>
          <span style="width: 50%;display: block;">项目名称：{{ arrDetails.projectName }}</span>
        </p>
        <p slot="content" class="basetext" style="display: flex;">
          <span style="width: 50%;display: block;">应用简介：{{ arrDetails.brief }}</span>
          <span style="width: 50%;display: block;">合约语言：{{arrDetails.languageType==='JS'?'Java Script':arrDetails.languageType}}</span>
        </p>
        <p slot="content" class="basetext">
          <span style="margin-right:-15px;">合约共享：{{ arrDetails.isShared ? '已共享' : '未共享' }}</span>
          <span v-if="arrDetails.isShared"><Button @click="onDetail" style="margin-bottom:2px;">查看详情</Button></span>
          <!-- <span v-if="!arrDetails.isDeployed">
            <Poptip trigger="hover" content="该合约还未部署成功">
              <Button @click="onShare" :disabled="!arrDetails.isDeployed" style="margin-bottom:2px;">向其它租户共享合约</Button>
            </Poptip>
          </span> -->
          <!-- <span v-if="!arrDetails.isShared && arrDetails.isDeployed">
            <Button @click="onShare" style="margin-bottom:2px;">向其它租户共享合约</Button>
          </span> -->
        </p>

      </Panel>
      <Panel name="2" style="background:#ECEFFC;display:block;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        合约版本信息
        <div slot="content">
          <!-- JavaScript语言 -->
          <div v-if="arrDetails.languageType==='JS'">
            <!-- js文件上传 -->
            <p class="basetext" style="margin-bottom:10px">
              <span style="float:left;padding:28px 0 0 26px;">JavaScript文件上传：</span>
              <Upload :height='100' multiple type="drag" action="" :before-upload="handleJs" :accept="'.js'" :format="['js']">
                <div v-if="this.fileStatus.jsstatus===false">
                  <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                  <h4>支持拖拽上传文件</h4><br />
                  <p style="color: #aaa;font-size: 12px;">JavaScript文件上传(必填)，并根据已上传的JavaScript文件生成abi文件，编辑后再次进行abi文件的上传。<br />(JavaScript文件与abi文件一一对应)</p>
                </div>
                <div style="padding: 20px 0" v-else>
                  <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
                  <h4>完成文件上传</h4>
                  <p style="color: color: #3399ff;font-size: 15px;">{{ jsFile ? jsFile.name : '' }}</p>
                </div>
              </Upload>
            </p>
            <!--  abi下载按钮 -->
            <div class="basetext" style="margin-bottom:10px;display:flex;width:100%">
              <span style="float:left;padding: 5px 0px 0px 90px;margin:0px;display: inline-block;width: 200px;">abi文件下载：</span>
              <span style="width:93.5%;margin: 0px 0px 0px 1%;"><Button type="success" style="width:100%" @click="abiDown" :disabled="abidisa">abi文件下载</Button></span>
            </div>
            <!-- abi文件上传 -->
            <p class="basetext" style="margin-bottom:10px">
              <span style="float:left;padding:28px 0 0 64px;">abi文件上传： </span>
              <Upload multiple type="drag" action="" :before-upload="handleAbi" :accept="'.abi'" :format="['abi']">
                <div v-if="this.fileStatus.abistatus===false">
                  <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                  <h4>支持拖拽上传文件</h4><br />
                  <p style="color: #aaa;font-size: 12px;">abi文件上传(必填),请上传已编辑好的abi文件。 <br />(JavaScript文件与abi文件一一对应)</p>
                </div>
                <div style="padding: 20px 0" v-else>
                  <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
                  <h4>完成文件上传</h4>
                  <p style="color: color: #3399ff;font-size: 15px;">{{abiFile ? abiFile.name : ''}}</p>
                </div>
              </Upload>
            </p>
            <!-- api文件上传 -->
            <p class="basetext" style="margin-bottom:10px">
              <span style="float:left;padding:28px 0 0 64px;">API文件上传：</span>
              <Upload :height='100' multiple type="drag" action="" :before-upload="handleApi" :accept="'.doc,.docx,.txt,.pdf'" :format="['doc','docx','txt','pdf']">
                <div v-if="this.fileStatus.apistatus===false">
                  <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                  <h4>支持拖拽上传文件</h4><br />
                  <p style="color: #aaa;font-size: 12px;">合约接口规范文件上传（必填），仅限单个文件上传； <br />
                    合约接口规范文件是根据业务需求，规定各个方法的各个字段是否必填，字段长度及类型的文档，无模板要求。</p>
                </div>
                <div style="padding: 20px 0" v-else>
                  <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
                  <h4>完成文件上传</h4>
                  <p style="color: color: #3399ff;font-size: 15px;">{{ apiFile ? apiFile.name : '' }}</p>
                </div>
              </Upload>
            </p>
            <!-- 安全扫描报告上传 -->
            <p class="basetext" style="margin-bottom:10px">
              <span style="float:left;padding:28px 0 0 26px;">安全扫描报告上传：</span>
              <Upload multiple type="drag" action="" :before-upload="handleSecurity" :accept="'.doc,.docx,.txt,.pdf,.xls,.xlsx,.zip,.rar,.png,.jpg,.jpeg'" :format="['doc','docx','txt','pdf','xls','xlsx','zip','rar','txt','png','jpg','jpeg']">
                <div style="padding: 10px 0" v-if="this.fileStatus.securitystatus===false">
                  <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                  <h4>支持拖拽上传文件</h4><br />
                  <p style="color: #aaa;font-size: 12px;">安全扫描报告（必填），安全扫描报告可以一个或多个;<br />安全扫描报告可以用磐舟或磐基的代码扫描工具扫描代码后生成，无格式要求 </p>
                </div>
                <div style="padding: 20px 0" v-else>
                  <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
                  <h4>完成文件上传</h4>
                  <p style="color: color: #3399ff;font-size: 15px;">{{ securityFile.length>0 ? securityFile.map(val =>val.name).join(',') : '' }}</p>
                </div>
              </Upload>
            </p>
            <p class="basetext" style="margin-bottom:10px;text-align:center;">
              <span style="">
                <Button :disabled='btndelete' type="error" ghost @click="jsFile=null;abiFile=null;securityFile=[];fileStatus.jsstatus=false;fileStatus.abistatus=false;fileStatus.securitystatus=false;abidisa = true;fileSize=[];apiFile=null;manyApiFile=null;cppApi=''">删除</Button>
                <Button type="primary" style="margin: 0 10px;" :disabled="hasEditPermission" icon="ios-cloud-upload-outline" @click="submitUpload" :loading="loadingjsStatus">{{ loadingjsStatus ? '上传中..' : '提交上传' }}</Button>
              </span>
            </p>
            <edit-table-mul :key="transferKey" :columns="jscolumns" v-model="jstableData"></edit-table-mul>

          </div>
          <!-- C++语言 -->
          <div v-else style="margin-bottom:10px">
            <!-- <div style="display: flex;margin-left: 3.1%;padding-top: 1%;">
              <p>cpp文件：</p>
              <RadioGroup v-model="changeModal">
                <Radio label="onecpp">单cpp</Radio>
                <Radio label="manyccpp">多cpp</Radio>
              </RadioGroup>
            </div> -->

            <!-- 单cpp -->
            <div v-if="this.changeModal==='onecpp'">
              <div v-if="ideShowZ">
                <div v-if="ideShow">
                  <div class="create-new" v-show="ideNew && ideOpen">
                    <span>上传新版本：</span>
                    <Button type="primary" @click="onlineEditContract('', 'ADD')" v-cloak>在线新建版本</Button>
                  </div>
                </div>
              </div>

              <p class="basetext" style="text-align: center;">
                <Upload action="" type="drag" multiple :accept="'.cpp,.hpp,.txt,.doc,.docx,.xlsx,.xls,.jpg,.png,.jpeg,.pdf,.zip,.rar'" :format="['cpp','hpp','txt','doc','docx','xlsx','xls','jpg','png','jpeg','pdf','zip','rar']" :max-size="maxsize" :before-upload="handleUpload" style="display:inline-table;width:82%;margin-top:10px;">
                  <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                  <h4>支持拖拽上传文件</h4><br />
                  <p style="color: #aaa;font-size: 12px;">支持上传cpp文件(必填)、hpp文件(选填)、安全扫描报告（必填），且cpp文件只能有一个且部署时需要与合约链账户名称一致;<br />hpp文件可以一个或多个，安全扫描报告可以一个或多个;<br />合约代码中类名需与合约名称一致。<br />安全扫描报告可以用磐舟或磐基的代码扫描工具扫描代码后生成，无格式要求</p>
                </Upload>
              </p>
              <p class="basetext" style="text-align: center;">
                <Upload action="" type="drag" multiple :accept="'.doc,.docx,.txt,.pdf'" :format="['doc','docx','txt','pdf']" :max-size="maxsize" :before-upload="handleUploadApi" style="display:inline-table;width:82%;margin-top:10px;">
                  <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                  <h4>支持拖拽上传文件</h4><br />
                  <p style="color: #aaa;font-size: 12px;">合约接口规范文件上传（必填），仅限单个文件上传；<br />
                    合约接口规范文件是根据业务需求，规定各个方法的各个字段是否必填，字段长度及类型的文档，无模板要求。</p>
                </Upload>
              </p>
              <div v-if="showfile" class="basetext">
                <p style="color:#2d8cf0">
                  <!-- <Icon type="ios-link" /> cpp文件: {{ cppfile ? cppfile.name : '' }} -->
                  <Icon type="ios-link" /> cpp文件:

                  <Tag v-if="cppfile" closable @on-close="cppfile=null">
                    {{ cppfile.name }}
                  </Tag>
                </p>
                <Divider />
                <p style="margin-left:0;color:#2d8cf0">
                  <!-- <Icon type="ios-link" /> hpp文件: {{ hppfile.length>0 ? hppfile.map(val =>val.name).join(',') : '' }} -->
                  <Icon type="ios-link" /> hpp文件:
                  <Tag v-for="(item,index) in hppfile" closable @on-close="handleCloseHpp(item)" :key='item.name'>{{item.name}}</Tag>
                </p>
                <Divider />
                <p style="margin-left:0;color:#2d8cf0 ">
                  <Icon type="ios-link" /> API文件:
                  <!-- {{  cppApi? cppApi.name : ''  }} -->

                  <Tag v-if="cppApi" closable @on-close="cppApi=null">
                    {{ cppApi.name }}
                  </Tag>
                </p>
                <Divider />
                <p style="margin-left:0;color:#2d8cf0 ">
                  <Icon type="ios-link" /> 安全扫描文件:
                  <Tag v-for="(item,index) in securityfile" closable @on-close="handleClose(item)" :key='item.name'>{{item.name}}</Tag>
                  <!-- <span v-for="item in securityfile" class="securityfileClass">
                    {{item.name}},
                  </span> -->
                  <!-- {{ securityfile.length>0 ? securityfile.map(val => val.name).join(',') : '' }} -->

                </p>
                <Divider />
                <span style="float:right;margin-bottom:10px">
                  <Button type="error" ghost @click="cppfile=null;hppfile=[];securityfile=[];showfile=false;apiFile=null;manyApiFile=null;cppApi=''">删除</Button>
                  <Button type="primary" style="margin: 0 10px;" :disabled="hasEditPermission" icon="ios-cloud-upload-outline" @click="modalEg=true" :loading="loadingStatus">{{ loadingStatus ? '上传中..' : '提交上传' }}</Button>
                </span>
                <Divider />
              </div>
            </div>

            <!-- 多cpp -->
            <div v-else>
              <div style="display:flex;">
                <span style="padding:28px 0 0 26px;">上传新版本：</span>
                <Button type="primary" @click="getFileDowns">多CPP文件模板下载</Button>
                <span class="fileDown" @click="getFileDown">文件规范下载</span>
              </div>
              <!--多cpp  -->
              <p class="" style="margin-bottom:10px;">
                <Upload multiple type="drag" action="" :before-upload="handlemanyCpp" style="display:inline-table;width:82%;margin-top:10px;margin-left: 7.5%;">
                  <div style="padding: 10px 0" v-if="this.manycppStatus.cppstatus===false">
                    <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                    <h4>支持拖拽上传文件</h4><br />
                    <p style="color: #aaa;font-size: 12px;">多cpp文件上传（必填）</p>
                  </div>
                  <div style="padding: 20px 0" v-else>
                    <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
                    <h4>完成文件上传</h4>
                    <p style="color: color: #3399ff;font-size: 15px;">{{manyCpp ? manyCpp.name : ''}}</p>
                  </div>
                </Upload>
              </p>
              <!--多api -->
              <p class="" style="margin-bottom:10px;">
                <Upload multiple type="drag" action="" :before-upload="handlemanyApi" style="display:inline-table;width:82%;margin-top:10px;margin-left: 7.5%;">
                  <div style="padding: 10px 0" v-if="this.manycppStatus.apistatus===false">
                    <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                    <h4>支持拖拽上传文件</h4><br />
                    <p style="color: #aaa;font-size: 12px;">合约接口规范文件上传（必填），仅限单个文件上传；<br />
                      合约接口规范文件是根据业务需求，规定各个方法的各个字段是否必填，字段长度及类型的文档，无模板要求。</p>
                  </div>
                  <div style="padding: 20px 0" v-else>
                    <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
                    <h4>完成文件上传</h4>
                    <p style="color: color: #3399ff;font-size: 15px;">{{manyApiFile ? manyApiFile.name : ''}}</p>
                  </div>
                </Upload>
              </p>
              <!--多安全扫描报告  -->
              <p class="" style="margin-bottom:10px;">
                <Upload multiple type="drag" action="" :before-upload="handlemanySecurity" :accept="'.doc,.docx,.txt,.pdf,.xls,.xlsx,.zip,.rar,.png,.jpg,.jpeg'" :format="['doc','docx','txt','pdf','xls','xlsx','zip','rar','txt','png','jpg','jpeg']" style="display:inline-table;width:82%;margin-top:10px;margin-left: 7.5%;">
                  <div style="padding: 10px 0" v-if="this.manycppStatus.securitystatus===false">
                    <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                    <h4>支持拖拽上传文件</h4><br />
                    <p style="color: #aaa;font-size: 12px;">安全扫描报告文件上传（必填）</p>
                  </div>
                  <div style="padding: 20px 0" v-else>
                    <Icon type="ios-checkmark-circle-outline" size="52" style="color: #3399ff" />
                    <h4>完成文件上传</h4>
                    <p style="color: color: #3399ff;font-size: 15px;">{{ manySecurityFile.length>0 ? manySecurityFile.map(val =>val.name).join(',') : '' }}</p>
                  </div>
                </Upload>
              </p>
              <!-- 提交 -->
              <p class="basetext" style="margin-bottom:10px;text-align:center;">
                <span style="">
                  <Button :disabled='manybtndelete' type="error" ghost @click="manyCpp=null;manyApiFile=null;apiFile=null;manySecurityFile=[];manycppStatus.cppstatus=false;manycppStatus.apistatus=false;manycppStatus.securitystatus=false;manyfileSize=[];cppApi=''">删除</Button>
                  <Button type="primary" style="margin: 0 10px;" :disabled="hasEditPermission" icon="ios-cloud-upload-outline" @click="submitmanyUpload" :loading="loadingjsStatus">{{ loadingjsStatus ? '上传中..' : '提交上传' }}</Button>
                </span>
              </p>
            </div>
            <edit-table-mul :key="transferKey" :columns="columns" v-model="tableData"></edit-table-mul>
          </div>
        </div>
      </Panel>
    </Collapse>
    <Modal v-model="modal" title="合约部署" :draggable="true" :mask-closable="false" sticky width="800" @on-cancel="initModal">
      <b style="line-height: 36px;">合约信息</b>
      <p style="line-height: 30px;">
        <span style="white-space: nowrap;">版本号：</span>
        {{uploadVersion }}
      </p>
      <p style="line-height: 30px;">
        <span style="white-space: nowrap;">应用名称：</span>
        {{ arrDetails.contractReadableName }}
      </p>
      <p style="line-height: 30px;">
        <span style="white-space: nowrap;">合约语言：</span>
        {{ arrDetails.languageType==='JS'?'Java Script' :arrDetails.languageType }}
      </p>
      <div v-if="arrDetails.languageType==='JS'">
        <p style="line-height: 30px;display:flex;margin-top:10px;">
          <span style="white-space: nowrap;">目标链：</span>
          <Select v-model="alertArr.chainId" placeholder="选择目标链" @on-change="getjsChainTable" filterable>
            <Option v-for="item in chainIdList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
            <Option :value="alertArr.chainId" :label="jsalertArr.chainId" :disabled="true" v-if="chainIdPageParam.pageIndex < pages && chainIdList.length>0" style="text-align:center">
              <span @mouseover="reachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
            </Option>
            <Option :value="alertArr.chainId" :label="alertArr.chainId" :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
              <span style="font-size:8px;">已加载全部</span>
            </Option>
          </Select>
        </p>
        <p style="margin-left: 8%;margin-top: 1%;color: #86909c;"> 提示：当前JavaScript合约，仅支持CMEOS链类型，下拉框中仅展示CMEOS类型的链</p>
        <p style="line-height:30px;display:flex;margin-top:10px;">
          <span style="white-space: nowrap;">链帐户：</span>
          <Select v-model="jsalertArr.chainAccountName" placeholder="选择链帐户名称" filterable clearable :key="transferKey3">
            <Option v-for="item in jschainTable" :value="item.chainAccountName" :key="item.id" @click.native="jsgetPastOps(item.id,item.chainAccountName)">{{item.chainAccountName}}</Option>
          </Select>
        </p>
      </div>
      <div v-else>
        <p style="line-height: 30px;display:flex;margin-top:10px;">
          <span style="white-space: nowrap;">目标链：</span>
          <Select v-model="alertArr.chainId" placeholder="选择目标链" @on-change="getChainTable" filterable>
            <Option v-for="item in chainIdList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
            <Option :value="alertArr.chainId" :label="alertArr.chainId" :disabled="true" v-if="chainIdPageParam.pageIndex < pages && chainIdList.length>0" style="text-align:center">
              <span @mouseover="reachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
            </Option>
            <Option :value="alertArr.chainId" :label="alertArr.chainId" :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
              <span style="font-size:8px;">已加载全部</span>
            </Option>
          </Select>
        </p>
        <p style="line-height:30px;display:flex;margin-top:10px;">
          <span style="white-space: nowrap;">链帐户：</span>
          <span style="display: block;border-radius: 3px;width: 100%;height: 30px;border: 1px solid;">{{this.chainTable.chainAccountName}}</span>
          <!-- <Input  v-model="chainTable.chainAccountName"   placeholder="选择合约链帐户名称"  @click.native="getPastOps(chainTable.chainAccountId)"/> -->
          <!-- <Select v-model="alertArr.chainAccountName" placeholder="选择合约链帐户名称" filterable clearable :key="transferKey2">
          <Option v-for="item in chainTable" :value="item.chainAccountName" :key="item.chainAccountName" @click.native="getPastOps(item.chainAccountId)"></Option>

        </Select> -->
        </p>
      </div>
      <b style="line-height: 36px;">运维信息</b>
      <div v-show="formgroup" :key="transferKey2">
        <p style="line-height:30px">
          合约类型：{{ ops.contractTypeDesc }}
          <Button style="float: right;" @click.native="editOps" type="primary">编辑</Button>
        </p>
        <p style="line-height:30px"> TPS预估：{{ ops.tps }} </p>
        <p style="line-height:30px"> 运维联系人：
          <span v-if="ops.opsLinkman&&ops.opsLinkman.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ ops.opsLinkman.tenantName }}</span>
          <span v-if="ops.opsLinkman&&ops.opsLinkman.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ ops.opsLinkman.name }}</span>
          <span v-if="ops.opsLinkman&&ops.opsLinkman.phone"><i class="ri-smartphone-line"></i>{{ ops.opsLinkman.phone }}</span>
        </p>
        <p style="line-height:30px"> 需求联系人：
          <span v-if="ops.demandSide&&ops.demandSide.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ ops.demandSide.tenantName }}</span>
          <span v-if="ops.demandSide&&ops.demandSide.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ ops.demandSide.name }}</span>
          <span v-if="ops.demandSide&&ops.demandSide.phone"><i class="ri-smartphone-line"></i>{{ ops.demandSide.phone }}</span>
        </p>
        <p class="diaoyong">
          <span style="vertical-align: top;line-height:30px">调用联系人：</span>
        <ul style="display:inline-block;line-height:30px;margin-left:4px;">
          <li v-for="item in ops.caller" :key="item.name + Math.random()">
            <span v-if="item.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ item.tenantName }}</span>
            <span v-if="item.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ item.name }}</span>
            <span v-if="item.phone"><i class="ri-smartphone-line"></i>{{ item.phone }}</span>
          </li>
        </ul>
        </p>
      </div>
      <!-- <div :key="transferKey2 + Math.random()"> -->
      <div>
        <FormGroup :tenantId="tenantId" style="margin-left: -50px;" :flag="!formgroup" ref="childMethod" :pastOps="JSON.stringify(ops)" v-show="!formgroup"></FormGroup>
      </div>
      <div slot="footer">
        <Button @click="initModal">取消</Button>
        <Button type="primary" @click="ok">确定</Button>
      </div>
    </Modal>
    <Modal v-model="modalDeploy" title="合约部署记录" width="900" :draggable="true" sticky :mask-closable="false" @on-ok="cancel" @on-cancel="cancel">
      <p style="line-height: 30px;padding-bottom:20px">
        <span style="white-space: nowrap;">版本号：</span>
        {{ uploadVersion }}
      </p>
      <edit-table-mul :columns="columnsDeploy" v-model="arrDeploy"></edit-table-mul>
    </Modal>
    <Modal v-model="modalCompileLog" title="合约编译日志" width="900" :draggable="true" sticky :mask-closable="false" @on-ok="cancel" @on-cancel="cancel">
      <p style="line-height:36px"> 应用名称：{{ arrCompileLog.contractReadableName }} &nbsp;&nbsp;&nbsp;&nbsp; 编译版本号：{{ arrCompileLog.compileVersion }} &nbsp;&nbsp;&nbsp;&nbsp; 编译状态：{{ arrCompileLog.compileStatus }} &nbsp;&nbsp;&nbsp;&nbsp; 上传日期：{{ arrCompileLog.uploadTime }} </p>
      <!-- <p style="background-color:#e8e8e8;">{{ arrCompileLog.compileLog }} </p> -->
      <div v-if="arrCompileLog.compileLog" style="padding-top:10px;">
        <textarea class="textarea-style" v-html="arrCompileLog.compileLog" />
      </div>
      <div v-else style="text-align:center;height:auto;">
        <img :src="nullUrl">
        <p style="color:#d4d3d3;font-size:8px;">暂无合约编译日志</p>
      </div>
    </Modal>
    <Modal v-model="modalCode" title="查询合约链码" width="900" :draggable="true" sticky :mask-closable="false" @on-ok="cancel" @on-cancel="cancel">
      <p style="line-height:36px">&nbsp;&nbsp;&nbsp;&nbsp;上传版本号：{{ arrChainCode.cppObj.uploadVersion }} </p>
      <br>
      <Collapse v-model="filepanel" simple accordion :key="transferKey1" v-if="arrChainCode.cppObj.fileContent || arrChainCode.hppObj.fileContent">
        <Panel name="codeDetails1" v-if="arrChainCode.cppObj.fileContent && arrChainCode.cppObj.fileContent !== undefined">
          cpp文件名：{{ arrChainCode.cppObj.fileName }}
          <div slot="content">
            <Spin v-if="cppTopLoading">
              <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
              <div>上一页加载中。。。</div>
            </Spin>
            <textarea class="textarea-style" v-html="arrChainCode.cppObj.fileContent" readonly @scroll="handScroll($event, 'cpp')"></textarea>
            <Spin v-if="cppBottomLoading">
              <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
              <div>下一页加载中。。。</div>
            </Spin>
          </div>
        </Panel>
        <Panel :name="item" v-for="item in alertArr.hppNames &&  alertArr.hppNames.length>0? alertArr.hppNames:[]" :key="item">
          hpp文件名：{{ item }}
          <div slot="content">
            <Spin v-if="hppTopLoading">
              <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
              <div>上一页加载中。。。</div>
            </Spin>
            <textarea class="textarea-style" v-html="arrChainCode.hppObj[item].fileContent" readonly @scroll="handScroll($event, 'hpp')" v-if="arrChainCode.hppObj[item]&&arrChainCode.hppObj[item].fileContent"></textarea>
            <Spin v-if="hppBottomLoading">
              <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
              <div>下一页加载中。。。</div>
            </Spin>
          </div>
        </Panel>
      </Collapse>
      <Spin v-else>
        <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
        <div>{{loadingMsg}}</div>
      </Spin>
    </Modal>
    <Modal v-model="modalEg" title="提示" @on-ok="upload" @on-cancel="modalEg=false" cancelText="取消" okText="确定，继续提交" class="eg-class">
      <p>请确定上传的版本中，“合约代码类名”与合约名称一致，否则将部署失败。</p>
      <p style="font-weight: bold">示例</p>
      <p>合约代码类名为：<a>hello</a>，则合约名称应为：<a>hello</a></p>
      <p style="height:auto;margin-top:10px">
        <img style="width:100%" :src="egImg" alt="">
      </p>
    </Modal>
    <ModalCommon ref="modalCommonRef" @getDetails='getDetails'></ModalCommon>
    <!-- 下载历史 -->
    <Modal v-model="historymodal" title="下载详情" width='700'>
      <edit-table-mul border :columns="modalHistory" v-model="modalHistoryData"></edit-table-mul>
    </Modal>
    <!--  -->
    <Modal v-model="modal3" footer-hide draggable class-name="vertical-center-modal">
      <!-- <p>当前cpp文件名称不符合合约链账户名称创建要求，请重新调整cpp文件名称</p> -->
      <p v-if="isSingleCpp=='0'">系统没有与当前版本压缩包名 称一致的合约链账户，点击确认按钮进入链账户创建页面，点击取消按钮返回部署界面。</p>
      <p v-else>系统没有与当前版本CPP文件名称一致的合约链账户，点击确认按钮进入链账户创建页面，点击取消按钮返回部署页面。</p>

      <div style="">
        <Button style="margin: 2% 2% 2% 35%;" type="primary" @click="jumpNew">确认</Button>
        <Button @click="cancelModal">取消</Button>
      </div>

    </Modal>
    <!-- js查看合约源码 -->
    <Modal v-model="jschaincode" title="查询合约链码" width='900px'>
      <p style="margin-bottom:20px">上传版本号：{{this.titlejs}}</p>
      <Collapse simple accordion>
        <Panel :name="jsFilename">
          {{jsFilename}}
          <p slot="content">
            <textarea class="textarea-style" v-html="CollContentfile.jscentent.fileContent" readonly @scroll="handScrolljs($event, 'js')"></textarea>
          </p>
        </Panel>
        <Panel :name="abiFilename">
          {{abiFilename}}
          <p slot="content">
            <textarea class="textarea-style" v-html="CollContentfile.abicentent.fileContent" readonly @scroll="handScrolljs($event, 'abi')"></textarea>
          </p>
        </Panel>
      </Collapse>
    </Modal>

    <!-- 查看多cpp合约源码 -->
    <Modal v-model="jschaincodes" title="查询合约链码" width='890px'>
      <div>
        上传版本号:{{this.titlejs}}
      </div>
      <Layout>
        <Sider hide-trigger :style="{background: '#fff'}">
          <Menu theme="light" width="auto" :open-names="['1']">
            <Submenu :name="key" v-for="(item,key) in cppsTitle" :key="key">
              <template slot="title">
                <Icon type="ios-folder"></Icon>
                {{key}}
              </template>
              <MenuItem :name="items.fileName" v-for="(items,i) in item" :key="i" @click.native="clickCpps(items.content)">{{items.fileName}}</MenuItem>
            </Submenu>
          </Menu>
        </Sider>
        <Content :style="{padding: ' 0 24px', minHeight: '280px', background: '#fff'}">
          <p>
            <textarea class="textarea-style" v-html="cppContent" readonly @scroll="handScrolljs($event, 'abi')"></textarea>
          </p>
        </Content>
      </Layout>
    </Modal>
  </div>
</template>

<script>
import { getChainIdList, getContractDetails, getContractChaincode, addContractDeploy, getContractDeploy, getContractCompileLog, uploadFile, getContractOps, recall, download, contractIdeNew, getChaincode } from '@/api/data'
import { getChainTableDataLian, uploadjsFile, getChainid, abidownFile, uploadmanyFile, getauditdownloadFile, getFile, getconfig } from '@/api/contract'
import EditTableMul from '_c/edit-table-mul'
import FormGroup from '_c/form-group/form-grouptwo'
import egImg from '@/assets/img/eg.png'
import ModalCommon from './modal-common.vue'
import { loginSysConfig } from '@/api/user'
import { isPowerAccount } from '../../../lib/check'
import { localRead } from '@/lib/util'
export default {
  name: 'contract_details',
  components: {
    EditTableMul,
    FormGroup,
    ModalCommon
  },
  data () {
    // const validateAccount = (rule, value, callback) => {
    //   if (!isPowerAccount(value)) {
    //     callback(new Error('仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字'))
    //   } else {
    //     callback()
    //   }
    // }
    return {

      manycppStatus: {
        cppstatus: false,
        apistatus: false,
        securitystatus: false
      },
      manyCpp: null, //
      manyApiFile: null, //
      manySecurityFile: [], //
      manyfileSize: [],
      changeModal: 'onecpp',
      manybtndelete: true,
      // 以上为s14迭代新增
      CollContentfile: { jscentent: {}, abicentent: {} },
      btndelete: true,
      abidisa: true,
      uploadIdw: '',
      abiFilename: '',
      jsFilename: '',
      CollContent: {},
      titlejs: '',
      codeData: {},
      jschaincode: false,
      transferKey3: 0,
      languageType: this.$route.params.languageType ? this.$route.params.languageType : '',
      jsFile: null,
      abiFile: null,
      apiFile: null,
      securityFile: [],
      jsFilesize: [],
      fileStatus: {
        jsstatus: false,
        abistatus: false,
        securitystatus: false,
        apistatus: false
      },
      loadingjsStatus: false,
      jstableData: [],
      jschaincodes: false,
      jscolumns: [
        { key: 'uploadVersion', title: '版本号', tooltip: true },
        { key: 'jsName', title: 'JavaScript文件名', tooltip: true },
        { key: 'abiName', title: 'abi文件名', tooltip: true },
        {
          key: 'interfacDoc',
          title: 'API文件',
          tooltip: true,
          render: (h, params) => {
            return h('span', params.row.interfacDoc === null ? '/' : params.row.interfacDoc)
          }
        },
        {
          key: 'securityScanReportNames',
          // title: '安全扫描报告文件名',
          renderHeader: (h, params) => {
            return h('div', [
              h('span', '安全扫描报告文件名'),
              h('Tooltip', {
                props: {
                  placement: 'top',
                  transfer: true,
                },
              },
                [
                  h('Icon', {
                    props: {
                      type: 'ios-help-circle-outline',
                    },
                    style: {
                      marginRight: '5px'
                    }
                  }),
                  h('span', {
                    slot: 'content', //slot属性
                    style: {
                      whiteSpace: 'normal',
                      wordBreak: 'break-all'
                    }
                  }, [
                    h('p'), '1、安全扫描报告可以用磐舟或磐基的代码扫描工具扫描代码后生成',
                    h('p'), '2、安全扫描报告无格式要求',
                  ])

                ]
              ),

            ])
          },
          render: (h, params) => {
            return h('div', params.row.securityScanReportNames.join(','))
          },
          tooltip: true
        },
        { key: 'uploadBrief', title: '上传备注', tooltip: true },
        { key: 'uploadTime', title: '上传时间', tooltip: true },
        { // key: 'action',
          title: '操作',
          minWidth: 130,
          align: 'left',
          render: (h, params) => {
            return h('div', { style: { margin: '5px 0 5px 0', padding: '5px' } },
              [
                h('Button', {
                  props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                  style: this.buttonStyle,
                  on: { click: () => { this.jsaddDeploy(params) } }
                }, '部署'),
                h('Button', {
                  props: { type: 'text', size: 'small' },
                  style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                  on: { click: () => { this.fileModal(params) } }
                }, '文件源码'),
                h('Dropdown', {
                  props: { placement: 'top' }
                }, [
                  h('Button', {
                    props: { type: 'text', size: 'small' },
                    style: { 'font-weight': '600', 'font-size': '18px', margin: '5px 8px 5px 0', color: '#3D73EF', border: '1px solid #3D73EF' }
                  }, '···'),
                  // class: params.index <2 ? 'dropdownmenu-diy-top' : 'dropdownmenu-diy-btm'
                  h(
                    'DropdownMenu',
                    {
                      slot: 'list'
                      // class: 'dropdownmenu-diy-btm'
                    },
                    [h('DropdownItem', [
                      h('Button', {
                        props: { type: 'text', size: 'small' },
                        style: { marginRight: '0px', color: '#3D73EF', border: '1px solid #3D73EF' },
                        on: { click: () => { this.getDeploy(params.index) } }
                      }, '部署记录')
                    ]),
                    h('DropdownItem', [
                      h('Button', {
                        props: { type: 'text', size: 'small' },
                        style: { marginRight: '0px', color: '#3D73EF', border: '1px solid #3D73EF' },
                        on: { click: () => { this.Downloadhistory(params.index) } }
                      }, '下载历史')
                    ]),
                    h('DropdownItem', [
                      h('Button', {
                        props: { type: 'text', size: 'small', disabled: !params.row.isRecall },
                        style: {
                          marginRight: '0px',
                          border: '1px solid #3D73EF',
                          borderColor: params.row.isRecall ? '#3D73EF' : '#c5c8ce',
                          color: params.row.isRecall ? '#3D73EF' : '#c5c8ce !important',
                          // background: params.row.isRecall ? '#fff' : '#f7f7f7',
                          opacity: params.row.isRecall ? 1 : 0.8
                        },
                        on: { click: () => { this.withDraw(params.index) } }
                      }, '撤回')
                    ])

                    ]
                  )
                ])
              ])
          }
        }
      ],
      jschainTable: [],
      jsalertArr: { index: null, chainId: '', chainAccountName: '', name: '' },
      jschainIdList: [], // 目标链
      //  以上是js合约新加的
      modal3: false,
      ideOpen: false,
      ideNew: false,
      infoSize: '',
      historymodal: false, // 下载历史弹框
      detailModal: false,
      shareModal: false,
      egImg,
      modalEg: false, // 上传提交前的提示
      timerStamp: null,
      isFlag: false,
      imgUrl: require('@/assets/img/arrow.png'),
      pages: 0,
      cppBottomLoading: false,
      cppTopLoading: false,
      hppBottomLoading: false,
      hppTopLoading: false,
      count: 0,
      nullUrl: require('@/assets/img/null.png'),
      loadingMsg: 'Loading',
      transferKey: 0,
      transferKey1: 0,
      transferKey2: 0,
      panelValue: ['1', '2'],
      filepanel: ['codeDetails1'],
      formgroup: false,
      modal: false,
      modalCode: false,
      modalDeploy: false,
      modalCompileLog: false,
      showfile: false,
      timer: null,
      cppfile: null,
      hppfile: [],
      maxsize: localStorage.getItem('MAX_FILE_SIZE') ? JSON.parse(localStorage.getItem('MAX_FILE_SIZE')) : 2048,
      loadingStatus: false,
      uploadVersion: '',
      contractId: this.$route.params.contractId ? this.$route.params.contractId : '',
      tenantId: this.$route.params.tenantId ? this.$route.params.tenantId : '',
      alertArr: { index: null, chainId: '', chainAccountName: '' },
      chainIdList: [],
      // chainTable: [],
      chainTable: {},
      arrCompileLog: {},
      arrDetails: {},
      arrDeploy: [],
      arrChainCode: { cppObj: {}, hppObj: {}, abiObj: {} },
      chainIdPageParam: { pagetotal: 0, pageSize: 60, pageIndex: 1 },
      tablePageParam: { pagetotal: 0, pageSize: 60, pageIndex: 1 },
      chainCodePageParam: {
        cpp: { pagetotal: 0, pageSize: 30, pageIndex: 1 },
        hpp: { pagetotal: 0, pageSize: 30, pageIndex: 1 }
      },
      codeTotalPages: { cpp: 0, hpp: 0 },
      chainCodePageParam1: { pagetotal: 0, pageSize: 30, pageIndex: 1 },
      tableData: [],
      columns: [
        { key: 'uploadVersion', title: '版本号', tooltip: true },
        { key: 'cppName', title: 'cpp文件名', tooltip: true },
        {
          key: 'hppNames',
          title: 'hpp文件名',
          tooltip: true,
          render: (h, params) => {
            return h('div', params.row.hppNames.join(','))
          }
        },
        // { key: 'interfacDoc', title: 'API文件', tooltip: true },
        {
          key: 'interfacDoc',
          title: 'API文件',
          tooltip: true,
          render: (h, params) => {
            return h('span', params.row.interfacDoc === null ? '/' : params.row.interfacDoc)
          }
        },
        {
          key: 'securityScanReportNames',
          // title: '安全扫描报告文件名',
          renderHeader: (h, params) => {
            return h('div', [
              h('span', '安全扫描报告文件名'),

              h('Tooltip', {
                props: {
                  placement: 'top',
                  transfer: true,
                },
              },
                [
                  h('Icon', {
                    props: {
                      type: 'ios-help-circle-outline',
                    },
                    style: {
                      marginRight: '5px'
                    }
                  }),
                  h('span', {
                    slot: 'content', //slot属性
                    style: {
                      whiteSpace: 'normal',
                      wordBreak: 'break-all'
                    }
                  }, [
                    h('p'), '1、安全扫描报告可以用磐舟或磐基的代码扫描工具扫描代码后生成',
                    h('p'), '2、安全扫描报告无格式要求',
                  ])

                ]
              ),

            ])
          },
          render: (h, params) => {
            return h('div', params.row.securityScanReportNames.join(','))
          },
          tooltip: true
        },
        { key: 'uploadBrief', title: '上传备注', tooltip: true },
        { key: 'uploadTime', title: '上传时间', tooltip: true },
        { // key: 'action',
          title: '操作',
          minWidth: 130,
          align: 'left',
          render: (h, params) => {
            return h('div', { style: { margin: '5px 0 5px 0', padding: '5px' } },
              [
                h('Button', {
                  props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                  style: this.buttonStyle,
                  on: { click: () => { this.addDeploy(params) } }
                }, '部署'),
                h('Button', {
                  props: { type: 'text', size: 'small', disabled: params.row.isEdit ? params.row.isSingleCpp == '0' ? true : false : true },
                  style: {
                    marginRight: '8px',
                    borderColor: params.row.isEdit ? params.row.isSingleCpp == '0' ? '#c5c8ce' : '#3D73EF' : '#c5c8ce',
                    color: params.row.isEdit ? params.row.isSingleCpp == '0' ? '#c5c8ce' : '#3D73EF' : '#c5c8ce',
                    display: this.ideShowZ ? this.ideShow ? this.ideOpen ? '' : 'none' : 'none' : 'none'
                  },
                  on: { click: () => { this.onlineEditContract(params, 'MODIFY') } }
                }, '在线编辑'),
                h('Button', {
                  props: { type: 'text', size: 'small' },
                  style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF', display: this.ideOpen ? 'none' : '' },
                  on: { click: () => { this.getChaincode(params.index) } }
                }, '文件源码'),
                h('Dropdown', {
                  props: { placement: 'top' }
                }, [
                  h('Button', {
                    props: { type: 'text', size: 'small' },
                    style: { 'font-weight': '600', 'font-size': '18px', margin: '5px 8px 5px 0', color: '#3D73EF', border: '1px solid #3D73EF' }
                  }, '···'),
                  // class: params.index <2 ? 'dropdownmenu-diy-top' : 'dropdownmenu-diy-btm'
                  h(
                    'DropdownMenu',
                    {
                      slot: 'list'
                      // class: 'dropdownmenu-diy-btm'
                    },
                    [
                      h('DropdownItem', [
                        h('Button', {
                          props: { type: 'text', size: 'small', disabled: !params.row.isRecall },
                          style: {
                            marginRight: '0px',
                            border: '1px solid #3D73EF',
                            borderColor: params.row.isRecall ? '#3D73EF' : '#c5c8ce',
                            color: params.row.isRecall ? '#3D73EF' : '#c5c8ce !important',
                            // background: params.row.isRecall ? '#fff' : '#f7f7f7',
                            opacity: params.row.isRecall ? 1 : 0.8
                          },
                          on: { click: () => { this.withDraw(params.index) } }
                        }, '撤回')
                      ]),

                      h('DropdownItem', { style: { display: this.ideOpen ? '' : 'none' } },
                        [
                          h('Button', {
                            props: { type: 'text', size: 'small' },
                            style: { marginRight: '0px', color: '#3D73EF', border: '1px solid #3D73EF' },
                            on: { click: () => { this.getChaincode(params.index) } }
                          }, '文件源码')
                        ]
                      ),

                      h('DropdownItem', [
                        h('Button', {
                          props: { type: 'text', size: 'small' },
                          style: { marginRight: '0px', color: '#3D73EF', border: '1px solid #3D73EF' },
                          on: { click: () => { this.getDeploy(params.index) } }
                        }, '部署记录')
                      ]),

                      h('DropdownItem', [
                        h('Button', {
                          props: { type: 'text', size: 'small' },
                          style: { marginRight: '0px', color: '#3D73EF', border: '1px solid #3D73EF' },
                          on: { click: () => { this.catCompileLog(params.index) } }
                        }, '编译日志')
                      ]),

                      h('DropdownItem', [
                        h('Button', {
                          props: { type: 'text', size: 'small' },
                          style: { marginRight: '0px', color: '#3D73EF', border: '1px solid #3D73EF' },
                          on: { click: () => { this.Downloadhistory(params.index) } }
                        }, '下载历史')
                      ])
                    ]
                  )
                ])
              ])
          }
        }
      ],
      columnsDeploy: [
        { key: 'chainName', title: '链名称', tooltip: true },
        { key: 'contractAccountName', title: '合约链账户名称', tooltip: true },
        { key: 'deployStatus', title: '部署状态', tooltip: true },
        { key: 'deployTime', title: '部署时间', tooltip: true },
        { key: 'auditorUserName', title: '审核人', tooltip: true },
        { key: 'auditRemark', title: '审核意见', tooltip: true },
        { key: 'errorMessage', title: '备注', tooltip: true },
        {
          title: '审批附件',
          minWidth: 100,
          render: (h, params) => {
            return h('div', [
              h('Span', {
                // props: {
                //   type: 'text'
                // },
                style: {
                  color: '#3D73EF',
                  cursor: 'pointer',
                  display: params.row.storePath === null ? 'none' : 'block'
                },
                on: {
                  click: () => {
                    this.downFile(params)
                  }
                }
              },
                '附件下载'
              )
            ])
          }
        }
      ],
      ops: { 'contractType': '', 'contractTypeDesc': '', 'tps': '', 'opsLinkman': { 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }, 'demandSide': { 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }, 'caller': [{ 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }] },
      modalHistory: [
        {
          title: '版本号',
          key: 'version'

        },
        {
          title: '用户名称',
          key: 'userLoginId',
          width: 180

        },
        {
          title: '下载时间',
          key: 'downloadTime'

        }
      ],
      modalHistoryData: [],
      fileSize: [],
      securityfile: [], // 安全报告
      cppname: '',
      size: 50 * 1024 * 1024,
      AllContractName: [],
      cppContent: '请选择要看的源码文件',
      cppsTitle: '',
      isSingleCpp: '',
      zipName: '',
      fileNameEnd: '',
      cppApi: '',
      infoSizeSe: '',
      ideShow: false,
      ideShowZ: false,
      userPermission: JSON.parse(localRead('userPermission')),
      // cppsTitle: [{ name: 'src', content: [{ fileName: 'fadsf1', content: '11111111' }] }, { name: 'src3', content: [{ fileName: 'fadsf2', content: '222222222222222' }] }, { name: 'src2', content: [{ fileName: 'fadsf3', content: '33333333333' }] }], //多cpp文件名
    }
  },
  methods: {

    // 多cpp
    handlemanyCpp (file) {
      if (file.name.toLowerCase().indexOf('zip') !== -1 || file.name.toLowerCase().indexOf('tar') !== -1) {
        this.manyCpp = file
        let indexdata = this.manyfileSize.findIndex(item => {
          return item.name == 'zip' || item.name == 'tar'
        })
        if (indexdata != -1) {
          this.manyfileSize.splice(indexdata, 1, { size: file.size, name: 'manyCpp' })
        } else {
          this.manyfileSize.push({ size: file.size, name: 'manyCpp' })
        }
        this.manycppStatus.cppstatus = true
      } else {
        this.msgInfo('error', '只允许上传zip/tar压缩包', true)
      }
      return false
    },
    // api
    handlemanyApi (file) {
      if (file.name.toLowerCase().indexOf('txt') !== -1) {
        this.manyApiFile = file
        let indexdata = this.manyfileSize.findIndex(item => {
          return item.name == 'txt'
        })
        if (indexdata != -1) {
          this.manyfileSize.splice(indexdata, 1, { size: file.size, name: 'api' })
        } else {
          this.manyfileSize.push({ size: file.size, name: 'api' })
        }
        this.manycppStatus.apistatus = true
      } else {
        this.msgInfo('error', '请上传txt文件', true)
      }
      return false
    },
    // 安全扫描报告
    handlemanySecurity (file) {
      this.manySecurityFile.push(file)
      this.manyfileSize.push({ size: file.size })
      this.manycppStatus.securitystatus = true
      return false
    },
    submitmanyUpload () {
      this.cppfile = null
      this.hppfile = []
      this.securityfile = []
      this.showfile = false
      let isSingleCpp = 0
      this.infoSize = this.manyfileSize.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
      this.loadingjsStatus = true
      uploadmanyFile(this.contractId, isSingleCpp, this.manyCpp, this.manyApiFile, this.infoSize, this.manySecurityFile, this.arrDetails.languageType).then(res => {
        if (res.code === '00000') {
          this.manyCpp = null;
          this.manyApiFile = null;
          this.manySecurityFile = [];
          this.manycppStatus.cppstatus = false;
          this.manycppStatus.apistatus = false;
          this.manycppStatus.securitystatus = false;
          this.manyfileSize = []
          this.msgInfo('success', res.message, true)
          this.loadingjsStatus = false
          this.getDetails()
        } else {
          this.msgInfo('error', res.message, true)
          this.loadingjsStatus = false
        }
      }).catch(err => {
        this.msgInfo('error', err.message, true)
        this.loadingjsStatus = false
      })
    },
    downFile (params) {
      getauditdownloadFile(params.row.deployId).then(res => {
        let blob = new Blob([res], { type: 'application/zip' })
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = params.row.contractAccountName
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    /** 以上是S14新增 */
    abiDown () {
      let name = this.jsFile.name.split('.')
      abidownFile(this.jsFile, '').then(res => {
        let blob = new Blob([res])
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = name[0] + '.abi'
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      })
    },
    // 上传js文件
    handleJs (file) {

      // console.log(file);
      if (file.name.indexOf('js') !== -1) {
        this.jsFile = file
        let indexdata = this.fileSize.findIndex(item => {
          return item.name == 'js'
        })
        if (indexdata != -1) {
          this.fileSize.splice(indexdata, 1, { size: file.size, name: 'js' })
        } else {
          this.fileSize.push({ size: file.size, name: 'js' })
        }
        this.fileStatus.jsstatus = true
        this.abidisa = false
      } else {
        this.msgInfo('error', '请上传JavaScript文件', true)
      }
      return false
    },

    handleAbi (file) {
      if (file.name.indexOf('abi') !== -1) {
        this.abiFile = file
        let indexdata = this.fileSize.findIndex(item => {
          return item.name == 'abi'
        })
        if (indexdata != -1) {
          this.fileSize.splice(indexdata, 1, { size: file.size, name: 'abi' })
        } else {
          this.fileSize.push({ size: file.size, name: 'abi' })
        }
        this.fileStatus.abistatus = true
      } else {
        this.msgInfo('error', '请上传abi文件', true)
      }
      return false
    },
    // 上传api文件
    handleApi (file) {
      if (file.name.toLowerCase().indexOf('doc') !== -1 || file.name.toLowerCase().indexOf('docx') !== -1 || file.name.toLowerCase().indexOf('txt') !== -1 || file.name.toLowerCase().indexOf('pdf') !== -1) {
        this.apiFile = file
        let indexdata = this.fileSize.findIndex(item => {
          return item.name == 'api'
        })
        if (indexdata != -1) {
          this.fileSize.splice(indexdata, 1, { size: file.size, name: 'api' })
        } else {
          this.fileSize.push({ size: file.size, name: 'api' })
        }
        this.fileStatus.apistatus = true
        // this.abidisa = false
      } else {
        this.msgInfo('error', '请上传格式为.doc,.docx,.txt,.pdf的API文件', true)
      }
      return false
    },
    // 上传安全扫描报告文件
    handleSecurity (file) {

      if (file.name.indexOf('doc') !== -1 || file.name.indexOf('docx') !== -1 || file.name.indexOf('txt') !== -1 || file.name.indexOf('pdf') !== -1 || file.name.indexOf('xls') !== -1 || file.name.indexOf('xlsx') !== -1 || file.name.indexOf('txt') !== -1 || file.name.indexOf('zip') !== -1 || file.name.indexOf('rar') !== -1 || file.name.indexOf('png') !== -1 || file.name.indexOf('jpg') !== -1 || file.name.indexOf('jpeg') !== -1) {

        let indexdata = this.securityFile.findIndex(item => {
          return item.name == file.name
        })
        if (indexdata != -1) {
          this.securityFile.splice(indexdata, 1, file)
        } else {
          this.securityFile.push(file)
        }
        this.fileStatus.securitystatus = true
      } else {
        this.msgInfo('error', '该文件类型不能上传！', true)
      }
      return false
      // this.securityFile.push(file)
      // this.fileSize.push({ size: file.size })
      // this.fileStatus.securitystatus = true
    },
    // js语言提交上传
    submitUpload () {
      this.infoSize = this.fileSize.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
      if (this.infoSize > this.size) {
        alert('上传文件大于50M,请重新上传！')
      } else if (this.jsFile && this.abiFile && this.securityFile.length > 0) {
        if (this.apiFile) {
          this.loadingjsStatus = true
          uploadjsFile(this.contractId, this.jsFile, this.abiFile, this.infoSize, this.securityFile, this.arrDetails.languageType, this.apiFile).then(res => {
            if (res.code === '00000') {
              this.fileSize = []
              this.getDetails()
              this.infoSize = ''
              this.jsFile = null
              this.abiFile = null
              this.securityFile = []
              this.fileStatus.jsstatus = false;
              this.fileStatus.abistatus = false;
              this.fileStatus.securitystatus = false;
              this.fileStatus.apistatus = false;
              this.abidisa = true
              this.loadingjsStatus = false;
              this.msgInfo('info', res.message)
            } else {
              this.msgInfo('error', res.message, true)
              this.loadingjsStatus = false;
            }
          }).catch(error => {
            console.log('uploadFile.error===>', error)
            this.msgInfo('error', error.message, true)
            this.loadingjsStatus = false;
          })
        } else {
          this.msgInfo('warning', '请上传API文件', true)

        }

      } else this.msgInfo('warning', 'js文件、abi文件、安全报告为必传项都不能为空', true)
    },
    jsaddDeploy (params) {
      this.$refs.childMethod.initOps()
      this.uploadVersion = this.jstableData[params.index].uploadVersion
      this.modal = true
      this.jsalertArr.chainAccountName = ''
      this.alertArr.chainId = ''
      if (this.chainIdList.length < 0) this.getChainList()

    },
    // 查询目标链
    getjsChainTable (data) {
      if (data) {
        getChainid(data).then(res => {
          if (res.code === '00000') {
            this.jsalertArr.chainAccountName = ''
            this.jschainTable = res.data

          } else {
            this.msgInfo('error', res.message, true)
          }
        }).catch(error => {
          this.msgInfo('error', error.message, true)
        })
      }

    },
    jsgetPastOps (e, name) {
      this.jsalertArr.name = name
      getContractOps(this.alertArr.chainId, e).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          if (res.data.ops) {
            this.formgroup = true
            this.ops = res.data.ops
            ++this.transferKey2
          } else {
            this.formgroup = false
            this.initOps()
          }
        }
      }).catch(error => {
        console.log('getContractOps.error===>', error)
        this.msgInfo('error', error.message, true)
      })

    },
    //单文件删除
    handleClose (file) {
      this.securityfile.splice(this.securityfile.findIndex(item => item.name === file.name), 1)
    },
    handleCloseHpp (file) {
      this.hppfile.splice(this.hppfile.findIndex(item => item.name === file.name), 1)
    },
    // colldata (key) {
    //   // console.log(this.codeData)
    //   if (key[0]) {
    //     this.codeData.fileName = key[0]
    //     // console.log(this.codeData)
    //     getChaincode(this.codeData).then(res => {
    //       if (res.code === '00000') {
    //         this.CollContent = res.data
    //       } else {
    //         this.msgInfo('error', res.message, true)
    //       }
    //     }).catch((error) => {
    //       this.msgInfo('error', error.message, true)
    //     })
    //   }
    // },
    // 点击文件源码
    fileModal (params) {
      // console.log(params.row)
      this.jschaincode = true
      this.titlejs = params.row.uploadVersion
      this.abiFilename = params.row.abiName
      this.jsFilename = params.row.jsName

      this.getJscode(this.codeData = {
        contractId: this.contractId,
        uploadVersion: params.row.uploadVersion,
        fileName: params.row.jsName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      })
      this.getAbicode(this.codeData = {
        contractId: this.contractId,
        uploadVersion: params.row.uploadVersion,
        fileName: params.row.abiName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      })
    },
    handScrolljs (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    getJscode (data) {
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          this.CollContentfile.jscentent = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    getAbicode (data) {
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          this.CollContentfile.abicentent = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 以上是js新增方法

    cancelModal () {
      this.modal3 = false
      this.chainTable.chainAccountName = ''
      this.formgroup = false
      this.initOps()
    },
    jumpNew () {
      // console.log(this.cppname)
      if (!isPowerAccount(this.cppname)) {
        this.msgInfo('error', '当前cpp文件名称不符合合约链账户名称创建要求，请重新调整cpp文件名称', true)
      } else {
        this.$router.push({
          name: 'new_user',
          query: {
            chainId: this.alertArr.chainId,
            cppName: this.cppname
          }
        })
      }
    },
    // 在线编辑
    onlineEditContract (params, flag) {
      let data = {
        type: flag,
        contractId: this.contractId,
        uploadVersion: params === '' ? null : params.row.uploadVersion
      }
      contractIdeNew(data).then(res => {
        if (res.code === '00000') {
          // var aElement = document.createElement('a')
          // aElement.setAttribute('href', res.data.url)
          // aElement.setAttribute('target', 'ide')
          // // aElement.href = res.data.url
          // // aElement.target = 'ide'
          // document.body.appendChild(aElement)
          // aElement.click()
          // document.body.removeChild(aElement)
          window.open(res.data.url, 'ide')
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 下载历史
    Downloadhistory (index) {
      this.historymodal = true
      if (this.arrDetails.languageType === 'JS') {
        this.uploadVersion = `${this.jstableData[index].uploadVersion}`
      } else {
        this.uploadVersion = `${this.tableData[index].uploadVersion}`
      }
      download(this.uploadVersion).then(res => {
        // console.log(res.data)
        this.modalHistoryData = res.data
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    reachBottom () {
      if (this.chainIdPageParam.pageIndex < this.pages) {
        this.chainIdPageParam.pageIndex += 1
        this.getChainList(true)
      }
    },
    handScroll (e, fileType) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        // console.log('到底了', fileType, this.codeTotalPages[fileType])
        if (parseInt(this.chainCodePageParam[fileType].pageIndex) < parseInt(this.codeTotalPages[fileType])) {
          if (fileType === 'cpp') {
            this.cppBottomLoading = true
          } else {
            this.hppBottomLoading = true
          }
          return new Promise(resolve => {
            this.timer = setTimeout(() => {
              ++this.chainCodePageParam[fileType].pageIndex
              this.getCode(this.alertArr.index, fileType, 'bottom')
              resolve()
            }, 50)
          })
        } else {
          clearTimeout(this.timerStamp)
          let that = this
          this.timerStamp = setTimeout(() => {
            let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
            if (height < 3 && height > 0) {
              that.msgInfo('info', '到底了！', true)
            }
          }, 500)
        }
      } else if (e.srcElement.scrollTop === 0) {
        if (parseInt(this.chainCodePageParam[fileType].pageIndex) !== 1) {
          if (fileType === 'cpp') {
            this.cppTopLoading = true
          } else {
            this.hppTopLoading = true
          }
          return new Promise(resolve => {
            this.timer = setTimeout(() => {
              --this.chainCodePageParam[fileType].pageIndex
              this.getCode(this.alertArr.index, fileType, 'top')
              resolve()
            }, 50)
          })
        } else {
          this.msgInfo('info', '已到首页！', true)
        }
      }
    },
    initModal () {
      this.$refs.childMethod.getOps()
      this.$refs.childMethod.initOps()
      // console.log(this.modal,'this.modal')
      this.modal = false
      this.uploadVersion = ''
      this.jsalertArr.chainAccountName = ''
      this.alertArr.chainId = ''
      this.formgroup = false
      this.initOps()

      // this.chainIdList = []
      // this.alertArr.chainAccountName = ''
      // this.init()
      // this.initOps()
    },
    init () {
      // this.chainTable = []
      this.chainTable = {}
      this.uploadVersion = ''
      this.alertArr.chainAccountName = ''
      if (this.chainIdList[0].chainId) this.alertArr.chainId = this.chainIdList[0].chainId
      else this.msgInfo('warning', '数据异常：目标链内容不能为空！', true)
      this.getChainTable()
    },
    initOps () {
      this.ops = { 'contractType': '', 'contractTypeDesc': '', 'tps': '', 'opsLinkman': { 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }, 'demandSide': { 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }, 'caller': [{ 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }] }
      ++this.transferKey2
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, top: 300 }) },
    getChainTable () {
      getChainTableDataLian(this.alertArr.chainId, this.cppname, this.alertArr.chainAccountName, 'CONTRACT', '', 'CHAIN_SUCCESS', 'DEPLOY', this.contractId, this.isSingleCpp, this.zipName).then(res => {
        // console.log('getChainTableData===>', res)
        // if (res.code !== '00000') this.msgInfo('warning', res.message, true)
        if (res.code === 'A0720') {
          this.modal3 = true
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else {
          // this.modal3 =
          this.formgroup = false
          this.alertArr.chainAccountName = ''
          this.chainTable = res.data
          this.getPastOps()

          // this.tablePageParam = {
          //   pagetotal: res.data.total,
          //   pageSize: res.data.size,
          //   pageIndex: res.data.current
          // }
          this.initOps()
        }
      }).catch(error => {
        // console.log('getChainTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    editOps () {
      setTimeout(() => {
        this.$refs.childMethod.getOps()
      }, 0)
      this.isFlag = true
      this.formgroup = false
    },
    getPastOps () {
      getContractOps(this.alertArr.chainId, this.chainTable.chainAccountId).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          if (res.data.ops) {
            this.formgroup = true
            this.ops = res.data.ops
            ++this.transferKey2
          } else {
            this.formgroup = false
            this.initOps()
          }
        }
      }).catch(error => {
        console.log('getContractOps.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    // 部署确定按钮
    ok () {
      if (this.contractId) {
        // console.log(this.$refs.childMethod.getOps())
        // console.log(this.contractId, this.chainId, this.uploadVersion, this.ops, this.formgroup, this.isFlag)
        if (!this.formgroup) {
          if (this.$refs.childMethod.getOps()) {
            this.ops = this.$refs.childMethod.getOps()
          } else {
            return false
          }
        }
        let deploychaintName = this.arrDetails.languageType === 'JS' ? this.jsalertArr.name : this.chainTable.chainAccountName
        // let deploychainId = this.languageType === 'JS' ? this.jsalertArr.chainId : this.alertArr.chainId
        addContractDeploy(this.contractId, this.alertArr.chainId, this.uploadVersion, deploychaintName, this.ops, this.cppname, this.arrDetails.languageType).then(res => {
          // console.log('addContractDeploy===>', res)
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.isFlag = true
              this.msgInfo('warning', res.message, true)
            }
          } else {
            this.isFlag = false
            this.msgInfo('info', res.message)
            this.modal = false
            this.getDetails()
            // this.init()
            this.chainTable = {}
            this.uploadVersion = ''
            this.alertArr.chainAccountName = ''
            if (this.chainIdList[0].chainId) this.alertArr.chainId = this.chainIdList[0].chainId
            else this.msgInfo('warning', '数据异常：目标链内容不能为空！', true)
          }
        }).catch(error => {
          console.log('addContractDeploy.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      } else this.msgInfo('warning', '内容不能为空！', true)
    },
    cancel () {
      this.codeTotalPages.cpp = 0
      this.codeTotalPages.hpp = 0
      this.chainCodePageParam.hpp = { pagetotal: 0, pageSize: 60, pageIndex: 1 }
      this.chainCodePageParam.cpp = { pagetotal: 0, pageSize: 60, pageIndex: 1 }
      this.alertArr.index = null
      this.arrDeploy = []
      this.getCompileLog = {}
      this.uploadVersion = ''
      this.beforeDestroy()
    },
    handleUpload (file) {
      let filename = file.name.replace(/.+\./, '').toLowerCase()

      if (file.name.indexOf('cpp') !== -1) {
        this.cppfile = file
        // this.fileSize.push({ size: file.size })
      } else if (file.name.indexOf('hpp') !== -1) {
        if (this.hppfile.findIndex(item => item.name === file.name) === -1) {
          this.hppfile.push(file)
        } else {
          this.hppfile.splice(this.hppfile.findIndex(item => item.name === file.name), 1, file)
        }

        this.fileSize.push({ size: file.size })
      } else if (filename !== 'cpp' && filename !== 'hpp') {
        if (filename === 'txt' || filename === 'doc' || filename === 'docx' || filename === 'xlsx' || filename === 'xls' || filename === 'pdf' || filename === 'zip' || filename === 'rar' || filename === 'jpg' || filename === 'png' || filename === 'jpeg') {
          // this.securityfile.push(file)


          if (this.securityfile.findIndex(item => item.name === file.name.toLowerCase()) === -1) {
            this.securityfile.push(file)
          } else {
            this.securityfile.splice(this.securityfile.findIndex(item => item.name === file.name.toLowerCase()), 1, file)
          }
        } else {
          this.msgInfo('error', '该文件类型不能上传！', true)
        }
      }
      this.showfile = true
      return false
    },
    // 单cppapi文档
    handleUploadApi (file) {
      let filename = file.name.replace(/.+\./, '').toLowerCase()
      if (filename === 'txt' || filename === 'doc' || filename === 'docx' || filename === 'pdf') {
        this.cppApi = file
        // console.log(this.securityfile)
        // this.fileSize.push({ size: file.size })
      } else {
        this.msgInfo('error', '请上传格式为.doc,.docx,.txt,.pdf的API文件', true)
      }


      this.showfile = true
      return false
    },
    getDetails () {
      getContractDetails(this.contractId).then(res => {
        // console.log('getDetails===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.arrDetails = res.data
          localStorage.setItem('languageType', res.data.languageType)
          this.AllContractName = res.data.eosContractBaseInfoDOS ? res.data.eosContractBaseInfoDOS : []
          // console.log(res.data.uploadRecords)
          // res.data.uploadRecords.map(item => {
          //   return item.securityScanReportNames.join(',')
          // })
          if (res.data.languageType === 'JS') {
            this.jstableData = res.data.uploadRecords ? res.data.uploadRecords : []
          } else {
            this.tableData = res.data.uploadRecords ? res.data.uploadRecords : []
            // listdata.map(item => {
            //   return {
            //     ...item,
            //     interfacDoc: item.interfacDoc !== null ? item.interfacDoc : '/'
            //   }
            // })
            // console.log(listdata)
            // this.tableData = listdata
          }
          this.ideNew = res.data.isNew
        }
        ++this.transferKey
      }).catch(error => {
        console.log('getContractDetails.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    upload () {

      this.manyCpp = null;
      this.manyApiFile = null;
      this.manySecurityFile = [];
      this.manycppStatus.cppstatus = false;
      this.manycppStatus.apistatus = false;
      this.manycppStatus.securitystatus = false;
      this.infoSizeHpp = this.hppfile.length > 0 ? this.hppfile.reduce((prev, next) => { return prev + next.size }, 0) : 0// 计算数组总和
      this.infoSizeSe = this.securityfile.length > 0 ? this.securityfile.reduce((prev, next) => { return prev + next.size }, 0) : 0// 计算数组总和
      this.infoSize = Number(this.infoSizeHpp) + Number(this.infoSizeSe) + Number(this.cppfile ? this.cppfile.size : 0) + Number(this.cppApi ? this.cppApi.size : 0)

      if (this.infoSize > this.size) {
        alert('上传文件大于50M,请重新上传！')
      } else if (this.cppfile && this.securityfile.length > 0) {
        // console.log('this.hppfile', this.hppfile)
        if (this.cppApi) {
          this.loadingStatus = true
          uploadFile(this.contractId, this.cppfile, this.hppfile, this.infoSize, this.securityfile, this.arrDetails.languageType, '1', this.cppApi).then(res => {
            if (res.code === '00000') {
              this.infoSize = ''
              this.cppfile = null
              this.cppApi = ''
              this.hppfile = []
              this.securityfile = []
              this.showfile = false
              this.manyfileSize = []
              this.msgInfo('info', res.message)
              this.getDetails()
              this.loadingStatus = false
            } else {
              this.msgInfo('error', res.message, true)
              this.loadingStatus = false
            }
          }).catch(error => {
            console.log('uploadFile.error===>', error)
            this.msgInfo('error', error.message, true)
            this.loadingStatus = false
          })
        } else this.msgInfo('warning', '请上传API文件', true)

      } else this.msgInfo('warning', 'cpp和安全报告为必传项不能为空', true)
    },
    // 部署
    addDeploy (params) {
      this.$refs.childMethod.initOps()
      this.uploadVersion = this.tableData[params.index].uploadVersion
      //       var a='sjhjsdcdjcn.json'
      // var b=a.split('.')[1]

      // var path='a.b.c.cpp
      // var index=path.lastIndexOf(".");
      // var name=path.substring(0,index);
      let index = params.row.cppName.lastIndexOf('.')
      this.cppname = params.row.cppName.substring(0, index)
      this.isSingleCpp = this.tableData[params.index].isSingleCpp
      this.zipName = this.tableData[params.index].zipName
      // this.cppname = params.row.cppName.split('.')[0]
      // console.log(this.cppname)
      // console.log(this.cppname)
      // console.log(this.AllContractName)

      if (this.tableData[params.index].isSingleCpp == '0') {
        if (this.arrDetails.contractName === this.zipName) {
          this.isFlag = false
          if (this.contractId) {
            this.init()
            this.modal = true
            this.uploadVersion = `${this.tableData[params.index].uploadVersion}`
          } else this.msgInfo('warning', '当前无合约账户，请从列表页重新跳转到详情页！', true)
        } else this.msgInfo('warning', '压缩包名称与合约名称不一致，请重新上传！', true)
      } else {
        if (this.arrDetails.contractName === null) {
          if (this.AllContractName.some(item => item.contractName === this.cppname)) {
            this.msgInfo('warning', 'CPP文件名称与已存在的合约名称一致，请重新上传！', true)
          } else {
            this.modal = true
            this.init()
            this.uploadVersion = `${this.tableData[params.index].uploadVersion}`
          }
        } else if (this.arrDetails.contractName === this.cppname) {
          this.isFlag = false
          if (this.contractId) {
            this.init()
            this.modal = true
            this.uploadVersion = `${this.tableData[params.index].uploadVersion}`
          } else this.msgInfo('warning', '当前无合约账户，请从列表页重新跳转到详情页！', true)
        } else this.msgInfo('warning', 'CPP文件名称与合约名称不一致，请重新上传！', true)
      }

    },
    withDraw (index) {
      if (this.arrDetails.languageType === 'JS') {
        this.uploadIdw = this.jstableData[index].uploadId
      } else {
        this.uploadIdw = this.tableData[index].uploadId
      }

      this.$Modal.confirm({
        content: '<p>确定要撤回当前智能合约吗？</p>',
        onOk: () => {
          this.withDrawRequest(this.uploadIdw)
        },
        onCancel: () => {
          // this.$Message.info('Clicked cancel')
        }
      })
    },
    withDrawRequest (uploadId) {
      recall({ uploadId }).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.msgInfo('success', res.message, true)
          this.getDetails()
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    catCompileLog (index) {
      if (this.contractId) {
        getContractCompileLog(this.contractId, `${this.tableData[index].uploadVersion}`).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {
            this.modalCompileLog = true
            this.arrCompileLog = res.data
          }
        }).catch(error => {
          console.log('getContractCompileLog.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      } else this.msgInfo('warning', '当前无合约账户，请从列表页重新跳转到详情页！', true)
    },
    getDeploy (index) {
      if (this.arrDetails.languageType === 'JS') {
        this.uploadVersion = `${this.jstableData[index].uploadVersion}`
      } else {
        this.uploadVersion = `${this.tableData[index].uploadVersion}`
      }
      if (this.contractId) {
        getContractDeploy(this.contractId, this.uploadVersion).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {
            this.modalDeploy = true
            this.arrDeploy = res.data.deployRecords
          }
        }).catch(error => {
          console.log('getContractDeploy.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      } else this.msgInfo('warning', '当前无合约账户，请从列表页重新跳转到详情页！', true)
    },
    getCode (index, filetype, val) {
      this.arrChainCode.cppObj.uploadVersion = `${this.tableData[index].uploadVersion}`
      if (`${this.tableData[index][filetype + 'Name']}` !== '/') {
        getContractChaincode(this.contractId, `${this.tableData[index].uploadVersion}`, `${this.tableData[index][filetype + 'Name']}`, this.chainCodePageParam[filetype]).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.loadingMsg = res.message
              this.msgInfo('warning', res.message, true)
            }
          } else {
            // this.modalCode = true
            // console.log('getCode--res.data:', res.data)
            this.arrChainCode[filetype + 'Obj'] = res.data
            ++this.transferKey1
            this.codeTotalPages[filetype] = res.data.pages
          }
        }).catch(error => {
          // console.log('getContractChaincode.error===>', error)
          this.loadingMsg = error.message
          this.msgInfo('error', error.message, true)
        })
      }
      if (val === 'bottom') {
        if (filetype === 'cpp') {
          this.cppBottomLoading = false
        } else {
          this.hppBottomLoading = false
        }
      } else if (val === 'top') {
        if (filetype === 'cpp') {
          this.cppTopLoading = false
        } else {
          this.hppTopLoading = false
        }
      }
    },
    getNewCode (fileName, filetype, val) {
      // const key = fileName.replace('.', '') // 之前为了去掉.后来发现没必要
      getContractChaincode(this.contractId, `${this.tableData[this.alertArr.index].uploadVersion}`, fileName, this.chainCodePageParam[filetype]).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.loadingMsg = res.message
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.modalCode = true
          if (filetype === 'cpp') {
            this.arrChainCode[filetype + 'Obj'] = res.data
            // console.log(this.arrChainCode.cppObj, 'cpp')
          } else {
            this.arrChainCode[filetype + 'Obj'][fileName] = res.data
            // console.log(this.arrChainCode.hppObj, 'hpp')
          }

          ++this.transferKey1
          // 发现这块没有用到
          // let pages = {}
          // pages[key] = res.data.pages
          // this.codeTotalPages[filetype] = pages
        }
      }).catch(error => {
        this.loadingMsg = error.message
        // console.log('getContractChaincode.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    getChaincode (index) {

      // console.log(this.tableData[index].fileContent);
      if (this.tableData[index].isSingleCpp == 0) {
        console.log(Object.keys(this.tableData[index].fileContent));
        this.cppsTitle = this.tableData[index].fileContent
        this.jschaincodes = true
      } else {
        this.loadingMsg = 'Loading'
        this.modalCode = true
        if (this.contractId) {
          this.codeTotalPages.cpp = 0
          this.codeTotalPages.hpp = 0
          this.arrChainCode.cppObj = {}
          this.arrChainCode.hppObj = {}
          this.alertArr.index = index
          this.alertArr['hppNames'] = this.tableData[index].hppNames
          // console.log(this.tableData[index].hppNames)
          if (`${this.tableData[index].cppName}` && `${this.tableData[index].cppName}` !== 'null') this.getCode(index, 'cpp')
          // if (`${this.tableData[index].hppName}` && `${this.tableData[index].hppName}` !== 'null') this.getCode(index, 'hpp')
          // console.log(this.tableData[index].hppNames)
          // console.log(this.tableData[index].hppFileName.length)
          if (this.tableData[index].hppNames && this.tableData[index].hppNames.length > 0) {
            this.tableData[index].hppNames.forEach(val => this.getNewCode(val, 'hpp'))
          }
        } else this.msgInfo('warning', '当前无合约账户，请从列表页重新跳转到详情页！', true)
      }

    },
    beforeDestroy () {
      clearInterval(this.timer)
      this.timer = null
      clearTimeout(this.timerStamp)
      this.timerStamp = null
    },
    getChainList (flag) {
      let searchItem = {
        languageType: this.arrDetails.languageType,
        chainName: '',
        engineTypeList: this.languageType === 'JS' ? ['CMEOS'] : [],
        status: undefined,
        chooseTenantId: this.tenantId ? this.tenantId : ''
      }
      getChainIdList(this.chainIdPageParam, searchItem).then(res => {
        // console.log(res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          if (flag) {
            this.chainIdList.push.apply(this.chainIdList, res.data.records)
            // if (this.languageType === 'JS') {
            //   this.jschainIdList.push.apply(this.chainIdList, res.data.records)
            // } else {
            //   this.chainIdList.push.apply(this.chainIdList, res.data.records)
            // }
          } else {
            this.chainIdList = res.data.records
            // if (this.languageType === 'JS') {
            //   this.jschainIdList = res.data.records
            // } else {
            //   this.chainIdList = res.data.records
            // }
          }
          this.chainIdPageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          this.pages = res.data.pages
        }
      }).catch(error => {
        console.log('getChainIdList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    onDetail () {
      this.$refs.modalCommonRef.openDetail(this.arrDetails.contractName, this.arrDetails.contractId)
    },
    onShare () {
      this.$refs.modalCommonRef.openShare(this.arrDetails.contractName, this.arrDetails.contractId)
    },
    getSystem () {
      loginSysConfig().then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.ideOpen = res.data.ideOpen === '1'
        }
      }).catch(error => {
        console.log('loginSysConfig.error===>', error)
      })
    },
    clickCpps (value) {
      this.cppContent = value

    },
    getFileDown () {


      getFile('FILE_SPECIFICATION').then(res => {
        let blob = new Blob([res])
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = '多CPP文件上传规范说明.docx'
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)

      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })

    },
    getFileDowns () {
      getFile('C_MORE_MODEL').then(res => {
        let blob = new Blob([res])
        let downloadElement = document.createElement('a')
        let fileName = this.fileName.substring(this.fileName.lastIndexOf('.'))
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = '多CPP文件模板.' + fileName
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)

      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  created () {
    // this.getSystem()
  },
  watch: {
    'fileStatus.jsstatus': {
      handler () {
        if (this.fileStatus.jsstatus == true) this.btndelete = false
        else this.btndelete = true
      }
    },
    'fileStatus.abistatus': {
      handler () {
        if (this.fileStatus.abistatus == true) this.btndelete = false
        else this.btndelete = true
      }
    },
    'fileStatus.securitystatus': {
      handler () {
        if (this.fileStatus.securitystatus == true) this.btndelete = false
        else this.btndelete = true
      }
    },
    'manycppStatus.cppstatus': {
      handler () {
        if (this.manycppStatus.cppstatus == true) this.manybtndelete = false
        else this.manybtndelete = true
      }
    },
    'manycppStatus.apistatus': {
      handler () {
        if (this.manycppStatus.apistatus == true) this.manybtndelete = false
        else this.manybtndelete = true
      }
    },
    'manycppStatus.securitystatus': {
      handler () {
        if (this.manycppStatus.securitystatus == true) this.manybtndelete = false
        else this.manybtndelete = true
      }
    }
  },
  computed: {
    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        margin: '5px 8px 5px 0',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
  mounted () {
    let name = 'LINE_IDE'
    getconfig(name).then((res) => {
      if (res.data) {
        this.ideShow = res.data.value == 1

      } else {
        this.ideShow = false
      }
    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })
    let nameIde = 'Z_LINE_IDE'
    getconfig(nameIde).then((res) => {
      if (res.data) {
        this.ideShowZ = res.data.value == 1

      } else {
        this.ideShowZ = false
      }
    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })
    if (!this.$route.params.contractId) {
      this.$router.push({
        name: 'contract_table'
      })
    } else {
      if (this.languageType == '' && localStorage.getItem('languageType') == 'JS') {
        this.languageType = localStorage.getItem('languageType')
      }
      this.getChainList()
    }
    // console.log(this.$route.params.languageType)
    this.getDetails()
    this.getSystem()
  }

}
</script>

<style lang="less" scoped>
/deep/.ivu-menu-submenu-title {
  background: #fff !important;
}
/deep/.ivu-menu,
.ivu-menu-dark,
.ivu-menu-vertical,
.ivu-menu-opened,
.ivu-menu-submenu-title {
  background: #fff !important;
}
.ivu-menu-vertical.ivu-menu-light:after {
  background: #fff;
}
.layout {
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}
.layout-logo {
  width: 100px;
  height: 30px;
  background: #5b6270;
  border-radius: 3px;
  float: left;
  position: relative;
  top: 15px;
  left: 20px;
}
.layout-nav {
  width: 420px;
  margin: 0 auto;
  margin-right: 20px;
}
.layout-footer-center {
  text-align: center;
}
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
}
.eg-class {
  p {
    height: 31px;
    line-height: 31px;
  }
}
.contract {
  margin: -16px;
  button.btn {
    position: absolute;
    right: 10px;
  }
  .basetext {
    padding-top: 20px;

    span {
      text-align: left;
      margin: 0 26px;
      line-height: 20px;
      word-break: break-all;
    }
  }
}

.create-new {
  padding: 26px 0 0 26px;
  height: 30px;
  line-height: 30px;
  a {
    color: #fff;
  }
}

/deep/.ivu-modal > .ivu-modal-content > .ivu-modal-body {
  max-height: 60vh;
  overflow: auto;
}
/deep/.ivu-upload-drag {
  height: 170px;
  background-color: #f8f8f9;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.ivu-card {
  background: #f2f6fd;
}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
/deep/.ivu-collapse[name="mainpanel"] {
  border: none;
}
/deep/.ivu-collapse[name="mainpanel"] > .ivu-collapse-item {
  border: none;
  border-radius: 5px 5px;
  margin-bottom: 10px;
}
/deep/.ivu-scroll-container {
  height: auto;
  overflow-y: auto;
}
</style>

<style lang="less" scope>
.textarea-style {
  width: 100%;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
.dropdownmenu-diy-top {
  position: relative;
  &::before {
    content: "";
    display: block;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px;
    border-color: transparent transparent #fff transparent;
    position: absolute;
    top: -20px;
    left: calc(50% - 8px);
  }
}
.dropdownmenu-diy-btm {
  position: relative;
  &::after {
    content: "";
    display: block;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px;
    border-color: #fff transparent transparent transparent;
    position: absolute;
    bottom: -20px;
    left: calc(50% - 8px);
  }
}
/deep/.ivu-table-wrapper,
.ivu-table {
  overflow: visible !important;
}
.diaoyong {
  /deep/.ivu-btn-icon-only {
    border-style: none;
  }
}
.fileDown {
  display: inline-block;
  margin-top: 2%;
  margin-left: 10px;
  color: #3d73ef;
  cursor: pointer;
}
.securityfileClass {
  margin: 0 !important;
  cursor: pointer;
}
</style>
