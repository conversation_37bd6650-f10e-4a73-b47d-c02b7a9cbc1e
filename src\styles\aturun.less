//提示弹框公共样式
.el-message-box{
  width:330px!important;
  .el-message-box__header{
    height: 52px;
    border-bottom: 1px solid #E8EAEC;
    padding: 0;
    .el-message-box__title{
      padding: 18px 0 0 17px;
      width: 330px;
      //height: 52px;
      background: #FFFFFF;

      border-radius: 4px 4px 0px 0px;
    }
  }
  .el-message-box__content{
    height: 137px;
    padding: 38px 0 30px 53px;
    overflow-y: scroll;
    //width: 224px;
    border-bottom: 1px solid #E8EAEC;
    .el-message-box__message{
      font-size: 14px;
      font-weight: 400;
      line-height: 24px;
      color: #666666;
      width:185px;
      word-wrap:break-word;
    }
    .el-icon-warning{
      color: red;
    }
  }
}

// 公共表格样式
.content-body{
  .el-table{
    .el-table__header{
      .el-table__cell{
        height: 39px!important;
        background-color: #f8f8f9!important;
        color: #3b3b3b;
        padding: 0;
      }
    }
    .el-table__body-wrapper{
      .el-table__cell{
        padding: 0;
        height: 48px;
        .el-button{
          min-width: 60px!important;
          margin-right: 2px!important;
          width: 50px;
          font-size: 12px;
          height: 24px!important;
          border: 1px solid #3D73EF;
          color: #3D73EF;
        }

      }
    }
  }

}


//输入弹框公共样式
.dialog_sty{
  .el-dialog__header{
    height: 52px;
    padding: 0;
    .el-dialog__title{
      padding: 0 0 0 17px;
      width: 150px;
      font-size: 16px;
      font-weight: 400;
      color: #444444;
      position: absolute;
      top: 20px;

    }
  }
  .el-dialog__body{
    border-top: 1px solid #E8EAEC;
    border-bottom: 1px solid #E8EAEC;
    padding:18px 0;
  }
  .el-form{
    .el-form-item{
      margin-bottom: 24px;
    }
    .el-form-item__label{
      height: 14px;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }
    .el-input{
      width:374px;

    }
    .el-textarea{
      width:374px;
    }
    .el-input__inner{
      height: 32px;
      line-height: 32px;
    }
  }
}


.cflex{
  .el-menu-item{
    background: #111945!important;
    &.is-active{
      background: #2D8CF0!important;
      font-weight: 800;
      //color: ;
    }
    span{
      color: #ffffff;

    }
    img{
      height: 16px;
      width: 16px;
    }
  }
  .el-submenu__title{
    background: #111945!important;
    span{
      color: #ffffff;

    }
    img{
      height: 16px;
      width: 16px;
      margin-right: 10px;
    }
  }
}

.el-table{
  .cell{
    .el-button{
      &:hover{
        background:#3D73EF;
        color: #ffffff!important;
      }
    }
  }
}

.head-nav{
  .left-nav{
    .el-breadcrumb{
      display: flex;
    }
  }
}


.dsfdg{
  word-wrap: break-word;
  width: 400px;
}




//消费者用户管理
.consumer_user{
  .content-body{
    .name-wrapper{
      overflow: hidden;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;

    }
  }
  .top-right-button{
    .el-button{
      height: 32px;
      border: 1px solid #19BE6B;
      color: #19BE6B;
      font-size: 14px;
      font-weight: 400;
      //line-height: 24px;
    }
  }
  .top-right-input{
    .el-input{
      height: 32px;
      width: 300px;
    }
    .el-input__inner{
      height: 32px;
    }
    .el-input--mini .el-input__inner{
      height:32px;
      line-height:32px
    }
  }
  .content-body{

  }

}

// 新建消费者用户


.new_consumer-dialog{
  .dialog_content{
    .top_tabs{
      display: flex;
      justify-content: space-around;
      padding-bottom: 20px;
      .tab_item{
        width: 120px;
        text-align: center;
        height: 18px;
        font-size: 18px;
        font-weight: 400;
        padding-bottom: 28px;
        //margin-left: 75px;
        &.active{
          color: #4197F1;
          border-bottom: 4px solid #4197F1;
        }

      }
    }
    .input-or-button{
      .el-form-item__content{
        display: flex;
        .el-input{
          width:307px;
        }

        .el-button{
          margin-left: 7px;
          margin-top: 4px!important;
          width: 60px;
          height: 32px;
          background: #2D8CF0;
          border-radius: 4px;
          min-width: 60px!important;
        }
        .text-info{
          margin-left: 7px;
          color: #2D8CF0;
          text-decoration: underline;
        }
      }
    }
  }
}
// 新建消费者用户


.new_source-dialog{
  .dialog_content{
    .password_look{
      .el-icon-view{
        margin-left: 10px;
        font-size: 20px;
        height: 32px;
        line-height: 32px;
        &.active{
          color: #2D8CF0;
        }
      }
    }
    .el-tabs{
      display: flex;
      justify-content: center;
      .el-tabs__active-bar{
        width: 25px!important;
      }
      .el-tabs__nav-wrap::after {
        display: none;
      }
    }
    .top_tabs{
      display: flex;
      justify-content: space-around;
      padding-bottom: 20px;
      .tab_item{
        width: 60px;
        text-align: center;
        height: 30px;
        font-size: 18px;
        font-weight: 400;
        padding-bottom: 3px;
        //margin-left: 75px;
        &.active{
          color: #4197F1;
          border-bottom: 4px solid #4197F1;
        }

      }
    }

    .el-radio-group{
      display: flex;
      justify-content: space-around;
      padding-bottom: 20px;
    }
    .input-or-button{
      .el-form-item__content{
        display: flex;
        .el-input{
          width:285px;

        }
        .el-button{
          margin-left: 7px;
          width: 80px;
          height: 32px;
          background: #2D8CF0;
          border-radius: 4px;
        }
        .text-info{
          margin-left: 7px;
          color: #2D8CF0;
          text-decoration: underline;
        }
      }
    }
    .public_item{
      .double_input{
        display: flex;
        .double_select{
          .el-form-item__content{
            margin-left:0px!important;
          }
        }
        .el-input{
          width:175px;

        }
        .el-select{
          margin-left:24px;
        }
      }
    }
  }
  .el-dialog__footer{
    .el-button{
      margin-left: 20px!important;
    }
  }
}

//信源管理
.source_management{
  .el-select,.el-date-editor,.top_text,.el-button{
    margin-left: 0px;
  }
  .top_text{
    margin-left: 10px;
  }
  .top_text-content{
    margin-right: 10px;
  }
  .el-input__inner{
    height: 32px;
  }
  .el-input__suffix-inner{
    .el-input__icon{
      line-height: 32px;
    }
  }
  .el-date-editor--datetime{
    height: 32px;

    .el-input__inner{
      height: 32px;
    }
    .el-input__icon{
      line-height: 32px;
    }
  }
  .top_text{
    height: 32px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 32px;
  }
  .content-body{
    .name-wrapper{
      overflow: hidden;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;

    }
  }
  .content-body{
    .el-table__body{
      .name-wrapper{
        .tag_succ{
          width: 74px;
          height: 24px;
          line-height: 24px;
          border: 1px solid #70B603;
          border-radius: 4px;
          color: #70B603;
          background: #ffffff;
          text-align:center;
        }
        .tag_err{
          width: 74px;
          height: 24px;
          line-height: 24px;
          border: 1px solid #ED4014;
          border-radius: 4px;
          color: #ED4014;
          background: #ffffff;
          text-align:center;
        }
        .tag_war{
          width: 74px;
          height: 24px;
          line-height: 24px;
          border: 1px solid #e7e439;
          border-radius: 4px;
          color: #e7e439;
          background: #ffffff;
          text-align:center;
        }
      }
    }
  }
  .top-right-button{
    .el-button{
      height: 32px;
      border: 1px solid #19BE6B;
      color: #19BE6B;
      font-size: 14px;
      font-weight: 400;
      //line-height: 24px;
    }
  }
  .top-right-input{
    .el-input{
      height: 32px;
      width: 300px;
    }
    .el-input__inner{
      height: 32px;
    }
    .el-input--mini .el-input__inner{
      height:32px;
      line-height:32px
    }
  }
  .content-body{
    .el-table__body{
      .el-button{
        border: 1px solid #3D73EF;
        color: #3D73EF;
      }
    }
    .el-table__header-wrapper{
      background: #F8F8F9;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
  }

}



//预言机模板
.prophecy_template{

  .content-body{
    .name-wrapper{
      overflow: hidden;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;

    }
  }
  .top-right-button{
    .el-button{
      height: 32px;
      border: 1px solid #19BE6B;
      color: #19BE6B;
      font-size: 14px;
      font-weight: 400;
      //line-height: 24px;
    }
  }
  .top-right-input{
    .el-input{
      height: 32px;
      width: 300px;
    }
    .el-input__inner{
      height: 32px;
    }
    .el-input--mini .el-input__inner{
      height:32px;
      line-height:32px
    }
  }
  .content-body{
    .el-table__body{
      .el-button{
        border: 1px solid #3D73EF;
        color: #3D73EF;
      }
    }
    .el-table__header-wrapper{
      background: #F8F8F9;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
  }

}


//预预言机列表
.prophecy_list{

  .el-select,.el-date-editor,.top_text,.el-button{
    margin-left: 5px;
  }
  .el-date-editor--datetime{
    height: 32px;

    .el-input__inner{
      height: 32px;
    }
    .el-input__icon{
      line-height: 32px;
    }
  }
  .top_text{
    height: 32px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 32px;
  }
  .top-right-button{
    .el-button{
      height: 32px;
      border: 1px solid #19BE6B;
      color: #19BE6B;
      font-size: 14px;
      font-weight: 400;
      //line-height: 24px;
    }
  }
  .top-right-input{
    .el-input{
      height: 32px;
      width: 300px;
    }
    .el-input__inner{
      height: 32px;
    }
    .el-input--mini .el-input__inner{
      height:32px;
      line-height:32px
    }
  }
  .content-body{
    .el-table__body{
      .el-button{
        border: 1px solid #3D73EF;
        color: #3D73EF;
      }
    }
    .el-table__header-wrapper{
      background: #F8F8F9;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
  }

}



//交易记录
.transaction_record{
  .top-right-button{
    .el-button{
      height: 32px;
      border: 1px solid #19BE6B;
      color: #19BE6B;
      font-size: 14px;
      font-weight: 400;
      //line-height: 24px;
    }
  }
  .el-input__inner{
    height: 32px;
  }
  .el-input__suffix-inner{
    .el-input__icon{
      line-height: 32px;
    }
  }
  .el-date-editor--datetime{
    height: 32px;
    width: 200px!important;
    .el-input__inner{
      width: 200px;
      height: 32px;
    }
    .el-input__icon{
      line-height: 32px;
    }
  }
  .top-right-input{

    .el-input{
      height: 32px;
      width: 300px;
    }
    .el-input__inner{
      height: 32px;
    }
    .el-input--mini .el-input__inner{
      height:32px;
      line-height:32px
    }
  }
  .content-body{
    .el-table__body{
      .el-button{
        border: 1px solid #3D73EF;
        color: #3D73EF;
      }
    }
    .el-table__header-wrapper{
      background: #F8F8F9;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
  }
  .content-top-right{
    .el-select,.el-date-editor,.top_text,.el-button{
      margin-left: 5px;
    }
    .top_text{
      height: 32px;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 32px;
    }
    .el-button{
      width: 82px;
      height: 32px;
      background: #2D8CF0;
      opacity: 1;
      border-radius: 4px;
    }
    .el-input{
      height: 32px;
      width: 160px;
    }
    .el-input--mini .el-input__inner{
      height:32px;
      line-height:32px
    }
    .consumer_user-select{
      .el-input{
        height: 32px;
        width: 280px;
      }
      .el-input--mini .el-input__inner{
        height:32px;
        line-height:32px
      }
    }
  }

}


//异常日志
.exception_log{
  .top-right-button{
    .el-button{
      height: 32px;
      border: 1px solid #19BE6B;
      color: #19BE6B;
      font-size: 14px;
      font-weight: 400;
      //line-height: 24px;
    }
  }
  .el-input__inner{
    height: 32px;
  }
  .el-input__suffix-inner{
    .el-input__icon{
      line-height: 32px;
    }
  }
  .el-date-editor--datetime{
    height: 32px;
    width: 200px!important;
    .el-input__inner{
      width: 200px;
      height: 32px;
    }
    .el-input__icon{
      line-height: 32px;
    }
  }
  .top-right-input{
    .el-input{
      height: 32px;
      width: 300px;
    }
    .el-input__inner{
      height: 32px;
    }
    .el-input--mini .el-input__inner{
      height:32px;
      line-height:32px
    }
  }
  .content-body{
    .el-table__body{
      .el-button{
        border: 1px solid #3D73EF;
        color: #3D73EF;
      }
    }
    .el-table__header-wrapper{
      background: #F8F8F9;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
    }
  }
  .content-top-right{
    .el-select,.el-date-editor,.top_text,.el-button{
      margin-left: 5px;
    }
    .top_text{
      height: 32px;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 32px;
    }
    .el-button{
      width: 82px;
      height: 32px;
      background: #2D8CF0;
      opacity: 1;
      border-radius: 4px;
    }
    .el-input{
      height: 32px;
      width: 160px;
    }
    .el-input--mini .el-input__inner{
      height:32px;
      line-height:32px
    }
    .consumer_user-select{
      .el-input{
        height: 32px;
        width: 280px;
      }
      .el-input--mini .el-input__inner{
        height:32px;
        line-height:32px
      }
    }
  }

}

.icon-search_suffix{

}
//.el-input__suffix{
//  top: 7px!important;
//  right: 12px;
//  font-size: 18px;
//}
//.content-top{
//  .icon-search_suffix{
//    top: 7px!important;
//    right: 12px;
//    font-size: 18px;
//  }
//}
//
//.top-right-input{
//  position: relative;
//}
//.dialog_content{
//  .icon-search_suffix{
//    top: 7px!important;
//    right: 9px;
//    font-size: 18px;
//    position: absolute;
//  }
//}

.icon-search_suffix{
  .el-input__suffix{
    top: 4px!important;
    right: 12px;
    font-size: 18px;
  }
}


.smart_contract{
  .icon-search_suffix{
    position: relative;
    .el-icon-search{
      position: absolute;
      top: 7px!important;
      right: 12px;
      font-size: 18px;
    }
  }
  .icon-search_suffixfff{
    position: relative;
    .el-icon-search{
      position: absolute;
      top: 7px!important;
      right: 12px;
      font-size: 18px;
    }
  }
}

.edit_registration{
  .el-dialog__footer{
    .el-button{
      margin-left: 10px!important;
    }
  }
}