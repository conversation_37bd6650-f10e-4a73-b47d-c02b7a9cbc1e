
<template>
  <div class="content">
    <li><span>工单列表</span>
      <Button class="btn" type="success" icon="md-add" ghost @click="handleUrl('new_workorder')">新建工单</Button>
    </li>
    <div class="chaintable">
      <edit-table-mul style="margin: 10px;" :columns="columns" v-model="tableData" :key="transferKey">
      </edit-table-mul>
      <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;margin: 10px;" />
    </div>
  </div>
</template>

<script>
// import { msgInfo } from '@/lib/tools'
import { getOrderListUer } from '@/api/data'
// import EditTable from '_c/edit-table'
import EditTableMul from '_c/edit-table-mul'
import { mapActions } from 'vuex'

export default {
  name: 'workorder-mng',
  components: {
    // EditTable,
    EditTableMul
  },
  data () {
    const statusDict = {
      UNAPPROVED: '待审核',
      PROCESSED_SUCCESS: '处理成功',
      PROCESSED_FAILED: '处理失败'
    }
    return {
      statusDict,
      transferKey: 0,
      tablePageParam: { pagetotal: 0, pageSize: 10, pageIndex: 1 },
      tableData: [],
      columns: [
        { key: 'title', title: '标题', tooltip: true },
        { key: 'reportTime', title: '填报日期', tooltip: true },
        { key: 'orderStatus', title: '状态', tooltip: true },
        { // key: 'action',// fixed: 'right',
          title: '操作',
          minWidth: 50,
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button',
                {
                  props: { type: 'text', size: 'small' },
                  style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                  on: { click: () => { this.showDetails(params) } }
                }, '详情')
            ])
          }
        }
      ]
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, duration: 8 }) },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    getTableData (datas) {
      const params = {
        pageParam: datas || this.tablePageParam
      }
      // console.log(this.tablePageParam, datas, 'getTableData====')
      getOrderListUer(params).then(res => {
        // console.log('getOrderListUer===>', res)
        if (res.code !== '00000') this.msgInfo('warning', res.message, true)
        else {
          this.tableData = res.data.records
          this.tableData = this.tableData.map(
            val => {
              val['orderStatus'] = this.statusDict[val.orderStatus]
              return val
            }
          )
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          ++this.transferKey
        }
      }).catch(error => {
        // console.log('getOrderListUer.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    handleUrl (url) { this.$router.push({ name: url }) },
    showDetails (params) {
      // console.log(params, params.row, params.row.orderId, ' this.orderId')
      this.$router.push({
        name: 'workorder_detail',
        params: {
          orderId: params.row.orderId || '11'
        }
      })
    },
    ...mapActions([
      'updateTablePageParam'
    ])
  },
  mounted () {
    const tablePageParam =
      this.$store.getters.tablePageParam !== '{}' ? JSON.parse(this.$store.getters.tablePageParam) : this.tablePageParam
    this.$nextTick(() => {
      this.getTableData(tablePageParam)
    })
  },
  destroyed () {
    this.updateTablePageParam(this.tablePageParam)
  }
}
</script>

<style lang="less" scoped>
ul,
li {
  list-style: none;
  margin-top: 10px;
}

li span {
  vertical-align: middle;
  display: table-cell;
  font-weight: bold;
  font-size: 16px;
  margin-top: 0px;
  cursor: default;
}
.content {
  li {
    display: flex;
    align-items: center;
    height: 40px;
    button.btn {
      position: absolute;
      right: 10px;
      margin: 0 10px;
    }
  }
}
</style>

<style lang="less">
.ivu-table-tip {
  overflow: hidden;
}
</style>
