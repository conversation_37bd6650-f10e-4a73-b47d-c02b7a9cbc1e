import { routes, routerMap } from '@/router/router'

const state = {
  // routers: routes, //  不借助于action就是初始化为routes,反之取routerMap
  routers: routerMap,
  hasGetRules: false
}
// const copyState = deepClone(state) // 拷贝state对象
function deepClone (obj) {
  var newObj = obj instanceof Array ? [] : {}
  for (var i in obj) {
    newObj[i] = typeof obj[i] === 'object' ? deepClone(obj[i]) : obj[i]
  }
  return newObj
  // return JSON.parse(JSON.stringify(obj))
}
const getters = {
  hasGetRules: (state) => {
    return state.hasGetRules
  }
}
const mutations = {
  CONCAT_ROUTES (state, routerList) {
    state.routers = routerList.concat(state.routers)
    state.hasGetRules = true
  },
  resetState (state) {
    const copyState = {
      // routers: routes, //  不借助于action就是初始化为routes,反之取routerMap
      routers: [],
      hasGetRules: false
    }
    for (let i in copyState) {
      state[i] = copyState[i] // 递归赋值
    }
  }
}

const getAccesRouterList = (routes, rules) => {
  return routes.filter(item => {
    if (rules[item.name]) {
      if (item.children) item.children = getAccesRouterList(item.children, rules)
      return true
    } else return false
  })
}

const actions = {
  concatRoutes ({ commit }, rules) {
    return new Promise((resolve, reject) => {
      try {
        // console.log(rules, 'rules===')
        let routerList = []
        const deepRoutes = deepClone(routes)
        // console.log('初始化', Object.entries(rules), Object.entries(rules).every(item => item[1]))
        // if (Object.entries(rules).every(item => item[1])) {
        //   console.log('进入之后', Object.entries(rules).every(item => item[1]))
        //   routerList = routes
        // } else {
        //   routerList = getAccesRouterList(deepRoutes, rules)
        // }
        routerList = getAccesRouterList(deepRoutes, rules)
        commit('CONCAT_ROUTES', routerList)
        // console.log(routerList,'routerList')
        resolve(state.routers)
      } catch (err) {
        reject(err)
      }
    })
  },
  resetRouterState ({ commit }) {
    commit('resetState')
  }
}

export default {
  getters,
  state,
  mutations,
  actions
}
