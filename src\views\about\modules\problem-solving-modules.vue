<template>
  <Modal
    v-model="visible"
    :title="isAdd ? '新增问题' : '编辑问题'"
    ok-text="提交"
    :loading="true"
    @on-visible-change="open"
  >
    <Form
      ref="form"
      :model="form"
      :rules="rules"
      :label-width="60"
      @submit.native.prevent="onSubmit"
      class="form"
    >
      <Form-item label="问题" prop="problemName">
        <Input v-model="form.problemName" placeholder="请输入问题" :maxlength="200"  @blur="(val)=>{inputChange(val,'problemName')}"></Input>
      </Form-item>
      <Form-item label="回答" prop="problemAnswer">
        <Input
          @on-blur="(val)=>{inputChange(val,'problemAnswer')}"
          v-model="form.problemAnswer"
          placeholder="请输入回答"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"
          :maxlength="500"
          show-word-limit
        ></Input>
      </Form-item>
    </Form>
    <span slot="footer">
      <Button type="text" @click="cancel">取 消</Button>
      <Button type="primary" @click="submitData">提 交</Button>
    </span>
  </Modal>
</template>

<script>
import { addOrEditCommonProblemVersion } from '@/api/data'
export default {
  props: {
    rowData: String | Object
  },
  data () {
    return {
      visible: false,
      isAdd: true,
      form: {
        problemName: '',
        problemAnswer: ''
      },
      rules: {
        problemName: [
          { required: true, message: '请输入问题', trigger: 'blur' },
        ],
        problemAnswer: [
          { required: true, message: '请输入答案', trigger: 'blur' },
        ]
      }
    }
  },
  methods: {
    /**
     * visible = true 开启弹框
     */
    open (visible) {
      if (visible) {
        this.isAdd = this.rowData === 'add'
        if (!this.isAdd) {
          this.form = { ...this.rowData }
        } else {
          this.form = {
            problemName: '',
            problemAnswer: ''
          }
        }
      } else {
        this.$refs['form'].resetFields()
      }
    },
    /**
     * @提交数据
     * isAdd 是否新增
     */
    submitData () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          addOrEditCommonProblemVersion(this.form)
            .then((res) => {
              if (res.code === '00000') {
                this.msgInfo('success', res.message, true)
                this.visible = false
                this.$emit('commonProblemList')
              } else {
                this.msgInfo('error', res.message)
              }
            })
            .catch((error) => {
              this.msgInfo('error', error.message, true)
            })
        }
      })
    },
    /**
     * 阻止form默认提交
     */
    onSubmit(){
      return false
    },
    cancel () {
      this.visible = false
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    inputChange(e,name){
      let value = e.currentTarget._value
      this.form[name] = value.trim()
    }
  }
}
</script>

<style lang="less" scoped>
.form {
  margin: 30px 40px;
}
</style>
