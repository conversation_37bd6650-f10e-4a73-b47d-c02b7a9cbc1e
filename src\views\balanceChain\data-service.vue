<template>
  <div class="contractApp">
    <Tabs :value="name" @on-click="clickTab">
      <TabPane label="数据采集服务" name="name2">
        <div style="margin:10px 10px 15px 0px;display:flex;justify-content: space-between;">
          <p>
            <Input class='bt1 width-input' placeholder="请输入服务名称/url" @on-enter="searchList1" style="vertical-align:baseline;width:230px;margin-right:10px" v-model.trim="datainput" @keyup.native="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm,'')" />
            <Button class='bt1' icon="ios-search" type="primary" @click="searchList1" style="margin-right:10px">查询</Button>
            <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting1">重置</Button>
          </p>
          <p>
            <Button class='bt1' type="success" ghost icon="md-add" @click="gatherData">新建数据采集</Button>
          </p>
        </div>
        <Table :columns="columns2" :data="tableData2">
          <template slot-scope="{ row, index }" slot="action">
            <Button type="text" size="small" :loading='row.loading' :disabled="row.status=='启动'" :style="row.status=='启动'? 'marginRight: 8px; color: #cccccc; border:1px solid #cccccc' : 'marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF'" @click="startbtn(row,index)">启用</Button>
            <Button type="text" size="small" :loading='row.loading1' :disabled="row.status=='停止'" :style="row.status=='停止'? 'marginRight: 8px; color: #cccccc; border:1px solid #cccccc' : 'marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF'" @click="stopbtn(row,index)">停止</Button>
            <Button type="text" size="small" :disabled="row.status=='启动'" :style="row.status=='启动'? 'marginRight: 8px; color: #cccccc; border:1px solid #cccccc' : 'marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF'" @click="editbtn(row)">编辑</Button>

            <!-- <Button type="text" size="small"
                   :disabled="!((row.statusMsg === '部署失败' || row.statusMsg === '审批通过')&&row.show===false)"
                   :style="(row.statusMsg === '部署失败' || row.statusMsg === '审批通过')&&row.show===false ? 'color:#3D73EF;border:1px solid #3D73EF' : 'color:#c5c8ce;border:1px solid #c5c8ce'"  @click="appretry(row)">重试<span v-if="row.retrySurplusTime!==0&&row.show===true">({{row.retrySurplusTime}})</span></Button> -->
          </template>
        </Table>
        <Page :total="tablePageParam2.pagetotal" :current.sync="tablePageParam2.pageIndex" @on-change="pageChange2" transfer :page-size="tablePageParam2.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange2" style="text-align:right;line-height:40px" />
      </TabPane>
      <TabPane label="数据推送服务" name="name1">
        <div style="margin:10px 10px 15px 0px;display:flex;justify-content: space-between;">
          <p>
            <Input class='bt1 width-input' placeholder="请输入服务名称" @on-enter="searchList" style="vertical-align:baseline;width:230px;margin-right:10px" v-model.trim="pushService" @keyup.native="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm,'')" />
            <Button class='bt1' icon="ios-search" type="primary" @click="searchList" style="margin-right:10px">查询</Button>
            <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting">重置</Button>
          </p>
          <p><Button class='bt1' type="success" ghost icon="md-add" @click="newData">新建数据推送</Button></p>
        </div>
        <Table :columns="columns" :data="tableData">
          <template slot-scope="{ row, index }" slot="action">
            <Button type="text" size="small" :loading='row.loading' :disabled="row.status=='启动'" :style="row.status=='启动'? 'marginRight: 8px; color: #cccccc; border:1px solid #cccccc' : 'marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF'" @click="upstartbtn(row,index)">启用</Button>
            <Button type="text" size="small" :loading='row.loading1' :disabled="row.status=='停止'" :style="row.status=='停止'? 'marginRight: 8px; color: #cccccc; border:1px solid #cccccc' : 'marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF'" @click="upstopbtn(row,index)">停止</Button>
            <Button type="text" size="small" :disabled="row.status=='启动'" :style="row.status=='启动'? 'marginRight: 8px; color: #cccccc; border:1px solid #cccccc' : 'marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF'" @click="upeditbtn(index)">编辑</Button>
          </template>
        </Table>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" transfer :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px" />
      </TabPane>

    </Tabs>
    <Modal v-model="modalPush" :title="appModal" :width="600" :draggable="true" sticky :mask-closable="false" @on-cancel="cancelApp">
      <Form ref="formItemRule" :rules="formItemRule" :model="arrTenant" :label-width="130" @submit.native.prevent>
        <FormItem label="目标链：" prop="">
          <p>天平链</p>
        </FormItem>
        <FormItem label="服务名称：" prop="username" v-if="appModal==='新建数据推送'">

          <!-- <Select v-model="arrTenant.username" placeholder="请选择要采集的服务名称" style="width:370px" filterable>
            <Scroll :on-reach-bottom="handleReachBottom" :distance-to-edge="[8,8]" :height="120" :loading-text="modifyTotal<=modifyParentList.length ? '已全部加载完成' : '加载中。。。。。。'">
              <Option v-for="item in modifyParentList" :value="item.id" :key="item.id">{{item.name}}</Option>
              <p class="p-finish" v-if="modifyTotal <= modifyParentList.length">已全部加载完成</p>
            </Scroll>
          </Select> -->

          <Select v-model="arrTenant.username" ref="stationSelect" placeholder="请选择要推送的服务名称" style="width:370px">
            <Option v-for="item in modifyParentList" :value="item.id" :key="item.id">{{ item.name }}</Option>
            <Option :value="arrTenant.username+''" :disabled="true" v-if="modifyTotal>modifyParentList.length" style="text-align:center">
              <span @mouseover="handleReachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
            </Option>
            <Option :value="arrTenant.username+''" :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
              <span style="font-size:8px;">已加载全部</span>
            </Option>
          </Select>
        </FormItem>
        <FormItem label="服务名称：" prop="username1" v-if="appModal==='编辑数据推送'">
          <p>{{arrTenant.username}}</p>
        </FormItem>
        <!-- <FormItem label="数据量：" prop="datavolume">
          <Input style="width:370px" v-model="arrTenant.datavolume" placeholder="请输入需要取的数据量，例1000条" />
        </FormItem> -->
        <FormItem label="cron表达式：" prop="representation">
          <Tooltip max-width="220" content="常用cron表达式" style="margin-left: -18px;" placement="top-start">
            <div slot="content">
              <p>每月1号0点执行 0 0 0 1 * ? </p>
              <p>每周一0点执行 0 0 0 ? * MON</p>
              <p>每天0点执行 0 0 0 * * ?</p>
            </div>
            <Icon type="md-help-circle" style="font-size:16px;" />
          </Tooltip>
          <!-- <Tooltip max-width="200" content="常用cron表达式" style="margin-left: -18px;">
            <Icon type="md-help-circle" style="font-size:16px;" />
          </Tooltip> -->
          <Input style="width:370px" v-model.trim="arrTenant.representation" :maxlength="50" placeholder="请根据推送周期输入cron表达式" />
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="default" @click="cancelApp">取消</Button>
        <Button type="primary" @click="ok('formItemRule')" :loading='pushLoading'>确定</Button>
      </div>
    </Modal>
    <Modal v-model="modalgather" :title="sourceModal" :width="600" :draggable="true" :mask-closable="false" sticky @on-cancel="sourcecancel">
      <Form ref="formItemsourceRule" :rules="formItemsourceRule" :model="sourceTenant" :label-width="130" @submit.native.prevent>
        <FormItem label="导出方式：" prop="">
          <p>数据管理组件导出</p>
        </FormItem>
        <FormItem label="服务名称：" prop="username" v-if="sourceModal==='新建数据源'">
          <!-- <Select v-model="sourceTenant.username" placeholder="请选择数据名称" style="width:370px" filterable>
            <Option v-for="item in modifyParentList" :value="item.id" :key="item.id">{{item.name}}</Option>
          </Select> -->
          <!-- <Select v-model="sourceTenant.username" placeholder="请选择要采集的服务名称" style="width:370px" filterable>
            <Scroll :on-reach-bottom="handleReachBottom" :distance-to-edge="[8,8]" :height="120" :loading-text="modifyTotal<=modifyParentList.length ? '已全部加载完成' : '加载中。。。。。。'">
              <Option v-for="item in modifyParentList" :value="item.id" :key="item.id">{{item.name}}</Option>
              <p class="p-finish" v-if="modifyTotal <= modifyParentList.length">已全部加载完成</p>
            </Scroll>
          </Select> -->

          <Select v-model="sourceTenant.username" ref="stationSelect" placeholder="请选择要推送的服务名称" style="width:370px">
            <Option v-for="item in modifyParentList" :value="item.id" :key="item.id">{{ item.name }}</Option>
            <Option :value="sourceTenant.username+''" :disabled="true" v-if="modifyTotal>modifyParentList.length" style="text-align:center">
              <span @mouseover="handleReachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
            </Option>
            <Option :value="sourceTenant.username+''" :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
              <span style="font-size:8px;">已加载全部</span>
            </Option>
          </Select>
        </FormItem>
        <FormItem label="服务名称：" prop="username1" v-if="sourceModal==='编辑数据源'">
          <p>{{sourceTenant.username}}</p>
        </FormItem>

        <FormItem label="cron表达式：" prop="representation">
          <!-- <Tooltip max-width="200" content="常用cron表达式" style="margin-left: -18px;">
            <Icon type="md-help-circle" style="font-size:16px;" />
          </Tooltip> -->
          <Tooltip max-width="220" content="常用cron表达式" style="margin-left: -18px;" placement="top-start">
            <div slot="content">
              <p>每月1号0点执行 0 0 0 1 * ? </p>
              <p>每周一0点执行 0 0 0 ? * MON</p>
              <p>每天0点执行 0 0 0 * * ?</p>
            </div>
            <Icon type="md-help-circle" style="font-size:16px;" />
          </Tooltip>

          <Input style="width:370px" v-model.trim="sourceTenant.representation" placeholder="请根据推送周期输入cron表达式" :maxlength="50" />
        </FormItem>
        <FormItem label="起始区块号：" prop="blockno">
          <Input style="width:370px" v-model.trim="sourceTenant.blockno" :maxlength="19" placeholder="请输入起始区块号" @keyup.native="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm,'')" />
        </FormItem>
        <FormItem label="数据量：" prop="datavolume">
          <Input style="width:370px" v-model.trim="sourceTenant.datavolume" :maxlength="19" placeholder="请输入需要取的数据量，例1000条" @keyup.native="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm,'')" />
        </FormItem>
        <FormItem label="url：" prop="url">
          <Input style="width:370px" v-model.trim="sourceTenant.url" placeholder="请输入数据管理组件导出服务的url" @keyup.native="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm,'')" />
          <!-- <p v-else>{{sourceTenant.url}}</p> -->
          <!-- <Tooltip placement="top" max-width="200" v-else>
            <div style="width:350px;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">{{sourceTenant.url}}</div>
            <div slot="content">
              <p>{{sourceTenant.url}}</p>
            </div>
          </Tooltip> -->
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="default" @click="sourcecancel">取消</Button>
        <Button type="primary" @click="sourceok('formItemsourceRule')" :loading='pullLoading'>确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
// import { getContractsData, getUserData, getContrApprDetails, contractApprovel, getContractChaincode, getChaincode } from '@/api/data'
import { datapushList, newdataPush, newpushfrom, updatepushfrom, updateStatus, newsavefrom, updateScalesfrom, getCron } from '@/api/balance'
// import cronParse from 'cron-parser'
import { isNumber } from "@/lib/check"
import EditTableMul from '_c/edit-table-mul'
import ScrollOption from '_c/scroll-option'
export default {
  name: '',
  components: {
    EditTableMul,
    ScrollOption
  },
  data () {
    const validateValue = (rule, value, callback) => {
      if (!isNumber(value)) {
        callback(new Error('请输入数字'))
      } else {
        callback()
      }
    }
    const checkData1 = (rule, value, callback) => {
      if (value) {
        getCron(value).then(res => {
          if (res.code == '00000') {
            callback()
          }
          callback(new Error('请输入正确的cron表达式'))
        })
      } else {
        callback(new Error('请输入cron表达式'))
      }

    }
    // const checkData1 = (rule, value, callback) => {
    //   if (value) {
    //     try {
    //       const interval = cronParse.parseExpression(value)
    //       console.log('cronDate:', interval.next().toDate())
    //     } catch (e) {
    //       callback('请输入正确的cron表达式')
    //     }
    //   } else {
    //     callback('执行表达式不能为空！')
    //   }
    //   callback()
    // }
    // const checkCron = (rule, value, callback) => {
    //   var regu = /^ +| +$/g // 匹配空格
    //   if (value === '') {
    //     return callback(new Error('请输入正确的Cron表达式'))
    //   } else if (value && regu.test(value)) {
    //     return callback(new Error('Cron表达式前后不能加空格!'))
    //     // else if (value && !(/^[(\u4e00-\u9fa5)|(0-9)]+$/).test(value)) {
    //     // return callback(new Error('请输入中文和数字及字母和下划线!'));
    //   } else {
    //     return callback()
    //   }
    // }

    return {
      imgUrl: require('@/assets/img/arrow.png'),
      modifyParentList: [],
      modifyTotal: 0,
      selectList: [{ id: 1, companyName: '1111' }, { id: 2, companyName: '222' }],
      formItemRule: {
        username: [{ required: true, message: '请选择要推送的服务名称' }],
        // datavolume: [{ required: true, message: '请输入需要取的数据量，例1000条', trigger: 'blur' }],
        representation: [{ required: true, message: "请输入cron表达式", trigger: "blur" },
        // {
        //   pattern:
        //     "^\\s*($|#|\\w+\\s*=|(\\?|\\*|(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?(?:,(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?)*)\\s+(\\?|\\*|(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?(?:,(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?)*)\\s+(\\?|\\*|(?:[01]?\\d|2[0-3])(?:(?:-|\\/|\\,)(?:[01]?\\d|2[0-3]))?(?:,(?:[01]?\\d|2[0-3])(?:(?:-|\\/|\\,)(?:[01]?\\d|2[0-3]))?)*)\\s+(\\?|\\*|(?:0?[1-9]|[12]\\d|3[01])(?:(?:-|\\/|\\,)(?:0?[1-9]|[12]\\d|3[01]))?(?:,(?:0?[1-9]|[12]\\d|3[01])(?:(?:-|\\/|\\,)(?:0?[1-9]|[12]\\d|3[01]))?)*)\\s+(\\?|\\*|(?:[1-9]|1[012])(?:(?:-|\\/|\\,)(?:[1-9]|1[012]))?(?:L|W)?(?:,(?:[1-9]|1[012])(?:(?:-|\\/|\\,)(?:[1-9]|1[012]))?(?:L|W)?)*|\\?|\\*|(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)(?:(?:-)(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC))?(?:,(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)(?:(?:-)(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC))?)*)\\s+(\\?|\\*|(?:[0-6])(?:(?:-|\\/|\\,|#)(?:[0-6]))?(?:L)?(?:,(?:[0-6])(?:(?:-|\\/|\\,|#)(?:[0-6]))?(?:L)?)*|\\?|\\*|(?:MON|TUE|WED|THU|FRI|SAT|SUN)(?:(?:-)(?:MON|TUE|WED|THU|FRI|SAT|SUN))?(?:,(?:MON|TUE|WED|THU|FRI|SAT|SUN)(?:(?:-)(?:MON|TUE|WED|THU|FRI|SAT|SUN))?)*)(|\\s)+(\\?|\\*|(?:|\\d{4})(?:(?:-|\\/|\\,)(?:|\\d{4}))?(?:,(?:|\\d{4})(?:(?:-|\\/|\\,)(?:|\\d{4}))?)*))$", message: '请输入正确的cron表达式', trigger: 'blur'
        // },
        { trigger: 'blur', validator: checkData1 }

        ]
      },

      formItemsourceRule: {
        username: [{ required: true, message: '请选择要采集的服务名称' }],
        representation: [{ required: true, message: "请输入cron表达式", trigger: "blur" },
        // {

        //   pattern:
        //     "^\\s*($|#|\\w+\\s*=|(\\?|\\*|(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?(?:,(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?)*)\\s+(\\?|\\*|(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?(?:,(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?)*)\\s+(\\?|\\*|(?:[01]?\\d|2[0-3])(?:(?:-|\\/|\\,)(?:[01]?\\d|2[0-3]))?(?:,(?:[01]?\\d|2[0-3])(?:(?:-|\\/|\\,)(?:[01]?\\d|2[0-3]))?)*)\\s+(\\?|\\*|(?:0?[1-9]|[12]\\d|3[01])(?:(?:-|\\/|\\,)(?:0?[1-9]|[12]\\d|3[01]))?(?:,(?:0?[1-9]|[12]\\d|3[01])(?:(?:-|\\/|\\,)(?:0?[1-9]|[12]\\d|3[01]))?)*)\\s+(\\?|\\*|(?:[1-9]|1[012])(?:(?:-|\\/|\\,)(?:[1-9]|1[012]))?(?:L|W)?(?:,(?:[1-9]|1[012])(?:(?:-|\\/|\\,)(?:[1-9]|1[012]))?(?:L|W)?)*|\\?|\\*|(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)(?:(?:-)(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC))?(?:,(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)(?:(?:-)(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC))?)*)\\s+(\\?|\\*|(?:[0-6])(?:(?:-|\\/|\\,|#)(?:[0-6]))?(?:L)?(?:,(?:[0-6])(?:(?:-|\\/|\\,|#)(?:[0-6]))?(?:L)?)*|\\?|\\*|(?:MON|TUE|WED|THU|FRI|SAT|SUN)(?:(?:-)(?:MON|TUE|WED|THU|FRI|SAT|SUN))?(?:,(?:MON|TUE|WED|THU|FRI|SAT|SUN)(?:(?:-)(?:MON|TUE|WED|THU|FRI|SAT|SUN))?)*)(|\\s)+(\\?|\\*|(?:|\\d{4})(?:(?:-|\\/|\\,)(?:|\\d{4}))?(?:,(?:|\\d{4})(?:(?:-|\\/|\\,)(?:|\\d{4}))?)*))$", message: '请输入正确的cron表达式', trigger: 'blur'
        // },
        { trigger: 'blur', validator: checkData1 }

        ],
        blockno: [{ required: true, message: '请输入区块号', trigger: 'blur', }, { trigger: 'blur', validator: validateValue }],
        datavolume: [{ required: true, message: '请输入需要取的数据量', trigger: 'blur', }, { trigger: 'blur', validator: validateValue }],
        url: [{ required: true, message: '请输入url', trigger: 'blur' }, {
          pattern:
            "(https?)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]", message: '请输入正确的url!', trigger: 'blur'
        }]
      },
      arrTenant: { username: '', datavolume: '', representation: '', id: '' },
      sourceTenant: { username: '', representation: '', blockno: '', datavolume: '', url: '', id: '' },
      name: this.$route.params.tabs || 'name2',
      loadingMsg: 'Loading',
      modalPush: false, // 数据推送
      modalgather: false, // 数据采集
      bizId: '',
      appModal: '新建数据推送',
      sourceModal: '新建数据源',
      datainput: '',
      userIdStr: '',
      tablePageParam: {
        pageSize: 10,
        pageIndex: 1,
        pagetotal: 0
      },
      tablePageParam2: {
        pageSize: 10,
        pageIndex: 1,
        pagetotal: 0
      },
      columns: [
        { key: 'scalePlatformResourceName', title: '服务名称', tooltip: true },
        { key: 'targetChain', title: '目标链', tooltip: true },
        { key: 'availableResources', title: '剩余资源（次）', tooltip: true },

        { key: 'createTime', title: '创建时间', tooltip: true },
        { key: 'updateTime', title: '操作时间', tooltip: true },
        { key: 'status', title: '状态', tooltip: true },
        // { key: 'statusMsg',
        //   title: '状态',
        //   minWidth: 130,
        //   tooltip: true,
        //   render: (h, params) => {
        //     const row = params.row
        //     const color = row.statusMsg === '等待管理员审批' ? 'primary' : row.statusMsg === '审批通过' ? '#2db7f5' : row.statusMsg === '审批驳回' ? 'error' : row.statusMsg === '部署成功' ? 'success' : '#515a6e'
        //     // const text = row.status === 1 ? 'Working' : row.status === 2 ? 'Success' : 'Fail';
        //     return h('Tag', {
        //       props: {
        //         type: 'dot',
        //         color: color
        //       },
        //       style: { marginLeft: '-8px' }
        //     }, row.statusMsg)
        //   }
        // },
        {
          slot: 'action',
          title: '操作',
          // fixed: 'right',
          align: 'left',
          minWidth: 130,
          startLoading: false
        }
      ],
      columns2: [
        { key: 'scalePlatformResourceName', title: '服务名称', tooltip: true },
        { key: 'dataSourcesUrl', title: 'url', tooltip: true },
        { key: 'createTime', title: '创建时间', tooltip: true },
        { key: 'updateTime', title: '操作时间', width: '170px', tooltip: true },
        { key: 'status', title: '状态', tooltip: true },
        {
          slot: 'action',
          title: '操作',
          // fixed: 'right',
          align: 'left',
          minWidth: 130
        }
      ],
      tableData: [],
      tableData2: [],
      nametype: '1',
      dataname: {
        pageParam: {
          pageIndex: 1,
          pageSize: 100
        }
      },
      selectType: '',
      pushService: '', // 推送服务
      pushLoading: false, //推送确定按钮loading
      pullLoading: false, //采集确定按钮loading
    }
  },
  computed: {

  },

  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    handleReachBottom () {
      return new Promise(resolve => {
        let tablePageParam = { pageSize: 10, pageIndex: 1 }
        let pageIndex = 1
        // if (this.arrRole.roleId) {
        pageIndex = this.modifyParentList.length / 10 + 1
        tablePageParam = Object.assign({}, tablePageParam, { pageIndex })
        if (this.modifyParentList.length % 10 !== 0 || Math.ceil(this.modifyTotal / 10) < pageIndex) {
          return resolve()
        } else {
          this.dataname.pageParam.pageIndex = tablePageParam.pageIndex
          this.dataname.pageParam.pageSize = tablePageParam.pageSize
          this.datanamelist()
        }

        resolve()
      })
    },
    // 数据服务名称列表
    datanamelist () {
      let data = {
        pageParam: this.dataname.pageParam,
        selectType: this.selectType
      }
      newdataPush(data).then(res => {
        if (res.code === '00000') {
          if (res.data.records && res.data.records.length > 0) {
            this.modifyParentList.push(...res.data.records)
            this.modifyTotal = res.data.total
          }
        } else {
          this.modifyParentList = []
          this.msgInfo('error', res.message)
        }
      }).catch(error => {
        this.modifyParentList = []
        this.msgInfo('error', error.message)
      })
    },
    // datanamelist1 (data) {
    //   newdataPush(data).then(res => {
    //     if (res.code === '00000') {
    //       this.modifyParentList1 = res.data.records
    //     } else {
    //       this.modifyParentList1 = []
    //       this.msgInfo('error', res.message)
    //     }


    //   }).catch(error => {
    //     this.modifyParentList1 = []
    //     this.msgInfo('error', error.message)
    //   })
    // },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.pushList()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.pushList()
    },
    pageChange2 (index) {
      this.tablePageParam2.pageIndex = index
      this.pullList()
    },
    pageSizeChange2 (index) {
      this.tablePageParam2.pageSize = index
      this.pullList()
    },
    clickTab (name) {
      if (name === 'name2') {
        this.pullList()
      } else {
        this.pushList()
      }
    },

    searchList () {
      this.tablePageParam = {
        pageSize: 10,
        pageIndex: 1,
      }
      this.pushList()
    },
    // 推送重置
    resetting () {
      this.pushService = ''
      this.tablePageParam = {
        pageSize: 10,
        pageIndex: 1,
      }
      this.pushList()
    },
    // 采集查询
    searchList1 () {
      this.tablePageParam2 = {
        pageSize: 10,
        pageIndex: 1,
      }
      this.pullList()
    },
    // 采集重置
    resetting1 () {
      this.datainput = ''
      this.tablePageParam2 = {
        pageSize: 10,
        pageIndex: 1,
      }
      this.pullList()
    },
    // 新建/编辑推送
    ok (formItemRule) {
      this.$refs[formItemRule].validate((valid) => {
        if (valid) {

          if (this.appModal === '新建数据推送') {
            this.pushLoading = true
            let datafrom = {
              targetChain: '天平链',
              scalePlatformResourceId: this.arrTenant.username,
              type: 2,
              cronExpressions: this.arrTenant.representation.trim(),
              batchNumber: this.arrTenant.datavolume
            }
            newpushfrom(datafrom).then(res => {
              if (res.code === '00000') {
                this.msgInfo('success', res.message, true)
                this.modalPush = false;
                this.pushLoading = false
                this.pushList()
              } else {
                this.pushLoading = false
                this.msgInfo('error', res.message, true)
              }
            }).catch(error => {
              this.pushLoading = false
              this.msgInfo('error', error.message, true)
            })
          } else {
            this.pushLoading = true
            let updatafron = {
              id: this.arrTenant.id,
              cronExpressions: this.arrTenant.representation.trim(),
              batchNumber: this.arrTenant.datavolume
            }
            updatepushfrom(updatafron).then(res => {
              if (res.code === '00000') {
                this.pushLoading = false
                this.msgInfo('success', res.message, true)
                this.modalPush = false;
                this.pushList()
              } else {
                this.pushLoading = false
                this.msgInfo('error', res.message, true)
              }
            }).catch(error => {
              this.pushLoading = false
              this.msgInfo('error', error.message, true)
            })
          }
        }
      })

    },
    // 推送新建
    newData () {
      this.modalPush = true
      this.pullLoading = false
      this.appModal = '新建数据推送'
      this.$nextTick(function () {
        this.$refs.formItemRule.resetFields()
      })

      this.dataname = {
        pageParam: {
          pageIndex: 1,
          pageSize: 100
        }
      }
      this.modifyTotal = 0
      this.modifyParentList = []
      this.selectType = 2
      this.datanamelist()
      // this.datanamelist1()
    },
    cancelApp () {
      this.modalPush = false
      this.$refs.formItemRule.resetFields()
    },
    // 推送启用
    upstartbtn (row, index) {
      this.arrTenant.id = this.tableData[index].id
      // 0关闭 1启用
      let upstart = {
        id: this.arrTenant.id,
        status: 1
      }
      row.loading = true
      // this.$set(this.loading1, index, true)
      updateStatus(upstart).then(res => {
        if (res.code === '00000') {
          this.pushList()
          row.loading = false
          this.msgInfo('success', res.message, true)

        } else {
          row.loading = false

          this.msgInfo('error', res.message, true)

        }

      }).catch(error => {
        row.loading = false
        this.msgInfo('error', error.message, true)

      })
    },
    // 推送停止
    upstopbtn (row, index) {
      this.arrTenant.id = this.tableData[index].id
      // 0关闭 1启用
      let upstop = {
        id: this.arrTenant.id,
        status: 0
      }
      row.loading1 = true
      updateStatus(upstop).then(res => {
        if (res.code === '00000') {
          this.pushList()
          row.loading1 = false
          this.msgInfo('success', res.message, true)
        } else {
          row.loading1 = false
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        row.loading1 = false
        this.msgInfo('error', error.message, true)
      })
    },
    // 推送编辑
    upeditbtn (index) {
      this.modalPush = true
      this.pushLoading = false
      this.appModal = '编辑数据推送'
      this.arrTenant.username = this.tableData[index].scalePlatformResourceName
      this.arrTenant.datavolume = this.tableData[index].batchNumber
      this.arrTenant.representation = this.tableData[index].cronExpressions
      this.arrTenant.id = this.tableData[index].id
    },
    // 采集
    gatherData () {
      this.modalgather = true
      this.pushLoading = false
      this.modifyParentList = []
      this.$nextTick(function () {
        this.$refs.formItemsourceRule.resetFields()
      })

      this.sourceModal = '新建数据源'
      this.dataname = {
        pageParam: {
          pageIndex: 1,
          pageSize: 100
        }
      },
        this.modifyTotal = 0
      this.selectType = 1
      this.datanamelist()
    },
    // 新建/编辑数据源
    sourceok (formItemsourceRule) {
      this.$refs[formItemsourceRule].validate((valid) => {
        if (valid) {
          if (this.sourceModal === '新建数据源') {
            this.pullLoading = true
            let newsave = {
              scalePlatformResourceId: this.sourceTenant.username,
              dataSourcesService: '数据管理组件导出',
              dataSourcesUrl: this.sourceTenant.url,
              type: 1,
              startBlock: this.sourceTenant.blockno,
              batchNumber: this.sourceTenant.datavolume,
              cronExpressions: this.sourceTenant.representation.trim()
            }
            newsavefrom(newsave).then(res => {
              if (res.code === '00000') {
                this.msgInfo('success', res.message, true)
                this.modalgather = false;
                this.pullLoading = false
                this.pullList()
              } else {
                this.pullLoading = false
                this.msgInfo('error', res.message, true)
              }
            }).catch(error => {
              this.pullLoading = false
              this.msgInfo('error', error.message, true)
            })
          } else {
            this.pullLoading = true
            let editdata = {
              id: this.sourceTenant.id,
              startBlock: this.sourceTenant.blockno,
              batchNumber: this.sourceTenant.datavolume,
              dataSourcesUrl: this.sourceTenant.url,
              cronExpressions: this.sourceTenant.representation.trim()

            }
            updateScalesfrom(editdata).then(res => {
              if (res.code === '00000') {
                this.msgInfo('success', res.message, true)
                this.modalgather = false;
                this.pullLoading = false
                this.pullList()
              } else {
                this.pullLoading = false
                this.msgInfo('error', res.message, true)
              }
            }).catch(error => {
              this.pullLoading = false
              this.msgInfo('error', error.message, true)
            })
          }
        }
      })


    },
    sourcecancel () {
      this.modalgather = false
      this.$refs.formItemsourceRule.resetFields()
    },
    // 采集启动
    startbtn (row, index) {
      row.loading = true
      this.sourceTenant.id = this.tableData2[index].id
      // 0关闭 1启用
      let upstart = {
        id: this.sourceTenant.id,
        status: 1
      }
      updateStatus(upstart).then(res => {
        if (res.code === '00000') {
          this.pullList()
          row.loading = false
          this.msgInfo('success', res.message, true)
        } else {
          row.loading = false
          this.msgInfo('error', res.message, true)
        }

      }).catch(error => {
        row.loading = false
        this.msgInfo('error', error.message, true)
      })
    },
    stopbtn (row, index) {
      row.loading1 = true
      this.sourceTenant.id = this.tableData2[index].id
      // 0关闭 1启用
      let upstart = {
        id: this.sourceTenant.id,
        status: 0
      }
      updateStatus(upstart).then(res => {
        if (res.code === '00000') {
          this.pullList()
          row.loading1 = false
          this.msgInfo('success', res.message, true)
        } else {
          row.loading1 = false
          this.msgInfo('error', res.message, true)
        }

      }).catch(error => {
        row.loading1 = false
        this.msgInfo('error', error.message, true)
      })
    },
    editbtn (row) {
      // sourceTenant: { username: '', representation: '', blockno: '', datavolume: '', url: '' },
      this.pullLoading = false
      this.modalgather = true
      this.sourceModal = '编辑数据源'
      this.sourceTenant.username = row.scalePlatformResourceName
      this.sourceTenant.datavolume = row.batchNumber + ''
      this.sourceTenant.representation = row.cronExpressions
      this.sourceTenant.blockno = row.startBlock + ''
      this.sourceTenant.url = row.dataSourcesUrl
      this.sourceTenant.id = row.id
    },

    // 数据推送列表2
    pushList () {
      let list = {
        type: 2,
        scalePlatformResourceName: this.pushService,
        pageParam: {
          pageIndex: this.tablePageParam.pageIndex,
          pageSize: this.tablePageParam.pageSize
        }
      }
      datapushList(list).then(res => {
        this.tablePageParam.pagetotal = res.data.total

        let resdata = {
          '0': '停止',
          '1': '启动'
        }
        let data = res.data.records.map(item => {
          return {
            ...item,
            status: item.status ? resdata[item.status] : resdata[item.status],
            loading: false,
            loading1: false,
          }
        })
        this.tableData = data

      })
    },
    // 数据采集1
    pullList () {
      let list = {
        type: 1,
        scalePlatformResourceName: this.datainput,
        dataSourcesUrl: this.datainput,
        pageParam: this.tablePageParam2
      }
      datapushList(list).then(res => {
        let resdata = {
          '0': '停止',
          '1': '启动'
        }
        let data = res.data.records.map(item => {
          return {
            ...item,
            status: item.status ? resdata[item.status] : resdata[item.status],
            loading: false,
            loading1: false,
          }
        })
        this.tableData2 = data
        this.tablePageParam2.pagetotal = res.data.total
      })
    }
  },

  mounted () {
    this.pushList()
    this.pullList()
  }
}
</script>

<style lang="less" scoped>
.p-finish {
  text-align: center;
}
input {
  margin: 0 0 10px;
}
// button.btn {
//   position: absolute;
//   right: 10px;
//   margin: 0 10px;
// }
.bt1 {
  margin-right: 10px;
}
.search-title {
  font-size: 12px;
}
.title {
  font-weight: bold;
  font-size: 16px;
}
.bs {
  text-indent: 10px;
  line-height: 15px;
  border-left: 5px solid #3d73ef;
  margin-bottom: 15px;
}
.contract-card {
  span {
    i {
      vertical-align: -0.15em;
    }
  }
}

/deep/.ivu-modal-footer {
  /* border-top: 1px solid #e8eaec; */
  /* padding: 12px 18px 12px 18px; */
  text-align: right;
  //background-color: #F5F6FA;
}
.contractApp {
  /deep/.ivu-tabs {
    min-height: calc(100vh - 208px);
  }
  button.btn {
    position: absolute;
    right: 10px;
  }
  .basetext {
    span {
      text-align: left;
      margin: 0 30px;
      line-height: 30px;
    }
  }
}
/deep/.ivu-modal > .ivu-modal-content > .ivu-modal-body {
  max-height: 45vh;
  overflow: auto;
}
// /deep/.ivu-card{background: #f2f6fd;}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
// /deep/.ivu-collapse{border: none;}
/deep/.ivu-collapse > .ivu-collapse-item {
  border-radius: 5px 5px;
  margin-bottom: 10px;
}

/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.ivu-scroll-container {
  height: auto;
  overflow-y: auto;
}
