
// 预言机路由

import Layout from '@/views/layout.vue'

import aicon from '/src/assets/prophecyIcon/tyu (4).png'
import bicon from '/src/assets/prophecyIcon/tyu (5).png'
import cicon from '/src/assets/prophecyIcon/tyu (7).png'
import eicon from '/src/assets/prophecyIcon/tyu (6).png'
import ficon from '/src/assets/prophecyIcon/tyu (1).png'
import gicon from '/src/assets/prophecyIcon/tyu (2).png'
import hicon from '/src/assets/prophecyIcon/tyu (3).png'
import licon from '/src/assets/prophecyIcon/tyu(9).png'

export const prophecyManagementRouters = [
    {
        path: '/prophecymanagement',
        name: 'prophecymanagement',
        component: Layout,
        selfIcon: aicon,
        selfIconb: aicon,
        meta: {
            title: '预言机业务管理',
            icon: aicon,
        },
        children: [
            {
              path: 'consumeruser',
              name: 'consumeruser',
              selfIcon: bicon,
              selfIconb: bicon,
                meta: {
                    title: '消费者用户管理',
                    icon: bicon,
                    routerType: 'leftmenu'
                },
                component: () => import('@/views/prophecyManagement/consumerUser'),
            },
            {
                path: 'sourcemanagement',
              name: 'sourcemanagement',
              selfIcon: cicon,
              selfIconb: cicon,
                meta: {
                    title: '信源管理',
                    icon: cicon,
                    routerType: 'leftmenu'
                },
                component: () => import('@/views/prophecyManagement/sourceManagement'),
            },
            {
                path: 'prophecytemplate',
              name: 'prophecytemplate',
              selfIcon: eicon,
              selfIconb: eicon,
                meta: {
                    title: '预言机模板',
                    icon: eicon,
                    routerType: 'leftmenu'
                },
                component: () => import('@/views/prophecyManagement/prophecyTemplate'),
            },
            {
                path: 'prophecylist',
              name: 'prophecylist',
              selfIcon: ficon,
              selfIconb: ficon,
                meta: {
                    title: '预言机列表',
                    icon: ficon,
                    routerType: 'leftmenu'
                },
                component: () => import('@/views/prophecyManagement/prophecyList'),
            },
            {
                path: 'transactionrecord',
              name: 'transactionrecord',
              selfIcon: gicon,
              selfIconb: gicon,
                meta: {
                    title: '交易记录',
                    icon: gicon,
                    routerType: 'leftmenu'
                },
                component: () => import('@/views/prophecyManagement/transactionRecord'),
            },
            {
                path: 'requestrecord',
              name: 'requestrecord',
              selfIcon: licon,
              selfIconb: licon,
                meta: {
                    title: '请求记录',
                    icon: licon,
                    routerType: 'leftmenu'
                },
                component: () => import('@/views/prophecyManagement/requestRecord'),
            },
            {
                path: 'exceptionlog',
              name: 'exceptionlog',
              selfIcon: hicon,
              selfIconb: hicon,
                meta: {
                    title: '异常日志',
                    icon: hicon,
                    routerType: 'leftmenu'
                },
                component: () => import('@/views/prophecyManagement/exceptionLog'),
            }
        ],

    }

]


