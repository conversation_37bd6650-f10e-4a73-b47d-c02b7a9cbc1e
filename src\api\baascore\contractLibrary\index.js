import axios from '../../index'
// const BASEURL = ''
const BASEURL = '/cmbaas/portal/fabric/CommonAPI'
const FormDATABASEURL = '/cmbaas/portal/fabric/CommonFormAPI'
// 已上传的合约信息列表
export function getPlatChaincodeList (query) {
  return axios.request({
    url: BASEURL + '?msgType=platChaincode%23getPlatChaincodeList',
    method: 'get',
    params: query
  })
}
// 获取可发布的区块链
export function getFilterChainList (obj) {
  let data = {
    msgType: 'platChaincode#getFilterChainListForDeployCC',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
// 执行发布
export function issuePlatChaincode (obj) {
  let data = {
    msgType: 'platChaincode#issuePlatChaincode',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}

// 合约删除
export function deletePlatChaincode (obj) {
  let data = {
    msgType: 'platChaincode#deletePlatChaincode',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}

// 上传合约
export function uploadPlatChaincode (obj) {
  return axios.request({
    url: FormDATABASEURL,
    method: 'post',
    data: obj
  })
}
// 获取合约源码
export function chaincodeFunc (obj) {
  let data = {
    msgType: 'chaincode#chaincodeFunc',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
export function loginByUsername (username, usertime, password) {
  const data = {
    userName: username,
    usertime,
    password: password
  }
  return axios.request({
    url: '/baasapi/auth/jwt/moptoken',
    method: 'post',
    data
  })
}
// 检查合约名是否已存在
export function checkChainCodeName (query) {
  return axios.request({
    url: BASEURL + '?msgType=platChaincode%23checkChainCodeName',
    method: 'get',
    params: query
  })
}
