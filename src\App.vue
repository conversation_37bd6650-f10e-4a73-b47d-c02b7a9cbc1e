<template>
  <div id="app">
    <router-view></router-view>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'
import { getLogsave } from './api/contract'
function sleep (ms) {
  const start = Date.now(); // 获取当前时间
  while (Date.now() - start < ms) {
    // 循环直到经过的时间大于 ms
  }
}
export default {
  data () {
    return {
      //
      isPageVisible: true,
      isReload: false,
    }
  },
  watch: {
    $route (newRoute) {
      this.UPDATE_ROUTER(newRoute)
    }
  },
  // created () {
  //   sessionStorage.setItem('isRefreshed', 'true')
  //   // //l当页面刷新时
  //   window, addEventListener('beforeunload', this.handleBeforeUnload);
  // },
  methods: {
    ...mapMutations(['UPDATE_ROUTER']),
    // 页面刷新/关闭的处理函数
    // onBeforeUnload (event) {
    //   // console.log('beforeunload 触发');
    //   // 标记即将卸载，防止刷新时触发USER_SWITCH_TAB上报
    //   window._isUnloading = true;
    //   let lastPage = sessionStorage.getItem('fromName')
    //   let currentPage = sessionStorage.getItem('toName')
    //   let operation = {
    //     "lastPage": '',
    //     "currentPage": currentPage,
    //     "operateType": 'USER_STOP_TAB'
    //   }
    //   getLogsave(operation).then((res) => {
    //     sessionStorage.setItem('fromName', currentPage)
    //   })
    //   // 使用 sendBeacon 进行日志上报，保证刷新/关闭时数据能发送
    //   // navigator.sendBeacon(
    //   //   '/cmbaas/portal/v2/page/operate/log/save',
    //   //   new Blob([JSON.stringify(operation)], { type: 'application/json' })
    //   //   // JSON.stringify(operation)
    //   // );
    //   // 不要设置 event.returnValue，否则部分浏览器会弹窗且可能阻断 sendBeacon
    // },
    // 切换标签页/窗口最小化的处理函数
    onVisibilityChange () {
      // 如果是刷新/关闭导致的 visibilitychange，不上报 USER_SWITCH_TAB
      if (window._isUnloading) return;
      if (document.hidden) {
        console.log('切走');
        this.isPageVisible = false;
        // 切换到其他标签页或窗口时上报
        let lastPage = sessionStorage.getItem('fromName')
        let currentPage = sessionStorage.getItem('toName')
        let islast = (lastPage == '登录' && currentPage == 'Dashboard') || (lastPage == 'undefined') ? '' : lastPage
        let operation = {
          "lastPage": 'USER_SWITCH_TAB' ? '' : islast,
          "currentPage": currentPage,
          "operateType": 'USER_SWITCH_TAB'
        }
        if (operation.currentPage != '') {
          getLogsave(operation).then((res) => {
            sessionStorage.setItem('fromName', currentPage)
          })
        }

      } else {
        this.isPageVisible = true;
        console.log('切回');
        // 回到当前标签页时上报
        let currentPage = sessionStorage.getItem('toName')
        console.log(currentPage);
        const currentData = ['中移链生产运维总览视图', '中移链生产运行驾驶舱', '中移链运营总览视图']
        if (!currentData.includes(currentPage)) {
          let operation = {
            "lastPage": '',
            "currentPage": currentPage,
            "operateType": 'NORMAL_VIEW'
          }
          if (operation.currentPage != '') {
            getLogsave(operation).then((res) => {
              // sessionStorage.setItem('fromName', currentPage)
            })
          }

        }

      }
    },
  },
  destroyed () {
    //页面关闭时清理 sessionStorage
    sessionStorage.removeItem('isRefreshed');
  },
  beforeDestroy () {
    // window.removeEventListener('beforeunload');
    // sessionStorage.removeItem('isRefreshed');
    // 在组件销毁时移除事件监听
    // document.removeEventListener('visibilitychange', (event) => this.handleVisibilityChange(event));
  },
  mounted () {
    // 调试：确认 mounted 是否执行
    console.log('mounted 执行');
    let _this = this
    // 调用示例alert(data,type,width);data必填，type，width,传值就取对应的值，不传值取默认值
    function selfAlert (msg, type = 'error', width = '520px') {
      const titleMsg = `<h3>提示</h3>`
      const contentMsg = `<div class="diaContent">${msg}</div>`
      _this.$Modal[type]({
        width,
        title: titleMsg,
        content: contentMsg
      })
    }
    // 重写覆盖原生的alert，保持组件风格一致
    window.alert = selfAlert

    // 保存回调引用，便于移除监听
    // this._onBeforeUnload = this.onBeforeUnload.bind(this)
    this._onVisibilityChange = this.onVisibilityChange.bind(this)
    // 分别注册事件监听
    window.addEventListener('beforeunload', () => {
      // setTimeout(() => {

      // }, 500);
      window._isUnloading = true;
      let currentPage = sessionStorage.getItem('toName')
      let operation = {
        "lastPage": '',
        "currentPage": currentPage,
        "operateType": 'USER_STOP_TAB'
      }
      if (operation.currentPage != '') {
        getLogsave(operation).then((res) => {
          sessionStorage.setItem('fromName', currentPage)
        })
      }
      // alert('卸载')
      sleep(1000);
    })
    // window.addEventListener('beforeunload', this._onBeforeUnload)
    document.addEventListener('visibilitychange', this._onVisibilityChange)
    // 调试：确认事件监听已注册
    // console.log('beforeunload 事件监听已注册');
  },


}
</script>
<style lang="less">
@import "./styles/xjc.less";
</style>
<style lang="less">
html,
body {
  height: 100%;
}
body {
  margin: 0;
}
#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  // text-align: center;
  color: #2c3e50;
  height: 100%;
}
.diaContent {
  word-wrap: break-word;
}
@font-face {
  font-family: "iconfont";
  src: url("./assets/font/iconfont.eot");
  src: url("./assets/font/iconfont.eot?#iefix") format("embedded-opentype"),
    url("./assets/font/iconfont.woff") format("woff"),
    url("./assets/font/iconfont.ttf") format("truetype"),
    url("./assets/font/iconfont.svg#iconfont") format("svg");
}
.iconfont {
  font-family: "iconfont" !important;
  // font-size:16px;
  font-size: 14px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}
.iconfont-svg {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
ul,
li {
  list-style: none;
}
/*修改滚动条样式*/
div::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
div::-webkit-scrollbar-track {
  background: rgb(239, 239, 239);
  border-radius: 2px;
}
div::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 10px;
}
div::-webkit-scrollbar-thumb:hover {
  background: #333;
}
div::-webkit-scrollbar-corner {
  background: #179a16;
}
.area_popper.el-popper {
  margin-top: 9px !important;
}
.footer-copyright {
  position: fixed;
  bottom: 0px;
  left: 50%;
  font-size: 12px;
  color: #a5a4bf;
  z-index: 200;
  transform: translate(-50%, -50%);
}
</style>
