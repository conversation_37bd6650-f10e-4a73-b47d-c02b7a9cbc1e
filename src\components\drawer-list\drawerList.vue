<template>
  <div style="height:100%;width:100%">
    <List class="list-style" v-show="approvalList.length>0">
      <ListItem v-for="(item, i) in approvalList" :key="i">
        <ListItemMeta>
          <template slot="avatar">
            <img :class="{opc: item.checked}" :src="approvalImg">
          </template>
          <template slot="title">
            <div :class="item.arrowShow ? 'otherProductItem' : 'otherProductItem1'" ref="abstractDom" @click="clickTo($event, item)" v-html="item.noticeMessage">
              <!-- <p v-html="item.noticeContent"></p> -->
            </div>
            <Icon :type="item.arrowShow === true ? 'ios-arrow-down':'ios-arrow-up'" class="icon" @click="clickOpen(item, i)" v-show="item.isOpen" />
          </template>
          <template slot="description">
            <div style="float:left">{{item.createTime}}</div>
          </template>
        </ListItemMeta>
      </ListItem>
    </List>
    <p style="text-align:center;" v-if="this.count < this.total && this.pages > this.pageParam.pageIndex">
      <span style="font-size:8px;color:#57a3f3;cursor:pointer;" @click="getMore">
        更多<img :src="moreUrl" style="cursor:pointer;margin-left:2px;" @click="getMore">
      </span>
    </p>
    <div class="data-none" v-show="approvalList && approvalList.length === 0">
      <img :src="imagesUrl">
      <p class="title-none" style="">暂时没有收到待办通知</p>
    </div>
  </div>
</template>
<script>
import { noticeManage } from '@/api/data'
import { mapActions } from 'vuex'
export default {
  name: 'drawList',
  props: {
    cur: {
      type: Number,
      default () {
        return 0
      }
    }
  },
  data () {
    return {
      moreUrl: require('@/assets/img/arrow.png'),
      imagesUrl: require('@/assets/img/null.png'),
      approvalImg: require('@/assets/notice/approval.png'),
      single: false,
      showMore: false,
      isOpen: false,
      isShowMoreBtn: false,
      arrowType: 'ios-arrow-down',
      isOpc: false,
      approvalList: [],
      pageParam: {
        pageSize: 8,
        pageIndex: 1
      },
      count: 0,
      total: 0,
      pages: 0,
      showRed: 0
    }
  },
  computed: {
    height () {
      return this.isOpen ? 'auto' : 50 + 'px'
    },
    getHidden () {
      if (this.count < this.total) {
        return ''
      } else {
        return 'display:none'
      }
    }
  },
  methods: {
    ...mapActions([
      'updateCur'
    ]),
    getMore () {
      if (this.pageParam.pageIndex < this.pages) {
        this.pageParam.pageIndex += 1
        this.getWaitDeal(true)
        const scrollDiv = document.getElementById('noticeScroll')
        scrollDiv.scrollTo({
          top: scrollDiv.scrollHeight,
          behavior: 'smooth'
        })
      }
    },
    clickTo (e, item) {
      if (e.target.localName.toLowerCase() === 'span') {
        if (this.routerTo(item)) {
          this.$emit('closeDrawer')
        }
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    clickOpen (item, i) {
      item.arrowShow = !item.arrowShow
      this.$set(this.approvalList, i, item)
    },
    getWaitDeal (flag) {
      noticeManage('WAIT_DEAL', this.pageParam).then(res => {
        if (res.code === '00000') {
          if (flag) {
            this.approvalList.push.apply(this.approvalList, res.data.records)
          } else {
            this.approvalList = res.data.records
          }
          this.count = this.approvalList.length
          this.total = res.data.total
          this.pages = res.data.pages
          this.showRed = this.count > 0 ? this.approvalList[0].showRed : 0
          this.$emit('getWaitDeal', this.showRed)
          this.$nextTick(() => {
            for (var item in this.approvalList) {
              let offsetHeight = this.$refs.abstractDom[item].offsetHeight
              if (offsetHeight > 50) {
                this.approvalList[item].arrowShow = true
                this.approvalList[item].isOpen = true
              } else {
                if (this.approvalList[item].isOpen === undefined) {
                  this.approvalList[item].isOpen = false
                }
              }
              this.$set(this.approvalList, item, this.approvalList[item])
            }
          })
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    routerTo (item) {
      // 链账户审批
      if (item.bizType === 'ACCOUNT_AUDIT') {
        this.$router.push({
          name: 'chain-approvel'
        })
        // 合约部署审批
      } else if (item.bizType === 'DEPLOY_AUDIT') {
        if (this.$route.path === '/contract-approvel') {
          this.$router.go(0)
        } else {
          this.$router.push({
            name: 'contract-approvel',
            params: {
              tabs: 'name1'
            }
          })
        }

        // 工单审批
      } else if (item.bizType === 'ORDER_AUDIT') {
        this.$router.push({
          name: 'workorder-approvel'
        })
        // 密码过期
      } else if (item.bizType === 'PASSWORD_EXPIRE') {
        this.updateCur(1)
        this.$router.push({
          name: 'user_info',
          params: {
            cur: 1
          }
        })
      } else if (item.bizType === 'SHARE_AUDIT') {
        // console.log('item:', item)
        this.$router.push({
          name: 'sharecontract-approvel',
          params: {
            tabs: 'name1'
          }
        })
        // 链账户资源管理
      } else if (item.bizType === 'ACCOUNT_UNASSIGNED') {
        // console.log('item:', item)
        this.$router.push({
          name: 'token_admin'
        })
        // 个人中心待办
      } else if (item.bizType === 'CONTRACT_APP_ON' || item.bizType === 'MARKET_AUDIT' || item.bizType === 'MARKET_AUDIT_OFF' || item.bizType === 'MARKET_AUDIT_REON') {
        this.$router.push({
          name: 'shelves_approval'
        })
      } else {
        this.msgInfo('warning', item.bizType + '没找到匹配路由', true)
        return false
      }
      return true
    },
    initPageParam () {
      this.pageParam = {
        pageSize: 8,
        pageIndex: 1
      }
      this.count = 0
      this.total = 0
      this.pages = 0
    }
  },
  mounted () {
    this.getWaitDeal()
  },
  watch: {
    cur: {
      handler (newVal, oldVal) {
        this.cur = newVal
      },
      deep: true,
      immediate: false
    }
  }
}
</script>

<style lang="less" scoped>
.opc {
  opacity: 0.5;
}
img {
  margin: 0 auto;
}
.otherProductItem {
  cursor: default;
  width: 260px;
  line-height: 25px;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  padding-right: 9px;
  margin-top: -5px;
  margin-left: -5px;
}
.otherProductItem1 {
  cursor: default;
  width: 260px;
  line-height: 25px;
  padding-right: 8px;
  margin-top: -5px;
  margin-left: -5px;
}
.icon {
  position: absolute;
  width: 32px;
  right: 6px;
  margin-top: -15px;
}
.data-none {
  position: relative;
  text-align: center;
  vertical-align: middle;
  margin: 0 auto;
  padding-top: 50px;
  .title-none {
    font-size: 8px;
    color: #bdbbbb;
  }
}
.list-style {
  max-height: calc(100vh - 140px);
  overflow-y: auto;
  padding-top: 10px;
}
</style>
