<template>
  <div class="subrecord_index">
    <keep-alive v-if="currentTab === 'application_admin'">
      <SubrecordTable />
    </keep-alive>
    <router-view />
  </div>
</template>

<script>
import SubrecordTable from './home.vue'
export default {
  name: 'application_admin',
  components: {
    SubrecordTable
  },
  data () {
    return {
      excludeArr: ['application_admin']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () { }
}
</script>
