body{
  font-size: 14px;
}

#app {
  .nan-item {
    margin: 0 !important;
  }

  .nan-item .nav-box {
    margin: 0 !important;
  }

  .nav-box {
    margin-bottom: 0;
    line-height: 1;
  }

  .nan-item .nav-box:hover {
    background: #EBF7FF !important;
    box-shadow: none !important;
    border-left: none;
    border-right: none;
    transition: all 300ms;
    //border-color: transparent !important;
  }

  .nav-box.el-row {
    border-top: 1PX solid #E9EBEF;
    border-bottom: 1PX solid #E9EBEF;
    border-top-color: transparent;
  }

  .el-row {
    min-height: 46px;
    //&:nth-child(even){
    //  background: #F8F8F9;
    //}
    display: flex;
    align-items: center;
    //position: static;
    @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
      &:after {//IE下兼容性ali和min不能同时
        content: '';
        min-height: inherit;
        font-size: 0;
      }
    }

  }
  .tissue-table:nth-child(odd) .nav-box {
    background: #FBFBFD;
  }
  .tissue-table:nth-child(even) .nav-box {
    background: #FCFDFD;
  }
  .table-wrapper{
    div.nan-item:nth-child(odd){
      .nav-box {
        background: #FBFBFD;
      }
    }
    div.nan-item:nth-child(even){
      .nav-box {
        background: #FCFDFD;
      }
    }
  }
  .table-wrapper:not(.node-list-nav) {
    position: relative;
    //margin-top: 30px;
    background: #fff;
    >div.nan-item{
      background: #FBFBFD;
      color: red;
    }
    //&:before {
    //  content: '';
    //  width: 100%;
    //  height: 1.5px;
    //  position: absolute;
    //  left: 0;
    //  bottom: 0;
    //  background-color: #dcdee2;
    //  z-index: 4;
    //}
  }

  .table-wrapper.node-list-nav > div .operate{
    .image {
      display: inline-block;
      width: 17px;
      height: 17px;
     // background-image: url('~@/assets/image/operate.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-position: center 1PX;
      &:hover {
        cursor: pointer;
    //    background-image: url('~@/assets/image/operate-blue.png');
      }
      &.active {
        cursor: pointer;
        //background-image: url('~@/assets/image/operate-blue.png');
      }
    }
    > div {
      //padding-left: 30px;
    }
  }

  .table-wrapper > div:first-child { //表格标题
    background: #FBFBFD;
    display: flex;
    align-items: center;

    &.el-row{
      height: 40px!important;
      min-height: auto;
    }
    div {
      // font-size: 17px;
      font-size: 14PX;
      color: #515a6e;
      font-weight: 700;
    }

  }

  .table-wrapper > div:not(:first-child) {
    color: #333;

  }


  .el-col {
    text-align: left !important;
    padding-left: 18px !important;
    color: #515a6e;
    // font-size: 17px; //表格内容
    font-size: 14PX;
    span {
      word-break: keep-all;
      word-wrap: break-word;
    }
  }

}

/*改表格样式结束*/
/*修改全部提醒*/
.el-message {
  justify-content: left;
  padding: 14px 14px!important;
  min-width: 120px!important;
}

.el-message__content {
  color: #333 !important;
}


.el-message__icon.el-icon-success {
  color: #10C038;
}

.el-message--success {
  background: #EDF8E8;
  border-color: #BCE4C5;
}


.el-message__icon.el-icon-error {
  color: #F04134;
}

.el-message--error {
  background: #FFEFE6;
  border-color: #FFA29F !important;
}


.el-message__icon.el-icon-info {
  color: #337DFF;
}

.el-message--info {
  background: #E1ECFF;
  border-color: #79A9FF;
}


.el-message__icon.el-icon-warning {
  color: #FF931D;
}

.el-message--warning {
  background: #FEF9ED;
  border-color: #FDDBB9;
}
.btn-after {
  box-shadow: none;
  &:hover {
    &::after{
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-radius: 3px;
      background-color: rgba(255,255,255,0.1);
    }
  }
  &:active {
    &::after{
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-radius: 3px;
      background-color: rgba(0,0,0,0.1);
    }
  }
}

/*表格里的操作按钮*/
.handle-btn {
  display: inline-block;
  font-family: PingFangSC-Regular;
  border:1PX solid #3D73EF;
  // font-size: 17px;
  font-size: 14px;
  color: #3D73EF;
  letter-spacing: 0;
  text-align: justify;
  height: 24px;
  line-height: 22px;
  padding: 0 8px;
  margin-right: 5px;
  cursor: pointer;
  border-radius: 3px;
  position: relative;
  &.delete-btn {
    border:1PX solid #FA5151;
    color: #FA5151;
    &:hover,&:active {
      background: #FA5151;
      color: #fff;
    }
  }
  &:hover {
    background: #3D73EF;
    color: #fff;
    &::after{
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-radius: 3px;
      background-color: rgba(255,255,255,0.1);
    }
  }

  &:active {
    background: #3D73EF;
    color: #fff;
    &::after{
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-radius: 3px;
      background-color: rgba(0,0,0,0.1);
    }
  }

  &.disable {
    cursor: default;
    background:#F0F0F0;
    border:1PX solid #E5E5E5;
    color: #C7C7C7;
    pointer-events: none;
  }
}

/*按钮*/
#app .el-button {
  border-radius: 4px;
  min-width: 90px;
  // height: 48px;
  // font-size: 17px!important;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
  font-family: Microsoft YaHei!important;
  padding: 0 15px;
}
#app .green-btn {
  position: relative;
  background-color: #19be6b;
  border-color: #19be6b;
  .btn-after()
  // &:hover{
  //   background-color: #47cb89!important;
  //   border-color: #47cb89;
  // }
  // &:focus{
  //   box-shadow: 0 0 0 2px rgba(25 ,190 ,107 ,0.2);
  // }
}
#app .el-button--small {
  // height: 42px;
  // padding: 0 20px;
  height: 24px;
  padding: 0 7px;
}

.el-button.is-active, .el-button.is-plain:active {
  background: #E1ECFF;
}

.btn-lf {
  margin-left: 15px;
}

.btn-rt {
  margin-right: 15px;
}

.btn-bt {
  margin-bottom: 26px;
}

/*标签*/
.tag {
  display: inline-block;
  min-width: 76px;
  padding: 0 6px;
  height: 24px;
  // font-size: 17px;
  font-size: 14px;
  line-height: 24px;
  border-radius: 2px;
  text-align: center;
  color: #fff;
  &.blue {
    //color: #4488FF;
    color: #3D73EF;
    background: #E1ECFF;
    border: 1PX solid #3D73EF;
  }

  &.green {
    color: #15AD31;
    background: #EDF8E8;
    border: 1PX solid #15AD31;
  }

  &.yellow {
    color: #FFC300;
    background: #FEF9ED;
    border: 1PX solid #FFC300;
  }

  &.red {
    color: #FA5151;
    background: #FFF2F1;
    border: 1PX solid #FA5151;
  }

  &.gray {
    color: #576479;
    background: #E9EDF2;
    border: 1PX solid #75849B;
  }
}

//表格无数据样式重写
.none {
  border-bottom: 1.5px solid #DCDEE2;
  height: 47px !important;
  line-height: 47px !important;
  // font-size: 17px !important;
  font-size: 14px;
  color: #150000 !important;
  .svg-icon{
    margin-right: 4PX;
  }
  .el-icon-loading{
    margin-right: 2PX;
  }
}

/*.el-input__inner {
  border: 1PX solid #DCDFE6 !important;
}*/
/*.el-textarea__inner {
  border: 1PX solid #DCDFE6;
}
.el-checkbox__inner {
  border: 1PX solid #DCDFE6;
}
.el-checkbox__inner {
  border: 1PX solid #DCDFE6;
}*/
// .el-checkbox__inner::after {
//   border: 1PX solid #FFF;
// }
.el-checkbox__inner::after {
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  content: "";
  border: 1PX solid #FFF;
  border-left: 0;
  border-top: 0;
  height:5PX;
  left:2PX;
  position: absolute;
  top: 0PX;
  -webkit-transform: rotate(
45deg
) scaleY(0);
  transform: rotate(
45deg
) scaleY(0);
  width: 3PX;
  -webkit-transition: -webkit-transform .15s ease-in .05s;
  transition: -webkit-transform .15s ease-in .05s;
  transition: transform .15s ease-in .05s;
  transition: transform .15s ease-in .05s, -webkit-transform .15s ease-in .05s;
  transition: transform .15s ease-in .05s,-webkit-transform .15s ease-in .05s;
  -webkit-transform-origin: center;
  transform-origin: center;
}
.el-checkbox__input.is-checked .el-checkbox__inner::after {
  -webkit-transform: rotate(
45deg
) scaleY(1);
  /* transform: rotate(
45deg
) scaleY(1); */
}



.el-popover{
  background: #555B68!important;
  color: #fff!important;
  padding:8px 10px!important;

}
.popper__arrow{
  border-top-color: #515151!important;
  &::after{
    border-top-color: #515151!important;
  }
}
