
pcre_jit on;



# error_log  logs/error.log;
# error_log  logs/error.log  notice;
# error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    # worker_connections  1024;
    worker_connections  100000;
}


http {
    large_client_header_buffers 7 512k;
    client_max_body_size 50M;
    include       mime.types;
    default_type  application/octet-stream;

    server_tokens off;
    more_set_headers 'Server: CMBaaSWebEngine';



    client_body_temp_path /var/run/openresty/nginx-client-body;
    proxy_temp_path       /var/run/openresty/nginx-proxy;
    fastcgi_temp_path     /var/run/openresty/nginx-fastcgi;
    uwsgi_temp_path       /var/run/openresty/nginx-uwsgi;
    scgi_temp_path        /var/run/openresty/nginx-scgi;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;

    server {
        listen       80;
        listen  [::]:80;
        server_name  localhost;
        add_header Content-Security-Policy "default-src *;style-src 'self' 'unsafe-inline';script-src 'self' 'unsafe-inline' 'unsafe-eval'; img-src * data: blob:;worker-src * blob:;font-src 'self' data:;";
        add_header X-Content-Type-Options "nosniff";
        add_header X-XSS-Protection "1";
        add_header X-Frame-Options SAMEORIGIN;
        
            # 设置发送超时时间

        #error_page 404 /index.html
        #charset koi8-r;
        #access_log  /var/log/nginx/host.access.log  main;
        location / {
            root  /usr/share/nginx/html/;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
            proxy_set_header Authorization $http_authorization;
            proxy_pass_header Authorization;
            limit_rate  512k;  #对每个连接的限速为2k/s
        }

        location ^~/cmbaas/ {
          proxy_send_timeout 600s;
          proxy_connect_timeout 600s;
          proxy_read_timeout 600s;
          # add_header Access-Control-Allow-Origin http://**********:9001;
          # add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";
          # add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
          #  add_header 'Access-Control-Allow-Origin' '';
           proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Authorization $http_authorization;
            proxy_pass http://**********:19101;

            #proxy_pass_request_headers on;
            #proxy_set_header Host $http_host;
            #proxy_pass_header Authorization;
            #more_set_input_headers 'Authorization: $http_Authorization';
        }

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root  /usr/share/nginx/html/;
        }


    }


    # Don't reveal OpenResty version to clients.
    # server_tokens off;
}