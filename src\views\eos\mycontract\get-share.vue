<template>
  <div class="contract">
    <p style="text-align:right;">
      <!-- <b style="float:left;margin: 10px;">智能合约</b> -->
      <Input style="width:250px;vertical-align:baseline;" placeholder="可输入合约名称或应用名称查询" v-model="queryName" @keyup.enter="searchList" @keyup.enter.native="searchList">
      <Icon type="ios-search" slot="suffix" @click="searchList" />
      </Input>
      <!-- <Button type="primary" @click="searchList" icon="ios-search">查询</Button> -->
    </p>
    <edit-table-mul style="margin: 10px 0;" :key="transferKey" :columns="columns" v-model="tableData"></edit-table-mul>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;" />

  </div>
</template>

<script>
import { shareList, unbound } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
export default {
  name: 'my_share',
  components: {
    EditTableMul
  },
  data () {
    return {
      transferKey: 0,
      transId: '', // 去详情时的contractId
      chainId: this.$route.params.chainId ? this.$route.params.chainId : '',
      queryName: '',
      tablePageParam: { pagetotal: 0, pageSize: 10, pageIndex: 1 },
      tableData: [],
      columns: [
        { key: 'contractName', title: '合约名称', tooltip: true },
        { key: 'contractReadableName', title: '应用名称', tooltip: true },
        { key: 'contractBrief', title: '应用简介', tooltip: true },
        {
          key: 'contractAccountName', title: '合约链账户', tooltip: true
        },
        {
          key: 'chainName', title: '链名称', tooltip: true
        },
        {
          key: 'shareTenantName', title: '发布租户', tooltip: true
        },
        { // key: 'action',
          title: '操作',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: { click: () => { this.showDetails(params.index) } }
              }, '详情'),
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: { click: () => { this.fireContract(params.index) } }
              }, '解绑')
            ])
          }
        }
      ]
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    async okRequest (shareRecordId, receivedTenantId) {
      // 调解除合约绑定的接口(现接口是错的，以后替换接口就好)
      // unbound(shareRecordId, receivedTenantId).then(res => {
      //   if (res.code !== '00000') {
      //     this.msgInfo('error', res.message, true)
      //   } else {
      //     this.init()
      //     this.msgInfo('success', res.message, true)
      //   }
      // }).catch(error => {
      //   console.log('getContractPower.error===>', error)
      //   // this.msgInfo('error', error.message, true)
      // })
    },
    fireContract (index) {
      this.$Modal.confirm({
        content: `确定解除与${this.tableData[index].contractName}合约的绑定吗？解除后将无法调用合约功能`,
        onOk: () => {
          this.okRequest(this.tableData[index].shareRecordId, this.tableData[index].receivedTenantId)
        },
        onCancel: () => {
          // this.$Message.info('Clicked cancel')
        }
      })
    },
    init () {
      this.tablePageParam = {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      }
      this.getTableData()
    },
    getTableData () {
      const params = {
        queryName: this.queryName,
        queryType: 'RECEIVED_CONTRACT',
        pageParam: this.tablePageParam
      }
      // shareList(params).then(res => {
      //   // console.log('shareList===>', res)
      //   if (res.code !== '00000') {
      //     if (res.code === '500') {
      //       this.msgInfo('error', res.message, true)
      //     } else {
      //       this.msgInfo('warning', res.message, true)
      //     }
      //   } else {
      //     this.tableData = res.data.records
      //     this.tablePageParam = {
      //       pagetotal: res.data.total,
      //       pageSize: res.data.size,
      //       pageIndex: res.data.current
      //     }
      //   }
      //   ++this.transferKey
      // }).catch(error => {
      //   console.log('shareList.error===>', error)
      //   this.msgInfo('error', error.message, true)
      // })
    },
    searchList () { this.getTableData() },
    showDetails (index) {
      this.$router.push({
        name: 'sharecontract_details',
        params: { shareRecordId: `${this.tableData[index].shareRecordId}`, receivedTenantId: `${this.tableData[index].receivedTenantId}`, languageType: `${this.tableData[index].languageType}` }
      })
    }
  },
  mounted () {
    this.getTableData()
  },
  deactivated () {
  },
  activated () {
    this.getTableData()
  }
}
</script>

<style lang="less" scoped>
.result-class {
  text-align: center;
  .result-title {
    height: 41px;
    font-size: 24px;
    font-weight: bold;
    line-height: 31px;
    color: #333333;
    opacity: 1;
  }
  .result-content {
    height: 31px;
    font-size: 16px;
    font-weight: 400;
    line-height: 23px;
    color: #9b9b9b;
    opacity: 1;
  }
}
.eg-class {
  p {
    height: 31px;
    line-height: 31px;
  }
}
.contract {
  button.btn {
    position: absolute;
    right: 10px;
  }
  .basetext {
    span {
      text-align: left;
      margin: 0 30px;
      line-height: 30px;
    }
  }
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
</style>
