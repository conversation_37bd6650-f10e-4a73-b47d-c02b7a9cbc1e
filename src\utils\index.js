export var forbidden = {
  data () {
    return {
    }
  },
  created () {

  },
  methods: {
    forbidden (totalPage = 1, pageNumber = 1) {
      if (totalPage == 1) {
        // 只有1页数据首页尾页禁用
        this.btnStartDisabled = true
        this.btnEndDisabled = true
      } else if (pageNumber == 1) {
        // 点击第一页首页禁用
        this.btnStartDisabled = true
        this.btnEndDisabled = false
      } else if (totalPage == pageNumber) {
        // 点击最后一页尾页禁用
        this.btnStartDisabled = false
        this.btnEndDisabled = true
      } else {
        this.btnStartDisabled = false
        this.btnEndDisabled = false
      }
    }
  }
}
// 合约库验证版本号vx.x.x格式
export function version (val) {
  const version = /^([vV]([0-9]{1,2}))(\.[0-9]{1,2}){2}$/
  return version.test(val)
}
export function base64_encode (e) {
  var _keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
  var t = ''
  var n, r, i, s, o, u, a
  var f = 0
  e = _utf8_encode(e)
  while (f < e.length) {
    n = e.charCodeAt(f++)
    r = e.charCodeAt(f++)
    i = e.charCodeAt(f++)
    s = n >> 2
    o = (n & 3) << 4 | r >> 4
    u = (r & 15) << 2 | i >> 6
    a = i & 63
    if (isNaN(r)) {
      u = a = 64
    } else if (isNaN(i)) {
      a = 64
    }
    t = t + _keyStr.charAt(s) + _keyStr.charAt(o) + _keyStr.charAt(u) + _keyStr.charAt(a)
  }
  return t
}
export function _utf8_encode (e) {
  e = e.replace(/rn/g, 'n')
  var t = ''
  for (var n = 0; n < e.length; n++) {
    var r = e.charCodeAt(n)
    if (r < 128) {
      t += String.fromCharCode(r)
    } else if (r > 127 && r < 2048) {
      t += String.fromCharCode(r >> 6 | 192)
      t += String.fromCharCode(r & 63 | 128)
    } else {
      t += String.fromCharCode(r >> 12 | 224)
      t += String.fromCharCode(r >> 6 & 63 | 128)
      t += String.fromCharCode(r & 63 | 128)
    }
  }
  return t
}
