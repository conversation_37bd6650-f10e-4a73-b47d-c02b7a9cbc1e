<template>
  <div class="polylineBox">
      <div ref="polyline" class="polyline" :style="{width:polylineWidth,height: polylineHeight}">
      </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
const option = {
  title: [{
    text: '',
    x: 'center',
    y: 'bottom',
    padding: 25,
    textAlign: 'center',
    textStyle: {
      fontSize: 12,
      fontStyle: 'normal',
      fontWeight: 'normal'
    }
  },
  {
    text: '',
    x: 'left',
    y: 'center',
    textAlign: 'center',
    padding: 10,
    textStyle: {
      fontSize: 12,
      fontStyle: 'normal',
      fontWeight: 'normal'
    }
  },
  {
    text: '',
    x: 'left',
    y: 'top',
    textAlign: 'left',
    textStyle: {
      fontSize: 12,
      color: '#3c4858'
    }
  }],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
      crossStyle: {
        color: '#000'
      },
      lineStyle: {
        type: 'dashed'
      }

    },
    textStyle: {
      align: 'left'
    }
  },
  legend: {
    orient: 'vertical',
    y: 'top',
    left: 'right',
    align: 'left',
    top: '26vh',
    itemWidth: 10,
    itemHeight: 10,
    padding: [20, 10, 0, 0],
    textStyle: { fontSize: '65%', lineHeight: 10 }
  },
  grid: {
    left: '5%',
    right: '14%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: true,
    axisTick: {
      alignWithLabel: true
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false,
      onZero: false
    }
  },
  animation: true,
  animationDelay: 100,
  animationThreshold: 250,
  animationDuration: 1000,
  animationDurationUpdate: 1000,
  animationEasing: 'ExponentialOut',
  // animationEasingUpdate: 'ExponentialOut',
  hoverLayerThreshold: 2000,
  hoverAnimationDuration: 2000
}
export default {
  name: 'Polylines',
  props: {
    polylineData: {
      type: Array,
      default () {
        return []
      }
    },
    polylineHeight: {
      type: String,
      default: '360px'
    },
    polylineWidth: {
      type: String,
      default: '570px'
    },
    polylineXaxis: {
      type: Array,
      default () {
        return []
      }
    },
    polylinexTitle: {
      type: String,
      default: '时间'
    },
    polylineyTitle: {
      type: String,
      default: '调用量/次'
    },
    polylineTitle: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default () {
        return []
      }
    }
  },
  data () {
    return {
    }
  },
  methods: {
    initPolylineData () {
      option.title[0].text = this.polylinexTitle
      option.title[1].text = this.polylineyTitle.replace(/\s/g, '').replace(/(.{1})/g, '$1\n')
      option.title[2].text = this.polylineTitle
      option.series = null
      option.series = this.polylineData
      option.xAxis.data = this.polylineXaxis
      option.legend.data = this.legendData
      this.mychart.setOption(option, true)
      window.addEventListener('resize', () => {
        this.mychart.resize()
      })
    }
  },
  mounted () {
    this.mychart = echarts.init(this.$refs.polyline)
    this.initPolylineData()
  },
  watch: {
    polylineData: {
      handler (newVal, oldVal) {
        this.polylineData = newVal
        this.initPolylineData()
      },
      deep: true,
      immediate: false
    },
    polylineXaxis: {
      handler (newVal, oldVal) {
        this.polylineXaxis = newVal
        this.initPolylineData()
      },
      deep: true,
      immediate: false
    },
    legendData: {
      handler (newVal, oldVal) {
        this.legendData = newVal
        this.initPolylineData()
      },
      deep: true,
      immediate: false
    }
  }
}
</script>
<style lang="less" scoped>
.polylineBox{
  height:100%;
  width:100%;
  color:rgba(81, 135, 223, 0);
}
</style>
