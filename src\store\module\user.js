import { login, register, authorization, loginInit } from '@/api/user'// authorization, newuser,
import { setToken, localSave } from '@/lib/util'

const state = {
  userLoginId: '',
  rules: {},
  faceUrl: '',
  cur: 0, // 当前的激活状态的东西
  chain: {},
  tablePageParam: JSON.stringify({}),
  tas: 1,
  tabNum: 'first',
  contractFlag: false,
  shareParams: {}
}
const getters = {
  firstLetter: (state) => {
    return state.userLoginId.substr(0, 1)
  },
  userLoginId: (state) => {
    return state.userLoginId
  },
  faceUrl: (state) => {
    return state.faceUrl
  },
  cur: (state) => {
    return state.cur
  },
  chain: (state) => {
    return state.chain
  },
  tablePageParam: (state) => {
    return state.tablePageParam
  },
  tas: (state) => {
    return state.tas
  },
  tabNum: (state) => {
    return state.tabNum
  },
  contractFlag: (state) => {
    return state.contractFlag
  },
  shareParams: (state) => {
    return state.shareParams
  }
}
const mutations = {
  SET_USER_NAME (state, params) {
    state.userLoginId = params
  },
  SET_RULES (state, rules) {
    state.rules = rules
  },
  SET_FACEURL (state, params) {
    state.faceUrl = params
  },
  SET_CUR (state, params) {
    state.cur = params
  },
  SET_CHAIN (state, params) {
    state.chain = params
  },
  SET_TABLEPAGEPARAM (state, params) {
    state.tablePageParam = JSON.stringify(params)
  },
  SET_TAS (state, params) {
    state.tas = params
  },
  SET_TABNUM (state, params) {
    state.tabNum = params
  },
  SET_FLAG (state, params) {
    state.contractFlag = params
  },
  SET_SHAREPARAMS (state, params) {
    state.shareParams = params
  }
}
const actions = {
  updateShareParams ({ commit, state }, value) {
    commit('SET_SHAREPARAMS', value)
  },
  updateFlag ({ commit, state }, value) {
    commit('SET_FLAG', value)
  },
  updateUserLoginId ({ commit, state }, value) {
    commit('SET_USER_NAME', value)
  },
  updateCur ({ commit, state }, value) {
    commit('SET_CUR', value)
  },
  updateFaceUrl ({ commit, state }, value) {
    commit('SET_FACEURL', value)
  },
  updateChain ({ commit, state }, value) {
    commit('SET_CHAIN', value)
  },
  updateTablePageParam ({ commit, state }, value) {
    commit('SET_TABLEPAGEPARAM', value)
  },
  updateTas ({ commit, state }, value) {
    commit('SET_TAS', value)
  },
  updateTabNum ({ commit, state }, value) {
    commit('SET_TABNUM', value)
  },
  authorization ({ commit }, roleId) {
    return new Promise((resolve, reject) => {
      authorization(roleId).then(res => {
        if (res.code === '00000') {
          console.log(res.data);
          localSave('MAX_FILE_SIZE', res.data.configJson.MAX_FILE_SIZE)
          localSave('userNameEncrypt', res.data.userNameEncrypt)
          localSave('privateEncrypt', res.data.privateEncrypt)
          localSave('accountEncrypt', res.data.accountEncrypt)
          localSave('sourceCodeEncrypt', res.data.sourceCodeEncrypt)
          localSave('manageAccountInfoEncrypt', res.data.manageAccountInfoEncrypt)//详情
          localSave('upsertManageAccountEncrypt', res.data.upsertManageAccountEncrypt)//新增、编辑
          resolve(res.data.sideMenuJson)
        } else reject(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  login ({ commit }, { userLoginId, password }) {
    return new Promise((resolve, reject) => {
      login({ userLoginId, password }).then(res => {
        if (res.code === '00000') {
          // console.log('res.data:', res.data)
          localStorage.clear()
          setToken(res.data.token)
          localSave('userLoginId', userLoginId)
          localSave('roleId', res.data.roleId)
          localSave('token', res.data.token)
          localSave('tenantName', res.data.tenantName)
          localSave('isShowQn', res.data.isShowQn + '')
          localSave('qnUrl', res.data.qnUrl)
          localSave('manualId', res.data.manualId)
          localSave('manualName', res.data.manualName)
          resolve(res)
        } else reject(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  loginInit ({ commit }, { userLoginId, token, userSource }) {
    return new Promise((resolve, reject) => {
      loginInit({ userLoginId, token, userSource }).then(res => {
        if (res.code === '00000') {
          // console.log('res.data:', res.data)
          localStorage.clear()
          setToken(res.data.token)
          localSave('userLoginId', userLoginId)
          localSave('roleId', res.data.roleId)
          localSave('token', res.data.token)
          localSave('tenantName', res.data.tenantName)
          localSave('isShowQn', res.data.isShowQn + '')
          localSave('qnUrl', res.data.qnUrl)
          localSave('manualId', res.data.manualId)
          localSave('manualName', res.data.manualName)
          resolve(res)
        } else reject(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  register ({ commit }, params) {
    return new Promise((resolve, reject) => {
      register(params).then(res => {
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  logout () {
    setToken('')
    localStorage.clear()
  }
}

export default {
  getters,
  state,
  mutations,
  actions,
  modules: {
    //
  }
}
