<template>
  <div class="page">
    <!-- <SelectChain></Select<PERSON>hain> -->
    <!--  -->
    <div class="content" v-if="!isShowDetial">
      <div class="info">
        <div class="infoTitle">
          <img :src="infoIcon" class="infoIcon" />
          <span class="infotext" @click="goChannelMgr">通道信息</span>
        </div>
        <div class="btnList clearfix">
          <el-button type="primary" class="green-btn btn-bt"  @click="getisShowCreatChannel">创建通道</el-button>
        </div>
      </div>
      <SpaceLayout top="0" paddingX="0" paddingY="0">
        <div slot="padding">
          <!-- <div class="clearfix">
            <el-button type="primary" class="blue-btn btn-bt"  @click="getisShowCreatChannel">创建通道</el-button>
          </div> -->
          <div class="table-wrapper">
            <el-row class="nav-box">
              <el-col :span="5"><div class="">通道名称</div></el-col>
              <el-col :span="7"><div class="">描述说明</div></el-col>
              <el-col :span="4"><div class="">创建时间</div></el-col>
              <el-col :span="8" style=""><div class="">操作</div></el-col>
            </el-row>
            <div class="nan-item" v-for="(item, index) in listData" :key="index">
              <el-row class="nav-box">
                <el-col :span="5"
                  ><div class="">
                    <span>{{ item.ChannelName }}</span>
                  </div></el-col
                >
                <el-col :span="7"
                  ><div class="">
                    <span>{{ item.Description }}</span>
                  </div></el-col
                >
                <el-col :span="4"
                  ><div class="">
                    <span>{{ item.CreateTime }}</span>
                  </div></el-col
                >
                <el-col :span="8">
                  <div class="">
                    <span
                      class="handle-btn"
                      @click="getOrgs(item.ChannelName)"
                      >添加节点</span
                    >
                    <span
                      class="handle-btn"
                      @click="setBatchTime(item.ChannelName)"
                      >设置区块打包时间</span
                    >
                    <span
                      class="handle-btn"
                      @click="goChannelDetial(item.ChannelName)"
                      >查看详情</span
                    >
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>

          <div class="pagination" v-if="listData.length > 0">
            <Page
              :total="total"
              :current.sync="pageIndex"
              @on-change="handleCurrentChange"
              :page-size="pageSize"
              :page-size-opts="[10,20,40,60,100]"
              show-total show-elevator show-sizer
              @on-page-size-change="handleSizeChange"
              style="text-align:right;"/>
            <!-- <pagination
              @toHandleSizeChange="handleSizeChange"
              @toHandleCurrentChange="handleCurrentChange"
              @toJumpFirstPage="jumpFirstPage"
              @toJumpLastPage="jumpLastPage"
              :fTotal="total"
              :fBtnStartDisabled="btnStartDisabled"
              :fBtnEndDisabled="btnEndDisabled"
              :fPageIndex="pageIndex"
              :fZys="zys"
            >
            </pagination> -->
          </div>
          <div class="none" v-if="listData.length == 0">
            <i class="el-icon-loading" v-if="paddingText == '数据请求中...'"></i>
            <!-- <svg-icon icon-class="table-empty" v-else/> -->
            {{ paddingText }}
          </div>
        </div>
      </SpaceLayout>
    </div>
    <!-- 创建通道弹框 -->
    <transition name="fade">
      <div class="alertBox" v-if="isShowCreatChannel">
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">创建通道
              <i class="el-icon-cursor el-icon-close fr" @click="closeDialog"></i>
            </div>
         </div>
          <div class="alert_box">
            <el-form ref="creatForm" :model="creatForm" :rules="rules" label-width="">
              <div class="selectBox">
                <div class="evertModule">
                  <el-form-item label="通道名称：" prop="ChannelName">
                    <el-input v-model="creatForm.ChannelName" maxLength="16" placeholder="请输入" @focus ='focusName'></el-input>
                    <span class="userTip" v-if="channelNameTip">4-16个小写字母、数字组成，必须小写字母开头</span>
                    <span class="userTip el-form-item__error" v-if="isReName">通道名称已存在</span>
                  </el-form-item>
                </div>
                <div class="evertModule">
                  <el-form-item label="描述说明：" prop="Description">
                    <el-input type="textarea" v-model="creatForm.Description" maxLength="30" placeholder="请输入" @focus ='focusDescription' ></el-input>
                    <span class="userTip" v-if="desTip">支持2-30个中文、英文、数字或顿号、逗号、句号</span>
                  </el-form-item>
                </div>
                <div class="evertModule">
                  <el-form-item label="组织节点：" prop="Orgs">
                    <el-table
                      ref="multipleTable"
                      :data="orgsData"
                      tooltip-effect="dark"
                      max-height="210"
                      :header-cell-style="headClass"
                      :cell-style="rowClass"
                      :row-class-name="tableRowClassName"
                      @selection-change="handleSelectionChange"
                    >
                      <el-table-column type="selection" width="40" >
                      </el-table-column>
                      <el-table-column label="组织名称" prop="Name">
                      </el-table-column>
                      <el-table-column prop="Peer" label="节点名称">
                        <template slot-scope="scope">
                          {{ getName(scope.row.Peer) }}
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <div class="btn-row">
            <Button  type="primary" class="sure-btn" @click="createChannel">创建</Button>
            <Button  class="border-btn" type="text" @click="deselect">取消</Button>
          </div>
        </div>
      </div>
    </transition>
    <!-- 添加成员弹框 -->
    <transition name="fade">
      <div class="alertBox" v-show="isShowOrgs">
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">添加节点
              <i class="el-icon-cursor el-icon-close fr" @click="deselect"></i>
            </div>
<!--            <div class="closeIcon close_img" @click="deselect">-->
<!--              <img :src="closeImg" alt="" />-->
<!--            </div>-->
          </div>
          <div class="alert_box">
            <el-form ref="orgsForm" :model="orgsForm" :rules="orgsRules" label-width="" >
              <div class="selectBox">
                <div class="evertModule">
                  <el-form-item label="组织节点：" prop="Orgs">
                    <el-table
                      ref="multipleTable1"
                      :data="addOrgsList"
                      tooltip-effect="dark"
                      max-height="210"
                      :header-cell-style="headClass"
                      :cell-style="rowClass"
                      :row-class-name="tableRowClassName"
                      @selection-change="handleSelectionAddChange">
                      <el-table-column type="selection" width="40"></el-table-column>
                      <el-table-column label="组织名称" prop="Name"></el-table-column>
                      <el-table-column prop="Peer" label="节点名称"><template slot-scope="scope">{{ getName(scope.row.Peer) }}</template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <div class="btn-row">
            <Button type="primary" class="sure-btn" @click="addOrgs()">添加</Button>
            <Button class="border-btn" type="text" @click="deselect">取消</Button>

          </div>
        </div>
      </div>
    </transition>
    <!-- 设置区块打包时间弹框 -->
    <transition name="fade">
      <div class="alertBox" v-show="isShowTime">
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">设置区块打包时间
               <i class="el-icon-cursor el-icon-close fr" @click="deselect"></i>
            </div>
          </div>
          <div class="alert_box form">
             <el-form ref="timeForm" :model="timeForm" :rules="timeRules" label-width="">
              <div class="evertModule time">
                <el-form-item label="打包时间：">
                  <span class="text">每</span>
                  <div class="subtraction" @click="subtractionTime">-</div>
                  <el-input class="numrule" v-model.trim="timeForm.BatchTimeout"
                    @input="timeForm.BatchTimeout=timeForm.BatchTimeout.replace(/[^\d]/g,'');" @blur="validaBatchTimeout" @focus="focusF" @change="changeBatchTime" maxlength="5"></el-input>
                  <div class="addition" @click="additionTime">+</div>
                  <span class="text">ms内生成一个区块</span>
                </el-form-item>
                <div class="error">设置范围在100-10000ms内</div>
                <div class="error el-form-item__error" v-if="isShowErrorTime">{{errorTimeText}}</div>
              </div>
            </el-form>
          </div>
          <div class="btn-row">
            <Button type="primary" class="sure-btn" @click="setChannelBlockBatchTimeout">确定</Button>
            <Button class="border-btn" type="text" @click="deselect">取消</Button>
          </div>
        </div>
      </div>
    </transition>
    <div v-if="loading" class="BoxLoading" v-loading="loading" :element-loading-text="loadingMsg" ></div>
    <ChannelDetial v-if="isShowDetial" :detail="detail" @getIsShowDetial="getIsShowDetial"></ChannelDetial>
    <!-- <router-view></router-view> -->
    <!-- <countDown v-if="isShowIcon" :state="countState" :countTime="countTime" :text="countText" @getCountDown="getCountDown"></countDown> -->
  </div>
</template>

<script>
import
  {
    getOrgPeerList,createChannel,joinPeerToChannel,
    getUnjoinedPeerListOfChannel,getChannelBlockConfig,setChannelBlockBatchTimeout,
    checkChannelName,checkChannelLimit
  }
from '@/api/baascore/channelMgr'
import {getChannelList} from '@/api/baascore/agreement'
import ChannelDetial from './compontents/detail'
import SelectChain from '../compontents/selectChain'
import SpaceLayout from '@/components/SpaceLayout'
export default {
  components: {
    ChannelDetial,
    SelectChain,
    SpaceLayout,
  },
  data() {
    return {
      countState: "",
      countTime: 2,
      countText: "",
      isShowIcon: false,
      paddingText: "数据请求中...",
      closeImg: require("@/assets/image/close.png"),
      infoIcon: require("@/assets/chainManage_images/overview/infoIcon.png"),
      listData: [],
      tableData: [],
      pageSize: 10,
      pageIndex: 1,
      btnStartDisabled: false, //用来判断首页尾页按钮是否禁用
      btnEndDisabled: false, //用来判断首页尾页按钮是否禁用
      zys: 0, //判断总页数
      total: 0,
      isShowDetial: false,
      detail: {
        ChannelName: "",
        ServiceId: "",
      },
      loading: false,
      isShowCreatChannel: false,
      creatForm: {
        ChannelName: "",
        Description: "",
        Orgs: [],
      },
      orgsForm: {
        Orgs: [],
      },
      rules: {
        ChannelName: [
          {
            required: true,
            validator: this.validaChannelName,
            trigger: "blur",
          },
        ],
        Description: [
          {
            required: true,
            validator: this.validaDescription,
            trigger: "blur",
          },
        ],
        Orgs: [{ required: true, message: "请添加组织节点", trigger: "blur" }],
      },
      orgsRules: {
        Orgs: [{ required: true, message: "请添加组织节点", trigger: "blur" }],
      },
      orgsData: [],
      orgPeerList: [],
      isShowOrgs: false,
      addOrgsList: [],
      ChannelName: "",
      isShowTime: false,
      timeForm: {
        BatchTimeout: "",
        PreferSize: "",
        MaxTxCount: "",
      },
      timeRules: {
      },
      selectRow: [],
      loadingMsg: "",
      channelNameTip:true,// 用户提示显示/隐藏
      desTip:true,
      isReName:false,
      isShowErrorTime:false,
      errorTimeText:'',
    };
  },
  watch: {
    chainItem: {
      handler(newvalue, oldvalue) {
        if (newvalue && newvalue.Id) {
          this.getChannelList();
        }
      },
      deep: true,
    },
    isShowDetial:{
      handler(newvalue) {
        if(!newvalue) {
          this.getChannelList();
        }
      }
    }
  },
  computed: {
    getName(name) {
      return function (name) {
        if (name.indexOf("-") != -1) {
          var strList = name.split("-");
          name = strList[0] + "-" + strList[1];
          return name;
        } else {
          return name;
        }
      };
    },
  },
  mounted() {
    // if( this.$route.path != '/chainManage/channelMgr') {
    //   this.isShowDetial = true
    // }else {
    //   this.isShowDetial = false
    // }
    this.getChannelList();
  },
  methods: {
    changeBatchTime(value) {
      if(isNaN(value)) {
        this.$set(this.timeForm,'BatchTimeout','')
      }else {
        this.timeForm.BatchTimeout = value
      }
     // isNaN(value)?this.$set(this.timeForm,'BatchTimeout','1') : this.timeForm.BatchTimeout = value

    },
    focusF(){
      //this.$refs["timeForm"].clearValidate(["BatchTimeout"]);
      this.isShowErrorTime = false
    },
    getCountDown(type) {
      this.isShowIcon = false;
    },
    getisShowCreatChannel() {
      // if (this.total >= 32){
      //   return this.$message({
      //     message: '通道数量达到上限，无法再创建新通道。',
      //     type: 'warning'
      //   })
      // }
      var chain = JSON.parse(sessionStorage.getItem("chainItem"));
      var params = {
        ServiceId:chain.Id
      }
      checkChannelLimit(params).then(res =>{
        if(res.code == 200) {
          this.isShowCreatChannel = true;
          this.channelNameTip = true;
          this.desTip = true;
          this.selectRow = [];
          this.getOrgPeerList();
        }else {
          this.$message({
                 message: '通道数量达到上限，无法再创建新通道。',
                 type: 'warning'
          })
        }
      })
    },
    //设置打包时间
    setChannelBlockBatchTimeout() {
      //this.$refs.timeForm.validate((valid) => {
        if (!this.isShowErrorTime) {
          var chain = JSON.parse(sessionStorage.getItem("chainItem"));
          var ServiceId = chain.Id;
          var data = {
            //msgType:'setChannelBlockBatchTimeout',
            //params:{
            ServiceId,
            ChannelName: this.ChannelName,
            BlockConfig: {
              BatchTimeout: Number(this.timeForm.BatchTimeout),
              PreferSize: this.timeForm.PreferSize,
              MaxTxCount: this.timeForm.MaxTxCount,
            },
            // }
          };
          setChannelBlockBatchTimeout(data).then((res) => {
            if (res.code == 200) {
              this.isShowTime = false;
              // this.isShowIcon = true
              // this.countState = 'success'
              // this.countText = '设置成功！'
              this.$message.success("设置成功!");
            } else {
              this.isShowTime = false;
              // this.isShowIcon = true
              // this.countState = 'error'
              // this.countText = '设置失败，请重试！'
              this.$message.error("设置失败，请重试！");
            }
          });
        } else {
          return false;
        }
   //   });
    },
    subtractionTime() {
      this.timeForm.BatchTimeout--;
      var value = this.timeForm.BatchTimeout
      this.$refs["timeForm"].resetFields();
      if (value < 1 ) {
        this.isShowErrorTime = true
        this.timeForm.BatchTimeout = 1
        this.errorTimeText = "打包时间须大于100ms"
      } else if (value <= 100) {
        this.timeForm.BatchTimeout = value
        this.isShowErrorTime = true
        this.errorTimeText = "打包时间须大于100ms"
      } else if (value >= 10000) {
        this.timeForm.BatchTimeout = value
        this.isShowErrorTime = true
        this.errorTimeText = "打包时间须小于10000ms"
      }else {
        this.timeForm.BatchTimeout = value
        this.isShowErrorTime = false
      }
    },
    additionTime() {
      this.timeForm.BatchTimeout++;
      var value = this.timeForm.BatchTimeout
      this.$refs["timeForm"].resetFields();
      if (value < 1 ) {
        this.isShowErrorTime = true
        this.timeForm.BatchTimeout = 1
        this.errorTimeText = "打包时间须大于100ms"
      } else if (value <= 100) {
        this.timeForm.BatchTimeout = value
        this.isShowErrorTime = true
        this.errorTimeText = "打包时间须大于100ms"
      } else if (value >= 10000) {
        this.timeForm.BatchTimeout = value
        this.isShowErrorTime = true
        this.errorTimeText = "打包时间须小于10000ms"
      }else {
        this.timeForm.BatchTimeout = value
        this.isShowErrorTime = false
      }
    },
    //获取区块打包时间
    setBatchTime(ChannelName) {
     // this.$refs["timeForm"].resetFields();
      this.isShowErrorTime = false
      var ChannelName = ChannelName;
      this.ChannelName = ChannelName;
      var chain = JSON.parse(sessionStorage.getItem("chainItem"));
      var ServiceId = chain.Id;
      var params = {
        ChannelName,
        ServiceId,
      };
      getChannelBlockConfig(params).then((res) => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if (res.data) {
            this.timeForm.BatchTimeout = res.data.BatchTimeout;
            this.timeForm.PreferSize = res.data.PreferSize;
            this.timeForm.MaxTxCount = res.data.MaxTxCount;
            this.isShowTime = true;
          }
        } else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      });
    },
    //添加成员
    addOrgs() {
      this.$refs.orgsForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.loadingMsg = "添加中";
          var chain = JSON.parse(sessionStorage.getItem("chainItem"));
          var ServiceId = chain.Id;
          var Orgs = this.orgsForm.Orgs;
          var data = {
            // msgType:'joinPeerToChannel',
            // params:{
            ChannelName: this.ChannelName,
            Orgs,
            ServiceId,
            // }
          };
          joinPeerToChannel(data)
            .then((res) => {
              if (res.code == 200) {
                this.isShowOrgs = false;
                this.loading = false;
                // this.isShowIcon = true
                // this.countState = 'success'
                // this.countText = '添加成功！'
                this.$message.success("添加成功!");
              } else {
                this.loading = false;
                this.isShowOrgs = true;
                // this.isShowIcon = true
                // this.countState = 'error'
                // this.countText = '添加成员失败,请重新添加！'
                this.$message.error("添加成员失败,请重新添加!");
              }
            })
            .catch(() => {
              //this.$message.error('网络异常,请检查网络')
              this.loading = false;
              this.isShowOrgs = true;
            });
        } else {
          return false;
        }
      });
    },
    getOrgs(ChannelName) {
      this.$refs["orgsForm"].resetFields();
      this.selectRow = [];
      var ChannelName = ChannelName;
      this.ChannelName = ChannelName;
      var chain = JSON.parse(sessionStorage.getItem("chainItem"));
      var ServiceId = chain.Id;
      var params = {
        ServiceId,
        ChannelName,
      };
      getUnjoinedPeerListOfChannel(params).then((res) => {
        if (res.code== 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if (res.data && res.data.Orgs) {
            var data = res.data.Orgs;
            var arr = [];
            var obj = {};
            var isHavePeers = false;
            data.forEach((item) => {
              if (item.Name && item.Peers && item.Peers.length > 0) {
                item.Peers.forEach((citem, cindex) => {
                  obj = {
                    Name: item.Name,
                    Peer: item.Peers[cindex],
                  };
                  arr.push(obj);
                  isHavePeers = true;
                });
              }
            });
            if (isHavePeers) {
              this.addOrgsList = arr;
              this.isShowOrgs = true;
            } else {
              this.isShowOrgs = false;
              // this.isShowIcon = true
              // this.countState = 'full'
              // this.countText = '通道成员节点数量已满,无法添加新成员！'
              this.$message.warning("通道成员节点数量已满,无法添加新成员");
            }
          }
        } else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '添加失败，请重试！'
          this.$message.error("添加失败，请重试！");
        }
      });
    },
    //创建通道
    createChannel() {
      this.$refs.creatForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.loadingMsg = "创建中";
          var chain = JSON.parse(sessionStorage.getItem("chainItem"));
          var ServiceId = chain.Id;
          var data = {
            // msgType:'createChannel',
            // params:{
            ChannelName: this.creatForm.ChannelName,
            Description: this.creatForm.Description,
            Orgs: this.creatForm.Orgs,
            ServiceId,
            // }
          };
          // var data2 = {
          //   //msgType:'joinPeerToChannel',
          //   //params:{
          //   ChannelName: this.creatForm.ChannelName,
          //   Orgs: this.creatForm.Orgs,
          //   ServiceId,
          //   // }
          // };
          createChannel(data)
            .then((res) => {
              if (res.code == 200) {
                this.$refs.creatForm.resetFields();
                this.getChannelList();
                this.isShowCreatChannel = false;
                this.loading = false;
                this.$message.success("创建成功");
                // joinPeerToChannel(data2)
                //   .then((res) => {
                //     if (res.status == 200) {
                //       this.getChannelList();
                //       this.isShowCreatChannel = false;
                //       this.loading = false;
                //       // this.isShowIcon = true
                //       // this.countState = 'success'
                //       // this.countText = '创建成功！'
                //       this.$message.success("创建成功");
                //     } else if(res.status == 4230) {
                //       this.loading = false;
                //       this.$message.error("创建操作失败，请稍后再试！");
                //     }
                //     else{
                //       this.isShowCreatChannel = true;
                //       this.loading = false;
                //       // this.isShowIcon = true
                //       // this.countState = 'error'
                //       // this.countText = '创建失败，请重试！'
                //       this.$message.error("创建失败，请重试！");
                //     }
                //   })
                //   .catch((err) => {
                //     this.isShowCreatChannel = true;
                //     this.loading = false;
                //     //this.$message.error("网络异常,请检查网络");
                //   });
              }else if(res.code == 4305) {
                this.isShowCreatChannel = true;
                this.loading = false;
                this.isReName = true
                //this.$message.error(res.message);
              }else if(res.code == 4230) {
                this.loading = false;
                this.$message.error("创建操作失败，请稍后再试！");
              }
              else {
                this.isShowCreatChannel = true;
                this.loading = false;
                // this.isShowIcon = true
                // this.countState = 'error'
                // this.countText = '创建失败，请重试！'
                this.$message.error("创建失败,请重试！");
              }
            })
            .catch((err) => {
              this.isShowCreatChannel = true;
              this.loading = false;
              // this.$message.error("网络异常,请检查网络");
            });
        } else {
          return false;
        }
      });
    },
    //关闭
    deselect() {
      if(this.$refs.creatForm) {
        this.$refs.creatForm.resetFields();
      }
      this.isShowCreatChannel = false;
      this.isShowOrgs = false;
      this.isShowTime = false;
      this.isReName = false
      this.isShowErrorTime = false
    },
    getOrgPeerList() {
      var chain = JSON.parse(sessionStorage.getItem("chainItem"));
      var ServiceId = chain.Id;
      var params = {
        ServiceId,
      };
      getOrgPeerList(params).then((res) => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          var data = res.data.Orgs;
          var arr = [];
          var obj = {};
          data.forEach((item) => {
            if (item.Name && item.Peers && item.Peers.length > 0) {
              item.Peers.forEach((citem, cindex) => {
                obj = {
                  Name: item.Name,
                  Peer: item.Peers[cindex],
                };
                arr.push(obj);
              });
            }
          });
          this.orgPeerList = res.data.Orgs;
          this.orgsData = arr;
        } else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      });
    },
    //添加组织
    handleSelectionAddChange(val) {
      this.selectRow = val;
      var list = val;
      var orgs = [];
      var tempArr = [];
      for (let i = 0; i < list.length; i++) {
        if (tempArr.indexOf(list[i].Name) === -1) {
          orgs.push({
            Name: list[i].Name,
            Peers: [list[i].Peer],
          });
          tempArr.push(list[i].Name);
        } else {
          for (let j = 0; j < orgs.length; j++) {
            if (orgs[j].Name == list[i].Name) {
              orgs[j].Peers.push(list[i].Peer);
              break;
            }
          }
        }
      }
      this.orgsForm.Orgs = orgs;
      this.$refs["orgsForm"].clearValidate(["Orgs"]);
    },
    //创建通道
    handleSelectionChange(val) {
      this.$refs["creatForm"].clearValidate(["Orgs"]);
      this.selectRow = val;
      var list = val;
      var orgs = [];
      var tempArr = [];
      for (let i = 0; i < list.length; i++) {
        if (tempArr.indexOf(list[i].Name) === -1) {
          orgs.push({
            Name: list[i].Name,
            Peers: [list[i].Peer],
          });
          tempArr.push(list[i].Name);
        } else {
          for (let j = 0; j < orgs.length; j++) {
            if (orgs[j].Name == list[i].Name) {
              orgs[j].Peers.push(list[i].Peer);
              break;
            }
          }
        }
      }
      this.creatForm.Orgs = orgs;
    },
    getIsShowDetial(type) {
      this.isShowDetial = type;
    },
    goChannelMgr() {
      this.$router.push({
        path: "/chainManage/channelMgr",
      });
    },
    //效验通道名称 ^(?!\d)[a-zA-Z0-9\u4e00-\u9fa5]+$
    validaChannelName(rule, value, callback) {
      this.channelNameTip=false;
      var re = /^[a-z]{1}[a-z0-9]{3,15}$/;
      if (value == "" || value == undefined) {
        callback(new Error("请输入通道名称"));
      }
      else if (!re.test(value)) {
        callback(new Error("通道名称格式错误"));
      }
      else {
        var chain = JSON.parse(sessionStorage.getItem("chainItem"));
        var ServiceId = chain.Id;
        var params = {
          ServiceId:ServiceId,
          ChannelName:value
        }
        checkChannelName(params).then(res =>{
          if(res.code == 200) {
            callback();
          }else {
            callback(new Error("通道名称已存在"));
          }
        })
      }
    },
    //效验描述说明
    validaDescription(rule, value, callback) {
      this.desTip=false;
      var re = /^[\da-zA-Z\u4e00-\u9f5a\(（.,，。、）)]{2,30}$/;
      // var re = /[a-zA-Z0-9\u4e00-\u9fa5]{2,30}/
      if (value == "" || value == undefined) {
        callback(new Error("请输入描述说明"));
      } else if (!re.test(value)) {
        callback(new Error("描述说明格式错误"));
      } else {
        callback();
      }
    },
    //效验打包时间
    validaBatchTimeout() {
      var value = this.timeForm.BatchTimeout
      if (value == "" || value == undefined) {
        this.isShowErrorTime = true
        this.errorTimeText = "请输入打包时间"
      } else if (value <= 100) {
        this.isShowErrorTime = true
        this.errorTimeText = "打包时间须大于100ms"
      } else if (value >= 10000) {
        this.isShowErrorTime = true
        this.errorTimeText = "打包时间须小于10000ms"
      } else {
        this.isShowErrorTime = false
      }
    },
    //详情页面
    goChannelDetial(name) {
      // var list = []
      // this.breadcrumbList.push({
      //   title:name + '通道详情'
      // })
      // if(this.breadcrumbList.length > 3) {
      //   list = this.breadcrumbList.slice(0,this.breadcrumbList.length-1)
      // }else {
      //   list = this.breadcrumbList
      // }
      // this.$store.dispatch("getBreadcrumbList", list).then(() =>{
          this.detail.ChannelName = name;
          this.isShowDetial = true
      // })
      // sessionStorage.setItem('detail',JSON.stringify(this.detail))
      // this.$router.push({
      //   path:'/chainManage/channelMgr/detail'
      // })
    },
    getListData() {
      // this.zys = Math.ceil(this.total / this.pageSize); //获取总页数
      // this.pageIndex > this.zys ? this.pageIndex = this.zys : '';
      this.listData = this.tableData.slice(
        (this.pageIndex - 1) * this.pageSize,
        this.pageSize * this.pageIndex
      );
    },
    getChannelList() {
      var chain = JSON.parse(sessionStorage.getItem("chainItem"));
      if (chain) {
        var ServiceId = chain.Id;
        this.detail.ServiceId = ServiceId;
        var params = {
          ServiceId,
        };
        getChannelList(params).then((res) => {
          if (res.code == 200) {
            // this.isShowIcon = true
            // this.countState = 'success'
            // this.countText = '请求成功！'
            if (res.data.Channels) {
              this.tableData = res.data.Channels;
              this.total = res.data.Channels.length;
              if (this.total == 0) {
                this.paddingText = "暂无数据";
              }
              this.zys = Math.ceil(Number(this.total) / this.pageSize);
              if (this.zys == 1) {
                //只有1页数据首页尾页禁用
                this.btnStartDisabled = true;
                this.btnEndDisabled = true;
              }
              this.getListData();
            } else {
              this.tableData = [];
              this.listData = [];
              this.paddingText = "暂无数据";
            }
          } else {
            this.tableData = [];
            this.total = 0;
            this.paddingText = "暂无数据";
            // this.isShowIcon = true
            // this.countState = 'error'
            // this.countText = '数据获取失败，请重新加载！'
            this.$message.error("数据获取失败，请重新加载！");
            this.getListData();
          }
        });
      }
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getListData();
    },
    handleCurrentChange(val) {
      if (val == 1) {
        this.btnStartDisabled = true;
        this.btnEndDisabled = false;
      } else if (val == this.zys) {
        this.btnStartDisabled = false;
        this.btnEndDisabled = true;
      } else {
        this.btnStartDisabled = false;
        this.btnEndDisabled = false;
      }
      this.pageIndex = val;
      this.getListData();
    },
    // 首页按钮
    jumpFirstPage() {
      this.pageIndex = 1;
      this.btnStartDisabled = true;
      this.btnEndDisabled = false;
      this.handleCurrentChange(1);
    },
    // 尾页按钮
    jumpLastPage() {
      this.pageIndex = this.zys;
      this.btnStartDisabled = false;
      this.btnEndDisabled = true;
      this.handleCurrentChange(this.pageIndex);
    },
    //表格数据居中显示
    headClass() {
      return "text-align:center;background:#F2F7FA;padding:4px 0 ;";
    },
    rowClass() {
      return "text-align:left";
    },
    tableRowClassName({ row, rowIndex }) {
      let color = "";
      this.selectRow.forEach((r, i) => {
        if (r.Peer === row.Peer) {
          color = "warning-row";
        }
      });
      return color;
    },
    // 关闭按钮关闭弹窗
    closeDialog() {
      if(this.$refs.creatForm) {
        this.$refs.creatForm.resetFields();
      }
      this.isReName = false
      this.isShowOrgs = false;
      this.isShowTime = false;
      this.isReName = false
      this.isShowCreatChannel = false; //创建通道弹框//删除弹窗//上传弹窗
    },
    focusName(){
      this.channelNameTip = true;
      this.isReName = false
      this.$refs["creatForm"].clearValidate(["ChannelName"]);
    },
    focusDescription(){
      this.desTip = true;
      this.$refs["creatForm"].clearValidate(["Description"]);
    }
  },
};
</script>

<style rel="stylesheet/less" lang="less" scoped>
@import "../../../styles/common/modal.less";
@import "../../../styles/common/select.less";
.page {
  width: 100%;
  height: 100%;
  .clearfix:after {
    content: "";
    height: 0;
    line-height: 0;
    display: block;
    visibility: hidden;
    clear: both;
  }
  .clearfix .btn {
    float: right;
    text-align: center;
    width: 120px;
    height: 46px;
    background: #337dff;
    border: 2px solid;
    border-radius: 4px;
    // font-size: 17px;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #ffffff;
    line-height: 42px;
    cursor: pointer;
  }
  .content {
    // .title {
    //   margin-top: 20px;
    //   .infoIcon {
    //     width: 6px;
    //     height: 14px;
    //     margin-right:5px;
    //     vertical-align: middle;
    //   }
    //   .infotext {
    //     // font-size: 22px;
    //     font-size: 14px;
    //     color: #333333;
    //     vertical-align: middle;
    //     cursor: default;
    //   }
    // }
    .info {
      width: 100%;
      margin: 20px 0px 10px;
      display: flex;
      justify-content: space-between;
      .infoTitle {
        // font-size: 20px;
        font-size: 14px;
        .infoIcon {
          width: 3px;
          height: 14px;
          margin-right:5px;
          vertical-align: middle;
        }
        .infotext {
          color: #333333;
          vertical-align: middle;
        }
      }
    }
    .none {
      text-align: center;
      height: 56px;
      background: #fff;
      line-height: 56px;
      // font-size: 17px;
      font-size: 14px;
    }
    .nav-box {
      color: #999999;
      // font-size: 17px;
      font-size: 14px;
      box-sizing: border-box;
      //background: #F3F4FD;
      /*margin-top: 20px;*/
    }
    .nav-box /deep/ .el-col {
      text-align: center;
      color: #999999;
      // font-size: 17px;
      font-size: 14px;
      padding: 10px 0;
    }
    .nav-box .el-col div span {
      /*margin:0 3px;*/
      /*cursor: default;*/
    }
    .nan-item .nav-box:hover {
      -moz-box-shadow: 2px 2px 6px #bbd1e6, -2px -2px 6px #bbd1e6;
      -webkit-box-shadow: 2px 2px 6px #bbd1e6, -2px -2px 6px #bbd1e6;
      box-shadow: 2px 2px 6px #bbd1e6, -2px -2px 6px #bbd1e6;
    }
    .nan-item .nav-box {
      // font-size: 17px;
      font-size: 14px;
      box-sizing: border-box;
      background: #fff;
      display: flex;
      align-items: center;
    }
    .nan-item .nav-box /deep/ .el-col {
      color: #666666;
    }
    .nan-item .nav-box .el-col div span.add {
      display: inline-block;
      width: 102px;
      height: 36px;
      border-radius: 4px;
      background: #00ada2;
      text-align: center;
      line-height: 36px;
      // font-size: 17px;
      font-size: 14px;
      color: #ffffff;
      cursor: pointer;
    }
    .nan-item .nav-box .el-col div span.setTime {
      display: inline-block;
      width: 178px;
      height: 36px;
      border-radius: 4px;
      text-align: center;
      line-height: 36px;
      background: #f2994a;
      // font-size: 17px;
      font-size: 14px;
      color: #ffffff;
      cursor: pointer;
    }
    .nan-item .nav-box .el-col div span.check {
      display: inline-block;
      width: 102px;
      height: 36px;
      border-radius: 4px;
      text-align: center;
      line-height: 36px;
      background: #1973cc;
      // font-size: 17px;
      font-size: 14px;
      color: #ffffff;
      cursor: pointer;
    }
    // 页码
    .pagination {
      /*margin: 20px 0px 20px;*/
    }
  }
.form {
        width: 100%;
        margin-top: 60px;
        .el-form {
          padding: 0 50px;
          // font-size: 17px;
          font-size: 14px;
          .el-form-item {
            margin-bottom: 0;
          }
          .el-input /deep/.el-input__inner {
            padding: 24px 0 24px 25px;
            border-left: none;
            // font-size: 17px;
            font-size: 14px;
          }
          .el-textarea /deep/.el-textarea__inner {
            padding-top: 10px;
            padding-left: 25px;
            // font-size: 17px;
            font-size: 14px;
          }
          .el-form-item /deep/.el-form-item__label {
            /*text-align: left;*/
            /*font-size: 17px;*/
            /*padding-top: 6px;*/
            /*font-weight: 400;*/
            /*color: #333333;*/
          }
          /deep/
          .el-input,
          .el-textarea {
            width: 420px;
          }
          .el-table {
            width: 420px;
            // font-size: 17px;
            border: 2px solid #e7ecef;
          }
          .el-table::before {
            height: 0px !important;
          }
          .el-table /deep/ td {
            border-bottom: 0px !important;
          }
          .el-table /deep/ .el-table-column--selection {
            text-align: left !important;
          }
          .el-table /deep/ tr:hover > td {
            background-color: #f0ffff !important;
          }
          .el-table /deep/ th.gutter {
            background: #f2f7fa !important;
          }
          .el-table /deep/ .warning-row {
            background-color: #f0ffff !important;
          }
          //#F04134
          .el-form-item.is-error /deep/ .el-form-item__error {
            color: #fc4f1d !important;
            // font-size: 17px;
            font-size: 14px;
          }
          /deep/ .el-checkbox__input.is-checked .el-checkbox__inner {
            border-color: #46b8c9;
            background: #46b8c9;
          }
          /deep/ .el-checkbox__input.is-indeterminate .el-checkbox__inner {
            border-color: #46b8c9;
            background: #46b8c9;
          }
          /deep/ .el-form-item.is-required .el-form-item__label:before,
          /deep/ .el-icon-upload:before {
            content: "";
          }
        }
        .evertModule {
          /*display: flex;*/
          align-items: center;
          margin-bottom: 40px;
          // font-size: 17px;
          font-family: Microsoft YaHei;
          font-weight: normal;
          color: #333333;
        }
        .time {
          position: relative;
          text-align: center;
          margin-bottom: 100px;
          /deep/.el-input {
            height: 50px;
          }
           /deep/
           .el-form-item.is-error .el-input__inner,
           .el-form-item.is-error .el-input__inner:focus,
           .el-form-item.is-error .el-textarea__inner,
           .el-form-item.is-error .el-textarea__inner:focus,
           .el-message-box__input input.invalid,
           .el-message-box__input input.invalid:focus{
              border-color:#DCDFE6;
              outline: none;
            /deep/ .el-input__inner{
                outline: none;
                border:none;
                padding-top: 0;
                padding-bottom: 0;
                height: 30px;
                text-align: center;
                width: 95px;
              }
           }

            /deep/ .el-input__inner:focus{
              outline: none;
              border-color:#d9d9d9 !important;
            }
          // .el-input {
          //   box-sizing: border-box;
          //   width: 220px;
          //   text-align: center;
          //   margin-right: 10px;
          //   padding-left: 50px;
          //   outline: none;
          //   height: 30px;
          //   padding-top: 0;
          //   padding-bottom: 0;
          // }
          // /deep/ .el-form-item__error {
          //   top: 168%;
          //   font-size: 16px;
          //   color: #fc4f1d !important;
          // }
          // .text {
          //   font-size: 17px;
          //   color: #333333;
          // }
        }
}
.error.el-form-item__error {
  top: 90%;
}
  .BoxLoading {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10000;
  }
  .BoxLoading /deep/ .el-loading-mask {
    background: rgba(0, 0, 0, 0.2) !important;
  }
  .BoxLoading /deep/ .el-loading-spinner .circular {
    width: 60px !important;
    height: 60px !important;
  }
  .BoxLoading /deep/ .el-loading-spinner .path {
    stroke: #fff !important;
  }
  .BoxLoading /deep/ .el-loading-spinner .el-loading-text {
    color: #fff !important;
    // font-size: 24px;
    font-size: 14px;
    font-weight: bold;
  }

}
 /deep/.numrule input::-webkit-outer-spin-button,
  /deep/.numrule input::-webkit-inner-spin-button {
    -webkit-appearance: none!important;
  }
  /deep/.numrule input[type="number"]{
    -moz-appearance: textfield;
  }
  /deep/
    .el-form-item__content{
      line-height: 32px;
    }
.ivu-page{
  margin-top:10px;
}
.ivu-btn {
  height:auto;
  padding:4px 15px;
}
</style>

