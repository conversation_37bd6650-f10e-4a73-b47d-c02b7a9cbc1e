<!--
 预言机列表
   Aturun
-->
<template>
    <div class="prophecy_list">
      <div class="content-top">
        <div class="content-top-lift-title">预言机列表</div>
        <div class="top_op">
          <div class="content-top-lift">
            <div class="content-top-bottom-left">

              <div class="top_text" style="width: 100px;">数据生成时间:</div>
              <el-date-picker
                v-model="entityIn.startTime"
                type="datetime"
                placeholder="选择日期">
              </el-date-picker>
              <div class="top_text">到</div>
              <el-date-picker
                v-model="entityIn.endTime"
                type="datetime"
                placeholder="选择日期">
              </el-date-picker>
              <div class="top-right-input icon-search_suffix">
                <el-input
                  placeholder="可输入预言机名称或者消费者用户查询"
                  v-model="input"
                  @keyup.enter.native="getOracleMachineList">
                  <i slot="suffix" class="el-icon-search" @click="getOracleMachineList(true)"></i>
                </el-input>
              </div>
              <el-button type="primary" icon="el-icon-search" @click="getOracleMachineList(true)">查 询</el-button>

            </div>
          </div>
          <div class="content-top-right">

            <div class="top-right-button">
              <el-button icon="el-icon-plus" @click="newProphecy">新建预言机</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="content-body">
        <el-table
            :data="tableData"
            style="width: 100%"
            height="520px"
            stripe
        >
          <el-table-column label="操作"  width="400px">
            <template slot-scope="scope">
              <el-button
                size="mini"
                @click="handleLook(scope.$index, scope.row)">查看</el-button>
              <el-button
                size="mini"
                @click="handleDelete(scope.$index, scope.row)">删除</el-button>
              <el-button
                size="mini"
                @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              <el-button
                size="mini"
                @click="handleStop(scope.$index, scope.row)">{{ scope.row.oracleEnable==1?'启用':'禁用' }}</el-button>
              <el-button
                v-if="scope.row.fetchType== 1"
                size="mini"
                @click="handleTest(scope.$index, scope.row)">测试</el-button>
            </template>
          </el-table-column>
          <el-table-column
              label="预言机名称">
            <template slot-scope="scope">
              {{scope.row.oracleName||'无订阅'}}
            </template>
          </el-table-column>
          <el-table-column
              prop="tempId"
              label="预言机模板"
              >
          </el-table-column>
          <el-table-column
              prop="consumerName"
              label="消费者用户"
             >
          </el-table-column>
          <el-table-column
              prop="providerName"
              label="信源"
             >
          </el-table-column>
          <el-table-column
              label="创建时间"
          >
            <template slot-scope="scope">
              {{setDates(scope.row.createTime)}}
            </template>
          </el-table-column>

        </el-table>
        <div class="block table_pag">
          <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="entityIn.rows"
              background
              layout="total, prev, pager, next, sizes, jumper"
              :total="total">
          </el-pagination>
        </div>
      </div>
      <newProphecyDialog ref="newProphecyDialogRef" @Refresh="Refresh"></newProphecyDialog>
    </div>
</template>

<script>
import newProphecyDialog from './components/newProphecyDialog'
import * as api from "./api";
import {getFormatDates} from '../../utils/atuUtils.js'
import {OracleMachineTest} from "./api";
    export default {
      components:{
        newProphecyDialog
      },
        data(){
            return {
              currentPage: 1,
              input:'',
              tableData: [],
              total:null,
              entityIn:{
                "endTime": "",
                "filter": {},
                "oracleName": "",
                "order": "",
                "page": 1,
                "rows": 10,
                "sort": "",
                "startTime": ""
              },
            }
        },
        created(){
          this.getOracleMachineList()
        },
        mounted(){

        },
        methods: {
          setDates(val){
            return getFormatDates(val,'yyyy-mm-dd MM:mm:ss')
          },
          Refresh(){
            this.getOracleMachineList()
          },
          newProphecy(){
            this.$refs.newProphecyDialogRef.operationState = 0
            this.$refs.newProphecyDialogRef.Visible=true
          },
          handleEdit(index, row) {
            this.$refs.newProphecyDialogRef.operationState=1
            this.$refs.newProphecyDialogRef.form=row
            this.$refs.newProphecyDialogRef.Visible=true
          },
          handleDelete(index, row) {
            if(row.oracleEnable==1){
              this.$confirm(`此操作将永久删除${row.oracleName}, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                showClose:false,
                type:'warning'
              }).then(() => {
                api.delOracleMachine({"oracleId": row.oracleId}).then(res=>{
                  if(res.code!=0) return this.$message.warning(res.msg)
                  this.$message({
                    type: 'success',
                    message: '删除成功!'
                  });
                  this.Refresh()
                })
              }).catch(() => {

              });
            }else {
              this.$confirm('预言机仍在使用中，不能删除！', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                showClose:false
              }).then(() => {
              }).catch(() => {

              });
            }

          },
          //获取数据用户列表
          getOracleMachineList(search=false){
            // console.log(this.entityIn.endTime)
            // console.log(getFormatDates(this.entityIn.endTime))
            // return
            if(this.entityIn.startTime&&this.entityIn.endTime){
              if(new Date(this.entityIn.startTime).getTime()>=new Date(this.entityIn.endTime).getTime()){
                return this.$message.warning('结束时间不能小于开始时间！')
              }
            }
            if(search){
              this.entityIn.page=1
              this.currentPage = 1
            }
            if(this.entityIn.startTime){
              this.entityIn.startTime = this.setDates(this.entityIn.startTime)
            }
            if(this.entityIn.endTime){
              this.entityIn.endTime = this.setDates(this.entityIn.endTime)
            }
            api.getOracleMachineList(
                {...this.entityIn,oracleName:this.input}
            ).then(res=>{
              if(res.code!=0) return this.$message.warning(res.msg)
              this.tableData=res.result.rows||[]
              this.total=res.result.total

            })
          },
          handleStop(index, row) {
            let oracleEnable = null
            oracleEnable = row.oracleEnable==1?0:1
            api.offlineOracleMachine(
                {"oracleEnable": oracleEnable,
                  "oracleId": row.oracleId}
            ).then(res=>{
              if(res.code!=0) return this.$message.warning(res.msg)
              this.getOracleMachineList()
              this.$confirm('操作成功！', '提示', {
                confirmButtonText: '确定',
                showClose:false,
                showCancelButton:false
              }).then(() => {
              }).catch(() => {
              });

            })

          },
          handleLook(index, row) {
            this.$refs.newProphecyDialogRef.operationState=2
            this.$refs.newProphecyDialogRef.form=row
            this.$refs.newProphecyDialogRef.Visible=true
          },
          handleTest(index, row) {
            api.OracleMachineTest(row.oracleId).then(res=>{
              if(res.code==0){
                this.$confirm('测试成功！', '提示', {
                  confirmButtonText: '确定',
                  showClose:false,
                  showCancelButton:false
                }).then(() => {
                }).catch(() => {
                });
              }else{
                this.$confirm('测试失败！', '提示', {
                  confirmButtonText: '确定',
                  showClose:false,
                  showCancelButton:false
                }).then(() => {
                }).catch(() => {
                });
              }
            }).catch(err=>{

            })
          },
          handleSizeChange(val) {
            this.entityIn.rows =val
            this.getOracleMachineList()
          },
          handleCurrentChange(val) {
            this.entityIn.page = val
            this.currentPage =val
            this.getOracleMachineList()
          }
        },
    }
</script>

<style lang="less" scoped>
.prophecy_list{
  margin: 16px 14px;
  background: #ffffff;
  height: 706px;
  .content-top{
    .content-top-lift-title{
      padding-left: 30px;
      font-size: 18px;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }
    .top_op{
      display: flex;
      justify-content: space-between;
    }
    .icon-search_suffix{
      margin-left: 10px;
    }
    .content-top-right{
      display: flex;
      padding: 10px 20px 0 0;
      .top-right-input{
        margin-right: 12px;
      }
      .top-right-button{
        .el-button{

        }
      }

    }
    .content-top-lift{
      padding: 0 0 0 23px;
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      color: #333333;
      opacity: 1;
      .content-top-bottom-left{
        padding-top: 10px;
        display: flex;
      }
    }
  }
  .content-body{
    margin: 11px 17px 0 16px;
  }
  .table_pag{
    margin: 12px 16px 0 0;
    display: flex;
    justify-content: flex-end;
  }
}
</style>



