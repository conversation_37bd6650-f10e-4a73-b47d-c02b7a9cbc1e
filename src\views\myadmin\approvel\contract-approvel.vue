<template>
  <div class="contractApp">
    <Tabs :value="name" @on-click="clickTab">
      <TabPane label="待审批" name="name1">
        <p style="margin:10px 10px 15px 0px;">
          <Input class='bt1 width-input' placeholder="请输入合约名称或应用名称" style="vertical-align:baseline;" v-model="contractName" />
          <Select class='bt1 width-input' v-model="userId" placeholder="请选择租户" multiple :max-tag-count="2">
            <Option v-for="item in userListData" :value="item.tenantId" :key="item.tenantId">{{ item.tenantName }}</Option>
          </Select>
          <Button class='bt1' icon="ios-search" type="primary" @click="searchList">查询</Button>
          <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting">重置</Button>
        </p>
        <edit-table-mul :columns="columns" v-model="tableData"></edit-table-mul>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px" />
      </TabPane>
      <TabPane label="已审批" name="name2">
        <p style="margin:10px 10px 15px 0px;">
          <Input class='bt1 width-input' placeholder="请输入合约名称或应用名称" style="vertical-align:baseline;" v-model="contractName2" />
          <Select class='bt1 width-input' v-model="userId2" placeholder="请选择租户" multiple :max-tag-count="2">
            <Option v-for="item in userListData" :value="item.tenantId" :key="item.tenantId">{{ item.tenantName }}</Option>
          </Select>
          <Select class='bt1 width-input' v-model="status2" placeholder="请选择状态" multiple :max-tag-count="2">
            <Option v-for="item in options" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          <Button class='bt1' icon="ios-search" type="primary" @click="searchList2">查询</Button>
          <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting2">重置</Button>
        </p>
        <Table :columns="columns2" :data="tableData2">
          <template slot-scope="{ row, index }" slot="action">
            <Button type="text" size="small" style="marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF" @click="detailbtn(index)">详情</Button>

            <Button type="text" size="small" :disabled="hasEditPermission || !((row.statusMsg === '部署失败' || row.statusMsg === '审批通过')&&row.show===false)" :style="hasEditPermission?'color:#c5c8ce;border:1px solid #c5c8ce': ( row.statusMsg === '部署失败' || row.statusMsg === '审批通过')&&row.show===false ? 'color:#3D73EF;border:1px solid #3D73EF' : 'color:#c5c8ce;border:1px solid #c5c8ce'" @click="appretry(row)">重试<span
                v-if="row.retrySurplusTime!==0&&row.show===true">({{row.retrySurplusTime}})</span></Button>
          </template>
        </Table>
        <Page :total="tablePageParam2.pagetotal" :current.sync="tablePageParam2.pageIndex" @on-change="pageChange2" :page-size="tablePageParam2.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange2" style="text-align:right;line-height:40px" />
      </TabPane>
    </Tabs>
    <Modal v-model="modalAppro" :title="appModal" width="900" :draggable="true" :height="getHeight" sticky :mask-closable="false">
      <Card dis-hover>
        <p class="title bs"> 合约基础信息 </p>
        <span>应用名称：{{ arrDetails.contractReadableName }}</span><br>
        <span>合约名称：{{ arrDetails.contractName }}</span><br>
        <span>合约语言：{{ this.languageType==='JS'?'Java Script': this.languageType}}</span><br>
        <span style="word-break:break-all;white-space: pre-wrap;">应用简介：{{ arrDetails.brief }}</span>
      </Card>
      <Card style="margin:10px 0">
        <p class="title bs">部署版本</p>
        <span>部署版本号：{{ arrDetails.uploadVersion }}</span><br>
        <span>上传时间：{{ arrDetails.uploadTime }}</span><br>
        <span>版本备注：{{ arrDetails.versionBrief }}</span><br>
        <span>合约链账户：{{ arrDetails.chainAccountName }}</span><br>
        <span>部署目标链：{{ arrDetails.chainName }}</span><br>
        <!-- <span>最近一次审批结果：{{ arrDetails.lastAuditStatus }}</span><br> -->
      </Card>
      <div v-if="isSingleCpp=='1'">
        <Collapse v-model="filepanel" simple accordion :key="transferKey1" v-if="this.languageType==='C++'">
          <Panel name="codeDetails1" v-if="arrDetails.cppFileName && arrDetails.cppFileName !== undefined">
            cpp文件名：{{ arrDetails.cppFileName }}
            <div slot="content">
              <textarea class="textarea-style" v-if="arrChainCode.cppObj.fileContent" v-html="arrChainCode.cppObj.fileContent" readonly @scroll="handleScroll($event, 'cpp', alertArr.cppName)"> </textarea>
              <Spin v-else>
                <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                <div>{{loadingMsg}}</div>
              </Spin>
            </div>
          </Panel>
          <Panel :name="item" v-for="(item,index) in alertArr.hppNames &&  alertArr.hppNames.length>0? alertArr.hppNames:[]" :key="item" v-show="arrDetails.hppFileNames[index]">
            hpp文件名：{{arrDetails.hppFileNames[index]}}
            <div slot="content">
              <textarea v-if="arrChainCode.hppObj[item]&&arrChainCode.hppObj[item].fileContent" class="textarea-style" v-html="arrChainCode.hppObj[item].fileContent" readonly @scroll="handleScroll($event, 'hpp', arrDetails.hppFileNames[index])"></textarea>
              <Spin v-else>
                <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                <div>{{loadingMsg}}</div>
              </Spin>
            </div>
          </Panel>
        </Collapse>
        <!-- js合约源码 -->
        <Collapse simple accordion v-else>
          <Panel :name="arrDetails.jsFileName" v-if="arrDetails.jsFileName">
            JavaScript文件名：{{arrDetails.jsFileName}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContentjs.fileContent" readonly @scroll="handScrolljs($event, 'js')"></textarea>
            </p>
          </Panel>
          <Panel :name="arrDetails.abiFileName" v-if="arrDetails.abiFileName">
            abi文件名：{{arrDetails.abiFileName}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContentabi.fileContent" readonly @scroll="handScrolljs($event, 'abi')"></textarea>
            </p>
          </Panel>
        </Collapse>
      </div>
      <div v-else>
        <Layout>
          <Sider hide-trigger :style="{background: '#fff'}">
            <Menu theme="light" width="auto" :open-names="['1']">
              <Submenu :name="key" v-for="(item,key) in cppsTitle" :key="key">
                <template slot="title">
                  <Icon type="ios-folder"></Icon>
                  {{key}}
                </template>
                <MenuItem :name="items.fileName" v-for="(items,i) in item" :key="i" @click.native="clickCpps(items.content)">{{items.fileName}}</MenuItem>
              </Submenu>
            </Menu>
          </Sider>
          <Content :style="{padding: ' 0 24px', minHeight: '280px', background: '#fff'}">
            <p>
            <p v-if=""></p>
            <textarea class="textarea-style" v-html="cppContent" readonly @scroll="handScrolljs($event, 'abi')"></textarea>
            </p>
          </Content>
        </Layout>
      </div>
      <Card dis-hove v-if="this.interfacDoc">
        <p class="title bs"> API接口文档 </p>
        <span style="line-height:30px">API接口文档：<a @click="fileDowneApi">{{interfacDoc.fileName}}</a><br></span><br>
      </Card>
      <Card dis-hover>
        <p class="title bs"> 安全扫描报告 </p>
        <span v-for="(item,index) in securityList2" :key="index" style="line-height:30px">安全扫描报告：<a @click="fileDowne(item)">{{item.fileName}}</a><br></span><br>
      </Card>
      <Card style="margin-top:5px;overflow:hidden" dis-hover class="contract-card">
        <p class="title bs"> 运维信息 </p>
        <div v-if="arrDetails.ops && arrDetails.ops !== undefined">
          <p style="line-height:30px">
            合约类型：{{ arrDetails.ops.contractTypeDesc }}
          </p>
          <p style="line-height:30px"> TPS预估：{{ arrDetails.ops.tps }} </p>
          <p style="line-height:30px"> 运维联系人：
            <span v-if="arrDetails.ops.opsLinkman && arrDetails.ops.opsLinkman.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ arrDetails.ops.opsLinkman.tenantName }}</span>
            <span v-if="arrDetails.ops.opsLinkman && arrDetails.ops.opsLinkman.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ arrDetails.ops.opsLinkman.name }}</span>
            <span v-if="arrDetails.ops.opsLinkman && arrDetails.ops.opsLinkman.phone"><i class="ri-smartphone-line"></i>{{ arrDetails.ops.opsLinkman.phone }}</span>
          </p>
          <p style="vertical-align: top;line-height:30px"> 需求联系人：
            <span v-if="arrDetails.ops.demandSide.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ arrDetails.ops.demandSide.tenantName }}</span>
            <span v-if="arrDetails.ops.demandSide.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ arrDetails.ops.demandSide.name }}</span>
            <span v-if="arrDetails.ops.demandSide.phone"><i class="ri-smartphone-line"></i>{{ arrDetails.ops.demandSide.phone }}</span>
          </p>
          <p style="line-height:30px">
            <span style="vertical-align:top;line-height:30px">调用联系人：</span>
          <ul style="display:inline-block;line-height:30px;margin-left:4px;" v-if="arrDetails.ops.caller && arrDetails.ops.caller !==undefined">
            <li v-for="item in arrDetails.ops.caller" :key="item.tenantName + 'ss'">
              <span v-if="item.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ item.tenantName }}</span>
              <span v-if="item.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ item.name }}</span>
              <span v-if="item.phone"><i class="ri-smartphone-line"></i>{{ item.phone }}</span>
            </li>
          </ul>
          </p>
        </div>
      </Card>
      <div slot="footer">
        <Card dis-hover>
          <p class="title" style="text-align:left;">审批意见</p>
          <i-Form :model="formItem" :rules="formItemRule" :label-width="80" ref="formItem" style="text-align:left;">
            <FormItem style="margin:1px;padding:1px;" label="是否同意" prop="approStatus">
              <RadioGroup v-model="formItem.approStatus">
                <Radio label="APPROVED">同意</Radio>
                <Radio label="REJECT">不同意</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem style="padding:20px 0 10px 0;" label="审批说明" prop="auditRemark" v-show="formItem.approStatus==='REJECT'">
              <Input v-model="formItem.auditRemark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="说明" :maxlength="30" show-word-limit></Input>
            </FormItem>
            <FormItem style="padding:20px 0 10px 0;" label="审批说明" v-show="formItem.approStatus!=='REJECT'">
              <Input v-model="formItem.auditRemark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="说明（选填）" :maxlength="30" show-word-limit></Input>
            </FormItem>
            <!-- 附件 -->
            <FormItem label="上传附件" style="margin:0px">
              <Upload multiple action="" :before-upload="handleUpload" :accept="'.bmp,.jpg,.jpeg,.png,.tif,.gif,.doc,.docx,.txt,.pdf,.zip,.tar,.xlsx,.xls'" :format="['bmp','jpg','jpeg','png','tif','gif','doc','docx','txt','pdf','zip','tar','xlsx','xls']">
                <a>
                  <Icon type="ios-link" />添加附件
                </a>
                <!-- <Button ></Button> -->
              </Upload>
            </FormItem>

            <!-- </div> -->
            <div v-show="allfile.length > 0 ? true : false" style="font-size:8px;background-color: #abccf052;padding:5px 0 5px 5px;margin-bottom: 5px;" v-for="(item, i) in allfile" :key="i">
              <div class="upload-file">
                <Icon type="md-list-box" style="color:#57a3f3;margin-top:-5px;" size="18" />
                {{item.name}}
                <span style="color:#bdbbbb;margin-left:5px;">({{getFileSize(item.size)}})</span>
                &nbsp;&nbsp;&nbsp;
                <span style="color:#57a3f3;cursor:pointer;font-size:8px;" @click="fileDel(item.name)">删除</span>
              </div>
            </div>
          </i-Form>
          <Button type="primary" @click="ok('formItem')" :loading="loadingStatus">{{ loadingStatus ? "审批中" : "提交" }}</Button>
          <Button type="default" @click="cancelApp('formItem')">取消</Button>
        </Card>
      </div>
    </Modal>
    <Modal v-model="modalDetail" :title="appModal" width="900" :draggable="true" :mask-closable="false" sticky>
      <Card dis-hover>
        <p class="title bs"> 合约基础信息 </p>
        <span style="line-height:30px">应用名称：{{ arrDetails.contractReadableName }}</span><br>
        <span style="line-height:30px">合约名称：{{ arrDetails.contractName }}</span><br>
        <span>合约语言：{{ this.languageType==='JS'?'Java Script': this.languageType}}</span><br>
        <span style="line-height:30px;word-break:break-all;white-space: pre-wrap;">应用简介：{{ arrDetails.brief }}</span>
      </Card>
      <Card style="margin-top:5px;" dis-hover>
        <p class="title bs">部署版本</p>
        <span style="line-height:30px">部署版本号：{{ arrDetails.uploadVersion }}</span><br>
        <span style="line-height:30px">上传时间：{{ arrDetails.uploadTime }}</span><br>
        <span style="line-height:30px">版本备注：{{ arrDetails.versionBrief }}</span><br>
        <span style="line-height:30px">合约链账户：{{ arrDetails.chainAccountName }}</span><br>
        <span style="line-height:30px">部署目标链：{{ arrDetails.chainName }}</span><br>
        <!-- <span>最近一次审批结果：{{ arrDetails.lastAuditStatus }}</span><br>    -->
      </Card>
      <br>
      <div v-if="isSingleCpp=='1'">
        <Collapse v-model="filepanelD" simple accordion :key="transferKey2" v-if="this.languageType==='C++'">
          <Panel name="codeDetls1" v-if="arrDetails.cppFileName && arrDetails.cppFileName !== undefined">
            cpp文件名：{{ arrDetails.cppFileName }}
            <div slot="content">
              <Spin v-if="cppTopLoading">
                <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                <div>上一页加载中。。。</div>
              </Spin>
              <textarea class="textarea-style" v-if="arrChainCode.cppObj.fileContent" v-html="arrChainCode.cppObj.fileContent" readonly @scroll="handleScroll($event, 'cpp', alertArr.cppName)"> </textarea>
              <Spin v-else>
                <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                <div>{{loadingMsg}}</div>
              </Spin>
              <Spin v-if="cppBottomLoading">
                <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                <div>下一页加载中。。。</div>
              </Spin>
            </div>
          </Panel>
          <Panel :name="item" v-for="(item,index) in alertArr.hppNames &&  alertArr.hppNames.length>0? alertArr.hppNames:[]" :key="item" v-show="arrDetails.hppFileNames[index]">
            hpp文件名：{{ arrDetails.hppFileNames[index] }}
            <div slot="content">
              <textarea v-if="arrChainCode.hppObj[item]&&arrChainCode.hppObj[item].fileContent" class="textarea-style" v-html="arrChainCode.hppObj[item].fileContent" readonly @scroll="handleScroll($event, 'hpp', item)"></textarea>
              <Spin v-else>
                <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
                <div>{{loadingMsg}}</div>
              </Spin>
            </div>
          </Panel>

        </Collapse>
        <!-- js合约源码 -->
        <Collapse simple accordion v-else>
          <Panel :name="arrDetails.jsFileName" v-if="arrDetails.jsFileName">
            JavaScript文件名：{{arrDetails.jsFileName}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContentjs.fileContent" readonly @scroll="handScrolljs($event, 'js')"></textarea>
            </p>
          </Panel>
          <Panel :name="arrDetails.abiFileName" v-if="arrDetails.abiFileName">
            abi文件名：{{arrDetails.abiFileName}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContentabi.fileContent" readonly @scroll="handScrolljs($event, 'abi')"></textarea>
            </p>
          </Panel>
        </Collapse>
      </div>
      <div v-else>
        <Layout>
          <Sider hide-trigger :style="{background: '#fff'}">
            <Menu theme="light" width="auto" :open-names="['1']">
              <Submenu :name="key" v-for="(item,key) in cppsTitle" :key="key">
                <template slot="title">
                  <Icon type="ios-folder"></Icon>
                  {{key}}
                </template>
                <MenuItem :name="items.fileName" v-for="(items,i) in item" :key="i" @click.native="clickCpps(items.content)">{{items.fileName}}</MenuItem>
              </Submenu>
            </Menu>
          </Sider>
          <Content :style="{padding: ' 0 24px', minHeight: '280px', background: '#fff'}">
            <p>

              <textarea class="textarea-style" v-html="cppContent" readonly @scroll="handScrolljs($event, 'abi')"></textarea>
            </p>
          </Content>
        </Layout>
      </div>
      <Card dis-hove v-if="this.interfacDoc">
        <p class="title bs"> API接口文档 </p>
        <span style="line-height:30px">API接口文档：<a @click="fileDowneApi">{{interfacDoc.fileName}}</a><br></span><br>
      </Card>
      <Card dis-hover style="margin-top:10px">
        <p class="title bs"> 安全扫描报告 </p>
        <span v-for="(item,index) in securityList" :key="index" style="line-height:30px">安全扫描报告：<a @click="fileDowne(item)">{{item.fileName}}</a><br></span><br>
      </Card>
      <Card style="margin-top:5px; overflow:hidden" dis-hover>
        <p class="title bs">运维信息</p>
        <div v-if="arrDetails.ops && arrDetails.ops !== undefined">
          <p style="line-height:30px">
            合约类型：{{ arrDetails.ops.contractTypeDesc }}
          </p>
          <p style="line-height:30px"> TPS预估：{{ arrDetails.ops.tps }} </p>
          <p style="line-height:30px"> 运维联系人：
            <span v-if="arrDetails.ops.opsLinkman && arrDetails.ops.opsLinkman.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ arrDetails.ops.opsLinkman.tenantName }}</span>
            <span v-if="arrDetails.ops.opsLinkman && arrDetails.ops.opsLinkman.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ arrDetails.ops.opsLinkman.name }}</span>
            <span v-if="arrDetails.ops.opsLinkman && arrDetails.ops.opsLinkman.phone"><i class="ri-smartphone-line"></i>{{ arrDetails.ops.opsLinkman.phone }}</span>
          </p>
          <p style="line-height:30px"> 需求联系人：
            <span v-if="arrDetails.ops.demandSide.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ arrDetails.ops.demandSide.tenantName }}</span>
            <span v-if="arrDetails.ops.demandSide.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ arrDetails.ops.demandSide.name }}</span>
            <span v-if="arrDetails.ops.demandSide.phone"><i class="ri-smartphone-line"></i>{{ arrDetails.ops.demandSide.phone }}</span>
          </p>
          <p style="line-height:30px">
            <span style="vertical-align: top;line-height:30px">调用联系人：</span>
          <ul style="display:inline-block;line-height:30px;margin-left:4px;" v-if="arrDetails.ops.caller && arrDetails.ops.caller !==undefined">
            <li v-for="item in arrDetails.ops.caller" :key="item.tenantName + 's'">
              <span v-if="item.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ item.tenantName }}</span>
              <span v-if="item.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ item.name }}</span>
              <span v-if="item.phone"><i class="ri-smartphone-line"></i>{{ item.phone }}</span>
            </li>
          </ul>
          </p>
        </div>
      </Card>
      <Card style="margin-top:5px; overflow:hidden" dis-hover v-if="this.deployStatus==='部署失败'">
        <p class="title bs"> 失败原因 </p>
        <p v-if="whyDetail.code===''||whyDetail.code===null">{{whyDetail.errorInfo===null?'': whyDetail.errorInfo }}</p>
        <div v-else>
          <span style="line-height:30px">编码信息：{{whyDetail.code}}</span><br>
          <span style="line-height:30px">失败原因：{{ !isNaN(whyDetail.code)?whyDetail.what+'，请联系管理员':whyDetail.what  }}</span><br>
          <span style="line-height:30px">方法：{{whyDetail.name}}</span><br>
        </div>
      </Card>
      <Card style="margin-top:5px; overflow:hidden" dis-hover v-else-if="this.deployStatus==='部署成功'">
        <p class="title bs"> 成功信息</p>
        <span v-if="whyDetail===null" style="line-height:30px"></span>
        <span v-else style="line-height:30px">交易ID：{{whyDetail.transactionId===null?'':whyDetail.transactionId}}</span><br>
      </Card>
      <div slot="footer" class="bg1" :bordered="false" v-show="arrDetails.lastAuditStatus === 'APPROVED'">
        <p class="title" style="text-align:left;">审批结果</p>
        <div class="divS">
          <p style="text-align:left; margin-left:5px; margin-top:15px;">结果:<span class="resultS">{{ arrDetails.lastAuditStatusDesc }}</span></p>
          <p style="text-align:left; margin-left:5px; margin-top:5px;display: flex;">
            <span> 说明:</span>
            <span style=" white-space: pre-wrap;display: inline-block;word-wrap: break-word;height: 40px;overflow-y: scroll;width: 80%;margin-left: 7px;">{{ arrDetails.auditRemark }}</span>
          </p>
          <p v-show="arrDetails.storePath!==null" style="text-align:left;margin-left:5px;margin-top:5px;">附件：<a @click="downfile">
              <Icon type="ios-link" />下载查看附件
            </a></p>
        </div>
        <Button type="default" @click="cancelDet('formItem')">返回</Button>
      </div>
      <div slot="footer" class="bg2" :bordered="false" v-show="arrDetails.lastAuditStatus !== 'APPROVED'">
        <p class="title" style="text-align:left;">审批结果</p>
        <div class="divS">
          <p style="text-align:left;margin-left:5px;margin-top:10px;">结果:<span class="resultF">{{ arrDetails.lastAuditStatusDesc }}</span></p>
          <p style="text-align:left;margin-left:5px;margin-top:5px;display: flex;">
            <span>说明:</span>
            <span style="white-space: pre-wrap;display: inline-block;word-wrap: break-word;height: 40px;overflow-y: scroll;width: 80%;margin-left: 7px;">{{ arrDetails.auditRemark }}</span>
          </p>
          <p v-show="arrDetails.storePath!==null" style="text-align:left;margin-left:5px;margin-top:5px;">附件：<a @click="downfile">
              <Icon type="ios-link" />下载查看附件
            </a></p>
        </div>
        <Button type="default" @click="cancelDet('formItem')">返回</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { getContractsData, getUserData, getContrApprDetails, contractApprovel, getContractChaincode, getChaincode } from '@/api/data'
import { getDownsecurity, ExaminationApproval, getauditdownloadFile } from '@/api/contract'
import EditTableMul from '_c/edit-table-mul'
import { localRead } from '@/lib/util'
export default {
  name: 'contract_approvel',
  components: {
    EditTableMul
  },
  data () {
    return {
      typeList: ['bmp', 'jpg', 'jpeg', 'png', 'tif', 'gif', 'doc', 'docx', 'txt', 'pdf', 'zip', 'tar', 'xlsx', 'xls'],
      size: 10 * 1024 * 1024,
      allfile: [],
      fileSise: [],
      deployId: '',
      // 以上是附件
      loadingStatus: false,
      timerStamp: null,
      name: this.$route.params.tabs || 'name1',
      cppBottomLoading: false,
      cppTopLoading: false,
      hppBottomLoading: {},
      hppTopLoading: {},
      hppPageObj: {},
      loadingMsg: 'Loading',
      transferKey1: 0,
      transferKey2: 0,
      chainCodePageParam: {
        cpp: { pagetotal: 0, pageSize: 30, pageIndex: 1 },
        hpp: { pagetotal: 0, pageSize: 30, pageIndex: 1 }
      },
      codeTotalPages: { cpp: 0, hpp: {} },
      arrChainCode: { cppObj: {}, hppObj: {}, abiObj: {} },
      alertArr: { contractId: '', cppName: '', hppName: '', uploadVersion: '' },
      ops: { 'contractType': '', 'contractTypeDesc': '', 'tps': 0, 'opsLinkman': { 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }, 'demandSide': { 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }, 'caller': [{ 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }] },
      modalAppro: false,
      modalDetail: false,
      formItem: {
        auditRemark: '',
        approStatus: ''
      },
      formItemRule: {
        approStatus: [{ required: true, message: '请选择是否同意！', trigger: 'change' }],
        auditRemark: [{ required: true, message: '请填写审批说明！', trigger: 'blur' }]
      },
      filepanel: 'codeDetails1',
      filepanelD: 'codeDetls1',
      bizId: '',
      appModal: '审核部署',
      approStatus: '',
      contype: '',
      contractName: '',
      contractName2: '',
      userIdStr: '',
      status2: [],
      statusStr: '',
      statusStr2: '',
      userId: [],
      userId2: [],
      userListData: [],
      depoyId: 0,
      depoyId2: 0,
      options: [{
        value: 'HAS_APPROVED',
        label: '审核通过'
      }, {
        value: 'AUDIT_REJECT',
        label: '审核拒绝'
      }, {
        value: 'SUCCESS',
        label: '部署成功'
      }, {
        value: 'DEPLOY_FAILED',
        label: '部署失败'
      }],
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      tablePageParam2: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        { key: 'contractName', title: '合约名称', tooltip: true },
        { key: 'readableName', title: '应用名称', tooltip: true },
        { key: 'chainAccountName', title: '链账户', tooltip: true },
        { key: 'submitName', title: '平台账号', tooltip: true },
        { key: 'tenantName', title: '租户', tooltip: true },
        { key: 'chainName', title: '链名称', tooltip: true },
        { key: 'submitTime', title: '提交时间', width: '170px', tooltip: true },
        // { key: 'auditName',
        //   title: '审批人',
        //   width: '80px',
        //   tooltip: true,
        //   render: (h, params) => {
        //     return h('span', params.row.auditName === null ? '/' : params.row.auditName)
        //   }
        // },
        {
          key: 'statusMsg',
          title: '状态',
          minWidth: 130,
          tooltip: true,
          render: (h, params) => {
            const row = params.row
            const color = row.statusMsg === '等待管理员审批' ? 'primary' : row.statusMsg === '审批通过' ? '#2db7f5' : row.statusMsg === '审批驳回' ? 'error' : row.statusMsg === '部署成功' ? 'success' : '#515a6e'
            // const text = row.status === 1 ? 'Working' : row.status === 2 ? 'Success' : 'Fail';
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, row.statusMsg)
          }
        },
        {
          key: 'action',
          title: '操作',
          align: 'left',
          // fixed: 'right',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                style: this.buttonStyle,
                on: {
                  click: () => {
                    this.ApproDetails(params.index)
                  }
                }
              }, '审批')
            ])
          }
        }
      ],
      arrDetails: {},
      columns2: [
        { key: 'contractName', title: '合约名称', tooltip: true },
        { key: 'readableName', title: '应用名称', minWidth: 10, tooltip: true },
        { key: 'chainAccountName', title: '链账户', tooltip: true },
        { key: 'submitName', title: '平台账号', tooltip: true },
        { key: 'tenantName', title: '租户', minWidth: 1, tooltip: true },
        { key: 'chainName', title: '链名称', tooltip: true },
        { key: 'submitTime', title: '提交时间', width: '170px', tooltip: true },
        { key: 'auditName', title: '审批人', width: '80px', tooltip: true },
        {
          key: 'statusMsg',
          title: '状态',
          // width: '200px',
          minWidth: 120,
          tooltip: true,
          render: (h, params) => {
            const row = params.row
            const color = row.statusMsg === '等待管理员审批' ? 'primary' : row.statusMsg === '审批通过' ? '#2db7f5' : row.statusMsg === '审批驳回' ? 'error' : row.statusMsg === '部署成功' ? 'success' : '#515a6e'
            // const text = row.status === 1 ? 'Working' : row.status === 2 ? 'Success' : 'Fail';
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, row.statusMsg)
          }
        },
        {
          slot: 'action',
          title: '操作',
          // fixed: 'right',
          align: 'left',
          width: 150
        }
      ],
      tableData: [],
      tableData2: [],
      securityList: [],
      securityList2: [],
      deployStatus: '',
      whyDetail: {},
      // 以下是js新添加
      languageType: '',
      jsFileNamelist: { jsFileName: '', abiFileName: '', contractId: '', uploadVersion: '' },
      CollContentjs: {},
      CollContentabi: {},
      cppContent: '请选择要看的源码文件',
      cppsTitle: '',
      isSingleCpp: '',
      interfacDoc: '',
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  computed: {
    getHeight: function (value) {
      if (this.tableData.length === 0) {
        return 90
      } else if (this.tableData.length < 5) {
        return 90 + 48 * (this.tableData.length - 1)
      } else {
        return 90 + 48 * 5
      }
    },


    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }

    },
  },
  methods: {
    // 下载附件
    downfile () {
      getauditdownloadFile(this.deployId).then(res => {
        let blob = new Blob([res], { type: 'application/zip' })
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = this.arrDetails.chainAccountName
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    handleUpload (file) {
      console.log(file)
      // let filename = file.name.replace(/.+\./, '')
      if (file.size < this.size) {
        let typeString = file.name.split('.')[1]
        let flag = false
        this.typeList.forEach(val => {
          if ((typeString && typeString.indexOf(val) !== -1) || file.name.indexOf(`.${val}`) !== -1) {
            flag = true
          }
        })
        if (!flag) {
          this.msgInfo('error', '附件格式不正确,只能为bmp,jpg,jpeg,png,tif,gif,doc,docx,txt,pdf,zip,tar,xlsx,xls', true)
          return
        }
        let fileName = this.allfile.some((item) => item.name === file.name)
        if (fileName) {
          this.msgInfo('error', '文件名称不允许重复', true)
        } else {
          this.allfile.push(file)
          this.fileSise.push({ size: file.size, name: file.name })
          // console.log(this.allfile)
        }
      } else {
        this.msgInfo('error', '附件大小不能超过10M', true)
      }

      return false
    },
    fileDel (name) {
      this.allfile = this.allfile.filter(item => item.name !== name)
      this.fileSise = this.allfile.filter(item => item.name !== name)
    },
    getFileSize (value) {
      if (value < 1024) {
        return value + 'B'
      } else if (value / (1024 * 1024) < 1) {
        return (value / 1024).toFixed(2) + 'KB'
      } else {
        return (value / 1024 / 1024).toFixed(2) + 'MB'
      }
    },
    handScrolljs (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    // // js点击折叠面板事件
    // colldata (key) {
    //   if (key[0]) {
    //     this.codeData.fileName = key[0]
    //     getChaincode(this.codeData).then(res => {
    //       if (res.code === '00000') {
    //         this.CollContent = res.data
    //       }
    //     }).catch((error) => {
    //       this.msgInfo('error', error.message, true)
    //     })
    //   }
    // },
    // 重试
    appretry (value) {
      let data = {
        bizId: value.bizId,
        type: 'CONTRACT_DEPLOY',
        languageType: value.languageType
      }
      ExaminationApproval(data).then(res => {
        if (res.code === '00000') {
          this.msgInfo('info', res.message, true)
          this.getTableData2()
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 下载安全报告
    fileDowne (data) {
      console.log(data)
      getDownsecurity(data.fileName, data.path).then(res => {
        let blob = new Blob([res])
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = data.fileName
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    fileDowneApi () {
      // console.log(data)
      getDownsecurity(this.interfacDoc.fileName, this.interfacDoc.path).then(res => {
        let blob = new Blob([res])
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = this.interfacDoc.fileName
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    clickTab (name) {
      if (name === 'name1') {
        this.resetting()
      }
    },
    handleScroll (e, fileType, name) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        // console.log('到底了====', e.srcElement.scrollTop + e.srcElement.offsetHeight, e.srcElement.scrollHeight)
        let hppFlag = false
        if (name && fileType === 'hpp') {
          this.chainCodePageParam[fileType].pageIndex = this.hppPageObj[name]
          if (parseInt(this.chainCodePageParam[fileType].pageIndex) < parseInt(this.codeTotalPages[fileType][name])) {
            this.hppBottomLoading[name] = true
            hppFlag = true
          }
        }
        if (parseInt(this.chainCodePageParam[fileType].pageIndex) < parseInt(this.codeTotalPages[fileType]) || hppFlag) {
          if (fileType === 'cpp') {
            this.cppBottomLoading = true
          } else {
            this.hppBottomLoading[name] = true
          }
          return new Promise(resolve => {
            clearTimeout(this.timer)
            this.timer = setTimeout(() => {
              if (name) {
                ++this.hppPageObj[name]
                this.chainCodePageParam[fileType].pageIndex = this.hppPageObj[name]
              } else {
                ++this.chainCodePageParam[fileType].pageIndex
              }
              this.getCode(name, fileType, 'bottom')
              resolve()
            }, 200)
          })
        } else {
          // let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight
          // if (height > 1 && height < 2) {
          //   this.msgInfo('info', '到底了！', true)
          // }
          clearTimeout(this.timerStamp)
          let that = this
          this.timerStamp = setTimeout(() => {
            let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
            if (height < 3 && height > 0) {
              that.msgInfo('info', '到底了！', true)
            }
          }, 500)
        }
      } else if (e.srcElement.scrollTop === 0) {
        // this.msgInfo('info', '到顶了！', true)
        if (parseInt(this.chainCodePageParam[fileType].pageIndex) !== 1) {
          if (fileType === 'cpp') {
            this.cppTopLoading = true
          } else {
            this.hppTopLoading[name] = true
          }
          return new Promise(resolve => {
            clearTimeout(this.timer)
            this.timer = setTimeout(() => {
              --this.chainCodePageParam[fileType].pageIndex
              if (name) {
                --this.hppPageObj[name]
                this.chainCodePageParam[fileType].pageIndex = this.hppPageObj[name] || 1
              } else {
                --this.chainCodePageParam[fileType].pageIndex
              }
              this.getCode(name, fileType, 'top')
              resolve()
            }, 200)
          })
        } else {
          this.msgInfo('info', '已到首页！', true)
        }
      }
    },
    init () {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.formItem = {
        auditRemark: '',
        approStatus: ''
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    pageChange2 (index) {
      this.tablePageParam2.pageIndex = index
      this.getTableData2()
    },
    pageSizeChange2 (index) {
      this.tablePageParam2.pageSize = index
      this.getTableData2()
    },
    getTableData () {
      // 查询待审核状态的数据
      this.statusStr = 'WAIT_APPROVED'

      this.userIdStr = ''
      for (var j = 0; j < this.userId.length; j++) {
        if (j < this.userId.length - 1) {
          this.userIdStr += this.userId[j] + ','
        } else {
          this.userIdStr += this.userId[j]
        }
      }
      getContractsData(this.tablePageParam, this.contractName, this.userIdStr, this.statusStr).then(res => {
        if (res.code === '00000') {
          this.tableData = res.data.records

          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getTableData2 () {
      // status数组拼装成“，”连接的字符串statusStr,已审核的所有数据
      this.statusStr2 = ''
      if (this.status2.length === 0) {
        this.statusStr2 = 'HAS_APPROVED,AUDIT_REJECT,SUCCESS,DEPLOY_FAILED'
      } else {
        for (var i = 0; i < this.status2.length; i++) {
          if (i < this.status2.length - 1) {
            this.statusStr2 += this.status2[i] + ','
          } else {
            this.statusStr2 += this.status2[i]
          }
        }
      }
      this.userIdStr2 = ''
      for (var j = 0; j < this.userId2.length; j++) {
        if (j < this.userId2.length - 1) {
          this.userIdStr2 += this.userId2[j] + ','
        } else {
          this.userIdStr2 += this.userId2[j]
        }
      }
      getContractsData(this.tablePageParam2, this.contractName2, this.userIdStr2, this.statusStr2).then(res => {
        if (res.code === '00000') {
          this.tableData2 = res.data.records
          this.tablePageParam2 = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          // this.tableData2.map((item) => {
          //   this.$set(item, 'show', false)
          // })
          this.tableData2.forEach((item) => {
            this.$set(item, 'show', false)
            if (item.retrySurplusTime !== null) {
              this.cratetimer(item)
            }
          })
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    cratetimer (item) {
      // let TIME_COUNT = time
      // if (!this.timer) {
      // thicount = item.retrySurplusTime
      // item.show = false
      let timer = setInterval(() => {
        if (item.retrySurplusTime > 0) {
          item.retrySurplusTime--
          item.show = true
        } else {
          item.show = false
          // this.getTableData2()
          clearInterval(timer)
          // this.timer = null
        }
      }, 1000)
      // }
    },
    searchList () { this.getTableData() },
    searchList2 () { this.getTableData2() },
    // 查询租户下拉框
    searchUserList () {
      getUserData(0).then(res => {
        if (res.code === '00000') {
          this.userListData = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 审批弹窗
    ApproDetails (index) {
      // console.log('进入弹窗函数')
      // 状态和审批描述置空(X掉弹窗后，置空上一次的操作。)
      this.formItem.approStatus = ''
      this.formItem.auditRemark = ''
      this.bizId = this.tableData[index].bizId
      this.loadingMsg = 'Loading'
      this.cppBottomLoading = false
      this.cppTopLoading = false
      this.hppBottomLoading = {}
      this.hppTopLoading = {}
      this.languageType = this.tableData[index].languageType

      getContrApprDetails(this.tableData[index].deployId).then(res => {
        if (res.code === '00000') {
          ++this.transferKey1
          this.modalAppro = true
          this.arrDetails = res.data
          this.securityList2 = res.data.securityScanReportFile ? res.data.securityScanReportFile : "" // 安全报告
          this.isSingleCpp = res.data.isSingleCpp
          this.cppsTitle = res.data.fileContent ? res.data.fileContent : ''
          this.interfacDoc = res.data.interfacDoc ? res.data.interfacDoc : "" // api接口文档
          // 调用查询合约源码接口
          if (this.languageType === 'C++') {
            this.codeTotalPages.cpp = 0
            this.codeTotalPages.hpp = {}
            this.arrChainCode.cppObj = {}
            this.arrChainCode.hppObj = {}
            this.alertArr.contractId = res.data.contractId
            this.alertArr.cppName = res.data.cppFileName
            this.alertArr.hppNames = res.data.hppFileNames.map(val => val !== null ? val : '')
            this.alertArr.uploadVersion = res.data.uploadVersion
            if (res.data.cppFileName && res.data.cppFileName !== 'null' && this.isSingleCpp !== '0') this.getCode(res.data.cppFileName, 'cpp')
            if (res.data.hppFileNames && res.data.hppFileNames.length > 0 && this.isSingleCpp !== '0') {
              res.data.hppFileNames.forEach(
                val => {
                  if (val !== null && val !== '') {
                    this.hppPageObj[val] = 1
                    this.getCode(val, 'hpp')
                  }
                })
            }
          } else {
            this.jsFileNamelist.jsFileName = res.data.jsFileName
            this.jsFileNamelist.abiFileName = res.data.abiFileName
            this.codeData = {
              contractId: res.data.contractId,
              uploadVersion: res.data.uploadVersion
            }
            this.gitJscode(res.data.jsFileName, 'js')
            this.gitAbicode(res.data.abiFileName, 'abi')
          }
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else this.msgInfo('info', res.message)
      }).catch(error => {
        // console.log('getContrApprDetails.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },

    gitJscode (fileName) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          this.CollContentjs = res.data
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    gitAbicode (fileName) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          this.CollContentabi = res.data
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    getCode (fileName, filetype, val) {
      // console.log('getCode===>fileName', fileName)
      // const key = fileName.replace('.', '')
      getContractChaincode(this.alertArr.contractId, this.alertArr.uploadVersion, fileName, this.chainCodePageParam[filetype]).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.loadingMsg = res.message
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.modalCode = true
          if (filetype === 'cpp') {
            this.arrChainCode[filetype + 'Obj'] = res.data
            this.codeTotalPages[filetype] = res.data.pages
            // console.log(this.arrChainCode.cppObj, 'cpp')
          } else {
            this.arrChainCode[filetype + 'Obj'][fileName] = res.data
            this.codeTotalPages[filetype][fileName] = res.data.pages
          }

          ++this.transferKey1
          // 发现这块没有用到
          // let pages = {}
          // pages[key] = res.data.pages
          // this.codeTotalPages[filetype] = pages
        }
      }).catch(error => {
        this.loadingMsg = error.message
        // console.log('getContractChaincode.error===>', error)
        this.msgInfo('error', error.message, true)
      })
      if (val === 'bottom') {
        if (filetype === 'cpp') {
          this.cppBottomLoading = false
        } else {
          this.hppBottomLoading[fileName] = false
        }
      } else if (val === 'top') {
        if (filetype === 'cpp') {
          this.cppTopLoading = false
        } else {
          this.hppTopLoading[fileName] = false
        }
      }
    },
    cppHandleReachBottom () {
      if (parseInt(this.chainCodePageParam.cpp.pageIndex) < parseInt(this.codeTotalPages.cpp)) {
        return new Promise(resolve => {
          this.timer = setTimeout(() => {
            ++this.chainCodePageParam.cpp.pageIndex
            this.getCode(this.alertArr.cppName, 'cpp')
            resolve()
          }, 2000)
        })
      } else {
        this.msgInfo('info', '已到最后一页！', true)
      }
    },
    hppHandleReachTop () {
      // 当前页码不是首页，调用翻页函数
      if (parseInt(this.chainCodePageParam.hpp.pageIndex) !== 1) {
        return new Promise(resolve => {
          this.timer = setTimeout(() => {
            --this.chainCodePageParam.hpp.pageIndex
            this.getCode(this.alertArr.hppName, 'hpp')
            resolve()
          }, 2000)
        })
      } else {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    cppHandleReachTop () {
      // 当前页码不是首页，调用翻页函数
      if (parseInt(this.chainCodePageParam.cpp.pageIndex) !== 1) {
        return new Promise(resolve => {
          this.timer = setTimeout(() => {
            --this.chainCodePageParam.cpp.pageIndex
            this.getCode(this.alertArr.cppName, 'cpp')
            resolve()
          }, 2000)
        })
      } else {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    hppHandleReachBottom () {
      // 当前页码小于总页数，调用翻页函数
      if (parseInt(this.chainCodePageParam.hpp.pageIndex) < parseInt(this.codeTotalPages.hpp)) {
        return new Promise(resolve => {
          this.timer = setTimeout(() => {
            ++this.chainCodePageParam.hpp.pageIndex
            this.getCode(this.alertArr.hppName, 'hpp')
            resolve()
          }, 2000)
        })
      } else {
        this.msgInfo('info', '已到最后一页！', true)
      }
    },

    ok (name) {
      this.$refs[name].validate((valid) => {
        // console.log('11111', this.formItem.auditRemark)
        // if (this.approStatus === 'REJECT' && this.formItem.auditRemark === '') {
        //   this.loadingStatus = false
        //   this.msgInfo('error', '审批说明不能为空', true)
        // }
        this.loadingStatus = true
        this.contype = 'CONTRACT_DEPLOY'
        let upfileSize = this.fileSise.reduce((prev, next) => { return prev + next.size }, 0)
        if (upfileSize > this.size) {
          this.loadingStatus = false
          alert('上传文件大于10M,请重新上传！')
        } else {
          if (!this.formItem.approStatus) {
            this.loadingStatus = false
            // this.msgInfo('warning', '请选择是否同意！', true)
          } else {
            let useMemory = ''
            contractApprovel(this.bizId, this.contype, this.formItem.approStatus, this.formItem.auditRemark, this.arrDetails.cppFileName, useMemory, this.languageType, this.arrDetails.uploadVersion, this.allfile).then(res => {
              // console.log('addChainApproval===>', res)
              // this.msgInfo('info', res.message)
              if (res.code === '00000') {
                this.statusMsg = '审核通过'
                this.loadingStatus = false
              } else {
                this.loadingStatus = false
                // this.msgInfo('error', res.message, true)
              }
              this.tipInfo(res)
            }).catch(error => {
              this.loadingStatus = false
              // console.log('contractApprovel.error===>', error)
              this.msgInfo('error', error.message, true)
            })
            // 审批一条后，状态和审批描述置空
          }
        }
      })
      this.beforeDestroy()
    },
    tipInfo (res) {
      if (res.code === '00000') {
        this.msgInfo('success', res.message, true)
        this.getTableData()
        this.getTableData2()
        this.modalAppro = false
        this.allfile = []
        this.formItem.approStatus = ''
        this.formItem.auditRemark = ''
      } else {
        // console.log('tipInfo-error:', res.message)
        this.msgInfo('error', res.message, true)
      }
    },
    cancelApp (name) {
      this.init()
      this.modalAppro = false
      // 用户操作清空，状态和审批描述置空
      this.formItem.approStatus = ''
      this.formItem.auditRemark = ''
      this.beforeDestroy()
      this.allfile = []
    },
    cancelDet (name) {
      this.init()
      this.modalDetail = false
      this.beforeDestroy()
    },
    // 详情弹窗
    detailbtn (index) {
      // console.log('进入弹窗函数', index)
      this.languageType = this.tableData2[index].languageType
      this.deployStatus = this.tableData2[index].statusMsg
      this.bizId = this.tableData2[index].bizId
      this.cppBottomLoading = false
      this.cppTopLoading = false
      this.hppBottomLoading = {}
      this.hppTopLoading = {}
      this.deployId = this.tableData2[index].deployId
      getContrApprDetails(this.tableData2[index].deployId).then(res => {
        if (res.code === '00000') {
          ++this.transferKey2
          this.modalDetail = true
          this.arrDetails = res.data
          this.whyDetail = res.data.message
          this.securityList = res.data.securityScanReportFile ? res.data.securityScanReportFile : "" // 安全报告
          this.interfacDoc = res.data.interfacDoc ? res.data.interfacDoc : "" // api接口文档
          this.isSingleCpp = res.data.isSingleCpp
          this.cppsTitle = res.data.fileContent
          // 调用查询合约源码接口
          if (this.languageType === 'C++') {
            this.codeTotalPages.cpp = 0
            this.codeTotalPages.hpp = {}
            this.arrChainCode.cppObj = {}
            this.arrChainCode.hppObj = {}
            this.alertArr.contractId = res.data.contractId
            this.alertArr.cppName = res.data.cppFileName
            this.alertArr.hppNames = res.data.hppFileNames.map(val => val !== null ? val : '')
            this.alertArr.uploadVersion = res.data.uploadVersion
            if (res.data.cppFileName && res.data.cppFileName !== 'null' && this.isSingleCpp == '1') this.getCode(res.data.cppFileName, 'cpp')
            if (res.data.hppFileNames && res.data.hppFileNames.length > 0 && this.isSingleCpp == '1') {
              res.data.hppFileNames.forEach(val => {
                if (val !== null && val !== '') {
                  this.hppPageObj[val] = 1
                  this.getCode(val, 'hpp')
                }
              })
            }
          } else {
            this.jsFileNamelist.jsFileName = res.data.jsFileName
            this.jsFileNamelist.abiFileName = res.data.abiFileName
            this.codeData = {
              contractId: res.data.contractId,
              uploadVersion: res.data.uploadVersion
            }
            this.gitJscode(res.data.jsFileName, 'js')
            this.gitAbicode(res.data.abiFileName, 'abi')
          }
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else this.msgInfo('info', res.message)
      }).catch(error => {
        // console.log('getContrApprDetails.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    resetting () {
      this.contractName = ''
      this.status = ['WAIT_APPROVED']
      this.userId = []
      this.getTableData()
    },
    resetting2 () {
      this.contractName2 = ''
      this.status2 = ''
      this.userId2 = []
      this.getTableData2()
    },
    beforeDestroy () {
      clearInterval(this.timer)
      this.timer = null
      clearTimeout(this.timerStamp)
      this.timerStamp = null
    },
    clickCpps (value) {
      this.cppContent = value

    }
  },
  // watch: {
  //   tableData: {
  //     handler (newVal) {
  //       //
  //     },
  //     deep: true,
  //     immediate: false
  //   }
  // },
  mounted () {
    this.getTableData()
    this.getTableData2()
    this.searchUserList()
  }
}
</script>

<style lang="less" scoped>
/deep/.ivu-menu-submenu-title {
  background: #fff !important;
}
/deep/.ivu-menu,
.ivu-menu-dark,
.ivu-menu-vertical,
.ivu-menu-opened,
.ivu-menu-submenu-title {
  background: #fff !important;
}
.ivu-menu-vertical.ivu-menu-light:after {
  background: #fff;
}
input {
  margin: 0 0 10px;
}
button.btn {
  position: absolute;
  right: 10px;
  margin: 0 10px;
}
.bt1 {
  margin-right: 10px;
}
.search-title {
  font-size: 12px;
}
.title {
  font-weight: bold;
  font-size: 16px;
}
.bs {
  text-indent: 10px;
  line-height: 15px;
  border-left: 5px solid #3d73ef;
  margin-bottom: 15px;
}
.contract-card {
  span {
    i {
      vertical-align: -0.15em;
    }
  }
}
.bg1 {
  position: relative;
  background-repeat: no-repeat;
  background: top right no-repeat;
  background-image: url("../../../assets/img/pass.png");
}
.bg2 {
  //以下是右下角图片设置
  position: relative;
  background-repeat: no-repeat;
  background: top right no-repeat;
  width: 100%;
  height: 140px;
  background-image: url("../../../assets/img/unpass.png");
}
.resultS {
  margin-top: 30px;
  font-weight: bold;
  font-size: 18px;
  color: #52c7aa;
  margin-left: 5px;
}
.resultF {
  margin-top: 20px;
  font-weight: bold;
  font-size: 18px;
  color: #ef7d68;
  margin-left: 5px;
}
.divS {
  //margin-top:10px;
  //margin-left: 30px;
  width: 750px;
  height: 80px;
  background-color: rgb(255, 255, 255);
}
/deep/.ivu-modal-footer {
  /* border-top: 1px solid #e8eaec; */
  /* padding: 12px 18px 12px 18px; */
  text-align: right;
  //background-color: #F5F6FA;
}
.contractApp {
  /deep/.ivu-tabs {
    min-height: calc(100vh - 208px);
  }
  button.btn {
    position: absolute;
    right: 10px;
  }
  .basetext {
    span {
      text-align: left;
      margin: 0 30px;
      line-height: 30px;
    }
  }
  .width-input{
    width:15vw;
    min-width:200px;
  }
}
/deep/.ivu-modal > .ivu-modal-content > .ivu-modal-body {
  max-height: 45vh;
  overflow: auto;
}
// /deep/.ivu-card{background: #f2f6fd;}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
// /deep/.ivu-collapse{border: none;}
/deep/.ivu-collapse > .ivu-collapse-item {
  border-radius: 5px 5px;
  margin-bottom: 10px;
}

/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.ivu-scroll-container {
  height: auto;
  overflow-y: auto;
}
</style>
<style lang="less">
.textarea-style {
  width: 100%;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
</style>
