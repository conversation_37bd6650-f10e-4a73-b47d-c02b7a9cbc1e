<!--
 预言机模板
   Aturun
-->
<template>
    <div class="prophecy_template">
      <div class="content-top">
        <div class="content-top-lift-title">
          预言机模板
        </div>
        <div class="top_op">
          <div class="content-top-lift">
            <div class="top-right-input icon-search_suffix">
              <el-input
                placeholder="可输入模板名称查询"
                v-model="input"
                @keyup.enter.native="getMachineTempList(true)">
                <i slot="suffix" class="el-icon-search" @click="getMachineTempList(true)"></i>
              </el-input>
            </div>
          </div>
          <div class="content-top-right">

          </div>
        </div>


      </div>
      <div class="content-body">
        <el-table
            :data="tableData"
            style="width: 100%"
            height="520px"
            stripe
        >
          <el-table-column
              prop="tempName"
              label="模板名称"
          >
          </el-table-column>
          <el-table-column
              prop="chainName"
              label="关联链"
          >
          </el-table-column>
          <el-table-column
              prop="appNum"
              label="信源个数"
              width="80">
          </el-table-column>
          <el-table-column

              label="业务描述">
            <template slot-scope="scope">
              <div v-if="scope.row.busDescription.length<=12">{{scope.row.busDescription}}</div>
              <el-popover v-else trigger="hover" placement="top">

                <div class="dsfdg" >{{ scope.row.busDescription }}</div>
                <div slot="reference" class="name-wrapper">
                  {{ scope.row.busDescription }}
                </div>
              </el-popover>
            </template>
          </el-table-column>


          <el-table-column
              label="创建时间"
          >
            <template slot-scope="scope">
              {{setDates(scope.row.createTime)}}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                  size="mini"
                  @click="handlelook(scope.$index, scope.row)">查看</el-button>
              <el-button
                  size="mini"
                  @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="block table_pag">
          <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="entityIn.rows"
              background
              layout="total, prev, pager, next, sizes, jumper"
              :total="total">
          </el-pagination>
        </div>
      </div>
      <prophecyTemplateDialog @Refresh="Refresh" ref="prophecyDialogRef"></prophecyTemplateDialog>
    </div>
</template>

<script>
import prophecyTemplateDialog from './components/prophecyTemplateDialog'
import * as api from "./api";
import {getFormatDates} from "../../utils/atuUtils";
    export default {
      components:{
        prophecyTemplateDialog
      },
        data(){
            return {
              objet:{"code":"0","msg":"success","result":{"total":3,"rows":[{"tempId":"4","tempName":"模板4(dev01)","chainName":"dev01","appNum":22,"busDescription":"12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890","createTime":"2021-10-25T08:28:05.000+00:00"},{"tempId":"5","tempName":"消费者合约(dev-eos)","chainName":"dev-eos","appNum":1,"busDescription":'',"createTime":null},{"tempId":"6","tempName":"信源合约(dev-eos)","chainName":"dev-eos","appNum":2,"busDescription":null,"createTime":null}]}},
              currentPage: 1,
              input:'',
              tableData: [],
              total:null,
              "busDescription":"12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890",
              entityIn:{
                "filter": {},
                "order": "",
                "page": 1,
                "rows": 10,
                "sort": "",
                "tempName": ""
              },
            }
        },
        created(){
          this.getMachineTempList()
          // console.log(this.busDescription.length)
        },
        mounted(){
        },
        methods: {
          handleEdit(index, row) {
            this.$refs.prophecyDialogRef.operationState=1
            this.$refs.prophecyDialogRef.form=row
            this.$refs.prophecyDialogRef.Visible=true
          },
          setDates(val){

            return getFormatDates(val,'yyyy-mm-dd MM:mm:ss')
          },
          Refresh(){
            this.getMachineTempList()
          },

          //获取数据用户列表
          getMachineTempList(search=false){
            if(search){
              this.entityIn.page=1
              this.currentPage = 1
            }
            // if(this.entityIn.startTime){
            //   this.entityIn.startTime = this.setDates(this.entityIn.startTime)
            // }
            // if(this.entityIn.endTime){
            //   this.entityIn.endTime = this.setDates(this.entityIn.endTime)
            // }
            api.getMachineTempList(
                {...this.entityIn,tempName:this.input}
            ).then(res=>{
              if(res.code!=0) return this.$message.warning(res.msg)
              this.tableData=[]
              res.result.rows.map(ele=>{
               let busDescription = ele['busDescription']?ele['busDescription']:''
               this.tableData.push({...ele,busDescription:busDescription})
             })
              this.total=res.result.total
            })
          },
          handlelook(index, row) {
            // console.log(index, row);
            this.$refs.prophecyDialogRef.operationState=0
            this.$refs.prophecyDialogRef.form=row
            this.$refs.prophecyDialogRef.Visible=true
          },
          handleSizeChange(val) {
            this.entityIn.rows =val
            this.getMachineTempList()
          },
          handleCurrentChange(val) {
            this.entityIn.page = val
            this.currentPage =val
            this.getMachineTempList()
          }
        },
    }
</script>

<style lang="less" scoped>
.prophecy_template{
  margin: 16px 14px;
  background: #ffffff;
  height: 706px;
  .content-top{

    .top_op{
      display: flex;
      justify-content: space-between;
    }
    .content-top-lift-title{
      padding-left: 30px;
      font-size: 18px;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }
    .content-top-right{
      display: flex;
      padding: 16px 20px 0 0;
      .top-right-input{
        margin-right: 12px;
      }
      .top-right-button{
        .el-button{

        }
      }
    }
    .content-top-lift{
      padding: 10px 0 0 23px;
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      color: #333333;
      opacity: 1;
    }
  }
  .content-body{
    margin: 11px 17px 0 16px;
  }
  .table_pag{
    margin: 12px 16px 0 0;
    display: flex;
    justify-content: flex-end;
  }
}
</style>



