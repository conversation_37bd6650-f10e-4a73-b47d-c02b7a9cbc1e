{"name": "vue-cmbaas", "version": "1.0.6", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "axios": "^1.7.2", "chokidar": "^3.0.0", "clonedeep": "^2.0.0", "core-js": "^3.8.3", "countup": "^1.8.2", "cron-parser": "^4.8.1", "echarts": "^5.5.1", "element-ui": "^2.14.1", "file-saver": "^2.0.5", "js-cookie": "^2.2.0", "jsencrypt": "^3.2.0", "jszip": "^3.9.1", "md5": "^2.2.1", "remixicon": "^2.5.0", "screenfull": "^5.1.0", "terser-webpack-plugin": "^5.3.10", "view-design": "^4.7.0", "vue": "^2.7.16", "vue-fullscreen": "^2.6.1", "vue-json-viewer": "^2.2.18", "vue-router": "^3.2.0", "vue-virtual-scroll-list": "^1.2.8", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-standard": "^3.0.1", "less": "^4.2.0", "less-loader": "^12.2.0", "vue-template-compiler": "^2.7.16"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}