<template>
  <div style="width:750px">
    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80">
      <FormItem label="模板类型" prop="name">
        <Input v-model="formValidate.name" placeholder="请填写模板类型" />
      </FormItem>
      <FormItem label="链类型" prop="chaincity">
        <Select v-model="formValidate.chaincity" placeholder="请选择链类型" @on-change="changechaincity">
          <Option v-for="item in selectList" :value="item.enumValue" :key="item.enumKey">{{item.enumValue}}</Option>
          <!-- <Option value="EOS">EOS</Option> -->
        </Select>
      </FormItem>
      <FormItem label="合约语言" prop="languagetype">
        <Select v-model="formValidate.languagetype" placeholder="请选择合约语言" @on-change="changelanguagetype">
          <Option v-for="item in languageList" :value="item.enumKey" :key="item.enumKey">{{item.enumValue}}</Option>
          <!-- <Option value="EOS">EOS</Option> -->
        </Select>
      </FormItem>
      <FormItem label="适用场景" prop="scenario">
        <Input v-model="formValidate.scenario" placeholder="请填写适用场景" />
      </FormItem>
      <FormItem label="描述" prop="describe">
        <Input v-model="formValidate.describe" placeholder="请填写描述内容" type="textarea" :maxlength="200" show-word-limit :autosize="{minRows: 3,maxRows: 5}" />
      </FormItem>
      <FormItem label="接口描述" prop="interfacedescribe" class="mandatory">
        <p style="float-right">
          <Button type="success" ghost @click="addInfo">新增</Button>
        </p>

        <div>
          <edit-table-mul border :columns="modalDescribe" v-model="modaldescribeData"></edit-table-mul>
        </div>

      </FormItem>
      <FormItem label="模板上传">
        <div v-if="ideShowZ">
          <p style="float-right" v-if="ideShow">
            <Button type="success" ghost @click="editbtn" :disabled="formValidate.languagetype==='JS'||changeModal==='manycpp'">在线编辑</Button>
          </p>
        </div>

        <div style="display: flex;padding-top: 1%;" v-if="formValidate.languagetype=='C++'">
          <!-- <p>cpp文件：</p>
          <RadioGroup v-model="changeModal" @on-change='getGroup'>
            <Radio label="onecpp">单cpp</Radio>
            <Radio label="manycpp">多cpp</Radio>
          </RadioGroup> -->
        </div>
        <Upload v-if="formValidate.chaincity=='ChainMaker'" action="" type="drag" multiple :before-upload="handleUpload" style="display: inline-table; width: 100%; margin-top: 10px">
          <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
          <h4>支持拖拽上传文件</h4>
          <br />
          <p v-if="formValidate.languagetype=='RUST'" style="color: #aaa; font-size: 12px">仅支持RS文件上传，RS文件上传最大10M</p>
          <p v-else-if="formValidate.languagetype=='GO'||formValidate.languagetype=='TINYGO'" style="color: #aaa; font-size: 12px">仅支持GO文件上传，GO文件上传最大10M</p>
          <p v-else-if="formValidate.languagetype=='SOLIDITY'" style="color: #aaa; font-size: 12px">仅支持SOL文件上传，SOL文件上传最大10M</p>
          <p v-else-if="formValidate.languagetype=='C++'&&changeModal=='onecpp'" style="color: #aaa; font-size: 12px">CPP/HPP文件支持多个文件上传,CPP文件只能有一个,<br>HPP文件可以零个、一个或多个,CPP/HPP文件总大小不能超过10M。</p>
          <p v-else-if="formValidate.languagetype=='C++'&&changeModal=='manycpp'" style="color: #aaa; font-size: 12px">请按照“多CPP文件模板（含CMakeList文件模板）”和“文件规范指引”,将cpp、hpp、CMakeLis文件按照文件规范打包上传(支持zip和tar格式)</p>
        </Upload>

        <Upload v-else action="" type="drag" multiple :format="['zip', 'tar']" :before-upload="handleUpload" style="display: inline-table; width: 100%; margin-top: 10px">
          <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
          <h4>支持拖拽上传文件</h4>
          <br />
          <p v-if="formValidate.languagetype=='C++'&&changeModal=='onecpp'" style="color: #aaa; font-size: 12px">
            支持上传cpp文件(必填)、hpp文件(选填)，且cpp文件文件只能有一个,hpp文件可以一个或多个;<br />合约代码中类名需与合约名称一致。总文件大小不能超过50M。
          </p>
          <p v-else-if="formValidate.languagetype=='JS'" style="color: #aaa; font-size: 12px">支持上传JavaScript文件（必填）、abi文件（必填）、并根据下上传的JavaScript文件<br>生成abi文件并下载编辑，再次进行abi文件的上传（注：JavaScript文件与abi文件一一对应）。</p>
          <p v-else-if="formValidate.languagetype=='C++'&&changeModal=='manycpp'" style="color: #aaa; font-size: 12px">请按照“多CPP文件模板（含CMakeList文件模板）”和“文件规范指引”,将cpp、hpp、CMakeLis文件按照文件规范打包上传(支持zip和tar格式)</p>
        </Upload>
        <edit-table-mul :key="transferKey1" :columns="columns" v-model="tableData"></edit-table-mul>

      </FormItem>
      <div class="newFromSubmit">
        <Button @click="handleReset('formValidate')" style="margin-right: 10px">取消</Button>
        <Button type="primary" @click="handleSubmit('formValidate',1)" v-prevent-re-click :loading="loadingStatus">{{ loadingStatus ? "上传中" : "提交" }}</Button>
      </div>

    </Form>
    <!-- 接口描述新增、修改弹框 -->
    <Modal v-model="newdescribe" :title="describeTitle" footer-hide>
      <Form ref="newfrom" :rules="newfromValidate" :model="newfrom" :label-width="80">
        <FormItem label="函数名" prop="funcName">
          <Input v-model="newfrom.funcName" placeholder="请填写函数名" />
        </FormItem>
        <FormItem label="参数" prop="parameter">
          <Input v-model="newfrom.parameter" placeholder="请填写参数" />
        </FormItem>
        <FormItem label="简介" prop="description">
          <Input v-model="newfrom.description" placeholder="请填写简介" />
        </FormItem>
        <!-- <FormItem style="maigin-left:35%">

        </FormItem> -->
      </Form>
      <div class="newdescribe">
        <Button type="primary" @click="describeOk('newfrom')">确定</Button>
        <Button @click="FormInfoCancel" style="margin-left: 8px">取消</Button>
      </div>
    </Modal>

    <!-- 在线编辑确定弹窗 -->
    <Modal v-model="modal2" title="在线编译" @on-ok="editok" @on-cancel="editcancel">
      <p style="padding:20px;height:100px;">请确认上述数据已填写并保存上述数据，同时页面跳转至IDE平台进行在线编辑</p>
    </Modal>
  </div>
</template>

<script>
import { abidownFile, getconfig } from '@/api/contract'
import { NewContract, getTempateEos, getTempateLanguage, goIDE } from '@/api/data'
import { TemContractName } from '../../../lib/check'
import EditTableMul from '_c/edit-table-mul'

export default {
  components: {
    EditTableMul
  },
  data () {
    const validateContractName = (rule, value, callback) => {
      let reg = /^[_a-zA-Z]/
      if (!reg.test(value.slice(0, 1))) {
        callback(new Error('只能以英文及下划线开头'))
      }
      if (!TemContractName(value)) {
        callback(new Error('支持英文和数字，特殊符号只能有英文句号.和英文_'))
      } else {
        callback()
      }
    }
    return {
      ideShow: false,
      ideShowZ: false,
      changeModal: 'onecpp',
      manyCpp: null, //
      // 以上是S14新增
      loadingStatus: false,
      selectList: [], // 链类型数组
      languageList: [], // 语言类型数组
      selecteos: 'CHAIN_TYPE', // 链类型传参
      selectlanguage: 'LANGUAGE_TYPE', // 语言类型传参
      cppfile: '',
      hppfile: [],
      file: [], // 文件名称
      newdescribe: false, // 接口描述新增、修改弹框
      size: 50 * 1024 * 1024,
      bool: false,
      infoSize: '',
      // 新建合约类型from表单
      formValidate: {
        name: '',
        chaincity: '', // 链类型
        languagetype: '', // 语言类型
        scenario: '',
        describe: ''
      },
      // from表单校验
      ruleValidate: {
        name: [
          { required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
          { max: 32, message: '不能多于32位', trigger: 'blur' },
          { trigger: 'blur', validator: validateContractName }
        ],
        //
        chaincity: [
          {
            required: true,
            message: '不能为空',
            trigger: 'change'
          }
        ],
        languagetype: [
          {
            required: true,
            message: '不能为空',
            trigger: 'change'
          }
        ],
        scenario: [
          { required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
          { max: 60, message: '不能多于60位', trigger: 'blur' }
        ],
        describe: [
          {
            required: true,
            max: 200,
            message: '不能为空且长度不能超过200位',
            trigger: 'blur'
          }
        ]
      },
      // 接口描述
      modalDescribe: [
        {
          title: '函数名（name）',
          key: 'funcName',
          width: 150,
          tooltip: true
        },
        {
          title: '参数（inputs）',
          key: 'parameter',
          tooltip: true
        },
        {
          title: '简介（description）',
          key: 'description',
          tooltip: true
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.descriptEdit(params)
                    }
                  }
                },
                '修改'
              ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.describeRemove(params)
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      modaldescribeData: [], // 接口描述数组
      newfromValidate: {
        funcName: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur'
          }
        ]
      },
      // 模板上传数组
      tableData: [],
      // 上传文件
      columns: [
        { key: 'cppName', title: '文件名', tooltip: true },
        {
          title: '操作',
          width: 200,
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    marginTop: '5px',
                    color: '#3D73EF',
                    float: 'left',
                    border: '1px solid #3D73EF',
                    display: params.row.cppName.indexOf('.js') !== -1 ? 'block' : 'none'
                  },
                  on: {
                    click: () => {
                      this.abiDown(params)
                    }
                  }
                },
                'abi文件下载'
              ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.templateRemove(params)
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      transferKey1: 0,
      describeTitle: '修改', // 修改或新增 标题
      // 接口描述的新增/修改表单
      newfrom: {
        funcName: '',
        parameter: '',
        description: '',
        index: null // 用于替换当前修改的行
      },
      curSelectList: [], // 当前选中列表
      // filesize: '',
      // tableDatasize: []// 用来存放文件大小
      modal2: false,
      accepttype: '',
      goFile: '',
      rsFile: '',
      solFile: '',
      // 以下是新添加js
      jsFile: '',
      abiFile: ''
    }
  },
  methods: {
    abiDown () {
      let name = this.jsFile.name.split('.')
      abidownFile(this.jsFile, '').then(res => {
        let blob = new Blob([res])
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = name[0] + '.abi'
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      })
    },
    // transSize (size) { // 传入 字节 返回兆
    //   // 字节转兆  1m = 1024k 1k = 1024b
    //   // return size / 1024 / 1024
    //   return size * 1024 * 1024
    // },
    // 上传
    handleUpload (file) {

      if (this.formValidate.languagetype === 'C++' && this.changeModal === 'onecpp') { // c++文件
        if (file.name.indexOf('.cpp') !== -1) {
          if (!this.cppfile) {
            this.cppfile = file
            this.tableData.push({ cppName: this.cppfile.name, size: file.size })
            this.bool = true
          } else {
            alert('只能上传一个cpp文件')
          }
        } else if (file.name.indexOf('.hpp') !== -1) {
          this.hppfile.push(file)
          this.tableData.push({ cppName: file.name, size: file.size })
          // let transSize = this.transSize(infoSize) // 字节转 M
        } else {
          alert('请上传对应的文件')
        }
      } else if (this.formValidate.languagetype === 'C++' && this.changeModal === 'manycpp') { // 多c++文件
        if (file.name.indexOf('zip') !== -1 || file.name.indexOf('tar') !== -1) {

          if (!this.manyCpp) {
            this.manyCpp = file
            this.tableData.push({ cppName: this.manyCpp.name, size: file.size })
            this.bool = true
          } else {
            alert('只能上传一个zip/tar压缩包')
          }
        } else {
          alert('只允许上传zip/tar压缩包')
        }
      } else if (this.formValidate.languagetype === 'GO' || this.formValidate.languagetype === 'TINYGO') { // go文件
        if (file.name.indexOf('.go') !== -1) {
          if (!this.goFile) {
            this.goFile = file
            this.tableData.push({ cppName: file.name, size: file.size })
          } else {
            alert('只能上传一个go文件')
          }
        } else {
          alert('请上传对应的文件')
        }
      } else if (this.formValidate.languagetype === 'RUST') { // rust文件
        if (file.name.indexOf('.rs') !== -1) {
          if (!this.rsFile) {
            this.rsFile = file
            this.tableData.push({ cppName: file.name, size: file.size })
          } else {
            alert('只能上传一个rs文件')
          }
        } else {
          alert('请上传对应的文件')
        }
      } else if (this.formValidate.languagetype === 'SOLIDITY') { // solidity文件
        if (file.name.indexOf('.sol') !== -1) {
          if (!this.solFile) {
            this.solFile = file
            this.tableData.push({ cppName: file.name, size: file.size })
          } else {
            alert('只能上传一个sol文件')
          }
        } else {
          alert('请上传对应的文件')
        }
      } else if (this.formValidate.languagetype === 'JS') {
        if (file.name.indexOf('.js') !== -1) {
          if (!this.jsFile) {
            this.jsFile = file
            this.tableData.push({ cppName: this.jsFile.name, size: file.size })
            this.bool = true
          } else {
            alert('只能上传一个js文件')
          }
        } else if (file.name.indexOf('.abi') !== -1) {
          if (!this.abiFile) {
            this.abiFile = file
            this.tableData.push({ cppName: this.abiFile.name, size: file.size })
            this.bool = true
          } else {
            alert('只能上传一个abi文件')
          }
        } else {
          alert('请上传对应的文件')
        }
      } else {
        alert('请选择语言类型')
      }
      return false
    },
    // 获取前表格所有选中项
    // getSelectAll (list) {
    //   this.curSelectList = list
    // },
    // 初始化接口描述新增/修改弹框input框数据
    initFormInfo () {
      this.newfrom.parameter = ''
      this.newfrom.funcName = ''
      this.newfrom.description = ''
      this.newfrom.index = null
    },
    // 接口描述 弹框 (修改、新增) 确定按钮
    describeOk () {
      this.$refs['newfrom'].validate((valid) => {
        // console.log(valid)
        if (valid) { // 检验通过
          this.newdescribe = false
          const { index, ...info } = this.newfrom
          if (this.describeTitle === '修改') {
            // 保存修改信息
            // console.log(info)
            this.modaldescribeData.splice(index, 1, info) // 替换当前项
          } else {
            // 保存新增
            this.modaldescribeData.push(info)
            // console.log(this.data1)
          }
          this.initFormInfo()
          // 关闭弹框
        } else { // 校验失败
          // console.log(this.newdescribe)
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
      // console.log('点击确定按钮')
    },
    // 修改信息 打开弹框
    descriptEdit (info) {
      let { row } = info
      this.newdescribe = true
      this.describeTitle = '修改'
      this.newfrom.parameter = row.parameter
      this.newfrom.funcName = row.funcName
      this.newfrom.description = row.description
      this.newfrom.index = info.index
    },
    // 打开新增弹框
    addInfo () {
      this.newdescribe = true
      this.describeTitle = '新增'
    }, // 接口描述列表删除
    describeRemove (info) {
      this.modaldescribeData.splice(info.index, 1)
    },
    // 接口描述 弹框取消清空表单
    FormInfoCancel () {
      this.newdescribe = false
      this.initFormInfo()
    },
    // 模板上传列表删除
    templateRemove (file) {
      this.tableData.splice(file.index, 1)
      this.hppfile.forEach((item, i) => {
        if (item.name === file.row.cppName) {
          console.log(i, 'index')
          this.hppfile.splice(i, 1)// 从hppfile删除这一项
        }
      })
      // this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和cppName
      // this.bool = this.tableData.some(item => {
      //     return item.cppName.indexOf('cpp') !== -1
      // })
      // if (!this.bool) {
      //     this.cppfile = ''
      // }
      if (file.row.cppName.indexOf('cpp') !== -1) {
        this.cppfile = ''
      } else if (file.row.cppName.indexOf('go') !== -1) {
        this.goFile = ''
      } else if (file.row.cppName.indexOf('rs') !== -1) {
        this.rsFile = ''
      } else if (file.row.cppName.indexOf('sol') !== -1) {
        this.solFile = ''
      } else if (file.row.cppName.indexOf('js') !== -1) {
        this.jsFile = ''
      } else if (file.row.cppName.indexOf('abi') !== -1) {
        this.abiFile = ''
      } else if (file.row.cppName.indexOf('zip') !== -1 || file.row.cppName.indexOf('tar') !== -1) {
        this.manyCpp = ''
      }
      console.log(this.hppfile, this.cppfile, '_____')
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    // 新建合约提交事件
    //  throttle()
    handleSubmit (name, type) {
      this.loadingStatus = true
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          if (this.modaldescribeData.length > 0) {

            let newList = {
              manycppFile: this.manyCpp, // cpp文件
              cppFile: this.cppfile, // cpp文件
              goFile: this.goFile, // go文件
              rsFile: this.rsFile, // rs文件
              solFile: this.solFile, // sol文件,
              uploadType: '',
              uploadBrief: '',

            }
            let fromList = {
              contractType: this.formValidate.name, // 合约类型
              chainType: this.formValidate.chaincity, // 链类型
              languageType: this.formValidate.languagetype, // 语言类型
              scene: this.formValidate.scenario, // 适用场景
              description: this.formValidate.describe, // 描述
              descriptionList: this.modaldescribeData, // 接口描述列表
              isSingleCpp: this.changeModal == 'onecpp' ? '1' : this.changeModal == 'manycpp' ? '0' : ''  //判断是单cpp和多cpp
            }
            let jsList = {
              jsFile: this.jsFile, // js文件
              abiFile: this.abiFile // abi文件
            }

            if (this.formValidate.chaincity === 'EOS' || this.formValidate.chaincity === 'BOS') {
              if (this.formValidate.languagetype == 'C++' && this.changeModal == 'manycpp') {
                this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                if (this.infoSize > this.size) {
                  this.loadingStatus = false
                  alert('上传文件大于50M,请重新上传！')
                } else {
                  this.newmodel(newList, fromList, type)
                }
              } else {
                if (this.cppfile === '' && this.tableData.length > 0) {
                  this.loadingStatus = false
                  alert('必须有一个cpp文件')
                } else {
                  this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                  if (this.infoSize > this.size) {
                    this.loadingStatus = false
                    alert('上传文件大于50M,请重新上传！')
                  } else {
                    this.newmodel(newList, fromList, type)
                  }
                }
              }

            } else if (this.formValidate.chaincity === 'CMEOS') {
              if (this.formValidate.languagetype === 'JS') {
                if (this.jsFile === '' || this.abiFile === '') {
                  if (this.tableData.length > 0) {
                    this.loadingStatus = false
                    alert('JavaScript文件和abi文件是必填项')
                  } else {
                    this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                    if (this.infoSize > this.size) {
                      this.loadingStatus = false
                      alert('上传文件大于50M,请重新上传！')
                    } else {
                      this.newmodel(jsList, fromList, type)
                    }
                  }
                } else {
                  this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                  if (this.infoSize > this.size) {
                    this.loadingStatus = false
                    alert('上传文件大于50M,请重新上传！')
                  } else {
                    this.newmodel(jsList, fromList, type)
                  }
                }
              } else {
                if (this.formValidate.languagetype == 'C++' && this.changeModal == 'manycpp') {
                  this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                  if (this.infoSize > this.size) {
                    this.loadingStatus = false
                    alert('上传文件大于50M,请重新上传！')
                  } else {
                    this.newmodel(newList, fromList, type)
                  }
                } else {
                  if (this.cppfile === '' && this.tableData.length > 0) {
                    this.loadingStatus = false
                    alert('必须有一个cpp文件')
                  } else {
                    this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                    if (this.infoSize > this.size) {
                      this.loadingStatus = false
                      alert('上传文件大于50M,请重新上传！')
                    } else {
                      this.newmodel(newList, fromList, type)
                    }
                  }
                }

              }
            } else if (this.formValidate.chaincity == 'ChainMaker') {
              if (this.formValidate.languagetype == 'C++' && this.changeModal == 'manycpp') {
                this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                if (this.infoSize > this.size) {
                  this.loadingStatus = false
                  alert('上传文件大于50M,请重新上传！')
                } else {
                  this.newmodel(newList, fromList, type)
                }
              } else {
                if (this.formValidate.languagetype == 'C++' && this.cppfile == '' && this.tableData.length > 0) {
                  this.loadingStatus = false
                  alert('必须有一个cpp文件')
                } else {
                  this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                  if (this.infoSize > 10 * 1024 * 1024) {
                    this.loadingStatus = false
                    alert('上传文件大于10M,请重新上传！')
                  } else {
                    this.newmodel(newList, fromList, type)
                  }
                }
              }

            }

          } else {
            this.loadingStatus = false
            this.msgInfo('warning', '接口描述不能为空', true)
          }
        } else {
          this.loadingStatus = false
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },
    // 切换多cpp和单cpp清空
    getGroup (e) {
      this.changeModal = e

      this.tableData = []
      this.hppfile = []
      this.cppfile = ''
      this.manyCpp = ''
    },
    handleReset (name) {
      this.$router.push({
        name: 'template_table'
      })
      this.$refs[name].resetFields()
      // this.modal1 = false
    },

    // s6
    newmodel (newList, fromList, type) {
      NewContract(newList, fromList, this.hppfile).then(res => {
        // console.log(res)
        if (res.code === 'B0001') {
          setTimeout(() => {
            this.loadingStatus = false
            this.msgInfo('warning', res.message, true)
          }, 3000)
        } else if (res.code === '00000') {
          setTimeout(() => {
            this.loadingStatus = false
            if (type == 1) {
              this.$router.push({
                name: 'template_table'
              })
              this.$Message.success('新建模板类型成功!')
            } else if (type == 2) {
              this.goide(res.data.contractModelId, 'ADD')
            }
          }, 2000)
        }
      }).catch(error => {
        setTimeout(() => {
          this.loadingStatus = false
          this.msgInfo('warning', error.message, true)
        }, 3000)
      })
    },
    goide (id, type) {
      goIDE(id, type).then((res) => {
        console.log(res.data.url)
        this.$router.push({
          name: 'template_table'
        })
        window.open(res.data.url, 'ide')
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    editbtn () {
      this.modal2 = true
    },
    editok () {
      this.handleSubmit('formValidate', 2)
    },
    editcancel () {

    },
    changechaincity (value) {
      this.formValidate.languagetype = ''
      this.cleardata()
      if (value === 'EOS' || value === 'BOS') {
        this.languageList = [
          { enumKey: 'C++', enumValue: 'C++' }
        ]
      } else if (value === 'CMEOS') {
        this.languageList = [
          { enumKey: 'C++', enumValue: 'C++' },
          { enumKey: 'JS', enumValue: 'Java Script' }
        ]
      } else if (value === 'ChainMaker') {
        // GO、C++、rust、tinygo、solidity
        this.languageList = [
          { enumKey: 'GO', enumValue: 'GO' },
          { enumKey: 'C++', enumValue: 'C++' },
          { enumKey: 'RUST', enumValue: 'RUST' },
          { enumKey: 'TINYGO', enumValue: 'TINYGO' },
          { enumKey: 'SOLIDITY', enumValue: 'SOLIDITY' }
        ]
      } else {
        this.languageList = []
      }
    },
    changelanguagetype (value) {
      this.formValidate.languagetype = value
      this.cleardata()
      if (value === 'C++') {
        this.accepttype = '.hpp,.cpp'
        this.bool = false// cpp文件是否为必传
      } else if (value === 'GO' || value === 'TINYGO') {
        this.accepttype = '.go'
        this.bool = true
      } else if (value === 'RUST') {
        this.accepttype = '.rs'
        this.bool = true
      } else if (value === 'SOLIDITY') {
        this.accepttype = '.sol'
        this.bool = true
      } else if (value === 'JS') {
        this.accepttype = '.js,.abi'
        this.bool = true
      }
    },
    cleardata () {
      this.tableData = []
      this.hppfile = []
      this.cppfile = ''
      this.goFile = ''
      this.rsFile = ''
      this.solFile = ''
      this.jsFile = ''
      this.abiFile = ''
    }

  },
  created () {
    let name = 'LINE_IDE'
    getconfig(name).then((res) => {
      if (res.data) {
        this.ideShow = res.data.value == 1
      } else {
        this.ideShow = false
      }
    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })
    let nameIde = 'Z_LINE_IDE'
    getconfig(nameIde).then((res) => {
      if (res.data) {
        this.ideShowZ = res.data.value == 1
      } else {
        this.ideShowZ = false
      }
    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })
    getTempateEos(this.selecteos).then(res => {
      this.selectList = res.data
    }).catch(error => {
      this.msgInfo('error', error.message, true)
    })
    // this.goide(18)
    // getTempateLanguage(this.selectlanguage).then(res => {
    //   // console.log(res.data)
    //   this.languageList = res.data
    //   // console.log(this.selectList)
    // })
  }

}
</script>

<style scoped lang="less">
// 接口描述 取消按钮
.newdescribe {
  margin-left: 38%;
}
//新增页面提交按钮
.newFromSubmit {
  margin-left: 40%;
}
.mandatory {
  /deep/.ivu-form-item-label::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}
</style>
