import Layout from '@/views/layout.vue'
import dash from '@/assets/selfIcon/dash.png'
import eos from '@/assets/selfIcon/eos.png'
import eos1009 from '@/assets/selfIcon/eos1009.png'
import eos1010 from '@/assets/selfIcon/eos1010.png'
import eos1011 from '@/assets/selfIcon/eos1011.png'
// import eos1012 from '@/assets/selfIcon/eos1012.png'
import mng from '@/assets/selfIcon/mng.png'
import mng1016 from '@/assets/selfIcon/mng1016.png'
import mng1017 from '@/assets/selfIcon/mng1017.png'
import mng1018 from '@/assets/selfIcon/mng1018.png'
import mng1019 from '@/assets/selfIcon/mng1019.png'
import mng1020 from '@/assets/selfIcon/mng1020.png'
import mng1021 from '@/assets/selfIcon/mng1021.png'
import mng1022 from '@/assets/selfIcon/mng1022.png'
import mng1023 from '@/assets/selfIcon/mng1023.png'
import mng1024 from '@/assets/selfIcon/mng1024.png'
import mng1025 from '@/assets/selfIcon/mng1025.png'
import mng1026 from '@/assets/selfIcon/mng1026.png'
import mng1027 from '@/assets/selfIcon/mng1027.png'
import mng1028 from '@/assets/selfIcon/mng1028.png'
import mng1030 from '@/assets/selfIcon/mng1030a.png'
import mng1032 from '@/assets/selfIcon/mng1032.png'

import fab from '@/assets/selfIcon/fab.png'
import fab01 from '@/assets/selfIcon/fab01.png'
import fab02 from '@/assets/selfIcon/fab02.png'
// 黑色
import dashb from '@/assets/selfIcon/dashb.png'
import eosb from '@/assets/selfIcon/eosb.png'
import eos1009b from '@/assets/selfIcon/eos1009b.png'
import eos1010b from '@/assets/selfIcon/eos1010b.png'
import eos1011b from '@/assets/selfIcon/eos1011b.png'
// import eos1012b from '@/assets/selfIcon/eos1012b.png'
import mngb from '@/assets/selfIcon/mngb.png'
import mng1016b from '@/assets/selfIcon/mng1016b.png'
import mng1017b from '@/assets/selfIcon/mng1017b.png'
import mng1018b from '@/assets/selfIcon/mng1018b.png'
import mng1019b from '@/assets/selfIcon/mng1019b.png'
import mng1020b from '@/assets/selfIcon/mng1020b.png'
import mng1021b from '@/assets/selfIcon/mng1021b.png'
import mng1022b from '@/assets/selfIcon/mng1022b.png'
import mng1023b from '@/assets/selfIcon/mng1023b.png'
import mng1024b from '@/assets/selfIcon/mng1024b.png'
import mng1025b from '@/assets/selfIcon/mng1025b.png'
import mng1026b from '@/assets/selfIcon/mng1026b.png'
import mng1027b from '@/assets/selfIcon/mng1027b.png'
import mng1028b from '@/assets/selfIcon/mng1028b.png'
import mng1030b from '@/assets/selfIcon/mng1030b.png'
import mng1032b from '@/assets/selfIcon/mng1032b.png'

import mng1033 from '@/assets/selfIcon/mng1033.png'
import mng1033b from '@/assets/selfIcon/mng1033b.png'
import mng1034 from '@/assets/selfIcon/mng1034.png'
import mng1034b from '@/assets/selfIcon/mng1034b.png'
import mng1035 from '@/assets/selfIcon/mng1035.png'
import mng1035b from '@/assets/selfIcon/mng1035b.png'
import mng1036 from '@/assets/selfIcon/mng1036.png'
import mng1036b from '@/assets/selfIcon/mng1036b.png'

import app01 from '@/assets/selfIcon/app01.png'
import app01b from '@/assets/selfIcon/app01b.png'
// import fabb from '@/assets/selfIcon/fabb.png'
import cm01 from '@/assets/img/chainmaker/cm01.png'
import cm02 from '@/assets/img/chainmaker/cm02.png'
import { prophecyManagementRouters } from './prophecy'
export const routerMap = []

export const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录'
    },
    component: () => import('@/views/login.vue')
  },
  {
    path: '/register',
    name: 'register',
    meta: {
      title: '注册'
    },
    component: () => import('@/views/register.vue')
  },
  {
    path: '/forget_pwd',
    name: 'forget_pwd',
    meta: {
      title: '忘记密码'
    },
    component: () => import('@/views/forget-pwd.vue')
  },
  {
    path: '/reset_pwd',
    name: 'reset_pwd',
    meta: {
      title: '忘记密码'
    },
    component: () => import('@/views/reset-pwd.vue')
  },
  {
    path: '/token_disabled',
    name: 'token_disabled',
    meta: {
      title: 'token失效'
    },
    component: () => import('@/views/token-disabled.vue')
  },
  {
    path: '/Logout',
    name: 'Logout',
    meta: {
      title: '退出登录'
    },
    component: () => import('@/views/Logout.vue')

  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: Layout,
    icon: 'md-home',
    selfIcon: dash,
    selfIconb: dashb,
    meta: {
      title: 'Dashboard'
    },
    children: [{
      path: 'dashboard_index',
      name: 'dashboard',
      icon: 'md-apps',
      selfIcon: dash,
      selfIconb: dashb,
      meta: {
        title: 'Dashboard'
      },
      component: () => import('@/views/dashboard/index.vue')
    }]
  },
  {
    path: '/home',
    name: 'home',
    component: Layout,
    icon: 'md-globe',
    selfIcon: eos,
    selfIconb: eosb,
    meta: {
      title: '开放网络'
    },
    children: [{
      path: '/browser_index',
      name: 'browser_index',
      icon: 'ios-analytics',
      selfIcon: eos1009,
      selfIconb: eos1009b,
      meta: {
        title: '浏览器'
      },
      component: () => import('@/views/eos/browser/index.vue'),
      // redirect: { name: 'browser' }, // 父路由重定向
      children: [
        // {
        //   path: '/browser',
        //   name: 'browser',
        //   icon: 'ios-analytics',
        //   selfIcon: eos1009,
        //   selfIconb: eos1009b,
        //   meta: {
        //     title: 'EOS浏览器'
        //   },
        //   component: () => import('@/views/eos/browser/browser.vue')
        // },
        {
          path: '/browser_block',
          name: 'browser_block',
          icon: 'ios-analytics',
          meta: {
            title: '特定区块'
          },
          hidden: true,
          component: () => import('@/views/eos/browser/browser-block.vue')
        },
        {
          path: '/browser_trade',
          name: 'browser_trade',
          icon: 'logo-buffer',
          meta: {
            title: '特定交易'
          },
          hidden: true,
          component: () => import('@/views/eos/browser/browser-trade.vue')
        },
        {
          path: '/browser_chain',
          name: 'browser_chain',
          icon: 'logo-buffer',
          meta: {
            title: '账户页'
          },
          hidden: true,
          component: () => import('@/views/eos/browser/browser-chain.vue')
        }
      ]
    },
    {
      path: '/chain_table',
      name: 'chain_table',
      icon: 'md-person-add',
      selfIcon: eos1010,
      selfIconb: eos1010b,
      meta: {
        title: '链账户'
      },
      component: () => import('@/views/eos/mychain/index.vue'),
      children: [
        // {
        //   path: '/chain_table',
        //   name: 'chain_table',
        //   icon: 'md-person-add',
        //   selfIcon: eos1010,
        //   selfIconb: eos1010b,
        //   meta: {
        //     title: '我的链账户'
        //   },
        //   component: () => import('@/views/eos/mychain/chain-table.vue')
        // },
        {
          path: '/new_user',
          name: 'new_user',
          icon: 'md-add-circle',
          meta: {
            title: '创建链账户'
          },
          hidden: true,
          component: () => import('@/views/eos/mychain/new-user.vue')
        },
        {
          path: '/role/:res',
          name: 'result_page',
          icon: 'md-ribbon',
          meta: {
            title: '处理结果'
          },
          hidden: true,
          component: () => import('@/views/eos/mychain/result-page.vue'),
          props: true
        },
        {
          path: '/chain_details',
          name: 'chain_details',
          icon: 'md-pricetag',
          meta: {
            title: '链账户详情'
          },
          hidden: true,
          component: () => import('@/views/eos/mychain/chain-details.vue')
        },
        {
          path: '/chain_resource_details',
          name: 'chain_resource_details',
          icon: 'md-pricetag',
          meta: {
            title: '资源详情'
          },
          hidden: true,
          component: () => import('@/views/eos/mychain/chain-resource-details.vue')
        }
      ]
    },
    {
      path: '/contract_table',
      name: 'contract_table',
      icon: 'md-bookmarks',
      selfIcon: eos1011,
      selfIconb: eos1011b,
      meta: {
        title: '智能合约'
      },
      component: () => import('@/views/eos/mycontract/index.vue'),
      // redirect: { name: 'contract_table' }, // 父路由重定向
      children: [
        {
          path: '/contract_area',
          name: 'contract_area',
          icon: 'md-bookmarks',
          meta: {
            title: '合约广场'
          },
          hidden: true,
          component: () => import('@/views/eos/mycontract/contract-area.vue')
        },
        {
          path: '/contract_details/:contractId/:tenantId',
          name: 'contract_details',
          icon: 'md-pricetag',
          meta: {
            title: '合约详情'
          },
          hidden: true,
          component: () => import('@/views/eos/mycontract/contract-details.vue')
        },
        {
          path: '/contract_analysis',
          name: 'contract_analysis',
          icon: '',
          meta: {
            title: '智能合约数据'
          },
          hidden: true,
          component: () => import('@/views/eos/mycontract/contract-analysis.vue')
        },
        {
          path: '/sharecontract_details/:shareRecordId/:receivedTenantId',
          name: 'sharecontract_details',
          icon: 'md-pricetag',
          meta: {
            title: '共享合约详情'
          },
          hidden: true,
          component: () => import('@/views/eos/mycontract/sharecontract-details.vue')
        },
        {
          path: '/contract_new',
          name: 'contract_new',
          icon: 'md-bookmarks',
          meta: {
            title: '新建合约详情'
          },
          hidden: true,
          component: () => import('@/views/eos/mycontract/contract-new.vue')
        },
        {
          path: '/contract_shelves/:listId',
          name: 'contract_shelves',
          icon: 'md-bookmarks',
          meta: {
            title: '上架合约市场'
          },
          hidden: true,
          component: () => import('@/views/eos/mycontract/contract-shelves.vue')
        }
      ]
    },
      // {
      //   path: '/template_table',
      //   name: 'template_table',
      //   icon: 'md-bookmarks',
      //   selfIcon: eos1011,
      //   selfIconb: eos1011b,
      //   meta: {
      //     title: '合约模板'
      //   },
      //   component: () => import('@/views/eos/mytemplate/index.vue'),
      //   children: [
      //     {
      //       path: '/tem_modify',
      //       name: 'tem_modify',
      //       meta: {
      //         title: '合约模板修改'
      //       },
      //       hidden: true,
      //       component: () => import('@/views/eos/mytemplate/template-modify.vue')
      //     },
      //     {
      //       path: '/template_details',
      //       name: 'template_details',
      //       icon: 'md-pricetag',
      //       meta: {
      //         title: '合约模板详情'
      //       },
      //       hidden: true,
      //       component: () => import('@/views/eos/mytemplate/template-details.vue')
      //     },
      //     {
      //       path: '/template_newmodal',
      //       name: 'template_newmodal',
      //       icon: 'md-pricetag',
      //       meta: {
      //         title: '新建模板类型'
      //       },
      //       hidden: true,
      //       component: () => import('@/views/eos/mytemplate/template-newmodal.vue')
      //     }
      //   ]
      // }
    ]
  },
  {
    path: '/fabric',
    name: 'fabric',
    component: Layout,
    icon: 'md-globe',
    selfIcon: fab,
    meta: {
      title: 'Fabric联盟网络'
    },
    children: [
      {
        path: '/instance',
        name: 'instance',
        icon: 'md-bookmarks',
        selfIcon: fab01,
        meta: {
          title: '实例管理'
        },
        component: () => import('@/views/fabric/fabric.vue'),
        children: [
          {
            path: '/chainManage/overview',
            name: 'overview',
            meta: {
              title: '实例详情'
            },
            hidden: true,
            component: () => import('@/views/fabric/overview/index.vue')
          },
          {
            path: '/guide/guide',
            hidden: true, // 不在侧边栏显示
            name: 'guide',
            component: () => import('@/views/fabric/guide/guide')
          },
          {
            path: '/guide/step',
            hidden: true, // 不在侧边栏显示
            name: 'step',
            meta: {
              title: '创建实例'
            },
            component: () => import('@/views/fabric/step/index')
          },
          {
            path: '/guide/creatChain/exChain',
            hidden: true, // 不在侧边栏显示
            name: 'exChain',
            meta: {
              title: '标准版'
            },
            component: () => import('@/views/fabric/creatChain/compontents/exChain-index'),
            children: [
              {
                path: '/guide/creatChain/exChain/deployChain',
                hidden: true, // 不在侧边栏显示
                name: 'cusChain',
                meta: {
                  title: '部署'
                },
                component: () => import('@/views/fabric/deployChain/index')
              }
            ]
          },
          {
            path: '/guide/creatChain/prChain',
            hidden: true, // 不在侧边栏显示
            name: 'prChain',
            meta: {
              title: '安全版'
            },
            component: () => import('@/views/fabric/creatChain/compontents/prChain-index'),
            children: [
              {
                path: '/guide/creatChain/prChain/deployPrChain',
                hidden: true, // 不在侧边栏显示
                name: 'cusPrChain',
                meta: {
                  title: '部署'
                },
                component: () => import('@/views/fabric/deployChain/index')
              }
            ]
          }
          // {
          //   path: '/guide/creatChain/cusChain',
          //   hidden: true, // 不在侧边栏显示
          //   name:'cusChain',
          //   meta: {
          //     title: '实例管理'
          //   },
          //   component:() => import('@/views/fabric/creatChain/compontents/cusChain'),
          // },
          // {
          //   path: '/guide/creatChain/deployChain',
          //   hidden: true, // 不在侧边栏显示
          //   name: 'cusChain',
          //   meta: {
          //     title: '部署'
          //   },
          //   component: () => import('@/views/fabric/deployChain/index')
          // }
        ]
      },
      {
        path: '/contractLibrary',
        name: 'contractLibrary',
        icon: 'md-bookmarks',
        selfIcon: fab02,
        meta: {
          title: '智能合约库'
        },
        component: () => import('@/views/fabric/contractLibrary/index.vue')
      }
    ]
  },
  {
    path: '/chainMaker',
    name: 'chainMaker',
    component: Layout,
    icon: 'md-globe',
    selfIcon: cm01,
    meta: {
      title: 'ChainMaker联盟网络'
    },
    children: [
      {
        path: '/chainMaker_index',
        name: 'chainMaker_index',
        icon: 'ios-analytics',
        selfIcon: cm02,
        meta: {
          title: 'ChainMaker浏览器'
        },
        component: () => import('@/views/chainMaker/index.vue'),
        children: [
          // {
          //   path: '/chainMaker_index',
          //   name: 'chainMaker_index',
          //   icon: 'ios-analytics',
          //   selfIcon: cm02,
          //   meta: {
          //     title: 'ChainMaker浏览器'
          //   },
          //   component: () => import('@/views/chainMaker/chainMaker.vue')
          // },
          {
            path: '/chainMaker_block',
            name: 'chainMaker_block',
            icon: 'ios-analytics',
            meta: {
              title: 'ChainMaker区块'
            },
            hidden: true,
            component: () => import('@/views/chainMaker/chainMaker-block.vue')
          },
          {
            path: '/chainMaker_trade',
            name: 'chainMaker_trade',
            icon: 'logo-buffer',
            meta: {
              title: 'ChainMaker交易'
            },
            component: () => import('@/views/chainMaker/chainMaker-trade.vue')
          }
        ]
      }
    ]
  },
  {
    path: '/admin_index',
    name: 'admin_index',
    icon: 'md-star',
    selfIcon: mng,
    selfIconb: mngb,
    meta: {
      title: '管理员权限'
    },
    component: Layout,
    children: [
      {
        path: '/application_admin',
        name: 'application_admin',
        icon: 'logo-usd',
        selfIcon: mng1020,
        selfIconb: mng1020b,
        meta: {
          title: '应用管理'
        },
        component: () => import('@/views/myadmin/applicationmenu/index.vue')
      },
      {
        path: '/config_admin',
        name: 'config_admin',
        icon: 'logo-usd',
        selfIcon: mng1020,
        selfIconb: mng1020b,
        meta: {
          title: '配置管理'
        },
        component: () => import('@/views/myadmin/settingmenu/index.vue')
      },
      {
        path: '/tenant_table',
        name: 'tenant_table',
        icon: 'md-contacts',
        selfIcon: mng1016,
        selfIconb: mng1016b,
        meta: {
          title: '租户管理'
        },
        component: () => import('@/views/myadmin/tenantadmin/index.vue'),
        children: [{
          path: '/tenant_details/:tenantId',
          name: 'tenant_details',
          icon: 'md-pricetag',
          meta: {
            title: '租户详情'
          },
          hidden: true,
          component: () => import('@/views/myadmin/tenantadmin/tenant-details.vue')
        },
        {
          path: '/new_company',
          name: 'new_company',
          icon: 'md-pricetag',
          meta: {
            title: '新建公司'
          },
          component: () => import('@/views/myadmin/tenantadmin/new-company.vue')
        }
        ]
      },
      {
        path: '/user_admin',
        name: 'user_admin',
        icon: 'md-people',
        selfIcon: mng1017,
        selfIconb: mng1017b,
        meta: {
          title: '用户管理'
        },
        component: () => import('@/views/myadmin/useradmin/index.vue'),
        children: [
          {
            path: '/user_log/:userLoginId',
            name: 'user_log',
            icon: 'ios-list-box-outline',
            meta: {
              title: '用户日志'
            },
            hidden: true,
            component: () => import('@/views/myadmin/useradmin/user-log.vue')
          },
          {
            path: '/user_statistics',
            name: 'user_statistics',
            icon: 'md-home',
            hidden: true, // 不在侧边栏显示
            meta: {
              title: '用户统计'
            },
            component: () => import('@/views/userstatistics/statistics_index.vue')
          }
        ]
      },
      {
        path: '/role_admin',
        name: 'role_admin',
        icon: 'md-contact',
        selfIcon: mng1018,
        selfIconb: mng1018b,
        meta: {
          title: '角色功能管理'
        },
        component: () => import('@/views/myadmin/role-admin.vue')
      },
      {
        path: '/menu_admin',
        name: 'menu_admin',
        icon: 'md-clipboard',
        selfIcon: mng1019,
        selfIconb: mng1019b,
        meta: {
          title: '菜单管理'
        },
        component: () => import('@/views/myadmin/menu-admin.vue')
      },
      {
        path: '/token_admin',
        name: 'token_admin',
        icon: 'logo-usd',
        selfIcon: mng1020,
        selfIconb: mng1020b,
        meta: {
          title: '链账户资源管理'
        },
        component: () => import('@/views/myadmin/token-admin.vue')
      },
      {
        path: '/approvel_admin',
        name: 'approvel_admin',
        icon: 'md-eye',
        selfIcon: mng1021,
        selfIconb: mng1021b,
        width: '17px',
        meta: {
          title: '审批管理'
        },
        showChild: true,
        component: () => import('@/views/myadmin/approvel/approvel-admin.vue'),
        // redirect: { name: 'contract-approvel' }, // 父路由重定向
        children: [{
          path: '/contract-approvel',
          name: 'contract-approvel',
          // icon: 'md-list-box',
          meta: {
            title: '合约审批'
          },
          component: () => import('@/views/myadmin/approvel/contract-approvel.vue')
        },
        {
          path: '/chain-approvel',
          name: 'chain-approvel',
          // icon: 'md-list-box',
          meta: {
            title: '链账户审批'
          },
          component: () => import('@/views/myadmin/approvel/chain-approvel.vue')
        },
        {
          path: '/workorder-approvel',
          name: 'workorder-approvel',
          // icon: 'md-list-box',
          meta: {
            title: '工单审批'
          },
          component: () => import('@/views/myadmin/approvel/workorder-approvel.vue')
        },
        {
          path: '/sharecontract-approvel',
          name: 'sharecontract-approvel',
          meta: {
            title: '合约共享审批'
          },
          component: () => import('@/views/myadmin/approvel/sharecontract-approvel.vue')
        },
        {
          path: '/shelves_approval',
          name: 'shelves_approval',
          meta: {
            title: '合约市场审批'
          },
          component: () => import('@/views/myadmin/approvel/contractapproval/index.vue'),
          children: [
            {
              path: '/pending_detail',
              name: 'pending_detail',
              meta: {
                title: '待审批详情'
              },

              component: () => import('@/views/myadmin/approvel/contractapproval/pending-detail.vue')
            },
            {
              path: '/has_detail',
              name: 'has_detail',
              meta: {
                title: '已审批详情'
              },

              component: () => import('@/views/myadmin/approvel/contractapproval/has-detail.vue')
            }

          ]
        },
        {
          path: '/chain-resource',
          name: 'chain-resource',
          meta: {
            title: '链账户资源审批'
          },
          component: () => import('@/views/myadmin/approvel/chain-resource.vue')
        }

        ]
      },
      {
        path: '/multilink_admin',
        name: 'multilink_admin',
        icon: 'md-aperture',
        selfIcon: mng1022,
        selfIconb: mng1022b,
        meta: {
          title: '链管理'
        },
        component: () => import('@/views/myadmin/multilink/index.vue'),
        children: [
          // {
          //   path: '/multilink_admin',
          //   name: 'multilink_admin',
          //   selfIcon: mng1022,
          //   selfIconb: mng1022b,
          //   meta: {
          //     title: '链管理'
          //   },
          //   component: () => import('@/views/myadmin/multilink/admin.vue')
          // },
          {
            path: '/multilink_details/:eosChainId',
            name: 'multilink_details',
            selfIcon: mng1022,
            selfIconb: mng1022b,
            meta: {
              title: '链详情'
            },
            hidden: true,
            component: () => import('@/views/myadmin/multilink/details.vue')
          }
        ]
      },
      // {
      //   path: '/multilink_details/:eosChainId',
      //   name: 'multilink_details',
      //   selfIcon: mng1022,
      //   selfIconb: mng1022b,
      //   meta: {
      //     title: '链管理 / 链详情'
      //   },
      //   hidden: true,
      //   component: () => import('@/views/myadmin/multilink/details.vue')
      // },
      {
        path: '/host_management',
        name: 'host_management',
        icon: 'md-analytics',
        selfIcon: mng1023,
        selfIconb: mng1023b,
        meta: {
          title: '主机管理'
        },
        component: () => import('@/views/myadmin/hostmanagement/index.vue')
      },
      {
        path: '/smart_center',
        name: 'smart_center',
        icon: 'md-globe',
        selfIcon: mng1023,
        selfIconb: mng1023b,
        meta: {
          title: '智慧中台数据信息'
        },
        component: () => import('@/views/myadmin/smartcenter/index.vue')
      },
      {
        path: '/constract_analysis',
        name: 'constract_analysis',
        icon: 'md-analytics',
        selfIcon: mng1023,
        selfIconb: mng1023b,
        meta: {
          title: '合约数据分析'
        },
        component: () => import('@/views/myadmin/analysis/constract-analysis.vue')
      },
      {
        path: '/blockchain_network',
        name: 'blockchain_network',
        icon: 'md-globe',
        selfIcon: fab,
        meta: {
          title: '区块链网络'
        },
        component: () => import('@/views/myadmin/network/index.vue'),
        children: [
          {
            path: '/network-details/:chainId',
            name: 'network-details',
            selfIcon: mng1022,
            selfIconb: mng1022b,
            meta: {
              title: '区块链网络详情'
            },
            hidden: true,
            component: () => import('@/views/myadmin/network/network-details.vue')
          }
        ]
      },

      {
        path: '/transaction_commit',
        name: 'transaction_commit',
        icon: 'md-globe',
        selfIcon: fab,
        meta: {
          title: '发起交易'
        },
        component: () => import('@/views/myadmin/sub_record/index.vue'),
        children: [
          {
            path: '/new_record',
            name: 'new_record',
            icon: 'md-globe',
            selfIcon: fab,
            meta: {
              title: '新增交易'
            },
            hidden: true,
            component: () => import('@/views/myadmin/sub_record/new_record.vue')
          }
        ]
      },
      // 节点管理
      {
        path: '/node_management',
        name: 'node_management',
        icon: 'md-person',
        selfIcon: mng1032,
        selfIconb: mng1032b,
        meta: {
          title: '节点管理'
        },
        component: () => import('@/views/nodemanagement/index.vue'),
        children: [
          {
            path: '/management_detail/:managementId',
            name: 'management_detail',
            meta: {
              title: '节点详情'
            },
            hidden: true,
            component: () => import('@/views/nodemanagement/management_detail.vue')
          },
          {
            path: '/new_recor',
            name: 'new_recor',
            meta: {
              title: '交易详情'
            },
            hidden: true,
            component: () => import('@/views/nodemanagement/new_record_detail.vue')
          }
        ]
      },

      {
        path: '/file_manage',
        name: 'file_manage',
        icon: 'md-globe',
        selfIcon: fab,
        meta: {
          title: '文件管理'
        },
        component: () => import('@/views/myadmin/filemanage/index.vue'),
        children: [
          {
            path: '/file_details/:id',
            name: 'file_details',
            icon: 'md-globe',
            selfIcon: fab,
            meta: {
              title: '项目详情'
            },
            hidden: true,
            component: () => import('@/views/myadmin/filemanage/detail.vue')
          },
          {
            path: '/file_annex_details/:id',
            name: 'file_annex_details',
            icon: 'md-globe',
            selfIcon: fab,
            meta: {
              title: '项目详情'
            },
            hidden: true,
            component: () => import('@/views/myadmin/filemanage/attachment_detail.vue')
          }
        ]
      },

    ]
  },
  {
    path: '/storageevidence',
    name: 'storageevidence',
    component: Layout,
    icon: 'md-person',
    selfIcon: mng1025,
    selfIconb: mng1025b,
    meta: {
      title: '区块链应用'
    },
    children: [
      {
        path: '/survival_direct',
        name: 'survival_direct',
        icon: 'md-person',
        selfIcon: mng1026,
        selfIconb: mng1026b,
        meta: {
          title: '存证应用'
        },
        component: () => import('@/views/storageevidence/survival/index.vue'),
        children: [
          {
            path: '/survival_details/:survivalId',
            name: 'survival_details',
            icon: 'md-pricetag',
            meta: {
              title: '存证详情'
            },
            hidden: true,
            component: () => import('@/views/storageevidence/survival/details.vue')
          },
          {
            path: '/new_deposit_certificate',
            name: 'new_deposit_certificate',
            icon: 'md-pricetag',
            meta: {
              title: '新增凭证'
            },
            hidden: true,
            component: () => import('@/views/storageevidence/survival/new_deposit_certificate.vue')
          }
        ]
      },
      {
        path: '/contractadmin_table',
        name: 'contractadmin_table',
        icon: 'md-person',
        selfIcon: mng1026,
        selfIconb: mng1026b,
        meta: {
          title: '合约库管理'
        },
        component: () => import('@/views/storageevidence/admin_contract/index.vue'),
        children: [
          {
            path: '/contractadmin_details',
            name: 'contractadmin_details',
            icon: 'md-pricetag',
            meta: {
              title: '合约库管理详情'
            },
            hidden: true,
            component: () => import('@/views/storageevidence/admin_contract/contractadmin_details.vue')
          }
        ]
      },
      // {
      //   path: '/operation_records',
      //   name: 'operation_records',
      //   icon: 'md-person',
      //   selfIcon: mng1029,
      //   selfIconb: mng1029b,
      //   meta: {
      //     title: '存证操作记录'
      //   },
      //   component: () => import('@/views/storageevidence/operation_records.vue'),
      // },
      {
        path: '/survival_log',
        name: 'survival_log',
        icon: 'md-person',
        selfIcon: mng1027,
        selfIconb: mng1027b,
        meta: {
          title: '用户行为记录'
        },
        component: () => import('@/views/storageevidence/survival_log.vue')
      },
      {
        path: '/system_log',
        name: 'system_log',
        icon: 'md-person',
        selfIcon: mng1028,
        selfIconb: mng1028b,
        meta: {
          title: '应用行为记录'

        },
        component: () => import('@/views/storageevidence/system_log.vue')
      },
      {
        path: '/contract_market',
        name: 'contract_market',
        icon: 'md-person',
        selfIcon: mng1030,
        selfIconb: mng1030b,
        meta: {
          title: '合约市场'
        },
        component: () => import('@/views/storageevidence/contractmarket/index.vue'),
        children: [
          {
            path: '/shelves_detail',
            name: 'shelves_detail',
            meta: {
              title: '上架合约查看'
            },
            hidden: true,
            component: () => import('@/views/storageevidence/contractmarket/shelves-detail.vue')
          },
          {
            path: '/my_detail',
            name: 'my_detail',
            meta: {
              title: '我的上架详情'
            },
            hidden: true,
            component: () => import('@/views/storageevidence/contractmarket/my-detail.vue')
          },
          {
            path: '/my_applyfordetail',
            name: 'my_applyfordetail',
            meta: {
              title: '我的申请详情'
            },
            hidden: true,
            component: () => import('@/views/storageevidence/contractmarket/my-applyfor.vue')
          }
        ]
      },
      {
        path: '/template_table',
        name: 'template_table',
        icon: 'md-bookmarks',
        selfIcon: eos1011,
        selfIconb: eos1011b,
        meta: {
          title: '合约模板'
        },
        component: () => import('@/views/eos/mytemplate/index.vue'),
        children: [
          {
            path: '/tem_modify',
            name: 'tem_modify',
            meta: {
              title: '合约模板修改'
            },
            hidden: true,
            component: () => import('@/views/eos/mytemplate/template-modify.vue')
          },
          {
            path: '/template_details',
            name: 'template_details',
            icon: 'md-pricetag',
            meta: {
              title: '合约模板详情'
            },
            hidden: true,
            component: () => import('@/views/eos/mytemplate/template-details.vue')
          },
          {
            path: '/template_newmodal',
            name: 'template_newmodal',
            icon: 'md-pricetag',
            meta: {
              title: '新建模板类型'
            },
            hidden: true,
            component: () => import('@/views/eos/mytemplate/template-newmodal.vue')
          }
        ]
      }
    ]
  },
  {
    path: '/userinfo',
    name: 'user_info',
    component: Layout,
    icon: 'md-person',
    selfIcon: mng1024,
    selfIconb: mng1024b,
    hidden: true,
    meta: {
      title: '个人中心'
    },
    children: [{
      path: '/user_index',
      name: 'user_index',
      icon: 'md-apps',
      meta: {
        title: '个人中心'
      },
      component: () => import('@/views/userinfo/index.vue'),
      // component: () => import('@/views/userinfo/user-info.vue'),
      children: [{
        path: '/userinfo_index',
        name: 'user_info',
        icon: 'md-apps',
        meta: {
          title: '个人中心'
        },
        hidden: true,
        component: () => import('@/views/userinfo/user-info.vue')
      },
      {
        path: '/new_workorder',
        name: 'new_workorder',
        meta: {
          title: '新建工单'
        },
        hidden: true,
        component: () => import('@/views/userinfo/new-workorder.vue')
      },
      {
        path: '/workorder_detail/:orderId',
        name: 'workorder_detail',
        meta: {
          title: '工单详情'
        },
        hidden: true,
        component: () => import('@/views/userinfo/workorder-detail.vue')
      }
      ]
    }]
  },
  // {
  //   path: '/user_statistics',
  //   name: 'user_statistics',
  //   component: Layout,
  //   icon: 'md-home',
  //   selfIcon: dash,
  //   selfIconb: dashb,
  //   hidden: true, // 不在侧边栏显示
  //   meta: {
  //     title: '用户统计'
  //   },
  //   children: [
  // ]
  // },
  {
    path: '/about_index',
    name: 'about_index',
    icon: 'md-star',
    selfIcon: mng1033,
    selfIconb: mng1033b,
    meta: {
      title: '关于CMBaaS'
    },
    component: Layout,
    hidden: true, // 不在侧边栏显示
    children: [
      {
        path: '/about_index',
        name: 'about_index',
        icon: 'md-contacts',
        selfIcon: mng1034,
        selfIconb: mng1034b,
        meta: {
          title: '关于我们'
        },
        component: () => import('@/views/about/index.vue')
      }
      // {
      //   path: '/iteration_description',
      //   name: 'iteration_description',
      //   icon: 'md-contacts',
      //   selfIcon: mng1034,
      //   selfIconb: mng1034b,
      //   meta: {
      //     title: '版本迭代说明'
      //   },
      //   component: () => import('@/views/about/iteration-description.vue')
      // },
      // {
      //   path: '/operation_manual',
      //   name: 'operation_manual',
      //   icon: 'md-contacts',
      //   selfIcon: mng1035,
      //   selfIconb: mng1035b,
      //   meta: {
      //     title: '用户操作手册'
      //   },
      //   component: () => import('@/views/about/operation-manual.vue')
      // },
      // {
      //   path: '/problem_solving',
      //   name: 'problem_solving',
      //   icon: 'md-contacts',
      //   selfIcon: mng1036,
      //   selfIconb: mng1036b,
      //   meta: {
      //     title: '常见问题解答'
      //   },
      //   component: () => import('@/views/about/problem-solving.vue')
      // }
    ]
  },
  {
    path: '/application_center',
    name: 'application_center',
    component: Layout,
    icon: 'md-home',
    selfIcon: app01,
    selfIconb: app01b,
    meta: {
      title: '应用中心'
    },
    children: [{
      path: '/application_index',
      name: 'application_center',
      icon: 'md-apps',
      selfIcon: app01,
      selfIconb: app01b,

      meta: {
        title: '应用中心'
      },
      component: () => import('@/views/applicationCenter/component-center.vue')
    }]
  },
  {
    path: '/ipfs_network',
    name: 'ipfs_network',
    component: Layout,
    icon: 'md-home',
    selfIcon: app01,
    selfIconb: app01b,
    meta: {
      title: 'IPFS网络'
    },
    children: [
      {
        path: '/ipfs_index',
        name: 'ipfs_index',
        icon: 'md-apps',
        selfIcon: app01,
        selfIconb: app01b,
        meta: {
          title: 'IPFS网络'
        },
        component: () => import('@/views/ipfsNetwork/index.vue'),
        children: [
          {
            path: '/ipfs_network',
            name: 'ipfs_network',
            icon: 'md-apps',
            selfIcon: app01,
            selfIconb: app01b,
            meta: {
              title: 'IPFS网络'
            },
            component: () => import('@/views/ipfsNetwork/table.vue')
          },
          {
            path: '/ipfs_new',
            name: 'ipfs_new',
            icon: 'md-apps',
            selfIcon: app01,
            selfIconb: app01b,
            meta: {
              title: '新建ipfs'
            },
            component: () => import('@/views/ipfsNetwork/new_ipfs.vue')
          },
          {
            path: '/ipfs_detail',
            name: 'ipfs_detail',
            icon: 'md-apps',
            selfIcon: app01,
            selfIconb: app01b,
            meta: {
              title: 'IPFS详情'
            },
            component: () => import('@/views/ipfsNetwork/detail.vue')
          }
        ]
      }
    ]

  },

  {
    path: '/balance_index',
    name: 'balance_index',
    icon: 'md-star',
    selfIcon: mng,
    selfIconb: mngb,
    meta: {
      title: '天平链服务'
    },
    component: Layout,
    children: [
      {
        path: '/data_service',
        name: 'data_service',
        icon: 'logo-usd',
        selfIcon: mng1020,
        selfIconb: mng1020b,
        meta: {
          title: '数据服务程序'
        },
        component: () => import('@/views/balanceChain/data-service.vue')
      },
      {
        path: '/resource_service',
        name: 'resource_service',
        icon: 'logo-usd',
        selfIcon: mng1020,
        selfIconb: mng1020b,
        meta: {
          title: '服务资源统计'
        },
        component: () => import('@/views/balanceChain/resource-service.vue')
      },
      {
        path: '/business_search',
        name: 'business_search',
        icon: 'logo-usd',
        selfIcon: mng1020,
        selfIconb: mng1020b,
        meta: {
          title: '交易查询'
        },
        component: () => import('@/views/balanceChain/business-search.vue')
      },
      {
        path: '/user_resource',
        name: 'user_resource',
        icon: 'logo-usd',
        selfIcon: mng1020,
        selfIconb: mng1020b,
        meta: {
          title: '用户资源管理'
        },
        component: () => import('@/views/balanceChain/user-resource.vue')
      },
      {
        path: '/serve_resource',
        name: 'serve_resource',
        icon: 'logo-usd',
        selfIcon: mng1020,
        selfIconb: mng1020b,
        meta: {
          title: '数据服务配置'
        },
        component: () => import('@/views/balanceChain/serve-resource.vue')
      }
    ]
  },

  {
    path: '*',
    component: () => import('@/views/error_404.vue')
  },
  // 预言机路由
  ...prophecyManagementRouters
]
