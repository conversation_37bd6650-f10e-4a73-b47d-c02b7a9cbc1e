<template>
  <div style="height:100%;">
    <div style="padding:20px 0px 40px 10px;">
      <div style="float:left;">
        <Select filterable :class="className" @on-open-change="selectClassName" v-model="chain.chainId" placeholder="选择目标链" @on-change="changeChain" style="width:280px">
          <Option v-for="item in chainIdList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
          <Option :value="chain.chainId" :label="chain.chainId" :disabled="true" v-if="pageParam.pageIndex < pages && chainIdList.length>0" style="text-align:center">
            <span @mouseover="handleReachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
          </Option>
          <Option :value="chain.chainId" :label="chain.chainId" :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
            <span style="font-size:8px;">已加载全部</span>
          </Option>
        </Select>
      </div>
      <div style="float:right;">
        <Input prefix="ios-search" type="text" style="width:400px;margin-right:-1px;" v-model="searchData" placeholder="区块高度/交易哈希/账户名称" @keyup.enter.native="browserBlur(searchData)">
        </Input>
        <Button type="primary" style="padding:2px;width:75px;height:31px;" @click="browserBlur(searchData)"> 搜索</Button>
      </div>
    </div>
    <div v-show="!isShow">
      <div class="title">
        <div class="bs"></div>
        <div>实时数据</div>
      </div>
      <Row :gutter="18">
        <i-col :xs="12" :md="12" :lg="6">
          <div class="bg-title">
            <img class="imgs" :src="imagesurl1">
            <div class="content" :style="blockHeight[0].params.unit ? '' : 'padding-top:5px;'">
              <p class="bg-title1">区块高度</p>
              <p ref="init" class="bg-title2">{{blockHeight[0].count}}</p>
              <div class="tip" :style="getWidth(blockHeight[0].params.val)" v-if="blockHeight[0].params.unit">
                <div class="inner">
                </div>
                <div class="inner1" :style="blockHeight[0].params.val >5 ? 'margin-left:-0.8vw;' : ''">
                  <span>{{blockHeight[0].params.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </i-col>
        <i-col :xs="12" :md="12" :lg="6">
          <div class="bg-title">
            <img class="imgs" :src="imagesurl2">
            <div class="content" :style="blockIrreversible[0].params.unit ? '' : 'padding-top:5px;'">
              <p class="bg-title1">不可逆区块数</p>
              <p class="bg-title2">{{blockIrreversible[0].count}}</p>
              <div class="tip" :style="getWidth(blockIrreversible[0].params.val)" v-if="blockIrreversible[0].params.unit">
                <div class="inner" style="background-color:#ef5c5e"></div>
                <div class="inner1" :style="blockIrreversible[0].params.val >5 ?'margin-left:-0.8vw;' : ''">
                  <span>{{blockIrreversible[0].params.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </i-col>
        <i-col :xs="12" :md="12" :lg="6">
          <div class="bg-title">
            <img class="imgs" :src="imagesurl3">
            <div class="content" :style="tradeCount[0].params.unit ? '' : 'padding-top:5px;'">
              <p class="bg-title1">交易数<span v-if="tradeCount[0].unit">({{tradeCount[0].unit}})</span></p>
              <p class="bg-title2">{{tradeCount[0].count}}</p>
              <div class="tip" :style="getWidth(tradeCount[0].params.val)" v-if="tradeCount[0].params.unit">
                <div class="inner" style="background-color:#677daa">
                </div>
                <div class="inner1">
                  <span>{{tradeCount[0].params.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </i-col>
        <i-col :xs="12" :md="12" :lg="6">
          <div class="bg-title">
            <img class="imgs" :src="imagesurl4">
            <div class="content" :style="contractCount[0].params.unit ? '' : 'padding-top:5px;'">
              <p class="bg-title1">合约数<span v-if="contractCount[0].unit">({{contractCount[0].unit}})</span></p>
              <p class="bg-title2">{{contractCount[0].count}}</p>
              <div class="tip" :style='getWidth(contractCount[0].params.val)' v-if="contractCount[0].params.unit">
                <div class="inner" style="background-color:#53577c"></div>
                <div class="inner1">
                  <span>{{contractCount[0].params.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </i-col>
      </Row>
      <Row :gutter="18" style="padding-top:10px;">
        <i-col :xs="12" :md="12" :lg="6" class="col-style">
          <card :bordered="false" class="card-style" :style="{ animationDelay: delay}" @mouseover.native="getTpsPercentInParent">
            <div class="tps-style">
              <div class="tps-style-1">
                <p class="tps-style1">TPS（当前/最高）</p>
                <p class="tps-style2">{{ tps.split('/')[0] }}<span style="margin: 0 7px 0 7px;">/</span>{{ tps.split('/')[1] }}</p>
              </div>
              <div class="tps-style-2" v-if="tps">
                <i-Circle :style="{ animationDelay: delay}" :percent="tpsPercent" :size="55" class="circle-style" stroke-color="#fff" trail-color="#eaedf133" stroke-linecap="round" :stroke-width="28" :trail-width="28">
                </i-Circle>
              </div>
            </div>
          </card>
        </i-col>
        <i-col :xs="12" :md="12" :lg="6" class="col-style">
          <card :bordered="false" class="card-style" :style="{ animationDelay: delay}" @mouseover.native="getNetPercentInParent" @mouseout.native="getNetPercentOutParent">
            <div class="tps-style">
              <div class="tps-style-1">
                <p class="tps-style1">网络（每区块/全链）</p>
                <p class="tps-style2" :style="{fontSize:netFontStyle}"><span class="size-change">{{ net }}</span><span style="margin: 0 7px 0 7px;">/</span><span :style="{fontSize:fontStyle}">{{net_max}}</span></p>
              </div>
              <div class="tps-style-2">
                <i-Circle :style="{ animationDelay: delay}" :percent="netPercent" :size="55" class="circle-style" stroke-color="#fff" trail-color="#eaedf133" stroke-linecap="round" :stroke-width="28" :trail-width="28">
                </i-Circle>
              </div>
            </div>
          </card>
        </i-col>
        <i-col :xs="12" :md="12" :lg="6" class="col-style">
          <card :bordered="false" class="card-style" :style="{ animationDelay: delay}" @mouseover.native="getCpuPercentInParent" @mouseout.native="getCpuPercentOutParent">
            <div class="tps-style">
              <div class="tps-style-1">
                <p class="tps-style1">计算资源（区块/链）</p>
                <p class="tps-style2"><span class="size-change">{{ cpu }}</span><span style="margin: 0 7px 0 7px;">/</span><span :style="{fontSize:cpuFontStyle}">{{cpu_max}}</span></p>
              </div>
              <div class="tps-style-2">
                <i-Circle :style="{ animationDelay: delay}" :percent="cpuPercent" :size="55" class="circle-style" stroke-color="#fff" trail-color="#eaedf133" stroke-linecap="round" :stroke-width="28" :trail-width="28">
                </i-Circle>
              </div>
            </div>
          </card>
        </i-col>
        <i-col :xs="12" :md="12" :lg="6" class="col-style">
          <card :bordered="false" class="card-style" @mouseenter.native="totalEnter">
            <div class="tps-style">
              <div class="tps-style-1">
                <p class="tps-style1">内存总量</p>
                <!-- <p class="tps-style2">{{ ram }}</p> -->
                <div class="tps-style2 diy">
                  <span class="roll-wrap" v-for="(item, index) in String(parseInt(ram))" :key="index" :class="{ start: index != 0, end: index == 0 }">
                    <span> {{ item }}</span>
                    <span> {{ item - 1 }}</span>
                    <span v-if="index != 0">{{ item - 2 }}</span>
                    <span v-if="index != 0">{{ item - 1 }}</span>
                    <span>{{ item }}</span>
                  </span>

                  <span class="roll-text">{{ ram.slice(String(parseInt(ram)).length) }}</span>
                </div>
              </div>
              <div class="tps-style-2 style-diy">
                <i-Circle :percent="piePercent" dashboard :size="66" class="circle-style diy" stroke-color="#eaedf133" trail-color="#eaedf133" stroke-linecap="round" :stroke-width="28" :trail-width="28" style="transparant: 0.5">
                  <div class="line"></div>
                </i-Circle>
              </div>
            </div>
          </card>
        </i-col>
      </Row>
      <div class="login_header">
        <div @click="cur=0" :class="{active:cur===0}" class="login_header_1">
          <img :src="cur === 0 ? logo1s : logo1" style="cursor:pointer;margin-right:10px;vertical-align:middle" />
          <span>最近区块</span>
        </div>
        <div @click="cur=1" :class="{active:cur===1}" class="login_header_2" style="">
          <img :src="cur === 1 ? logo2s : logo2" style="cursor:pointer;margin-right:10px;vertical-align:middle">
          <span>最近交易</span>
        </div>
      </div>
      <div v-show="cur===0" class="tab-1">
        <edit-table-mul :columns="columns" v-model="tableData"></edit-table-mul>
      </div>
      <div v-show="cur===1" class="tab-1">
        <edit-table-mul :columns="columns2" v-model="tableData2"></edit-table-mul>
      </div>
    </div>
    <div v-show="isShow" class="show-style">
      <img class="imgs" :src="showUrl">
      <p class="msg-style">{{showMsg}}</p>
    </div>
  </div>
</template>

<script>
import { getBrowserMain, getBlocksData, getTradesData, getChainIdList } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import { isBlockNum, isAccount, isTrxId } from '@/lib/check'
import { transform, transformCpu, transferData, transferVal } from '@/lib/transformUnit'
import { mapState, mapActions } from 'vuex'
export default {
  name: 'browser_index',
  components: {
    EditTableMul
  },
  data () {
    return {
      isShow: false,
      showUrl: require('@/assets/img/null.png'),
      showMsg: '',
      piePercent: 0, // 总量进度
      cur: 0,
      searchData: '',
      timer: null,
      netFontStyle: `1.77vw`,
      fontStyle: `1.77vw`,
      cpuFontStyle: `1.77vw`,
      imagesurl1: require('@/assets/img/browser/bg-1.png'),
      imagesurl2: require('@/assets/img/browser/bg-2.png'),
      imagesurl3: require('@/assets/img/browser/bg-3.png'),
      imagesurl4: require('@/assets/img/browser/bg-4.png'),
      logo1: require('@/assets/img/browser/logo1.png'),
      logo1s: require('@/assets/img/browser/logo1s.png'),
      logo2: require('@/assets/img/browser/logo2.png'),
      logo2s: require('@/assets/img/browser/logo2s.png'),
      num: 0,
      blockHeight: [{ count: 0, unit: '', params: { unit: '', val: 0 } }],
      blockIrreversible: [{ count: 0, unit: '', params: { unit: '', val: 0 } }],
      tradeCount: [{ count: 0, unit: '', params: { unit: '', val: 0 } }],
      contractCount: [{ count: 0, unit: '', params: { unit: '', val: 0 } }],
      chain: {
        chainId: 0,
        chainName: ''
      },
      tps: '0/0',
      net: '0',
      net_max: '0',
      cpu: '0',
      cpu_max: '0',
      ram: '0',
      netPercent: 0,
      cpuPercent: 0,
      tpsPercent: 0,
      maxCpuPercent: 0,
      maxNetPercent: 0,
      maxTpsPercent: 0,
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      tablePageParam2: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        {
          key: 'blockHeight',
          title: '区块高度',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  marginRight: '5px',
                  color: 'blue',
                  whiteSpace: 'nowrap',
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    this.handleGoBlock(params.row.blockHeight)
                  }
                }
              }, params.row.blockHeight)
            ])
          }
        },
        { key: 'blockHash', title: '区块哈希', minWidth: 300 },
        { key: 'tradeCount', title: '交易数量' },
        { key: 'blockTime', title: '时间' }
      ],
      columns2: [
        {
          key: 'tradeHash',
          title: '交易哈希',
          minWidth: 300,
          render: (h, params, key) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  marginRight: '5px',
                  color: 'blue',
                  whiteSpace: 'nowrap',
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    this.handleGoTrade(params.row.tradeHash)
                  }
                }
              }, params.row.tradeHash)
            ])
          }
        },
        {
          key: 'blockHeight',
          title: '区块高度',
          render: (h, params, key) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  marginRight: '5px',
                  color: 'blue',
                  whiteSpace: 'nowrap',
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    this.handleGoBlock(params.row.blockHeight)
                  }
                }
              }, params.row.blockHeight)
            ])
          }
        },
        { key: 'tradeTime', title: '交易时间' }
      ],
      tableData: [],
      tableData2: [],
      hisPath: '',
      className: 'select-style1',
      pageParam: { pageTotal: 0, pageSize: 60, pageIndex: 1 },
      pages: 0,
      chainIdList: [],
      imgUrl: require('@/assets/img/arrow.png'),
      cha: {}
    }
  },
  computed: {
    delay () {
      // 转化为延迟多少秒
      return `-${this.num}s`
    },
    ...mapState({
      chains: state => state.user.chain
    })
  },
  methods: {
    ...mapActions([
      'updateChain'
    ]),
    selectClassName () {
      this.className = this.className === 'select-style1' ? 'select-style2' : 'select-style1'
    },
    // 总量进度图进入
    totalEnter () {
      this.piePercent = 0
      const timer = setInterval(() => {
        this.piePercent += 10
        if (this.piePercent >= 100) {
          this.piePercent = 100
          clearInterval(timer)
        }
      }, 100)
    },
    fn2 () {
      this.piePercent = 20
    },
    getWidth (val) {
      return `width: ${0.2 + val}vw`
    },
    getFont () {
      this.fontStyle = `1.07vw`
      if (this.net.indexOf('Byte(s)') !== -1) {
        this.netFontStyle = `1.07vw`
      } else {
        this.netFontStyle = `1.77vw`
      }
    },
    getFontOut (value) {
      this.fontStyle = `1.77vw`
      this.netFontStyle = `1.77vw`
    },
    getCpuFont (value) {
      this.cpuFontStyle = `1.07vw`
    },
    getCpuFontOut (value) {
      this.cpuFontStyle = `1.77vw`
    },
    getCpuPercentInParent (value) {
      this.getCpuPercentIn()
      this.getCpuFont(this.cpu_max)
    },
    getCpuPercentOutParent (value) {
      this.getCpuFontOut()
    },
    getCpuPercentIn (value) {
      this.cpuPercent = 0
      const timer = setInterval(() => {
        this.cpuPercent += this.maxCpuPercent / 10
        if (this.cpuPercent >= this.maxCpuPercent) {
          this.cpuPercent = this.maxCpuPercent
          clearInterval(timer)
        }
      }, 100)
    },
    getCpuPercentOut (value) {
      this.cpuPercent = 0
    },
    getNetPercentInParent (value) {
      this.getFont()
      this.getNetPercentIn()
    },
    getNetPercentOutParent (value) {
      this.getFontOut()
    },
    getNetPercentIn (value) {
      this.netPercent = 0
      const timer = setInterval(() => {
        this.netPercent += this.maxNetPercent / 10
        if (this.netPercent >= this.maxNetPercent) {
          this.netPercent = this.maxNetPercent
          clearInterval(timer)
        }
      }, 100)
    },
    getNetPercentOut (value) {
      this.netPercent = 0
    },
    getTpsPercentInParent (value) {
      this.getTpsPercentIn()
    },
    getTpsPercentIn (value) {
      this.tpsPercent = 0
      const timer = setInterval(() => {
        this.tpsPercent += this.maxTpsPercent / 10
        if (this.tpsPercent >= this.maxTpsPercent) {
          this.tpsPercent = this.maxTpsPercent
          clearInterval(timer)
        }
      }, 100)
    },
    getTpsPercentOut (value) {
      this.tpsPercent = 0
    },
    startAnimate (step, limit, speed) {
      setTimeout(() => {
        if (this.num < limit) {
          this.num += step
          this.startAnimate(step, limit, speed)
        } else {
          this.num = limit
        }
      }, speed)
    },
    browserBlur (val) {
      if (isBlockNum(val)) {
        this.$router.push({
          name: 'browser_block',
          query: {
            blockNum: val,
            chain: JSON.stringify(this.chain),
            tag: true
          }
        })
      } else if (isAccount(val)) {
        this.$router.push({
          name: 'browser_chain',
          query: {
            accountName: val,
            chain: JSON.stringify(this.chain),
            tag: true
          }
        })
      } else if (isTrxId(val)) {
        this.$router.push({
          name: 'browser_trade',
          query: {
            trxId: val,
            chain: JSON.stringify(this.chain),
            tag: true
          }
        })
      } else {
        if (val === '') {
          this.msgInfo('warning', '未输入任何查询信息，请检查！', true)
        } else {
          this.msgInfo('warning', '输入信息有误，请检查！', true)
        }
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    transformData (val1, val2, val3) {
      if (!val3) {
        val2[0].count = val1
        val2[0].params.unit = transferData(val1).unit
        val2[0].params.val = transferData(val1).val
      } else {
        val2[0].count = transferVal(val1).value
        val2[0].unit = transferVal(val1).unit
        val2[0].params.unit = transferData(val2[0].count).unit
        val2[0].params.val = transferData(val2[0].count).val
      }
    },
    // 查询实时数据
    getBrowserData (chainId) {
      getBrowserMain(chainId).then(res => {
        if (res.code === '00000') {
          this.isShow = false
          this.transformData(res.data.blockHeight ? res.data.blockHeight : 0, this.blockHeight, false)
          this.transformData(res.data.blockIrreversible ? res.data.blockIrreversible : 0, this.blockIrreversible, false)
          this.transformData(res.data.tradeCount ? res.data.tradeCount : 0, this.tradeCount, true)
          this.transformData(res.data.contractCount ? res.data.contractCount : 0, this.contractCount, true)
          this.tps = res.data.tps ? res.data.tps : '0/0'
          this.net = res.data.net ? res.data.net : '0'
          this.net_max = res.data.netMax ? res.data.netMax : '0'
          this.cpu = res.data.cpu ? res.data.cpu : '0'
          this.cpu_max = res.data.cpuMax ? res.data.cpuMax : '0'
          this.ram = res.data.ram ? res.data.ram : '0'
          // 百分比
          this.maxNetPercent = (Number(this.net) / Number(this.net_max)) * 100
          this.maxCpuPercent = (Number(this.cpu) / Number(this.cpu_max)) * 100
          this.maxTpsPercent = Number(this.tps.split('/')[0]) / Number(this.tps.split('/')[1]) * 100
          // 单位换算
          this.net = transform(+this.net)
          this.net_max = transform(+this.net_max)
          this.cpu = transformCpu(+this.cpu)
          this.cpu_max = transformCpu(+this.cpu_max)
          this.ram = transform(+this.ram)
          this.getTableData(chainId)
          this.getTableData2(chainId)
        } else {
          this.isShow = true
          this.showMsg = '当前区块链网络未部署相应服务，导致获取数据失败，请联系移动办公群客服人员咨询'
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.isShow = true
        this.showMsg = '当前区块链网络未部署相应服务，导致获取数据失败，请联系移动办公群客服人员咨询'
        this.msgInfo('error', error.message, true)
        if (error.code === 'C0005') {
          clearInterval(this.timer)
        }
      })
    },
    // 查询最近区块
    getTableData (chainId) {
      getBlocksData(chainId).then(res => {
        if (res.code === '00000') {
          this.tableData = res.data ? res.data : []
        } else {
          this.isShow = true
          this.showMsg = '当前区块链网络未部署相应服务，导致获取数据失败，请联系移动办公群客服人员咨询'
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.isShow = true
        this.showMsg = '当前区块链网络未部署相应服务，导致获取数据失败，请联系移动办公群客服人员咨询'
        this.msgInfo('error', error.message, true)
        if (error.code === 'C0005') {
          clearInterval(this.timer)
        }
      })
    },
    // 查询最近交易
    getTableData2 (chainId) {
      getTradesData(chainId).then(res => {
        if (res.code === '00000') {
          this.tableData2 = res.data ? res.data : []
        } else {
          this.isShow = true
          this.showMsg = '当前区块链网络未部署相应服务，导致获取数据失败，请联系移动办公群客服人员咨询'
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.isShow = true
        this.showMsg = '当前区块链网络未部署相应服务，导致获取数据失败，请联系移动办公群客服人员咨询'
        this.msgInfo('error', error.message, true)
        if (error.code === 'C0005') {
          clearInterval(this.timer)
        }
      })
    },
    // 区块高度超链接
    handleGoBlock (value) {
      this.$router.push({
        name: 'browser_block',
        query: {
          blockNum: value,
          chain: JSON.stringify(this.chain)
        }
      })
    },
    // 交易哈希超链接
    handleGoTrade (value) {
      this.$router.push({
        name: 'browser_trade',
        query: {
          trxId: value,
          chain: JSON.stringify(this.chain)
        }
      })
    },
    startTimer () {
      if (this.timer) {
        clearInterval(this.timer)
      } else {
        this.timer = setInterval(() => {
          this.startAnimate(1, this.cpuPercent, 50)
          if (this.chain.chainId !== 0) {
            this.getBrowserData(this.chain.chainId)
          }
        }, 10000)
      }
    },
    handleReachBottom () {
      if (this.pageParam.pageIndex < this.pages) {
        this.pageParam.pageIndex += 1
        this.getChainList(true)
      }
    },
    getChainList (flag) {
      getChainIdList(this.pageParam).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        }
        else {
          if (flag) {
            let index = res.data.records.findIndex(item => {
              if (item.chainId === this.cha.chainId) {
                return true
              }
            })
            if (index !== -1) {
              res.data.records.splice(index, 1)
            }
            this.chainIdList.push.apply(this.chainIdList, res.data.records)
          } else {
            this.chainIdList = res.data.records && res.data.records.length > 0 ? res.data.records : []
            if (JSON.stringify(this.cha) !== '{}') {
              this.chain = this.cha
              this.pushChainId()
            } else {
              this.chain.chainId = this.chains.chainId || this.chainIdList.length > 0 ? this.chainIdList.length > 0 ? this.chainIdList[0].chainId : 0 : 0
              this.chain.chainName = this.chains.chainName || this.chainIdList.length > 0 ? this.chainIdList.length > 0 ? this.chainIdList[0].chainName : '' : ''
            }
          }
          this.pageParam = {
            pageTotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          this.size = this.chainIdList.length
          this.pages = res.data.pages
          this.chain.chainId ? this.chain.chainId ? this.getBrowserData(this.chain.chainId) : '' : ''
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    pushChainId () {
      let flag = false
      for (let i in this.chainIdList) {
        if (this.chainIdList[i].chainId === this.cha.chainId) {
          flag = true
          break
        }
      }
      if (!flag) {
        this.chainIdList.push(this.cha)
      }
    },
    changeChain () {
      if (this.chain.chainId !== 0) {
        this.getChainName(this.chain.chainId)
        this.getBrowserData(this.chain.chainId)
      }
    },
    getChainName (value) {
      for (var item in this.chainIdList) {
        if (this.chainIdList[item].chainId === value) {
          this.chain.chainName = this.chainIdList[item].chainName
        }
      }
    }
  },
  mounted () {
    if (this.hisPath !== '') {
      this.cha = this.chains
    }
    this.getChainList()
    this.startTimer()
  },
  deactivated () {
    clearInterval(this.timer)
    this.timer = null
  },
  destroyed () {
    this.updateChain({})
    clearInterval(this.timer)
    this.timer = null
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (from.name === 'browser_block' || from.name === 'browser_trade' || from.name === 'browser_chain') {
        vm.hisPath = from.name
      } else {
        vm.hisPath = ''
      }
    })
  },
  watch: {
    cpuPercent () {
      this.startAnimate(1, this.cpuPercent, 50)
    },
    netPercent () {
      this.startAnimate(1, this.netPercent, 50)
    },
    tpsPercent () {
      this.startAnimate(1, this.tpsPercent, 50)
    },
    fontStyle (newVal, oldVal) {
      this.fontStyle = newVal
    },
    cpuFontStyle (newVal, oldVal) {
      this.cpuFontStyle = newVal
    }
    // screenHeight (val) {
    //   // 为了避免频繁触发resize函数导致页面卡顿，使用定时器
    //   if (!this.timer) {
    //     // 一旦监听到的screenWidth值改变，就将其重新赋给data里的screenWidth
    //     this.screenHeight = val
    //     this.timer = true
    //     let that = this
    //     setTimeout(function () {
    //       // 打印screenWidth变化的值
    //       console.log(that.screenHeight)
    //       that.timer = false
    //     }, 400)
    //   }
    // }

  }
}
</script>

<style lang="less" scoped>
.active {
  color: #3d73ef;
  padding-bottom: 8px;
  border-bottom: 3px solid #3d73ef;
  cursor: pointer;
}
/deep/.ivu-card-body {
  padding: 0px !important;
}
.circles:hover {
  display: block;
}
.title {
  height: 18px;
  font-weight: bold;
  font-size: 16px;
  font-family: "Microsoft YaHei";
  line-height: 18px;
  color: #333333;
  margin: 30px 0 10px 10px;
  vertical-align: middle;
  .bs {
    float: left;
    width: 6px;
    height: 18px;
    background: #19c3a0;
    opacity: 1;
    border-radius: 3px;
    margin-right: 6px;
  }
}
.font {
  font-family: "D-DIN";
}
.bg-title {
  position: relative;
  color: #ffffff;
  .content {
    text-align: left;
    position: absolute;
    z-index: 2;
    top: 45%;
    left: 25%;
    transform: translate(-25%, -50%);
    .bg-title1 {
      font-size: 0.84vw;
      font-family: "Microsoft YaHei";
      font-weight: 400;
      color: #ffffff;
      text-shadow: -1px -1px 1px rgba(103, 67, 10, 0.25);
      opacity: 0.7;
    }
    .bg-title2 {
      font-size: 1.77vw;
      font-family: "D-DIN";
    }
    .tip {
      opacity: 0.6;
      width: 90px;
      height: 1px;
      border-top: 1px solid #c5c4c4;
      margin-top: -2px;
      .inner {
        background-color: #f37441;
        width: 5px;
        height: 6px;
        border: 1px solid #e2e1e1;
        position: relative;
        left: 10px;
        top: -4px;
        transform: rotate(45deg);
        border-right: 0px;
        border-bottom: 0px;
      }
      .inner1 {
        margin-top: -8px;
        font-size: 8px;
        transform: scale(0.8);
        color: #e2e1e1;
        margin-left: 4px;
      }
    }
  }
}
.imgs {
  height: auto;
  width: auto;
  width: 100%;
}
.col-style {
  height: 110px;
  text-align: center;
  vertical-align: middle;
  .card-style {
    max-width: 91%;
    margin-left: 5%;

    overflow: hidden;
    &:hover /deep/.ivu-card-body {
      overflow: hidden;
      animation: widthchange 0.3s linear alternate;
      background: -moz-linear-gradient(top, #7098f5 0%, #397cf9 100%);
      background: -webkit-linear-gradient(top, #7098f5 0%, #397cf9 100%);
      background: -o-linear-gradient(top, #7098f5 0%, #397cf9 100%);
      background: -ms-linear-gradient(top, #7098f5 0%, #397cf9 100%);
      background: linear-gradient(to right, #7098f5 0%, #397cf9 100%);
      border-radius: 6px;
      vertical-align: middle;
      @keyframes widthchange {
        from {
          width: 0px;
        }
        to {
          width: 100%;
        }
      }
      .tps-style1,
      .tps-style2 {
        color: #fff !important;
        text-align: left;
      }
      .tps-style-2 {
        margin-left: 15%;
        transition: margin 0.3s linear;
        min-width: 50px;
        width: 55px;
        opacity: 1;
        .line {
          margin-left: 48%;
          transform: translateY(2px) rotate(-23deg);
          width: 0;
          height: 0;
          border-top: 1px solid transparent;
          border-left: 40px solid #fff;
          border-bottom: 1px solid transparent;
        }
      }
      .roll-wrap {
        &.end {
          transform: translateY(-5.2vw) !important;
          transition: all 0.2s cubic-bezier(0, 0, 0.58, 1) 0.2s !important;
        }
        &.start {
          width: 20px;
          transform: translateY(-10.5vw) !important;
          transition: all 0.4s cubic-bezier(0, 0, 0.58, 1);
        }
      }
      .roll-text {
        margin-left: 0.1vw;
      }
      .size-change {
        font-size: 1.5vw;
      }
    }
    .tps-style {
      max-width: 100%;
      padding: 8px 1vw;
      display: flex;
      justify-content: center;
      .tps-style1 {
        font-size: 0.74vw;
        color: #707070;
        font-family: D-DIN;
        white-space: nowrap;
      }
      .tps-style2 {
        font-size: 1.77vw;
        color: #3d73ef;
        font-weight: 500;
        font-family: D-DIN;
        overflow: hidden;
        height: 2.4vw;
        span {
          transition: font-size 0.3s linear;
        }
        &.diy {
          display: flex;
        }
        .roll-wrap {
          display: flex;
          flex-direction: column;
          transition: all 0.3s linear;
          transform: translateY(0px);
        }
      }

      .tps-style-2 {
        width: 0px;
        overflow: hidden;
        transition: all 0.3s linear !important;
        opacity: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        .line {
          margin-left: 48%;
          transform: translateY(2px) rotate(-70deg);
          transform-origin: -8px 50%;
          margin-bottom: -20px;
          width: 0;
          height: 0;
          border-top: 1px solid transparent;
          border-left: 40px solid #fff;
          border-bottom: 1px solid transparent;
          transition: all 0.1s linear 0.3s;
        }
        &.style-diy {
          // width: 70px;
          margin-top: 0.9vw;
          height: 35px !important;
          overflow: hidden;
          .circle-style.diy {
            top: 16px;
          }
        }
      }

      .roll-wrap {
        width: 1.1vw;
      }
    }
  }
}
/deep/.ivu-card {
  transition: none;
}
.login_header {
  height: 72px;
  font-size: 16px;
  background-image: url("../../../assets/img/browser/head.png");
  background-repeat: no-repeat;
  background-size: 100% 72px;
}
.login_header_1 {
  margin-right: 30px;
  cursor: pointer;
  font-weight: bold;
  width: 100px;
  display: inline-block;
  margin-left: 30px;
}
.login_header_2 {
  cursor: pointer;
  font-weight: bold;
  padding-top: 15px;
  width: 100px;
  display: inline-block;
}

.tab-1 {
  width: 100%;
  height: 100%;
  background: #ffffff;
  opacity: 1;
  border-radius: 22px 22px 0px 0px;
  border: 1px solid #a5a4bf17;
  margin-top: -20px;
  padding: 20px;
}
/deep/.ivu-table-tip {
  overflow: hidden;
}
.show-style {
  display: table;
  text-align: center;
  vertical-align: middle;
  margin: 0 auto;
  position: relative;
  padding: 8%;
  .msg-style {
    color: #b7b8b9;
    font-size: 12px;
  }
}
</style>
<style lang="less" scoped>
/deep/.select-style1 {
  .ivu-select-arrow {
    padding: 9px;
    margin-right: -9px;
    background-color: #57a3f3;
    color: #fff;
    border-radius: 0 5px 5px 0;
    transition: none;
  }
  .ivu-icon-ios-arrow-down:before {
    color: #fff;
  }
}
/deep/.select-style2 {
  .ivu-select-arrow {
    padding: 9px;
    margin-right: -9px;
    background-color: #57a3f3;
    color: #fff;
    border-radius: 5px 0px 0px 5px;
    transition: none;
  }
  .ivu-icon-ios-arrow-down:before {
    color: #fff;
  }
}
/deep/.ivu-input {
  border-radius: 4px 0 0 4px;
}
/deep/.ivu-btn {
  border-radius: 0 4px 4px 0;
}
</style>
