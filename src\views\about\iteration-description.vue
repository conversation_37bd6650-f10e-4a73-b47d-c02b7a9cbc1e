<!--
    版本迭代说明
-->
<template>
  <div class="iteration-description">
    <div class="title-wrap">
      <span class="title"> 版本迭代说明 </span>
      <Button
        class="btn"
        type="success"
        icon="md-add"
        ghost
        @click="editionHandle(false)"
        v-if="roleId === '1'"
        >新增版本</Button
      >
    </div>
    <Table
      stripe
      border
      :columns="columns"
      :data="dataList"
    >
    </Table>
    <div class="page-wrap">
      <Page
        :total="tablePageParam.pagetotal"
        :current.sync="tablePageParam.pageIndex"
        @on-change="pageChange"
        :page-size="tablePageParam.pageSize"
        :page-size-opts="[5, 8, 10]"
        show-total
        show-elevator
        show-sizer
        @on-page-size-change="sizeChange"
        style="text-align: right"
      />
    </div>
    <operation-version
      ref="version"
      :editData="editData"
      @iterationVersionList="iterationVersionList"
    />
  </div>
</template>
<script>
import OperationVersion from './modules/operation-version.vue'
import { localRead } from '@/lib/util'
import { iterationVersionList, delIterationVersion } from '@/api/data'
export default {
  components: {
    OperationVersion
  },
  data () {
    return {
      roleId: localRead('roleId'),
      editData: 'add',
      tablePageParam: {
        pagetotal: 0,
        pageIndex: 1,
        pageSize: 5
      },
      columns: [
        {
          title: '版本',
          key: 'versionNum',
          width: 200
        },
        {
          title: '迭代日期',
          key: 'iterationDate',
          width: 200
        },
        {
          title: '迭代说明',
          key: 'iterationDesc'
        },
        {
          title: '操作',
          align: 'left',
          width: 200,
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: { type: 'text', size: 'small' },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.editionHandle(params.row)
                    }
                  }
                },
                '编辑'
              ),
              h(
                'Poptip',
                {
                  props: {
                    transfer: true,
                    placement: 'top-end',
                    confirm: true,
                    title: '确认删除吗?',
                    'ok-text': '确认',
                    'cancel-text': '取消'
                  },
                  on: {
                    'on-ok': () => {
                      this.deleteOk(params.row)
                    }
                  }
                },
                [
                  h(
                    'Button',
                    {
                      props: { type: 'text', size: 'small' },
                      style: {
                        marginRight: '8px',
                        color: '#3D73EF',
                        border: '1px solid #3D73EF'
                      }
                    },
                    '删除'
                  )
                ]
              )
            ])
          }
        }
      ],
      dataList: []
    }
  },
  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    /**
     * 编辑或新增打开弹框
     */
    editionHandle (row) {
      if (row) {
        this.editData = {
          id: row.id,
          versionNum: row.versionNum,
          iterationDate: row.iterationDate,
          iterationDesc: row.iterationDesc
        }
      } else {
        this.editData = 'add'
      }
      this.$refs.version.visible = true
    },
    /**
     * 删除
     */
    deleteOk (row) {
      delIterationVersion({ id: row.id })
        .then((res) => {
          if (res.code === '00000') {
            this.msgInfo('success', res.message, true)
            this.iterationVersionList()
          } else {
            this.msgInfo('error', res.message, true)
          }
        })
        .catch((error) => {
          this.msgInfo('error', error.message, true)
        })
    },
    /**
     * 每页条数改变
     */
    sizeChange (size) {
      this.tablePageParam.pageSize = size
      this.iterationVersionList()
    },
    /**
     * 页码改变
     */
    pageChange (page) {
      this.tablePageParam.pageIndex = page
      this.iterationVersionList()
    },
    /**
     * 获取列表
     */
    iterationVersionList () {
      let params = {
        pageSize: this.tablePageParam.pageSize,
        pageIndex: this.tablePageParam.pageIndex
      }
      iterationVersionList(params)
        .then((res) => {
          if (res.code === '00000') {
            this.dataList = res.data.records
            this.tablePageParam = {
              pagetotal: res.data.total,
              pageIndex: res.data.current,
              pageSize: res.data.size
            }
          } else {
            this.dataList = []
            this.tablePageParam.pagetotal = 0
            this.msgInfo('error', res.message, true)
          }
        })
        .catch((error) => {
          this.tablePageParam.pagetotal = 0
          this.msgInfo('error', error.message, true)
        })
    },
    /**
     * 设置table 操作 按钮权限
     * roleId === '1'  管理员
     */
    permissionControl () {
      if (this.roleId !== '1') {
        this.columns.splice(this.columns.length - 1, 1)
      }
    }
  },
  created () {
    this.permissionControl()
    this.iterationVersionList()
  }
}
</script>

<style lang="less" scoped>
.iteration-description {
  margin: 20px;
  .title-wrap {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      font-size: 18px;
      font-weight: bold;
    }
  }
  .add-color {
    color: #409eff;
    cursor: pointer;
  }
  .page-wrap {
    margin-top: 10px;
  }
  /deep/.ivu-table-body{
    overflow: hidden;
  }
  /deep/.ivu-table-cell{
    white-space:pre-line
  }
}
</style>
