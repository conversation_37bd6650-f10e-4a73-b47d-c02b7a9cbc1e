import { getListTypeMap } from '@/api/data'// authorization, newuser,
import { optionList } from '@/lib/tools'

const state = {
  options: {}
}
const getters = {
  getDict: (state) => {
    return state.options
  }
}
const mutations = {
  setOptions (state, obj) {
    let { key, value } = obj
    try {
      state.options[key] = value
    } catch (error) {
      console.log(error)
    }
  }
}
const actions = {
  getOptions (context, listType) {
    return new Promise((resolve, reject) => {
      let dict = context.state.options
      if (dict[listType]) {
        resolve(true)
        return false
      } else {
        // getListTypeMap().then(res => {
        getListTypeMap(listType).then(res => {
          if (res.code === '00000') {
            const finishData = optionList(res.data)
            context.commit('setOptions', {
              key: listType,
              value: finishData
            })
            // resolve(true, res.data)
            resolve(res.data)
          } else reject(res)
        }).catch(error => {
          reject(error)
        })
      }
    })
  }
}

export default {
  getters,
  state,
  mutations,
  actions,
  modules: {
    //
  }
}
