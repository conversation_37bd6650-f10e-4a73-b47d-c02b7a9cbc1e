<template>
  <div>
    <hr />
    <div class="new_center">
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80">
        <FormItem label="交易名称" prop="name">
          <Input v-model="formValidate.name" placeholder="请输入交易名称"></Input>
        </FormItem>
        <FormItem label="可交易链" prop="childIdOne">
          <Select v-model="formValidate.childIdOne" @on-change="onChangeName">
            <Option v-for="item in nameList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
            <Page :total="pageParam.pageTotal" size="small" show-total :page-size="pageParam.pageSize" :current="pageParam.pageIndex" @on-change="handlePageName" />
          </Select>
        </FormItem>
        <FormItem label="链账户" prop="childId1">
          <Select v-model="formValidate.childId1" :disabled="!nameList1.length">
            <Option v-for="item in nameList1" :value="item.chainAccountId" :key="item.chainAccountId">{{ item.chainAccountName }}</Option>
            <Page v-if="nameList1.length" :total="pageTotal" show-total  size="small" :page-size="pageParam1.pageSize" :current="pageParam1.pageIndex" @on-change="handlePageName1" />
          </Select>
          <p v-show="formValidate.childIdOne?!nameList1.length?true:false:false" style="color:red">该交易链未分配普通链账户请先分配</p>
        </FormItem>
        <FormItem label="交易类型" prop="radio">
          <RadioGroup v-model="disabledGroup">
            <Radio label="文件" @click.native="click_file(0)"></Radio>
            <Radio label="内容" @click.native="click_center(1)"></Radio>
          </RadioGroup>
        </FormItem>
        <!--  文件-->
        <div v-if="upload_tab == '0'" class="new_file">
          <Upload :before-upload="handleUpload" action="Url" :max-size="2 * 1024">
            <Button icon="ios-cloud-upload-outline">文件最大2M</Button>
          </Upload>

          <div v-if="file !== null">Upload&nbsp;file: {{ file.name }}</div>
          <div class="new_btn">
            <Button type="primary" @click="upload_file('formValidate')" :loading="loadingStatus">{{ loadingStatus ? "上传中" : "提交" }}</Button>
            <Button type="primary" @click="upload">返回</Button>
          </div>
        </div>

        <!-- 内容 -->
        <div class="user_log" v-if="upload_tab == '1'">
          <FormItem label="" prop="textarea">
            <Input id="a" v-model="formValidate.textarea" type="textarea" :rows="6" placeholder="请输入内容" />
          </FormItem>
          <FormItem>
            <div class="new_btn">
              <Button type="primary" :loading="loadingStatus" @click.native="upload_value('formValidate')">提交</Button>
              <Button type="primary" @click.native="upload">返回</Button>
            </div>
          </FormItem>
        </div>
      </Form>
    </div>
  </div>
</template>

<script>
import { newRecordFile, newRecordContent, getChainIdLists, getChainTableDatas } from '@/api/data'
export default {
  name: 'new_record',
  data () {
    return {
      chainstatus: 'CHAIN_SUCCESS',
      pagetotal: 0,
      des: true,
      nameList: [], // 机构列表
      nameList1: [], // 机构列表
      accountType: 'NORMAL',
      upload_tab: '0',
      file: '',
      model3: 'EOS',
      loadingStatus: false,
      Url: '',
      disabledSingle: true,
      disabledGroup: '文件',
      pageParam: { 'pageTotal': 0, 'pageSize': 10, 'pageIndex': 1 },
      pageParam1: {

        pageSize: 5,
        pageIndex: 1 },
      count: '',
      userLoginId: '', // 用户名称
      // 存证名称 存证类型
      formValidate: {
        name: '',
        check: '',
        textarea: '', // 内容存证
        childIdOne: '',
        childId1: ''

      },
      developList: [
        {
          value: 'EOS',
          label: 'EOS'
        },
        {
          value: 'Chainmaker',
          label: 'Chainmaker'
        }
      ],
      ruleValidate: {
        name: [
          {
            required: true,
            message: '这个名称不能为空',
            trigger: 'blur'
          }
        ],
        textarea: [
          {
            required: true,
            message: '这个名称不能为空',
            trigger: 'blur'
          }
        ],
        childIdOne: [
          {
            required: true,
            type: 'number',
            message: '请选择可交易链',
            trigger: 'change'
          }
        ],
        childId1: [
          {
            type: 'number',
            required: true,
            message: '请选择链账户',
            trigger: 'change'
          }
        ]
      }
    }
  },
  methods: {
    //  上传文件
    click_file (e) {
      this.upload_tab = e
    },
    click_center (e) {
      this.upload_tab = e
    },
    // upload 返回
    upload () {
      this.$router.push({ path: '/transaction_commit' })
    },
    click_type (e) {
      this.model3 = e
      e === 'EOS' ? this.modelNum = 1 : this.modelNum = 2
    },
    handleUpload (file) {
      this.file = file
      if (file.size > 2097152) {
        this.$Message.warning('文件最大2M,请重新上传！')
        this.file = ''
      } else if (file.size === 0) {
        this.$Message.warning('文件内容不能为空,请重新上传！')
        this.file = ''
      }
      return false
    },
    onChangeName (e) {
      this.pull1()
      this.formValidate.childId1 = ''
      if (this.nameList.length) {
        this.des = false
      }
    },
    // 上传文件
    upload_file (name) {
      if (this.file) {
        this.$refs[name].validate(valid => {
          if (valid) {
            this.loadingStatus = true
            newRecordFile(this.formValidate.name, this.file, this.userLoginId, this.formValidate.childIdOne, this.formValidate.childId1)
              .then(res => {
                if (res.code === 'B0001') {
                  setTimeout(() => {
                    this.loadingStatus = false
                    this.$Message.warning(res.message)
                  }, 1500)
                } else if (res.code === '00000') {
                  setTimeout(() => {
                    this.loadingStatus = false
                    const title = '提示'
                    const content = `<div><p>可根据交易id，在EOS开放网络->EOS浏览器中查询该交易</p>
                    <p style='word-wrap:break-word;'>交易ID：${res.data.trxId}</p><div>`
                    this.$Modal.success({
                      title: title,
                      content: content,
                      onOk: () => {
                        this.$router.push({ path: '/transaction_commit' })
                      }
                    })
                  }, 1500)
                } else {
                  setTimeout(() => {
                    this.loadingStatus = false
                    this.$Message.error(res.message)
                  }, 1500)
                }
              })
              .catch(error => {
                setTimeout(() => {
                  this.loadingStatus = false
                  this.$Message.error(error.message)
                }, 1500)
              })
          } else {
            this.$Message.error('请完善提交信息!')
          }
        })
      } else {
        this.$Message.error('请完善提交信息!')
      }
    },
    // descontent () {
    //   if (!this.nameList1.length) {

    //   }
    // },
    // 上传内容
    upload_value (name) {
      this.$refs[name].validate(valid => {
        if (valid) {
          this.loadingStatus = true
          newRecordContent(this.formValidate.name, this.formValidate.textarea, this.userLoginId, this.formValidate.childIdOne, this.formValidate.childId1)
            .then(res => {
              if (res.code === 'B0001') {
                setTimeout(() => {
                  this.loadingStatus = false
                  this.$Message.warning(res.message)
                }, 1500)
              } else if (res.code === '00000') {
                setTimeout(() => {
                  this.loadingStatus = false
                  const title = '提示'
                  const content = `<div><p>可根据交易id，在EOS开放网络->EOS浏览器中查询该交易</p>
                    <p style='word-wrap:break-word;'>交易ID： ${res.data.trxId}</p><div>`
                  this.$Modal.success({
                    title: title,
                    content: content,
                    styles: {
                      width: 500
                    },
                    onOk: () => {
                      this.$router.push({ path: '/transaction_commit' })
                    }
                  })
                }, 1500)
              } else {
                setTimeout(() => {
                  this.loadingStatus = false
                  this.$Message.error(res.message)
                }, 1500)
              }
            })
            .catch(error => {
              setTimeout(() => {
                this.loadingStatus = false
                this.$Message.error(error.message)
              }, 1500)
            })
        } else {
          this.$Message.error('请正确填写全部信息!')
        }
      })
    },
    // 下拉分页请求
    pull () {
      let dataConst = {
        statusKey: 'ENABLE',
        pageParam: this.pageParam
      }
      getChainIdLists(dataConst).then(res => {
        if (res.code === '00000') {
          console.log(res.data.records)
          this.nameList = res.data.records
          this.pageParam.pageSize = res.data.size
          this.pageParam.pageTotal = res.data.total
        } else {
          this.$Message.error(res.message)
        }
      }).catch((error) => {
        this.$Message.error(error.message)
      })
    },
    pull1 () {
      getChainTableDatas(this.formValidate.childIdOne, this.pageParam1, this.accountType, this.chainstatus).then(res => {
        if (res.code === '00000') {
          // console.log('链账户'+res.data.records)
          // this.nameList1 = res.data.records
          //        this.nameList1.map((item)=>{
          // return item.accountTypeKey=="NORMAL"
          //           })
          //           console.log(this.nameList1)
          // let aa = res.data.records.filter((item) => {
          //   return item.accountTypeKey == 'NORMAL'
          // })
          this.nameList1 = res.data.records
          //  console.log(this.nameList1)
          // this.pageParam1.pageSize = res.data.size
          // this.pageParam1.pageIndex = res.data.pages
          this.pageTotal = res.data.total
        } else {
          this.$Message.error(res.message)
        }
      }).catch((error) => {
        this.$Message.error(error.message)
      })
    },
    // 下拉分页
    // onChangeType (val) {
    //   this.typeData = val
    //   this.handlePageName(1)
    // },
    handlePageName (size) {
      this.pageParam.pageIndex = size
      this.pull()
    },
    handlePageName1 (size) {
      this.pageParam1.pageIndex = size
      this.pull1()
    }
  },

  created () {
    this.userLoginId = localStorage.getItem('userLoginId')
    this.pull()
  }
}
</script>

<style scoped>
.new_center {
  width: 25%;
  margin: 50px;
}
.ivu-radio-group {
  font-size: 14px;
  vertical-align: middle;
  display: flex;
  width: 319px;
  justify-content: space-around;
}
.ivu-btn-default {
  width: 384px;
  height: 180px;
}
.new_btn {
  display: flex;
  width: 220px;
  margin-top: 40px;
  justify-content: space-evenly;
}
#a {
  height: 140px;
}
.new_file {
  margin-left: 20px;
}
</style>
