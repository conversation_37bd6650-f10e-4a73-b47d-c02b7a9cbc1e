<template>
  <!-- ipfs -->
  <div>
    <div class="cz_header">
      <Input style="width:15%;margin-right:10px;" placeholder="IPFS网络名称" v-model="search_value" @on-enter="getmanagementList" />
      <Button type="primary" icon="ios-search" @click.native="getmanagementList">查询</Button>
      <Button type="primary" ghost icon="md-sync" @click.native="reset">重置</Button>
      <Button style="margin-left:10px;float:right;" type="success" ghost @click="addipfs" icon="md-add">新建IPFS</Button>
    </div>
    <div class="cz_table">
      <Table :columns="historyColumns" :data="historyData">
        <template slot-scope="{ row, index }" slot="action">
          <Button size="small" style="margin-right:5px;color:#3D73EF;border:1px solid #3D73EF;" @click="detailipfs(row)">详情</Button>
        </template>
      </Table>
      <Page :total="dataCount" :page-size="tablePageParam.pageSize" show-sizer show-total show-elevator class="paging" @on-change="changepage" :current.sync="tablePageParam.pageIndex" style="text-align: right;margin-top:20px;" @on-page-size-change="pageSizeChange" :pageSizeOpts="pageSizeOpts"></Page>
    </div>
  </div>
</template>
<script>
import { ipfsList, alicationDelete, getTenantList } from '@/api/contract'
export default {
  data () {
    return {
      deleId: '',
      pageSizeOpts: [10, 20, 40, 60, 100],
      userListData: [],
      status: '', // 搜索下拉框值
      dataCount: 0, // 总条数
      search_value: '', // 输入框
      tablePageParam: { pageIndex: 1, pageSize: 10 }, // 分页
      historyColumns: [//   table 表头
        {
          title: '序号',
          type: 'index',
          width: 80,
          align: 'center'
        },
        {
          title: 'IPFS网络名称',
          key: 'ipfsName',
          tooltip: true
        },
        {
          title: '创建者',
          key: 'creatorName',
          tooltip: true
        },
        {
          title: '描述',
          key: 'ipfsBrief',
          tooltip: true
        },
        {
          title: '创建时间',
          key: 'createTime',
          tooltip: true
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
            // const color = params.row.status === '关闭' ? '#C7C7C7' : '#15AD31'
            return h('span', {
              // props: {
              //   // type: 'dot',
              //   color: color
              // },
              style: { marginLeft: '-8px', color: params.row.status === '关闭' ? 'red' : '#15AD31' }
            }, params.row.status)
          }
        },
        {
          title: '操作',
          key: 'resultCode',
          slot: 'action',
          width: '250'
        }
      ],
      // 表格数据
      historyData: [],
      isedit: false
    }
  },
  mounted () {
    this.getmanagementList()
    this.gettenantlist()
  },

  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    // 获取租户列表
    gettenantlist () {
      getTenantList().then(res => {
        if (res.code !== '00000') {
          this.msgInfo('warning', res.message, true)
        } else {
          this.userListData = res.data
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询列表接口
    getmanagementList () {
      let params = {
        ipfsName: this.search_value,
        pageParam: this.tablePageParam // 分页
      }
      ipfsList(params).then((res) => {
        if (res.code === '00000') {
          // console.log(res)
          this.historyData = res.data.records
          this.tablePageParam = {
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          this.dataCount = res.data.total
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 打开新增配置弹窗
    addipfs () {
      this.$router.push({
        name: 'ipfs_new'
      })
    },
    // 详情
    detailipfs (row) {
      // console.log(row)
      this.$router.push({
        name: 'ipfs_detail',
        params: row
      })
      // let statusA = {
      //   '启动': 'ENABLE',
      //   '禁用': 'DISABLE'
      // }
      // this.isedit = true
      // this.formValidate = { ...row, status: statusA[row.status] }
      // this.modal1 = true
    },
    // // 删除
    // remove (id) {
    //   this.modal3 = true
    //   this.deleId = id
    // },
    // 确定删除
    detailConfig (param) {
      alicationDelete(this.deleId).then(res => {
        if (res.code === '00000') {
          this.msgInfo('success', res.message, true)
          this.getmanagementList()
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 取消删除
    closemodal3 () {
      this.modal3 = false
    },
    // 翻页
    changepage (index) {
      this.tablePageParam.pageIndex = index // 当前页
      this.getmanagementList()
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      // 当前展示条数
      this.tablePageParam.pageSize = size
      this.getmanagementList()
    },
    // 重置按钮事件
    reset () {
      this.search_value = ''
      this.status = ''
      this.tablePageParam = { pageIndex: 1, pageSize: 10 }
      this.getmanagementList()
    }
  },
  watch: {
  }

}
</script>

<style lang="less" scoped>
/deep/ .valueinput {
  textarea.ivu-input {
    padding-bottom: 15px;
    min-height: 80px !important;
    height: 120px !important;
    overflow-y: scroll !important;
  }
}
.cz_header {
  // display: flex;
  margin-top: 10px;
  /deep/ .ivu-select,
  /deep/ .ivu-date-picker {
    width: 15%;
    margin-right: 10px;
  }
}

//
/deep/ .ivu-modal {
  width: 700px;
}
// table
.cz_table {
  margin-top: 2% !important;
}
//
.cz_gjz {
  height: 33px;
  line-height: 32px;
  width: 99px;
  text-align: center;
  border: 1px solid;
  background: #2d8cf0;
  color: #fff;
}
.ivu-btn-primary {
  margin-left: 7px;
}
.ivu-col-span-12 {
  display: block;
  flex: 0 0 41%;
  max-width: 50%;
}
/deep/ .ivu-table-wrapper {
  overflow: visible;
}

/deep/ .ivu-btn-small:hover {
  background-color: rgba(61, 115, 239, 0.8) !important;
  color: #fff !important;
}
/deep/ .ivu-btn-small:active {
  background-color: #3d73ef;
}

/deep/.ivu-tooltip-inner {
  max-width: 400px;
  z-index: 10000;
}
/deep/ .ivu-table-overflowX {
  overflow-x: hidden;
}
/deep/ .ivu-table:before {
  display: none;
}

// 弹窗样式
.input_reset_css {
  border: 1px solid #dcdee2;
  padding-bottom: 20px;
  border-radius: 4px;
  /deep/ .ivu-input {
    border: none !important;
  }
  /deep/ .ivu-input:focus {
    border: none !important;
    box-shadow: 0px 0px 0px #fff !important;
  }
}

.input_reset_css_focus {
  border-color: #57a3f3;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
}
/deep/ .ivu-form-item-error {
  .input_reset_css {
    border: 1px solid red;
  }
  // .input_reset_css{
  //     box-shadow:0 0 0 2px rgba(237,64,20,0.2);
  // }
  .input_reset_css_focus {
    //  border: 1px solid red;
    box-shadow: 0 0 0 2px rgba(237, 64, 20, 0.2);
  }
}
</style>
<style lang="less">
// .ivu-tooltip-popper{
//     max-height: 400px!important;
//     overflow-y:auto;
// }
.ivu-tooltip-contentr {
  max-height: 400px;
  overflow-y: auto;
}
.ivu-tooltip-inner {
  max-height: 300px;
  overflow-y: auto;
}
</style>
