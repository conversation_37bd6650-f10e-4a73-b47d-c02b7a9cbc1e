<template>
  <div class="dashboard">

    <div class="chainSelect" v-if="tabNum==='first'">
      <Select filterable :class="className" @on-open-change="selectClassName" v-model="chain.chainId" placeholder="选择目标链" @on-change="changeChain" style="width:280px;">
        <Option v-for="item in chainIdList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
        <Option :value="chain.chainId" :label="chain.chainId" :disabled="true" v-if="pageParam1.pageIndex < pages && chainIdList.length>0" style="text-align:center">
          <span @mouseover="handleReachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
        </Option>
        <Option :value="chain.chainId" :label="chain.chainId" :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
          <span style="font-size:8px;">已加载全部</span>
        </Option>
      </Select>
    </div>
    <Row :gutter="24" style="padding-top: 10px">
      <i-col span="24">
        <div class="login_header">
          <div @click="clickEos" :class="{active:tabNum==='first'}" class="login_header_1">
            <img v-if="tabNum === 'first'" :src="require('@/assets/img/dashboard/eos-check.png')" class="icon" alt="" />
            <img v-else class="icon" :src="require('@/assets/img/dashboard/eos.png')" alt="" />
            <span>开放网络</span>

          </div>
          <div @click="clickFabric" :class="{active:tabNum==='second'}" class="login_header_2" style="" v-if="isFabric">
            <img v-if="tabNum == 'second'" :src="require('@/assets/img/dashboard/fabric-check.png')" class="icon" alt="" />
            <img v-else class="icon" :src="require('@/assets/img/dashboard/fabric.png')" alt="" />
            <span>Fabric</span>
          </div>
        </div>
        <!-- EOS -->
        <div v-if="tabNum==='first'" class="tab-1">

          <OverviewData @getHeight="getHeight" :chainId='chain.chainId'></OverviewData>
          <Row style="padding-top: 20px;" :gutter="24">
            <!-- 我的链账户和合约 -->
            <!-- 地图 -->
            <!-- <i-col span="12">
              <Card style="height:48vh">
                <NodeMap :chainId='chain.chainId'></NodeMap>
              </Card>
            </i-col> -->
            <!-- 工作代办/平台公告 -->
            <i-col span="24" style="height:30vh;padding-left: 12px;">
              <div class="work">
                <!-- <div style="width: 49%;  padding: 15px;background-color: #fff;cursor: pointer;" @click="devClick" v-if="RouterShow">
                  <div>
                    <span class="bs"></span><span class="title-more">开发者社区</span>
                  </div>
                  <div style="height:100%;position: relative;">
                    <img class="title-more-img" :src="imagesurl1">
                  </div>
                </div> -->

                <div :style="RouterShow?'width: 49%; padding: 15px;background-color: #fff; cursor: pointer;':'width: 100%; padding: 15px;background-color: #fff; cursor: pointer;'" @click="textClick">
                  <div>
                    <span class="bs"></span><span class="title-more">平台文档</span>
                  </div>
                  <div style="height:100%;position: relative;">
                    <img class="title-more-img" :src="imagesurl">

                  </div>
                </div>
                <div :style="RouterShow?'width: 49%; padding: 15px;background-color: #fff; cursor: pointer;':'width: 100%; padding: 15px;background-color: #fff; cursor: pointer;'">
                  <EosNoticeData :noticeHeight="height"></EosNoticeData>
                </div>
              </div>

            </i-col>
          </Row>
          <!-- 使用流程 -->
          <div class="map-wrap" style="padding: 20px 40px 0 20px;background:#fff;margin-top:20px; ">
            <div class="map-title">
              <div class="eos">
                使用流程
              </div>
            </div>
            <Row :gutter='24' style="padding-top: 8px" type="flex" justify="space-around" class="code-row-bg">

              <Col span='4' v-for=" (item,index) in flowText" :key="index" v-if="item.showTxt">

              <div style=" display: flex;flex-direction: column;justify-content: space-between;height:180px;padding-bottom:20px">
                <div>
                  <h3 style="text-align: center;width:210px;overflow: hidden;">
                    {{item.title}}
                  </h3>

                  <Tooltip max-width="200">
                    <p class="textContent">{{item.text}}</p>
                    <div slot="content">
                      <p>{{item.text}}</p>

                    </div>
                  </Tooltip>

                </div>

                <div style="text-align: right;">
                  <div style="text-align: right;margin-bottom:10px">
                    <span @click="clickRouterTxt" style="color:#3D73EF;cursor: pointer;">查看引导》</span>
                  </div>
                  <Button type="primary" :disabled="userPermission.isOperationsAdmin" @click="clickRouter(item.router)">{{item.btnText}}</Button>
                </div>
              </div>

              </Col>

            </Row>
          </div>

          <Row :gutter='24' justify="space-between">
            <!-- 我的链账户 -->
            <Col span="12" style="margin:20px 0 0 0">
            <div style="background:#fff;width:100%;height:100%">
              <div class="map-wrap" style="background:#fff; padding: 20px 10px 0 10px;margin:0 0 0 10px">
                <div class="map-title">
                  <div class="eos">
                    <div>链账户</div>
                    <div class="eosMaor" @click="getMore">查看更多》</div>

                  </div>
                </div>
                <Table :columns="columns1" :data="data1" @on-row-click='getTable' :height="260"></Table>
                <!-- <edit-table-mul :columns="columns1" v-model="data1" @on-row-click='getTable' :height="260">
              </edit-table-mul> -->
              </div>
            </div>

            </Col>
            <!-- 我的链账户资源详情 -->
            <Col span="12" style="padding-left:5px">
            <div class="map-wrap" style="background:#fff; padding: 20px 10px 0 15px;margin:20px 0 0 0">
              <div class="map-title">
                <div class="eos">
                  链账户资源详情
                </div>
              </div>

              <Row :gutter='24' justify="space-between">
                <Col span="9">
                <div class="progressArm">链账户名称:{{chainTitle}}</div>

                <div v-show="chainTitle">

                  <div class="progressArm">
                    <div style="white-space: nowrap;">RAM用量:{{resourceObj.ram}}/{{resourceObj.rams}} Byte</div>
                    <Progress :percent="resourceObj.ram?resourceObj.ram/resourceObj.rams>1?100:resourceObj.ram/resourceObj.rams*100:0" hide-info :stroke-color="'#519FF2'" />
                  </div>

                  <div class="progressArm">
                    <div style="white-space: nowrap;">NET用量:{{resourceObj.net}}/{{resourceObj.nets}} Byte</div>
                    <Progress :percent="resourceObj.net?+resourceObj.net/resourceObj.nets>1?100:+resourceObj.net/resourceObj.nets*100:0" hide-info :stroke-color="'#52C41A'" />
                  </div>

                  <div class="progressArm">
                    <div style="white-space: nowrap;">CPU用量:{{resourceObj.cpu}}/{{resourceObj.cpus}} us</div>
                    <Progress :percent="resourceObj.cpu?+resourceObj.cpu/resourceObj.cpus>1?100:+resourceObj.cpu/resourceObj.cpus*100:0" hide-info :stroke-color="'#FAAD14'" />
                  </div>
                </div>

                <div v-show="!chainTitle" class="null">暂无数据</div>
                </Col>
                <Col span="14">
                <div class="progressArm">链账户资源消耗</div>
                <div v-show="!consumeShow" style="width: 400px;  height: 250px;" ref="lineRefs" id="linsEs"></div>
                <div v-show="consumeShow" class="null">暂无数据</div>
                </Col>
              </Row>
            </div>
            </Col>
          </Row>
        </div>
        <!-- Fabric -->
        <div v-else class="tab-1">
          <FabricOverviewData @getHeight="getHeight"></FabricOverviewData>
          <Row style="padding-top: 8px">
            <i-col span="7">
              <div class="chain">
                <div style="width: 88%; margin: 0 auto; padding-bottom: 8px">
                  <span class="bs"></span><span class="title-more">我的实例</span>
                  <span class="mores" @click="goInstance">更多
                    <Icon type="ios-arrow-forward" />
                  </span>
                </div>
                <div v-if="chainList && chainList.length > 0 ? true : false">
                  <div class="fabric-list" v-for="(item,index) in chainList.slice(0, 3)" :key="index" @click="goFabricChain(item)">
                    <div class="listwrap">
                      <div>
                        <div class="name">{{item.chainDisplayName}}</div>
                        <div class="type">{{item.TemplateType | getTemplateType}}</div>
                      </div>
                      <div>
                        <span class="more">
                          <Icon type="ios-arrow-forward" size="25" />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <Row v-else>
                  <div class="img-style">
                    <img :src="imagesurl" />
                    <p class="contract-none">暂无实例</p>
                  </div>
                  <Button ghost icon="md-add" style="margin:5px 10px 0 10px;width:95%;height:4vh;position:relative;color:#519FF2;border:1px solid #519FF2" type="success" @click="goCreat">创建我的实例
                  </Button>
                </Row>

              </div>
              <div class="chain">
                <div style="width: 88%; margin: 0 auto; padding-bottom: 8px">
                  <span class="bs" style="background: #19c3a0"></span>
                  <span class="title-more">合约列表</span>
                  <span class="mores" @click="goContractLibrary">更多
                    <Icon type="ios-arrow-forward" />
                  </span>
                </div>
                <div v-if="chaincodeList && chaincodeList.length > 0 ? true : false">
                  <div class="fabric-list" v-for="(item,index) in chaincodeList.slice(0, 3)" :key="index" @click="goContractLibrary">
                    <div class="listwrap">
                      <div>
                        <div class="name">{{item.Name}}</div>
                        <div class="type">版本{{item.Version}}</div>
                      </div>
                      <div>
                        <span class="more">
                          <Icon type="ios-arrow-forward" size="25" />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <Row v-else>
                  <div class="img-style">
                    <img :src="imagesurl" />
                    <p class="contract-none">暂无合约</p>
                  </div>
                  <Button ghost icon="md-add" style="margin:5px 10px 0 10px;width:95%;height:4vh;position:relative;color:#519FF2;border:1px solid #519FF2" type="success" @click="upContractLibrary">创建合约
                  </Button>
                </Row>

              </div>
            </i-col>
            <i-col span="10">
              <Card style="width: 98%;margin-left: 1%">
                <FabicNodeMap></FabicNodeMap>
              </Card>
            </i-col>
            <i-col span="7">
              <FabricNoticeData :noticeHeight="height"></FabricNoticeData>
            </i-col>
          </Row>
        </div>
      </i-col>
    </Row>
    <transition name="fade" v-if="isShowCreat">
      <div class="alertBox confirmBox">
        <div class="addTissue">
          <div class="delText notitle">
            <img :src="warningImg" class="iconImage">
            <div class="errmessage">当前资源不足，无法创建实例。</div>
          </div>
          <div class="confirmBottom">
            <Button type="primary" class="sure-btn" @click="cancel">知道了</Button>
          </div>
        </div>
      </div>
    </transition>
    <!-- <div class="servizio">
      <div class="ser_div">
        <Poptip placement="left" width='300'>
          <img src="../../../src/assets/kefu/service.png" alt="">
          <div slot="content">
            <p class="p_elail">
              <span class="img_elail"><img src="../../../src/assets/kefu/Email.png" alt=""></span>
              <span class="info row" style="margin-left:10px">{{servizioinfo.CUSTOMER_SERVICE_EMAIL}}</span>
            </p>
            <Divider />
            <p class="p_elail">
              <span class="img_elail"><img src="../../../src/assets/kefu/phone.png" alt=""></span>
              <span class="info row" style="margin-left:10px">{{servizioinfo.CUSTOMER_SERVICE_PHONE}}</span>
            </p>
            <p class="p_elail" style="margin-top: 3px;">
              <span class="info">时间：{{servizioinfo.CUSTOMER_SERVICE_TIME}}</span>
            </p>
          </div>
        </Poptip>
      </div>

    </div> -->
  </div>
</template>

<script>
import { localRead, localSave } from '@/lib/util'
import { getChainAccount, getContractList, noticeManage, getChainIdList } from '@/api/data'
import { getconfig } from '@/api/contract'
import { userPermission } from '@/api/user'
import { getMyChain, getChainDetail } from '@/api/dashborad'
import { getClusterList, checkChainLimit } from '@/api/baascore/overview'
import { getPlatChaincodeList } from '@/api/baascore/contractLibrary'
import OverviewData from './eos-overview-data.vue'
import NodeMap from './eos-node-map.vue'
import EosNoticeData from './eos-notice-data'
import FabricOverviewData from './fabric-overview-data'
import FabicNodeMap from './fabric-node-map'
import FabricNoticeData from './fabric-notice-data'
import { mapActions, mapState } from 'vuex'
import EosWork from './eos-work'
import { getConfigMap } from '@/api/contract'
import EditTableMul from '_c/edit-table-mul'
import * as echarts from 'echarts'

export default {
  name: 'dashboard',
  components: {
    OverviewData,
    NodeMap,
    EosNoticeData,
    FabricOverviewData,
    FabicNodeMap,
    FabricNoticeData,
    EosWork,
    EditTableMul
  },
  data () {
    return {
      imagesurl: require('@/assets/img/19.png'),
      servizioinfo: '',
      timer: null,
      tabNum: this.$store.state.user.tabNum || 'first',
      left: 36,
      color: '',
      userLoginId: localRead('userLoginId'),
      imagesurl1: require('@/assets/img/18.png'),
      chainAccountData: [],
      contractNameData: [],
      height: '',
      chainList: [],
      chaincodeList: [],
      isShowCreat: false,
      isFabric: true,
      warningImg: require('@/assets/image/el-warning.png'),
      pageParam: {
        pageSize: 8,
        pageIndex: 1
      },
      approvalList: [],
      chainIdList: [],
      chain: {
        chainId: 0,
        chainName: ''
      },
      pageParam1: { pageTotal: 0, pageSize: 60, pageIndex: 1 },
      pageParamChain: {
        pageParam: {
          pageSize: 10, pageIndex: 1
        }
      },

      pages: 0,
      cha: {},
      imgUrl: require('@/assets/img/arrow.png'),
      className: 'select-style1',
      columns1: [
        {
          title: '链账户名称',
          key: 'chainAccountName',
          minWidth: 120,
          tooltip: true,
        },
        {
          title: '链名称',
          key: 'chainName',
          minWidth: 80,
          tooltip: true,
        },

        {
          title: '资源',
          minWidth: 320,
          render: (h, params) => {
            return h('div', [
              h('Tooltip', {
                props: {
                  maxWidth: 400, // 设置最大宽度
                  content: 'RAM:' + params.row.residueRam + ' ' + 'NET:' + params.row.residueNet + ' ' + 'CPU:' + params.row.residueCpu
                }
              }, [
                h('span', {
                  style: {
                    display: 'inline-block',
                    width: '320px',
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis'
                  }
                }, [
                  h('span', {
                    style: {
                      fontWeight: 'bold',
                    }
                  }, 'RAM:'),
                  ' ' + params.row.residueRam + ' ',
                  h('span', {
                    style: {
                      marginLeft: '10px',
                      display: 'inline-block',
                      fontWeight: 'bold',
                    }
                  }, 'NET:'),
                  ' ' + params.row.residueNet + ' ',
                  h('span', {
                    style: {
                      marginLeft: '10px',
                      display: 'inline-block',
                      fontWeight: 'bold',
                    }
                  }, 'CPU:'),
                  ' ' + params.row.residueCpu
                ])
              ])
            ])
          },
        },
      ],
      data1: [

      ],
      option: {
        // title: {
        //   text: '链账户资源消耗'
        // },

        tooltip: {
          trigger: 'axis'
        },
        grid: {
          show: false,
          // top: '20%',
          // right: '5%',
          // bottom: '10%',
          left: '10%'
        },
        legend: {

          itemWidth: 8,
          itemHeight: 8,
          icon: 'circle',
          // x: 'center',
          // y: 'center',
          // orient: 'vertical', //设置图例排列纵向显示
          align: 'left', //设置图例中文字位置在icon标识符的右侧
          left: '40%',
          top: '12%',
          itemGap: 10, //设置图例之间的间距
          padding: [0, 0, 0, 0], //设置图例与圆环图之间的间距

        },

        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: []
        },
        yAxis: {
          type: 'value'
        },
        axisLabel: {
          margin: 8,
          formatter: function (value, index) {
            if (value >= 10000 && value < 10000000) {
              value = value / 10000 + '万'
            } else if (value >= 10000000 && value < 100000000) {
              value = value / 10000000 + '千万'
            } else if (value >= 100000000) {
              value = value / 100000000 + '亿'
            }
            return value
          }
        },
        series: [
          {
            name: 'CPU(us)',
            type: 'line',
            data: [],
            color: '#FAAD14',
            lineStyle: {
              color: '#FAAD14' // 设置第一条线的颜色为红色
            },
            smooth: true
          },
          {
            name: 'NET(Byte)',
            type: 'line',
            data: [],
            color: '#52C41A',
            lineStyle: {
              color: '#52C41A' // 设置第一条线的颜色为红色
            },
            smooth: true
          },
          {
            name: 'RAM(Byte)',
            type: 'line',
            data: [],
            color: '#519FF2',
            lineStyle: {
              color: '#519FF2' // 设置第一条线的颜色为红色
            },
            smooth: true
          },

        ]
      },
      blockShow: true,
      flowText: [
        {
          title: '区块链网络的创建',
          text: '了解如何通过一键部署，进行区块链网络的搭建及启用等。',
          btnText: '创建区块链网络',
          router: 'blockchain_network',
          showTxt: true
        },
        {
          title: '合约链账户的创建',
          text: '用户可通过手动创建合约链账户，为后续的部署合约进行准备。',
          btnText: '创建合约链账户',
          router: 'new_userC',
          showTxt: true
        },
        {
          title: '智能合约的创建/部署',
          text: '用户可通过上传文件，创建不同语言类型的合约。同时用户可选择已创建好的合约链账户进行合约部署。',
          btnText: '智能合约的创建及部署',
          router: 'contract_new',
          showTxt: true
        },
        {
          title: '普通链账户的创建及绑定',
          text: '用户创建普通链账户，并将创建好的普通链账户和部署好的合约链账户绑定，完成后即可通过普通链账户进行业务数据上链。',
          btnText: '普通链账户的创建及绑定',
          router: 'new_user',
          showTxt: true
        },
        // {
        //   title: '发起交易',
        //   text: '用户可以选择绑定好的普通链账户，进行交易发起上链。',
        //   btnText: '发起交易',
        //   router: 'transaction_commit',
        // },

      ],
      cityList: [
        {
          value: 'New York',
          label: 'New York'
        },
        {
          value: 'London',
          label: 'London'
        },
        {
          value: 'Sydney',
          label: 'Sydney'
        },
        {
          value: 'Ottawa',
          label: 'Ottawa'
        },
        {
          value: 'Paris',
          label: 'Paris'
        },
        {
          value: 'Canberra',
          label: 'Canberra'
        }
      ],
      model1: '',
      chainId: '',
      resourceObj: {
        ram: '',
        cpu: '',
        net: '',
        rams: '',
        cpus: '',
        nets: '',
      },
      chainAccountIdDetails: 0,
      chainTitle: '',
      pageParamChainPages: 0,
      RouterShow: false,
      RouterShowPath: '',
      consumeShow: false,

      blockShowPath: '',
      // userPermission: JSON.parse(localRead('userPermission')),
      userPermission: ''
    }

  },
  // computed: {
  //   hasEditPermission () {
  //     // 建议根据实际业务需求定义权限组合
  //     return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
  //   },

  // },
  methods: {
    // 以上是新增工作代办方法
    ...mapActions([
      'updateTabNum'
    ]),
    ...mapState({
      chains: state => state.user.chain
    }),
    selectClassName () {
      this.className = this.className === 'select-style1' ? 'select-style2' : 'select-style1'
    },
    changeChain () {
      if (this.chain.chainId != 0) {
        this.getChainName(this.chain.chainId)
        // this.getBrowserData(this.chain.chainId)
        this.chainId = this.chain.chainId
        this.chainTitle = ''
        this.resourceObj.ram = 0
        this.resourceObj.cpu = 0
        this.resourceObj.net = 0
        this.resourceObj.rams = 0
        this.resourceObj.cpus = 0
        this.resourceObj.nets = 0
        this.chainAccountIdDetails = 0
      }
    },
    getChainName (value) {
      for (var item in this.chainIdList) {
        if (this.chainIdList[item].chainId === value) {
          this.chain.chainName = this.chainIdList[item].chainName
        }
      }
    },
    getMore () {
      this.$router.push({
        name: 'chain_table',

      })
    },
    // 开发者社区
    devClick () {
      if (this.RouterShow) {
        let path = this.RouterShowPath + '?token=' + localRead('token')
        window.open(path)
        // this.$router.push({
        //   name: this.RouterShowPath,
        //   // params: { name1: 'name2' }
        // })
      }
    },
    textClick () {
      this.$router.push({
        name: 'about_index',
        params: { name1: 'name2' }
      })

    },
    // 链账户
    handleReachBottom () {
      if (this.pageParam1.pageIndex < this.pages) {
        this.pageParam1.pageIndex += 1
        this.getChainList(true)
      }
    },
    // 使用流程路由跳转
    clickRouter (router) {
      if (router == 'blockchain_network') {
        this.$router.push({
          name: this.blockShowPath,
          params: { show: 'show' }
        })
      } else if (router == 'new_userC') {
        this.$router.push({
          name: 'new_user',
          params: { account: 'CONTRACT' }
        })

      } else {
        this.$router.push({
          name: router
        })
      }

    },
    clickRouterTxt () {
      this.$router.push({
        name: 'about_index',
        params: { name1: 'name2' }
      })
    },
    getChainList (flag) {

      getChainIdList(this.pageParam1).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        }
        else {
          if (flag) {
            let index = res.data.records.findIndex(item => {
              if (item.chainId === this.cha.chainId) {
                return true
              }
            })
            if (index !== -1) {
              res.data.records.splice(index, 1)
            }
            this.chainIdList.push.apply(this.chainIdList, res.data.records)
          } else {
            this.chainIdList = res.data.records && res.data.records.length > 0 ? res.data.records : []
            if (JSON.stringify(this.cha) !== '{}') {
              this.chain = this.cha
              this.pushChainId()
            } else {
              this.chain.chainId = this.chains.chainId || this.chainIdList.length > 0 ? this.chainIdList.length > 0 ? this.chainIdList[0].chainId : 0 : 0
              this.chain.chainName = this.chains.chainName || this.chainIdList.length > 0 ? this.chainIdList.length > 0 ? this.chainIdList[0].chainName : '' : ''

            }
          }
          this.pageParam1 = {
            pageTotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          this.size = this.chainIdList.length
          this.pages = res.data.pages
          // this.chain.chainId ? this.chain.chainId ? this.getBrowserData(this.chain.chainId) : '' : ''
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    pushChainId () {
      let flag = false
      for (let i in this.chainIdList) {
        if (this.chainIdList[i].chainId === this.cha.chainId) {
          flag = true
          break
        }
      }
      if (!flag) {
        this.chainIdList.push(this.cha)
      }
    },
    getHeight (data) {
      this.height = '43.2vh + ' + (96 + data) + 'px'
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    createMyChain () {
      this.$router.push({
        name: 'new_user'
      })
    },
    clickFabric () {
      this.tabNum = 'second'
      this.getHyList()
      this.getChainList()
    },
    clickEos () {
      this.tabNum = 'first'
      this.getChainAccountData()
      this.getContractListData()
    },
    // getMyChain () {
    //   this.$router.push({
    //     name: 'chain_table'
    //   })
    // },
    getMyChainDetail (chainId, chainAccountId, chainAccountName, chainName) {
      this.$router.push({
        name: 'chain_details',
        query: {
          chainId: chainId,
          chainAccountId: chainAccountId,
          chainAccountName: chainAccountName,
          chainName: chainName
        }
      })
    },
    createMyContract () {
      this.$router.push({
        name: 'contract_new',
        params: {
          modal: true
        }
      })
    },
    getMyContractTable () {
      this.$router.push({
        name: 'contract_table'
      })
    },
    getMyContractDetail (contractId) {
      this.$router.push({
        name: 'contract_details',
        params: {
          contractId: contractId
        }
      })
    },
    // 查询我的链账户
    getChainAccountData () {
      getChainAccount()
        .then((res) => {
          if (res.code === '00000') {
            this.chainAccountData = res.data
          } else {
            this.msgInfo('error', res.message, true)
          }
        })
        .catch((error) => {
          this.msgInfo('error', error.message, true)
        })
    },
    getContractListData () {
      getContractList()
        .then((res) => {
          if (res.code === '00000') {
            this.contractNameData = res.data
          } else {
            this.msgInfo('error', res.message, true)
          }
        })
        .catch((error) => {
          this.msgInfo('error', error.message, true)
        })
    },
    // fabric查询链账户
    // getChainList () {
    //   this.chainList = []
    //   getChainList().then(res => {
    //     if (res.code === '200') {
    //       if (res.data && res.data.length > 0) {
    //         res.data.forEach((item, index) => {
    //           if (item.state == 2 || item.state == 6) {
    //             this.chainList.push(item)
    //           }
    //         })
    //       }
    //     } else {
    //       this.$message.error('fabric数据获取失败，请重新加载！')
    //     }
    //   }).catch((error) => {
    //     this.msgInfo('error', error.message, true)
    //   })
    // },
    goInstance () {
      this.$router.push({
        path: '/instance'
      })
    },
    goFabricChain (item) {
      this.$store.dispatch('getChainItem', item).then(() => {
        this.$router.push({
          path: '/chainManage/overview'
        })
      })
    },
    goCreat () {
      sessionStorage.clear()
      getClusterList().then(res => {
        if (res.code === '200' && res.data) {
          var params = {
            ClusterName: res.data[0].ClusterName
          }
          checkChainLimit(params).then(res => {
            if (res.code === '200') {
              this.$router.push({
                path: '/guide/step'
              })
            } else if (res.code === '4140') {
              this.isShowCreat = true
            } else {
              this.$message.error('fabric数据获取失败，请重新加载！')
            }
          })
        } else {
          this.$message.error('fabric数据获取失败，请重新加载！')
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    cancel () {
      this.isShowCreat = false
    },
    // 获取合约列表
    getHyList () {
      getPlatChaincodeList().then(res => {
        if (res.code === '200') {
          this.chaincodeList = res.data.ChainCodes || []
        } else {
          this.$message.error('fabric数据获取失败，请重新加载！')
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    upContractLibrary () {
      this.$router.push({
        path: '/contractLibrary',
        query: { 'isUpload': true }
      })
    },
    goContractLibrary () {
      this.$router.push({
        path: '/contractLibrary'
      })
    },
    getTable (row) {
      const filterArr = this.data1.filter(item => item.chainAccountId == row.chainAccountId)
      this.resourceObj.ram = filterArr[0].usedRam
      this.resourceObj.cpu = filterArr[0].usedCpu
      this.resourceObj.net = filterArr[0].usedNet
      this.resourceObj.rams = filterArr[0].countRam
      this.resourceObj.cpus = filterArr[0].countCpu
      this.resourceObj.nets = filterArr[0].countNet
      this.chainAccountIdDetails = filterArr[0].chainAccountId
      this.chainTitle = filterArr[0].chainAccountName
      this.getChainDetailList()

    },
    getSelect (e) {
      this.chainId = e
    },
    getMyChainList () {
      if (this.chain.chainId != 0) {
        getMyChain(this.chain.chainId, this.pageParamChain).then(res => {
          if (res.code === '00000') {
            if (res.data.records.length > 0) {
              this.data1 = [...this.data1, ...res.data.records]
              this.chainTitle = this.data1[0].chainAccountName
              this.pageParamChainPages = res.data.pages
              if (this.data1.length > 0) {
                this.resourceObj.ram = this.data1[0].usedRam
                this.resourceObj.cpu = this.data1[0].usedCpu
                this.resourceObj.net = this.data1[0].usedNet
                this.resourceObj.rams = this.data1[0].countRam
                this.resourceObj.cpus = this.data1[0].countCpu
                this.resourceObj.nets = this.data1[0].countNet
                this.chainAccountIdDetails = this.data1[0].chainAccountId
                this.getChainDetailList()
              }
            } else {
              this.data1 = res.data.records
              this.consumeShow = true
            }


          } else {
            this.$message.error(res.message)
          }
        }).catch((error) => {

          this.msgInfo('error', error.message, true)
        })
      }

    },
    getChainDetailList () {
      if (this.chain.chainId != 0) {
        getChainDetail(this.chain.chainId, this.chainAccountIdDetails).then(res => {
          if (res.code === '00000') {
            this.consumeShow = false;
            this.option.xAxis.data = res.data.timeList
            this.option.series[0].data = res.data.cpuList ? res.data.cpuList : []
            this.option.series[1].data = res.data.netList ? res.data.netList : []
            this.option.series[2].data = res.data.ramList ? res.data.ramList : []
            this.mychart.setOption(this.option)
            // this.optionLine.series[0].data = res.data.series[0].data
            // timeList
          } else {
            this.$message.error(res.message)
            this.consumeShow = true
          }
        }).catch((error) => {
          this.consumeShow = true
          this.msgInfo('error', error.message, true)
        })
      }

    }

  },

  // created () {
  //   userPermission().then(res => {
  //     if (res.data) {

  //       localSave('userPermission', JSON.stringify(res.data))

  //     }
  //   }).catch((error) => {
  //     this.msgInfo('error', error.message, true)
  //   })
  // },
  mounted () {


    let name = 'CMBAAS_ROUTER'
    getconfig(name).then((res) => {
      console.log(res);

      if (res.data) {
        this.RouterShow = true
        this.RouterShowPath = res.data.value

      } else {
        this.RouterShow = false
        this.RouterShowPath = ''

      }

    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })
    let name1 = 'CMBAAS_CRENET'
    getconfig(name1).then((res) => {
      console.log(res);

      // 设置 blockShow 的初始值
      if (res.data) {
        // this.blockShow ? localStorage.getItem('roleId') == 1 ? false : true : false,
        this.flowText[0].showTxt = localStorage.getItem('roleId') == 1 ? true : false
        // this.RouterShow = true
        this.blockShowPath = res.data.value
      } else {
        this.flowText[0].showTxt = false

        this.blockShowPath = ''
      }

    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })
    let dom = document.querySelector('.ivu-table-body')
    dom.addEventListener('scroll', () => {
      // scrollDistance 为零时滚动到底部
      const scrollDistance = dom.scrollHeight - dom.scrollTop - dom.clientHeight
      // console.log(scrollDistance, '------', dom.scrollHeight, dom.scrollTop, dom.clientHeight)
      if (scrollDistance <= 0 && this.pageParamChain.pageParam.pageIndex < this.pageParamChainPages) { // 等于0证明已经到底，可以请求接口
        this.pageParamChain.pageParam.pageIndex++
        this.getMyChainList()
      }
    })


    this.mychart = echarts.init(this.$refs.lineRefs, null, { renderer: 'svg' }) // 获取mapbox盒子
    // let that = this
    window.addEventListener('resize', () => {
      if (window.innerHeight > 0) {
        console.log(1111)
        // 重新渲染图表
        this.mychart.resize();

      }

    });

    let info = {
      configNames: ['CUSTOMER_SERVICE_EMAIL', 'CUSTOMER_SERVICE_PHONE', 'CUSTOMER_SERVICE_TIME']

    }
    getConfigMap(info).then(res => {
      this.servizioinfo = res.data
    })
    // this.getChainList()
    // this.getHyList()
    // console.log('localStorage.fabricOpen:', localStorage.fabricOpen)
    // localStorage.fabricOpen = '1'
    if (localStorage.fabricOpen === '0') {
      this.isFabric = false
    } else {
      // this.getChainList()
      this.getHyList()
    }
    this.getChainAccountData()
    this.getContractListData()
    this.getChainList()
    // this.getMyChainList()

    userPermission().then(res => {
      this.userPermission = res?.data
      localSave('userPermission', JSON.stringify(res?.data))
    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })

  },
  watch: {
    'chain.chainId': {
      handler (newVal, oldVal) {
        if (oldVal !== undefined) {
          this.data1 = []
          this.getMyChainList(newVal)

        }

      },
      deep: true,
      immediate: true
    },

  },
  filters: {
    getTemplateType (type) {
      switch (type) {
        case 'SOLO_CMRI':
          return '标准版'
        case 'KAFKA_CMRI':
          return '安全版'
      }
    }
  },
  destroyed () {
    this.updateTabNum(this.tabNum)
  },
  // watch: {
  //   'chain.chainId'(value) {

  //   }
  // }
}
</script>

<style lang="less" scoped>
.row {
  white-space: pre-wrap;
  display: inline-block;
  word-wrap: break-word;
  width: 80%;
}
/deep/.ivu-poptip-body-content {
  overflow: hidden;
}
/deep/.ivu-divider-horizontal {
  margin: 5px 0;
}
// /deep/.ivu-tooltip-inner{
//   background: white;
// }
// /deep/.ivu-tooltip-arrow{
//   background-color: white !important;
// }
// 客服样式
.servizio {
  width: 65px;
  height: 65px;
  // border: 1px solid red;
  position: fixed;
  bottom: 30px;
  right: 20px;
  .ser_div {
    border-radius: 5px;
    background: #588fff;
    float: right;
    width: 65px;
    height: 65px;
    // border: 1px solid blue;
    .p_elail {
      display: flex;
      // span{
      //   display: inline-block;
      // }
      .img_elail {
        width: 30px;
        height: 30px;
        display: inline-block;

        img {
          width: 100%;
        }
      }
      .info {
        color: #6190ff;
        line-height: 30px;
        font-size: 15px;
      }
    }
  }
}
/*******************************/
.work {
  display: flex;
  justify-content: space-between;
  height: calc(30vh + 10px);
  width: 100%;
  border: 2px solid #f5f4f4;
  // padding-top: 16px;
  // background-color: #fff;
  border-radius: 4px;
  margin-bottom: 6px;

  /deep/.ivu-row {
    width: 100%;
  }
  .ivu-timeline {
    width: 100%;
  }
  .data-noned {
    position: relative;
    text-align: center;
    vertical-align: middle;
    margin: 0 auto;
    // padding-top: 50px;
    .title-none {
      font-size: 8px;
      color: #bdbbbb;
    }
  }
}
.ivu-timeline {
  padding: 16px;
}
.time {
  font-size: 14px;
  font-weight: bold;
}
.content {
  padding-left: 5px;
}
/deep/.ivu-btn-dashed {
  color: #57a3f3;
  border-color: #57a3f3;
}
.title-3 {
  font-size: 12px;
  color: #9b9b9b;
  // margin-top:-15px;
  // margin-left:13px;
  font-family: "Microsoft YaHei";
  font-weight: 400;
  .publishDate {
    float: right;
    margin-right: 7%;
  }
}
.otherProductItem {
  cursor: default;
  width: 260px;
  line-height: 25px;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  padding-right: 9px;
  margin-top: -5px;
  margin-left: -5px;
}
.otherProductItem1 {
  cursor: default;
  width: 260px;
  line-height: 25px;
  padding-right: 8px;
  margin-top: -5px;
  margin-left: -5px;
}

// 以上是新添加
/deep/ .ivu-btn {
  font-size: 12px;
}

.title {
  font-size: 16px;
  font-weight: bold;
}

.click {
  cursor: pointer;
}

.dashboard {
  width: calc(100% + 32px);
  background-color: #f2f6fd;
  margin: -16px;
  // padding-bottom: 15px;
  /deep/ .page-card {
    min-height: 0;
  }

  .create_chain {
    font-size: 18px;
    .click;
  }

  // .chain {
  //   height: calc(40vh + 10px);
  //   width: 100%;
  //   border: 2px solid #f5f4f4;
  //   padding-top: 16px;
  //   background-color: #fff;
  //   border-radius: 4px;
  //   margin-bottom: 6px;

  //   &:last-child {
  //     margin-bottom: 0;
  //   }

  .chain {
    height: calc(30.5vh + 10px);
    width: 100%;
    border: 2px solid #f5f4f4;
    padding-top: 16px;
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 6px;

    &:last-child {
      margin-bottom: 0;
    }

    .fabric-list {
      width: 88%;
      margin: 0 auto;
      height: 5vh;
      // height: calc((28vh) / 4);
      background: rgba(255, 255, 255, 1);
      box-shadow: 0px 0px 5px rgba(153, 153, 153, 0.35);
      border-left: 3px solid #3d73ef;
      margin-bottom: 10px;
      border-radius: 0 5px 5px 0;
      cursor: pointer;

      .listwrap {
        height: inherit;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 20px;

        &.add {
          justify-content: center;
        }

        /deep/ .ivu-icon-md-add:before {
          color: #3d73ef;
        }
      }

      .name {
        font-size: 14px;
        font-weight: bold;
      }

      .type {
        font-size: 12px;
      }
    }
  }

  .chainAccount {
    width: 88%;
    height: 5vh;
    border: 1px solid #f1f0f0;
    box-shadow: 1px 1px 1px #f5f4f4;
    border-radius: 4px;
    margin: 4px auto;
    box-shadow: 0px 0px 3px rgba(112, 119, 134, 0.16);
    opacity: 1;
    border-left: 4px solid #3d73ef;
    padding-left: 5px;
  }

  .chainAccount-add {
    padding: 0 49% 0 47%;
    cursor: pointer;
  }

  .account-1 {
    width: 80%;
    font-size: 0.8vw;
    font-weight: bold;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .account-2 {
    width: 12vw;
    font-size: 0.6vw;
    color: #9b9b9b;
    font-family: "Microsoft YaHei";
    font-weight: 400;
    //line-height: 20px;
    overflow: hidden;
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    //margin-top: 1px;
  }

  .distance {
    margin-top: 5px;
  }

  .center {
    vertical-align: middle;
    line-height: 50px;
  }

  .img-style {
    text-align: center;
    margin: 0 auto;
    vertical-align: middle;
    position: relative;
    width: 65%;

    img {
      max-width: 65%;
      max-height: 65%;
    }

    .contract-none {
      font-size: 8px;
      color: #d4d3d3;
      margin-top: -10px;
      position: relative;
    }
  }

  .data-none {
    position: relative;
    text-align: center;
    vertical-align: middle;
    display: table-cell;
    margin: 0 auto;
    padding-top: 50px;

    img {
      max-width: 82%;
      max-height: 82%;
    }

    .title-none {
      font-size: 8px;
      color: #bdbbbb;
    }
  }

  .title-more {
    font-weight: bold;
    padding-left: 6px;
    text-indent: 6px;
    line-height: 6px;
    font-size: 16px;
    cursor: pointer;
  }
  .title-more-img {
    max-width: 75%;
    max-height: 75%;
    position: absolute;
    left: 45%;
    top: 40%;
    margin-left: -50px;
    margin-top: -50px;
  }
  .chain-more {
    border-left: 4px solid #3d73ef;
  }

  .contract-more {
    border-left: 4px solid #19c3a0;
  }

  .mores {
    .click;
    float: right;
    font-size: 12px;
    color: #acaaaa;
  }

  .more {
    .mores;
    margin-right: 20px;
  }

  .tip-left {
    .click;
    margin-left: -10px;
    font-size: 10px;
    color: #2d8cf0;
  }

  .tip-right {
    .click;
    float: right;
    font-size: 10px;
    color: #2d8cf0;
  }

  .user {
    height: 31px;
    font-weight: bold;
    font-size: 24px;
    font-family: "Microsoft YaHei";
    line-height: 31px;
    color: #3d73ef;
  }

  /deep/ .ivu-card {
    height: 100%;
  }

  /deep/ .ivu-card:hover {
    box-shadow: none !important;

    .ivu-card.ivu-card-shadow:hover {
      box-shadow: none !important;
    }
  }
}

/deep/ .ivu-card.ivu-card-shadow {
  box-shadow: none;
}

.bs {
  float: left;
  width: 6px;
  height: 18px;
  background: #19c3a0;
  opacity: 1;
  border-radius: 3px;
}
</style>
<style lang="less" scoped>
.login_header {
  height: 62px;
  font-size: 18px;
  border-radius: 8px;
  background: linear-gradient(
    92deg,
    rgba(229, 233, 252, 0.7) 0%,
    rgba(228, 232, 250, 0.73) 100%
  );
  position: relative;

  &::before {
    content: "";
    display: block;
    position: absolute;
    right: 19px;
    bottom: 0;
    width: 136px;
    height: 52px;
    background-image: url("../../assets/img/dashboard/nav-icon.png");
  }
}

.login_header_1 {
  margin-right: 30px;
  cursor: pointer;
  font-weight: bold;
  width: 120px;
  display: inline-block;
  padding-top: 20px;
  margin-left: 30px;
}

.login_header_2 {
  cursor: pointer;
  font-weight: bold;
  padding-top: 20px;
  width: 90px;
  display: inline-block;
}

.tab-1 {
  opacity: 1;
  margin-top: 10px;
}

.active {
  color: #3d73ef;
  padding-bottom: 10px;
  border-bottom: 4px solid #3d73ef;
  cursor: pointer;
}

.icon {
  cursor: pointer;
  margin-right: 10px;
  vertical-align: middle;
}

.fabric-wrap {
  height: 80vh;
  background-color: #fff;
}
</style>
<style lang="less" scoped>
/*弹框遮罩位置*/
.alertBox {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1002;
}

/*弹框相对位置*/
.alertBox .addTissue {
  &.max-width {
    width: 750px;
  }

  position: absolute;
  width: 400px;
  max-width: 750px;
  min-width: 400px;
  background: #fff;
  border-radius: 4px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/*弹框标题*/
.el-icon-cursor {
  cursor: pointer;
}

.addTissue {
  padding: 43px 43px 29px;
  width: auto;
  min-width: 365px;
  max-width: 500px;
  .delText {
    margin-bottom: 24px;
    font-size: 14px;
    color: #333;
  }
  .confirmBottom {
    text-align: right;
  }
}

.iconImage {
  // width: 28px;
  // height: 28px;
  width: 16px;
  height: 16px;
  margin-right: 14px;
  vertical-align: middle;
}
//

.confirmBox {
  .iconImage {
    // width: 28px;
    // height: 28px;
    width: 16px;
    height: 16px;
    margin-right: 14px;
    vertical-align: middle;
  }
  //
  .errmessage {
    margin-left: 30px;
    margin-top: 14px;
    // font-size: 17px;
    font-size: 14px;
    color: #555555;
    letter-spacing: 0;
    line-height: 25px;
  }
  /*重置icon样式*/
  .el-icon-warning {
    color: #fe983d;
    margin-right: 14px;
    // font-size: 28px;
    font-size: 16px;
  }
  .addTissue {
    padding: 43px 43px 29px;
    width: auto;
    min-width: 365px;
    max-width: 500px;

    .delText {
      margin-bottom: 24px;
      font-size: 14px;
      color: #333;
    }
    .confirmBottom {
      text-align: right;
    }
  }
  //提示是一行的
  .notitle {
    display: flex;
    align-items: center;
    .errmessage {
      margin: 0;
    }
  }
}
.map-title {
  // margin: 10px 0 0 20px;
  margin-bottom: 10px;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: calc(50% - 7.5px);
    left: 0;
    width: 6px;
    height: 16px;
    background: #19c3a0;
    opacity: 1;
    border-radius: 3px;
  }
  .eos {
    margin-left: 10px;
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    line-height: 21px;
    color: #333333;
    opacity: 1;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding-right: 20px;
    .eosMaor {
      color: cornflowerblue;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
    }
  }
}
.boxLines {
  width: 390px;
  height: 300px;
}
.progressArm {
  width: 95%;
  margin-top: 25px;
  white-space: nowrap;
  // height: 60px;
}
/deep/.select-style1 {
  .ivu-select-arrow {
    padding: 9px;
    margin-right: -9px;
    background-color: #57a3f3;
    color: #fff;
    border-radius: 0 5px 5px 0;
    transition: none;
  }
  .ivu-icon-ios-arrow-down:before {
    color: #fff;
  }
}
/deep/.select-style2 {
  .ivu-select-arrow {
    padding: 9px;
    margin-right: -9px;
    background-color: #57a3f3;
    color: #fff;
    border-radius: 5px 0px 0px 5px;
    transition: none;
  }
  .ivu-icon-ios-arrow-down:before {
    color: #fff;
  }
}
.chainSelect {
  display: flex;
  flex-direction: row-reverse;
  margin-right: 10px;
}
.textContent {
  width: 210px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.null {
  text-align: center;
  height: 300px;
  line-height: 300px;
}
</style>
