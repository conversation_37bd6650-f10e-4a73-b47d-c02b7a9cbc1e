import Vue from 'vue'
import Router from 'vue-router'
import {
  routes
} from './router'
import store from '@/store'
import {
  setTitle,
  setToken,
  getToken,
  localRead,
  getparams,
  localSave
} from '@/lib/util'
// import { url4A } from '../../static/config.json'
import {
  Modal
} from 'view-design'
// import { MessageBox } from 'element-ui'
import 'view-design/dist/styles/iview.css'
import {
  loginSysConfig
} from '@/api/user'
import {
  getconfig,
  getLogsave
} from '@/api/contract'
// import '../styles/common/elementmodal.less'
// import { Modal,Message } from "view-design";
// import clonedeep from 'clonedeep'

// 解决路由跳转问题
const originalPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}
// Vue.use(iview)
Vue.use(Router)
const router = new Router({
  mode: 'history',
  // mode: 'hash',
  routes
})
let totalSeconds = 30 * 60;
let timer = null;
const setTime = (fromData, toData) => {
  // console.log('倒计时开始');
  timer = setInterval(() => {
    if (totalSeconds > 0) {
      totalSeconds--;
      // console.log(`剩余时间: ${Math.floor(totalSeconds / 60)} 分 ${totalSeconds % 60} 秒`);
    } else {
      clearInterval(timer); // 倒计时结束时清除定时器
      getLog(fromData, toData, 'USER_STOP_TIMEOUT')
    }
  }, 1000);
};
const burialPoint = ["Dashboard", "浏览器", "链账户管理", "智能合约", "链管理", "链账户资源管理", "服务管理", "配置管理", "文件管理", "租户管理", "用户管理", "角色管理", "菜单管理", "合约审批", "链账户审批", "工单审批", "链账户资源审批", "用户行为日志", "应用行为日志",
  "服务中心", "中移链生产运维总览视图", "中移链运营总览视图", "中移链生产运行驾驶舱"
]
const stopData = ['关于我们', '新建工单', '个人中心','视图中心']
sessionStorage.setItem('isLog', true)
const getLog = (fromData, toData, type) => {
  let operation = {
    "lastPage": fromData, //上一页页面路由
    "currentPage": toData, //当前的
    "operateType": type
  }
  getLogsave(operation).then((res) => {
    if (res.code != '00000') {
      sessionStorage.setItem('isLog', false)
      sessionStorage.setItem('fromName', fromData);
    } else {
      sessionStorage.setItem('isLog', true)
      sessionStorage.setItem('fromName', fromData);
      sessionStorage.setItem('recordCurrent', toData)
    }
  })
}
const getStopLog = (fromData, toData, type) => {
  let currentPage = sessionStorage.getItem('toName')
  let operation = {
    "lastPage": '',
    "currentPage": toData,
    "operateType": 'USER_STOP_TAB'
  }
  getLogsave(operation).then((res) => {
    // sessionStorage.setItem('fromName', currentPage)
  })
}

router.beforeEach((to, from, next) => {
  // console.log('即将要去的路由：', to.meta.title, '从哪个路由过来的：', from.meta.title);
  let isLog = sessionStorage.getItem('isLog') //获取记录的埋点是否成功状态
  let fromData = null //从哪个路由名称过来的
  let toData = to.meta.title //即将要去的路由名称
  if (timer) {
    clearInterval(timer); // 清除现有的定时器
  }
  totalSeconds = 30 * 60; // 重置倒计时
  if (burialPoint.includes(from.meta.title)) {
    // 当埋点成功的时候
    if (isLog == 'true') {
      if (from.meta.title == '视图中心') {
        let lastFrom = sessionStorage.getItem('fromName');
        fromData = lastFrom
      } else {
        // 当用户是从登陆过来的，fromData为空
        fromData = from.meta.title == '登录' ? "" : from.meta.title
      }
    } else {
      // 埋点失败时，获取的上一次存储的fromData页面的名称，作为下一次的传递
      let lastPages = sessionStorage.getItem('fromName');
      // console.log('请求失败后获取的上一次页面的路由', lastPages);
      fromData = from.meta.title == '登录' ? "" : lastPages
    }
  } else {
    let newFrom = sessionStorage.getItem('fromData');
    fromData = newFrom
    // console.log('newFromnewFromnewFromnewFromnewFrom', newFrom);
  }


  if (to.meta.title !== '视图中心') {
    let isToName = burialPoint.includes(to.meta.title)
    sessionStorage.setItem('toName', isToName ? to.meta.title : '');;
  }
  if (from.meta.title !== '视图中心') {
    let isFromName = burialPoint.includes(from.meta.title)
    sessionStorage.setItem('fromName', isFromName ? from.meta.title : '') //上一页面的路由
  }
  console.log('传递给后端的上一个页面', fromData, '传递给后端的当前页面', toData);
  // 正常查看
  //  console.log('rentrecordCurrent',sessionStorage.getItem('recordCurrent')); 
  let recordCurrentName = sessionStorage.getItem('recordCurrent')
  let stopToDataName = sessionStorage.getItem('stopToData')
  if (burialPoint.includes(toData)) {
    if (recordCurrentName !== toData) {
      getLog(fromData, toData, 'NORMAL_VIEW')
      setTime(fromData, toData); // 重启倒计时
    } else if (stopData.includes(stopToDataName)) {
      getLog('', toData, 'NORMAL_VIEW')
      setTime('', toData); 
    }

  }
  if (stopData.includes(toData)) {
    sessionStorage.setItem('stopToData', toData);
    getStopLog('', recordCurrentName, '')
    // setTime(fromData, toData); // 重启倒计时
  }
  next();
  let params = getparams()
  // 判断4a用户登录条件
  let name = 'CONNECT_4A_OPEN'
  getconfig(name).then((res) => {
    localStorage.setItem('CONNECT_4A_OPEN', res.data.value)
    console.log(to.name);
    if (to.name === 'register' || to.name === 'forget_pwd') {
      next()
    }
    if ((to.name === 'login') && !params.token) {
      if (res.data.value == 1 || params.userSource == 1) {
        next({
          name: 'token_disabled',
          params: {
            token_disabled: 1
          }
        })
        // next()
      } else {
        next({
          name: 'login'
        })
      }
    } else {
      next()
    }
  })


  if (params.token && to.name == 'login') {
    // 保存CONNECT_4A_OPEN值
    const connect4aOpen = localRead('CONNECT_4A_OPEN')
    window.localStorage.clear()
    // 重新设置CONNECT_4A_OPEN值
    localSave('CONNECT_4A_OPEN', connect4aOpen)
    console.log(to, 'to')
    localSave('userSource', params.userSource) // 保存用户来源
    // token登录
    store.dispatch('loginInit', {
      userLoginId: params.userLoginId,
      password: '',
      token: params.token,
      userSource: Number(params.userSource)
    }).then((res) => {
      localSave('userSource', res.data.userSource) // 保存用户来源
      store.dispatch('updateUserLoginId', params.userLoginId) // 更新用户名
      localSave('publicKey', res.data.publicKey) // 存公钥
      store.dispatch('updateCur', 0) // 初始化数据
      store.dispatch('updateTas', 1)
      localSave('fabricOpen', res.data.fabricOpen) // 存fabricOpen
      console.log(params, '_______')
      next({
        name: 'dashboard'
      }) // 跳转到首页
      if (res.data.faceUrl) {
        const faceUrl = res.data.faceUrl
        Vue.prototype.$http.get(faceUrl).then(res => {
          if (res.status === 200) {
            localSave('faceUrl', faceUrl)
            store.dispatch('updateFaceUrl', faceUrl) // 更新头像
          }
        }).catch(error => {
          console.log(error.message + ',图片获取失败！！')
          localSave('faceUrl', '')
          store.dispatch('updateFaceUrl', '') // 清空头像
        })
      } else {
        localSave('faceUrl', '')
        store.dispatch('updateFaceUrl', '') // 清空头像
      }
    }).catch(error => {

      if (error.code === 'B0001') {
        next({
          name: 'token_disabled',
          params: {
            token_disabled: 1
          }
        }) // token_disabled验证token失效的值
      } else {
        loginSysConfig().then((res) => {
          if (res.data.url) {
            Modal.error({
              title: '提示',
              content: error.message,
              showCancelButton: false,
              onOk: () => {
                window.location.href = `${res.data.url}`
              }
            })
          } else {
            next({
              name: 'token_disabled',
              params: {
                token_disabled: 1
              }
            }) // token_disabled验证token失效的值
          }
        })

      }
    })
  } else {
    to.meta && setTitle(to.meta.title)
    const token = getToken()
    if (token && to.name === 'login') {
      // 如果已有token且要跳转到login页面，直接跳转到dashboard
      next({
        name: 'dashboard'
      })
    } else if (to.name === 'login' || to.name === 'register' || to.name === 'forget_pwd' || to.name === 'reset_pwd' || to.name === 'token_disabled' || to.name === 'Logout') {
      if (token) next({
        name: 'dashboard'
      })
      else {
        if (localRead('userSource') == 1) { // 判断4a用户获取权限失败回调跳转
          setToken('')
          next({
            name: 'token_disabled',
            params: {
              token_disabled: 1
            }
          })
          window.localStorage.clear()
        } else {
          setToken('')
          window.localStorage.clear()
          next()
        }
      }
    } else if (token) {
      store.dispatch('authorization', localRead('roleId')).then(rules => {
        // console.log('hasGetRules', store.state.router.hasGetRules, store.getters.hasGetRules)
        if (rules[to.name]) next()
        else {
          alert('无访问权限，可与管理员联系申请权限！')
          // router.go(-1) // 返回上一页
          next({
            name: 'dashboard'
          })
          return
        }
        // if (!store.getters.hasGetRules) {
        if (!store.state.router.hasGetRules) {
          store.dispatch('concatRoutes', rules)
        }
      }).catch(() => {
        if (localRead('userSource') == 1) { // 判断4a用户获取权限失败回调跳转
          setToken('')
          window.localStorage.clear()
          next({
            name: 'token_disabled',
            params: {
              token_disabled: 1
            }
          })
        } else {
          setToken('')
          window.localStorage.clear()

          if (localRead('CONNECT_4A_OPEN') == 1) {
            // 如果CONNECT_4A_OPEN为1，跳转到token_disabled页面
            next({
              name: 'token_disabled',
              params: {
                token_disabled: 1
              }
            })
          } else {
            alert('获取权限失败，即将跳转到登录页面！')
            next({
              name: 'login'
            })
          }
        }
      })
    } else if (to.params.userSource == 1) { // 判断4a用户退出操作
      next()
    } else if (to.params.token_disabled == 1) { // 判断4a用户token失效条件
      next()
    } else {
      // window.localStorage.clear()
      if (localRead('CONNECT_4A_OPEN') == 1) {
        // 如果CONNECT_4A_OPEN为1，跳转到token_disabled页面
        next({
          name: 'token_disabled',
          params: {
            token_disabled: 1
          }
        })
      } else {
        alert('登录超时或重复登录，即将跳转到登录页面！')
        next({
          name: 'token_disabled',
          params: {
            token_disabled: 1
          }
        })
      }
    }
  }


  // 获取版本号和说明
  getconfig('CMBAAS_COPYRIGHT_MARKING_CONFIG').then((res) => {
    if (res.data) {
      if (res.data.value) {
        sessionStorage.setItem('versionData', res.data.value)
        const versionData = JSON.parse(res.data.value) || {};
        const link = document.querySelector("link[rel*='icon']") || document.createElement('link');
        link.type = 'image/x-icon';
        link.rel = 'shortcut icon';
        link.href = versionData.iconUrl || '/data/favicon.ico';
        document.head.appendChild(link);
      }
    } else {
      console.log(res)
    }
  }).catch((error) => {
    this.msgInfo('error', error.message, true)
  })

  next()
})

// router.afterEach((to, from) => {
//   // logining = false
// })

export default router