import axios from '../../index'
const BASEURL = '/cmbaas/portal/fabric/CommonAPI'
// 通道列表获取通道信息
export function getChannelList (query) {
  return axios.request({
    url: BASEURL + '?msgType=channel%23getChannelList',
    method: 'get',
    params: query
  })
}

// 取某个通道上的智能合约列表
export function getChannelChaincodeList (query) {
  return axios.request({
    // url: BASEURL + 'chaincode/getChannelChaincodeList',
    url: BASEURL + '?msgType=chaincode%23getChannelChaincodeList',
    method: 'get',
    params: query
  })
}

// （合约安装）查询某合约没有被安装的节点列表
export function getNoChaincodePeerList (query) {
  return axios.request({
    //  url: BASEURL + 'chaincode/getNoChaincodePeerList',
    url: BASEURL + '?msgType=chaincode%23getNoChaincodePeerList',
    method: 'get',
    params: query
  })
}

// 合约安装（执行）
export function InstallChainCode (obj) {
  let data = {
    msgType: 'chaincode#installChaincode',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
  // return axios.request({
  //   url: BASEURL + 'chaincode/installChaincode',
  //   method: 'post',
  //   data: obj
  // })
}

// 2)	智能合约的初始化
export function initChaincode (obj) {
  // return axios.request({
  //   url: BASEURL + 'chaincode/initChaincode',
  //   method: 'post',
  //   data: obj
  // })
  let data = {
    msgType: 'chaincode#initChaincode',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}

// 获取背书策略和组织节点信息
// export function getChaincodeEndorseRule(query) {
//     return axios.request({
//         url: BASEURL + 'chaincode/getChaincodeEndorseRule',
//         method: 'get',
//         params: query
//     })
// }

// 调用合约（执行）
export function callChaincode (obj) {
  // return axios.request({
  //   url: BASEURL + 'chaincode/callChaincode',
  //   method: 'post',
  //   data: obj
  // })
  let data = {
    msgType: 'chaincode#callChaincode',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}

// 获取合约源码
export function chaincodeFunc (obj) {
  // return axios.request({
  //   url: BASEURL + 'chaincode/chaincodeFunc',
  //   method: 'post',
  //   data: obj
  // })
  let data = {
    msgType: 'chaincode#chaincodeFunc',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
