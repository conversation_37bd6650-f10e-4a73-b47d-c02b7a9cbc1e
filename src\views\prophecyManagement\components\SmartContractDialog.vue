<!--
  aturun
 智能合约选择
  2021/10/21

-->
<template>
  <el-dialog
      class="dialog_sty smart_contract"
      :title="tableTitle"
      :visible.sync="Visible"
      width="1000px"
      :modal="false"
      destroy-on-close
      :close-on-click-modal="false"
      :before-close="handleClose"
      @opened="open"
  >
    <div>
      <div class="dialog_content">
        <div class="content-top">
          <div class="content-top-lift">
            <span @click="goBack()" v-if="tableActive!=0" style="padding-right: 15px;"><-返回</span>{{tableTitle}}
          </div>
          <div class="content-top-right" v-show="tableActive==1">
            <div class="top-right-input icon-search_suffix" v-if="operationState==1">
              <el-input
                  :placeholder="inpPlaceholder"
                  v-model="input"
                  @keyup.enter.native="getlist">
              </el-input>
              <i slot="suffix" class="el-icon-search" @click="getlist"></i>
            </div>

          </div>

        </div>
        <div class="content-body">
          <el-table
              :data="tableData"
              style="width: 100%"
              height="640"
              stripe
          >
            <el-table-column :prop="item.field" :label="item.lable" v-for="(item,index) in tableTitleInfo" :key="index"></el-table-column>
            <el-table-column label="操作" v-if="operationState==1||(operationState==0&&tableActive!=2)">
              <template slot-scope="scope">
                <el-button
                    v-if="operationState==1"
                    style="width: 90px"
                    size="mini"
                    @click="handleChoice(scope.$index, scope.row)">选择</el-button>
                <el-button
                    v-if="operationState==0&&tableActive!=2"
                    style="width: 90px"
                    size="mini"
                    @click="handlelook(scope.$index, scope.row)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
<!--    <span slot="footer" class="dialog-footer">-->
<!--    <el-button @click="Visible = false">取 消</el-button>-->
<!--    <el-button type="primary" @click="submitForm">确 定</el-button>-->
<!--  </span>-->
  </el-dialog>
</template>

<script>
import * as api from "../api";
import {contractmessage, getHttpContractListTwo, getVersion} from "../api";
export default {
  name: "SmartContractDialog",
  components: {},
  props:[],
  data() {
    return {
      contractId:null,
      eosChainId:null,
      sourceMode:null,//0请求合约1消费者合约
      operationState:0,
      tableTitle:'合约链账户',
      tableTitleInfo:[],
      inpPlaceholder:'可输入合约链账户查询',
      input:'',
      chainAccountId:null,
      isVersion:true,
      Visible:false,
      ChoiceValue:'',
      Form:{
        name:''
      },
      contractAccountId:null,
      tableActive:0,
      ContractChainAccountT:[ //1 合约链账户
        {lable:'链账户名称',field:'contractAccountName'},
        {lable:'描述',field:'auditRemark'},
        {lable:'创建时间',field:'createTime'},
      ],
      SmartContractT:[ //2 智能合约
        {lable:'合约名称',field:'contractName'},
        {lable:'描述',field:'brief'},
        {lable:'创建时间',field:'createTime'},
      ],
      VersionT:[ //3 版本
        {lable:'版本号',field:'uploadVersion'},
        {lable:'CPP文件名',field:'cppName'},
        // {lable:'hpp文件名',field:'uu'},
        {lable:'上传备注',field:'uploadBrief'},
        {lable:'上传时间',field:'uploadTime'},
      ],
      tableData: [

      ],
      rules: {
        name: [
          {required: true, message: '请输入用户名称', trigger: 'blur'},
        ],
      },
      ChoiceForm:{
        ContractChainAccount:'',
        SmartContract:'',
        Version:''
      }
    }
  },

  watch: {
    tableActive(newVal){
     this.setDataInfo(newVal)
    }
  },
  created() {
    this.tableTitleInfo = this.ContractChainAccountT
  },
  mounted() {
  },
  methods: {
    setDataInfo(newVal){
      if(this.operationState!=0){
        if(newVal==0){
          this.tableTitle = '合约链账户'
          this.inpPlaceholder = '可输入合约链账户查询'
          this.tableTitleInfo = this.ContractChainAccountT
          this.getHttpUserList()
        } else if(newVal==1){
          this.tableTitle = '智能合约'
          this.inpPlaceholder = '可输入合约名称查询'
          this.tableTitleInfo = this.SmartContractT
          this.getHttpContractList()
        }else if(newVal==2){
          this.tableTitle = '合约版本号'
          this.inpPlaceholder = '可输入合约版本号查询'
          this.tableTitleInfo = this.VersionT
          this.getVersion(this.contractId)
          // this.tableData= [
          //   {version:'U_1329',yy:'test001.cpp',uu:'/',ii:'预言机示例',oo:'2021-01-01 00:00:00'},
          //   {version:'U_1325',yy:'test002.cpp',uu:'/',ii:'预言机示例',oo:'2021-01-01 00:00:00'},
          //   {version:'U_1324',yy:'test003.cpp',uu:'/',ii:'预言机示例',oo:'2021-01-01 00:00:00'},
          // ]
        }
      }
      else { // 示例
        // this.tableTitle = '合约示例'
        if(newVal==0){
          this.tableTitle = '合约链账户'
          this.inpPlaceholder = '可输入合约链账户查询'
          this.tableTitleInfo = this.ContractChainAccountT
          this.tableData= [
            {contractAccountName:'示例链账户Test01',auditRemark:'预言机示例',createTime:'2021-01-01 00:00:00'},
            {contractAccountName:'示例链账户Test02',auditRemark:'预言机示例',createTime:'2021-01-01 00:00:00'},
          ]
        } else if(newVal==1){
          this.tableTitle = '智能合约'
          this.inpPlaceholder = '可输入合约名称查询'
          this.tableTitleInfo = this.SmartContractT
          this.tableData= [
            {contractName:'示例合约1',brief:'示例',createTime:'2021-01-01 00:00:00'},
            {contractName:'示例合约2',brief:'示例',createTime:'2021-01-01 00:00:00'},
            {contractName:'示例合约3',brief:'示例',createTime:'2021-01-01 00:00:00'},
          ]
        }else if(newVal==2){
          this.tableTitle = '合约版本号'
          this.inpPlaceholder = '可输入合约版本号查询'
          this.tableTitleInfo = this.VersionT
          this.tableData= [
            {uploadVersion:'U_1329',cppName:'test001.cpp',uploadBrief:'预言机示例',uploadTime:'2021-01-01 00:00:00'},
            {uploadVersion:'U_1325',cppName:'test002.cpp',uploadBrief:'预言机示例',uploadTime:'2021-01-01 00:00:00'},
            {uploadVersion:'U_1324',cppName:'test003.cpp',uploadBrief:'预言机示例',uploadTime:'2021-01-01 00:00:00'},
          ]
        }
      }
    },
    getVersion(id){
      this.tableData=[]
      api.getVersion(id).then(res=>{
        this.tableData= res.data.uploadRecords
      }).catch(err=>{

      })
    },
    getlist(){
      if(this.tableActive==0){
        this.getHttpUserList()
      }else if(this.tableActive==1){
        this.getHttpContractList()
      }else if(this.tableActive==2){

      }

    },
    open(){
      this.tableActive=0
      this.setDataInfo(0)
    },
    Refresh(){
      this.input = ''
      this.tableActive=0
    },
    getHttpUserList(){
      api.contractmessage(this.eosChainId,this.chainAccountId,null)
      .then(res=>{
          // let arr = res.data.records

          this.tableData = res.data.contractRecords
          // arr.map(item=>{
          //   if(item.accountTypeKey=='CONTRACT'){
          //     this.tableData.push(item)
          //   }
          // })
      })
      // api.getEosUserList(
      //   this.eosChainId,
      //   {
      //     "accountType": "",
      //     "bizType": "",
      //     "chainAccountName": this.input,
      //     "contractId": null,
      //     "pageParam": {
      //       "pageIndex": 1,
      //       "pageSize": 100
      //     },
      //     "status": "",
      //     "tenantName": ""
      //
      //   }
      // ).then(res=>{
      //   let arr = res.data.records
      //   this.tableData = []
      //   arr.map(item=>{
      //     if(item.accountTypeKey=='CONTRACT'){
      //       this.tableData.push(item)
      //     }
      //   })
      // })
    },
    getHttpContractList(){
      api.getHttpContractListTwo({
        "pageParam": {
          "pageIndex": 1,
          "pageSize": 100
        },
        chainId:this.chainId,
        "contractAccountId":this.contractAccountId,
        "queryName": this.input
      }).then(res=>{
        this.tableData=res.data
      })
    },
    goBack(){
      if(this.operationState!=0){
        let val = []
        this.input = ''
        val=this.ChoiceValue.split(',')
        if(this.tableActive==1){
          this.ChoiceValue= ''
        } else if(this.tableActive==2){
          this.ChoiceValue = val[0]
        }
      }
      this.tableActive --
    },
    submitForm() {

      this.$emit('setChoiceInfo', {ChoiceValue:this.ChoiceValue,ChoiceForm:this.ChoiceForm})
      this.ChoiceValue = ''
      this.eosChainId = null
      this.ChoiceForm={
            ContractChainAccount:'',
            SmartContract:'',
            Version:''
      }
      this.Visible =false
    },
    resetForm() {
      this.$refs['ss'].resetFields();
    },
    handleClose(done) {
      this.Refresh()
       done();
    },
    handleChoice(index, row) {
     this.input = ''
    if(this.tableActive==0){
      this.ChoiceValue = row.contractAccountName
      this.ChoiceForm={...this.ChoiceForm,
            ContractChainAccount:JSON.stringify(row),
            SmartContract:JSON.stringify(row),
            Version:''
      }
      this.chainId=row.chainId
      this.contractAccountId = row.contractAccountId
      // console.log(row.contractAccountId)
      this.tableActive++
      } else if(this.tableActive==1){
      this.ChoiceValue =this.ChoiceValue+','+ row.contractName
      this.ChoiceForm={
        ...this.ChoiceForm,
        SmartContract:JSON.stringify(row),
      }
      this.contractId = row.contractId
      this.tableActive++
    } else if(this.tableActive==2){
      this.ChoiceValue =this.ChoiceValue+','+ row.uploadVersion
      this.ChoiceForm={
        ...this.ChoiceForm,
        Version:row.version
      }

      this.submitForm()
      }
    },
    handlelook(index, row) {
      if(this.tableActive==0){
        this.tableActive++
      } else if(this.tableActive==1){
        this.tableActive++
      } else if(this.tableActive==2){
        this.tableActive=0
        this.Visible =false
      }
    },
  },

}
</script>

<style lang="less" scoped>
.smart_contract{
  .dialog_content{
    .content-top{
      display: flex;
      justify-content: space-between;
      .content-top-right{
        display: flex;
        padding: 16px 20px 0 0;
        .top-right-input{
          margin-right: 12px;
        }
        .top-right-button{
          .el-button{

          }
        }
      }
      .content-top-lift{
        padding: 25px 0 0 23px;
        font-size: 14px;
        font-weight: 500;
        line-height: 24px;
        color: #333333;
        opacity: 1;
        span{
          color: #2D8CF0;
        }
      }
    }
    .content-body{
      margin: 11px 17px 0 16px;
    }
    .table_pag{
      margin: 12px 16px 0 0;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
