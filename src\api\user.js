import axios from "./index";
import { encryptedData } from "@/lib/encrypt";

// export const getUserInfo = ({ userId }) => {
//   return axios.request({
//     url: '/getUserInfo',
//     method: 'post',
//     data: {
//       userId
//     }
//   })
// }

// 查询侧边菜单栏权限
export const authorization = (roleId) => {
  return axios.request({
    url: "/cmbaas/portal/role/resources/" + JSON.parse(roleId),
    method: "GET",
  });
};
// 登录
export const login = ({ userLoginId, password }) => {
  return axios.request({
    url: "/cmbaas/portal/user/login",
    method: "POST",
    data: {
      userLoginId,
      // password:'',
      password: password,
    },
  });
};

// 4A用户登录
export const loginInit = ({ userLoginId, token, userSource }) => {
  return axios.request({
    url: "/cmbaas/portal/user/loginInit",
    method: "POST",
    data: {
      userLoginId: userLoginId,
      token: token,
      userSource: userSource,
    },
  });
};
// 注册
export const register = (params) => {
  return axios.request({
    url: "/cmbaas/portal/user/register",
    method: "POST",
    data: params,
  });
};
// 获取登录前的配置
export const loginSysConfig = () => {
  return axios.request({
    url: "/cmbaas/portal/config/sysConfig",
    method: "GET",
  });
};
// 判断用户角色
export const userPermission = () => {
  return axios.request({
    url: "/cmbaas/portal/role/role/permission/info",
    method: "GET",
  });
};
