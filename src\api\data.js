import axios from './index'
import { encryptedData } from '@/lib/encrypt'

// try {
//   JSON.parse(JSON.stringify(pageParam)) // 如果不是json字符串就会抛异常
//   console.log('是')
// } catch(e) {
//   console.log('否')
// }

/** ****************1多链管理相关接口**************** */
// 查询EOS链列表
export const getChainIdList = (pageParam, searchItem = {}) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/list',
    data: {
      languageType: searchItem.languageType,
      chainName: searchItem.chainName ? searchItem.chainName : '',
      engineTypeList: searchItem.engineTypeList
        ? searchItem.engineTypeList
        : [],
      statusKey: searchItem.status !== undefined ? searchItem.status : 'ENABLE',
      chooseTenantId: searchItem.chooseTenantId,
      startTime: searchItem.startTime,
      endTime: searchItem.endTime,
      pageParam
    },
    method: 'POST'
  })
}
// 修改链信息
export const reviseMultiChain = (formItem) => {
  return axios.request({
    // url: '/cmbaas/chain/eos/multi/chain',
    url: '/cmbaas/chain/eos/multi/chain/update',
    // data: {
    //   chainId: formItem.chainId,
    //   chainName: formItem.chainName,
    //   eosChainId: formItem.eosChainId,
    //   chainBrief: formItem.chainBrief,
    //   engineTypeKey: formItem.engineTypeKey,
    //   ownershipKey: formItem.ownershipKey,
    //   statusKey: formItem.statusKey,
    //   // needAudited: formItem.needAuditedKey
    //   auditList: formItem.auditList,
    //   expType: formItem.expType,
    //   chainSource: formItem.chainSource, // 主子链
    //   companyId: formItem.companyId// 公司id
    // },
    data: formItem,
    method: 'POST'
  })
}
// 添加链
export const addMultiChain = (formItem) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain',
    // data: {
    //   chainName: formItem.chainName,
    //   eosChainId: formItem.eosChainId,
    //   chainBrief: formItem.chainBrief,
    //   engineTypeKey: formItem.engineTypeKey,
    //   ownershipKey: formItem.ownershipKey,
    //   statusKey: formItem.statusKey,
    //   auditList: formItem.auditList,
    //   expType: formItem.expType,
    //   // needAudited: formItem.needAuditedKey
    //   chainSource: formItem.chainSource, // 主子链
    //   companyId: formItem.companyId // 公司id
    // },
    data: formItem,
    method: 'POST'
  })
}
// 查询链基本信息
export const getMultiLinkDetails = (eosChainId) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/' + eosChainId,
    method: 'GET'
  })
}
// 更新链节点信息
export const reviseMultiChainNode = (formItem) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/node',
    data: {
      chainId: formItem.chainId,
      nodeId: +formItem.nodeId,
      nodeName: formItem.nodeNamen,
      nodeAddress: formItem.nodeAddressn,
      nodeApiPort: formItem.nodeApiPort,
      nodeTypeKey: formItem.nodeTypeKey,
      statusKey: formItem.statusKey,
      nodeP2pPort: formItem.nodeP2pPort,
      location: formItem.location,
      provinceCode: +formItem.provinceCode, // 省code
      cityCode: +formItem.cityCode, // 市code
      provinceName: formItem.provinceName, // 省名称
      cityName: formItem.cityName, // 市名称
      diskDir: formItem.diskdir// 磁盘挂载
    },
    method: 'PUT'
  })
}
// 添加链节点
export const addMultiChainNode = (formItem) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/nodeList',
    data: {
      chainId: formItem.chainId,
      nodeList: [
        {
          nodeName: formItem.nodeNamen,
          nodeAddress: formItem.nodeAddressn,
          nodeApiPort: +formItem.nodeApiPort,
          nodeTypeKey: formItem.nodeTypeKey,
          nodeP2pPort: +formItem.nodeP2pPort,
          location: formItem.location,
          provinceCode: +formItem.provinceCode, // 省code
          cityCode: +formItem.cityCode, // 市code
          provinceName: formItem.provinceName, // 省名称
          cityName: formItem.cityName, // 市名称
          diskDir: formItem.diskdir// 磁盘挂载
        }
      ]
    },
    method: 'POST'
  })
}
// 向指定EOS链添加管理链账户
export const addManageAccount = (data) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/' + data.chainId + '/manageAccount',
    data: {
      manageAccountName: data.manageAccountName,
      ownerPrivateKey: data.ownerPrivateKey,
      activePrivateKey: data.activePrivateKey
    },
    method: 'POST'
  })
}
// 移除管理链账户
export const deleteManageAccount = (chainId, manageAccountName) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/multi/chain/' +
      chainId +
      '/manageAccount/' +
      manageAccountName,
    method: 'POST'
  })
}
// 查询eos链节点列表接口
export const getNodeList = (pageParam, chainId, status, nodeStatus) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/node/list',
    data: {
      chainId: chainId,
      status: status,
      nodeStatus: nodeStatus,
      pageParam
    },
    method: 'POST'
  })
}
// 修改链租户可见性接口
export const reviseVisible = (chainId, tenantVisibility) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/tenant',
    data: {
      chainId: chainId,
      tenantVisibility: tenantVisibility
    },
    method: 'POST'
  })
}
// 查询链租户列表接口
export const getChainTenant = (
  pageParam,
  chainId,
  visibleState,
  tenantName
) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/tenant/list',
    data: {
      chainId: chainId,
      visibleState: visibleState,
      tenantName: tenantName,
      pageParam
    },
    method: 'POST'
  })
}
// 添加链租户列表接口
export const addChainTenant = (chainId, tenantIds) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/tenant/new',
    data: {
      chainId: chainId,
      tenantIds: tenantIds
    },
    method: 'POST'
  })
}
// 删除链租户接口
export const delChainTenant = (chainId, tenantId) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/tenant/delete',
    data: {
      chainId: chainId,
      tenantId: tenantId
    },
    method: 'POST'
  })
}

// 新增或修改es配置接口
export const upsert = (params) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/config/es/upsert',
    data: params,
    method: 'POST'
  })
}
// 测试es连接接口
export const connectionTest = (params) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/config/es/test',
    data: params,
    method: 'POST'
  })
}

// 查询es配置接口
export const configQuery = (chainId) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/config/es/' + chainId,
    method: 'GET'
  })
}
// 新增或修改hyperion配置接口
export const hyperioncConfig = (params) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/config/hyperion/save',
    data: params,
    method: 'POST'
  })
}
// 查询hyperion配置接口
export const hyperioncConfigQuery = (chainId) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/config/hyperion/' + chainId,
    method: 'GET'
  })
}

/** ****************多链管理相关接口**************** */

/** ****************2链账户相关接口**************** */
// 校验链账户名称
export const checkChainAccountName = (chainId, chainAccountName) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/account/check/' +
      chainAccountName,
    method: 'GET'
  })
}
// 创建链账户
export const addChainNewuser = (chainId, params) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + JSON.parse(chainId) + '/account',
    method: 'POST',
    data: params
  })
}
// 查询链账户列表
export const getChainTableData = (
  chainId,
  pageParam,
  queryName = '',
  accountType = '',
  tenantName = '',
  status = '',
  bizType = '',
  contractId = '',
) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + JSON.parse(chainId) + '/account/list',
    data: {
      chainId,
      accountType,
      queryName,
      tenantName,
      pageParam,
      status,
      bizType,
      contractId
    },
    method: 'POST'
  })
}
// 查询链账户列表我的链账户
export const getChainTableDataAll = (
  params
) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/account/list',
    data: params,
    method: 'POST'
  })
}
// 修改链账户描述
export const reviseDescription = (
  chainAccountName,
  chainId,
  chainAccountUuid,
  description,
  accountCompanyId
) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' + JSON.parse(chainId) + '/account/description',
    data: {
      chainAccountName,
      chainAccountUuid,
      description,
      accountCompanyId
    },
    method: 'PUT'
  })
}
// 查询链账户详情
export const getChainTableDetails = (chainId, chainAccountName) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/account/' +
      chainAccountName,
    method: 'GET'
  })
}
// 创建链账户权限
export const addChainPower = (chainId, chainAccountName, permission) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' + JSON.parse(chainId) + '/account/permission',
    data: {
      chainAccountName,
      permission
    },
    method: 'POST'
  })
}
// 链账户权限部署
export const addChainDeploy = (
  chainId,
  chainAccountId,
  chainAccountPermissionId
) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/account/permissions/deploy/' +
      JSON.parse(chainAccountId) +
      '/' +
      JSON.parse(chainAccountPermissionId),
    method: 'POST'
  })
}
// 链账户审批通过
export const addChainApproval = (chainId, chainAccountId) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/account/approve_account/' +
      JSON.parse(chainAccountId),
    method: 'POST'
  })
}
// 查询链账户私钥权限
export const getPrivateKey = (
  chainId,
  chainAccountId,
  password,
  permissionName = '',
  treasuryToken = ''
) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/' +
      JSON.parse(chainAccountId) +
      '/permission/privateKey',
    data: {
      password: encryptedData(password),
      // password: encryptedData(password, localStorage.publicKey),
      permissionName,
      treasuryToken
    },
    method: 'POST'
  })
}
// 查询合约链账户下所有的合约类型
export const getContractType = () => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/account/contractType/list',
    method: 'GET'
  })
}
// 查询运维信息
export const getContractOps = (chainId, chainAccountId) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/' +
      JSON.parse(chainAccountId) +
      '/ops/query',
    method: 'GET'
  })
}
// 修改运维信息
export const editOpsData = (chainId, chainAccountId, ops) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/' +
      JSON.parse(chainAccountId) +
      '/ops/edit',
    data: {
      ops
    },
    method: 'PUT'
  })
}
/** ****************链账户相关接口**************** */

/** ****************3智能合约相关接口**************** */
// 新建or修改智能合约
export const getContractPower = (params) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/base',
    data: params,
    method: 'POST'
  })
}
// 查询智能合约列表
export const getContractTableData = (pageParam, queryName = '') => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/list',
    data: {
      pageParam,
      queryName
    },
    method: 'POST'
  })
}
// 查询智能合约详情
export const getContractDetails = (contractId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/' + contractId,
    method: 'GET'
  })
}
// 部署智能合约
export const addContractDeploy = (
  contractId,
  chainId,
  uploadVersion,
  contractAccountName,
  ops,
  cppName,
  languageType
) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/deploy',
    data: {
      chainId,
      contractId,
      uploadVersion,
      contractAccountName,
      ops,
      cppName,
      languageType
    },
    method: 'POST'
  })
}
// 查询合约部署记录
export const getContractDeploy = (contractId, uploadVersion) => {
  return axios.request({
    url:
      '/cmbaas/contract/eos/contract/deployContract/' +
      JSON.parse(contractId) +
      '/' +
      uploadVersion,
    method: 'GET'
  })
}
// 查询合约链码
export const getContractChaincode = (
  contractId,
  uploadVersion,
  fileName,
  pageParam
) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/chaincode',
    data: {
      contractId,
      uploadVersion,
      fileName,
      pageParam
    },
    method: 'POST'
  })
}
// 上传智能合约源代码文件
// 上传智能合约源代码文件
export const uploadFile = (
  contractId,
  cppFile,
  hppFile,
  size,
  securityScanReportFile,
  languageType,
  isSingleCpp,
  interfacDoc
) => {
  const data = new FormData()
  data.append('size', size)
  data.append('cppFile', cppFile)
  data.append('languageType', languageType)
  data.append('isSingleCpp', isSingleCpp)
  data.append('interfacDoc', interfacDoc)
  hppFile.forEach((val) => {
    data.append('hppFile', val)
  })
  securityScanReportFile.forEach((val) => {
    data.append('securityScanReportFile ', val)
  })
  return axios.request({
    url: '/cmbaas/contract/eos/contract/base/file/' + JSON.parse(contractId),
    data: data,
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 查询合约编译日志
export const getContractCompileLog = (contractId, uploadVersion) => {
  return axios.request({
    url:
      '/cmbaas/contract/eos/contract/compileLog/' +
      JSON.parse(contractId) +
      '/' +
      uploadVersion,
    method: 'GET'
  })
}
// 撤回智能合约
export const recall = (params) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/recall',
    data: params,
    method: 'POST'
  })
}

// new ---start 合约共享
// 查询共享合约列表（已获取的共享合约、待获取的共享合约、我共享的合约）
export const shareList = (params) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/list',
    data: params,
    method: 'POST'
  })
}
// 获取共享给我的合约接口(获取操作)
export const contractShareObtain = (params) => {
  return axios.request({
    url: `/cmbaas/contract/eos/contract/share/obtain`,
    data: params,
    method: 'POST'
  })
}
// 查询共享合约详情接口
export const getShareDetail = (shareRecordId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/detail/' + shareRecordId,
    method: 'GET'
  })
}
// 查询指定版本的共享合约部署信息（仅指同一合约链账户）
export const shareVersionList = (params) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/version',
    data: params,
    method: 'POST'
  })
}
// 查询已部署且未共享的合约链账户列表
export const unshareChainAccountList = (contractId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/chainAccountList/' + contractId,
    method: 'GET'
  })
}
// 查询可共享的租户列表
export const shareableTenantList = (params) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/shareableTenantList',
    data: params,
    method: 'POST'
  })
}
// 发起合约共享（待审批或自动审批通过）
export const sharingContract = (params) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/sharingContract',
    data: params,
    method: 'POST'
  })
}
// 查询共享合约的可见租户列表
export const findSharedContractTenantList = (params) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/findSharedContractTenantList',
    data: params,
    method: 'POST'
  })
}
// 添加共享合约的可见租户列表
export const addSharedContractTenantList = (params) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/addSharedContractTenantList',
    data: params,
    method: 'POST'
  })
}
// 查询已共享的合约的链账户列表 查询某一个合约 的已共享的合约链账户列表
export const shareAccountContractList = (params) => {
  return axios.request({
    url: `/cmbaas/contract/eos/contract/share/account/${params.contractId}`,
    data: params,
    method: 'POST'
  })
}
// 查询发布方租户列表
export const shareTenantList = (params) => {
  return axios.request({
    url: `/cmbaas/contract/eos/contract/share/tenant/list`,
    data: params,
    method: 'POST'
  })
}
// 共享合约下架
export const cancelContract = (shareRecordId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/offShareRecord/' + shareRecordId,
    method: 'GET'
  })
}
// 查询已共享合约的租户数(此合约共享给了多少租户的数量)
export const countShared = (shareRecordId) => {
  return axios.request({
    url:
      '/cmbaas/contract/eos/contract/share/countSharedContractTenants/' +
      shareRecordId,
    method: 'GET'
  })
}
// 共享合约解绑，修改被共享租户的状态为已解绑
export const unbound = (shareRecordId, receivedTenantId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/unbound',
    data: {
      shareRecordId,
      receivedTenantId
    },
    method: 'POST'
  })
}
// 删除合约共享的租户，修改被共享租户的状态为已删除
export const shareDelete = (shareRecordId, receivedTenantId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/delete',
    data: {
      shareRecordId,
      receivedTenantId
    },
    method: 'POST'
  })
}
// new ---end 合约共享

/** ****************智能合约相关接口**************** */

/** ****************4租户管理相关接口**************** */
// 创建租户
export const addTenantPower = (
  tenantName,
  tenantBrief,
  tenantId = '',
  companyId
) => {
  return axios.request({
    // url: '/cmbaas/portal/tenant',
    url: '/cmbaas/portal/tenant/save',
    data: {
      tenantId,
      tenantName,
      tenantBrief,
      companyId
    },
    method: 'POST'
  })
}
// 查询租户列表
export const getTenantTableData = (pageParam, tenantName = '') => {
  return axios.request({
    url: '/cmbaas/portal/tenant/list',
    data: {
      tenantName,
      pageParam
    },
    method: 'POST'
  })
}
// 修改租户基本信息
export const reviseTenantBrief = (tenantId, tenantName, tenantBrief) => {
  return axios.request({
    url: '/cmbaas/portal/tenant/' + JSON.parse(tenantId),
    data: {
      tenantId,
      tenantName,
      tenantBrief
    },
    method: 'POST'
  })
}
// 查询租户基本信息
export const getTenantDetailsf = (tenantId) => {
  return axios.request({
    url: '/cmbaas/portal/tenant/detail/' + JSON.parse(tenantId),
    method: 'GET'
  })
}
// 查询租户的已分配用户列表
export const getAssigned = (
  tenantId,
  pageParam,
  userName = '',
  fullQuery = 1,
  skipVerify = 0
) => {
  return axios.request({
    url: '/cmbaas/portal/tenant/assigned/list/' + JSON.parse(tenantId),
    data: {
      userName,
      fullQuery,
      skipVerify,
      pageParam
    },
    method: 'POST'
  })
}
// 查询租户的未分配用户列表
export const getUnassigned = (tenantId, pageParam, userName = '') => {
  return axios.request({
    url: '/cmbaas/portal/tenant/unassigned/list/' + JSON.parse(tenantId),
    data: {
      userName,
      pageParam
    },
    method: 'POST'
  })
}
// 查询登录用户可分配角色列表
export const getTenantRoleList = (tenantId) => {
  return axios.request({
    url: '/cmbaas/portal/tenant/role/list/' + JSON.parse(tenantId),
    method: 'GET'
  })
}
// 租户添加/删除用户
export const getTenantUser = (tenantId, datas, type = 'POST') => {
  return axios.request({
    url: '/cmbaas/portal/tenant/user/' + JSON.parse(tenantId),
    data: datas,
    method: type
  })
}

export const getTenantUser1 = (tenantId, datas, type = 'POST') => {
  return axios.request({
    url: '/cmbaas/portal/tenant/user/delete/' + JSON.parse(tenantId),
    data: datas,
    method: type
  })
}
/** ****************租户管理相关接口**************** */

/** ****************5角色管理相关接口**************** */
// 查询角色名称是否存在
export const checkName = (roleName) => {
  return axios.request({
    url: '/cmbaas/portal/role/checkName/' + roleName,
    method: 'GET'
  })
}
// 查询父角色列表
export const getRoleparentList = (datas) => {
  return axios.request({
    url: '/cmbaas/portal/role/parentList',
    data: datas,
    method: 'POST'
  })
}
// 查询角色列表
export const getRoleTableData = (pageParam,roleName) => {
  return axios.request({
    url: '/cmbaas/portal/role/list',
    data: {
      pageParam,
      roleName
    },
    method: 'POST'
  })
}
// 新建or修改角色
export const getRoleAction = (params, method) => {
  return axios.request({
    url: '/cmbaas/portal/role',
    data: params,
    method: method
  })
}
// 查询角色资源树
export const getRoleResources = (params) => {
  return axios.request({
    url: '/cmbaas/portal/role/resources/tree',
    data: params,
    method: 'POST'
  })
  // 新修改的
  // return axios.request({
  //   url: '/cmbaas/portal/role/resources/tree',
  //   data: {
  //     roleId,
  //     'parentId': parentResourceId
  //   },
  //   method: 'POST'
  // })
}
// 更新角色资源关系
export const editRoleTableData = (params) => {
  return axios.request({
    url: '/cmbaas/portal/role/resources',
    data: params,
    method: 'POST'
  })
}
/** ****************角色管理相关接口**************** */

/** ****************6合约权限管理相关接口**************** */
// 查询已部署的合约信息
export const getContractInfo = (chainId, chainAccountId) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/' +
      JSON.parse(chainAccountId) +
      '/contractInfo',
    method: 'GET'
  })
}
// 获取合约action的绑定记录 --********修改
export const getContractInfoList = (
  chainId,
  chainAccountId,
  action,
  contractSource = 'OWN_CONTRACT',
  shareRecordId = null,
  receivedTenantId,
  normalAccountId
) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + JSON.parse(chainId) + '/action/link/list',
    data: {
      chainAccountId,
      action,
      contractSource,
      shareRecordId,
      receivedTenantId,
      normalAccountId
    },
    method: 'POST'
  })
}
// 查询普通链账户列表
export const getNormalAccountList = (
  chainId,
  chainAccountId,
  action,
  contractSource = 'OWN_CONTRACT',
  shareRecordId = null,
  receivedTenantId,
  normalAccountId
) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' + JSON.parse(chainId) + '/action/account/list',
    data: {
      chainAccountId,
      action,
      contractSource,
      shareRecordId,
      receivedTenantId,
      normalAccountId
    },
    method: 'POST'
  })
}
// 查询链账户权限列表
export const getPermissionList = (
  chainId,
  chainAccountId,
  action,
  normalAccountId
) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + JSON.parse(chainId) + '/permission/list',
    data: {
      chainAccountId,
      normalAccountId,
      action
    },
    method: 'POST'
  })
}
// 合约权限绑定
export const contractbundling = (chainId, FormData) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + JSON.parse(chainId) + '/permission/link',
    data: FormData,
    method: 'POST'
    // headers: {
    //   'Content-Type': 'multipart/form-data'
    // }
  })
}
// 合约权限解绑
export const contractUnbundling = (
  chainId,
  chainAccountId,
  normalAccountId,
  action
) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' + JSON.parse(chainId) + '/permission/unlink',
    data: {
      chainAccountId,
      normalAccountId,
      action
    },
    method: 'POST'
  })
}
/** ****************合约权限管理相关接口**************** */

/** ****************7用户管理相关接口**************** */
export const getUserTableData = (pageParam, userLoginId, tenantId,zoneType) => {
  return axios.request({
    url: '/cmbaas/portal/user/list',
    data: {
      userLoginId,
      tenantId,
      pageParam,
      zoneType
    },
    method: 'POST'
  })
}
// 重置密码--用户输入验证码以完成身份校验
export const checkVerificationCode = (
  userLoginId,
  email,
  phone,
  verificationCode,
  changeType
) => {
  return axios.request({
    url: '/cmbaas/portal/user/checkVerificationCode',
    data: {
      userLoginId: userLoginId,
      email: email ? encryptedData(email, localStorage.publicKey) : null,
      verificationCode: verificationCode,
      changeType: changeType,
      phoneNumber: phone ? encryptedData(phone, localStorage.publicKey) : null,
    },
    method: 'POST'
  })
}
// 重置密码--用户请求邮箱验证码接口
export const requestVerificationCode = (
  userLoginId,
  email,
  phone,
  changeType
) => {
  return axios.request({
    url: '/cmbaas/portal/user/requestVerificationCode',
    data: {
      userLoginId: userLoginId,
      email: email ? encryptedData(email, localStorage.publicKey) : null,
      changeType: changeType,
      phoneNumber: phone ? encryptedData(phone, localStorage.publicKey) : null,
    },
    method: 'POST'
  })
}

// // 注册请求验证码
// export const requestCodeRegister = (email) => {
//   return axios.request({
//     url: '/cmbaas/portal/user/requestVerificationCode',
//     data: {
//       email: email
//     },
//     method: 'POST'
//   })
// }
// 根据用户账号精确查询用户
export const checkUserLoginId = (loginId) => {
  return axios.request({
    url: '/cmbaas/portal/user/checkLoginId/' + loginId,
    method: 'GET'
  })
}
// 根据邮箱精确查询
export const checkEmail = (email) => {
  return axios.request({
    url: '/cmbaas/portal/user/checkEmail/' + email,
    method: 'GET'
  })
}
// 根据手机号精确查询
export const checkPhone = (phoneNumber) => {
  return axios.request({
    url: '/cmbaas/portal/user/checkPhone/' + phoneNumber,
    method: 'GET'
  })
}
// 用户输入新密码以进行重置
export const enterNewPassword = (data) => {
  return axios.request({
    url: '/cmbaas/portal/user/enterNewPassword',
    data: {
      userLoginId: data.userLoginId,
      email: data.email,
      pss: data.password
    },
    method: 'POST'
  })
}
export const userLogout = () => {
  return axios.request({
    url: '/cmbaas/portal/user/logout',
    method: 'POST'
  })
}

/** ****************用户管理相关接口**************** */

/** ****************8EOS链账户资源配额管理相关接口**************** */
export const getManageAccounts = (chainId) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/multi/chain/' +
      JSON.parse(chainId) +
      '/manageAccounts',
    method: 'GET'
  })
}
export const getChainAccountResources = (chainId, chainAccountName) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/account/' +
      chainAccountName +
      '/resources',
    method: 'GET'
  })
}
export const addBuyRam = (
  chainId,
  manageAccountName,
  chainAccountName,
  ramBytes,
  chainAccountId
) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/account/resources/buyRam',
    data: {
      chainId,
      manageAccountName,
      chainAccountName,
      ramBytes: +ramBytes,
      chainAccountId
    },
    method: 'POST'
  })
}
export const reclaimRam = (chainId, chainAccountId, ramBytes = 0) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/account/resources/reclaimRam',
    data: {
      chainAccountId,
      ramBytes: +ramBytes
    },
    method: 'POST'
  })
}
export const reclaimToken = (chainId, chainAccountId, manageAccountName) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/account/' +
      JSON.parse(chainAccountId) +
      '/resources/token/reclaim',
    data: {
      manageAccountName
    },
    method: 'POST'
  })
}
export const stakeResources = (
  chainId,
  chainAccountName,
  manageAccountName,
  netQuantity = 0,
  cpuQuantity = 0,
  chainAccountId
) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/account/resources/bandwidth/stake',
    data: {
      manageAccountName,
      chainAccountName,
      netQuantity: +netQuantity,
      cpuQuantity: +cpuQuantity,
      chainAccountId
    },
    method: 'POST'
  })
}
export const unstakeResources = (
  chainId,
  chainAccountName,
  manageAccountName,
  unstakeNetQuantity = 0,
  unstakeCpuQuantity = 0,
  chainAccountId
) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/chain/' +
      JSON.parse(chainId) +
      '/account/resources/bandwidth/unstake',
    data: {
      manageAccountName,
      chainAccountName,
      unstakeNetQuantity: +unstakeNetQuantity,
      unstakeCpuQuantity: +unstakeCpuQuantity,
      chainAccountId
    },
    method: 'POST'
  })
}
/** ****************EOS链账户资源配额管理相关接口**************** */

/** ****************9以下是审批功能相关接口**************** */
// 查询链账户列表
export const getChainsData = (
  pageParam,
  chainAccountName,
  tenantIdList,
  statusList
) => {
  return axios.request({
    url: '/cmbaas/chain/audit/chainAccount/list',
    data: {
      chainAccountName,
      tenantIdList,
      statusList,
      pageParam
    },
    method: 'POST'
  })
}
// 查询链账户审批详情
export const getChainApprDetails = (chainAccountId) => {
  return axios.request({
    url: '/cmbaas/chain/audit/chainAccount/detail/' + chainAccountId,
    method: 'GET'
  })
}

// 查询审核合约部署列表
export const getContractsData = (
  pageParam,
  queryName,
  tenantIdList,
  statusList
) => {
  return axios.request({
    url: '/cmbaas/chain/audit/contract/list',
    data: {
      queryName,
      tenantIdList,
      statusList,
      pageParam
    },
    method: 'POST'
  })
}

// 查询租户下拉框
export const getUserData = (id) => {
  return axios.request({
    url: '/cmbaas/chain/audit/tenant/list/all/' + id,
    method: 'GET'
  })
}

// 查询合约审批详情
export const getContrApprDetails = (depoyid) => {
  return axios.request({
    url: '/cmbaas/chain/audit/contract/detail/' + depoyid,
    method: 'GET'
  })
}

// 合约审批通过
// 合约审批通过
export const contractApprovel = (
  bizId,
  type,
  status,
  remark,
  cppName,
  useMemory,
  languageType,
  uploadVersion,
  auditFile
) => {
  const data = new FormData()
  data.append('bizId', bizId)
  data.append('type', type)
  data.append('status', status)
  data.append('remark', remark)
  data.append('cppName', cppName)
  data.append('useMemory', useMemory)
  data.append('languageType', languageType)
  data.append('uploadVersion', uploadVersion)
  auditFile.forEach((val) => {
    data.append('auditFile', val)
  })
  return axios.request({
    url: '/cmbaas/chain/audit/biz',
    data: data,
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
/** ****************以上是审批功能相关接口**************** */

/** ****************10以下是浏览器功能相关接口**************** */
// 查询最近区块
export const getBlocksData = (chainId) => {
  return axios.request({
    url: '/cmbaas/chain/explorer/main/' + chainId + '/block/list',
    method: 'GET'
  })
}
// 查询最近交易
export const getTradesData = (chainId) => {
  return axios.request({
    url: '/cmbaas/chain/explorer/main/' + chainId + '/trade/list',
    method: 'GET'
  })
}
// 查询区块信息
export const getBlockInfo = (chainId, blockNumOrId, pageIndex, pageSize) => {
  return axios.request({
    url: '/cmbaas/chain/explorer/block/' + chainId,
    data: {
      blockNumOrId: blockNumOrId,
      pageIndex: pageIndex,
      pageSize: pageSize
    },
    method: 'POST'
  })
}
// 查询交易信息
export const getTradeInfo = (chainId, trxId, pageIndex, pageSize) => {
  return axios.request({
    url: '/cmbaas/chain/explorer/transaction/' + chainId,
    data: {
      trxId: trxId,
      pageIndex: pageIndex,
      pageSize: pageSize
    },
    method: 'POST'
  })
}
// 查询浏览器概览数据
export const getBrowserMain = (chainId) => {
  return axios.request({
    url: '/cmbaas/chain/explorer/main/' + chainId,
    method: 'GET'
  })
}
// 查询账户信息
export const getAccountData = (chainId, accountName, pageSize, pageIndex) => {
  return axios.request({
    url: '/cmbaas/chain/explorer/account/' + chainId,
    data: {
      accountName: accountName,
      pageSize: pageSize,
      pageIndex: pageIndex
    },
    method: 'POST'
  })
}

// 查询账户页面Actions列表，超过20条数据
export const getActionData = (
  chainId,
  accountName,
  total,
  pageSize,
  pageIndex
) => {
  return axios.request({
    url: '/cmbaas/chain/explorer/action/' + chainId + '/list',
    data: {
      accountName: accountName,
      total: total,
      pageSize: pageSize,
      pageIndex: pageIndex
    },
    method: 'POST'
  })
}

/** ****************以上是浏览器功能相关接口**************** */

/** ****************11Dashboard相关接口**************** */
// 查询平台概览数据
export const getDashboardOverview = () => {
  return axios.request({
    url: '/cmbaas/portal/dashboard/overview',
    method: 'GET'
  })
}
// 查询平台公告
export const getAnnouncement = (pageParam) => {
  return axios.request({
    url: '/cmbaas/portal/dashboard/announcement',
    data: {
      pageSize: pageParam.pageSize,
      pageIndex: pageParam.pageIndex
    },
    method: 'POST'
  })
}
// 查询平台通知
export const getNotice = () => {
  return axios.request({
    url: '/cmbaas/portal/dashboard/notice',
    method: 'GET'
  })
}
// 查询我的链账户
export const getChainAccount = () => {
  return axios.request({
    url: '/cmbaas/portal/dashboard/chain_account',
    method: 'GET'
  })
}
// 查询我的合约
export const getContractList = () => {
  return axios.request({
    url: '/cmbaas/portal/dashboard/contract',
    method: 'GET'
  })
}
// 查询EOS节点信息
export const getEosMap = (chanId) => {
  return axios.request({
    url: '/cmbaas/portal/dashboard/node/map/' + chanId,
    method: 'GET'
  })
}
// 查询枚举下拉列表接口
export const getListTypeMap = (listType) => {
  return axios.request({
    url: `/cmbaas/portal/dashboard/drop-down/${listType}`,
    method: 'GET'
  })
}
/** ****************以上是Dashboard相关接口**************** */

/** ****************12个人中心相关接口**************** */
// 图片上传接口
export const uploadImageFile = (file) => {
  const data = new FormData()
  data.append('facePic', file)
  return axios.request({
    url: '/cmbaas/portal/user/uploadFacePic',
    data: data,
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 查看用户详情
export const getUserInfo = (userLoginId) => {
  return axios.request({
    url: '/cmbaas/portal/user/' + userLoginId,
    method: 'GET'
  })
}
// 修改密码
export const updatePassword = (currentPassword, newPassword) => {
  return axios.request({
    url: '/cmbaas/portal/user/updatePassword',
    data: {
      currentPss: currentPassword,
      newPss: newPassword
    },
    method: 'POST'
  })
}
// 查询我的租户用户列表
export const tenantList = (pageParam) => {
  return axios.request({
    url: '/cmbaas/portal/user/tenant/user/list',
    data: {
      pageParam: pageParam
    },
    method: 'POST'
  })
}
// 更新用户信息，更新成功后会重新生成token
export const updateUserInfo = (
  userId,
  userLoginId,
  email,
  realName,
  phoneNumber,
  organization
) => {
  return axios.request({
    url: '/cmbaas/portal/user/update',
    data: {
      userId: userId,
      userLoginId: userLoginId,
      email: email,
      realName: realName,
      phoneNumber: phoneNumber,
      organization: organization
    },
    method: 'PUT'
  })
}

/** ****************以上是个人中心相关接口**************** */
/** ****************13菜单管理相关接口**************** */
// 查询菜单树
export const getMenuInfo = (resourceType) => {
  return axios.request({
    url: '/cmbaas/portal/resource/tree/' + resourceType,
    method: 'GET'
  })
}
// 新增或更新菜单/接口信息
export const addMenuInfo = (menuDetail) => {
  return axios.request({
    url: '/cmbaas/portal/resource',
    data: menuDetail,
    method: 'POST'
  })
}
// 批量删除菜单/接口信息
export const deleteMenuInfo = (resourceIdList) => {
  return axios.request({
    url: '/cmbaas/portal/resource/delete',
    data: {
      resourceIdList
    },
    method: 'POST'
  })
}
// 查询菜单明细
export const getMenuDetail = (info) => {
  return axios.request({
    url: '/cmbaas/portal/resource/detail',
    // url: '/cmbaas/portal/resource/detail/' + resourceValue,
    data:info,
    method: 'POST'
  })
}
/** ****************以上是菜单管理相关接口**************** */
/** ****************14公告管理相关接口**************** */
// 添加或修改公告信息接口
export const updateAnnouncement = (editData) => {
  return axios.request({
    url: '/cmbaas/portal/announcement',
    data: {
      announceId: editData.announceId,
      title: editData.title,
      publisher: editData.publisher,
      content: editData.content,
      fileIdList: editData.fileIdList
    },
    method: 'POST'
  })
}
// 删除公告信息接口
export const deleteAnnouncement = (announceId) => {
  return axios.request({
    url: '/cmbaas/portal/announcement/' + announceId,
    method: 'POST'
  })
}
// 上传文件
export const uploadAnnounceFile = (uploadType, file) => {
  const data = new FormData()
  data.append('file', file)
  return axios.request({
    url: '/cmbaas/portal/announcement/file/upload/' + uploadType,
    data: data,
    method: 'POST'
  })
}
// 文件下载
export const downloadAnnounceFile = (fileId) => {
  return axios.request({
    url: '/cmbaas/portal/announcement/file/download/' + fileId,
    method: 'POST',
    responseType: 'blob'
  })
}
/** ****************以上是公告管理相关接口**************** */

/** ****************15以下是合约数据分析接口**************** */
// 查询各能力合约调用量统计
export const getAllContractTypeAnalysis = (chainId, timeScope) => {
  return axios.request({
    url:
      '/cmbaas/statistical/contract/' +
      chainId +
      '/allContractType/' +
      timeScope,
    method: 'GET'
  })
}
// 查询各能力合约调用量历史数据
export const getHistoryAnalysis = (chainId, timeScope) => {
  return axios.request({
    url:
      '/cmbaas/statistical/contract/' +
      chainId +
      '/allContractType/history/' +
      timeScope,
    method: 'GET'
  })
}
// 查询某能力合约调用量统计
export const getContractTypeAnalysis = (chainId, contractType, timeScope) => {
  return axios.request({
    url:
      '/cmbaas/statistical/contract/' +
      chainId +
      '/' +
      contractType +
      '/' +
      timeScope,
    method: 'GET'
  })
}
// 查询合约调用量
export const getAccountNameAnalysis = (chainId, accountNameList, timeScope) => {
  return axios.request({
    url: '/cmbaas/statistical/contract/' + chainId + '/caller',
    data: {
      accountNameList,
      timeScope
    },
    method: 'POST'
  })
}
// 查询所有时间范围
export const getTimeScope = () => {
  return axios.request({
    url: '/cmbaas/statistical/contract/timeScope',
    method: 'GET'
  })
}
// 查询账户资源使用情况
export const getResourcesObj = (chainId, accountId) => {
  return axios.request({
    url: '/cmbaas/statistical/contract/' + chainId + '/resource/' + accountId,
    method: 'GET'
  })
}
// 查询账户资源使用情况
export const getRecentList = (chainId, accountId) => {
  return axios.request({
    url: '/cmbaas/statistical/contract/' + chainId + '/recent/' + accountId,
    method: 'GET'
  })
}
// 查询合约调用单位
export const getContractCallerList = (chainId, accountName, timeScope) => {
  return axios.request({
    url: '/cmbaas/statistical/contract/' + chainId + '/caller/list',
    data: {
      accountName,
      timeScope
    },
    method: 'POST'
  })
}
// 查询合约调用单位
export const getContractCallerDetail = (
  chainId,
  accountName,
  callerNameList,
  timeScope
) => {
  return axios.request({
    url: '/cmbaas/statistical/contract/' + chainId + '/caller/detail',
    data: {
      accountName,
      callerNameList,
      timeScope
    },
    method: 'POST'
  })
}
/** ****************以上是合约数据分析接口**************** */
/** ****************以下是工单需求接口**************** */
// 添加工单信息接口
export const orderNew = (params) => {
  return axios.request({
    url: '/cmbaas/portal/order/new',
    data: params,
    method: 'POST'
  })
}
// 查询未读工单列表接口
export const unreadList = () => {
  return axios.request({
    url: '/cmbaas/portal/order/unread/list',
    method: 'GET'
  })
}
// 查询工单列表
export const getOrderList = (params) => {
  return axios.request({
    url: '/cmbaas/portal/order/list',
    data: params,
    method: 'POST'
  })
}
export const getOrderListUer = (params) => {
  return axios.request({
    url: '/cmbaas/portal/order/user/list',
    data: params,
    method: 'POST'
  })
}
/** ****************查询用户日志列表接口**************** */
export const getLogList = (userLoginId, searchData, pageParam) => {
  return axios.request({
    url: '/cmbaas/portal/log/list',
    data: {
      userLoginId: userLoginId,
      startTime: searchData.startTime,
      endTime: searchData.endTime,
      query: searchData.query,
      pageParam: pageParam
    },
    method: 'POST'
  })
}
/** ****************以上是查询用户日志列表接口**************** */

//
export const getOrderDetailNew = (orderId) => {
  return axios.request({
    url: '/cmbaas/portal/order/detail/approval/' + orderId,
    method: 'GET'
  })
}
// 查询工单详情
export const getOrderDetail = (orderId) => {
  return axios.request({
    url: '/cmbaas/portal/order/detail/' + orderId,
    method: 'GET'
  })
}
// 查询用户对应租户角色列表接口
export const getUserIdList = (userId) => {
  return axios.request({
    url: `/cmbaas/portal/order/user/${userId}/list`,
    method: 'GET'
  })
}
// 查询租户对应用户角色列表接口
export const getTenantIdList = (tenantId) => {
  return axios.request({
    url: `/cmbaas/portal/order/tenant/${tenantId}/list`,
    method: 'GET'
  })
}
// 工单审批
export const orderApprovel = (orderId, orderStatus, auditRemark) => {
  return axios.request({
    url: '/cmbaas/portal/order/approve',
    data: {
      orderId: orderId,
      orderStatus: orderStatus,
      auditRemark: auditRemark
    },
    method: 'POST'
  })
}
// ** ****************以上是工单需求接口**************** *//

// ** ****************消息通知管理需求接口**************** *//
export const noticeManage = (
  noticeType,
  pageParam,
  onlyShow,
  queryKey,
  bizType,
  processStatus
) => {
  return axios.request({
    url: '/cmbaas/portal/notice/list',
    data: {
      noticeType: noticeType,
      pageParam,
      onlyShow: onlyShow,
      queryKey: queryKey,
      bizType: bizType,
      processStatus: processStatus

    },
    method: 'POST'
  })
}
export const read = (noticeIdList) => {
  return axios.request({
    url: '/cmbaas/portal/notice/read',
    data: {
      noticeIdList: noticeIdList
    },
    method: 'POST'
  })
}
// 查询租户和租户成员列表
export const tenantMembers = (params = {}) => {
  return axios.request({
    url: '/cmbaas/portal/notice/tenantMembers',
    params,
    method: 'GET'
  })
}
// 添加手动通知
export const newMsg = (receiverIds, noticeMessage, noticeType) => {
  return axios.request({
    url: '/cmbaas/portal/notice/new',
    data: {
      receiverIds,
      noticeMessage,
      noticeType
    },
    method: 'POST'
  })
}
// 查询消息接收人
export const getReceiverData = (noticeId) => {
  return axios.request({
    url: '/cmbaas/portal/notice/user/' + noticeId,
    method: 'GET'
  })
}
// ** ****************以上是消息通知管理需求接口**************** *//
// ** ****************长安链管理接口**************** *//
// 查询浏览器概览数据
export const chainMakerBrowser = () => {
  return axios.request({
    url: '/cmbaas/portal/chainmaker/explorer/main',
    method: 'GET'
  })
}
// 查询最近区块 查询最近10条区块信息
export const chainMakerBlock = () => {
  return axios.request({
    url: '/cmbaas/portal/chainmaker/explorer/main/block/list',
    method: 'GET'
  })
}
// 查询最近交易 查询最近10条交易信息 从最近的区块开始查询 不够10条就取前一个区块，直到取够10
export const chainMakerTrade = () => {
  return axios.request({
    url: '/cmbaas/portal/chainmaker/explorer/main/trade/list',
    method: 'GET'
  })
}
// 查询chainmaker区块信息
export const chainMakerBlockInfo = (blockHeight, pageIndex, pageSize) => {
  return axios.request({
    url: '/cmbaas/portal/chainmaker/explorer/block',
    data: {
      blockHeight: blockHeight,
      pageSize: pageSize,
      pageIndex: pageIndex
    },
    method: 'POST'
  })
}
// 查询chainmaker交易信息
export const chainMakerTradeInfo = (txId, pageIndex, pageSize) => {
  return axios.request({
    url: '/cmbaas/portal/chainmaker/explorer/transaction',
    data: {
      txId: txId,
      pageSize: pageSize,
      pageIndex: pageIndex
    },
    method: 'POST'
  })
}
// ** ****************以上是长安链管理接口**************** *//

// ******用户日志接口*******
export const application = (userdata) => {
  return axios.request({
    // url: '/cmbaas/chain/application/log/list',
    url: '/cmbaas/portal/application/log/list',
    data: userdata,
    method: 'POST'
  })
}
// 新增用户日志导出接口
export const exportApplication = (params) => {
  const data = new FormData()
  data.append('userLoginId', params.userLoginId)
  data.append('beginTime', params.beginTime)
  data.append('endTime', params.endTime)
  data.append('applicationName', params.applicationName)
  data.append('opLevelId', params.opLevelId || 0)
  data.append('opType', params.opType)
  return axios.request({
    url: '/cmbaas/portal/export/exportUserBehaviorRecords',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    method: 'POST'
  })
}

// *******系统日志接口*******
export const interfaceo = (intData) => {
  return axios.request({
    url: '/cmbaas/portal/log/interfaceLoglist',
    data: intData,
    method: 'POST'
  })
}

// 新增凭证文件
export const newDeposit = (title, arg, userLoginId, develo) => {
  const data = new FormData()
  data.append('title', title)
  data.append('arg', arg)
  data.append('platformId', 1)
  data.append('userLoginId', userLoginId)
  data.append('chainType', +develo)
  return axios.request({
    url: '/cmbaas/chain/storeevidence/add/file',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    method: 'POST'
  })
}
// 新增凭证内容
export const newDepositValue = (title, content, userLoginId, develo) => {
  const data = new FormData()
  data.append('title', title)
  data.append('content', content)
  data.append('platformId', 1)
  data.append('userLoginId', userLoginId)
  data.append('chainType', +develo)
  return axios.request({
    url: '/cmbaas/chain/storeevidence/add/info',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    method: 'POST'
  })
}
// 存证上传次数
export const survivalCount = () => {
  return axios.request({
    url: '/cmbaas/chain/storeevidence/add/limitcount',
    method: 'GET'
  })
}
// 存证管理
export const survivalSearch = (params) => {
  return axios.request({
    url: '/cmbaas/chain/storeevidence/list',
    data: params,
    method: 'POST'
  })
}

// 操作记录查询
export const operation = (data) => {
  return axios.request({
    url: '/cmbaas/portal/storageevidence/log/list',
    data: data,
    method: 'POST'
  })
}
// 详情页面的文件下载
export const downloadFile = (fileId) => {
  return axios.request({
    url: '/cmbaas/portal/storeevidence/file/download/' + fileId,
    method: 'POST',
    responseType: 'blob'
  })
}
// 存证记录
export const recordGet = (surIndex) => {
  const { code } = surIndex
  const data = new FormData()
  data.append('code', code)
  return axios.request({
    url: '/cmbaas/portal/storeevidence/record/list',
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: data
  })
}

// ******************以上存证应用需求接口*********************//

// 网络添加链
export const addMultiChainNet = (formItem) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/addBlockChain',
    data: {
      chainName: formItem.chainName,
      eosChainId: formItem.eosChainId,
      chainBrief: formItem.chainBrief,
      engineTypeKey: formItem.engineTypeKey,
      ownershipKey: formItem.ownershipKey,
      statusKey: formItem.statusKey,
      auditList: formItem.auditList
      // needAudited: formItem.needAuditedKey
    },
    method: 'POST'
  })
}

// 网络修改链信息
export const reviseMultiChainNet = (formItem) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/updateBlockChain',
    data: {
      chainId: formItem.chainId,
      chainName: formItem.chainName,
      eosChainId: formItem.eosChainId,
      chainBrief: formItem.chainBrief,
      engineTypeKey: formItem.engineTypeKey,
      ownershipKey: formItem.ownershipKey,
      statusKey: formItem.statusKey,
      // needAudited: formItem.needAuditedKey
      auditList: formItem.auditList
    },
    method: 'PUT'
  })
}

// 查询链基本信息new
export const getMultiLinkDetailsNet = (eosChainId) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/' + eosChainId,
    method: 'GET'
  })
}

// 更新链节点信息
export const reviseMultiChainNodeNet = (formItem) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/node/update',
    data: {
      chainId: +formItem.chainId,
      nodeId: +formItem.nodeId,
      nodeName: formItem.nodeName,
      nodeMemory: formItem.nodeMemory,
      nodeDisk: formItem.nodeDisk,
      nodeCpu: formItem.nodeCpu,
      online: formItem.online
    },
    method: 'PUT'
  })
}
// 添加节点信息
export const addMultiChainNodeNet = (formItem) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/node/save',
    data: {
      chainId: formItem.chainId,
      nodeDTO: {
        nodeName: formItem.nodeName,
        nodeMemory: formItem.nodeMemory,
        nodeDisk: formItem.nodeDisk,
        nodeCpu: formItem.nodeCpu
      }
    },
    method: 'POST'
  })
}

// 查询EOS链列表
export const getChainIdListNet = (pageParam, searchItem = {}) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/list',
    data: {
      chainName: searchItem.chainName ? searchItem.chainName : '',
      engineTypeList: searchItem.engineTypeList
        ? searchItem.engineTypeList
        : [],
      statusKey: searchItem.status !== undefined ? searchItem.status : 'ENABLE',
      pageParam
    },
    method: 'POST'
  })
}

// 加入退出接口
export const jion = (jionN) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/node/updatestatus',
    data: jionN,
    method: 'PUT'
  })
}

// 赠送用户
export const GiviUer = (data) => {
  return axios.request({
    url: '/cmbaas/portal/user/userlist',
    data: data,
    method: 'POST'
  })
}

// 赠送用户节点
export const GiviUersNode = (data) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/node/updateuser',
    data: data,
    method: 'PUT'
  })
}

// 角色
export const getRole = (data) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/node/checkrole',
    data: data,
    method: 'PUT'
  })
}
// 启动按钮状态
export const qiStart = (data) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/updateBlockChainStatus',
    data: data,
    method: 'PUT'
  })
}

// 向指定EOS链添加管理链账户 new
export const addManageAccountNet = (data) => {
  return axios.request({
    url: '/cmbaas/portal/chain/node/account/' + data.chainId + '/manageAccount',
    data: {
      manageAccountName: data.manageAccountName,
      ownerPrivateKey: data.ownerPrivateKey,
      activePrivateKey: data.activePrivateKey
    },
    method: 'POST'
  })
}

// 移除管理链账户new
export const deleteManageAccountNet = (chainId, manageAccountName) => {
  return axios.request({
    url:
      '/cmbaas/chain/eos/multi/chain' +
      chainId +
      '/manageAccount/' +
      manageAccountName,
    method: 'POST'
  })
}

// 查询eos链节点列表接口 new
export const getNodeListNet = (pageParam, chainId) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/node/list',
    data: {
      chainId: chainId,
      pageParam
    },
    method: 'POST'
  })
}

// ***************** 以上是新增区块链网络需求接口**************** //

export const subSearch = (params) => {
  return axios.request({
    url: '/cmbaas/chain/blockchain/sendtrx/list',
    data: params,
    method: 'POST'
  })
}

export const newRecordFile = (
  title,
  arg,
  userLoginId,
  chainId,
  chainAccountId
) => {
  const data = new FormData()
  data.append('title', title)
  data.append('arg', arg)
  data.append('platformId', 1)
  data.append('userLoginId', userLoginId)
  data.append('chainId', chainId)
  data.append('chainAccountId', chainAccountId)
  return axios.request({
    url: '/cmbaas/chain/blockchain/sendtrx/file',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    method: 'POST'
  })
}

export const newRecordContent = (
  title,
  content,
  userLoginId,
  chainId,
  chainAccountId
) => {
  const data = new FormData()
  data.append('title', title)
  data.append('content', content)
  // data.append('platformId', 1)
  data.append('userLoginId', userLoginId)
  data.append('chainId', chainId)
  data.append('chainAccountId', chainAccountId)
  return axios.request({
    url: '/cmbaas/chain/blockchain/sendtrx/info',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    method: 'POST'
  })
}

export const getChainIdLists = (data) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/list',
    data: data,
    method: 'POST'
  })
}
// ****************以上是交易记录的需求接口**************** //

// 节点管理列表
export const nodeManagement = (nodeData) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/node/alllist',
    data: nodeData,
    method: 'POST'
  })
}
// 节点详情信息
export const Managementconsume = (consumId) => {
  return axios.request({
    url: '/cmbaas/chain/blockchain/monitor/' + consumId,
    method: 'GET'
  })
}
// 详情信息交易列表
export const Transaction = (transactionList) => {
  return axios.request({
    url: '/cmbaas/chain/block/chain/node/tx/processed/list',
    data: transactionList,
    method: 'POST'
  })
}

// 新增交易文件
export const TransactionFile = (arg, userLoginId, detailid) => {
  const data = new FormData()
  data.append('arg', arg)
  // data.append("platformId", 1);
  // data.append("userLoginId", userLoginId);
  data.append('nodeId', detailid)
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/node/sendtx/file',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    method: 'POST'
  })
}
// 新增交易内容
export const TransactionContent = (title, content, userLoginId, detailid) => {
  const data = new FormData()
  // data.append("title", title);
  data.append('content', content)
  // data.append("platformId", 1);
  // data.append("userLoginId", userLoginId);
  data.append('nodeId', detailid)
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/node/sendtx/info',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    method: 'POST'
  })
}
export const getChainTableDatas = (chainId, pageParam, accountType, status) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + JSON.parse(chainId) + '/account/list',
    data: {
      chainId,
      pageParam,
      status
    },
    method: 'POST'
  })
}
//* **************以上是节点管理需求接口***************//

export const adduser = (adduserdata) => {
  return axios.request({
    url: '/cmbaas/portal/adminadduser/createuser',
    data: adduserdata,
    method: 'POST'
  })
}
// 所属用户
export const Belonginguser = (consumId) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/node/' + consumId,
    method: 'GET'
  })
}
//* **************以上是新建用户需求接口***************//

//* **************查询在线用户数***************//
export const getOnlineUser = () => {
  return axios.request({
    url: '/cmbaas/portal/onlineUser/Count',
    method: 'GET'
  })
}
//* **************以上是查询在线用户数***************//
//* **************查询合约共享审批列表***************//
// 查询合约共享审批列表
export const shareContractData = (
  pageParam,
  queryName,
  tenantIdList,
  statusList
) => {
  return axios.request({
    url: '/cmbaas/chain/audit/contract/share/list',
    data: {
      queryName: queryName,
      tenantIdList: tenantIdList,
      statusList: statusList,
      pageParam: pageParam
    },
    method: 'POST'
  })
}
// 查询审批的合约共享详情
export const shareContractDetail = (shareId) => {
  return axios.request({
    url: '/cmbaas/chain/audit/contract/share/detail/' + shareId,
    method: 'GET'
  })
}
//* **************以上是查询合约共享审批列表***************//
//* **************查询合约共享详情***************//
// 查询指定版本的共享合约部署信息
export const getVersionDetails = (shareRecordId, uploadVersion) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/version',
    data: {
      shareRecordId,
      uploadVersion
    },
    method: 'POST'
  })
}
// 查询共享合约详情
export const getShareDetails = (shareRecordId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/share/detail/' + shareRecordId,
    method: 'GET'
  })
}
//* **************以上是查询合约共享详情***************//

/** ****************关于CMBaaS**************** */
// 分页查看迭代版本列表
export const iterationVersionList = (params) => {
  return axios.request({
    url: '/cmbaas/portal/iterationversion/iterationVersionList',
    data: params,
    method: 'POST'
  })
}
// 新增或编辑迭代版本
export const addOrEditIterationVersion = (params) => {
  return axios.request({
    url: '/cmbaas/portal/iterationversion/addOrEditIterationVersion',
    data: params,
    method: 'POST'
  })
}
// 新增或编辑迭代版本
export const delIterationVersion = (params) => {
  return axios.request({
    url: '/cmbaas/portal/iterationversion/delIterationVersion',
    data: params,
    method: 'POST'
  })
}
// 分页查看常见问题列表
export const commonProblemList = (params) => {
  return axios.request({
    url: '/cmbaas/portal/commonproblem/commonProblemList',
    data: params,
    method: 'POST'
  })
}
// 新增或编辑常见问题
export const addOrEditCommonProblemVersion = (params) => {
  return axios.request({
    url: '/cmbaas/portal/commonproblem/addOrEditCommonProblemVersion',
    data: params,
    method: 'POST'
  })
}
// 删除迭代版本
export const delCommonProblem = (params) => {
  return axios.request({
    url: '/cmbaas/portal/commonproblem/delCommonProblem',
    data: params,
    method: 'POST'
  })
}

// 合约模板列表
export const ContractTemplate = (TemplateTable) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/model/list',
    data: TemplateTable,
    method: 'POST'
  })
}

// 合约模板删除
export const ContractTemplateDelete = (chainId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/model/delete/' + chainId,
    method: 'POST'
  })
}
// 合约模板详情
export const TemplateDetails = (detailList) => {
  const data = new FormData()
  data.append('contractModelId', detailList.contractModelId)
  return axios.request({
    url: '/cmbaas/contract/eos/contract/model/details',
    data: data,
    method: 'POST'
  })
}

// 查询合约链码
export const QueryChainCode = (ChainCode) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/model/chaincode',
    data: ChainCode,
    method: 'POST'
  })
}
// 新建合约类型
export const NewContract = (newList, fromList, hppfiles) => {
  const data = new FormData()
  data.append('jsFile', newList.jsFile)
  data.append('abiFile', newList.abiFile)
  data.append('cppFile', newList.cppFile)
  data.append('goFile', newList.goFile)
  data.append('rsFile', newList.rsFile)
  data.append('solFile', newList.solFile)
  data.append('zipFile', newList.manycppFile)
  if (fromList.isSingleCpp == '1') {
    hppfiles.forEach((val) => {
      data.append('hppFile', val)
    })
  }

  data.append('isSingleCpp', fromList.isSingleCpp)
  data.append('uploadType', JSON.stringify(newList.uploadType))
  data.append('uploadBrief', JSON.stringify(newList.uploadBrief))
  data.append('contractTypeVO', JSON.stringify(fromList))
  return axios.request({
    url: '/cmbaas/contract/eos/contract/model/add',
    data: data,
    method: 'POST'
  })
}
// 获取修改里面的模板预览
export const TemplatePreview = (chainId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/model/getUploadList/' + chainId,
    method: 'POST'
  })
}
// 合约修改
export const PreviewContractType = (newList, upList, hppfile) => {
  const data = new FormData()
  data.append('jsFile', upList.jsFile)
  data.append('abiFile', upList.abiFile)
  data.append('cppFile', upList.cppFile)
  data.append('goFile', upList.goFile)
  data.append('rsFile', upList.rsFile)
  data.append('solFile', upList.solFile)
  data.append('zipFile', upList.manycppFile)
  data.append('isSingleCpp', newList.isSingleCpp)

  hppfile.forEach((val) => {
    data.append('hppFile', val)
  })
  data.append('contractTypeVO', JSON.stringify(newList))
  return axios.request({
    url: '/cmbaas/contract/eos/contract/model/update',
    data: data,
    method: 'POST'
  })
}
//* **************新建合约详情***************//
export const getnewContract = (newContractData) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/base',
    data: newContractData,
    method: 'POST'
  })
}
// 合约模板多选框
export const getTempate = (chainType) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/modelchoice/' + chainType,
    method: 'POST'
  })
}
// 合约模板详情

export const getTempateDetails = (id) => {
  const data = new FormData()
  data.append('contractModelId', id)
  return axios.request({
    url: '/cmbaas/contract/eos/contract/details',
    data: data,
    method: 'POST'
  })
}
// 模板预览智能合约
export const getContractPreview = (chaincode) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/filename/' + chaincode,
    method: 'GET'
  })
}
// 模板预览合约模板
export const getTempatePreview = (chaincode) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/model/filename/' + chaincode,
    method: 'GET'
  })
}
// 上传
export const getUpdata = (upId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/model/getUploadList/' + upId,
    method: 'POST'
  })
}
// 下载模板

export const getDown = (modleContractId, contractName) => {
  return axios.request({
    url:
      '/cmbaas/contract/eos/contract/file/download/' +
      modleContractId +
      '/' +
      contractName,
    method: 'GET',
    responseType: 'blob',
    headers: { 'Content-Type': 'application/json; application/octet-stream' }
  })
}

// 折叠面板智能合约
export const getCollContract = (getCollContent) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/chaincode/preview',
    data: getCollContent,
    method: 'POST'
  })
}
// 折叠面板合约模板
export const getColl = (getCollContent) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/model/chaincode',
    data: getCollContent,
    method: 'POST'
  })
}
// 新建链类型接口
export const getTempateEos = (selecteos) => {
  return axios.request({
    url: '/cmbaas/portal/dashboard/drop-down/' + selecteos,
    method: 'GET'
  })
}
// 新建语言类型接口
export const getTempateLanguage = (selectlanguage) => {
  return axios.request({
    url: '/cmbaas/portal/dashboard/drop-down/' + selectlanguage,
    method: 'GET'
  })
}
//* **************以上是合约模板***************//
// 上架合约市场
export const getShelves = (getCollContent) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/market/add',
    data: getCollContent,
    method: 'POST'
  })
}
// 查询智能合约版本信息
export const getShelvesInfo = (shelvesId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/market/contractVersion/' + shelvesId,
    method: 'GET'
  })
}
// 下载历史
export const download = (versionId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/market/downloadRecord/' + versionId,
    method: 'GET'
  })
}
// 待审批列表
export const PendingList = (getpending) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/market/pending/approval/list',
    data: getpending,
    method: 'POST'
  })
}

export const getMarketInfo = (InfoId, sortId) => {
  // console.log(InfoId)
  return axios.request({
    // url: '/cmbaas/portal/eos/contract/market/marketInfo/' + InfoId,
    url:
      '/cmbaas/contract/eos/contract/market/marketInfo/' +
      InfoId +
      '/' +
      sortId,
    method: 'GET'
  })
}

// 待审批提交审批
export const Pendingsubmit = (getpending) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/market/add/approval',
    data: getpending,
    method: 'POST'
  })
}
// 查看运维信息
export const getAccountOpsDTOMessage = (uploadVersion, contractId) => {
  return axios.request({
    url:
      '/cmbaas/contract/eos/contract/market/getAccountOpsDTOMessage/' +
      uploadVersion +
      '/' +
      contractId,
    method: 'GET'
  })
}

// 查询待办列表
export const serchPendingList = (pageParam) => {
  return axios.request({
    url: '/cmbaas/portal/notice/todo/list',
    data: pageParam,
    method: 'POST'
  })
}
// 查询待阅列表
export const serchReadingList = (pageParam) => {
  return axios.request({
    url: '/cmbaas/portal/notice/toread/list',
    data: pageParam,
    method: 'POST'
  })
}
// 更新状态列表
export const Updatastatus = (updata) => {
  return axios.request({
    url: '/cmbaas/portal/notice/update/noticestatus',
    data: updata,
    method: 'PUT'
  })
}

// 待审批列表
export const getContractMarketList = (getpending) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/market/getContractMarketList',
    data: getpending,
    method: 'POST'
  })
}

// 查看

export const getcontractBagId = (contractBagId) => {
  return axios.request({
    url:
      '/cmbaas/contract/eos/contract/market/findcontractInfoMarketVO/' +
      contractBagId,
    method: 'GET'
  })
}

// 查看源码信息
export const getChaincode = (chaincode) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/chaincode',
    data: chaincode,
    method: 'POST'
  })
}
// 详情下载
export const getDownDe = (modelContractBagId, id, contractName, treasuryToken) => {
  const data = new FormData()
  data.append('treasuryToken', treasuryToken)
  return axios.request({
    url:
      '/cmbaas/contract/eos/contract/market/downloadFile?modelContractBagId=' +
      modelContractBagId +
      '&id=' +
      id +
      '&contractName=' +
      contractName,
    method: 'POST',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下架/恢复
export const getChangeShow = (contractBagId, status) => {
  return axios.request({
    url:
      '/cmbaas/contract/eos/contract/market/updateStatus/' +
      contractBagId +
      '/' +
      status,
    method: 'GET'
  })
}

// 我的上架合约列表
export const ContractOwnerList = (getContractOwnern) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/market/getContractOwnerList',
    data: getContractOwnern,
    method: 'POST'
  })
}
// 我的上架合约列表详情
export const ContractOwnerPreview = (contractId, sort) => {
  return axios.request({
    url:
      '/cmbaas/contract/eos/contract/market/marketInfo/' +
      contractId +
      '/' +
      sort,
    method: 'GET'
  })
}
// 我的上架中恢复上架或申请下架
export const MyApplyFor = (getApplyforInfo) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/market/owner/apply',
    data: getApplyforInfo,
    method: 'POST'
  })
}
//* **************以上是关于CMBaaS***************//
/** ****************合约开发平台对接接口**************** */
export const contractIdeNew = (params) => {
  return axios.request({
    url: '/cmbaas/contract/ide/url',
    data: params,
    method: 'POST'
  })
}
/** ****************以上是合约开发平台对接接口 *****************/

/** ****************应用中心**************** */
// 查询应用列表 分页查询
export const applicationList = (params) => {
  return axios.request({
    url: '/cmbaas/portal/application/center/list',
    data: params,
    method: 'POST'
  })
}
//* **************以上是应用中心***************//
// 进度条

export const getProgress = (data) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/node/network',
    data: data,
    method: 'POST'
  })
}
// 查询Hyperion
export const getHyperionSerch = (chainId) => {
  return axios.request({
    url:
      '/cmbaas/autodeploy/block/chain/node/editBlockChinIpAddress/' + chainId,
    method: 'GET'
  })
}
// 插入Hyperion
export const getHyperionAdd = (chainId, hyperionValue) => {
  return axios.request({
    url:
      '/cmbaas/autodeploy/block/chain/node/UpdateIpAddress/' +
      chainId +
      '/' +
      hyperionValue,
    method: 'GET'
  })
}
//* **************以上是区块链网络新增接口***************//

//* **************以上是区块链网络新增接口***************//

// 新增查询租户列表
export const getTenantList = () => {
  return axios.request({
    url: '/cmbaas/portal/tenant/listAll',
    method: 'GET'
  })
}
// 回显租户
export const getTenantListId = () => {
  return axios.request({
    url: '/cmbaas/portal/tenant/listAllV2',
    method: 'GET'
  })
}

export const goIDE = (contractModelId, operationType) => {
  return axios.request({
    url:
      '/cmbaas/contract/ide/model/url?contractModelId=' +
      contractModelId +
      '&operationType=' +
      operationType,
    method: 'GET'
  })
}

//对接金库  查询接口
// /cmbaas/chain/eos/multi/chain/manageAccountInfo/{eosChainId}?accountName=&treasuryToken=
export const manageAccountInfo = (data) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/manageAccountInfo',
    data: data,
    method: 'POST'
  })
}
// 服务中心
export const getAppCenter = (params) => {
  return axios.request({
    url: '/cmbaas/portal/application/center/list/app/center',
    data: params,
    method: 'POST'
  })
}
// 视图中心
export const getWebCenter = (params) => {
  return axios.request({
    url: '/cmbaas/portal/application/center/list/web/center',
    data: params,
    method: 'POST'
  })
}
export const getChainIdListv2 = (pageParam, searchItem = {}) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/listV2',
    data: {
      languageType: searchItem.languageType,
      chainName: searchItem.chainName ? searchItem.chainName : '',
      engineTypeList: searchItem.engineTypeList
        ? searchItem.engineTypeList
        : [],
      statusKey: searchItem.status !== undefined ? searchItem.status : 'ENABLE',
      chooseTenantId: searchItem.chooseTenantId,
      startTime: searchItem.startTime,
      endTime: searchItem.endTime,
      pageParam
    },
    method: 'POST'
  })
}
// 角色下拉
export const searchRoleList = (params) => {
  return axios.request({
    url: '/cmbaas/portal/role/search/list',
    data: params,
    method: 'POST'
  })
}