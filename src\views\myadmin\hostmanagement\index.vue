<template>
  <!-- 主机管理 -->
  <div>
    <div class="cz_header">
      <Input style="width:15%;margin-right:10px;" placeholder="请输入主机名称/IP地址" v-model="search_value" @on-enter="getmanagementList" />
      <Button type="primary" icon="ios-search" @click.native="getmanagementList">查询</Button>
      <Button type="primary" ghost icon="md-sync" @click.native="reset">重置</Button>
      <Button style="margin-left:10px;float:right;" type="success" ghost @click="addhost" icon="md-add">添加主机</Button>
    </div>
    <div class="cz_table">
      <Table :columns="historyColumns" :data="historyData">
        <template slot-scope="{ row, index }" slot="action">
          <Button size="small" @click="detailhost(row)" style="margin-right:5px;color:#3D73EF;border:1px solid #3D73EF;">详情</Button>
          <Button size="small" style="margin-right:5px;" @click="edithost(row)" :disabled="row.useStatus=='被使用'||row.delStatus=='1'" :style="row.useStatus=='被使用'||row.delStatus=='1'?'color:#c5c8ce;border:1px solid #c5c8ce;':'color:#3D73EF;border:1px solid #3D73EF;'">编辑</Button>
          <Button size="small" :disabled="row.useStatus=='被使用'||row.delStatus=='1'" :style="row.useStatus=='被使用'||row.delStatus=='1'?'color:#c5c8ce;border:1px solid #c5c8ce;':'color:#3D73EF;border:1px solid #3D73EF;'" @click="deletehost(row)">删除</Button>
        </template>
      </Table>
      <Page :total="dataCount" :page-size="tablePageParam.pageSize" show-sizer show-total show-elevator class="paging" @on-change="changepage" :current.sync="tablePageParam.pageIndex" style="text-align: right;margin-top:20px;" @on-page-size-change="pageSizeChange" :pageSizeOpts="pageSizeOpts"></Page>
    </div>
    <!-- 新增/编辑主机 -->
    <Modal v-model="hostmodal" :title="formItem.alertTitle" @on-cancel="cancelhost" footer-hide>
      <Form ref="formItem" :rules="formItemRule" :model="formItem" :label-width="150">
        <FormItem label="主机名称：" prop="hostName">
          <Input placeholder="请输入主机名称" style="width: 300px; vertical-align: baseline" v-model.trim="formItem.hostName" />
        </FormItem>
        <FormItem label="主机IP：" prop="hostIp">
          <Input placeholder="请输入主机IP地址" style="width: 300px; vertical-align: baseline" v-model="formItem.hostIp" />
        </FormItem>
        <FormItem label="node：" prop="key" class="newnode">
          <Input placeholder="key" style="width: 140px; vertical-align: baseline" v-model="formItem.key" /> &nbsp;:&nbsp;
          <FormItem prop="value">
            <Input placeholder="value" style="width: 150px; vertical-align: baseline" v-model="formItem.value" />
          </FormItem>
        </FormItem>
      </Form>
      <div class="sloatbtn" style="margin-top: 10%;">
        <Button type="primary" ghost @click="cancelhost('formItem')">取消</Button>
        <Button type="primary" @click="ok('formItem')">确定</Button>
      </div>
    </Modal>
    <!--  -->
    <Modal v-model="detailmodal" title="主机详情" footer-hide @on-cancel="cancel" width="400">
      <div style="padding-left: 1%;">
        <div class="detail_info">
          <p>主机名称：{{ detailinfo.name }}</p>
          <p>主机IP：{{ detailinfo.ip }}</p>
          <p>node：{{ detailinfo.nodeSelectKey + '&nbsp;' +'：' + detailinfo.nodeSelectValue }}</p>
        </div>
        <div class="sloatbtn">
          <Button type="primary" ghost @click="detailcancel">取消</Button>
          <Button type="primary" @click="detailok">确认</Button>
        </div>
      </div>
    </Modal>
  </div>
</template>
<script>
import { findCmeosNodeLableList, addCmeosNodelable, findCmeosNodelableById, editCmeosNodelable, deleteById } from '@/api/arrange'
export default {
  data () {
    return {
      detailinfo: {},
      detailmodal: false,
      hostmodal: false,
      deleId: '',
      pageSizeOpts: [10, 20, 40, 60, 100],
      userListData: [],
      status: '', // 搜索下拉框值
      dataCount: 0, // 总条数
      search_value: '', // 输入框
      tablePageParam: { pageIndex: 1, pageSize: 10 }, // 分页
      historyColumns: [//   table 表头
        {
          title: '主机名称',
          key: 'name',
          tooltip: true
        },
        {
          title: '主机IP',
          key: 'ip',
          tooltip: true
        },
        {
          title: 'node',
          key: 'node',
          tooltip: true
        },
        {
          title: '状态',
          key: 'useStatus'
          // render: (h, params) => {
          //   // const color = params.row.status === '关闭' ? '#C7C7C7' : '#15AD31'
          //   return h('span', {
          //     // props: {
          //     //   // type: 'dot',
          //     //   color: color
          //     // },
          //     style: { marginLeft: '-8px', color: params.row.status === '关闭' ? 'red' : '#15AD31' }
          //   }, params.row.status)
          // }
        },
        {
          title: '操作',
          slot: 'action',
          width: '250'
          // align: 'center'
        }
      ],
      // 表格数据
      historyData: [],
      isedit: false,
      formItem: {
        alertTitle: '添加主机',
        hostName: '',
        hostIp: '',
        key: '',
        value: '',
        id: '',
        useStatus: ''

      },
      formItemRule: {
        hostName: [{ required: true, message: '不能为空', trigger: 'blur' }, { max: 20, message: '不能多于20位', trigger: 'blur' }],
        hostIp: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            type: 'string',
            pattern: /^[_.0-9]{1,20}$/,
            message: '格式有误,长度为20位,内容为.和数字',
            trigger: 'blur'
          }
        ],
        key: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            type: 'string',
            pattern: /^[a-zA-Z0-9]{1,10}$/,
            message: '格式有误,长度为10位,内容为字母和数字',
            trigger: 'blur'
          },
          { max: 10, message: '不能多于10位', trigger: 'blur' }
        ],
        value: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            type: 'string',
            pattern: /^[a-zA-Z0-9]{1,10}$/,
            message: '格式有误,长度为10位,内容为字母和数字',
            trigger: 'blur'
          }
        ]

      }
    }
  },
  mounted () {
    this.getmanagementList()
  },

  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    cancel () { },

    // 查询列表接口
    getmanagementList () {
      let params = {
        name: this.search_value,
        // ip: this.search_value,
        pageParam: this.tablePageParam // 分页
      }
      findCmeosNodeLableList(params).then((res) => {
        console.log(res)
        if (res.code === '00000') {
          let list = res.data.records.map((item) => {
            return {
              ...item,
              useStatus: item.useStatus === '0' ? '未被使用' : '被使用',
              node: item.nodeSelectKey + ' ' + '：' + item.nodeSelectValue
            }
          })
          console.log(list)
          this.historyData = list
          this.tablePageParam = {
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          this.dataCount = res.data.total
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 打开新增配置弹窗
    addhost () {
      this.hostmodal = true
      this.formItem.alertTitle = '添加主机'
      this.$refs['formItem'].resetFields()
    },
    cancelhost () {
      this.hostmodal = false
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.formItem.value = ''
    },
    // 新增/编辑提交
    ok (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let additem = {
            name: this.formItem.hostName, // 主机名称
            ip: this.formItem.hostIp, // 主机IP
            delStatus: '0',
            useStatus: '0',
            nodeSelectKey: this.formItem.key,
            nodeSelectValue: this.formItem.value
          }
          if (this.formItem.alertTitle === '添加主机') {
            addCmeosNodelable(additem).then((res) => {
              if (res.code === '00000') {
                this.hostmodal = false
                this.msgInfo('success', '添加主机成功', true)
                this.getmanagementList()
              } else {
                this.msgInfo('error', res.message, true)
              }
            }).catch((error) => {
              this.msgInfo('error', error.message, true)
            })
          } else {
            let edititem = {
              name: this.formItem.hostName, // 主机名称
              ip: this.formItem.hostIp, // 主机IP
              delStatus: '0',
              useStatus: '0',
              nodeSelectKey: this.formItem.key,
              nodeSelectValue: this.formItem.value,
              id: this.formItem.id
            }
            editCmeosNodelable(edititem).then((res) => {
              if (res.code === '00000') {
                this.hostmodal = false
                this.msgInfo('success', '修改主机成功', true)
                this.getmanagementList()
              } else {
                this.msgInfo('error', res.message, true)
              }
            }).catch((error) => {
              this.msgInfo('error', error.message, true)
            })
          }
        } else {
          this.msgInfo('error', '存在字段校验不符合规范，请检查！', true)
        }
      })
    },
    // 查询详情
    detailhost (row) {
      this.detailmodal = true
      findCmeosNodelableById(row.id).then((res) => {
        if (res.code === '00000') {
          this.detailinfo = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    detailok () { this.detailmodal = false },
    detailcancel () { this.detailmodal = false },
    // 编辑
    edithost (row) {
      this.hostmodal = true
      findCmeosNodelableById(row.id).then((res) => {
        if (res.code === '00000') {
          this.formItem = {
            alertTitle: '编辑主机',
            hostName: res.data.name,
            hostIp: res.data.ip,
            key: res.data.nodeSelectKey,
            value: res.data.nodeSelectValue,
            id: res.data.id,
            useStatus: res.data.useStatus
          }
          this.editinfo = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
      // this.formItem = {
      //   alertTitle: '编辑主机',
      //   hostName: row.name,
      //   hostIp: row.ip,
      //   key: row.nodeSelectKey,
      //   value: row.nodeSelectValue,
      //   id: row.id,
      //   useStatus: row.useStatus
      // }
    },
    // 删除
    deletehost (row) {
      let deletedata = {
        id: row.id,
        name: row.name,
        ip: row.ip,
        delStatus: '1',
        useStatus: '0',
        nodeSelectKey: row.nodeSelectKey,
        nodeSelectValue: row.nodeSelectValue
      }
      deleteById(deletedata).then((res) => {
        if (res.code === '00000') {
          this.msgInfo('success', '删除成功', true)
          this.getmanagementList()
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 翻页
    changepage (index) {
      this.tablePageParam.pageIndex = index // 当前页
      this.getmanagementList()
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      // 当前展示条数
      this.tablePageParam.pageSize = size
      this.getmanagementList()
    },
    // 重置按钮事件
    reset () {
      this.search_value = ''
      this.status = ''
      this.tablePageParam = { pageIndex: 1, pageSize: 10 }
      this.getmanagementList()
    }
  },
  watch: {
  }

}
</script>

<style lang="less" scoped>
.newnode {
  /deep/.ivu-form-item-error-tip {
    width: 135px;
  }
}
/deep/.ivu-btn-default:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-form-item-content {
  display: flex;
}
.detail_info {
  p {
    padding: 2%;
  }
}
.sloatbtn {
  text-align: center;
}
.cz_header {
  // display: flex;
  margin-top: 10px;
  /deep/ .ivu-select,
  /deep/ .ivu-date-picker {
    width: 15%;
    margin-right: 10px;
  }
}

//
/deep/ .ivu-modal {
  width: 700px;
}
// table
.cz_table {
  margin-top: 2% !important;
}
//

.ivu-btn-primary {
  margin-left: 7px;
}
</style>
