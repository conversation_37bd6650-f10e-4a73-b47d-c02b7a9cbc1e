<template>
  <div>
    <div class="contract_header">
      <!-- 搜索 -->
      <div class="cz_ss">
        <Input style="width: 127px; vertical-align: baseline; " placeholder="可输入合约包名称或合约名称查询" v-model="inputvalue" @keyup.enter="searchList" @keyup.enter.native="searchList" />
        <!-- <Input placeholder="请输入用户名称" v-model="search_value" @on-enter="uservalue" /> -->
      </div>
      <!--  更新时间-->
      <div>
        <Row>
          <div class="sl_timout">更新时间</div>
          <Col span="14">
          <DatePicker format="yyyy-MM-dd" type="daterange" :transfer='true' placement="bottom-end" :value="daterange" v-model="daterange" :editable='false' :clearable='false' placeholder="开始日期~结束日期" @on-change="timeout_click"></DatePicker>
          </Col>
        </Row>
      </div>
      <!-- 排序方式 -->
      <div style="margin-left:-30px">
        <Row>
          <div class="sl_timout">排序方式</div>
          <Col span="10">
          <Select v-model="security_level" style="width: 80px" placeholder="下载量">
            <Option v-for="item in SecurityList" :value="item.value" :key="item.value" @click.native="get_Type(item)">{{ item.label }}</Option>
          </Select>
          </Col>
          <!-- <Button type="primary" icon="ios-search" @click.native="information">查询</Button> -->
        </Row>
      </div>
      <!-- 链类型查询 -->
      <div style="margin-left:8px">
        <Row>
          <div class="sl_timout">链类型</div>
          <Col span="10">
          <Select v-model="chainType" style="width: 80px" placeholder="链类型" @on-change="changechaincity">
            <Option v-for="item in ChainList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          </Col>
          <!-- <Button type="primary" icon="ios-search" @click.native="information">查询</Button> -->
        </Row>
      </div>
      <!-- 筛选方式 -->
      <div :style="`display:${this.shjlsfzh===true ? 'inline-block' : 'none'};`">
        <Row>
          <div class="sl_timout">筛选方式</div>
          <Col span="10">
          <Select v-model="screen_ing" style="width: 65px" placeholder="全部" @on-clear="clearValue" :clearable="true">
            <Option v-for="item in ScreeningList" :value="item.value" :key="item.value" @click.native="get_Type(item)">{{ item.label }}</Option>
          </Select>
          </Col>
          <!-- <Button type="primary" icon="ios-search" @click.native="information">查询</Button> -->
        </Row>
      </div>
      <!-- 合约语言 -->
      <div style="margin-left:8px">
        <Row>
          <div class="sl_timout">合约语言</div>
          <Col span="10">
          <Select v-model="language_value" style="width: 90px" placeholder="合约语言">
            <Option v-for="item in languageList" :value="item.enumKey" :key="item.enumKey">{{ item.enumValue}}</Option>
          </Select>
          </Col>
          <!-- <Button type="primary" icon="ios-search" @click.native="information">查询</Button> -->
        </Row>
      </div>
      <!--查询  -->
      <div class="header_serch">
        <div class="serch_btn">
          <Button type="primary" @click.native="information" icon="ios-search">查询</Button>
          <Button type="primary" ghost @click.native="reset" icon="md-sync">重置</Button>
        </div>
        <div class="header_icon">
          <!-- <Tooltip max-width="200" content="合约调用量数值计算方式为：内部人员调用+合约共享" placement="bottom">
            <Icon type="ios-help" size="28" />
          </Tooltip> -->
          <div v-if="showimg" @click="sorting"><img src="../../../assets/img/shang.png" alt=""></div>
          <div v-show="!showimg" @click="sorting"><img src="../../../assets/img/xia.png" alt=""></div>
        </div>

      </div>
    </div>
    <!-- 表格 -->
    <div style="text-align: center;padding:10px;border-bottom: 1px solid #e8eaec;">{{this.nodata?'暂无数据':''}}</div>
    <div class="shelves_table">
      <!-- 高亮 -->
      <div class="highlighted">
        <div class="highlighted_one" v-for="(item,index) in tablehighlighted" :key="index" :style="`background:${item.status===1 ? '#C4C5C7' : 'none'}`">
          <div class="left">
            <div style="margin-top:17px">
              <p>
                <img :src="item.imgSrc.icon" alt="" />
              </p>
              <h5>{{item.contractBagName}}</h5>
            </div>
          </div>
          <div class="right">
            <p style="margin-top:20px">合约调用量: <span>{{item.contractUseCount}}</span> </p>
            <p>下载量: <span>{{item.downLoadCount}}</span> </p>
            <p>链类型:<span>{{item.chainType}}</span> </p>
            <p>合约语言:<span>{{item.languageType==='JS'?'Java Script':item.languageType}}</span> </p>
            <p>更新时间:<span>{{item.updateTime}}</span> </p>
            <p class="btn">
              <Button type="text" @click="checkDetail(item)">查看</Button>
              <Button type="text" @click="down(item)">下载</Button>
              <Poptip confirm title="确定要下架吗？" @on-ok="changeShow(item)">
                <Button v-if="shjlsfzh" type="text" v-show='item.status===2'>下架</Button>
              </Poptip>
              <Poptip confirm title="确定要恢复吗？" @on-ok="changeRecover(item)">
                <Button v-if="shjlsfzh" type="text" v-show='item.status===1'>恢复</Button>
              </Poptip>
            </p>
          </div>
        </div>
      </div>
      <!-- 正常 -->
      <div class="normal_table">
        <div class="normal" v-for="(item,index) in tableList" :key='index' :style="`background:${item.status===1 ? '#C4C5C7' : '#F6F7F9'}`">
          <Tooltip max-width="200" :content="item.contractBagName" placement="bottom">
            <h5>{{item.contractBagName}}</h5>
          </Tooltip>
          <p>合约调用量 : <span>{{item.contractUseCount}}</span> </p>
          <p>下载量 : <span>{{item.downLoadCount}}</span> </p>
          <p>链类型:<span>{{item.chainType}}</span> </p>
          <p>合约语言:<span>{{item.languageType==='JS'?'Java Script':item.languageType}}</span> </p>
          <p>更新时间 : <span>{{item.updateTime}}</span> </p>
          <p class="btn">
            <Button type="text" @click="checkDetail(item)">查看</Button>
            <Button type="text" @click="down(item)">下载</Button>
            <Poptip confirm title="确定要下架吗？" @on-ok="changeShow(item)">
              <Button v-if="shjlsfzh" type="text" v-show='item.status===2'>下架</Button>
            </Poptip>
            <Poptip confirm title="确定要恢复吗？" @on-ok="changeRecover(item)">
              <Button v-if="shjlsfzh" type="text" v-show='item.status===1'>恢复</Button>
            </Poptip>
          </p>
        </div>
      </div>
      <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" placement='top' @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px" />
    </div>

  </div>
</template>
<script>
import { getContractMarketList, getChangeShow, getDownDe } from '@/api/data'
// let imgArr =
// 使用全局变量存储窗口引用，避免Vue响应式系统处理窗口对象
let globalTargetWin = null;
export default {
  data () {
    return {
      language_value: '',
      languageList: [],
      nodata: false,
      showimg: 'true',
      screen_ing: '', // 筛选方式
      shjlsfzh: '',
      imgArr: [ // 图片
        { id: 1, icon: require('../../../assets/img/paihang1.png') },
        { id: 2, icon: require('../../../assets/img/paihang2.png') },
        { id: 3, icon: require('../../../assets/img/paihang3.png') }
      ],
      inputvalue: '', // 搜索
      daterange: [], // 日期
      security_level: 'DOWNLOADCOUNTDESC', // 排序方式
      chainType: '',
      name: '',
      security_arr: {
        updateTime: '',
        downLoadCount: 'DOWNLOADCOUNTDESC',
        contractUseCount: ''
      },
      // 排序下拉框
      SecurityList: [
        {
          value: 'CONTRACTUSECONUTDESC',
          label: '合约调用量'
        },
        {
          value: 'DOWNLOADCOUNTDESC',
          label: '下载量'
        },
        {
          value: 'UPDATETIMEDESC',
          label: '更新时间'
        }
      ],
      ChainList: [
        {
          value: 'EOS',
          label: 'EOS'
        },
        {
          value: 'BOS',
          label: 'BOS'
        },
        {
          value: 'CMEOS',
          label: 'CMEOS'
        },
        {
          value: 'ChainMaker',
          label: 'ChainMaker'
        }
      ],
      // 筛选下拉框
      ScreeningList: [
        {
          value: '2',
          label: '上架'
        },
        {
          value: '1',
          label: '下架'
        },
        {
          value: '',
          label: '全部'
        }
      ],
      //
      screening: {
        all: '',
        up_applyfor: 'DOWNLOADCOUNTDESC',
        down_applyfor: ''

      },
      // 高亮表格
      tablehighlighted: [],
      // 常规表格
      tableList: [],
      // 分页
      tablePageParam: {
        pagetotal: 0,
        pageIndex: 1,
        pageSize: 5
      },
      // 开始时间和结束时间
      startTime: '',
      endTime: '',
      targetWin: '',
      funcName: '',
      bg: false,
      jin: false,
      downid: '',
      downcontractBagName: ''
    }
  },
  methods: {
    changechaincity (value) {
      this.language_value = ''
      if (value === 'EOS' || value === 'BOS') {
        this.languageList = [
          { enumKey: 'C++', enumValue: 'C++' }
        ]
      } else if (value === 'CMEOS') {
        this.languageList = [
          { enumKey: 'C++', enumValue: 'C++' },
          { enumKey: 'JS', enumValue: 'Java Script' }
        ]
      } else if (value === 'ChainMaker') {
        // GO、C++、rust、tinygo、solidity
        this.languageList = [
          { enumKey: 'GO', enumValue: 'GO' },
          { enumKey: 'C++', enumValue: 'C++' },
          { enumKey: 'RUST', enumValue: 'RUST' },
          { enumKey: 'TINYGO', enumValue: 'TINYGO' },
          { enumKey: 'SOLIDITY', enumValue: 'SOLIDITY' }
        ]
      } else {
        this.languageList = []
      }
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, top: 300 }) },
    showBank (func) {
      var iWidth = 700; //模态窗口宽度
      var iHeight = 450;//模态窗口高度
      var iTop = (window.screen.height - iHeight - 100) / 2;
      var iLeft = (window.screen.width - iWidth) / 2;
      var winOption = 'height=' + iHeight + ',innerHeight=' + iHeight + ',width=' + iWidth + ',innerWidth=' + iWidth + ',top=' + iTop + ',left=' + iLeft + ',toolbar=no,menubar=no,scrollbars=no,resizeable=no,location=no,status=no';
      var obj = new Object();
      obj.operCode = localStorage.getItem("sourceCodeEncrypt");
      obj.mainLoginName = "";
      obj.subLoginName = localStorage.getItem("userNameEncrypt")
      obj.appCode = "JTNGCMBAAS";
      obj.sessionId = sessionStorage.getItem("session");
      obj.serverIp = window.location.host.split(":")[0]
      obj.serverPort = window.location.port;
      obj.checkSessionUrl = "/";
      obj.svcNum = "";
      obj.operContent = "";
      // var returnValue;
      //增加浏览器的判断，ie走if原有逻辑，非ie走else逻辑,通过遮罩层实现。
      var a1 = navigator.userAgent;
      var yesIE = a1.search(/Trident/i);
      if (window.ActiveXObject || window.attachEvent || yesIE > 0) { //IE
        // var returnValue = window.showModalDialog("b.html?id=" + new Date(), obj, "dialogHeight:" + iHeight + "px; dialogWidth:" + iWidth + "px; toolbar:no; menubar:no;  titlebar:no; scrollbars:yes; resizable:no; location:no; status:no;left:" + iLeft + "px;top:" + iTop + "px;");
        var me = new Object();
        // me.data = returnValue;
        this.receiveMsg(me);
      } else {  //非IE
        // this.showDiv();//显示遮罩层
        this.openWindowWithPostRequest(iWidth, iHeight, iTop, iLeft, winOption, obj);
        if (window.addEventListener) {
          //为window注册message事件并绑定监听函数
          window.addEventListener('message', this.receiveMsg, false);
        } else {
          window.attachEvent('message', this.receiveMsg);
        }
      }
    },

    openWindowWithPostRequest (iWidth, iHeight, iTop, iLeft, winOption, obj) {
      var winName = "sWindow";
      var winURL = "http://api.it4a.cmit.cmcc:7081/uac/web3/jsp/goldbank/goldbank3!goldBankIframeAction.action";//应用侧对应后台服务action
      var form = document.createElement("form");
      form.setAttribute("method", "post");
      form.setAttribute("action", winURL);
      form.setAttribute("target", winName);
      for (var i in obj) {
        if (obj.hasOwnProperty(i)) {
          var input = document.createElement('input');
          input.type = 'hidden';
          input.name = i;
          input.value = obj[i];
          form.appendChild(input);
        }
      }
      document.body.appendChild(form);
      //打开地址，刚开始时，打开一个不存在的地址，这样才有返回值
      globalTargetWin = window.open("", winName, winOption);
      form.target = winName;
      form.submit();
      document.body.removeChild(form);
      if (window.focus) {
        globalTargetWin.focus();
      }
    },

    //接收返回值后处理函数
    receiveMsg (e) {
      // returnValue = e.data;
      console.log(e, 'eeeeeeeeeeeeeee');
      if (e.data && typeof e.data === 'string') {
        var dataStatus = e.data.split("#")
        const statusMessageMap = {
          '-3': { message: '金库应急开启中，允许业务继续访问', allowAccess: true },
          '-2': { message: '金库场景或元业务未开启，允许业务继续访问', allowAccess: true },
          '-1': { message: '直接关闭窗口，未申请审批，不允许业务继续访问', allowAccess: false },
          '1': { message: '审批通过，允许业务继续访问', allowAccess: true },
          '0': { message: '审批不通过，不允许业务继续访问', allowAccess: false },
          '2': { message: '超时，允许业务继续访问', allowAccess: true },
          '3': { message: '超时，不允许业务继续访问', allowAccess: false },
          '4': { message: '出现错误或异常（包括数据异常），不允许业务继续访问', allowAccess: false },
          '5': { message: '未配置策略，允许业务继续访问', allowAccess: true },
          '6': { message: '未配置策略，不允许继续访问', allowAccess: false }
        };

        const status = dataStatus[0];
        const statusInfo = statusMessageMap[status];
        console.log(statusInfo, 'statusInfo');
        if (statusInfo) {
          if (statusInfo.allowAccess) {
            // 允许业务继续访问，继续请求接口
            // 这里调用你继续请求接口的函数
            getDownDe(this.downid, '', this.downcontractBagName, e.data).then(res => {
              let reader = new FileReader();
              reader.readAsText(res);
              reader.onload = () => {
                try {
                  let jsonData = JSON.parse(reader.result);
                  if (jsonData.code === '00000') {
                    // this.showBank()
                    this.msgInfo('warning', jsonData.message, true)
                  } else {
                    this.msgInfo('error', jsonData.message, true)
                  }
                } catch (e) {
                  let blob = new Blob([res], { type: 'application/zip' })
                  let downloadElement = document.createElement('a')
                  let href = window.URL.createObjectURL(blob)
                  downloadElement.href = href
                  downloadElement.download = item.contractBagName
                  document.body.appendChild(downloadElement)
                  downloadElement.click()
                  document.body.removeChild(downloadElement)
                  window.URL.revokeObjectURL(href)
                  this.getContractMarket()

                }

              };
            }).catch(error => {
              console.log(error);
              this.msgInfo('error', error.message, true)
            })
          } else {
            // 不允许业务继续访问，弹出提示框
            this.msgInfo('warning', statusInfo.message, true);
            // this.closeDiv();
          }
        }
      } else {
        console.error('e.data 不是字符串类型或为空', e.data);
        // this.msgInfo('error', '金库返回数据格式错误', true);
      }

      // alert("returnValue1111111===" + e.data);

      /**
         *
         *在这里处理业务，执行回调函数
         */
      // console.log(returnValue);
      // if (returnValue != 'undefined' && returnValue != '') {
      //   eval(funcName);
      // }
      if (globalTargetWin != null) {
        globalTargetWin.close();
        // this.closeDiv();//关闭遮罩层
      }
    },
    // //回调测试函数
    test1 () {
      alert("test1");
    },
    // 下载
    down (item, treasuryToken = '') {
      this.downid = item.id
      this.downcontractBagName = item.contractBagName
      getDownDe(item.id, '', item.contractBagName, treasuryToken).then(res => {
        console.log(res);

        let reader = new FileReader();
        reader.readAsText(res);
        reader.onload = () => {
          try {
            let jsonData = JSON.parse(reader.result);
            console.log(jsonData);
            if (jsonData.code === 'A0314') {
              // this.receiveMsg('3#*************')
              this.showBank()
            } else {
              this.msgInfo('error', jsonData.message, true)

            }
          } catch (e) {
            console.log(e);
            if (e != 'SecurityError: An attempt was made to break through the security policy of the user agent.') {
              let blob = new Blob([res], { type: 'application/zip' })
              let downloadElement = document.createElement('a')
              let href = window.URL.createObjectURL(blob)
              downloadElement.href = href
              downloadElement.download = item.contractBagName
              document.body.appendChild(downloadElement)
              downloadElement.click()
              document.body.removeChild(downloadElement)
              window.URL.revokeObjectURL(href)
              this.getContractMarket()
            }
          }

        };
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 下架
    changeShow (item) {
      // console.log(item)
      getChangeShow(item.id, 1).then(res => {
        if (res.code === '00000') {
          this.getContractMarket()
        } else {
          this.$Message.error(res.message)
        }
      }).catch(errpr => {
        this.$Message.error(errpr.message)
      })
      // this.getContractMarket()
    },
    // 恢复
    changeRecover (item) {
      getChangeShow(item.id, 2).then(res => {
        if (res.code === '00000') {
          this.getContractMarket()
        } else {
          this.$Message.error(res.message)
        }
      }).catch(errpr => {
        this.$Message.error(errpr.message)
      })
    },
    searchList () {
    },
    // 查看跳转
    checkDetail (data) {
      this.$router.push({
        name: 'shelves_detail',
        params: {
          contractId: data.id,
          languageType: data.languageType
        }
      })
    },
    // 调用量时间

    timeout_click (time1) {
      this.startTime = time1[0] + ' ' + '00:00:00'
      this.endTime = time1[1] + ' ' + '23:59:59'
    },
    get_Type (item) {
      switch (item.label) {
        case '合约调用量':
          this.security_arr.updateTime = ''
          this.security_arr.downLoadCount = ''
          this.security_arr.contractUseCount = item.value
          break
        case '下载量':
          this.security_arr.updateTime = ''
          this.security_arr.downLoadCount = item.value
          this.security_arr.contractUseCount = ''
          break
        case '更新时间':
          this.security_arr.updateTime = item.value
          this.security_arr.downLoadCount = ''
          this.security_arr.contractUseCount = ''
          break
        default:
          break
      }

      // this.security_level = value
    },
    // 待审批分页事件
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getContractMarket()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getContractMarket()
    },
    // 排序
    sorting () {
      if (this.security_arr.contractUseCount === 'CONTRACTUSECONUTDESC') {
        this.security_arr.contractUseCount = 'CONTRACTUSECONUTASC'

        this.getContractMarket()
      } else if (this.security_arr.contractUseCount === 'CONTRACTUSECONUTASC') {
        this.security_arr.contractUseCount = 'CONTRACTUSECONUTDESC'
        this.getContractMarket()
      } else if (this.security_arr.downLoadCount === 'DOWNLOADCOUNTDESC') {
        this.security_arr.downLoadCount = 'DOWNLOADCOUNTASC'

        this.getContractMarket()
      } else if (this.security_arr.downLoadCount === 'DOWNLOADCOUNTASC') {
        this.security_arr.downLoadCount = 'DOWNLOADCOUNTDESC'

        this.getContractMarket()
      } else if (this.security_arr.updateTime === 'UPDATETIMEDESC') {
        this.security_arr.updateTime = 'UPDATETIMEASC'

        this.getContractMarket()
      } else if (this.security_arr.updateTime === 'UPDATETIMEASC') {
        this.security_arr.updateTime = 'UPDATETIMEDESC'

        this.getContractMarket()
      }
      this.showimg = !this.showimg
    },
    // 查询
    information () {
      // this.tablePageParam.pageIndex = 1
      // this.tablePageParam.pageSize = 8
      this.getContractMarket()
    },
    clearValue () {
      this.screen_ing = ''
    },
    // 重置
    reset () {
      this.nodata = false
      this.inputvalue = ''
      this.chainType = ''
      this.clearValue()
      this.security_level = 'DOWNLOADCOUNTDESC'
      this.security_arr.downLoadCount = 'DOWNLOADCOUNTDESC'
      this.security_arr.updateTime = ''
      this.security_arr.contractUseCount = ''
      this.language_value = ''
      // 获取当前时间
      let myDate = new Date()
      let year = myDate.getFullYear() // 获取当前年份
      let month = myDate.getMonth() + 1 // 获取当前月份(0-11,0代表1月所以要加1);
      // let day = myDate.getDate() + 1 // 获取当前日（1-31）
      let day1 = myDate.getDate() // 获取当前日（1-31）
      let sday = myDate.getDate() // 获取当前日（1-31）
      let minutes = myDate.getMinutes()
      if (minutes >= 0 && minutes <= 9) {
        minutes = '0' + minutes
      }
      // 获取一年前时间
      var dates = new Date(myDate)
      dates.setDate(dates.getDate() - 365)
      var yearn = dates.getFullYear()
      // this.endTime = `${year}-${month}-${day} `
      this.endTime = `${year}-${month}-${day1} 23:59:59`
      this.startTime = `${yearn}-${month}-${sday} 00:00:00`
      this.daterange = [this.startTime, this.endTime]
      this.tablePageParam = { pageSize: 5, pageIndex: 1 }
      this.getContractMarket()
      this.showimg = 'true'
    },
    // 请求方法
    // 请求方法
    getContractMarket () {
      if (this.startTime === '' && this.endTime === '') {
        this.msgInfo('error', '上架时间不能为空', true)
      } else {
        let SeachList = {
          contractBagName: this.inputvalue,
          pageParam: this.tablePageParam,
          startTime: this.startTime,
          endTime: this.endTime,
          updateTime: this.security_arr.updateTime,
          downLoadCount: this.security_arr.downLoadCount,
          contractUseCount: this.security_arr.contractUseCount,
          status: this.screen_ing,
          chainType: this.chainType,
          languageType: this.language_value// 合约语言
        }
        getContractMarketList(SeachList).then(res => {
          if (res.code === '00000') {
            let shjl = res.data.records.pop()
            this.shjlsfzh = shjl.isManager
            // let shj2 = shjl.newList.pop()
            if (shjl.newList) {
              this.tablehighlighted = shjl.newList
              this.tablehighlighted.map((item, index) => {
                item.imgSrc = this.imgArr[index]
              })
              this.tableList = res.data.records
              this.tablePageParam.pagetotal = res.data.total
              this.nodata = false
              // this.inputvalue = ''
            } else {
              this.tablehighlighted = []
              this.tableList = []
              this.nodata = true
              // this.msgInfo('info', '暂无数据', true)
            }
          } else {
            this.$Message.error(res.message)
          }
        }).catch(error => {
          this.$Message.error(error.message)
        })
      }
    }

  },
  created () {
    // 获取当前时间
    let myDate = new Date()
    let year = myDate.getFullYear() // 获取当前年份
    let month = myDate.getMonth() + 1 // 获取当前月份(0-11,0代表1月所以要加1);
    // let day = myDate.getDate() + 1 // 获取当前日（1-31）
    let day1 = myDate.getDate() // 获取当前日（1-31）
    let sday = myDate.getDate() // 获取当前日（1-31）
    let minutes = myDate.getMinutes()
    if (minutes >= 0 && minutes <= 9) {
      minutes = '0' + minutes
    }
    // 获取一年前时间
    var dates = new Date(myDate)
    dates.setDate(dates.getDate() - 365)
    var yearn = dates.getFullYear()
    // this.endTime = `${year}-${month}-${day} `
    this.endTime = `${year}-${month}-${day1} 23:59:59`
    this.startTime = `${yearn}-${month}-${sday} 00:00:00`
    this.daterange[0] = this.startTime
    this.daterange[1] = this.endTime
    this.getContractMarket()
  },
  beforeDestroy () {
    window.removeEventListener('message', this.receiveMsg, false);

    // 确保关闭全局窗口引用
    if (globalTargetWin) {
      try {
        globalTargetWin.close();
      } catch (e) {
        console.error('关闭窗口失败', e);
      }
      globalTargetWin = null;
    }

    clearInterval(this.timer);
    this.timer = null;
  },
  activated () {
    // console.log(this.imgArr[0])
    this.getContractMarket()
  }
}
</script>
<style lang="less" scoped>
/deep/.ivu-btn-ghost {
  margin-left: 2% !important;
}
.ivu-row {
  // margin-right: 5%;
  display: flex;
  flex-flow: row wrap;
}
// .ivu-card-body {
//   padding: 20px;

//   .ivu-select-single .ivu-select-selection {
//     background: red;
//   }
// }
// 搜索
.contract_header {
  display: flex;
  margin-top: 10px;
  width: 100%;
  .header_serch {
    margin-left: 1%;
    display: flex;
    // width: 390px;
    // border:1px solid red;
    .serch_btn {
      display: flex;
    }
    .header_icon {
      cursor: pointer;
      margin-left: 6px;
      // display: flex;
      // margin-right: 40px;
      // .icon_style:hover{
      // content: 'fgfhghjgj';
      // }
      // div:nth-child(1) {
      //   width: 30px;
      //   height: 30px;
      //   border: 1px solid #606266;
      //   border-radius: 50%;
      //   margin-left: 15px;
      //   margin-right: 10px;
      // }
      // div:nth-child(1) {
      //   position: relative;
      //   width: 30px;
      //   height: 30px;
      //   //  border: 1px solid #606266;
      //   border-radius: 50%;
      //   cursor: pointer;
      //   img {
      //     position: absolute;
      //     left: 8px;
      //     // top: 4px;
      //   }
      // }
    }
  }
  .sl_timout {
    height: 33px;
    padding: 5px 8px;
    text-align: center;
    border-radius: 4px;
  }
}
.ivu-btn-primary {
  width: auto;
  margin-right: 3px;
}
.ivu-col-span-12 {
  display: block;
  flex: 0 0 41%;
  max-width: 52%;
}
// 整体表格
.shelves_table {
  // width: 90%;
  margin-top: 2%;
  //  border: 1px solid blue;
  //  高亮
  .highlighted {
    display: flex;
    .highlighted_one {
      .left {
        width: 40%;
        float: left;
        height: 190px;
        // border: 1px solid blue;
        h5 {
          text-align: center;
          font-weight: bold;
          // overflow: hidden;
          // overflow: hidden;
          // border: 1px solid red;
          // height: 29px;
          font-size: 1.2vw;
          width: 10vw;
          white-space: normal;
          word-break: break-all;
          word-wrap: break-word;
          margin-top: 0.5vw;
          // overflow: hidden;
          // text-overflow: ellipsis;
          // white-space: wrap;
        }
        p:nth-child(1) {
          position: relative;
          text-align: center;
          // font-weight: bold;
          width: 100px;
          height: 55px;
          img {
            position: absolute;
            top: 30%;
            left: 4%;
          }
          // line-height: 160px;
        }
      }
      .right {
        font-size: 14px;
        width: 60%;
        height: 190px;
        float: right;
        // border: 1px solid red;
        p {
          margin-top: 5px;
          margin-left: 15px;
          // text-align:center;
        }
        //
      }
    }
    .highlighted_one:nth-child(1) {
      width: 30%;
      height: 190px;
      border-radius: 10px;
      border: 1px solid #ffe900;
    }
    .highlighted_one:nth-child(2) {
      margin: 0 3%;
      width: 30%;
      height: 190px;
      border-radius: 10px;
      border: 1px solid #b6c3ce;
    }
    .highlighted_one:nth-child(3) {
      width: 30%;
      height: 190px;
      border-radius: 10px;
      border: 1px solid #e9c8b0;
    }
    .btn {
      display: flex;
      justify-content: flex-end;
      box-sizing: border-box;
      padding-right: 5%;
      margin-top: 0 !important;
      .ivu-btn-text {
        margin-right: 3%;
        font-size: 15px !important;
        padding: 0 !important;
        border: none !important;
        box-shadow: none !important;
        color: #2e8cf0;
        background: none;
        // span{
        //   margin-bottom: 2px;
        // }
      }
      //   span{
      //     cursor:pointer;
      // display: inline-block;
      // width: 50px;
      //     // height: 50px;
      //     color: #2E8CF0;
      //   }
    }
  }
  // 下方表格
  .normal_table {
    display: flex;
    width: 100%;
    .normal {
      width: 19%;
      margin: 1% 1% 1% 0;
      background-image: url("../../../assets/img/beijing.png") !important ;
      background-size: 101% 100% !important;
      // background:url('../../../assets/img/beijing.png')no-repeat 4px 5px !important;
      box-sizing: border-box;
      height: 208px;
      border-radius: 10px;
      border: 1px solid #2e8cf0;
      font-size: 13px;
      text-align: center;
      h5 {
        font-size: 1.2vw;
        white-space: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 10vw;
        text-align: center;
        //font-size: 17px;
        // text-overflow: ellipsis;
        // width: 10vw;
        // white-space: nowrap;
        // word-break: break-all;
        //   word-wrap: break-word;
      }
      p {
        text-align: left;
        margin-bottom: 2%;
        // margin-top: 2%;
        margin-left: 10px;
        span {
          margin-left: 5px;
        }
      }
    }
    .btn {
      display: flex;
      justify-content: flex-end;
      box-sizing: border-box;
      padding-right: 5%;
      margin-top: 0 !important;
      .ivu-btn-text {
        margin-right: 3%;
        margin-top: 0;
        font-size: 15px !important;
        padding: 0 !important;
        border: none !important;
        box-shadow: none !important;
        color: #2e8cf0;
        background: none;
        // span{
        //   margin-bottom: 2px;
        // }
      }
      //   span{
      //     cursor:pointer;
      // display: inline-block;
      // width: 50px;
      //     // height: 50px;
      //     color: #2E8CF0;
      //   }
    }
  }
  /deep/.ivu-tabs {
    overflow: visible !important;
    // overflow: initial !important;
    // overflow: inherit !important;
  }
  //
}
</style>
