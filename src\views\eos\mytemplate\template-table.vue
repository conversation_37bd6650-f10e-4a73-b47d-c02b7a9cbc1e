<template>
  <div>
    <div class="cz_header">
      <div class="cz_search">
        <Row>
          <div class="sl_timout">模板类型</div>
          <Col span="12">
          <Input style="width: 180px; vertical-align: baseline; " placeholder="可输入模板类型名称查询" v-model="inputvalue" @keyup.enter="searchList" @keyup.enter.native="searchList">
          <!-- <Icon type="ios-search" slot="suffix" @click="searchList" /> -->
          </Input>
          </Col>
          <!-- <Button type="primary" icon="ios-search" @click.native="information">查询</Button> -->
        </Row>
        <div style="margin-left:8px">
          <Row>
            <div class="sl_timout">链类型</div>
            <Col span="12">
            <Select v-model="chainType" style="width: 104px" placeholder="链类型" @on-change="changechaincity">
              <Option v-for="item in ChainList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
            </Col>
            <!-- <Button type="primary" icon="ios-search" @click.native="information">查询</Button> -->
          </Row>
        </div>
        <div style="margin-left:8px">
          <Row>
            <div class="sl_timout">合约语言</div>
            <Col span="12">
            <Select v-model="contractLanguage" style="width: 104px" placeholder="合约语言">
              <Option v-for="item in languageList" :value="item.enumKey" :key="item.enumKey">{{item.enumValue}}</Option>
            </Select>
            </Col>
            <!-- <Button type="primary" icon="ios-search" @click.native="information">查询</Button> -->
          </Row>
        </div>
        <div>
          <Button type="primary" @click.native="searchList" class="serch_btn" icon="ios-search">查询</Button>
          <Button type="primary" ghost @click.native="reset" icon="md-sync">重置</Button>
        </div>
      </div>
      <!-- <Button type="success" ghost icon="md-add" @click="modal1 = true"
        >新建合约类型</Button
      > -->
      <Button type="success" ghost icon="md-add" @click="newType" :disabled="hasEditPermission">新建模板类型</Button>
    </div>
    <!-- table -->
    <div class="cz_table">
      <edit-table-mul :columns="contractTable" v-model="contractTableData" :key="transferKey"></edit-table-mul>
      <!-- <Table :columns="columns7" :data="data6"></Table> -->
      <Page :total="PageParam.pagetotal" :page-size="PageParam.pageSize" :current.sync="PageParam.pageIndex" :page-size-opts="[10, 20, 40, 60, 100]" show-sizer show-total show-elevator class="paging" @on-change="changepage" style="text-align: right" @on-page-size-change="pageSizeChange"></Page>
    </div>
    <!-- 删除弹框 -->
    <Modal v-model="modal1" @on-ok="ok" @on-cancel="cancel">
      <p style="text-align: center;height:30px;margin-top:20px; line-height: 30px;">请确认是否删除</p>
    </Modal>
  </div>
</template>
<script>
import EditTableMul from '_c/edit-table-mul'
import { ContractTemplate, ContractTemplateDelete } from '@/api/data'
import { localRead } from '@/lib/util'
export default {
  components: {
    EditTableMul
  },
  data () {
    return {
      languageList: [],
      contractLanguage: '', // 合约语言
      chainType: '',
      modal1: false, // 弹框
      inputvalue: '', // 搜索框
      transferKey: 0,
      transferKey1: 0,

      // 分页
      PageParam: {
        pageSize: 10,
        pageIndex: 1,
        pagetotal: 0
      },
      ChainList: [
        {
          value: 'EOS',
          label: 'EOS'
        },
        {
          value: 'BOS',
          label: 'BOS'
        },
        {
          value: 'CMEOS',
          label: 'CMEOS'
        },
        {
          value: 'ChainMaker',
          label: 'ChainMaker'
        }
      ],
      // 合约类型表格
      contractTable: [
        {
          title: '模板类型',
          key: 'contractType'
        },
        {
          title: '链类型',
          key: 'chainType'
        },
        {
          title: '合约语言',
          key: 'languageType'
        },
        {
          title: '适用场景',
          key: 'scene',
          tooltip: true
        },
        {
          title: '描述',
          key: 'description',
          tooltip: true
        },
        {
          title: '更新时间',
          key: 'updateTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 200,
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.show(params.index)
                    }
                  }
                },
                '详情'
              ),
              h(
                'Button',
                {
                  props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                  style: this.buttonStyle,
                  on: {
                    click: () => {
                      this.changeData(params.index)
                    }
                  }
                },
                '修改'
              ),
              h(
                'Button',
                {
                  props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                  style: this.buttonStyle,
                  on: {
                    click: () => {
                      this.datadelete(params.index)
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      contractTableData: [],
      listId: '',
      previewup: [],
      userPermission: JSON.parse(localRead('userPermission')),
      // preview: []// 模板预览数组

    }
  },
  methods: {
    changechaincity (value) {
      this.contractLanguage = ''
      if (value === 'EOS' || value === 'BOS') {
        this.languageList = [
          { enumKey: 'C++', enumValue: 'C++' }
        ]
      } else if (value === 'CMEOS') {
        this.languageList = [
          { enumKey: 'C++', enumValue: 'C++' },
          { enumKey: 'JS', enumValue: 'Java Script' }
        ]
      } else if (value === 'ChainMaker') {
        // GO、C++、rust、tinygo、solidity
        this.languageList = [
          { enumKey: 'GO', enumValue: 'GO' },
          { enumKey: 'C++', enumValue: 'C++' },
          { enumKey: 'RUST', enumValue: 'RUST' },
          { enumKey: 'TINYGO', enumValue: 'TINYGO' },
          { enumKey: 'SOLIDITY', enumValue: 'SOLIDITY' }
        ]
      } else {
        this.languageList = []
      }
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, top: 300 }) },
    changepage (index) {
      // 改变页码时触发
      this.PageParam.pageIndex = index
      this.getTablist() // 获取表格列表
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      // 当前页条数
      this.PageParam.pageSize = size
      this.getTablist() // 获取表格列表
    },
    // 搜索
    searchList () {
      this.getTablist()
      // this.inputvalue = ''
    },

    // // 合约模板列表删除
    // remove (info) {
    //   console.log(info.row)
    //   // debugger
    //   // 临时假删除
    //   this.data6.splice(info.index, 1)
    // },
    // 新建合约类型
    newType () {
      // console.log('1111')
      this.$router.push({
        name: 'template_newmodal'
      })
    },
    // // 获取前表格所有选中项
    // getSelectAll (list) {
    //   this.curSelectList = list
    // },
    // 合约列表弹框
    datadelete (index) {
      this.modal1 = true
      this.listId = this.contractTableData[index].id
    },
    // 合约列表删除事件
    ok () {
      ContractTemplateDelete(this.listId).then(res => {
        // console.log(res.code)
        if (res.code === '00000') {
          this.$Message.success('删除模板类型成功!')
          this.getTablist()
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    cancel () {
      this.modal1 = false
    },
    // 合约列表详情跳转
    show (index) {
      this.$router.push({
        name: 'template_details',
        params: {
          listId: this.contractTableData[index].id,
          content: this.contractTableData[index]
        }

      })
    },
    // 合约列表修改跳转
    changeData (index) {
      this.$router.push({
        name: 'tem_modify',
        params: {
          listIddisy: this.contractTableData[index].id
          // content: this.previewup
        }
      })

      // console.log(this.contractTableData[index].id)
      // 获取修改里的模板上传数组接口
      // TemplatePreview(this.contractTableData[index].id).then(res => {
      //   console.log(res.data)
      //   let previewup1 = res.data.map(item => {
      //     return {
      //       name: item.fileName, fileid: item.id
      //     }
      //   })
      //   this.previewup = previewup1
      //   console.log(this.previewup)
      //   //         let aa = res.data.records.filter((item)=>{
      //   // return item.accountTypeKey=="NORMAL"
      //   // })
      //   // this.preview = res.data
      // })
      // console.log(this.previewup)

      // console.log(this.contractTableData[index])
    },
    reset () {
      this.inputvalue = ''
      this.chainType = ''
      this.contractLanguage = ''
      this.PageParam = { pageSize: 10, pageIndex: 1 }
      this.getTablist()
    },
    // 请求
    getTablist () {
      let contractData = {
        pageParam: this.PageParam, // 分页
        contractType: this.inputvalue,
        chainType: this.chainType,
        languageType: this.contractLanguage
      }
      ContractTemplate(contractData).then(res => {
        // console.log(res.message)
        if (res.code === 'C0006') {
          this.msgInfo('error', res.message, true)
        } else {
          // console.log(res.data.records)
          let tabledata = res.data.records.map(item => {
            return {
              ...item,
              languageType: item.languageType === 'JS' ? 'JavaScript' : item.languageType
            }
          })
          this.contractTableData = tabledata

          this.PageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  mounted () {
    this.getTablist()
  },
  computed: {


    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }

    },
  },
  activated () {
    this.getTablist()
  }
}
</script>
<style lang="less" scoped>
.ivu-card-body {
  .ivu-input-wrapper {
    line-height: 34px;
  }
}
.cz_header {
  display: flex;
  margin-top: 10px;
  justify-content: space-between;

  .cz_ss {
    display: flex;
  }
  .s_type {
    display: flex;
  }
}
.b_search {
  display: flex;
}
.cz_hao,
.s_type,
.sl_time {
  margin-right: 3%;
}
.cz_hao {
  display: flex;
}
.sl_time {
  display: flex;
}
.btn_search {
  margin-right: 10px;
}
// table
.cz_table {
  margin-top: 2% !important;
}
button.btn {
  position: absolute;
  right: 10px;
  margin: 0 10px;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
.btn_title {
  width: 80px;
  height: 33px;
  text-align: center;
  line-height: 33px;
  display: inline-block;
}

.newcontract_modal {
  max-height: 500px;
  overflow-y: scroll;
}
// 删除弹框事件
/deep/.ivu-modal-content {
  width: 300px;
  // height: 100px;
  margin: 0 auto;
}
.serch_btn {
  margin: 0 10px !important;
}
.cz_search {
  display: flex;
  .sl_timout {
    height: 33px;
    padding: 5px 8px;
    text-align: center;
    border-radius: 4px;
  }
}
</style>
