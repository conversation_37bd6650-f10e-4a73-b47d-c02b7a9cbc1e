/**
 * Created by ji<PERSON>n<PERSON> on 16/11/18.
 */

export function isvalidUsername (str) {
  // const valid_map = ['admin', 'editor', 'test1', 'test2', 'test3']
  // return valid_map.indexOf(str.trim()) >= 0
  return true
}

/* 合法uri */
export function validateURL (textval) {
  const urlregex = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return urlregex.test(textval)
}

/* 小写字母 */
export function validateLowerCase (str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/* 大写字母 */
export function validateUpperCase (str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/* 大小写字母 */
export function validatAlphabets (str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * validate email
 * @param email
 * @returns {boolean}
 */
export function validateEmail (email) {
  const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return re.test(email)
}

// 注册手机号验证
export function phoneRegeFun (phone) {
  const phoneRege = /^(((13|14|15|16|18|17|19)\d{9}))$/
  return phoneRege.test(phone)
}

// 注册密码验证
export function paswordRegeFun (val) {
  const paswordRege = /^(?=.*?[a-z)(?=.*>[A-Z])(?=.*?[0-9])[a-zA-Z0-9]{6,8}$/
  return paswordRege.test(val)
}

// 合约库验证版本号vx.x.x格式
export function version (val) {
  const version = /^([vV]([0-9]{1,2}))(\.[0-9]{1,2}){2}$/
  return version.test(val)
}

// utc 转北京时间
export function utc2beijing (utc_datetime) {
  // console.log(utc_datetime)
  if (!utc_datetime) {
    return '-'
  }
  function formatFunc (str) {
    return str > 9 ? str : '0' + str
  }
  let date2 = new Date(utc_datetime)
  let year = date2.getFullYear()
  let mon = formatFunc(date2.getMonth() + 1)
  let day = formatFunc(date2.getDate())
  let hour = date2.getHours()
  let noon = hour >= 12 ? 'PM' : 'AM' // 判断是上午还是下午
  hour = hour >= 12 ? hour - 12 : hour // 12小时制
  hour = formatFunc(hour)
  let min = formatFunc(date2.getMinutes())
  let sec = formatFunc(date2.getSeconds())
  let dateStr = year + '-' + mon + '-' + day + ' ' + hour + ':' + min + ':' + sec
  return dateStr

  // 转为正常的时间格式 年-月-日 时:分:秒
  // let T_pos = utc_datetime.indexOf('T');
  // let Z_pos = utc_datetime.indexOf('Z');
  // let year_month_day = utc_datetime.substr(0, T_pos);
  // let hour_minute_second = utc_datetime.substr(T_pos + 1, Z_pos - T_pos - 1);
  // let new_datetime = year_month_day + " " + hour_minute_second; // 2017-03-31 08:02:06
  // // return new_datetime;
  // // 处理成为时间戳
  // timestamp = new Date(Date.parse(new_datetime));
  // timestamp = timestamp.getTime();
  // timestamp = timestamp / 1000;

  // // 增加8个小时，北京时间比utc时间多八个时区
  // let timestamp = timestamp + 8 * 60 * 60;

  // // 时间戳转为时间
  // let beijing_datetime = new Date(parseInt(timestamp) * 1000).toLocaleString().replace(/年|月/g, "-").replace(/日/g, " ");
  // return beijing_datetime; // 2017-03-31 16:02:06
}

// 显示日期在页面上  yyy-MM-dd
export function getCurrentDate (num = 0) {
  let now = new Date()
  if (num == 1) {
    now = new Date((new Date()).getTime() - 24 * 60 * 60 * 1000)
  }
  // console.log(now);
  let year = now.getFullYear() // 得到年份
  let month = now.getMonth()// 得到月份
  let date = now.getDate()// 得到日期
  let day = now.getDay()// 得到周几
  let hour = now.getHours()// 得到小时
  let minu = now.getMinutes()// 得到分钟
  let sec = now.getSeconds()// 得到秒
  month = month + 1
  if (month < 10) month = '0' + month
  if (date < 10) date = '0' + date
  if (hour < 10) hour = '0' + hour
  if (minu < 10) minu = '0' + minu
  if (sec < 10) sec = '0' + sec
  // let time = "";
  let time = year + '/' + month + '/' + date + ' ' + hour + ':' + minu + ':' + sec
  // 精确到天
  // if(format==1){
  //   time = year + "-" + month + "-" + date;
  // }
  // //精确到分
  // else if(format==2){
  //   time = year + "-" + month + "-" + date+ " " + hour + ":" + minu + ":" + sec;
  // }
  return time
}

// 把时间戳转为为普通日期格式
export function getLocalTime (nS) {
  return new Date(parseInt(nS) * 1000).toLocaleString().replace(/:\d{1,2}$/, ' ')
}
// 把时间戳转为为普通日期格式
export function getYMDHMS (timestamp, index) {
  let date = new Date(timestamp * 1000)// 时间戳为10位需*1000，时间戳为13位的话不需乘1000
  let Y = date.getFullYear()
  let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1)
  let D = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  let h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  let m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  let s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  // return Y + '-' + '-' + M + '-' + D + ' ' + h + ':' + m + ':' + s;
  // M + '/' + D +
  if (index == 1) {
    return h + ':' + m
  }
  if (index == 2) {
    return M + '/' + D + '&nbsp;' + h + ':' + m
  }
}

// 断是否是IE 11及以下或者其他(其他里包括IE edge)
export function isIE () {
  // if (document.documentMode) return document.documentMode;
  let userAgent = navigator.userAgent // 取得浏览器的userAgent字符串
  let isIE = userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1 // 判断是否IE<11浏览器
  let isEdge = userAgent.indexOf('Edge') > -1 && !isIE // 判断是否IE的Edge浏览器
  let isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf('rv:11.0') > -1
  // console.log(isIE, isIE11, isEdge)
  if (isIE || isIE11) {
    return true
  } else {
    return false
  }
}
// 计算数组字
export function diffNumber (number) {
  number = Number(number)
  if (number >= 100000 && number < 100000000) {
    number = Math.round(number / 10000) + '万'
  }
  if (number >= 100000000) {
    number = Math.round(number / 100000000) + '亿'
  }
  return number
}
