<!--
 消费者用户管理
   Aturun
-->
<template>
    <div class="consumer_user">
      <div class="content-top">
        <div class="content-top-lift-title">
          数据用户
        </div>
        <div class="top_op">
          <div class="content-top-lift">
            <div class="top-right-input icon-search_suffix">
              <el-input
                placeholder="可输入用户名称或消费者合约查询"
                v-model="input"
                @keyup.enter.native="getDataConsumerList(true)">
                <i slot="suffix" class="el-icon-search" @click="getDataConsumerList(true)"></i>
              </el-input>
            </div>
          </div>
          <div class="content-top-right">
            <div class="top-right-button">
              <el-button icon="el-icon-plus" @click="newConsumer">新建消费者账户</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="content-body">
        <el-table
            :data="tableData"
            style="width: 100%"
            height="520px"
            stripe>
          <el-table-column
              prop="consumerName"
              label="用户名称"
              width="160">
            <template slot-scope="scope">
              <div v-if="scope.row.consumerName.length<=9">{{scope.row.consumerName}}</div>
              <el-popover v-else trigger="hover" placement="top">
                <div class="dsfdg" >{{ scope.row.consumerName }}</div>
                <div slot="reference" class="name-wrapper">
                  {{ scope.row.consumerName }}
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
              prop="tempName"
              label="模板名称">
          </el-table-column>
          <el-table-column
            prop="tempName"
            label="预言机名称">
          </el-table-column>
          <el-table-column
                label="类型">
              <template slot-scope="scope">
                <div v-if="scope.row.fetchType=='0'">定时服务用户</div>
                <div v-if="scope.row.fetchType=='1'">请求服务用户</div>
              </template>
          </el-table-column>
          <el-table-column
              prop="chainName"
              label="所属链">
          </el-table-column>
          <el-table-column
              label="业务描述">
            <template slot-scope="scope">
              <div v-if="scope.row.memo.length<=12">{{scope.row.memo}}</div>
              <el-popover v-else trigger="hover" placement="top">
                <div class="dsfdg" >{{ scope.row.memo }}</div>
                <div slot="reference" class="name-wrapper">
                  {{ scope.row.memo }}
                </div>
              </el-popover>
            </template>
          </el-table-column>
<!--          <el-table-column-->
<!--              label="请求合约"-->
<!--          >-->
<!--            <template slot-scope="scope">-->
<!--              {{setConsumerContractName(scope.row.requestContractName)}}-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column
              label="消费者合约">
            <template slot-scope="scope">
              {{setConsumerContractName(scope.row.consumerContractName)}}
            </template>
          </el-table-column>
          <el-table-column
              label="创建时间">
            <template slot-scope="scope">
              {{setDates(scope.row.createTime)}}
            </template>
          </el-table-column>
<!--          <el-table-column-->
<!--              label="姓名"-->
<!--              width="180">-->
<!--            <template slot-scope="scope">-->
<!--              <el-popover trigger="hover" placement="top">-->
<!--                <p>姓名: {{ scope.row.name }}</p>-->
<!--                <p>住址: {{ scope.row.address }}</p>-->
<!--                <div slot="reference" class="name-wrapper">-->
<!--                  <el-tag size="medium">{{ scope.row.name }}</el-tag>-->
<!--                </div>-->
<!--              </el-popover>-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column label="操作"  width="380">
            <template slot-scope="scope">
              <el-button
                  size="mini"
                  @click="handlelook(scope.$index, scope.row)">查看</el-button>
              <el-button
                  size="mini"
                  @click="handleDelete(scope.$index, scope.row)">删除</el-button>
              <el-button
                  size="mini"
                  @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="block table_pag">
          <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="entityIn.rows"
              background
              layout="total, prev, pager, next, sizes, jumper"
              :total="total">
          </el-pagination>
        </div>
      </div>
      <newConsumerDialog ref="newConsumerRef" @Refresh="Refresh"></newConsumerDialog>

    </div>
</template>

<script>
import newConsumerDialog from './components/newConsumerDialog'
import * as api from "./api";
import {getFormatDates} from '../../utils/atuUtils.js'
    export default {
  components:{
    newConsumerDialog,
  },
        data(){
            return {
              currentPage: 1,
              input:'',
              tableData: [],
              entityIn:{
                "consumerId": '',
                "consumerName": '',
                "filter": {},
                "order": '',
                "page": 1,
                "rows": 10,
                "sort":''
              },
              total:1
            }
        },
        created(){
          this.getDataConsumerList()
        },
        mounted(){

        },
        methods: {
          // 数据重排返现
          setConsumerContractName(val){
            let value=val
            if (!value) return '/'
            value=value.split(',')[1]+'('+value.split(',')[2]+')'
            return value
          },
          // 格式化日期时间
          setDates(val){
            return getFormatDates(val,'yyyy-mm-dd MM:mm:ss')
          },
          // 打开新增弹窗
          newConsumer(){
            this.$refs.newConsumerRef.operationState=0
            this.$refs.newConsumerRef.Visible=true
          },
          Refresh(){
            this.getDataConsumerList()
          },
          //获取数据用户列表
          getDataConsumerList(search=false){
            if(search){
              this.entityIn.page=1
              this.currentPage = 1
            }
            api.getDataConsumerList({...this.entityIn,consumerName:this.input}
            ).then(res=>{
              if(res.code!=0) return this.$message.warning(res.msg)
              this.tableData=[]
              res.result.rows.map(ele=>{
                let memo = ele['memo']?ele['memo']:''
                this.tableData.push({...ele,memo:memo})
              })
              this.total=res.result.total
            })
          },
          handleEdit(index, row) {
             this.$refs.newConsumerRef.operationState=1
              this.$refs.newConsumerRef.form=row
              this.$refs.newConsumerRef.Visible=true
          },
          handleDelete(index, row) {
            this.$confirm(`操作将删除${row.consumerName}, 是否继续?`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
              showClose:false
            }).then(() => {
              api.delDataConsumer({consumerId:row.consumerId}).then(res=>{
                if(res.code!=0) return this.$message.warning(res.msg)
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                });
                this.Refresh()
              })
            }).catch(() => {
            });
          },
          handlelook(index, row) {
            this.$refs.newConsumerRef.operationState=2
            this.$refs.newConsumerRef.form=row
            this.$refs.newConsumerRef.Visible=true
          },
          handleSizeChange(val) {
            this.entityIn.rows =val
            this.getDataConsumerList()
          },
          handleCurrentChange(val) {
           this.entityIn.page = val
            this.currentPage =val
            this.getDataConsumerList()
          }
        },
    }
</script>

<style lang="less" scoped>
.consumer_user{
  margin: 16px 14px;
  background: #ffffff;
  height: 706px;
  .content-top{
    .top_op{
      display: flex;
      justify-content: space-between;
    }
    .content-top-lift-title{
      padding-left: 30px;
      font-size: 18px;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }
    .content-top-right{
      display: flex;
      padding: 0 20px 0 0;
      .top-right-input{
        margin-right: 12px;
      }
      .top-right-button{
        .el-button{

        }
      }
    }
    .content-top-lift{
      padding: 10px 0 0 23px;
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      color: #333333;
      opacity: 1;
    }
  }
  .content-body{
    margin: 11px 17px 0 16px;
  }
  .table_pag{
    margin: 12px 16px 0 0;
    display: flex;
    justify-content: flex-end;
  }
}
</style>



