<template>
  <div>
    <div class="auto-login">加载中...
    </div>
  </div>
</template>

<script>
  import {loginByUsername} from "@/api/baascore/contractLibrary";
  import {setToken} from '@/lib/util.js'
  import md5 from 'js-md5';
  export default {
    components: {},
    name: "check",
    data() {
      return {
        loginForm: {
          username: '', // zxlei0513 zxlei051301
          usertime: '',
          password: "",
        },
        timer: null,
      };
    },
    mounted() {
      this.handleLogin()
    },
    destroyed() {
     // clearInterval(this.timer);
    },
    methods: {
      usersDetails() {
      },
      checkAuth() {

      },
      handleLogin() {
        let timestamp = new Date().getTime()
        this.loginForm.usertime = timestamp
        let md5Val=timestamp + this.loginForm.username + 'Cmri9527Cmchain^!*'
        this.loginForm.password = md5(md5Val)
        loginByUsername(this.loginForm.username,this.loginForm.usertime, this.loginForm.password)
          .then((res) => {
            if (res.token && res.expire) {
            setToken(res.token)
          }
          })
        return
      },
    },

  };
</script>

<style rel="stylesheet/less" lang="less">
  @bg: #2d3a4b;
  @light_gray: #eee;

</style>

<style>

  .auto-login {
    text-align: center;
    padding-top: 300px;

  }

  .auto-login span {
    color: blue;
    cursor: pointer;
  }
</style>
