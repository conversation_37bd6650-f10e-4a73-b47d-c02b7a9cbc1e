<template>
  <div class="tenant" style="margin:10px 5px 30px 10px;">
      <p class="info-title">
        <span class="bs"></span>
        <Select v-model="watchType" style="width:150px;margin: -8px 0 0 5px;" filterable @on-change="watchTypeChange">
          <Option v-for="item in watchTypeList" :value="item.key" :key="item.key">{{item.value}}</Option>
        </Select>
        <span :style="{display: getDisplay}" style="vertical-align: middle;float:right;margin-top:-5px;">
          <Button ghost type="success" @click="addTenant" icon="md-add" style="margin-right:10px;">添加租户</Button>
          <Input placeholder="请输入租户名称" style="width:250px;vertical-align:baseline;" v-model="isTenantName" @keyup.enter.native="searchName">
             <Icon type="ios-search" slot="suffix" @click="searchName"/>
          </Input>
        </span>
      </p>
      <p class="info-desc">{{getDesc}}</p>
      <div :style="{display: getDisplay}">
        <edit-table-mul :columns="tenantColums" v-model="selectTableData" :key="transferKey"></edit-table-mul>
        <Page :total="selectPageParam.pagetotal" :current.sync="selectPageParam.pageIndex" @on-change="selectPageChange" :page-size="selectPageParam.pageSize" :page-size-opts="[5,10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="selectPageSizeChange" style="text-align:right;padding-top:15px;"/>
      </div>
  <Modal
    :draggable="true"
    v-model="addTenantModal"
    width="800"
    title="添加租户"
    sticky
    :scrollable="true"
    :mask-closable="false"
    >
    <div style="padding: 10px 0 20px 0">
      <Input placeholder="请输入租户名称" class="input-search" style="width:250px;margin-left:515px;vertical-align:middle;" v-model="noTenantName" @keyup.enter.native="searchNoTenantName">
        <Icon type="ios-search" slot="suffix" @click="searchNoTenantName"/>
      </Input>
    </div>
    <div style="width:100%;">
      <Table :columns="selectColums" :data="notSelectTableData" stripe :key="transferKey" ref="selection" :show-header="true" @on-select="onSelect" @on-select-all="onSelectAll" @on-select-cancel="onSelectCancel" @on-select-all-cancel="onSelectAllCancel" max-height="300"></Table>
      <Page :total="noSelectPageParam.pagetotal" :current.sync="noSelectPageParam.pageIndex" @on-change="noSelectPageChange" :page-size="noSelectPageParam.pageSize" :page-size-opts="[5,10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="noSelectPageSizeChange" style="text-align:right;padding-top:15px;"/>
    </div>
    <div slot="footer">
      <div v-show="selectRecords.length > 0" style="padding:8px 0 10px 10px;float:left;">
        <span>选择了{{selectRecords.length}}个租户</span>
      </div>
      <Button type="text" size="large" @click="cancel">取消</Button>
      <Button type="primary" size="large" :disabled="selectRecords.length > 0 ? false : true" @click="ok">确定</Button>
    </div>
  </Modal>
  <Modal v-model="delModal" :styles="{top: '280px'}" :closable="false" :z-index="1000" :mask-closable="false">
    <div style="text-align:left;font-size:18px;font-weight:500;">
        <Icon type="ios-information-circle" size="25" style="color:#f60;"></Icon>
        <span style="margin-left:5px;">{{'确定要删除[' + delName +']租户吗？'}}</span>
    </div>
    <div style="text-align:left;padding:10px 0 10px 35px">删除后该租户将不可见此链</div>
    <div slot="footer">
      <Button @click="delCancel">取消</Button>
      <Button type="primary"  @click="delConfirm">确定</Button>
    </div>
  </Modal>
  <Modal v-model="tipModal" :styles="{top: '280px'}" :closable="false" :z-index="1000" :mask-closable="false">
    <div style="text-align:left;font-size:18px;font-weight:500;">
        <Icon type="ios-information-circle" size="25" style="color:#f60;"></Icon>
        <span style="margin-left:5px;">{{this.tip.title}}</span>
    </div>
    <div style="text-align:left;padding:10px 0 10px 35px">{{this.tip.desc}}</div>
    <div slot="footer">
      <Button @click="tipCancel">取消</Button>
      <Button type="primary"  @click="tipConfirm">确定</Button>
    </div>
  </Modal>
  </div>
</template>

<script>
import { getChainTenant, addChainTenant, delChainTenant, reviseVisible } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import { watchTypeList } from './typeList'
export default {
  name: 'tenant',
  components: {
    EditTableMul
  },
  props: {
    tenantVisibility: {
      type: String,
      default: ''
    },
    chainId: {
      type: Number,
      default: 0
    },
    chainName: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      selectPageParam: {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      },
      noSelectPageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      visibleState: ['VISIBLE', 'INVISIBLE'],
      single: false,
      watchTypeList: [],
      watchType: '',
      status: { watchType: '', desc: '' },
      backWatchType: '',
      tip: { title: '', desc: '' },
      tipModal: false,
      tenantVisibilityTrans: '',
      tenantName: '',
      isTenantName: '',
      noTenantName: '',
      delName: '',
      delId: 0,
      addTenantModal: false,
      delModal: false,
      transferKey: 0,
      onSelectionChangeData: [],
      onSelectData: [],
      selectRecords: [],
      eosChainId: this.$route.params.eosChainId ? this.$route.params.eosChainId : '',
      selectTableData: [],
      notSelectTableData: [],
      tenantColums: [
        // { key: 'tenantId', title: '租户ID' },
        { key: 'tenantName', title: '租户名称' },
        { key: 'tenantBrief', title: '租户描述', tooltip: true },
        { key: 'configTime', title: '配置时间' },
        { key: 'action',
          title: '操作',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { size: 'small', type: 'text' },
                style: { color: '#E62412', border: '1px solid #FA5151' },
                on: {
                  click: () => {
                    this.delTenantName(params.index)
                  }
                }
              }, '删除')
            ]
            )
          }
        }
      ],
      selectColums: [
        {
          type: 'selection',
          width: 35,
          align: 'center'
        },
        { key: 'tenantName', title: '租户名称' },
        { key: 'tenantBrief', title: '租户描述', minWidth: 300, tooltip: true }
      ]
    }
  },
  computed: {
    getDesc () {
      if (this.backWatchType === 'ALL_VISIBLE') {
        return '此状态下，平台所有租户可见本链'
      } else if (this.backWatchType === 'PARTIALLY_VISIBLE') {
        return '此状态下，可配置部分租户对本链的可见性'
      } else if (this.backWatchType === 'ALL_INVISIBLE') {
        return '此状态下，对平台所有租户隐藏本链'
      } else {
        return ''
      }
    },
    getDisplay () {
      if (this.backWatchType === 'ALL_VISIBLE') {
        return 'none'
      } else if (this.backWatchType === 'PARTIALLY_VISIBLE') {
        return 'block'
      } else if (this.backWatchType === 'ALL_INVISIBLE') {
        return 'none'
      } else {
        return ''
      }
    }
  },
  methods: {
    watchTypeChange () {
      if (this.watchType === 'ALL_VISIBLE') {
        this.tip.title = '确定将该链状态改为“全部租户可见”吗？'
        this.tip.desc = '此状态下，平台所有租户可见此链'
      } else if (this.watchType === 'PARTIALLY_VISIBLE') {
        this.tip.title = '确定将该链状态改为“部分租户可见”吗？'
        this.tip.desc = '此状态下，可配置部分租户对此链的可见性'
      } else if (this.watchType === 'ALL_INVISIBLE') {
        this.tip.title = '确定将该链状态改为“全部租户不可见”吗？'
        this.tip.desc = '此状态下，对平台所有租户隐藏此链'
      }
      this.tipModal = true
    },
    tipCancel () {
      this.tipModal = false
      this.watchType = this.backWatchType
    },
    tipConfirm () {
      reviseVisible(this.chainId, this.watchType).then(res => {
        if (res.code === '00000') {
          this.msgInfo('success', res.message, true)
          this.tipModal = false
          if (this.watchType === 'PARTIALLY_VISIBLE') {
            this.getChainTenantList(this.selectPageParam, 0)
          }
          this.backWatchType = this.watchType
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    handleSelectAll (status) {
      this.$refs.selection.selectAll(status)
    },
    onSelectAll (selection) {
      selection.forEach(item => {
        this.selectRecords.push(item.tenantId)
      })
      this.selectRecords = Array.from(new Set(this.selectRecords))
    },
    onSelectAllCancel (selection) {
      this.notSelectTableData.forEach(item => {
        this.selectRecords.forEach((e, index) => {
          if (item.tenantId === e) {
            this.selectRecords.splice(index, 1)
          }
        })
      })
    },
    onSelect (selection, row) {
      this.selectRecords.push(row.tenantId)
      this.notSelectTableData.forEach(item => {
        if (item.tenantId === row.tenantId) {
          item['_checked'] = true
        }
      })
    },
    onSelectCancel (selection, row) {
      this.selectRecords.forEach((item, index) => {
        if (row.tenantId === item) {
          this.selectRecords.splice(index, 1)
        }
      })
    },
    handleTableChecked (datas) {
      this.selectRecords.forEach(item => { // 判断本页数据状态
        datas.forEach(e => {
          if (item === e.tenantId) {
            e['_checked'] = true
          }
        })
      })
    },
    selectPageChange (index) {
      this.selectPageParam.pageIndex = index
      this.getChainTenantList(this.selectPageParam, 0)
    },
    selectPageSizeChange (index) {
      this.selectPageParam.pageSize = index
      this.getChainTenantList(this.selectPageParam, 0)
    },
    searchName () {
      this.getChainTenantList(this.selectPageParam, 0)
    },
    searchNoTenantName () {
      this.getChainTenantList(this.noSelectPageParam, 1)
    },
    noSelectPageChange (index) {
      this.noSelectPageParam.pageIndex = index
      this.getChainTenantList(this.noSelectPageParam, 1)
    },
    noSelectPageSizeChange (index) {
      this.noSelectPageParam.pageSize = index
      this.getChainTenantList(this.noSelectPageParam, 1)
    },
    init () {
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    getChainTenantList (pageParam, i, val) {
      if (val) {
        this.tenantName = ''
      } else {
        this.tenantName = i === 0 ? this.isTenantName : this.noTenantName
      }
      getChainTenant(pageParam, this.chainId, this.visibleState[i], this.tenantName).then(res => {
        if (res.code === '00000') {
          if (res.data && i === 0) {
            this.selectTableData = res.data.records ? res.data.records : []
            this.selectPageParam = {
              pagetotal: res.data.total,
              pageSize: res.data.size,
              pageIndex: res.data.current
            }
          } else if (res.data && i === 1) {
            this.notSelectTableData = res.data.records ? res.data.records : []
            this.noSelectPageParam = {
              pagetotal: res.data.total,
              pageSize: res.data.size,
              pageIndex: res.data.current
            }
            this.handleTableChecked(this.notSelectTableData)
          }
          ++this.transferKey
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    ok () {
      this.addTenantModal = false
      addChainTenant(this.chainId, this.selectRecords).then(res => {
        if (res.code === '00000') {
          this.addTenantModal = false
          this.msgInfo('success', '已成功添加' + this.selectRecords.length + '个为“' + this.chainName + '”的可见租户', true)
          this.getChainTenantList(this.selectPageParam, 0, true)
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    cancel () {
      this.addTenantModal = false
    },
    addTenant () {
      this.addTenantModal = true
      this.noSelectPageParam = {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      }
      this.noTenantName = ''
      this.notSelectTableData = []
      this.selectRecords = []
      this.getChainTenantList(this.noSelectPageParam, 1)
    },
    delTenantName (index) {
      this.delModal = true
      this.delName = this.selectTableData[index].tenantName
      this.delId = this.selectTableData[index].tenantId
    },
    delCancel () {
      this.delModal = false
    },
    delConfirm () {
      delChainTenant(this.chainId, this.delId).then(res => {
        if (res.code === '00000') {
          this.msgInfo('success', '已成功删除“' + this.delName + '”对' + this.chainName + '的可见性', true)
          this.delModal = false
          this.getChainTenantList(this.selectPageParam, 0)
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  created () {
  },
  mounted () {
    this.watchTypeList = watchTypeList
    // this.$Message.config({
    //   top: 250,
    //   duration: 5
    // })
  },
  watch: {
    single: {
      handler (newVal, oldVal) {
        this.$refs.selection.selectAll(newVal)
      },
      deep: true,
      immediate: false
    },
    chainId: {
      handler (newVal, oldVal) {
        this.chainId = newVal
      },
      deep: true,
      immediate: false
    },
    tenantVisibility: {
      handler (newVal, oldVal) {
        this.tenantVisibility = newVal
        this.watchType = this.tenantVisibility
        this.backWatchType = this.watchType
        if (this.watchType === 'PARTIALLY_VISIBLE') {
          this.getChainTenantList(this.selectPageParam, 0)
        }
      },
      deep: true,
      immediate: false
    }
  }
}
</script>

<style lang="less" scoped>
.tenant{
  .info-title{
      font-size:16px;
      font-weight:bold;
      margin-bottom:25px;
      height:18px;
      font-family: 'Microsoft YaHei';
      line-height: 18px;
      color: #333333;
    }
  .bs{
      float:left;
      width: 6px;
      height: 18px;
      background: #19C3A0;
      opacity: 1;
      border-radius: 3px;
  }
  .info-desc{
    margin:20px 0 20px 15px;
    font-weight: 400;
    color:#807e7e;
  }
  .single{
    vertical-align:middle;
    margin-top:20px;
    padding-left:20px;
  }
  /deep/.ivu-select-selection {
    border: none;
  }
  /deep/.ivu-select-input {
    font-size:16px;
    font-weight: bold;
    padding: 0 24px 0 5px
  }
}
 /deep/.ivu-table .demo-table-info-row td{
    background-color: #f8f8f9;
    }
/deep/.ivu-scroll-loader-wrapper{
  padding: 0;
}
</style>
