<!--
  aturun
 信源合约选择
  2021/10/21

-->
<template>
  <el-dialog class="dialog_sty consumer_contract_dialog" title="合约" :visible.sync="Visible" width="1000px" :modal="false" :before-close="handleClose" destroy-on-close :close-on-click-modal="false" @opened="getContractList">
    <div>
      <div class="dialog_content">
        <div class="content-top">
          <div class="content-top-lift">
            信源合约
          </div>
          <div class="content-top-right">
            <div class="top-right-input icon-search_suffixfff">
              <el-input placeholder="可输入合约名称或中文名称查询" v-model="input" @keyup.enter.native="getContractList">
                <i slot="suffix" class="el-icon-search" @click="getContractList"></i>
              </el-input>
            </div>
          </div>
        </div>
        <div class="content-body">
          <el-table :data="ContractList" style="width: 100%" height="640" stripe>
            <el-table-column :prop="item.field" :label="item.lable" v-for="item in tableTitleInfo"></el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button size="mini" @click="handleChoice(scope.$index, scope.row)">选择</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!--    <span slot="footer" class="dialog-footer">-->
    <!--    <el-button @click="Visible = false">取 消</el-button>-->
    <!--    <el-button type="primary" @click="submitForm">确 定</el-button>-->
    <!--  </span>-->
  </el-dialog>
</template>

<script>
import * as api from "../api";

export default {
  name: "SmartContractDialog",
  components: {},
  props: ['nodeId'],
  data () {
    return {
      tableTitleInfo: [
        { lable: '合约名称', field: 'contractName' },
        { lable: '描述', field: 'memo' },
        // {lable:'创建时间',field:'memo'},
      ],
      input: '',
      Visible: false,
      Form: {
        name: ''
      },

      rules: {
        name: [
          { required: true, message: '请输入用户名称', trigger: 'blur' },
        ],
      },
      ContractList: [{
        contractName: 'oracle',
        memo: '预言机合约'
      }]
    }
  },

  watch: {
  },
  created () {
  },
  mounted () {
  },
  methods: {
    //获取列地址列表
    getContractList () {
      // api.getContractList(
      //     {
      //       contractName:this.input,
      //       nodId:this.nodeId
      //     }
      // ).then(res=>{
      //   this.ContractList=res.result
      // })
    },
    handleChoice (index, row) {

      this.$emit('setSecretKey', row.contractName)
      this.Visible = false
    },
    handleClose (done) {
      done();
    },

  },

}
</script>

<style lang="less" scoped>
.smart_contract {
  .dialog_content {
    .content-top {
      display: flex;
      justify-content: space-between;
      .content-top-right {
        display: flex;
        padding: 16px 20px 0 0;
        .top-right-input {
          margin-right: 12px;
        }
        .top-right-button {
          .el-button {
          }
        }
      }
      .content-top-lift {
        padding: 25px 0 0 23px;
        font-size: 14px;
        font-weight: 500;
        line-height: 24px;
        color: #333333;
        opacity: 1;
        span {
          color: #2d8cf0;
        }
      }
    }
    .content-body {
      margin: 11px 17px 0 16px;
    }
    .table_pag {
      margin: 12px 16px 0 0;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
