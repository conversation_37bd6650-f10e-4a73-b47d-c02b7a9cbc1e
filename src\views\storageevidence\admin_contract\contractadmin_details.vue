<template>
  <div class="contract">
    <Collapse v-model="panelValue" simple name="mainpanel">
      <Panel name="1" style="background:#ECEFFC;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        合约基础信息
        <div v-if="this.$route.params.name==='EOS'||this.$route.params.name==='CMEOS'||this.$route.params.name==='BOS'" slot="content" class="basetext">
          <p slot="content" class="basetext" style="display: flex;">
            <span style="width: 50%;display: block;">合约名称：{{arrDetails.contractName}}</span>
            <span style="width: 50%;display: block;">创建时间：{{arrDetails.createTime}}</span>
          </p>
          <p slot="content" class="basetext" style="display: flex;">
            <span style="width: 50%;display: block;">应用名称：{{arrDetails.contractReadableName}}</span>
            <span style="width: 50%;display: block;">项目名称：{{ arrDetails.projectName }}</span>
          </p>
          <p slot="content" class="basetext" style="display: flex;">
            <span style="width: 50%;display: block;">应用简介：{{ arrDetails.brief }}</span>
            <span style="width: 50%;display: block;">合约语言：{{ arrDetails.languageType=== 'JS' ? 'Java Script' : arrDetails.languageType }}</span>
          </p>
        </div>
        <div v-else-if="this.$route.params.name==='ChainMaker'" slot="content" class="basetext">
          <p slot="content" class="basetext" style="display: flex;">
            <span style="width: 50%;display: block;">区块链名称：</span>
            <span style="width: 50%;display: block;">区块链ID：</span>
          </p>
          <p slot="content" class="basetext" style="display: flex;">
            <span style="width: 50%;display: block;">合约名称：</span>
            <span style="width: 50%;display: block;">虚拟机类型：</span>
          </p>
          <p slot="content" class="basetext" style="display: flex;">
            <span style="width: 50%;display: block;">额外信息：</span>
          </p>
          <p slot="content" class="basetext" style="display: flex;">
            <span style="width: 50%;display: block;">合约调用方法：</span>
          </p>
        </div>
      </Panel>
      <Panel name="2" style="background:#ECEFFC;display:block;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        合约版本信息
        <div slot="content" class="basetext">
          <Table :columns="dataTitle" :data="dataEos">
            <template slot-scope="{ row, index }" slot="action">
              <Button type="text" size="small" style="margin-right: 5px; color: #3D73EF;border: 1px solid #3D73EF" @click="filecode(row)">文件源码</Button>
              <Button type="text" size="small" style="margin-right: 5px; color: #3D73EF;border: 1px solid #3D73EF" @click="down(row)">文件下载</Button>
            </template>
          </Table>
        </div>

        <!-- 查询合约源码 -->
        <Modal v-model="chaincode" title="查询合约链码" width='900px'>
          <p style="margin-bottom:20px">上传版本号：{{this.title}}</p>
          <div v-if="isSingleCpp=='0'">
            <Layout>
              <Sider hide-trigger :style="{background: '#fff'}">
                <Menu theme="light" width="auto" :open-names="['1']">
                  <Submenu :name="key" v-for="(item,key) in cppsTitle" :key="key">
                    <template slot="title">
                      <Icon type="ios-folder"></Icon>
                      {{key}}
                    </template>
                    <MenuItem :name="items.fileName" v-for="(items,i) in item" :key="i" @click.native="clickCpps(items.content)">{{items.fileName}}</MenuItem>
                  </Submenu>
                </Menu>
              </Sider>
              <Content :style="{padding: ' 0 24px', minHeight: '280px', background: '#fff'}">
                <p>
                  <textarea class="textarea-style" v-html="cppContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
                </p>
              </Content>
            </Layout>
          </div>
          <div v-else>
            <Collapse simple accordion v-if="this.arrDetails.languageType==='C++'">
              <Panel :name="transferKey1" :key="transferKey1">
                {{transferKey1}}
                <p slot="content">
                  <textarea class="textarea-style" v-html="CollContent.cppcentent.fileContent" readonly @scroll="handScroll($event, 'cpp')"></textarea>
                </p>
              </Panel>
              <Panel :name="item" v-for="item in filesHpp" :key='item'>
                {{item}}
                <p slot="content">
                  <textarea class="textarea-style" v-html="CollContent.hppcentent.fileContent" readonly @scroll="handScroll($event, 'hpp')"></textarea>
                </p>
              </Panel>
            </Collapse>
            <Collapse simple accordion v-else>
              <Panel :name="transferKey1" :key="transferKey1">
                {{transferKey1}}
                <p slot="content">
                  <textarea class="textarea-style" v-html="CollContent.jscentent.fileContent" readonly @scroll="handScroll($event, 'js')"></textarea>
                </p>
              </Panel>
              <Panel :name="fileName" v-if="fileName">
                {{fileName}}
                <p slot="content">
                  <textarea class="textarea-style" v-html="CollContent.abicentent.fileContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
                </p>
              </Panel>
            </Collapse>
          </div>

        </Modal>
      </Panel>
    </Collapse>

  </div>
</template>

<script>
import { getChaincode } from '@/api/data'
import { getdownloadFile, getnewContractDetails } from '@/api/contract'

// 使用全局变量存储窗口引用，避免Vue响应式系统处理窗口对象
let globalTargetWin = null;
export default {
  name: 'contractadmin_details',

  data () {
    return {
      fileName: '',
      chaincode: false, // 查询合约链码弹框，
      fileTpye: [],
      CollContent: { cppcentent: {}, hppcentent: {}, jscentent: {}, abicentent: {} },
      codeData: {},
      filesHpp: [],
      transferKey1: '',
      title: '', // 查看文件源码标题
      contractId: this.$route.params.contractId ? this.$route.params.contractId : '', // 合约id
      languagetype: this.$route.params.languagetype ? this.$route.params.languagetype : '', // 语言类型
      arrDetails: {},
      panelValue: ['1', '2'],
      dataEos: [],
      dataTitle: [],
      columnsEos: [
        { key: 'uploadVersion', title: '版本号', tooltip: true },
        { key: 'cppName', title: 'cpp文件名', tooltip: true },
        {
          key: 'hppNames',
          title: 'hpp文件名',
          tooltip: true,
          render: (h, params) => {
            return h('div', params.row.hppNames.join(','))
          }
        },
        {
          key: 'securityScanReportNames',
          title: '安全扫描报告文件名',
          render: (h, params) => {
            return h('div', params.row.securityScanReportNames.join(','))
          },
          tooltip: true
        },
        { key: 'uploadBrief', title: '上传备注', tooltip: true },
        { key: 'uploadTime', title: '上传时间', tooltip: true },
        {
          title: '操作',
          slot: 'action',
          width: 190,
          align: 'left'
        }
      ],
      columnsMaker: [
        {
          title: '版本号',
          key: 'uploadVersion'
        },
        {
          title: '文件名',
          key: 'fileName',
          tooltip: true
        },
        {
          title: '上传者',
          key: 'user',
          tooltip: true
        },
        {
          title: '创建时间',
          key: 'uploadTime',
          tooltip: true
        },
        {
          title: '部署时间',
          key: 'address',
          tooltip: true
        },
        {
          title: '操作',
          slot: 'action',
          width: 190,
          align: 'left'
        }
      ],
      columnsCm: [
        {
          title: '版本号',
          key: 'uploadVersion',
          tooltip: true
        },
        {
          title: 'JavaScript文件名',
          key: 'jsName'
        },
        {
          title: 'abi文件名',
          key: 'abiName'
        },
        {
          title: '安全扫描报告文件名',
          key: 'securityScanReportNames',
          render: (h, params) => {
            return h('div', params.row.securityScanReportNames.join(','))
          }
        },
        {
          title: '上传备注',
          key: 'uploadBrief',
          tooltip: true
        },
        {
          title: '上传时间',
          key: 'uploadTime',
          tooltip: true
        },
        {
          title: '操作',
          slot: 'action',
          width: 190,
          align: 'left'
        }
      ],
      cppContent: '请选择要看的源码文件',
      cppsTitle: '',
      isSingleCpp: '',
      targetWin: '',
      uploadVersion: '',
      downcontractBagName: ''
    }
  },

  mounted () {
    // console.log(this.$route.params)
    if (this.$route.params.contractId) {
      this.dataTitle = this.languagetype === 'C++' ? this.columnsEos : this.languagetype === 'JS' ? this.columnsCm : this.columnsMaker
      getnewContractDetails(this.contractId).then(res => {
        if (res.code !== '00000') this.msgInfo('warning', res.message, true)
        else {
          console.log(res)
          this.arrDetails = res.data
          let listData = res.data.uploadRecords.map(item => {
            return {
              ...item,
              hppFileNames: item.hppFileNames ? item.hppFileNames : [],
              securityScanReportNames: item.securityScanReportNames ? item.securityScanReportNames : []
            }
          })
          this.dataEos = listData
          // console.log(this.dataEos)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    } else {
      this.$router.push({
        name: 'contractadmin_table',
      })
    }

  },

  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },

    // 点击文件源码
    filecode (row) {
      this.isSingleCpp = row.isSingleCpp
      if (row.fileContent) {
        this.cppsTitle = row.fileContent
      }
      // console.log(row.isSingleCpp);
      this.chaincode = true
      this.title = row.uploadVersion
      this.codeData = {
        contractId: JSON.parse(this.contractId),
        uploadVersion: row.uploadVersion
      }
      if (this.languagetype === 'C++') {
        this.transferKey1 = row.cppName
        this.filesHpp = row.hppNames
        this.getCode(row.cppName, 'cpp')
        if (row.hppNames && row.hppNames.length > 0) {
          row.hppNames.forEach(val => this.getNewCode(val, 'hpp'))
        }
      } else {
        this.transferKey1 = row.jsName
        this.fileName = row.abiName
        this.getCode(row.jsName, 'js')
        this.getNewCode(row.abiName, 'abi')
      }
    },
    getCode (fileName) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          if (this.languagetype === 'C++') {
            this.CollContent.cppcentent = res.data
          } else {
            this.CollContent.jscentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    getNewCode (fileName, val) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          if (this.languagetype === 'C++') {
            this.CollContent.hppcentent = res.data
          } else {
            this.CollContent.abicentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 点击折叠面板事件
    // colldata (key) {
    //   if (key[0]) {
    //     this.codeData.fileName = key[0]
    //     getChaincode(this.codeData).then(res => {
    //       if (res.code === '00000') {
    //         this.CollContent = res.data
    //       } else {
    //         this.msgInfo('error', res.message, true)
    //       }
    //     }).catch((error) => {
    //       this.msgInfo('error', error.message, true)
    //     })
    //   }
    // },
    // 滚动
    handScroll (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },

    showBank (func) {
      var iWidth = 700; //模态窗口宽度
      var iHeight = 450;//模态窗口高度
      var iTop = (window.screen.height - iHeight - 100) / 2;
      var iLeft = (window.screen.width - iWidth) / 2;
      var winOption = 'height=' + iHeight + ',innerHeight=' + iHeight + ',width=' + iWidth + ',innerWidth=' + iWidth + ',top=' + iTop + ',left=' + iLeft + ',toolbar=no,menubar=no,scrollbars=no,resizeable=no,location=no,status=no';
      var obj = new Object();
      obj.operCode = localStorage.getItem("sourceCodeEncrypt");
      obj.mainLoginName = "";
      obj.subLoginName = localStorage.getItem("userNameEncrypt")
      obj.appCode = "JTNGCMBAAS";
      obj.sessionId = sessionStorage.getItem("session");
      obj.serverIp = window.location.host.split(":")[0]
      obj.serverPort = window.location.port;
      obj.checkSessionUrl = "/";
      obj.svcNum = "";
      obj.operContent = "";
      // var returnValue;
      //增加浏览器的判断，ie走if原有逻辑，非ie走else逻辑,通过遮罩层实现。
      var a1 = navigator.userAgent;
      var yesIE = a1.search(/Trident/i);
      if (window.ActiveXObject || window.attachEvent || yesIE > 0) { //IE
        // var returnValue = window.showModalDialog("b.html?id=" + new Date(), obj, "dialogHeight:" + iHeight + "px; dialogWidth:" + iWidth + "px; toolbar:no; menubar:no;  titlebar:no; scrollbars:yes; resizable:no; location:no; status:no;left:" + iLeft + "px;top:" + iTop + "px;");
        var me = new Object();
        // me.data = returnValue;
        this.receiveMsg(me);
      } else {  //非IE
        // this.showDiv();//显示遮罩层
        this.openWindowWithPostRequest(iWidth, iHeight, iTop, iLeft, winOption, obj);

        if (window.addEventListener) {
          //为window注册message事件并绑定监听函数
          window.addEventListener('message', this.receiveMsg, false);
        } else {
          window.attachEvent('message', this.receiveMsg);
        }
      }
    },

    openWindowWithPostRequest (iWidth, iHeight, iTop, iLeft, winOption, obj) {
      var winName = "sWindow";
      var winURL = "http://api.it4a.cmit.cmcc:7081/uac/web3/jsp/goldbank/goldbank3!goldBankIframeAction.action";//应用侧对应后台服务action
      var form = document.createElement("form");
      form.setAttribute("method", "post");
      form.setAttribute("action", winURL);
      form.setAttribute("target", winName);
      for (var i in obj) {
        if (obj.hasOwnProperty(i)) {
          var input = document.createElement('input');
          input.type = 'hidden';
          input.name = i;
          input.value = obj[i];
          form.appendChild(input);
        }
      }
      document.body.appendChild(form);
      //打开地址，刚开始时，打开一个不存在的地址，这样才有返回值
      globalTargetWin = window.open("", winName, winOption);
      form.target = winName;
      form.submit();
      document.body.removeChild(form);
      if (window.focus) {
        globalTargetWin.focus();
      }
    },

    //接收返回值后处理函数
    receiveMsg (e) {
      // returnValue = e.data;
      console.log(e, 'eeeeeeeeeeeee');
      if (e.data && typeof e.data === 'string') {
        var dataStatus = e.data.split("#")
        const statusMessageMap = {
          '-3': { message: '金库应急开启中，允许业务继续访问', allowAccess: true },
          '-2': { message: '金库场景或元业务未开启，允许业务继续访问', allowAccess: true },
          '-1': { message: '直接关闭窗口，未申请审批，不允许业务继续访问', allowAccess: false },
          '1': { message: '审批通过，允许业务继续访问', allowAccess: true },
          '0': { message: '审批不通过，不允许业务继续访问', allowAccess: false },
          '2': { message: '超时，允许业务继续访问', allowAccess: true },
          '3': { message: '超时，不允许业务继续访问', allowAccess: false },
          '4': { message: '出现错误或异常（包括数据异常），不允许业务继续访问', allowAccess: false },
          '5': { message: '未配置策略，允许业务继续访问', allowAccess: true },
          '6': { message: '未配置策略，不允许继续访问', allowAccess: false }
        };

        const status = dataStatus[0];
        const statusInfo = statusMessageMap[status];
        console.log(statusInfo, 'statusInfo');
        if (statusInfo) {
          if (statusInfo.allowAccess) {
            // 允许业务继续访问，继续请求接口
            // 这里调用你继续请求接口的函数
            getdownloadFile(JSON.parse(this.contractId), this.arrDetails.contractName, this.uploadVersion, e.data).then(res => {
              let reader = new FileReader();
              reader.readAsText(res);
              reader.onload = () => {
                try {
                  let jsonData = JSON.parse(reader.result);
                  if (jsonData.code === '00000') {
                    // this.showBank()
                    this.msgInfo('warning', jsonData.message, true)
                  } else {
                    this.msgInfo('error', jsonData.message, true)
                  }
                } catch (e) {
                  let blob = new Blob([res], { type: 'application/zip' })
                  let downloadElement = document.createElement('a')
                  let href = window.URL.createObjectURL(blob)
                  downloadElement.href = href
                  downloadElement.download = this.arrDetails.contractName
                  document.body.appendChild(downloadElement)
                  downloadElement.click()
                  document.body.removeChild(downloadElement)
                  window.URL.revokeObjectURL(href)

                }

              };
            }).catch(error => {
              console.log(error);
              this.msgInfo('error', error.message, true)
            })
          } else {
            // 不允许业务继续访问，弹出提示框
            this.msgInfo('warning', statusInfo.message, true);
            // this.closeDiv();
          }
        }
      } else {
        console.error('e.data 不是字符串类型或为空', e.data);
        // this.msgInfo('error', '金库返回数据格式错误', true);
      }


      if (globalTargetWin != null) {
        globalTargetWin.close();
        // this.closeDiv();//关闭遮罩层
      }
    },
    // //回调测试函数
    test1 () {
      alert("test1");
    },
    // 下载
    down (row, treasuryToken = "") {
      this.uploadVersion = row.uploadVersion
      getdownloadFile(JSON.parse(this.contractId), this.arrDetails.contractName, row.uploadVersion, treasuryToken).then((res) => {
        let reader = new FileReader();
        reader.readAsText(res);

        reader.onload = () => {
          try {
            let jsonData = JSON.parse(reader.result);
            console.log(jsonData);

            if (jsonData.code === 'A0314') {
              // this.receiveMsg('3#*************')
              this.showBank()
            } else {
              this.msgInfo('error', jsonData.message, true)

            }
          } catch (e) {
            if (e != 'SecurityError: An attempt was made to break through the security policy of the user agent.') {
              let blob = new Blob([res], { type: 'application/zip' })
              let downloadElement = document.createElement('a')
              let href = window.URL.createObjectURL(blob)
              downloadElement.href = href
              downloadElement.download = this.arrDetails.contractName
              document.body.appendChild(downloadElement)
              downloadElement.click()
              document.body.removeChild(downloadElement)
              window.URL.revokeObjectURL(href)
            }
          }

        };


      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    clickCpps (value) {
      this.cppContent = value

    }
  },
  beforeDestroy () {
    window.removeEventListener('message', this.receiveMsg, false);

    // 确保关闭全局窗口引用
    if (globalTargetWin) {
      try {
        globalTargetWin.close();
      } catch (e) {
        console.error('关闭窗口失败', e);
      }
      globalTargetWin = null;
    }

    clearInterval(this.timer);
    this.timer = null;
  },
}
</script>

<style lang="less" scoped>
/deep/.ivu-menu-submenu-title {
  background: #fff !important;
}
/deep/.ivu-menu,
.ivu-menu-dark,
.ivu-menu-vertical,
.ivu-menu-opened,
.ivu-menu-submenu-title {
  background: #fff !important;
}
.ivu-menu-vertical.ivu-menu-light:after {
  background: #fff;
}
.contract {
  margin: -16px;
  button.btn {
    position: absolute;
    right: 10px;
  }
  .basetext {
    padding-top: 20px;
    span {
      text-align: left;
      margin: 0 26px;
      line-height: 20px;
      word-break: break-all;
    }
  }
}

/deep/.ivu-modal > .ivu-modal-content > .ivu-modal-body {
  max-height: 60vh;
  overflow: auto;
}
/deep/.ivu-upload-drag {
  background-color: #f8f8f9;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.ivu-card {
  background: #f2f6fd;
}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
/deep/.ivu-collapse[name="mainpanel"] {
  border: none;
}
/deep/.ivu-collapse[name="mainpanel"] > .ivu-collapse-item {
  border: none;
  border-radius: 5px 5px;
  margin-bottom: 10px;
}
/deep/.ivu-scroll-container {
  height: auto;
  overflow-y: auto;
}

// 滚动条
.textarea-style {
  width: 100%;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
</style>
