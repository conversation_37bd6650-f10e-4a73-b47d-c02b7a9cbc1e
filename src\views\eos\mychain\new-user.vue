<template>
  <div class="form-wrapper">
    <b style="line-height: 36px;">链账户信息</b>
    <Form ref="formItem" :model="formItem" :rules="ruleForm" label-position="right" :label-width="160" style="margin-right:8%">
      <FormItem label="目标链：" prop="chainId">
        <Tooltip max-width="200" content="创建链账户需要绑定当前所在链" style="margin-left: -18px;">
          <Icon type="md-help-circle" style="font-size:16px;" />
        </Tooltip>
        <Select v-model="formItem.chainId" placeholder="请选择目标链" filterable>
          <Option v-for="item in chainIdList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
        </Select>
      </FormItem>
      <FormItem label="链帐户类型：" prop="accountType">
        <Tooltip max-width="200" content="普通链账户用于发起交易，合约链账户用于部署智能合约" style="margin-left: -18px;">
          <Icon type="md-help-circle" style="font-size:16px;" />
        </Tooltip>
        <RadioGroup v-model="formItem.accountType" @on-change="changeModel">
          <Radio label="NORMAL">普通链账户</Radio>
          <Radio label="CONTRACT">合约链账户</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="链帐户名称：" prop="chainAccountName">
        <Tooltip max-width="200" content="链帐户名称长度支持5-12位,仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字" style="margin-left: -18px;">
          <Icon type="md-help-circle" style="font-size:16px;" />
        </Tooltip>
        <Input style="display:inline-table;" search enter-button="校验" @on-search="handleJudge" v-model="formItem.chainAccountName" :maxlength="12" show-word-limit :placeholder="placeholderChainName" />
      </FormItem>
      <FormItem label="主要业务描述：" prop="description">
        <Input v-model="formItem.description" type="textarea" :maxlength="255" show-word-limit :autosize="{minRows: 3,maxRows: 5}" placeholder="如：解决交通信息数据一致性问题" />
      </FormItem>
      <FormItem label="链账户归属公司：" prop="accountCompanyId">
        <Select v-model="formItem.accountCompanyId" placeholder="请选择链账户归属公司" filterable>
          <Option v-for="item in selectList" :value="item.id" :key="item.id">{{ item.companyName }}</Option>

        </Select>
      </FormItem>

      <div v-if="this.formItem.accountType==='NORMAL'">
        <FormItem label="是否使用内存：" prop="useMemory">
          <Tooltip max-width="200" content="由平台管理员分配内存，计算公式: 使用内存量=单条数据大小*单日数据上链条数*数据保存天数(计算时自行转换单位)， 示例:  每条数据大小1K,每天上链2000条，需保存1个月的数据(转化MB单位如下所示)1*2000* (1*30) =60000KB=58 .59375MB≈60MB
选择否，默认分配20M使用内存量" style="margin-left: -18px;">
            <Icon type="md-help-circle" style="font-size:16px;" />
          </Tooltip>

          <RadioGroup v-model="formItem.useMemory">
            <Radio label="y">是</Radio>
            <Radio label="n">否</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="使用内存量：" prop="useMemoryAmount" class="mandatory" v-if="this.formItem.useMemory==='y'">
          <Input v-model="formItem.useMemoryAmount" :maxlength="17" show-word-limit style="width: 200px" /><Button @click="btn_select">{{this.btn_title}}</Button>
        </FormItem>
        <FormItem label="理由：" prop="reason">
          <Input v-model="formItem.reason" type="textarea" :maxlength="255" show-word-limit :autosize="{minRows: 3,maxRows: 5}" placeholder="请填写理由" />
        </FormItem>
      </div>
      <!-- 是否使用内存 -->

      <FormItem label="权限绑定：" v-show="chainpower">
        <Table :columns="columnsTable" :data="formItem.permissions"></Table>
        <Button @click="addPermission" type="success" ghost long icon="md-add">新建权限</Button>
      </FormItem>
      <div v-show="!chainpower">
        <b style="line-height: 36px;">运维信息</b>
        <FormGroup :key="transferKey" :flag="isFlag" ref="childMethod" :pastOps="JSON.stringify(formItem.ops)"></FormGroup>
      </div>
      <FormItem style="margin:50px auto 0;text-align:center;">
        <Button @click="handleSubmit" icon="md-arrow-round-forward" type="primary">提交</Button>
        <!-- <Button @click="handleReset" style="margin-left: 8px">重置</Button> -->
      </FormItem>
    </Form>
    <Modal v-model="modal" title="新建权限" :draggable="true" sticky :mask-closable="false">
      <Form ref="permission" :rules="permissionRule" :model="objectArr" :label-width="150" @submit.native.prevent>
        <FormItem label="权限名称：" prop="permissionName">
          <Tooltip max-width="200" content="权限长度支持1-12位,仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字" style="margin-left: -18px;">
            <Icon type="md-help-circle" style="font-size:16px;" />
          </Tooltip>
          <Input v-model="objectArr.permissionName" :maxlength="12" show-word-limit placeholder="请填写" style="width:300px;" />
        </FormItem>
        <FormItem label="父权限：" prop="parentPermissionName">
          <Select v-model="objectArr.parentPermissionName" placeholder="请选择" style="width:300px;">
            <Option v-for="item in formItem.permissions" v-show="item.permissionName !== 'owner'" :value="item.permissionName" :key="item.permissionName">{{ item.permissionName }}</Option>
          </Select>
        </FormItem>
      </Form>
      <Form ref="permissionKey" :rules="permissionKeyRule" :model="objectArr" :label-width="150" @submit.native.prevent>
        <FormItem label="密钥生成方式：">
          <i-switch v-model="objectArr.autoGenerateKeyPair" @on-change="change" size="large">
            <span slot="open">自动</span>
            <span slot="close">手动</span>
          </i-switch>
          <Tooltip max-width="200" v-if="!objectArr.autoGenerateKeyPair" content="在钱包节点使用cleos create key --to-console命令创建密钥对">
            <Icon type="md-help-circle" style="font-size:16px;" />
          </Tooltip>
        </FormItem>

        <FormItem label="公钥：" v-show="!objectArr.autoGenerateKeyPair" prop="publicKey">
          <Input v-model="objectArr.publicKey" :maxlength="53" show-word-limit placeholder="请填写" style="width:300px;" />
        </FormItem>
        <FormItem label="私钥：" v-show="!objectArr.autoGenerateKeyPair" prop="privateKey">
          <Input v-model="objectArr.privateKey" :maxlength="51" show-word-limit placeholder="请填写" style="width:300px;" />
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="cancel('permission')">取消</Button>
        <Button type="primary" @click="ok('permission')">确定</Button>
      </div>
      <!-- <div style="line-height:50px;margin-left:30px;">
        <span class="tdstyle">权限名称：
        <Tooltip max-width="200" content="权限长度支持1-12位,仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字" style="margin-left: -8px;">
          <Icon type="md-help-circle" style="font-size:16px;" />
        </Tooltip>
        </span>
        <Input v-model="objectArr.permissionName" :maxlength="12" show-word-limit placeholder="请填写" style="width:300px;" />
      </div>
      <div style="line-height:50px;margin-left:30px;">
        <span class="tdstyle">父权限：</span>
        <Select v-model="objectArr.parentPermissionName" placeholder="请选择" style="width:300px;" >
          <Option v-for="item in formItem.permissions" v-show="item.permissionName !== 'owner'" :value="item.permissionName" :key="item.permissionName">{{ item.permissionName }}</Option>
        </Select>
      </div>
      <div style="line-height:50px;margin-left:30px;">
        <span class="tdstyle">密钥生成方式：</span>
        <i-switch v-model="objectArr.autoGenerateKeyPair" @on-change="change" size="large">
          <span slot="open">自动</span>
          <span slot="close">手动</span>
        </i-switch>
        <div style="line-height:50px;" v-show="!objectArr.autoGenerateKeyPair">
          <span class="tdstyle">公钥：</span>
          <Input v-model="objectArr.publicKey" placeholder="请填写" style="width:300px;" />
        </div>
        <div style="line-height:50px;" v-show="!objectArr.autoGenerateKeyPair">
          <span class="tdstyle">私钥：</span>
          <Input v-model="objectArr.privateKey" placeholder="请填写" style="width:300px;" />
        </div>
      </div> -->
    </Modal>
  </div>
  <!-- <alert-page v-model="modal"></alert-page> -->
</template>

<script>
// import { mapActions } from 'vuex'
import { getChainIdList, checkChainAccountName, addChainNewuser } from '@/api/data'
import { getconfig, getPermission } from '@/api/contract'
import { localSave, localRead } from '@/lib/util'
// import AlertPage from '_c/alert-page'
import FormGroup from '_c/form-group'
import { isPowerAccount } from '../../../lib/check'
import { tenantcompanyList } from '@/api/contract'
let unitConversion = {
  Byte (value) { return value * 1 },
  KB (value) { return value * 1024 },
  MB (value) { return value * 1024 * 1024 },
  GB (value) { return value * 1024 * 1024 * 1024 }
}
export default {
  components: {
    // AlertPage
    FormGroup
  },
  data () {
    const validateAccount = (rule, value, callback) => {
      if (!isPowerAccount(value)) {
        callback(new Error('仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字'))
      } else {
        callback()
      }
    }

    return {
      btn_title: 'MB',
      isFlag: false, // 应用于表单组件提交的
      modal: false,
      chainpower: true,
      transferKey: 0,
      // chainId: this.$route.params.chainId ? this.$route.params.chainId : this.$route.query.chainId,
      objectArr: {
        permissionName: '',
        parentPermissionName: '',
        publicKey: '',
        privateKey: '',
        autoGenerateKeyPair: true
      },
      formItem: {
        chainId: this.$route.params.chainId ? this.$route.params.chainId : this.$route.query.chainId,
        accountType: 'NORMAL',
        useMemory: 'y', // 内存按钮
        reason: '', // 理由
        useMemoryAmount: '', // 内存量
        // chainAccountName: this.$route.params.cppName ? this.$route.params.cppName : this.$route.query.cppName,
        chainAccountName: this.$route.params.cppName ? this.$route.params.cppName : this.$route.query.cppName,
        description: '',
        permissions: [{ 'permissionName': 'owner', 'parentPermissionName': '/', 'publicKey': '/', 'privateKey': '******', 'autoGenerateKeyPair': true }, { 'permissionName': 'active', 'parentPermissionName': 'owner(默认)', 'publicKey': '/', 'privateKey': '******', 'autoGenerateKeyPair': true }],
        ops: null,
        tenantId: '',
        accountCompanyId: ''
      },
      ruleForm: {
        chainId: [{ required: true, message: '目标链不能为空' }],
        accountType: [{ required: true, message: '类型不能为空' }],
        useMemory: [{ required: true, message: '请选择是否使用内存' }], // 内存
        useMemoryAmount: [{ required: true, message: '内存量不能为空' }, { required: true, trigger: 'blur', pattern: /^([1-9]\d*)$/, message: '不能为0且必须为1-9纯数字' }],
        reason: [{ required: true, message: '理由不能为空' }, { type: 'string', max: 255, message: '不能多于255位字符', trigger: 'blur' }],
        chainAccountName: [
          { required: true, pattern: /^(?![.])(?!\d+$)[a-z1-5.]{5,12}(?<![.])$/, message: '不能少于5位或多于12位,仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字', trigger: 'change' },

        ],
        description: [{ type: 'string', max: 255, message: '不能多于255位字符', trigger: 'blur' }],
        accountCompanyId: [{ required: true, message: '链账户归属公司不能为空' }],
      },
      columnsTable: [
        { title: '权限', key: 'permissionName', tooltip: true },
        { title: '父权限', key: 'parentPermissionName', tooltip: true },
        { title: '公钥', key: 'publicKey', tooltip: true },
        { title: '私钥', key: 'privateKey', tooltip: true },
        { // key: 'action',
          title: '操作',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' }, //, disabled: true
                style: {
                  display: params.row.permissionName === 'owner' || params.row.permissionName === 'active' ? 'none' : 'black',
                  marginRight: '8px',
                  color: '#FA5151',
                  border: '1px solid #FA5151'
                },
                on: { click: () => { this.showContractDetails(params.index) } }
              }, '删除')
            ])
          }
        }
      ],
      chainIdList: [],
      selectList: [], // 归属公司
      chainIdPageParam: { pagetotal: 0, pageSize: 60, pageIndex: 1 },
      permissionRule: {
        permissionName: [{ required: true, message: '不能为空', trigger: 'blur' },
        { required: true, trigger: 'blur', validator: validateAccount }],
        parentPermissionName: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      permissionKeyRule: {
        publicKey: [{ required: true, message: '不能为空', trigger: 'blur' },
        { type: 'string', pattern: /^[a-zA-Z0-9]{53}$/, message: '格式有误,长度必须为53位,a-zA-Z0-9', trigger: '  ' }],
        privateKey: [{ required: true, message: '不能为空', trigger: 'blur' },
        { type: 'string', pattern: /^[a-zA-Z0-9]{51}$/, message: '格式有误,长度必须为51位,a-zA-Z0-9', trigger: 'blur' }]
      },
      open: false,//开关
      contract: '',
      normol: '',
      placeholderChainName: '给链账户起个名称'

    }
  },
  methods: {
    // 切换单位
    btn_select () {
      switch (this.btn_title) {
        case 'KB': this.btn_title = 'Byte'
          break
        case 'MB': this.btn_title = 'GB'
          break
        case 'GB': this.btn_title = 'KB'
          break
        case 'Byte': this.btn_title = 'MB'
          break
      }
    },
    addPermission () {
      this.init()
      this.modal = true
      this.$nextTick(() => {
        this.$refs['permission'].resetFields()
      })
      this.$nextTick(() => {
        this.$refs['permissionKey'].resetFields()
      })
    },
    init () {
      this.modal = false
      this.objectArr = { permissionName: '', parentPermissionName: '', publicKey: '', privateKey: '', autoGenerateKeyPair: true }
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    showContractDetails (index) { this.formItem.permissions.splice(index, 1) },
    handleJudge () {

      if (this.formItem.chainId || this.chainId) {
        let chainId = this.formItem.chainId ? this.formItem.chainId : this.chainId
        checkChainAccountName(chainId, this.formItem.chainAccountName).then(res => {
          if (res.code === '00000') {
            this.msgInfo('info', res.message)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        }).catch(error => {
          console.log('checkChainAccountName.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      } else this.msgInfo('warning', '请对目标链进行选择后查询！', true)
    },
    handleSubmit () {
      // 新增判断是否为管理员且选择了租户
      if (!this.$store.state.tenantId && localStorage.getItem('roleId') == 1) {
        this.msgInfo('error', '创建失败，未选择租户，请选择租户后重新尝试', true)
        return
      }
      this.formItem.tenantId = this.$store.state.tenantId
      let res = false
      let byte = unitConversion[this.btn_title](this.formItem.useMemoryAmount)
      let params = {
        chainId: this.formItem.chainId,
        accountType: this.formItem.accountType,
        chainAccountName: this.formItem.chainAccountName,
        description: this.formItem.description,
        accountCompanyId: this.formItem.accountCompanyId,
        useMemory: this.formItem.useMemory,
        useMemoryAmount: byte,
        reason: this.formItem.reason,
        permissions: this.formItem.permissions,
        ops: null,
        tenantId: this.formItem.tenantId,

      }
      this.$refs['formItem'].validate((valid) => {
        if (valid) res = true
        else res = false
      })

      if (this.formItem.accountType === 'NORMAL' && this.normol) {
        if (!this.formItem.chainAccountName.startsWith(this.normol)) {
          this.msgInfo('error', '请输入开头为' + this.normol + '的普通链账户', true)
          return
        }
      }
      if (this.formItem.accountType === 'CONTRACT' && this.contract) {
        if (!this.formItem.chainAccountName.startsWith(this.contract)) {
          this.msgInfo('error', '请输入开头为' + this.contract + '的合约链账户', true)
          return
        }
      }

      if (this.formItem.accountType === 'NORMAL' && res) {
        // console.log('NORMAL~~')
        params.permissions = this.formItem.permissions
      } else if (this.formItem.accountType === 'CONTRACT' && res) {
        // this.formItem.useMemory = '' // 内存按钮
        // this.formItem.reason = '' // 理由
        // this.formItem.useMemoryAmount = '' // 内存量
        params.useMemory = '' // 内存按钮
        params.reason = '' // 理由
        params.useMemoryAmount = '' // 内存量
        const getOps = this.$refs.childMethod.getOps()
        // params.ops = getOps
        if (!getOps) {
          return false
        } else {
          this.isFlag = true
          params.permissions = []
          params.ops = getOps
        }
        // console.log('CONTRACT~~', this.$refs.childMethod.getOps())
      } else return false
      // if (this.formItem.memoryType === 'y' && this.formItem.useMemoryAmount === '') {
      //   this.msgInfo('error', '请填写内存量', true)
      // } else {
      if (localStorage.getItem('roleId') == 1) {
        this.$Modal.confirm({
          content: '<p>请确认所选租户是否正确？</p>',
          onOk: () => {

            // this.formItem.useMemoryAmount = unitConversion[this.btn_title](this.formItem.useMemoryAmount)
            addChainNewuser(this.formItem.chainId, params).then(res => {
              // console.log('addChainNewuser===>', res)
              if (res.code === '00000') {
                localStorage.removeItem('newUser')
                this.$router.push({
                  path: '/role/success',
                  query: {
                    status: res.data.status
                  }
                })
                this.isFlag = false
              } else {
                this.msgInfo('error', res.message, true)
                localSave('newUser', JSON.stringify(this.formItem))
                this.$router.push({
                  path: '/role/fail'
                })
              }
            }).catch(error => {
              this.msgInfo('error', error.message, true)
              localSave('newUser', JSON.stringify(this.formItem))
              this.$router.push({
                path: '/role/fail'
              })
            })
          },
          onCancel: () => {
            // this.$Message.info('Clicked cancel')
          }
        })
      } else {
        // this.formItem.permissions = ''
        // this.formItem.useMemoryAmount = unitConversion[this.btn_title](this.formItem.useMemoryAmount)
        let byte1 = unitConversion[this.btn_title](this.formItem.useMemoryAmount)
        let params1 = {
          chainId: this.formItem.chainId,
          accountType: this.formItem.accountType,
          chainAccountName: this.formItem.chainAccountName,
          description: this.formItem.description,
          useMemory: this.formItem.useMemory,
          accountCompanyId: this.formItem.accountCompanyId,
          useMemoryAmount: byte1,
          reason: this.formItem.reason,
          permissions: this.formItem.permissions,
          ops: null,
          tenantId: this.formItem.tenantId,
        }
        this.$refs['formItem'].validate((valid) => {
          if (valid) res = true
          else res = false
        })

        if (this.formItem.accountType === 'NORMAL' && res) {
          // console.log('NORMAL~~')
          params1.permissions = this.formItem.permissions
        } else if (this.formItem.accountType === 'CONTRACT' && res) {
          // this.formItem.useMemory = '' // 内存按钮
          // this.formItem.reason = '' // 理由
          // this.formItem.useMemoryAmount = '' // 内存量
          params1.useMemory = '' // 内存按钮
          params1.reason = '' // 理由
          params1.useMemoryAmount = '' // 内存量
          const getOps = this.$refs.childMethod.getOps()
          // params.ops = getOps
          if (!getOps) {
            return false
          } else {
            this.isFlag = true
            params1.permissions = []
            params1.ops = getOps
          }
          // console.log('CONTRACT~~', this.$refs.childMethod.getOps())
        } else return false

        addChainNewuser(this.formItem.chainId, params1).then(res => {
          // console.log('addChainNewuser===>', res)
          if (res.code === '00000') {
            localStorage.removeItem('newUser')
            this.$router.push({
              path: '/role/success',
              query: {
                status: res.data.status
              }
            })
            this.isFlag = false
          } else {
            this.msgInfo('error', res.message, true)
            localSave('newUser', JSON.stringify(this.formItem))
            this.$router.push({
              path: '/role/fail'
            })
          }
        }).catch(error => {
          this.msgInfo('error', error.message, true)
          localSave('newUser', JSON.stringify(this.formItem))
          this.$router.push({
            path: '/role/fail'
          })
        })
      }
    },
    ok (val) {
      // if (!this.objectArr.permissionName || !this.objectArr.parentPermissionName) {
      //   this.msgInfo('warning', '权限名称或父权限不能为空！', true)
      //   this.init()
      //   return
      // }
      // if (!this.objectArr.autoGenerateKeyPair) {
      //   if (!this.objectArr.publicKey || !this.objectArr.privateKey) {
      //     this.msgInfo('warning', '当前为手动模式，公钥及密钥不能为空！', true)
      //     this.init()
      //     return
      //   }
      // }
      let power = false
      this.$refs[val].validate((valid) => {
        if (valid) {
          power = valid
        }
      })
      if (!this.objectArr.autoGenerateKeyPair) {
        this.$refs['permissionKey'].validate((validKey) => {
          if (validKey && power) {
            let data = {
              publicKey: this.objectArr.publicKey,
              privateKey: this.objectArr.privateKey
            }
            getPermission(data).then(res => {
              if (res.code === '00000') {
                this.formItem.permissions = this.formItem.permissions.concat(this.objectArr)
                this.init()
              } else {
                this.msgInfo('error', res.message, true)
              }
            }).catch(error => {
              this.msgInfo('error', error.message, true)
            })

          }
        })
      } else if (power) {
        this.objectArr.publicKey = ''
        this.objectArr.privateKey = ''
        this.formItem.permissions = this.formItem.permissions.concat(this.objectArr)
        this.init()
      }
      // console.log('init===>', this.objectArr, this.formItem)
    },
    cancel () { this.init() },
    change (val) {
      this.objectArr.autoGenerateKeyPair = val
      // if (val) {
      //   this.permission.publicKey = ''
      //   this.permission.privateKey = ''
      // }
    },
    // 获取链账户
    getchainuser () {
      let searchItem = {
        chainName: '',
        engineTypeList: [],
        status: undefined,
        chooseTenantId: this.tenantId ? this.tenantId : ''
      }
      getChainIdList(this.chainIdPageParam, searchItem).then(res => {
        this.chainIdList = res.data.records
      }).catch(error => {
        console.log('getChainIdList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
      let listcompany = {
        companyName: ''
      }
      tenantcompanyList(listcompany).then(res => {
        if (res.code === '00000') {
          this.selectList = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    changeModel (e) {

      if (e === 'NORMAL') {

        this.formItem.chainAccountName = this.normol
        this.placeholderChainName = this.normol ? '请输入开头为' + this.normol + '的普通链账户' : '给链账户起个名称'
      } else {
        this.formItem.chainAccountName = this.contract
        this.placeholderChainName = this.contract ? '请输入开头为' + this.contract + '的合约链账户' : '给链账户起个名称'
      }
    }
  },
  watch: {
    formItem: {
      handler () {
        if (this.formItem.accountType === 'CONTRACT') {
          this.chainpower = false
        } else {
          this.chainpower = true
        }

        if (this.formItem.ops) ++this.transferKey
      },
      deep: true,
      immediate: false
    },
    tenantId: function () {
      this.formItem.chainId = ''
      this.getchainuser()
    }
  },
  computed: {
    tenantId () {
      return this.$store.state.tenantId
    }
  },

  mounted () {
    if (!this.$store.state.tenantId && localStorage.getItem('roleId') == 1) {
      this.msgInfo('warning', '您当前还未选择租户，请选择后进行链账户创建', true)
    }
    this.getchainuser()
    if (this.$route.params.cppName || this.$route.query.cppName) {
      this.formItem.accountType = 'CONTRACT'
    } else {
      this.formItem.accountType = 'NORMAL'
    }

    if (localRead('newUser')) {
      this.formItem = JSON.parse(localRead('newUser'))
      this.$refs.childMethod.getOpsFlag()
      localStorage.removeItem('newUser')
    }
    getconfig('NORMOL_PREFIX').then(res => {
      if (res.code === '00000') {
        if (this.formItem.accountType === 'NORMAL') {
          this.formItem.chainAccountName = res.data.value
          this.placeholderChainName = '请输入开头为' + res.data.value + '的普通链账户'

        }

        this.normol = res.data.value

      } else {
        this.normol = ''
      }
    })
    getconfig('CONTRACT_PREFIX').then(res => {
      if (res.code === '00000') {
        if (this.formItem.accountType === 'CONTRACT') {
          this.formItem.chainAccountName = res.data.value
          this.placeholderChainName = '请输入开头为' + res.data.value + '的合约链账户'
        }
        this.contract = res.data.value

      } else {
        this.contract = ''
      }
    })
    if (this.$route.params.account == 'CONTRACT') {
      this.formItem.accountType = 'CONTRACT'

    }

  },


}
</script>

<style lang="less" scoped>
.form-wrapper {
  padding: 20px;
  .ivu-form-item:nth-child(3) .ivu-form-item-content {
    display: flex;
  }
}
.tdstyle {
  display: inline-block;
  width: 100px;
  text-align: right;
}

.mandatory {
  /deep/.ivu-form-item-label::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}
</style>
