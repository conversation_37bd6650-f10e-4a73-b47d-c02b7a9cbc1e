<template>
  <div class="content">
    <CreatHeader></CreatHeader>
    <div class="base" v-if="isShowLastStep">
      <div class="title">
        <img :src="infoIcon" class="image">
        <span class="text">基本参数设置</span>
      </div>
      <div class="from">
        <div class="listWrap">
          <div class="list">
            <span class="label">区块链名称：</span>
            <el-input v-model="form.ServiceName" maxLength="16" placeholder="4-16位字符，支持中文、英文或数字" @blur="checkServiceName(form.ServiceName)"></el-input>
            <span class="required">*</span>
            <div class="error" v-show="isShowErrorText">区块链名称格式有误</div>
            <div class="error" v-show="isShowErrorLength">区块链名称长度不小于4个字符</div>
            <div class="error" v-show="isShowNameError">请输入区块链名称</div>
          </div>
        </div>
        <div class="listWrap">
          <div class="list">
            <span class="label">区块链版本：</span>
            <el-select v-model="form.Version" @blur="checkVersion(form.Version)" @change="checkVersion(form.Version)">
              <el-option :label="item.Version" :value="item.Version" v-for="(item,index) in versionList" :key="index" ></el-option>
            </el-select>
            <span class="required">*</span>
            <div class="error" v-show="isShowVersion">请选择区块链版本</div>
          </div>
        </div>
        <div class="list">
          <span class="label">安全机制：</span>
          <el-select v-model="form.CryptoType">
            <el-option label="国密" value="SM2"></el-option>
            <el-option label="ECDSA" value="ECDSA"></el-option>
          </el-select>
          <span class="required">*</span>
        </div>
        <div class="list">
          <span class="label">共识方式：</span>
          <el-select v-model="form.DeployTemplate" @change="getChainType">
            <el-option label="SOLO" value="SOLO"></el-option>
            <el-option label="KAFKA" value="KAFKA"></el-option>
            <!-- <el-option label="BFT" value="BFT"></el-option> -->
          </el-select>
          <span class="required">*</span>
        </div>
        <div class="list">
          <span class="label">等保级别：</span>
          <el-select v-model="form.level">
            <el-option label="等保3级" value="等保3级"></el-option>
            <el-option label="等保2级" value="等保2级"></el-option>
          </el-select>
          <span class="required">*</span>
        </div>
        <div class="listWrap">
          <div class="list">
            <span class="label">集群名称：</span>
            <el-select v-model="clusterObj" value-key="ClusterId" @blur="checkClusterName(clusterObj)" @change="checkClusterName(clusterObj)">
              <el-option :label="item.ClusterName" :value="item" v-for="(item,index) in clusterList" :key="index"></el-option>
            </el-select>
            <span class="required">*</span>
            <div class="error" v-show="isShowClusterName">请选择集群名称</div>
          </div>
        </div>
        <div class="list">
          <span class="label">组织数量：</span>
          <el-select v-model="form.OrgCount" @change="getOrgPeerCount">
            <el-option label="1" value="1"></el-option>
            <el-option label="2" value="2"></el-option>
            <el-option label="3" value="3"></el-option>
            <el-option label="4" value="4"></el-option>
          </el-select>
          <span class="required">*</span>
        </div>
        <div class="list">
          <span class="label">Orderer节点数量：</span>
          <el-select v-model="form.OrdererCount" :disabled="form.DeployTemplate == 'SOLO'">
            <el-option label="1" value="1" v-if="form.DeployTemplate != 'KAFKA'"></el-option>
            <el-option label="2" value="2"></el-option>
            <el-option label="3" value="3"></el-option>
            <el-option label="4" value="4"></el-option>
          </el-select>
          <span class="required">*</span>
        </div>
        <div class="listWrap">
          <div class="list" v-for="(item,index) in orgPeerCount" :key="index">
            <span class="label">组织{{index  + 1}}Peer节点数量：</span>
            <el-input v-model.trim="orgPeerCount[index].PeerCount" placeholder="最多可设置4个Peer节点" @blur="getMaxNum(index)" onkeyup="this.value=this.value.replace(/\D|^0/g,'')"></el-input>
            <span class="required">*</span>
            <div class="error"  v-show="isShowError && isNaN(orgPeerCount[index].PeerCount)">只能输入数字</div>
            <div class="error"  v-show="isShowError && !orgPeerCount[index].PeerCount">请输入Peer节点数量</div>
            <div class="error"  v-show="isShowError && Number(orgPeerCount[index].PeerCount) > 4">最多可设置4个Peer节点</div>
          </div>
        </div>

      </div>
    </div>
    <div class="base" v-if="isShowNextStep">
      <div class="title">
        <img :src="infoIcon" class="image">
        <span class="text">Orderer节点设置</span>
      </div>
      <div class="from">
        <div class="list">
          <span class="label ordername">节点名称</span>
          <span class="text">主机设置</span>
          <span class="required"></span>
        </div>
        <div class="list" v-for="(item,index) in orderList" :key="index">
          <span class="label">{{item.key}}：</span>
          <span class="text">
            <el-select v-model="orderList[index].value" @focus="getK8SNodeList">
              <el-option :label="item.Name" :value="item.Name" v-for="(item,cindex) in orderAndPeer" :key="cindex"></el-option>
            </el-select>
          </span>
          <span class="required">*</span>
        </div>
      </div>
    </div>
    <div class="base" v-if="isShowNextStep">
      <div class="title">
        <img :src="infoIcon" class="image">
        <span class="text">组织节点设置</span>
      </div>
      <div class="from">
        <div v-for="(item,index) in peerNames" :key="index">
          <div class="list org">
            <span class="label orgname">组织{{index+1}}名称：</span>
            <span class="text orgtext">{{item.Name}}</span>
            <span class="required"></span>
          </div>
          <div class="list">
            <span class="label name">节点名称</span>
            <span class="text">主机设置</span>
            <span class="required"></span>
          </div>
          <div class="list" v-for="(citem,cindex) in peerNames[index].list" :key="cindex">
            <span class="label">
              <el-popover trigger="hover" placement="top" class="label">
                <p>{{citem.key}}</p>
                <div slot="reference" class="name-wrapper">
                  <span slot="reference" class="label">{{checkName(citem.key)}}：</span>
                </div>
              </el-popover>
            </span>
            <span class="text">
              <el-select v-model="peerNames[index].list[cindex].value" @focus="getK8SNodeList" @change="peerChange">
                <el-option :label="item.Name" :value="item.Name" v-for="(item,optionindex) in orderAndPeer" :key="optionindex"></el-option>
              </el-select>
            </span>
            <span class="required">*</span>
          </div>
          <div class="line" v-show="peerNames.length >1 && (index + 1) != peerNames.length"></div>
        </div>
      </div>
    </div>
    <div class="btn-wrap">
      <el-button plain @click="getLastStep">上一步</el-button>
      <el-button type="primary" @click="getNextStep" v-if="!isShowNextStep">下一步</el-button>
      <el-button type="primary" @click="goDeploy"  v-if="isShowNextStep">{{loading?'部署中':'部署'}}</el-button>
    </div>
    <div v-if="loading" class="BoxLoading" v-loading="loading" element-loading-text="部署中"></div>
    <!-- <countDown v-if="isShowIcon" :state="countState" :countTime="countTime" :text="countText" @getCountDown="getCountDown"></countDown> -->
  </div>
</template>

<script>
import {getSupportFabricVersionList,getChainPredefinedInfo,getK8SNodeList,deployNewChain} from '@/api/baascore/creatChain'
import { getClusterList } from "@/api/baascore/overview";
import CreatHeader from './header'
export default {
  components: {
    CreatHeader
  },
  data() {
    return {
      countState:'',
      countTime:2,
      countText:'',
      isShowIcon:false,
      infoIcon:require('@/assets/chainManage_images/overview/infoIcon.png'),
      form:{
        level:'等保3级',
        ServiceName:'',//名称
        Version:'',//版本号
        CryptoType:'ECDSA', //安全机制
        DeployTemplate:'SOLO',
        OrgCount:1, //组织数量
        OrdererCount:1,
      },
      isShowLastStep:true,
      isShowNextStep:false,
      clusterObj:{},
      versionList:[],
      clusterList:[],
      orgPeerCount:[{
        Name:'',
        PeerCount:''
      }],
      orderAndPeer:[],
      orderList:[],
      peerNames:[],
      deployObj:{},
      isShowErrorText:false,
      isShowErrorLength:false,
      loading:false,
      isShowError:false,
      isShowNameError:false,
      isShowVersion:false,
      isShowClusterName:false,
      activeIndex:'',
    };
  },
  computed: {
    checkIsNumber(value) {
      return function(value) {
        let re = /^[0-9]*$/;
        if(!re.test(value) && value != '') {
          return true
        }else{
          return false
        }
      }
    },
    checkName(name) {
      return function(name) {
        if(name.indexOf('-') != -1) {
          var strList = name.split('-')
          name = strList[0]
          return name
        }else {
          return name
        }
      }
    },
  },
  mounted() {
    var form = JSON.parse(sessionStorage.getItem('cusform'))
    var clusterObj = JSON.parse(sessionStorage.getItem('cusCluster'))
    var prList = JSON.parse(sessionStorage.getItem('cusList'))
    if(form && clusterObj) {
      this.form = form
      this.clusterObj = clusterObj
      this.orgPeerCount = prList
    }
    this.getSupportFabricVersionList()
    this.getClusterList()
  },
  methods: {
    getCountDown(type) {
      this.isShowIcon = false
    },
    checkClusterName(name) {
      var ClusterName = name.ClusterName
      if(!ClusterName) {
        this.isShowClusterName = true
      } else {
        this.isShowClusterName = false
      }
    },
    checkVersion(value) {
      if(!value) {
        this.isShowVersion = true
      }else {
        this.isShowVersion = false
      }
    },
    checkServiceName(value) {
        var re =  /^(?![0-9]+$)[0-9a-z]{4,16}$/
        if(value == '') {
          this.isShowNameError = true
        }else {
          this.isShowNameError = false
        }
        if(value != '' && !re.test(value)) {
          this.isShowErrorText = true
        }else {
          this.isShowErrorText = false
        }
        if(re.test(value) && value.length<4) {
          this.isShowErrorLength = true
        } else {
          this.isShowErrorLength = false
        }
    },
    peerChange(value) {
      this.$forceUpdate()
    },
    getK8SNodeList() {
      if(this.orderAndPeer.length > 0) {
        return
      }
      var params = {
        ClusterId:this.clusterObj.ClusterId
      }
      getK8SNodeList(params).then(res =>{
        if(res.status == 200) {
          this.orderAndPeer = res.data.Hosts
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
        }else{
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      })
    },
    getMaxNum(index) {
      this.isShowError = true
    },
    getOrgPeerCount(value) {
      this.isShowError = false
      this.orgPeerCount = []
      var OrgCount = Number(value)
      var OrgsObj = {}
      for(let i = 0; i<OrgCount;i++) {
          OrgsObj = {
          Name:'org' + (i+1),
          PeerCount:''
        }
        this.orgPeerCount.push(OrgsObj)
      }
    },
    getChainType(value) {
      sessionStorage.setItem('chainType',(value+'_CMRI'))
      if(value == 'KAFKA') {
        this.form.OrdererCount = 2
      } else {
        this.form.OrdererCount = 1
      }
    },
    getSupportFabricVersionList() {
      getSupportFabricVersionList().then(res =>{
        if(res.status == '200') {
          this.versionList = res.data
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
        } else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      })
    },
    getClusterList() {
      getClusterList().then(res =>{
        if(res.status == '200') {
          this.clusterList = res.data
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
        }else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      })
    },
    getLastStep() {
      if(this.isShowLastStep) {
        this.$router.go(-1)
      }
      this.orderList = []
      this.loading = false
      this.isShowNextStep = false
      this.isShowLastStep = true
    },
    getNextStep() {
      var orgPeerCount = this.orgPeerCount
      var isOk = true
      orgPeerCount.forEach((item,index) =>{
        this.getMaxNum()
        if(Number(item.PeerCount) > 4 || item.PeerCount == '' || isNaN(item.PeerCount)) {
          isOk = false
        }
      })
      this.checkClusterName(this.clusterObj)
      this.checkVersion(this.form.Version)
      this.checkServiceName(this.form.ServiceName)
      if(this.isShowErrorText || this.isShowErrorLength || this.isShowVersion || this.isShowClusterName || this.isShowNameError) {
        return
      }
      this.orderList = []
      if(this.form.ServiceName && isOk && this.form.Version && this.clusterObj.ClusterName && this.form.DeployTemplate) {
        sessionStorage.setItem('cusform',JSON.stringify(this.form))
        sessionStorage.setItem('cusCluster',JSON.stringify(this.clusterObj))
        sessionStorage.setItem('cusList',JSON.stringify(this.orgPeerCount))
        var Orgs = []
        var OrgsObj = {}
        for(let i = 0; i<orgPeerCount.length;i++) {
           OrgsObj = {
            Name:'org' + (i+1),
            PeerCount:Number(orgPeerCount[i].PeerCount)
          }
          Orgs.push(OrgsObj)
        }
        var data = {
         // msgType:'getChainPredefinedInfo',
          //params:{
            Orgs,
            ServiceName:this.form.ServiceName,
            ClusterName:this.clusterObj.ClusterName,
            DeployTemplate:this.form.DeployTemplate + '_CMRI',
            OrdererCount:Number(this.form.OrdererCount),
            CryptoType:this.form.CryptoType,
            Version:this.form.Version,
         // }
        }
        getChainPredefinedInfo(data).then(res =>{
          if(res.status == 200) {
            // this.isShowIcon = true
            // this.countState = 'success'
            // this.countText = '请求成功！'
            this.deployObj = res.data
            var obj = res.data.NodeDeploy.commons
            var peerNames = res.data.DeployInfo.PeerOrgs
            var peerObj = res.data.NodeDeploy
            var cobj = {}
            //PEER
            peerNames.forEach(item =>{
              var bkeys = Object.keys(peerObj).sort()
              bkeys.forEach(bitem =>{
                if(item.Name == bitem) {
                  var peerList = []
                  var keys = Object.keys(peerObj[bitem]).sort()
                  keys.forEach(citem =>{
                    cobj = {
                      key:citem,
                      value:peerObj[bitem][citem]
                    }
                    peerList.push(cobj)
                    item.list = peerList
                  })
                }
              })
            })
            this.peerNames = peerNames
            //order
            var orderKey = Object.keys(obj).sort()
             orderKey.forEach(item =>{
              let cobj = {
                key : item,
                value:obj[item]
              }
              this.orderList.push(cobj)
            })
            this.isShowLastStep = false
            this.isShowNextStep = true
            document.body.scrollTop = 0;
            document.documentElement.scrollTop = 0;
          }else if(res.status == 4102) {
            //this.$message.error(res.message)
            //this.$message.error("区块链名称已存在，请重新设置。")
            this.isShowReName = true
          } 
          else {
            // this.isShowIcon = true
            // this.countState = 'error'
            // this.countText = '数据获取失败，请重新加载！'
            this.$message.error('数据获取失败，请重新加载！')
          }
        })
      }
    },
    goDeploy() {
      this.loading = true
      this.$emit('getChildLoading',true)
      var object = JSON.parse(JSON.stringify(this.deployObj))
      var orderNames = this.orderList
      orderNames.forEach(item =>{
        object.NodeDeploy.commons[item.key] = item.value
      })
      var peerList = this.peerNames
      peerList.forEach(item =>{
        item.list.forEach(citem =>{
          object.NodeDeploy[item.Name][citem.key]  = citem.value
        })
      })
      object.ClusterName = this.clusterObj.ClusterName
      object.DeployTemplate = this.form.DeployTemplate +'_CMRI'
      object.DeployInfo.PeerOrgs.forEach((item) =>{
        delete item.list
      })
      var params = Object.assign(this.form,object)
      // var data = {
      //   msgType:'deployNewChain',
      //   params,
      // }
      deployNewChain(params).then(res =>{
        //this.loading = false
        if(res.status == 200) {
          var ServiceId = res.data
          sessionStorage.setItem('ServiceId',ServiceId)
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '部署成功！'
          //this.$message.success('部署成功！')
          this.$router.push({
            path:'/guide/creatChain/deployChain',
          })
        }else if(res.status == 4102) {
          this.loading = false
          this.$emit('getChildLoading',false)
          this.$message.error("区块链名称已存在，请重新设置。")
        } else {
          this.loading = false
          this.$emit('getChildLoading',false)
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '部署失败，请检查网络！'
          this.$message.error('部署失败，请检查网络！')
        }
      }).catch(err => {
          this.loading = false
          this.$emit('getChildLoading',false)
          //this.$message.error('网络异常,请检查网络')
        });
    }
  }
}
</script>

<style lang="less" scoped>
  .content {
    width: 100%;
    //margin-top: 70px;
    .base {
      margin-top: 26px;
      .title {
        .image {
          width: 6px;
          height: 20px;
          vertical-align: middle;
        }
        .text {
          // font-size: 20px;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #333333;
          vertical-align: middle;
        }
      }
      .from {
        margin: 26px 0 0px;
        padding: 20px 0 50px;
        width:100%;
        background: #FFFFFF;
        // border: 2px solid rgba(228, 227, 227, 0.22);
        // box-shadow: 0px 4px 10px 0px rgba(218, 218, 218, 0.17);
        // border-radius: 4px;
        .listWrap {
          position: relative;
        }
        .error {
          color: #f56c6c;
          position: absolute;
          top: 24px;
          left:780px;
          // font-size: 17px;
          font-size: 14px;
          &.active {
            top: 20px;
          }
        }
        .list {
          position: relative;
          width: 100%;
          display: flex;
          align-items: center;
          //justify-content: center;
          margin-top: 30px;
          padding-left:38px ;
          color: #333;
          //margin-left: -50px;
          &.org {
            color: #00ADA2;
          }
          .label {
            width: 180px;
            // font-size: 17px;
            font-size: 14px;
            text-align: left;
            &.ordername {
              width: 130px;
            }
            .label {
              display: inline-block;
              text-align: left;
              span {
                //padding-right:14px;
              }
            }
            &.name {
              width: 128px;
            }
          }
          .text {
            width: 500px;
            // font-size: 17px;
            font-size: 14px;
            color: #333333;
            text-align: center;
            margin-right:20px;
            &.orgtext {
              text-align: left;
              color: #00ADA2;
            }
          }
          .el-input {
            width: 500px;
            height: 56px;
            margin-right: 20px;
            // font-size: 17px;
            font-size: 14px;
            /deep/.el-input--medium .el-input__inner {
              height: 56px;
            }
          }
          .el-select {
            margin-right: 20px;
            //margin-left: 4px;
            // font-size: 17px;
            font-size: 14px;
          }
          /deep/ .el-input--medium .el-input__inner{
            width: 500px;
            height: 56px;
            // font-size: 17px;
            font-size: 14px;
          }
          .required {
            color: #FF3A4C;
            // font-size: 40px;
            font-size: 14px;
            line-height: 40px;
            margin-top: 18px;
          }
        }
        .line {
          margin:40px auto;
          width: 95%;
          height: 1Px;
          background: #DFDFDF;
        }
      }
    }
    .btn-wrap {
      background: #fff;
      padding: 0px 0 50px 218px;
      text-align: center;
      display: flex;
      //justify-content: center;
      .last-step {
        width: 210px;
        height: 64px;
        background: #fff;
        border: 3px solid #E7ECEF;
        border-radius: 4px;
        // font-size: 22px;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 58px;
        cursor: pointer;
        margin-right: 180px;
      }
      .next-step {
        width: 192px;
        height: 64px;
        background: #337DFF;
        border: 2px solid;
        border-radius: 4px;
        // font-size: 22px;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 58px;
        cursor: pointer;
      }
    }
  }
.el-loading-spinner /deep/ i{
  // font-size: 40px;
  font-size: 16px;
}
.el-loading-spinner /deep/.el-loading-text {
  // font-size: 20px;
  font-size: 14px;
}
.BoxLoading{
  position:fixed;
  top:0;
  right:0;
  bottom:0;
  left:0;
  z-index: 10000
}
.BoxLoading /deep/ .el-loading-mask{
  background: rgba(0, 0, 0, 0.2) !important;
}
.BoxLoading /deep/ .el-loading-spinner .circular{
  width:60px !important;
  height: 60px !important;
}
.BoxLoading /deep/ .el-loading-spinner .path{
  stroke:#fff !important;
}
.BoxLoading /deep/ .el-loading-spinner .el-loading-text{
  color:#fff !important;
  // font-size:24px;
  font-size: 14px;
  font-weight:bold;
}
</style>
