<template>
<div>
      <div style="padding:10px 0px 35px 10px;">
      <div style="float:right;" class="input-search">
          <Input
            prefix="ios-search"
              type="text"
              style="width:400px;margin-right:-1px;"
              v-model="searchData"
              placeholder="区块高度/交易哈希"
              @keyup.enter.native="browserBlur(searchData)">
          </Input>
          <Button type="primary" style="padding:2px;width:75px;height:31px;" @click="browserBlur(searchData)"> 搜索</Button>
      </div>
      </div>
      <div v-show="!isShow">
        <Card class="back" :bordered="false" style="margin-top:10px;padding-left:15px;">
            <div class="title"><div class="bs"></div><div>区块信息</div></div>
            <p class="title1" style="padding:20px 0 15px 0;">
              <span class="title1">区块高度：</span>
              <Button :disabled="+blockNum <= 0" :class="bt1" @click.native="handleSufix(blockNum)"><Icon type="ios-arrow-back" style="margin-left:-6px;color:#fff"></Icon></Button>
              <span class="icon-title">{{blockNum}}</span>
              <Button :class="bt2" :disabled="+blockNum >= maxBlockNum" @click.native="handlePrefix(blockNum)"><Icon type="ios-arrow-forward" style="margin-left:-6px;color:#fff"></Icon></Button>
            </p>
            <p class="title2"> <span class="title1">区块哈希：</span>{{blockHash}}</p>
            <p class="title2"> <span class="title1">交易数：</span>{{txCount}}</p>
            <p class="title2"> <span class="title1">时间：</span>{{blockTimestamp}}</p>
            <p class="title2"> <span class="title1">提案组织：</span>{{proposerOrg}}</p>
            <p class="title2"> <span class="title1">版本：</span> {{blockVersion}}</p>
            <p class="title2"> <span class="title1">签名：</span>{{signature}}</p>
          </Card>
          <div class="login_header">
              <div @click="cur=0" :class="{active:cur===0}" class="login_header_1">
                <img :src="cur === 0 ? logo1s : logo1" class="logo"/>
                <span>交易</span>
              </div>
              <div @click="cur=1" :class="{active:cur===1}" class="login_header_2">
                <img :src="cur === 1 ? logo3 : logo3s" class="logo">
                <span>区块头</span>
              </div>
            </div>
            <div v-show="cur===0" class="tab-1">
              <edit-table-mul :columns="columns" v-model="blockTableData" :key="transferKey"></edit-table-mul>
              <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;padding-top:10px;"/>
            </div>
            <div class="announce scroll" v-show="cur===1" v-if="blockHeader">
              <json-viewer :value="blockHeader" :copyable="copyable"></json-viewer>
            </div>
            <div v-else class="tab-1" style="text-align:center;border-bottom: 1px solid #eeecec" v-show="cur===1">暂无数据</div>
        </div>
        <div v-show="isShow" class="show-style">
           <img class="imgs" :src="showUrl">
           <p class="msg-style">{{showMsg}}</p>
        </div>
</div>
</template>
<script>
import EditTableMul from '_c/edit-table-mul'
import { chainMakerBrowser, chainMakerBlockInfo } from '@/api/data'
import { isCmBlockNum, isTrxId } from '@/lib/check'
import JsonViewer from 'vue-json-viewer'
// import { mapActions } from 'vuex'
export default {
  name: 'chainMaker_block',
  components: {
    EditTableMul,
    JsonViewer
  },
  data () {
    return {
      isShow: false,
      showUrl: require('@/assets/img/null.png'),
      showMsg: '',
      logo1: require('@/assets/img/browser/logo2.png'),
      logo1s: require('@/assets/img/browser/logo2s.png'),
      logo3: require('@/assets/img/chainmaker/block.png'),
      logo3s: require('@/assets/img/chainmaker/block-1.png'),
      copyable: { copyText: '复制', copiedText: '已复制' },
      cur: 0,
      iconColor: '#fff',
      searchData: '',
      hisPath: 'chainMaker_index',
      blockNum: this.$route.query.blockNum || '',
      maxBlockNum: 0,
      blockHash: '',
      txCount: '',
      blockVersion: '',
      blockTimestamp: '',
      proposerOrg: '',
      signature: '',
      blockHeader: null,
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      transferKey: 0,
      columns: [
        {
          key: 'txId',
          title: '交易哈希',
          minWidth: 320,
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  color: 'blue',
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    this.handleGo(params.row.txId)
                  }
                }
              }, params.row.txId)
            ])
          }
        },
        { key: 'timestamp',
          title: '交易时间戳' },
        { key: 'result',
          title: '交易执行结果',
          minWidth: 200,
          render: (h, params) => {
            if (params.row.result) {
              return h('div', [
                h(JsonViewer, {
                  props: {
                    value: params.row.result,
                    copyable: this.copyable,
                    expandDepth: 0
                  },
                  style: { background: 'transparent' }
                })
              ])
            }
          } }
      ],
      blockTableData: [],
      bt1: 'bt1',
      bt2: 'bt2'
    }
  },
  computed: {
  },
  methods: {
    browserBlur (val) {
      if (isCmBlockNum(val)) {
        // this.blockNum = val
        this.getBlockData(val)
      } else if (isTrxId(val)) {
        this.$router.push({
          name: 'chainMaker_trade',
          query: {
            txId: val,
            tag: true
          }
        })
      } else {
        if (val === '') {
          this.msgInfo('warning', '未输入任何查询信息，请检查！', true)
          this.showMsg = '未输入任何查询信息，请检查！'
        } else {
          this.msgInfo('warning', '输入信息有误，请检查！', true)
          this.showMsg = '输入信息有误，请检查！'
        }
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getBlockData(this.blockNum)
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getBlockData(this.blockNum)
    },
    getBlockData (value) {
      chainMakerBlockInfo(value, this.tablePageParam.pageIndex, this.tablePageParam.pageSize).then(res => {
        if (res.code === '00000') {
          this.isShow = false
          this.blockNum = res.data.blockHeight ? res.data.blockHeight : 0
          this.blockHash = res.data.blockHash ? res.data.blockHash : '无记录'
          this.blockTimestamp = res.data.blockTimestamp ? res.data.blockTimestamp : '无记录'
          this.txCount = res.data.txCount ? res.data.txCount : 0
          this.signature = res.data.signature ? res.data.signature : '无记录'
          this.proposerOrg = res.data.proposerOrg ? res.data.proposerOrg : ''
          this.previous = res.data.previous ? res.data.previous : '无记录'
          this.blockVersion = res.data.blockVersion ? res.data.blockVersion : '无记录'
          this.blockHeader = res.data.blockHeader ? res.data.blockHeader : null
          this.blockTableData = res.data.txs
          this.tablePageParam.pagetotal = res.data.total
          ++this.transferKey
          this.getBrowserData()
        } else {
          this.isShow = true
          this.msgInfo('error', res.message, true)
          this.showMsg = '区块高度[' + value + ']，' + res.message
        }
      }).catch(error => {
        this.isShow = true
        this.showMsg = '区块高度[' + value + ']，' + error.message
        this.msgInfo('error', error.message, true)
      })
    },
    handleSufix (value) {
      this.getBlockData(+value - 1)
    },
    handlePrefix (value) {
      this.getBlockData(+value + 1)
    },
    handleGo (value) {
      this.$router.push({
        name: 'chainMaker_trade',
        query: {
          txId: value
        }
      })
    },
    reback () {
      this.$router.push({
        name: 'chainMaker_index'
      })
    },
    getBrowserData () {
      chainMakerBrowser().then(res => {
        if (res.code === '00000') {
          this.maxBlockNum = res.data.blockHeight ? res.data.blockHeight : 0
          this.bt1 = this.blockNum <= 0 ? 'bt1 bts' : 'bt1'
          this.bt2 = this.blockNum >= this.maxBlockNum ? 'bt2 bts' : 'bt2'
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  mounted () {
    if (this.$route.query.blockNum) {
      if (this.$route.query.tag) {
        this.searchData = this.$route.query.blockNum + ''
      }
      this.getBlockData(this.$route.query.blockNum)
    } else {
      this.reback()
    }
  },
  beforeRouteEnter (to, from, next) {
    if (from.name) {
      next(vm => {
        vm.hisPath = from.name
      })
    } else {
      next('/chainMaker_index')
    }
  }
}
</script>
<style lang="less" scoped>
.active {
  color: #3f7dff;
  padding-bottom: 10px;
  border-bottom: 3px solid #3f7dff;
  cursor: pointer;
}
.size{
  font-weight: bold;
  font-size:16px;
}
.title{
    .size;
    margin-top:5px;
    height:18px;;
    font-family: 'Microsoft YaHei';
    line-height: 18px;
    color: #333333;
    vertical-align: middle;
  }
.bs{
    float:left;
    width: 6px;
    height: 18px;
    background: #19C3A0;
    opacity: 1;
    border-radius: 3px;
    margin-right:6px;
}

.title2{
    font-size:14px;
    font-family: MicrosoftYaHei;
    color:#333333;
    opacity: 1;
    margin-bottom:5px;
    margin-left: 3.0em;
    text-indent: -3.0em;
    word-break: break-all;
}
.title1{
  font-size:14px;
  color:#9B9B9B;
  padding:0 0 5px 0;
  }
.icon-style{
   font-size:12px;
   padding: 17px 4px 8px 4px;
   border: 1px solid #e0e0e0;
   cursor: pointer;
}

.icon-title{
    padding:0px 15px 0px 15px;
    background: #EDF0FC;
    font-family: 'D-DIN';
    vertical-align: middle;
    height: 34px;
    font-size: 30px;
    font-weight: bold;
    line-height: 23px;
    color: #3D73EF;
    letter-spacing: 1px;
    opacity: 1;
   }
.announce{
    .tab-1;
    height:400px;
    overflow-y:hidden;
    overflow-x:hidden;
  }
.scroll{
     overflow-y:auto;
     overflow-x:hidden;
   }
  .scroll::-webkit-scrollbar{
    width : 5px;  /*高宽分别对应横竖滚动条的尺寸*/
    min-height: 1px;
  }
  .scroll::-webkit-scrollbar-thumb{
    border-radius   : 10px;
    background-color: rgb(135, 158, 235);
  }
 ul li{
   word-break:break-all;
   margin-left:5px;
 }
 .login_header{
    margin-top:20px;
    height:72px;
    font-size:16px;
    background-image:url('../../assets/img/browser/head.png');
    background-repeat:no-repeat;
    background-size:100% 72px;
      .login_header_1{
      margin-right:30px;
      cursor:pointer;
      font-weight:bold;
      width:65px;
      display:inline-block;
      margin-left:30px;
    }
    .login_header_2{
      cursor:pointer;
      font-weight:bold;
      padding-top:15px;
      width:85px;
      display:inline-block;
    }
    .logo{
      cursor:pointer;
      margin-right:10px;
      vertical-align:middle;
    }
}
.tab-1{
    width:100%;
    height:100%;
    background: #FFFFFF;
    opacity: 1;
    border-radius: 22px 22px 0px 0px;
    border: 1px solid #a5a4bf17;
    margin-top:-20px;
    padding:20px;
  }
/deep/.ivu-table-tip{
  overflow: hidden;
}
.back{
    background-image:url('../../assets/img/browser/block.png');
    background-repeat:no-repeat;
    background-size:100% 100%;
  }
.show-style{
  display: table;
  text-align: center;
  vertical-align: middle;
  margin:0 auto;
  position: relative;
  padding:8% 0 8% 0;
  .msg-style{
    color:#b7b8b9;
    font-size:12px;
  }
}
.bt1 {
    border-radius: 6px 0 0 6px;
    width:8px;
    height:40px;
    background-color:#2D8CF0;
  }
.bt2 {
  border-radius: 0 6px 6px 0;
  width:8px;
  height:40px;
  background-color:#2D8CF0;
}
.bts {
  background-color: #B7C0D6;
}

/deep/.input-search {
  .ivu-btn{
    border-radius: 0 4px 4px 0;
  }
  .ivu-input{
     border-radius: 4px 0 0 4px;
  }
}
</style>
