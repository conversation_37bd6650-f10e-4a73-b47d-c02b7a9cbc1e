<template>
<div>
      <div style="padding:20px 10px 40px 10px;">
      <div style="float:left;" class="select-option">
        <Select filterable :class="className" @on-open-change="selectClassName" v-model="chain.chainId" placeholder="选择目标链" @on-change="changeChain" style="width:280px">
            <Option v-for="item in chainIdList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
            <Option :value="chain.chainId" :label="chain.chainId"  :disabled="true" v-if="pageParam.pageIndex < pages && chainIdList.length>0" style="text-align:center">
              <span @mouseover="handleReachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
            </Option>
            <Option :value="chain.chainId" :label="chain.chainId"  :disabled="true" v-else style="text-align:center;cursor:not-allowed">
              <span style="font-size:8px;">已加载全部</span>
            </Option>
          </Select>
      </div>
      <div style="float:right;" class="input-search">
          <Input
            prefix="ios-search"
              type="text"
              style="width:400px;margin-right:-1px;"
              v-model="searchData"
              placeholder="区块高度/交易哈希/账户名称"
              @keyup.enter.native="browserBlur(searchData)">
          </Input>
          <Button type="primary" style="padding:2px;width:75px;height:31px;" @click="browserBlur(searchData)"> 搜索</Button>
      </div>
      </div>
      <div v-show="!isShow">
        <Card class="back" :bordered="false" style="margin-top:10px;padding-left:15px;">
            <div class="title"><div class="bs"></div><div>区块信息</div></div>
            <p class="title1" style="padding:20px 0 15px 0;">
              <span class="title1">区块高度：</span>
              <Button :disabled="blockNum <= 1" :class="bt1" @click.native="handleSufix(blockNum)"><Icon type="ios-arrow-back" style="margin-left:-6px;color:#fff"></Icon></Button>
              <span class="icon-title">{{blockNum}}</span>
              <Button :class="bt2" :disabled="blockNum >= maxBlockNum" @click.native="handlePrefix(blockNum)"><Icon type="ios-arrow-forward" style="margin-left:-6px;color:#fff"></Icon></Button>
            </p>
            <p class="title2"> <span class="title1">区块哈希：</span>{{blockId}}</p>
            <p class="title2"> <span class="title1">区块生产者：</span>{{producer}}</p>
            <p class="title2"> <span class="title1">时间：</span>{{timestamp}}</p>
            <p class="title2"> <span class="title1">区块签名：</span> {{producerSignature}}</p>
            <p class="title2"> <span class="title1">已确认：</span> {{confirmed}}</p>
            <p class="title2"> <span class="title1">父哈希：</span> {{previous}}</p>
            <p class="title2"> <span class="title1">M-root(交易)：</span> {{transactionMroot}}</p>
            <p class="title2"> <span class="title1">M-root(操作)：</span>{{actionMroot}}</p>
          </Card>
          <div class="login_header">
              <div @click="cur=0" :class="{active:cur===0}" class="login_header_1">
                <img :src="cur === 0 ? logo1s : logo1" class="logo"/>
                <span>交易</span>
              </div>
              <div @click="cur=1" :class="{active:cur===1}" class="login_header_2">
                <img :src="cur === 1 ? logo3 : logo3s" class="logo">
                <span>区块原始数据</span>
              </div>
            </div>
            <div v-show="cur===0" class="tab-1">
              <edit-table-mul :columns="columns" v-model="blockTableData" :key="transferKey"></edit-table-mul>
              <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;padding-top:10px;"/>
            </div>
            <div class="announce scroll" v-show="cur===1" v-if="origMessage">
              <json-viewer :value="origMessage" :copyable="copyable"></json-viewer>
            </div>
            <div v-else class="tab-1" style="text-align:center;" v-show="cur===1">暂无数据</div>
        </div>
        <div v-show="isShow" class="show-style">
           <img class="imgs" :src="showUrl">
           <p class="msg-style">{{showMsg}}</p>
        </div>
</div>
</template>
<script>
import EditTableMul from '_c/edit-table-mul'
import { getBlockInfo, getBrowserMain, getChainIdList } from '@/api/data'
import { isBlockNum, isAccount, isTrxId } from '@/lib/check'
import JsonViewer from 'vue-json-viewer'
import { mapActions } from 'vuex'
export default {
  name: 'browser_block',
  components: {
    EditTableMul,
    JsonViewer
  },
  data () {
    return {
      isShow: false,
      showUrl: require('@/assets/img/null.png'),
      showMsg: '',
      logo1: require('@/assets/img/browser/logo1.png'),
      logo1s: require('@/assets/img/browser/logo1s.png'),
      logo3: require('@/assets/img/browser/logo3.png'),
      logo3s: require('@/assets/img/browser/logo3s.png'),
      copyable: { copyText: '复制', copiedText: '已复制' },
      cur: 0,
      iconColor: '#fff',
      searchData: '',
      goFlag: true,
      hisPath: 'browser_index',
      blockNum: this.$route.query.blockNum || 0,
      maxBlockNum: 0,
      searchBack: '',
      blockId: '',
      producer: '',
      timestamp: '',
      confirmed: 0,
      producerSignature: '',
      previous: '',
      transactionMroot: '',
      actionMroot: '',
      origMessage: null,
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      transferKey: 0,
      columns: [
        {
          key: 'trxId',
          title: '交易哈希',
          minWidth: 320,
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  color: 'blue',
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    this.handleGo(params.row.trxId)
                  }
                }
              }, params.row.trxId)
            ])
          }
        },
        { key: 'actionCount',
          title: 'action数量' },
        { key: 'cpuUsage',
          title: '计算资源(/us)' },
        { key: 'netUsage',
          title: '网络资源(/byte)' },
        { key: 'expiration',
          title: '超时时间' }
      ],
      blockTableData: [],
      chain: JSON.parse(this.$route.query.chain) || {
        chainId: 0,
        chainName: ''
      },
      routeChain: JSON.parse(this.$route.query.chain) || {
        chainId: 0,
        chainName: ''
      },
      bt1: 'bt1',
      bt2: 'bt2',
      className: 'select-style1',
      pageParam: { pageTotal: 0, pageSize: 60, pageIndex: 1 },
      pages: 0,
      chainIdList: [],
      imgUrl: require('@/assets/img/arrow.png'),
      size: 0,
      cha: {}
    }
  },
  computed: {
    getHeight: function (value) {
      if (this.blockTableData.length === 0) {
        return 88
      } else if (this.blockTableData.length < 4) {
        return 88 + 48 * (this.blockTableData.length - 1)
      } else {
        return 88 + 48 * 4
      }
    }
  },
  methods: {
    ...mapActions([
      'updateChain'
    ]),
    browserBlur (val) {
      if (isBlockNum(val)) {
        this.blockNum = val
        this.goFlag = false
        this.getBlockData(val)
      } else if (isAccount(val)) {
        this.$router.push({
          name: 'browser_chain',
          query: {
            accountName: val,
            chain: JSON.stringify(this.chain),
            tag: true
          }
        })
      } else if (isTrxId(val)) {
        this.$router.push({
          name: 'browser_trade',
          query: {
            trxId: val,
            chain: JSON.stringify(this.chain),
            tag: true
          }
        })
      } else {
        if (val === '') {
          this.msgInfo('warning', '未输入任何查询信息，请检查！', true)
          this.showMsg = '未输入任何查询信息，请检查！'
        } else {
          this.msgInfo('warning', '输入信息有误，请检查！', true)
          this.showMsg = '输入信息有误，请检查！'
        }
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getBlockData(this.blockNum)
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getBlockData(this.blockNum)
    },
    getBlockData (value) {
      getBlockInfo(this.chain.chainId, value, this.tablePageParam.pageIndex, this.tablePageParam.pageSize).then(res => {
        if (res.code === '00000') {
          this.isShow = false
          this.blockNum = res.data.blockNum ? res.data.blockNum : 0
          this.blockId = res.data.blockId ? res.data.blockId : '暂无记录'
          this.timestamp = res.data.timestamp ? res.data.timestamp : '暂无记录'
          this.producer = res.data.producer ? res.data.producer : '暂无记录'
          this.producerSignature = res.data.producerSignature ? res.data.producerSignature : '暂无记录'
          this.confirmed = res.data.confirmed ? res.data.confirmed : 0
          this.previous = res.data.previous ? res.data.previous : '暂无记录'
          this.transactionMroot = res.data.transactionMroot ? res.data.transactionMroot : '暂无记录'
          this.actionMroot = res.data.actionMroot ? res.data.actionMroot : '暂无记录'
          this.origMessage = res.data.origMessage ? JSON.parse(res.data.origMessage) : null
          this.blockTableData = res.data.transactions
          this.tablePageParam.pagetotal = res.data.total
          ++this.transferKey
          this.getBrowserData()
        } else {
          this.isShow = true
          if (res.code === 'A1000') {
            this.msgInfo('error', res.message, true)
            this.showMsg = '区块高度[' + value + ']，' + '该区块信息在[' + this.chain.chainName + ']上不存在！'
          } else {
            this.msgInfo('error', '查询区块高度[' + value + ']，' + res.message, true)
            this.showMsg = res.message
          }
        }
      }).catch(error => {
        this.isShow = true
        this.showMsg = error.message
        this.msgInfo('error', error.message, true)
      })
    },
    transferMessage (value) {
      if (value) {
        let arr = []
        var replaceStr = ','
        var replaceStr2 = '{'
        var replaceStr3 = '}'
        var newMessage = value.replace(new RegExp(replaceStr, 'g'), ',替')
        newMessage = newMessage.replace(new RegExp(replaceStr2, 'g'), '替{替')
        newMessage = newMessage.replace(new RegExp(replaceStr3, 'g'), '替}')
        arr = newMessage.split('替')
        return arr
      } else {
        return []
      }
    },
    handleSufix (value) {
      this.goFlag = false
      this.getBlockData(+value - 1)
    },
    handlePrefix (value) {
      this.goFlag = false
      this.getBlockData(+value + 1)
    },
    handleGo (value) {
      this.$router.push({
        name: 'browser_trade',
        query: {
          trxId: value,
          chain: JSON.stringify(this.chain)
        }
      })
    },
    reback () {
      this.$router.push({
        name: 'browser_index'
      })
    },
    getBrowserData () {
      getBrowserMain(this.chain.chainId).then(res => {
        if (res.code === '00000') {
          this.maxBlockNum = res.data.blockHeight ? res.data.blockHeight : 0
          this.bt1 = this.blockNum <= 1 ? 'bt1 bts' : 'bt1'
          this.bt2 = this.blockNum >= this.maxBlockNum ? 'bt2 bts' : 'bt2'
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    selectClassName () {
      this.className = this.className === 'select-style1' ? 'select-style2' : 'select-style1'
    },
    handleReachBottom () {
      if (this.pageParam.pageIndex < this.pages) {
        // this.pageParam.pageSize += 3
        this.pageParam.pageIndex += 1
        // this.updatePageSize(this.pageParam.pageSize)
        this.getChainList(true)
      }
    },
    getChainList (flag) {
      getChainIdList(this.pageParam).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          if (flag) {
            let index = res.data.records.findIndex(item => {
              if (item.chainId === this.routeChain.chainId) {
                return true
              }
            })
            if (index !== -1) {
              res.data.records.splice(index, 1)
            }
            this.chainIdList.push.apply(this.chainIdList, res.data.records)
          } else {
            this.chainIdList = res.data.records && res.data.records.length > 0 ? res.data.records : []
            if (this.chain.chainId === 0 && this.chainIdList[0].chainId) {
              this.chain.chainId = this.chainIdList[0].chainId
              this.chain.chainName = this.chainIdList[0].chainName
            } else {
              this.pushChainId()
            }
          }
          this.pageParam = {
            pageTotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          this.size = this.chainIdList.length
          this.pages = res.data.pages
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    pushChainId () {
      let flag = false
      for (let i in this.chainIdList) {
        if (this.chainIdList[i].chainId === this.routeChain.chainId) {
          flag = true
          break
        }
      }
      if (!flag) {
        this.chainIdList.push(this.routeChain)
      }
    },
    changeChain () {
      this.$router.push({
        name: 'browser_index'
      })
      // if (this.chain.chainId !== 0) {
      //   this.getChainName(this.chain.chainId)
      //   this.getBlockData(this.blockNum)
      // }
    },
    getChainName (value) {
      for (let item in this.chainIdList) {
        if (this.chainIdList[item].chainId === value) {
          this.chain.chainName = this.chainIdList[item].chainName
        }
      }
    }
  },
  mounted () {
    if (this.$route.query.blockNum && this.$route.query.chain) {
      if (this.$route.query.tag) {
        this.searchData = this.$route.query.blockNum + ''
      }
      this.getChainList()
      if (this.chain.chainId !== 0) {
        this.getBlockData(this.$route.query.blockNum)
      }
    } else {
      this.reback()
    }
  },
  beforeRouteEnter (to, from, next) {
    if (from.name) {
      next(vm => {
        vm.hisPath = from.name
      })
    } else {
      next('/browser_index')
    }
  },
  destroyed () {
    this.updateChain(this.chain)
  }
}
</script>
<style lang="less" scoped>
.active {
  color: #3f7dff;
  padding-bottom: 10px;
  border-bottom: 3px solid #3f7dff;
  cursor: pointer;
}
.size{
  font-weight: bold;
  font-size:16px;
}
.title{
    .size;
    margin-top:10px;
    height:18px;;
    font-family: 'Microsoft YaHei';
    line-height: 18px;
    color: #333333;
    vertical-align: middle;
  }
.bs{
    float:left;
    width: 6px;
    height: 18px;
    background: #19C3A0;
    opacity: 1;
    border-radius: 3px;
    margin-right:6px;
}

.title2{
    font-size:14px;
    font-family: MicrosoftYaHei;
    color:#333333;
    opacity: 1;
    margin-bottom:5px;
    margin-left: 5.5em;
    text-indent: -5.5em;
    word-break: break-all;
}
.title1{
  font-size:14px;
  padding-bottom:5px;
  color:#9B9B9B;
  }
.icon-style{
   font-size:12px;
   padding: 17px 4px 8px 4px;
   border: 1px solid #e0e0e0;
   cursor: pointer;
}
.icon-title{
    padding:0px 15px 0px 15px;
    background: #EDF0FC;
    font-family: 'D-DIN';
    vertical-align: middle;
    height: 40px;
    font-size: 30px;
    font-weight: bold;
    line-height: 23px;
    color: #3D73EF;
    letter-spacing: 1px;
    opacity: 1;
   }
.announce{
    .tab-1;
    height:400px;
    overflow-y:hidden;
    overflow-x:hidden;
  }
.scroll{
     overflow-y:auto;
     overflow-x:hidden;
   }
  .scroll::-webkit-scrollbar{
    width : 5px;  /*高宽分别对应横竖滚动条的尺寸*/
    min-height: 1px;
  }
  .scroll::-webkit-scrollbar-thumb{
    border-radius   : 10px;
    background-color: rgb(135, 158, 235);
  }
 ul li{
   word-break:break-all;
   margin-left:5px;
 }
 .login_header{
    margin-top:20px;
    height:72px;
    font-size:16px;
    background-image:url('../../../assets/img/browser/head.png');
    background-repeat:no-repeat;
    background-size:100% 72px;
      .login_header_1{
      margin-right:30px;
      cursor:pointer;
      font-weight:bold;
      width:65px;
      display:inline-block;
      margin-left:30px;
    }
    .login_header_2{
      cursor:pointer;
      font-weight:bold;
      padding-top:15px;
      width:130px;
      display:inline-block;
    }
    .logo{
      cursor:pointer;
      margin-right:10px;
      vertical-align:middle;
    }
}
.tab-1{
    width:100%;
    height:100%;
    background: #FFFFFF;
    opacity: 1;
    border-radius: 22px 22px 0px 0px;
    border: 1px solid #a5a4bf17;
    margin-top:-20px;
    padding:20px;
  }
/deep/.ivu-table-tip{
  overflow: hidden;
}
.back{
    background-image:url('../../../assets/img/browser/block.png');
    background-repeat:no-repeat;
    background-size:100% 100%;
  }
.show-style{
  display: table;
  text-align: center;
  vertical-align: middle;
  margin:0 auto;
  position: relative;
  padding:8%;
  .msg-style{
    color:#b7b8b9;
    font-size:12px;
  }
}
.bt1 {
    border-radius: 6px 0 0 6px;
    width:8px;
    height:40px;
    background-color:#2D8CF0;
  }
.bt2 {
  border-radius: 0 6px 6px 0;
  width:8px;
  height:40px;
  background-color:#2D8CF0;
}
.bts {
  background-color: #B7C0D6;
}
</style>
<style lang="less" scoped>
/deep/.select-style1{
   .ivu-select-arrow{
     padding:9px;
     margin-right:-9px;
     background-color: #57a3f3;
     color: #fff;
     border-radius: 0 5px 5px 0;
     transition: none;
   }
   .ivu-icon-ios-arrow-down:before{
     color:#fff;
   }
}
/deep/.select-style2{
   .ivu-select-arrow{
     padding:9px;
     margin-right:-9px;
     background-color: #57a3f3;
     color: #fff;
     border-radius: 5px 0px 0px 5px;
     transition: none;
   }
   .ivu-icon-ios-arrow-down:before{
     color:#fff;
   }
}
/deep/.input-search {
  .ivu-btn{
    border-radius: 0 4px 4px 0;
  }
  .ivu-input{
     border-radius: 4px 0 0 4px;
  }
}
/deep/.select-option{
  .ivu-btn{
   border-radius: 0 4px 4px 0;
}
}
</style>
