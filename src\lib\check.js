// 手机号
function isPhoneNumber (phoneNumber) {
  var reg = /^(13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
  return reg.test(phoneNumber)
}

function isEmail (email) {
  var reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
  return reg.test(email)
}

function isNumber (value) {
  var reg = /^\d+$/
  return reg.test(value)
}

function isBlockNum (value) {
  var reg = /^([1-9]\d*)$/
  return reg.test(value)
}

function isCmBlockNum (value) {
  var reg = /^([0-9]\d*)$/
  return reg.test(value)
}

function isAccount (value) {
  var reg = /^(?![.])(?!\d+$)[a-z1-5.]{5,12}(?<![.])$/
  return reg.test(value)
}

function isTrxId (value) {
  var reg = /^([0-9a-f]{64})$/
  return reg.test(value)
}

function isStar (value) {
  var reg = /^[*]/
  return reg.test(value)
}

function isUser (value) {
  var reg = /^[\u4E00-\u9FA5a-zA-Z0-9_]{1,20}$/
  return reg.test(value)
}

function isLoginId (value) {
  var reg = /^([A-Za-z0-9_]{1,30})$/
  return reg.test(value)
}

function isRealName (value) {
  var reg = /^(?=.*[a-z\u4E00-\u9FA5])[\u4E00-\u9FA5A-Za-z0-9]{1,15}$/
  return reg.test(value)
}

function isPassword (value) {
  var reg = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!#$%])[a-zA-Z0-9!#$%]{8,20}$/
  return reg.test(value)
}

function isContractName (value) {
  var reg = /^[_.0-9a-zA-Z]{5,32}$/
  return reg.test(value)
}

function isChinese (value) {
  var reg = /[\u4E00-\u9FA5]/
  return reg.test(value)
}

function isPowerAccount (value) {
  var reg = /^(?![.])(?!\d+$)[a-z1-5.]{1,12}(?<![.])$/
  return reg.test(value)
}
// 新建合约类型-校验
function TemContractName (value) {
  var reg = /^[_.0-9a-zA-Z]{5,60}$/
  return reg.test(value)
}
//  反千分位转换
function rmoney (s) {
  let str = ''
  if (s) {
    str = String(s).replace(/,/g, '')
  }
  if (s && (s + '').indexOf('.') > -1 && Number(str)) {
    return String(s).replace(/[^\d\.-]/g, '')
  } else if (s && Number(str)) {
    return str
  } else {
    return s
  }
}
function isNodeName (value) {
  var reg = /^(?![-])(?!\d+$)[a-z1-9-]{1,20}(?<![-])$/
  return reg.test(value)
}
export {
  isPhoneNumber,
  isEmail,
  isNumber,
  isAccount,
  isTrxId,
  isBlockNum,
  isStar,
  isUser,
  isLoginId,
  isRealName,
  isPassword,
  isContractName,
  isCmBlockNum,
  isChinese,
  isPowerAccount,
  TemContractName,
  rmoney,
  isNodeName

}
