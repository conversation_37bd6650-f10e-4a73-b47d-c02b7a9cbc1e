<template>
  <div class="comp-wrap">
    <div class="title"><span class="bs"></span>
      数据服务配置</div>
    <div style="margin:10px 10px 15px 0px;display:flex;justify-content: space-between;">
      <p>
        <Input class='bt1 width-input' placeholder="请输入服务名称/用户名称" @on-enter="searchList" style="vertical-align:baseline;width:230px" v-model.trim="datainput" @keyup.native="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm,'')" />
        <Button class='bt1' icon="ios-search" type="primary" @click="searchList">查询</Button>
        <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting">重置</Button>
      </p>
      <div>
        <p><Button class='bt1' type="success" ghost icon="md-add" @click="newData">新建服务</Button></p>
      </div>
    </div>

    <!-- <div class="business_details" v-if="tranDataStatus">
      <Row>
        <Col span="4">{{tranData.scalePlatformResourceName}}</Col>
        <Col span="4">{{tranData.transactionId}}</Col>
        <Col span="4">{{tranData.createTime}}</Col>
        <Col span="4">{{tranData.judicialTxid}}</Col>
        <Col span="4">{{tranData.updateTime}}</Col>
        <Col span="4">{{tranData.status}}</Col>
      </Row>
    </div> -->
    <!-- <div class="title"><span class="bs"></span>今日交易</div> -->
    <Table :columns="columns" :data="tableData">
      <template slot-scope="{ row, index }" slot="action">
        <Button type="text" size="small" v-if="row.status=='未使用'" style='marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF' @click="upeditbtn(index)">编辑</Button>
        <Button type="text" size="small" v-if="row.status=='未使用'" style='marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF' @click="remove(index)">删除</Button>
        <Button type="text" size="small" v-if="row.status=='启用'" style='marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF' @click="upstopbtn(index)">详情</Button>
      </template>
    </Table>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px" />
    <Modal v-model="modalPush" :title="appModal" :width="600" :draggable="true" sticky :mask-closable="false" @on-cancel="cancelApp">

      <Form ref="formItemRule" :rules="formItemRule" :model="arrTenant" :label-width="130" @submit.native.prevent>

        <FormItem label="服务名称：" prop="serveName">
          <Input v-if="detailModal" style="width:370px" v-model.trim="arrTenant.serveName" placeholder="请输入服务名称" :maxlength="30" @keyup.native="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm,'')" />
          <p v-else>{{arrTenant.serveName}}</p>
        </FormItem>

        <FormItem label="租户：" prop="tenement">
          <Select v-if="detailModal" v-model="arrTenant.tenement" placeholder="请选择租户" style="width:370px" @on-change="changeTenement" @on-open-change="showCourseTenement">
            <Option v-for="(item,index) in tenementList" :value="item.tenantId" :key="index">{{ item.tenantName }}</Option>
            <Option :value="arrTenant.tenement+''" :disabled="true" v-if="modifyTotal>tenementList.length" style="text-align:center">
              <span @mouseover="handleReachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
            </Option>
            <Option :value="arrTenant.tenement+''" :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
              <span style="font-size:8px;">已加载全部</span>
            </Option>
          </Select>
          <p v-else>{{arrTenant.tenement}}</p>
        </FormItem>

        <FormItem label="用户：" prop="username">
          <Select v-if="detailModal" v-model="arrTenant.username" ref="resetSelect" placeholder="请选择用户" @on-change="changeUsername" @on-open-change="showCourseUsername" style="width:370px" :disabled='arrTenant.tenement?false:true'>
            <Option v-for="(item,index) in usernameList" :value="item.userId" :key="index">{{ item.userLoginId }}</Option>
            <Option :value="arrTenant.username+''" :disabled="true" v-if="modifyTota2>usernameList.length" style="text-align:center">
              <span @mouseover="handleReachBottom2" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
            </Option>
            <Option :value="arrTenant.username+''" :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
              <span style="font-size:8px;">已加载全部</span>
            </Option>
          </Select>
          <p v-else>{{arrTenant.username}}</p>
        </FormItem>

        <FormItem label="链名称：" prop="chainname">
          <Select v-if="detailModal" v-model="arrTenant.chainname" placeholder="请选择链名称" style="width:370px" @on-change="changeChainname" @on-open-change="showCourseChainname" :disabled='arrTenant.username?false:true'>
            <Option v-for="(item,index) in chainnameList" :value="item.chainId" :key="index">{{ item.chainName }}</Option>
            <Option :value="arrTenant.chainname+''" :disabled="true" v-if="modifyTota3>chainnameList.length" style="text-align:center">
              <span @mouseover="handleReachBottom3" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
            </Option>
            <Option :value="arrTenant.chainname+''" :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
              <span style="font-size:8px;">已加载全部</span>
            </Option>
          </Select>
          <p v-else>{{arrTenant.chainname}}</p>
        </FormItem>

        <FormItem label="合约链账户：" prop="account">
          <Select v-if="detailModal" v-model="arrTenant.account" placeholder="请选择合约链账户" style="width:370px" @on-change="changeAccount" @on-open-change="showCourseAccount" :disabled='arrTenant.chainname?false:true'>
            <Option v-for="(item,index) in accountList" :value="item.id" :key="index">{{ item.chainAccountName }}</Option>
          </Select>
          <p v-else>{{arrTenant.account}}</p>
        </FormItem>

        <FormItem label="合约方法：" prop="contractmethod">
          <Select v-if="detailModal" v-model="arrTenant.contractmethod" :disabled='arrTenant.account?false:true' @on-open-change="showCourseContractmethod" placeholder="请选择合约方法" style="width:370px">
            <Option v-for="(item,index) in contractmethodList" :value="item.value" :key="index">{{ item.label }}</Option>
          </Select>
          <p v-else>{{arrTenant.contractmethod}}</p>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="default" @click="cancelApp">取消</Button>
        <Button type="primary" @click="ok('formItemRule')" :loading='serveLoading'>确定</Button>
      </div>
    </Modal>
    <!-- 删除弹框 -->
    <Modal v-model="modal3" @on-cancel="closemodal3" width=25>
      <p style="text-align: center;height:30px;margin-top:20px; line-height: 30px;">请确认是否删除</p>
      <div slot="footer">
        <Button type="default" @click="closemodal3">取消</Button>
        <Button type="primary" @click="detailConfig">确定</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { newdataPush, getNewServeResourceList, getChainIdList, getTenantTableData, getAssigned, getContractList, getContractInfo, getUpdateServeResourceList, getDeleteServeResourceList } from '@/api/balance'
import EditTableMul from '_c/edit-table-mul'

export default {
  components: {
    EditTableMul
  },
  data () {
    return {
      imgUrl: require('@/assets/img/arrow.png'),
      datainput: '',
      columns: [
        { key: 'name', title: '服务名称', tooltip: true },
        { key: 'userLoginId', title: '用户', tooltip: true },
        { key: 'chainName', title: '链名称', tooltip: true },
        { key: 'chainAccountName', title: '合约链账户', tooltip: true },
        { key: 'chainAccountActionName', title: '合约方法', tooltip: true },
        { key: 'createTime', title: '创建时间', tooltip: true },
        {
          key: 'status', title: '状态', tooltip: true, minWidth: 70,
          // width: '200px',
          render: (h, params) => {
            const row = params.row
            const color = row.status === '未使用' ? '#515a6e' : 'success'
            // const text = row.status === 1 ? 'Working' : row.status === 2 ? 'Success' : 'Fail';
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, row.status)
          }
        },
        {
          slot: 'action',
          title: '操作',
          // fixed: 'right',
          align: 'left',
          minWidth: 130
        }

      ],
      tableData: [],
      arrTenant: { serveName: '', tenement: '', username: '', chainname: '', account: '', contractmethod: '', id: '' },
      tablePageParam: {
        pageSize: 10,
        pageIndex: 1,
        pagetotal: 0,
      },
      modalPush: false,
      appModal: '新建服务',
      modal3: false,
      tenementList: [],//租户
      usernameList: [],//用户
      chainnameList: [],//链账户
      accountList: [],//合约链账户
      contractmethodList: [],//合约方法
      modifyTotal: 0,
      modifyTota2: 0,
      modifyTota3: 0,
      deleId: '',//删除id
      formItemRule: {
        serveName: [{ required: true, message: '请输入服务名称' }],
        tenement: [{ required: true, message: '请选择租户' }],
        username: [{ required: true, message: '请选择用户' }],
        chainname: [{ required: true, message: '请选择链名称' }],
        account: [{ required: true, message: '请选择合约链账户' }],
        contractmethod: [{ required: true, message: '请选择合约方法' }],
        // datavolume: [{ required: true, message: '请输入需要取的数据量，例1000条', trigger: 'blur' }],
        // representation: [{ required: true, message: "请输入Cron表达式", trigger: "blur" },
        // {
        //   pattern:
        //     "^\\s*($|#|\\w+\\s*=|(\\?|\\*|(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?(?:,(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?)*)\\s+(\\?|\\*|(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?(?:,(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?)*)\\s+(\\?|\\*|(?:[01]?\\d|2[0-3])(?:(?:-|\\/|\\,)(?:[01]?\\d|2[0-3]))?(?:,(?:[01]?\\d|2[0-3])(?:(?:-|\\/|\\,)(?:[01]?\\d|2[0-3]))?)*)\\s+(\\?|\\*|(?:0?[1-9]|[12]\\d|3[01])(?:(?:-|\\/|\\,)(?:0?[1-9]|[12]\\d|3[01]))?(?:,(?:0?[1-9]|[12]\\d|3[01])(?:(?:-|\\/|\\,)(?:0?[1-9]|[12]\\d|3[01]))?)*)\\s+(\\?|\\*|(?:[1-9]|1[012])(?:(?:-|\\/|\\,)(?:[1-9]|1[012]))?(?:L|W)?(?:,(?:[1-9]|1[012])(?:(?:-|\\/|\\,)(?:[1-9]|1[012]))?(?:L|W)?)*|\\?|\\*|(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)(?:(?:-)(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC))?(?:,(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)(?:(?:-)(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC))?)*)\\s+(\\?|\\*|(?:[0-6])(?:(?:-|\\/|\\,|#)(?:[0-6]))?(?:L)?(?:,(?:[0-6])(?:(?:-|\\/|\\,|#)(?:[0-6]))?(?:L)?)*|\\?|\\*|(?:MON|TUE|WED|THU|FRI|SAT|SUN)(?:(?:-)(?:MON|TUE|WED|THU|FRI|SAT|SUN))?(?:,(?:MON|TUE|WED|THU|FRI|SAT|SUN)(?:(?:-)(?:MON|TUE|WED|THU|FRI|SAT|SUN))?)*)(|\\s)+(\\?|\\*|(?:|\\d{4})(?:(?:-|\\/|\\,)(?:|\\d{4}))?(?:,(?:|\\d{4})(?:(?:-|\\/|\\,)(?:|\\d{4}))?)*))$",
        //   message: "请输入正确的Cron表达式", trigger: 'blur'
        // }
        // ]
      },
      pageParamList: {
        pageParamTenement: { pageTotal: 0, pageSize: 100, pageIndex: 1 },
        pageParamUsername: { pageTotal: 0, pageSize: 100, pageIndex: 1 },
        pageParamaccount: { pageTotal: 0, pageSize: 100, pageIndex: 1 },
      },

      detailModal: true,
      serveLoading: false,

    }
  },
  methods: {
    // 提示
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    // 滚动加载 服务名称
    handleReachBottom () {
      return new Promise(resolve => {
        let tablePageParam = { pageSize: 10, pageIndex: 1 }
        let pageIndex = 1
        // if (this.arrRole.roleId) {
        pageIndex = this.tenementList.length / 10 + 1
        tablePageParam = Object.assign({}, tablePageParam, { pageIndex })
        if (this.tenementList.length % 10 !== 0 || Math.ceil(this.modifyTotal / 10) < pageIndex) {
          return resolve()
        } else {
          this.pageParamList.pageParamTenement.pageIndex = tablePageParam.pageIndex
          this.pageParamList.pageParamTenement.pageSize = tablePageParam.pageSize
          this.getTenementList()
        }

        resolve()
      })
    },
    handleReachBottom2 () {
      return new Promise(resolve => {
        let tablePageParam2 = { pageSize: 10, pageIndex: 1 }
        let pageIndex = 1
        // if (this.arrRole.roleId) {
        pageIndex = this.usernameList.length / 10 + 1

        tablePageParam2 = Object.assign({}, tablePageParam2, { pageIndex })
        if (this.usernameList.length % 10 !== 0 || Math.ceil(this.modifyTotal / 10) < pageIndex) {
          return resolve()
        } else {
          this.pageParamList.pageParamaccount.pageIndex = tablePageParam2.pageIndex
          this.pageParamList.pageParamaccount.pageSize = tablePageParam2.pageSize
          this.getTenementList(this.arrTenant.tenement)
        }

        resolve()
      })
    },
    handleReachBottom3 () {
      return new Promise(resolve => {

        let tablePageParam3 = { pageSize: 10, pageIndex: 1 }
        let pageIndex = 1
        // if (this.arrRole.roleId) {
        pageIndex = this.chainnameList.length / 10 + 1
        tablePageParam3 = Object.assign({}, tablePageParam3, { pageIndex })
        if (this.chainnameList.length % 10 !== 0 || Math.ceil(this.modifyTotal / 10) < pageIndex) {
          return resolve()
        } else {
          this.pageParamList.pageParamaccount.pageIndex = tablePageParam3.pageIndex
          this.pageParamList.pageParamaccount.pageSize = tablePageParam3.pageSize

        }

        resolve()
      })
    },
    // 分页
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.serverList()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.serverList()
    },

    // 搜索
    searchList () {
      this.tablePageParam = {
        pageSize: 10,
        pageIndex: 1,
      }
      this.serverList()
    },
    // 重置
    resetting () {
      this.datainput = ''
      this.tablePageParam = {
        pageSize: 10,
        pageIndex: 1,
      }
      this.serverList()
    },

    serverList () {
      let searchData = {
        name: this.datainput,
        userLoginId: this.datainput,
        pageParam: {
          pageIndex: this.tablePageParam.pageIndex,
          pageSize: this.tablePageParam.pageSize
        }
      }
      newdataPush(searchData).then(res => {
        if (res.code === '00000') {
          let resdata = {
            '0': '未使用',
            '1': '启用',

          }
          let data = res.data.records.map(item => {
            return {
              ...item,
              status: item.status ? resdata[item.status] : resdata[item.status]
            }
          })
          this.tableData = data
          this.tablePageParam.pagetotal = res.data.total

        } else {
          this.tranDataStatus = false
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.tranDataStatus = false
        this.msgInfo('error', error.message, true)
      })
    },
    // 取消按钮
    cancelApp () {
      this.modalPush = false
      this.$refs.formItemRule.resetFields()

    },
    // 详情
    upstopbtn (index) {
      this.appModal = '服务信息'
      this.modalPush = true;
      this.detailModal = false;
      this.arrTenant.serveName = this.tableData[index].name
      this.arrTenant.tenement = this.tableData[index].tenantName
      this.arrTenant.username = this.tableData[index].userLoginId
      this.arrTenant.chainname = this.tableData[index].chainName
      this.arrTenant.account = this.tableData[index].chainAccountName
      this.arrTenant.contractmethod = this.tableData[index].chainAccountActionName
    },
    // 新建
    newData () {
      this.appModal = '新建服务'
      this.modalPush = true;
      this.detailModal = true;
      this.serveLoading = false;
      this.$refs.formItemRule.resetFields()
      this.pageParamList = {
        pageParamTenement: { pageTotal: 0, pageSize: 100, pageIndex: 1 },
        pageParamUsername: { pageTotal: 0, pageSize: 100, pageIndex: 1 },
        pageParamaccount: { pageTotal: 0, pageSize: 100, pageIndex: 1 },
      }
      this.tenementList = []
      this.usernameList = []
      this.chainnameList = []
      this.accountList = []
      this.contractmethodList = []
      // this.getTenementList()
    },
    // 编辑
    upeditbtn (index) {
      this.appModal = '编辑服务'
      this.modalPush = true;
      this.detailModal = true;
      this.serveLoading = false
      // this.tenementList = []
      // this.arrTenant.username = 21
      this.arrTenant.serveName = this.tableData[index].name
      this.tenementList = [{ tenantId: this.tableData[index].tenantId, tenantName: this.tableData[index].tenantName }]
      this.usernameList = [{ userId: this.tableData[index].userId, userLoginId: this.tableData[index].userLoginId }]
      this.chainnameList = [{ chainId: this.tableData[index].chainId, chainName: this.tableData[index].chainName }]
      this.accountList = [{ id: this.tableData[index].chainAccountId, chainAccountName: this.tableData[index].chainAccountName }]
      this.contractmethodList = [{ value: this.tableData[index].chainAccountActionName, label: this.tableData[index].chainAccountActionName }]
      this.arrTenant.tenement = this.tableData[index].tenantId
      this.arrTenant.username = this.tableData[index].userId
      this.arrTenant.chainname = this.tableData[index].chainId
      this.arrTenant.account = this.tableData[index].chainAccountId
      this.arrTenant.contractmethod = this.tableData[index].chainAccountActionName
      this.arrTenant.id = this.tableData[index].id
      // this.getTenementList()
    },
    ok (formItemRule) {
      this.$refs[formItemRule].validate((valid) => {
        if (valid) {
          this.serveLoading = true
          if (this.appModal === '新建服务') {
            let newServeData = {
              name: this.arrTenant.serveName,
              tenantId: this.arrTenant.tenement,
              userId: this.arrTenant.username,
              chainId: this.arrTenant.chainname,
              chainAccountId: this.arrTenant.account,
              chainAccountActionName: this.arrTenant.contractmethod,
            }
            getNewServeResourceList(newServeData).then(res => {
              if (res.code === '00000') {
                this.msgInfo('success', res.message, true)
                this.modalPush = false;
                this.serveLoading = false
                this.serverList()
              } else {
                this.serveLoading = false
                this.msgInfo('error', res.message, true)
              }
            }).catch(error => {
              this.serveLoading = false
              this.msgInfo('error', error.message, true)
            })
          } else if (this.appModal === '编辑服务') {
            let editServeData = {
              id: this.arrTenant.id,
              name: this.arrTenant.serveName,
              tenantId: this.arrTenant.tenement,
              userId: this.arrTenant.username,
              chainId: this.arrTenant.chainname,
              chainAccountId: this.arrTenant.account,
              chainAccountActionName: this.arrTenant.contractmethod,
            }
            getUpdateServeResourceList(editServeData).then(res => {
              if (res.code === '00000') {
                this.serveLoading = false
                this.msgInfo('success', res.message, true)
                this.modalPush = false;
                this.serverList()
              } else {
                this.serveLoading = false
                this.msgInfo('error', res.message, true)
              }
            }).catch(error => {
              this.serveLoading = false
              this.msgInfo('error', error.message, true)
            })
          } else {
            this.serveLoading = false
            this.modalPush = false;
            this.detailModal = true;
          }


        }
      })

    },
    changeTenement (e) {
      this.usernameList = []
      this.chainnameList = []
      this.accountList = []
      this.contractmethodList = []
      this.arrTenant.username = ''
      this.arrTenant.chainname = ''
      this.arrTenant.account = ''
      this.arrTenant.contractmethod = ''
    },
    changeUsername (e) {
      this.chainnameList = []
      this.accountList = []
      this.contractmethodList = []
      this.arrTenant.chainname = ''
      this.arrTenant.account = ''
      this.arrTenant.contractmethod = ''
    },
    changeChainname (e) {
      this.accountList = []
      this.contractmethodList = []
      this.arrTenant.account = ''
      this.arrTenant.contractmethod = ''
    },
    changeAccount (e) {
      this.contractmethodList = []
      this.arrTenant.contractmethod = ''
    },

    // 租户点击事件
    showCourseTenement (e) {
      if (e) {
        this.pageParamList.pageParamTenement.pageTotal = 0
        this.pageParamList.pageParamTenement.pageSize = 100
        this.pageParamList.pageParamTenement.pageIndex = 1
        this.modifyTota2 = 0
        this.modifyTota3 = 0
        this.getTenementList()
      }
    },
    // 用户点击事件
    showCourseUsername (e) {
      if (e) {
        this.pageParamList.pageParamTenement.pageTotal = 0
        this.pageParamList.pageParamTenement.pageSize = 100
        this.pageParamList.pageParamTenement.pageIndex = 1
        this.getUserList()
      }
    },
    // 链名称点击事件
    showCourseChainname (e) {
      if (e) {
        this.pageParamList.pageParamTenement.pageTotal = 0
        this.pageParamList.pageParamTenement.pageSize = 100
        this.pageParamList.pageParamTenement.pageIndex = 1
        this.getChainList()
      }
    },
    // 合约链账户点击事件
    showCourseAccount (e) {
      if (e) {
        this.contractList()
      }
    },
    // 合约方法点击事件
    showCourseContractmethod (e) {
      if (e) {
        this.contractInfo()
      }
    },
    // 取消删除
    closemodal3 () {
      this.modal3 = false
    },
    // 确定删除
    detailConfig (param) {
      getDeleteServeResourceList(this.deleId).then(res => {
        if (res.code === '00000') {
          this.msgInfo('success', res.message, true)
          this.modal3 = false
          this.serverList()
        } else {
          this.modal3 = false
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.modal3 = false
        this.msgInfo('error', error.message, true)
      })
    },
    remove (id) {
      this.modal3 = true
      this.deleId = this.tableData[id].id
    },
    // 数组去重
    unique (arr, id) {
      const res = new Map();
      return arr.filter((arr) => !res.has(arr[id]) && res.set(arr[id], 1))
    },
    // // 用户去重
    // uniqueuser (arr) {
    //   const res = new Map();
    //   return arr.filter((arr) => !res.has(arr.userId) && res.set(arr.userId, 1))
    // },
    // 租户列表
    getTenementList () {
      getTenantTableData(this.pageParamList.pageParamTenement).then(res => {
        if (res.code === '00000') {
          if (res.data.records && res.data.records.length > 0) {
            this.tenementList.push(...res.data.records)
            this.tenementList = this.unique(this.tenementList, 'tenantId')
            this.modifyTotal = res.data.total

          }
        } else {
          this.tenementList = []
          this.msgInfo('error', res.message)
        }
      }).catch(error => {
        this.tenementList = []
        this.msgInfo('error', error.message)
      })

    },
    // 用户列表
    getUserList () {
      if (this.arrTenant.tenement) {
        getAssigned(this.arrTenant.tenement, this.pageParamList.pageParamUsername, '', 1, 1).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
            // this.msgInfo('warning', res.message, true)
          } else {
            if (res.data.records && res.data.records.length > 0) {
              this.usernameList.push(...res.data.records)
              this.usernameList = this.unique(this.usernameList, 'userId')
              this.modifyTota2 = res.data.total
            }
          }
        }).catch(error => {
          // console.log('getAssigned.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }

    },
    // 链名称列表
    getChainList () {
      let searchItem = {
        status: undefined,
        chooseTenantId: this.arrTenant.tenement ? this.arrTenant.tenement : ''
      }
      getChainIdList(this.pageParamList.pageParamaccount, searchItem).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
          // this.msgInfo('warning', res.message, true)
        } else {
          if (res.data.records && res.data.records.length > 0) {
            this.chainnameList.push(...res.data.records)
            this.chainnameList = this.unique(this.chainnameList, 'chainId')
            // this.chainnameList.push(...res.data.records)
            this.modifyTota3 = res.data.total
          }
        }
      }).catch(error => {
        // console.log('getAssigned.error===>', error)
        this.msgInfo('error', error.message, true)
      })

    },
    // 合约链名称
    contractList () {
      if (this.arrTenant.chainname) {
        getContractList(this.arrTenant.chainname).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
            // this.msgInfo('warning', res.message, true)
          } else {
            if (res.data && res.data.length > 0) {
              this.accountList = res.data
            }
          }
        }).catch(error => {
          // console.log('getAssigned.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }

    },
    // 合约方法
    contractInfo () {
      if (this.arrTenant.chainname && this.arrTenant.account) {
        getContractInfo(this.arrTenant.chainname, this.arrTenant.account).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
            // this.msgInfo('warning', res.message, true)
          } else {
            if (res.data.actionTypeList && res.data.actionTypeList.length > 0) {
              let data = res.data.actionTypeList.map(item => {
                return {
                  value: item,
                  label: item
                }
              })
              this.contractmethodList = data
            }
          }
        }).catch(error => {
          // console.log('getAssigned.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }

    }


  },
  mounted () {
    this.serverList()
    this.searchList()
  },
  // watch: {
  //   'arrTenant.tenement': {
  //     handler (newVal, oldVal) {
  //       // this.tenementList = []
  //       // this.usernameList = []
  //       // this.chainnameList = []
  //       // this.accountList = []
  //       // this.contractmethodList = []
  //       this.arrTenant.username = ''
  //       this.arrTenant.chainname = ''
  //       this.arrTenant.account = ''
  //       this.arrTenant.contractmethod = ''
  //       this.modifyTota2 = 0
  //       this.modifyTota3 = 0
  //       this.getUserList()
  //       this.getChainList()


  //     },
  //     deep: true,
  //     immediate: false
  //   },
  //   'arrTenant.username': {
  //     handler (newVal, oldVal) {
  //       // this.chainnameList = []
  //       // this.accountList = []
  //       // this.contractmethodList = []
  //       this.arrTenant.chainname = ''
  //       this.arrTenant.account = ''
  //       this.arrTenant.contractmethod = ''
  //       this.modifyTota3 = 0
  //       this.getChainList()
  //     },
  //     deep: true,
  //     immediate: false
  //   },
  //   'arrTenant.chainname': {
  //     handler (newVal, oldVal) {
  //       // this.accountList = []
  //       // this.contractmethodList = []
  //       this.arrTenant.account = ''
  //       this.arrTenant.contractmethod = ''
  //       this.contractList()
  //     },
  //     deep: true,
  //     immediate: false
  //   },
  //   'arrTenant.account': {
  //     handler (newVal, oldVal) {
  //       // this.contractmethodList = []
  //       this.arrTenant.contractmethod = ''
  //       this.contractInfo()
  //     },
  //     deep: true,
  //     immediate: false
  //   },
  // }
}
</script>
<style lang="less" scoped>
.comp-wrap {
  padding: 0 40px;
  box-sizing: border-box;
  .title {
    margin: 15px 0;
    font-size: 16px;
    font-weight: bold;
    height: 25px;
    line-height: 25px;
    vertical-align: middle;
  }
  .bs {
    float: left;
    width: 6px;
    height: 16px;
    background: #348eff;
    opacity: 1;
    border-radius: 3px;
    margin: 4px 5px 0 0;
  }
  .bt1 {
    margin-right: 10px;
  }
  .business_details {
    // border: 1px solid #000;
    height: 50px;
    box-sizing: border-box;
    // line-height: 50px;
    text-align: center;
    margin: 40px 0;
  }
  /deep/.ivu-tag-dot {
    border: 1px none #e8eaec !important;
    background: transparent !important;
  }
  /deep/.ivu-tag {
    font-size: inherit !important;
  }
  /deep/.ivu-col-span-4 {
    word-break: break-all;
    word-wrap: break-word;
    white-space: normal;
  }
  /deep/.ivu-btn-text:hover {
    background-color: rgba(61, 115, 239, 0.8);
    color: #fff !important;
  }
  /deep/.ivu-btn-text:active {
    background-color: #3d73ef;
  }
}
</style>
