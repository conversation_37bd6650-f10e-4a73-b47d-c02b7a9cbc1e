<template>
  <div class="page">
    <SpaceLayout top="20">
      <div slot="padding">
        <div class="header">
        <ol class="steps">
            <li class="step-active active">
              <div class="step-line active-line"></div>
              <div class="step-content">
                  <span class="step-num el-icon-check"></span>
                  <div class="step-text">选择建链方式</div>
              </div>
            </li>
            <li class="step-active active">
              <div class="step-line" :class="{'active-line':isDeploy2}"></div>
              <div class="step-content">
                  <span class="step-num" v-if="!isDeploy2">2</span>
                  <span class="step-num el-icon-check" v-else></span>
                  <div class="step-text">基本参数配置</div>
              </div>
            </li>
            <li class="step-active" :class="{'active':isDeploy2}">
              <div class="step-line" :class="{'active-line':isDeploy3}"></div>
              <div class="step-content">
                  <span class="step-num" v-if="!isDeploy3">3</span>
                  <span class="step-num el-icon-check" v-else></span>
                  <div class="step-text">节点配置</div>
              </div>
            </li>
            <li class="step-active" :class="{active:isDeploy3}">
              <div class="step-line" :class="{'active-line':isAllSuccess}"></div>
              <div class="step-content">
                  <span class="step-num" v-if="!isDeploy4">4</span>
                  <span class="step-num el-icon-check" v-else></span>
                  <div class="step-text">部署状态检测</div>
              </div>
            </li>
            <li class="step-active" :class="{active:isAllSuccess}">
              <div class="step-content">
                  <span class="step-num" v-if="!isAllSuccess">5</span>
                  <span class="step-num el-icon-check" v-else></span>
                  <div :class="{'step-text':isAllSuccess}">完成</div>
              </div>
            </li>
        </ol>
        </div>
      </div>
    </SpaceLayout>
  </div>
</template>

<script>
import SpaceLayout from '@/components/SpaceLayout'
export default {
  components: {
    SpaceLayout
  },
  props:{
    isDeploy2: {
      type: Boolean,
      default: false
    },
    isDeploy3: {
      type: Boolean,
      default: false
    },
    isDeploy4: {
      type: Boolean,
      default: false
    },
    isAllSuccess :{
      type: Boolean,
      default: false
    }
  },
  watch:{
  },
  data() {
    return {
    };
  },
  mounted() {
  },
  methods:{
  }
}
</script>

<style lang="less" scoped>
.page {
  width: 100%;
  min-height: 100%;
  ul  {
    list-style: none;
  }
  .changeChain {
    .title {
      // font-size: 24px;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
    }
    .arrow {
      color: #666666;
      // font-size: 28px;
      font-size: 14px;
    }
    .name {
      // font-size: 24px;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
    }
  }
  .header {
    // width: 80%;
    // margin-top: 68px;
    margin-left: 20%;
     ol.steps::-webkit-scrollbar { /* chrome 隐藏滚动条*/
            display: none;
        }
        ol.steps{
            list-style: none;
            display: flex;
            height: 45px;
        }
        ol.steps li{
            float: left;
            flex: 1;
            position: relative;
           // width:140px;
        }
        ol.steps li .step-line{
          position: absolute;
          left: 30px;
          right: 30px;
          height: 1Px;
          border-bottom: 1.5Px solid #3D73EF;
        }
        ol.steps li:nth-child(2) .step-line {
          left:30px;
          right:40px;
        }
        ol.steps li:nth-child(3) .step-line {
          left:18px;
          right:26px;
        }
        ol.steps li:nth-child(4) .step-line {
          left: 30px;
          right:48px;
        }
        // ol.steps li  .active-line {
        //   width: 100%;
        //   height: 1Px;
        //   border: 1Px solid#3D73EF;
        // }
        ol.steps .step-content{
            position: absolute;
            top:-20px;
            left:-40px ;
            text-align: center;
        }
        // ol.steps li.active .step-content{
        //     position: absolute;
        //     top:-20px;
        //     left:-40px ;
        //     text-align: center;
        // }
        ol.steps .step-content div {
          margin-top: 15px;
          // font-size: 20px;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #666666;
        }
        ol.steps .step-content .step-text {
          color: #333333;
        }
        ol.steps li .step-content .step-num{
            display: inline-block;
            height: 40px;
            width: 40px;
            color: #3D73EF;
            background-color: #fff;
            line-height: 38px;
            border-radius: 50%;
            text-align: center;
            border:2px solid #3D73EF;
            // font-size:20px;
            font-size: 14px;
            font-weight: bold;
        }
        ol.steps li.active .step-content .step-num{
            height: 40px;
            width: 40px;
            background-color:#3D73EF;
            line-height:38px;
            color: #fff;
            border:2px solid #3D73EF;
        }
        ol.steps li.step-end{
            width: 120px!important;
            flex: inherit;
        }
        ol.steps li.step-end .step-line{
            display: none;
        }

  }
}
</style>
