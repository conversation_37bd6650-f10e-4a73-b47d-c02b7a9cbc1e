<template>
  <div>
      <p style="margin:10px 10px 15px 0px;">
        <!-- <Input class='bt1 width-input' placeholder="可输入合约包名称或合约名称" style="vertical-align:baseline;" v-model="contractName"/> -->
   <Input
        style="width: 250px; vertical-align: baseline; margin-right: 10px"
        placeholder="可输入合约包名称或合约名称"
        v-model="inputvalue"
        @keyup.enter="searchList"
        @keyup.enter.native="searchList"
      >
        <Icon type="ios-search" slot="suffix" @click="searchList" />
     </Input>
      </p>
      <edit-table-mul :columns="columns" v-model="tableData" ></edit-table-mul>
       <Page placement='top' :total="dataCount" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px"/>
  </div>
</template>

<script>
import EditTableMul from '_c/edit-table-mul'
import { PendingList } from '@/api/data'
export default {
  components: {
    EditTableMul
  },
  data () {
    return {
      inputvalue: '', // 待审批搜索
      columns: [
        { key: 'contractBagName', title: '合约包名称', tooltip: true },
        { key: 'contractName', title: '合约名称', tooltip: true },
        { key: 'contractReadableName', title: '应用名称', tooltip: true },
        { key: 'brief', title: '应用简介', tooltip: true },
        // { key: 'userLoginId', title: '提交审批人', tooltip: true },
        { key: 'createTime', title: '提交时间', tooltip: true },
        { key: 'bizType', title: '申请类型', tooltip: true },
        { key: 'action',
          title: '操作',
          align: 'left',
          // fixed: 'right',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    this.ApproDetails(params.index)
                  }
                }
              }, '审批')

            ])
          }
        }
      ],
      // 待审批数组
      tableData: [],
      tablePageParam: {
        pageSize: 10,
        pageIndex: 1
      },
      dataCount: 0
    }
  },
  methods: {
    // 待审批搜索
    searchList () {
      this.getTablist()
      this.contractName = ''
    },
    // 待审批分页事件
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTablist()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTablist()
    },
    // 审批跳转
    ApproDetails (index) {
      // console.log(this.tableData[index].sort)
      // console.log(this.tableData[index].contractId)
      let typeparams = {
        '申请上架': 'MARKET_AUDIT',
        '申请下架': 'MARKET_AUDIT_OFF',
        '恢复上架': 'MARKET_AUDIT_REON'
      }
      this.$router.push({
        name: 'pending_detail',
        params: {
          noticeid: this.tableData[index].noticeId, //
          bizType: typeparams[this.tableData[index].bizType], // 申请类型
          listId: this.tableData[index].sort,
          usercontract: this.tableData[index].contractId,
          applyReason: this.tableData[index].applyReason, // 申请内容
          languageType: this.tableData[index].languageType
          // params: { contractId: `${this.tableData[index].contractId}` }
        }
      })
    },
    getTablist () {
      let pendingList = {
        contractBagName: this.inputvalue,
        pageParam: this.tablePageParam
      }
      PendingList(pendingList).then(res => {
        let typeinfo = {
          'MARKET_AUDIT': '申请上架',
          'MARKET_AUDIT_OFF': '申请下架',
          'MARKET_AUDIT_REON': '恢复上架'
        }
        this.tableData = res.data.records.map(item => {
          return {
            ...item,
            bizType: typeinfo[item.bizType]
          }
        })

        this.dataCount = res.data.total
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  mounted () {
    this.getTablist()
  }

}
</script>
