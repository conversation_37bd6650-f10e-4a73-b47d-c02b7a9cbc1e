export function searchKey (value, list) {
  for (var item in list) {
    if (list[item].value === value) {
      return list[item].key
    }
  }
}

export function getKey (value, list) {
  for (var j = 0; j < list.length; j++) {
    if (list[j].accountName === value) {
      return list[j]
    }
  }
}

export function changeKey (list) {
  for (var i = 0; i < list.length; i++) {
    list[i].activePrivateKey = '......'
    list[i].ownerPrivateKey = '......'
  }
  return list
}
export function searchDictKey (value, list) {
  for (var item in list) {
    if (list[item].enumValue === value) {
      return list[item].enumKey
    }
  }
}
export function arrToDic (list) {
  let dic = {}
  for (let item in list) {
    dic[list[item].enumKey] = list[item].enumValue
  }
  return dic
}
export function optionList (list) {
  return list.map(element => {
    element['value'] = element.enumValue
    element['key'] = element.enumKey
    return element
  })
}
