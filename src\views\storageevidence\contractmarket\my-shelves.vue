<template>
  <div>
    <div class="search_title">
      <div class="search_title_left">
        <div>
          <!-- <b style="float:left;margin: 10px;">智能合约</b> -->
          <Input style="width:250px;vertical-align:baseline;" placeholder="可输入合约包名称或合约名称查询" v-model="inputvalue" @keyup.enter="searchList" @keyup.enter.native="searchList">
          <!-- <Icon type="ios-search" slot="suffix" @click="searchList" /> -->
          </Input>
        </div>
        <!-- 链类型查询 -->
        <div style="margin-left:8px">
          <Row>
            <div class="sl_timout">链类型</div>
            <Col span="12">
            <Select v-model="chain_level" style="width: 104px" placeholder="链类型" @on-change="changechaincity">
              <Option v-for="item in ChainList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
            </Col>
            <!-- <Button type="primary" icon="ios-search" @click.native="information">查询</Button> -->
          </Row>
        </div>
        <div style="margin-left:8px">
          <Row>
            <div class="sl_timout">合约语言</div>
            <Col span="12">
            <Select v-model="language_value" style="width: 104px" placeholder="请选择合约语言">
              <Option v-for="item in languageList" :value="item.enumKey" :key="item.enumKey">{{ item.enumValue }}</Option>
            </Select>
            <!-- <Select v-model="language_value" style="width: 104px" placeholder="合约语言">
            <Option value="C++">C++</Option>
            <Option value="JS">Java Script</Option>
            </Select> -->
            </Col>
            <!-- <Button type="primary" icon="ios-search" @click.native="information">查询</Button> -->
          </Row>
        </div>
        <div class="serch_btn">
          <Button type="primary" style="margin:0 15px;" icon="ios-search" @click.native="searchList">查询</Button>
          <Button type="primary" ghost icon="md-sync" @click.native="reset">重置</Button>
        </div>
      </div>

      <Button style="text-align:right;margin-left:5px;" @click="create" type="success" ghost>上传合约</Button>
    </div>

    <edit-table-mul :columns="myshelvesList" v-model="myshelvesData">
      <!-- <template slot-scope="{ row, index }" slot="action">
            <Button type="primary" size="small" style="margin-right: 5px" >{{row}}</Button>
        </template> -->
    </edit-table-mul>
    <Page placement='top' :total="myPageParam.pagetotal" :current.sync="myPageParam.pageIndex" @on-change="pageChange" :page-size="myPageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px" />
    <!-- 上传合约列表-->
    <Modal v-model="upmodal" title="上传合约" width='800px'>
      <div>
        <edit-table-mul :height="400" :columns="upColumns" v-model="upData"></edit-table-mul>
        <Page :total="upPageParam.pagetotal" :current.sync="upPageParam.pageIndex" @on-change="uppageChange" :page-size="upPageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="uppageSizeChange" style="text-align:right;line-height:40px" />
      </div>

    </Modal>
    <!-- 上传合约from详情 -->
    <Modal v-model="upmodaldetail" title="上传合约" width='900px' footer-hide @on-cancel="handleReset('formValidate')">
      <div class="shelvesInfo">
        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80">
          <FormItem label="合约包名称" prop="name">
            <Input v-model="formValidate.name" placeholder="请输入合约包名称" style="width: 300px" />
          </FormItem>
          <FormItem label="链类型" prop="">
            <p>{{TypeName.chainType}}</p>
            <!-- <Select v-model="formValidate.chaincity" placeholder="请选择链类型" style="width:200px" @on-change="changechaincity">
              <Option v-for="item in selectList" :value="item.enumValue" :key="item.enumKey">{{item.enumValue}}</Option>
            </Select> -->
          </FormItem>
          <FormItem label="合约语言" prop="">
            <p>{{TypeName.contractLanguage==='JS'?'Java Script':TypeName.contractLanguage}}</p>
            <!-- <Select v-model="formValidate.languagetype" placeholder="请选择语言类型" style="width:150px">
              <Option v-for="item in languageList" :value="item.enumValue" :key="item.enumKey">{{item.enumValue}}</Option>
            </Select> -->
          </FormItem>
          <FormItem label="适用场景信息" prop="describe">
            <Input v-model="formValidate.describe" placeholder="输入合约包适用场景信息" type="textarea" :maxlength="50" show-word-limit :autosize="{minRows: 3,maxRows: 5}" style="width: 350px" />
          </FormItem>
          <FormItem label="版本信息" prop="versioninfo" class="mandatory">
            <div>
              <edit-table-mul :height="200" border ref="selection" :columns="tableTitle" v-model="VersionData" @on-selection-change="getSelectAll"></edit-table-mul>
            </div>
          </FormItem>
          <div class="newFromSubmit">
            <Button @click="handleReset('formValidate')" style="margin-right: 10px">取消</Button>
            <Button type="primary" @click="handleSubmit('formValidate')" :loading="loadingStatus">{{ loadingStatus ? "审批中" : "提交审批" }}</Button>
            <!-- <Button type="primary" @click="handleSubmit('formValidate')" v-prevent-re-click :loading="loadingStatus">{{ loadingStatus ? "上传中" : "提交" }}</Button> -->
          </div>

        </Form>
      </div>

    </Modal>
    <!-- 查询合约链码 -->
    <Modal v-model="preview" title="查询合约链码" width='900px' @on-ok="ok" @on-cancel="ok">
      <p style="margin-bottom:20px">上传版本号：{{this.title}}</p>
      <Collapse simple @on-change="colldata" accordion>
        <Panel :name="transferKey1" :key="transferKey1">
          {{transferKey1}}
          <p slot="content">
            <textarea class="textarea-style" v-html="CollContent.fileContent" readonly @scroll="handScroll($event, 'cpp')"></textarea>
          </p>
        </Panel>
        <Panel :name="fileName" v-if="fileName">
          {{fileName}}
          <p slot="content">
            <textarea class="textarea-style" v-html="CollContent.fileContent" readonly @scroll="handScroll($event, 'js')"></textarea>
          </p>
        </Panel>
        <Panel :name="item" v-for="item in filesHpp" :key='item'>
          {{item}}
          <p slot="content">
            <textarea class="textarea-style" v-html="CollContent.fileContent" readonly @scroll="handScroll($event, 'hpp')"></textarea>
          </p>
        </Panel>
      </Collapse>
    </Modal>
  </div>
</template>

<script>
import EditTableMul from '_c/edit-table-mul'
import { isContractName } from '../../../lib/check'
import { ContractOwnerList, getContractTableData, getShelvesInfo, getTempateEos, getTempateLanguage, getContractChaincode, getShelves } from '@/api/data'
let mycolor = {
  '审核通过': 'green',
  '审核不通过': 'red',
  '审核中': 'orange'
}
export default {
  components: {
    EditTableMul
  },
  props: [],
  data () {
    const validateContractName = (rule, value, callback) => {
      let reg = /^[_a-zA-Z]/

      if (!reg.test(value.slice(0, 1))) {
        callback(new Error('只能以英文及下划线开头'))
      }
      if (!isContractName(value)) {
        callback(new Error('支持英文和数字，特殊符号只能有英文句号.和英文_'))
      } else {
        callback()
      }
    }
    return {
      loadingStatus: false,
      isshow: false,
      selectList: [], // 链类型数组
      languageList: [], // 语言类型数组
      upmodal: false, // 上传合约
      upmodaldetail: false, // 上传合约详情
      preview: false, // 查询合约链码
      inputvalue: '', // 我的上架搜索
      CollContent: {},
      chain_level: '', // 链类型
      codeData: {},
      filesHpp: [],
      title: '', // 查看文件源码标题
      transferKey1: '',
      tablePageParam: { pagetotal: 0, pageSize: 10, pageIndex: 1 },
      selecteos: 'CHAIN_TYPE', // 链类型传参
      selectlanguage: 'LANGUAGE_TYPE', // 语言类型传参
      // 我的上架列表
      myshelvesList: [
        { key: 'contractBagName', title: '合约包名称', tooltip: true },
        { key: 'contractName', title: '合约名称', tooltip: true },
        { key: 'chainType', title: '链类型', tooltip: true },
        { key: 'languageType', title: '合约语言', tooltip: true },
        { key: 'contractReadableName', title: '应用名称', tooltip: true },
        // { key: 'brief', title: '应用简介', tooltip: true },
        { key: 'createTime', title: '提交时间', tooltip: true },
        {
          key: 'auditStatus',
          title: '审核状态',
          tooltip: true,
          render: (h, params) => {
            return h('span', {
              style: {
                color: mycolor[params.row.auditStatus]
                // border: "1px solid #3D73EF"
              }

            }, params.row.auditStatus)
          }
        },
        { key: 'status', title: '上架状态', tooltip: true },
        {
          key: 'action',
          title: '操作',
          align: 'center',
          width: '200px',
          // fixed: 'right',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    this.ApproDetails(params.index)
                  }
                }
              }, '详情'),
              h('Button', {
                props: { type: 'text', size: 'small', disabled: (!params.row.latest) },
                style: {
                  marginRight: '8px',
                  // color: '#3D73EF',

                  border: `${!params.row.latest ? '1px solid #c5c8ce' : '1px solid #3D73EF'}`,
                  color: `${!params.row.latest ? '#c5c8ce' : '#3D73EF'}`,
                  background: `${!params.row.latest ? 'white' : 'none'}`,
                  display: `${params.row.isManager ? 'none' : 'inline-block'}`

                },
                on: {
                  click: () => {
                    this.UpShelves(params.index)
                  }
                }
              }, params.row.status === '未上架' ? '恢复上架' : '申请下架')

            ])
          }
        }
      ],
      myshelvesData: [],
      myPageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      // 上传合约列表
      upColumns: [
        { key: 'contractName', title: '合约名称', tooltip: true },
        { key: 'contractReadableName', title: '应用名称', tooltip: true },
        { key: 'brief', title: '应用简介', tooltip: true },
        {
          key: 'action',
          title: '操作',
          align: 'center',
          width: 200,
          // fixed: 'right',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small', disabled: (!params.row.existVersion) },
                style: {
                  marginRight: '8px',

                  border: `${!params.row.existVersion ? '1px solid #c5c8ce' : '1px solid #3D73EF'}`,
                  background: `${!params.row.existVersion ? 'white' : 'none'}`,
                  color: `${!params.row.existVersion ? '#c5c8ce' : '#3D73EF'}`
                },
                on: {
                  click: () => {
                    this.UpDetail(params.index)
                  }
                }
              }, '上架合约市场')

            ])
          }
        }
      ],
      upData: [],
      upPageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      // 合约详情列表
      VersionTitle: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '版本号',
          key: 'uploadVersion',
          width: 180,
          tooltip: true
        },
        {
          title: 'cpp文件名',
          key: 'cppFileName'

        },
        {
          title: 'hpp文件名',
          key: 'hppFileNames',
          tooltip: true,
          render: (h, params) => {
            return h('div', params.row.hppFileNames.join(','))
          }
        },
        {
          title: '上架状态',
          key: 'contractMarketStatus',
          tooltip: true
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      VersionData: [],
      // form表单
      formValidate: {
        name: '',
        chaincity: '', // 链类型
        languagetype: '', // 语言类型
        scenario: '',
        describe: ''
      },
      // from表单校验
      ruleValidate: {

        name: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
          { max: 32, message: '不能多于32位', trigger: 'blur' },
          { trigger: 'blur', validator: validateContractName }

        ],
        //
        chaincity: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur'
          }
        ],
        languagetype: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur'
          }
        ]

      },
      contractIds: '',
      AllList: [], // 选中的列表
      contractNameinfo: '', // 合约名称
      ChainList: [
        {
          value: 'EOS',
          label: 'EOS'
        },
        {
          value: 'BOS',
          label: 'BOS'
        },
        {
          value: 'CMEOS',
          label: 'CMEOS'
        },
        {
          value: 'ChainMaker',
          label: 'ChainMaker'
        }
      ],
      Manager: '',
      // 以下是js添加的
      TypeName: {}, // 合约语言
      columnsJs: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '版本号',
          key: 'uploadVersion',
          tooltip: true
        },
        {
          title: 'JavaScript文件名',
          key: 'jsFileName',
          tooltip: true,
          width: 150
        },
        {
          title: 'abi文件名',
          key: 'abiFileName'
        },
        {
          title: '上架状态',
          key: 'contractMarketStatus',
          tooltip: true
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      tableTitle: [],
      fileName: '',
      language_value: ''

    }
  },
  methods: {
    changechaincity (value) {
      this.language_value = ''
      if (value === 'EOS' || value === 'BOS') {
        this.languageList = [
          { enumKey: 'C++', enumValue: 'C++' }
        ]
      } else if (value === 'CMEOS') {
        this.languageList = [
          { enumKey: 'C++', enumValue: 'C++' },
          { enumKey: 'JS', enumValue: 'Java Script' }
        ]
      } else if (value === 'ChainMaker') {
        // GO、C++、rust、tinygo、solidity
        this.languageList = [
          { enumKey: 'GO', enumValue: 'GO' },
          { enumKey: 'C++', enumValue: 'C++' },
          { enumKey: 'RUST', enumValue: 'RUST' },
          { enumKey: 'TINYGO', enumValue: 'TINYGO' },
          { enumKey: 'SOLIDITY', enumValue: 'SOLIDITY' }
        ]
      } else {
        this.languageList = []
      }
    },
    tabsFun (param) {
      if (param === 'name2') {
        this.getTablist()
      }
    },
    // 上架按钮
    UpShelves (index) {
      let reslt = {
        已上架: 'off',
        未上架: 'recover'
      }
      this.$router.push({
        name: 'my_applyfordetail',
        params: {
          status: reslt[this.myshelvesData[index].status],
          contractId: this.myshelvesData[index].contractId,
          sort: this.myshelvesData[index].sort
        }
      })
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, top: 300 }) },
    ok () {
      this.preview = false
      this.upmodaldetail = true
    },
    // 清空上传合约from表单
    empty () {
      this.formValidate.name = ''
      this.formValidate.chaincity = ''
      this.formValidate.languagetype = ''
      this.formValidate.describe = ''
    },
    // 我的上架搜索
    searchList () {
      this.getTablist()
      // this.inputvalue = ''
      // this.contractName = ''
    },
    // 重置
    reset () {
      this.inputvalue = ''
      this.chain_level = ''
      this.language_value = ''
      this.myPageParam = { pagetotal: 0, pageSize: 10, pageIndex: 1 }
      this.getTablist()
    },
    // 我的上架分页事件
    pageChange (index) {
      this.myPageParam.pageIndex = index
      this.getTablist()
    },
    pageSizeChange (index) {
      this.myPageParam.pageSize = index
      this.getTablist()
    },
    // 上传合约分页
    uppageChange (index) {
      this.upPageParam.pageIndex = index
      this.create()
    },
    uppageSizeChange (index) {
      this.upPageParam.pageSize = index
      this.create()
    },
    // 我的上架列表详情跳转
    ApproDetails (index) {
      this.$router.push({
        name: 'my_detail',
        params: {
          contractId: this.myshelvesData[index].contractId,
          sort: this.myshelvesData[index].sort,
          languageType: this.myshelvesData[index].languageType === 'JavaScript' ? 'JS' : this.myshelvesData[index].languageType
        }

      })
    },
    // 上传合约按钮点击事件
    create () {
      this.upmodal = true
      getContractTableData(this.upPageParam).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          // let resdata = {
          //   '1': '合约模板',
          //   '2': '手动新增'
          // }
          // let tableData = res.data.records.map(item => {
          //   return {
          //     ...item,
          //     contractSources: resdata[item.contractSources]
          //   }
          // })
          this.upData = res.data.records
          // console.log(res)
          this.upPageParam.pagetotal = res.data.total
        }
        ++this.transferKey
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 详情跳转
    UpDetail (index) {
      this.upmodaldetail = true
      this.upmodal = false
      this.contractIds = this.upData[index].contractId
      this.contractNameinfo = this.upData[index].contractName
      this.Manager = this.upData[index].isManager
      this.tableTitle = this.upData[index].languageType === 'C++' ? this.VersionTitle : this.columnsJs
      getShelvesInfo(this.contractIds).then(res => {
        if (res.code === '00000') {
          this.TypeName = res.data[0]
          if (res.code === '00000') {
            let statusdata = {
              1: '已上架',
              2: '未上架'
            }
            let versionData = res.data.map(item => {
              return {
                ...item,
                hppFileNames: item.hppFileNames ? item.hppFileNames : [],
                contractMarketStatus: statusdata[item.contractMarketStatus]
              }
            })
            this.VersionData = versionData
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
      getTempateEos(this.selecteos).then(res => {
        if (res.code === '00000') {
          this.selectList = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
      // getTempateLanguage(this.selectlanguage).then(res => {
      //   this.languageList = res.data
      // })
    },
    // 多选事件
    getSelectAll (list) {
      this.AllList = list.map(item => item.uploadVersion)
    },
    // 详情取消事件
    handleReset (name) {
      this.$refs[name].resetFields()
      this.upmodaldetail = false
      this.upmodal = true
      this.empty()
    },
    // 提交审批
    handleSubmit () {
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          this.loadingStatus = true
          if (this.AllList.length > 0) {
            let fromList = {
              contractName: this.contractNameinfo, // 合约名称
              contractId: this.contractIds, // 合约id
              contractBagName: this.formValidate.name, // 合约包名称
              chainType: this.TypeName.chainType, // 链类型
              contractLanguage: this.TypeName.contractLanguage, // 合约语言
              applicaSecene: this.formValidate.describe, // 适用场景
              selectVersion: this.AllList // 版本信息
            }
            console.log(fromList)
            getShelves(fromList).then(res => {
              if (res.code === 'A1503') {
                this.msgInfo('warning', '已存在此合约包名称，请修改重试', true)
                this.loadingStatus = false
              } else if (res.code === 'A1505') {
                this.msgInfo('warning', '该合约下存在待审批的上架记录，不允许再次上架', true)
                this.loadingStatus = false
              } else if (res.code === '500') {
                this.msgInfo('error', res.message, true)
                this.loadingStatus = false
              } else if (res.code === '00000') {
                this.loadingStatus = false
                this.upmodaldetail = false
                this.upmodal = false
                // this.$Message.success('新建合约类型成功!')
                this.$Message.success(this.Manager === true ? '已提交合约上架信息' : '已提交合约上架信息，需等待平台管理员审批!')
                this.empty()
                this.getTablist()
              }
            }).catch((error) => {
              this.loadingStatus = false
              this.msgInfo('error', error.message, true)
            })
          } else {
            this.msgInfo('warning', '请勾选版本信息！', true)
            this.loadingStatus = false
          }
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
          this.loadingStatus = false
        }
      })
    },
    // 查询合约链码
    fileModal (params) {
      this.preview = true
      this.upmodaldetail = false
      this.title = params.row.uploadVersion
      if (params.row.contractLanguage === 'C++') {
        this.fileName = ''
        this.transferKey1 = params.row.cppFileName
        this.filesHpp = params.row.hppFileNames
      } else {
        this.transferKey1 = params.row.jsFileName
        this.fileName = params.row.abiFileName
      }
      this.codeData = {
        contractId: params.row.contractId,
        uploadVersion: params.row.uploadVersion,
        fileName: params.row.cppFileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
    },
    colldata (key) {
      // console.log(this.codeData)
      // console.log(key)
      if (key[0]) {
        this.codeData.fileName = key[0]
        getContractChaincode(this.codeData.contractId, this.codeData.uploadVersion, this.codeData.fileName, this.codeData.pageParam).then(res => {
          if (res.code === '00000') {
            this.CollContent = res.data
          } else {
            this.msgInfo('error', res.message, true)
          }
        }).catch((error) => {
          this.msgInfo('error', error.message, true)
        })
      }
    },
    // 查询合约链码关闭事件
    handle1preview () {
      this.upmodaldetail = true
      this.preview = false
    },
    // 请求
    getTablist () {
      let contractData = {
        pageParam: this.myPageParam, // 分页
        contractBagName: this.inputvalue,
        chainType: this.chain_level,
        languageType: this.language_value
      }
      ContractOwnerList(contractData).then(res => {
        if (res.code === '00000') {
          let auditStatusdata = {
            'UNAPPROVED': '审核中',
            'APPROVED': '审核通过',
            'REJECT': '审核不通过'
          }
          let statusdata = {
            1: '未上架',
            2: '已上架'
          }
          let myshelvesData = res.data.records.map(item => {
            return {
              ...item,
              auditStatus: auditStatusdata[item.auditStatus],
              status: statusdata[item.status],
              languageType: item.languageType === 'JS' ? 'JavaScript' : item.languageType
            }
          })
          this.myshelvesData = myshelvesData
          // console.log(this.myshelvesData)
          this.myPageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  activated () {
    this.getTablist()
  }

}
</script>

<style lang="less" scoped>
// from表单
.shelvesInfo {
  padding: 2%;
  // border: 1px solid red;
  /deep/.ivu-form-item-label {
    width: 110px !important;
  }
  /deep/.ivu-form-item-content {
    margin-left: 110px !important;
  }

  .mandatory {
    /deep/.ivu-form-item-label::before {
      content: "*";
      display: inline-block;
      margin-right: 4px;
      line-height: 1;
      font-family: SimSun;
      font-size: 14px;
      color: #ed4014;
    }
  }
}
// 滚动条
.textarea-style {
  width: 820px;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8) !important;
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
.search_title {
  display: flex;
  justify-content: space-between;
  .search_title_left {
    display: flex;
  }
  .sl_timout {
    height: 33px;
    padding: 5px 8px;
    text-align: center;
    border-radius: 4px;
  }
}
</style>
