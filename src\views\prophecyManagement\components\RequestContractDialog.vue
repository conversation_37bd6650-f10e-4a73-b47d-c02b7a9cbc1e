<!--
  aturun
 智能合约选择
  2021/10/21

-->
<template>
  <el-dialog
      class="dialog_sty smart_contract"
      :title="tableTitle"
      :visible.sync="Visible"
      width="1000px"
      :modal="false"
      destroy-on-close
      :close-on-click-modal="false"
      :before-close="handleClose"
      @opened="open">
    <div>
      <div class="dialog_content">
        <div class="content-top">
          <div class="content-top-lift">
            <span @click="goBack()" v-if="tableActive!=0"><-返回</span>{{tableTitle}}
          </div>
          <div class="content-top-right">
            <div class="top-right-input icon-search_suffix" v-if="operationState==1">
              <el-input
                  :placeholder="inpPlaceholder"
                  v-model="input"
                  @keyup.enter.native="getlist">
              </el-input>
              <i slot="suffix" class="el-icon-search" @click="getlist"></i>
            </div>

          </div>

        </div>
        <div class="content-body">
          <el-table
              :data="tableData"
              style="width: 100%"
              height="640"
              stripe
          >
            <el-table-column :prop="item.field" :label="item.lable" v-for="(item,index) in tableTitleInfo" :key="index"></el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button
                    v-if="operationState==1"
                    size="mini"
                    @click="handleChoice(scope.$index, scope.row)">选择</el-button>
                <el-button
                    v-if="operationState==0"
                    size="mini"
                    @click="handlelook(scope.$index, scope.row)">{{ tableActive==2?'关闭':'查看详情' }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
<!--    <span slot="footer" class="dialog-footer">-->
<!--    <el-button @click="Visible = false">取 消</el-button>-->
<!--    <el-button type="primary" @click="submitForm">确 定</el-button>-->
<!--  </span>-->
  </el-dialog>
</template>

<script>
import * as api from "../api";
export default {
  name: "SmartContractDialog",
  components: {},
  props:[],
  data() {
    return {
      eosChainId:null,
      sourceMode:null,//0请求合约1消费者合约
      operationState:0,
      tableTitle:'请求合约',
      tableTitleInfo:[],
      inpPlaceholder:'可输入合约链账户查询',
      input:'',
      isVersion:true,
      Visible:false,
      ChoiceValue:'',
      Form:{
        name:''
      },
      tableActive:0,
      ContractChainAccountT:[ //1 合约链账户
        {lable:'链账户名称',field:'chainAccountName'},
        {lable:'描述',field:'description'},
        {lable:'创建时间',field:'createTime'},
      ],
      SmartContractT:[ //2 智能合约
        {lable:'合约名称',field:'contractName'},
        {lable:'描述',field:'brief'},
        {lable:'创建时间',field:'createTime'},
      ],
      VersionT:[ //3 版本
        {lable:'版本号',field:'version'},
        {lable:'CPP文件名',field:'yy'},
        {lable:'hpp文件名',field:'uu'},
        {lable:'上传备注',field:'ii'},
        {lable:'上传时间',field:'oo'},
      ],
      tableData: [

      ],
      rules: {
        name: [
          {required: true, message: '请输入用户名称', trigger: 'blur'},
        ],
      },
      ChoiceForm:{
        ContractChainAccount:'',
        SmartContract:'',
        Version:''
      }
    }
  },

  watch: {
    tableActive(newVal){
      if(newVal==0){
        this.tableTitle = '合约链账户'
        this.inpPlaceholder = '可输入合约链账户查询'
        this.tableTitleInfo = this.ContractChainAccountT
        this.getHttpUserList()
      } else if(newVal==1){
        this.tableTitle = '智能合约'
        this.inpPlaceholder = '可输入合约名称查询'
        this.tableTitleInfo = this.SmartContractT
        this.getHttpContractList()
      }else if(newVal==2){
        this.tableTitle = '合约版本号'
        this.inpPlaceholder = '可输入合约版本号查询'
        this.tableTitleInfo = this.VersionT
        this.tableData= [
          {version:'11.25',yy:'yy',uu:'uugfgfh',ii:'rrrr',oo:'sdffdfsfs'},
          {version:'444444',yy:'dgfdsfgds',uu:'asdfgadsc',ii:'qwqq',oo:'retret5'},
        ]
      }else {

      }
     // if(this.operationState==0){
     //   this.tableTitle = '合约示例'
     // }
    }
  },
  created() {
    this.tableTitleInfo = this.ContractChainAccountT
    // let val = []
    // val=this.ChoiceValue.split(',')
    // console.log(val)
  },
  mounted() {
  },
  methods: {
    getlist(){
      if(this.tableActive==0){
        this.getHttpUserList()
      }else if(this.tableActive==1){
        this.getHttpContractList()
      }else if(this.tableActive==2){

      }
    },
    open(){
      this.getHttpUserList()
    },
    Refresh(){
      this.tableActive=0
    },
    getHttpUserList(){
      api.getEosUserList(
        this.eosChainId,
        {
          "accountType": "",
          "bizType": "",
          "chainAccountName": "",
          "contractId": null,
          "pageParam": {
            "pageIndex": 1,
            "pageSize": 100
          },
          "status": "",
          "tenantName": ""
        }
      ).then(res=>{
        let arr = res.data.records
        this.tableData = []
        arr.map(item=>{
          if(item.accountTypeKey=='CONTRACT'){
            this.tableData.push(item)
          }
        })
      })
    },
    getHttpContractList(){
      api.getHttpContractList({
        "pageParam": {
          "pageIndex": 1,
          "pageSize": 100
        },
        "queryName": ""
      }).then(res=>{
        this.tableData = []
        this.tableData=res.data.records
      })
    },
    goBack(){
      let val = []
      val=this.ChoiceValue.split(',')
      if(this.tableActive==1){
        this.ChoiceValue = ''
      } else if(this.tableActive==2){
        this.ChoiceValue = val[0]
      }
      this.tableActive --
    },
    submitForm() {

      this.$emit('setChoiceInfo', {ChoiceValue:this.ChoiceValue,ChoiceForm:this.ChoiceForm})
      this.ChoiceValue = ''
      this.eosChainId = null
      this.ChoiceForm={
            ContractChainAccount:'',
            SmartContract:'',
            Version:''
      }
      this.tableActive=0
      this.Visible =false
    },
    resetForm() {
      this.$refs['ss'].resetFields();
    },
    handleClose(done) {
      this.Refresh()
       done();
    },
    handleChoice(index, row) {
    if(this.tableActive==0){
      this.ChoiceValue = row.chainAccountName
      this.ChoiceForm={...this.ChoiceForm,
        ContractChainAccount:JSON.stringify(row),
            SmartContract:JSON.stringify(row),
            Version:''
      }
      this.tableActive++
      } else if(this.tableActive==1){
      this.ChoiceValue =this.ChoiceValue+','+ row.contractName
      this.ChoiceForm={
        ...this.ChoiceForm,
        SmartContract:JSON.stringify(row),
      }
      this.tableActive++
    } else if(this.tableActive==2){
      this.ChoiceValue =this.ChoiceValue+','+ row.version
      this.ChoiceForm={
        ...this.ChoiceForm,
        Version:row.version
      }

      this.submitForm()
      }
    },
    handlelook(index, row) {
      if(this.tableActive==0){
        this.tableActive++
      } else if(this.tableActive==1){
        this.tableActive++
      } else if(this.tableActive==2){
        this.tableActive=0
        this.Visible =false
      }
    },

  },

}
</script>

<style lang="less" scoped>
.smart_contract{
  .dialog_content{
    .content-top{
      display: flex;
      justify-content: space-between;
      .content-top-right{
        display: flex;
        padding: 16px 20px 0 0;
        .top-right-input{
          margin-right: 12px;
        }
        .top-right-button{
          .el-button{

          }
        }
      }
      .content-top-lift{
        padding: 25px 0 0 23px;
        font-size: 14px;
        font-weight: 500;
        line-height: 24px;
        color: #333333;
        opacity: 1;
        span{
          color: #2D8CF0;
        }
      }
    }
    .content-body{
      margin: 11px 17px 0 16px;
    }
    .table_pag{
      margin: 12px 16px 0 0;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
