export function fileTest (file, maxsize, typeList) {
  let obj = { flag: true, msg: '' }
  if (file.size / 1024 / 1024 <= maxsize / 1024) {
    let typeString = file.type.split('/')[1]
    let flag = false
    typeList.forEach(val => {
      if ((typeString && typeString.indexOf(val) !== -1) || file.name.indexOf(`.${val}`) !== -1) {
        flag = true
      }
    })
    if (!flag) {
      obj.flag = false
      obj.msg = file.name + '格式不正确,只能为' + typeList.toString()
    }
  } else {
    obj.flag = false
    obj.msg = '文件' + file.name + '太大,不能超过' + maxsize / 1024 + 'M'
  }
  return obj
}
