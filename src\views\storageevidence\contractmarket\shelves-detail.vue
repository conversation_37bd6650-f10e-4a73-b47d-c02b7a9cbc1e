<template>
  <div>
    <Collapse v-model="panelValue" simple name="mainpanel">
      <Panel name="1" style="background:#ECEFFC;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        合约上架信息
        <div slot="content">
          <div class="management_middle">
            <Row class="top_center">
              <Col span="6">合约包名称： <span>{{detailData.contractBagName}}</span></Col>
              <Col span="6">合约名称： <span>{{detailData.contractName}}</span></Col>
              <Col span="6">应用名称： <span>{{detailData.contractReadableName}}</span></Col>

            </Row>
            <Row class="middle_center">
              <Col span="6">应用简介： <span>{{detailData.brief}}</span></Col>
              <Col span="6">发布者： <span>{{detailData.createUser}}</span></Col>
              <Col span="6">链类型： <span>{{detailData.chainType}}</span></Col>
            </Row>
            <Row class="middle_center">
              <Col span="6">合约语言： <span>{{detailData.contractLanguage==='JS'?'Java Script':detailData.contractLanguage}}</span></Col>
              <Col span="6">发布时间： <span>{{detailData.createTime}}</span></Col>
              <Col span="6">大小： <span>{{detailData.fileSize==='0'?'--':detailData.fileSize}}</span></Col>

            </Row>
            <Row class="middle_center">
              <Col span="24">适用场景信息： <span>{{detailData.applicaSecene}}</span></Col>
            </Row>
          </div>

        </div>

      </Panel>
      <Panel name="2" style="background:#ECEFFC;display:block;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        版本信息
        <div slot="content" style="padding-top:1%">
          <edit-table-mul :height="200" border :columns="tableTitle" v-model="VersionData"></edit-table-mul>
          <!-- <Button :size="buttonSize" type="primary" @click="$router.back(-1)" style="margin-left: 90%;width: 100px;margin-top: 2%;">返回</Button> -->
        </div>
      </Panel>
    </Collapse>

    <Modal v-model="chaincode" title="查询合约链码" width='900px'>
      <p style="margin-bottom:20px">上传版本号：{{this.title}}</p>
      <div v-if="isSingleCpp=='0'">
        <Layout>
          <Sider hide-trigger :style="{background: '#fff'}">
            <Menu theme="light" width="auto" :open-names="['1']">
              <Submenu :name="key" v-for="(item,key) in cppsTitle" :key="key">
                <template slot="title">
                  <Icon type="ios-folder"></Icon>
                  {{key}}
                </template>
                <MenuItem :name="items.fileName" v-for="(items,i) in item" :key="i" @click.native="clickCpps(items.content)">{{items.fileName}}</MenuItem>
              </Submenu>
            </Menu>
          </Sider>
          <Content :style="{padding: ' 0 24px', minHeight: '280px', background: '#fff'}">
            <p>
              <textarea class="textarea-style" v-html="cppContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
            </p>
          </Content>
        </Layout>
      </div>
      <div v-else>
        <Collapse simple accordion v-if="this.detailData.contractLanguage === 'C++'">
          <Panel :name="transferKey1" :key="transferKey1">
            {{transferKey1}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.cppcentent.fileContent" readonly @scroll="handScroll($event, 'cpp')"></textarea>
            </p>
          </Panel>
          <Panel :name="item" v-for="item in filesHpp" :key='item'>
            {{item}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.hppcentent.fileContent" readonly @scroll="handScroll($event, 'hpp')"></textarea>
            </p>
          </Panel>

        </Collapse>
        <Collapse simple accordion v-else>
          <Panel :name="transferKey1" :key="transferKey1">
            {{transferKey1}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.jscentent.fileContent" readonly @scroll="handScroll($event, 'js')"></textarea>
            </p>
          </Panel>
          <Panel :name="fileName" v-if="fileName">
            {{fileName}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.abicentent.fileContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
            </p>
          </Panel>
        </Collapse>
      </div>

    </Modal>
    <!-- 详情弹框 -->
    <!-- <Modal v-model="versionmodal" title="版本详情" width='900px'>
      <div class="versionDetailone">
        <div class="detailModalInfo">
          <h3>运维信息</h3>
        </div>
        <p class="detailModal">
           合约名称：<span>{{detailData.contractName}}</span>
        </p>
        <p class="detailModal">
          版本号：<span>{{this.uploadVersion}}</span>
        </p>
        <p class="detailModal">
          合约类型：<span>{{contractyype}}</span>
        </p>
        <p class="detailModal">
          TPS预估：<span>{{tps}}</span>
        </p>
      </div>
      <div class="versionDetailtwo">
         <ul class="pending_ui">
          <li> 运维联系人：<span v-if="opsLinkman.tenantName"><i class="ri-organization-chart"></i>{{opsLinkman.tenantName}}</span> </li>
          <li> <span v-if="opsLinkman.name"><i class="ri-user-line"></i>{{opsLinkman.name}}</span> </li>
          <li><span v-if="opsLinkman.phone"><i class="ri-smartphone-line"></i>{{opsLinkman.phone}}</span> </li>
        </ul>
        <ul class="pending_ui">
          <li> 需求联系人：<span v-if="demandSide.tenantName"><i class="ri-organization-chart"></i>{{demandSide.tenantName}}</span> </li>
          <li> <span v-if="demandSide.name"><i class="ri-user-line"></i>{{demandSide.name}}</span> </li>
          <li><span v-if="demandSide.phone"><i class="ri-smartphone-line"></i>{{demandSide.phone}}</span> </li>
        </ul>

         <ul class="pending_ui" v-for="item in callData" :key="item.id">
            <li>调用联系人： <span v-if="item.tenantName"><i class="ri-organization-chart"></i>{{item.tenantName}}</span> </li>
          <li> <span v-if="item.name"><i class="ri-user-line"></i>{{item.name}}</span> </li>
          <li><span v-if="item.phone"><i class="ri-smartphone-line"></i>{{item.phone}}</span> </li>
        </ul>
      </div>

    </Modal> -->
    <Button type="primary" @click="back">返回</Button>
  </div>
</template>
<script>
import EditTableMul from '_c/edit-table-mul'
import { getcontractBagId, getChaincode, getDownDe } from '@/api/data'
// 使用全局变量存储窗口引用，避免Vue响应式系统处理窗口对象
let globalTargetWin = null;
export default {
  components: {
    EditTableMul
  },
  data () {
    return {
      callData: [], // 调用联系人
      panelValue: ['1', '2'],
      chaincode: false, // 查询合约链码弹框
      versionmodal: false, // 版本详情弹框
      detailData: {},
      transferKey1: '',
      VersionTitle: [

        {
          title: '版本号',
          key: 'uploadVersion',
          with: 180,
          tooltip: true
        },
        {
          title: 'cpp文件名',
          key: 'cppFileName'

        },
        {
          title: 'hpp文件名',
          key: 'hppFileNames',
          tooltip: true,
          render: (h, params) => {
            return h('div', params.row.hppFileNames.join(','))
          }
        },
        {
          title: '上传备注',
          key: 'uploadBrief',
          tooltip: true
        },
        {
          title: '下载量',
          key: 'downloadCount',
          with: 100

        },
        {
          title: '上传时间',
          key: 'uploadTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 260,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              // h(
              //   'Button',
              //   {
              //     props: {
              //       type: 'text',
              //       size: 'small'
              //     },
              //     style: {
              //       marginRight: '8px',
              //       color: '#3D73EF',
              //       border: '1px solid #3D73EF'
              //     },
              //     on: {
              //       click: () => {
              //         this.detailModal(params)
              //       }
              //     }
              //   },
              //   '详情'
              // ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.down(params)
                    }
                  }
                },
                '文件下载'
              )

            ])
          }
        }
      ],
      VersionData: [],
      opsLinkman: {},
      demandSide: {},
      tps: '',
      contractyype: '',
      fileTpye: [],
      CollContent: { cppcentent: {}, hppcentent: {}, jscentent: {}, abicentent: {} },
      codeData: {},
      filesHpp: [],
      title: '', // 查看文件源码标题
      // 以下是新加js
      columnsJs: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '版本号',
          key: 'uploadVersion',
          tooltip: true
        },
        {
          title: 'JavaScript文件名',
          key: 'jsFileName'
        },
        {
          title: 'abi文件名',
          key: 'abiFileName'
        },
        {
          title: '上传备注',
          key: 'uploadBrief',
          tooltip: true
        },
        {
          title: '下载量',
          key: 'downloadCount',
          with: 80

        },
        {
          title: '上传时间',
          key: 'uploadTime'
        },
        {
          title: '操作',
          key: 'action',
          width: 260,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.down(params)
                    }
                  }
                },
                '文件下载'
              )

            ])
          }
        }
      ],
      tableTitle: [],
      fileName: '',
      cppContent: '请选择要看的源码文件',
      cppsTitle: '',
      isSingleCpp: '',
      targetWin: '',
      funcName: '',
      bg: false,
      jin: false,
      downid: '',
      downcontractBagName: ''
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, top: 300 }) },
    // 点击文件源码
    fileModal (params) {
      this.chaincode = true
      this.title = params.row.uploadVersion
      this.codeData = {
        contractId: params.row.contractId,
        uploadVersion: params.row.uploadVersion
      }
      this.isSingleCpp = params.row.isSingleCpp
      this.cppsTitle = params.row.fileContent
      if (this.detailData.contractLanguage === 'C++' && this.isSingleCpp == '1') {
        this.transferKey1 = params.row.cppFileName
        this.filesHpp = params.row.hppFileNames
        this.getCode(params.row.cppFileName, 'cpp')
        if (params.row.hppFileNames && params.row.hppFileNames.length > 0) {
          params.row.hppFileNames.forEach(val => this.getNewCode(val, 'hpp'))
        }
      } else {
        this.transferKey1 = params.row.jsFileName
        this.fileName = params.row.abiFileName
        this.getCode(params.row.jsFileName, 'js')
        this.getNewCode(params.row.abiFileName, 'abi')
      }
    },
    getCode (fileName) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        isSingleCpp: this.isSingleCpp,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          if (this.detailData.contractLanguage === 'C++') {
            this.CollContent.cppcentent = res.data
            this.cppsTitle = res.data.fileContent
          } else {
            this.CollContent.jscentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    getNewCode (fileName, val) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          if (this.detailData.contractLanguage === 'C++') {
            this.CollContent.hppcentent = res.data
          } else {
            this.CollContent.abicentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    back () {
      this.$router.push({
        name: 'contract_market'
      })
    },
    // colldata (key) {
    //   // console.log(this.codeData)
    //   if (key[0]) {
    //     this.codeData.fileName = key[0]
    //     getChaincode(this.codeData).then(res => {
    //       if (res.code === '00000') {
    //         this.CollContent = res.data
    //       } else {
    //         this.msgInfo('error', res.message, true)
    //       }
    //     }).catch((error) => {
    //       this.msgInfo('error', error.message, true)
    //     })
    //   }
    // },
    // 点击详情
    // detailModal (params) {
    //   this.versionmodal = true
    //   this.uploadVersion = params.row.uploadVersion
    //   console.log(this.uploadVersion)
    //   let contractId = params.row.contractId
    //   getAccountOpsDTOMessage(this.uploadVersion, contractId).then(res => {
    //     if (res.code === '00000') {
    //       this.callData = res.data.caller
    //       this.opsLinkman = res.data.opsLinkman
    //       this.demandSide = res.data.demandSide
    //       this.tps = res.data.tps
    //       this.contractyype = res.data.contractTypeDesc
    //     }
    //   })
    // },

    handScroll (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    // 请求方法
    getcontract () {
      // console.log(this.$route.params.languageType)
      this.tableTitle = this.$route.params.languageType === 'JS' ? this.columnsJs : this.VersionTitle
      getcontractBagId(this.$route.params.contractId).then(res => {
        if (res.code === '00000') {
          this.detailData = res.data

          let dataList = res.data.records.map(item => {
            return {
              ...item,
              hppFileNames: item.hppFileNames ? item.hppFileNames : []
            }
          })
          this.VersionData = dataList
        } else {
          this.$Message.error(res.message)
        }
      }).catch(error => {
        this.$Message.error(error.message)
      })
    },

    showBank (func) {
      var iWidth = 700; //模态窗口宽度
      var iHeight = 450;//模态窗口高度
      var iTop = (window.screen.height - iHeight - 100) / 2;
      var iLeft = (window.screen.width - iWidth) / 2;
      var winOption = 'height=' + iHeight + ',innerHeight=' + iHeight + ',width=' + iWidth + ',innerWidth=' + iWidth + ',top=' + iTop + ',left=' + iLeft + ',toolbar=no,menubar=no,scrollbars=no,resizeable=no,location=no,status=no';
      var obj = new Object();
      obj.operCode = localStorage.getItem("sourceCodeEncrypt");
      obj.mainLoginName = "";
      obj.subLoginName = localStorage.getItem("userNameEncrypt")
      obj.appCode = "JTNGCMBAAS";
      obj.sessionId = sessionStorage.getItem("session");
      obj.serverIp = window.location.host.split(":")[0]
      obj.serverPort = window.location.port;
      obj.checkSessionUrl = "/";
      obj.svcNum = "";
      obj.operContent = "";
      // var returnValue;
      //增加浏览器的判断，ie走if原有逻辑，非ie走else逻辑,通过遮罩层实现。
      var a1 = navigator.userAgent;
      var yesIE = a1.search(/Trident/i);
      if (window.ActiveXObject || window.attachEvent || yesIE > 0) { //IE
        // var returnValue = window.showModalDialog("b.html?id=" + new Date(), obj, "dialogHeight:" + iHeight + "px; dialogWidth:" + iWidth + "px; toolbar:no; menubar:no;  titlebar:no; scrollbars:yes; resizable:no; location:no; status:no;left:" + iLeft + "px;top:" + iTop + "px;");
        var me = new Object();
        // me.data = returnValue;
        this.receiveMsg(me);
      } else {  //非IE
        // this.showDiv();//显示遮罩层
        this.openWindowWithPostRequest(iWidth, iHeight, iTop, iLeft, winOption, obj);
        // this.receiveMsg({
        //   data: '1#****************'
        // })
        if (window.addEventListener) {
          //为window注册message事件并绑定监听函数
          window.addEventListener('message', this.receiveMsg, false);
        } else {
          window.attachEvent('message', this.receiveMsg);
        }
      }
    },

    openWindowWithPostRequest (iWidth, iHeight, iTop, iLeft, winOption, obj) {
      var winName = "sWindow";
      var winURL = "http://api.it4a.cmit.cmcc:7081/uac/web3/jsp/goldbank/goldbank3!goldBankIframeAction.action";//应用侧对应后台服务action
      var form = document.createElement("form");
      form.setAttribute("method", "post");
      form.setAttribute("action", winURL);
      form.setAttribute("target", winName);
      for (var i in obj) {
        if (obj.hasOwnProperty(i)) {
          var input = document.createElement('input');
          input.type = 'hidden';
          input.name = i;
          input.value = obj[i];
          form.appendChild(input);
        }
      }
      document.body.appendChild(form);
      //打开地址，刚开始时，打开一个不存在的地址，这样才有返回值
      globalTargetWin = window.open("", winName, winOption);
      form.target = winName;
      form.submit();
      document.body.removeChild(form);
      if (window.focus) {
        globalTargetWin.focus();
      }
    },

    //接收返回值后处理函数
    receiveMsg (e) {
      // returnValue = e.data;
      console.log(e, 'eeeeeeeeeeeeeee');
      if (e.data && typeof e.data === 'string') {
        var dataStatus = e.data.split("#")
        const statusMessageMap = {
          '-3': { message: '金库应急开启中，允许业务继续访问', allowAccess: true },
          '-2': { message: '金库场景或元业务未开启，允许业务继续访问', allowAccess: true },
          '-1': { message: '直接关闭窗口，未申请审批，不允许业务继续访问', allowAccess: false },
          '1': { message: '审批通过，允许业务继续访问', allowAccess: true },
          '0': { message: '审批不通过，不允许业务继续访问', allowAccess: false },
          '2': { message: '超时，允许业务继续访问', allowAccess: true },
          '3': { message: '超时，不允许业务继续访问', allowAccess: false },
          '4': { message: '出现错误或异常（包括数据异常），不允许业务继续访问', allowAccess: false },
          '5': { message: '未配置策略，允许业务继续访问', allowAccess: true },
          '6': { message: '未配置策略，不允许继续访问', allowAccess: false }
        };

        const status = dataStatus[0];
        const statusInfo = statusMessageMap[status];
        console.log(statusInfo, 'statusInfo');
        if (statusInfo) {
          if (statusInfo.allowAccess) {
            // 允许业务继续访问，继续请求接口
            // 这里调用你继续请求接口的函数
            getDownDe(this.downid, '', this.downcontractBagName, e.data).then(res => {
              let reader = new FileReader();
              reader.readAsText(res);
              reader.onload = () => {
                try {
                  let jsonData = JSON.parse(reader.result);
                  if (jsonData.code === '00000') {
                    // this.showBank()
                    this.msgInfo('warning', jsonData.message, true)
                  } else {
                    this.msgInfo('error', jsonData.message, true)
                  }
                } catch (e) {
                  let blob = new Blob([res], { type: 'application/zip' })
                  let downloadElement = document.createElement('a')
                  let href = window.URL.createObjectURL(blob)
                  downloadElement.href = href
                  downloadElement.download = item.contractBagName
                  document.body.appendChild(downloadElement)
                  downloadElement.click()
                  document.body.removeChild(downloadElement)
                  window.URL.revokeObjectURL(href)
                  this.getContractMarket()

                }

              };
            }).catch(error => {
              console.log(error);
              this.msgInfo('error', error.message, true)
            })
          } else {
            // 不允许业务继续访问，弹出提示框
            this.msgInfo('warning', statusInfo.message, true);
            // this.closeDiv();
          }
        }
      } else {
        console.error('e.data 不是字符串类型或为空', e.data);
        // this.msgInfo('error', '金库返回数据格式错误', true);
      }

      // alert("returnValue1111111===" + e.data);

      /**
         *
         *在这里处理业务，执行回调函数
         */
      // console.log(returnValue);
      // if (returnValue != 'undefined' && returnValue != '') {
      //   eval(funcName);
      // }
      if (globalTargetWin != null) {
        globalTargetWin.close();
        // this.closeDiv();//关闭遮罩层
      }
    },
    // //回调测试函数
    test1 () {
      alert("test1");
    },
    // 下载
    down (params, treasuryToken = '') {
      this.downid = params.row.id
      this.downcontractBagName = params.row.contractBagName
      getDownDe(params.row.id, '', params.row.contractBagName, treasuryToken).then(res => {

        let reader = new FileReader();
        reader.readAsText(res);
        reader.onload = () => {
          try {
            let jsonData = JSON.parse(reader.result);
            console.log(jsonData);
            if (jsonData.code === 'A0314') {
              // this.receiveMsg('3#*************')
              this.showBank()
            } else {
              this.msgInfo('error', jsonData.message, true)

            }
          } catch (e) {
            console.log(e);
            if (e != 'SecurityError: An attempt was made to break through the security policy of the user agent.') {
              let blob = new Blob([res], { type: 'application/zip' })
              let downloadElement = document.createElement('a')
              let href = window.URL.createObjectURL(blob)
              downloadElement.href = href
              downloadElement.download = this.detailData.contractBagName
              document.body.appendChild(downloadElement)
              downloadElement.click()
              document.body.removeChild(downloadElement)
              window.URL.revokeObjectURL(href)
              this.getcontract()
            }
          }

        };
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
      // getDownDe('', params.row.id, params.row.contractBagName, treasuryToken).then(res => {
      //   let blob = new Blob([res], { type: 'application/zip' })
      //   let downloadElement = document.createElement('a')
      //   let href = window.URL.createObjectURL(blob)
      //   downloadElement.href = href
      //   downloadElement.download = this.detailData.contractBagName
      //   document.body.appendChild(downloadElement)
      //   downloadElement.click()
      //   document.body.removeChild(downloadElement)
      //   window.URL.revokeObjectURL(href)
      //   this.getcontract()
      // }).catch(error => {
      //   this.msgInfo('error', error.message, true)
      // })
    },
    clickCpps (value) {
      this.cppContent = value

    }
  },
  mounted () {
    if (this.$route.params.contractId) {
      this.getcontract()
    } else {
      this.$router.push({
        name: 'contract_market'
      })
    }
  },
  beforeDestroy () {
    window.removeEventListener('message', this.receiveMsg, false);

    // 确保关闭全局窗口引用
    if (globalTargetWin) {
      try {
        globalTargetWin.close();
      } catch (e) {
        console.error('关闭窗口失败', e);
      }
      globalTargetWin = null;
    }

    clearInterval(this.timer);
    this.timer = null;
  },
}
</script>
<style lang="less" scoped>
/deep/.ivu-menu,
.ivu-menu-dark,
.ivu-menu-vertical,
.ivu-menu-opened,
.ivu-menu-submenu-title {
  background: #ffffff !important;
}
/deep/.ivu-menu-submenu-title {
  background: #ffffff !important;
}
/deep/.ivu-menu-vertical.ivu-menu-light:after {
  background: #ffffff;
}

.detailModalInfo {
  margin-bottom: 2%;
}
.versionDetailone {
  margin: 2%;
  p {
    margin-bottom: 2%;
  }
}
.versionDetailtwo {
  i {
    vertical-align: -0.15em;
  }

  padding: 2%;
  // margin-top: 3%;
  // margin: 2%;
  .pending_ui {
    margin-top: 2%;
  }
}
.detailModal {
  span {
    margin-left: 3% !important;
  }
}
.pending_ui {
  display: flex;

  li:nth-child(2) {
    margin-left: 2%;
  }
  li:nth-child(3) {
    margin-left: 2%;
  }
}
// 滚动条
.textarea-style {
  width: 100%;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}

.management_middle {
  // border-bottom: 1px solid #bfbfbf;
  // padding-top: 1%;
  padding: 2%;
  // margin-bottom: 1%;
  .middle_center {
    // margin-bottom: 3%;
    margin-top: 3%;
    span {
      margin-left: 1%;
    }
  }
  .top_center {
    display: flex;
    flex-flow: row wrap;
    span {
      margin-left: 1%;
    }
    // margin-left: 3%;
    // margin-top: 1%;
    // margin-bottom: 3%;
    // .ivu-col {
    //   margin-right: 2%;
    // }
  }
}
/deep/.ivu-card {
  background: #f2f6fd;
}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
/deep/.ivu-collapse[name="mainpanel"] {
  border: none;
}
/deep/.ivu-collapse[name="mainpanel"] > .ivu-collapse-item {
  border: none;
  border-radius: 5px 5px;
  margin-bottom: 10px;
}
/deep/.ivu-scroll-container {
  height: auto;
  overflow-y: auto;
}
</style>
