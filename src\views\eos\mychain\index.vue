<template>
  <div class="chain_index">
    <keep-alive>
    <ChainTable v-if="currentTab==='chain_table'" />
  </keep-alive>
  <router-view v-if="currentTab!=='chain_table'"/>
  </div>
</template>

<script>
import ChainTable from './chain-table.vue'
export default {
  name: 'chain_index',
  components: {
    ChainTable
  },
  data () {
    return {
      // excludeArr: ['new_user', 'chain_details', 'result_page']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
