<!--
  at<PERSON><PERSON>
  新建预言机
  2021/10/21

-->
<template>
  <el-dialog
      class="dialog_sty new_consumer-dialog"
      :title="title"
      :visible.sync="Visible"
      width="580px"
      :modal="true"
      destroy-on-close
      :close-on-click-modal="false"
      :before-close="handleClose"
      @opened="open">
    <div class="dialog_content">
      <el-form :model="form" :rules="rules" ref="newConsumerForm" label-width="140px">
        <el-form-item label="预言机名称：" prop="oracleName" >
          <el-input show-word-limit maxlength="60" v-model="form.oracleName" @keyup.native="btKeyUp" placeholder="请输入" :disabled="operationState==2"></el-input>
        </el-form-item>
        <el-form-item label="预言机模板：" prop="tempId" >
          <el-select v-model="form.tempId" placeholder="请选择预言机模板" @change="changeOracleModel" :disabled="operationState==2">
            <el-option :label="item.tempName" :value="item.tempId" v-for="(item,index) in MachineTempList" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="消费者用户类型：" prop="fetchType">
          <el-select v-model="form.fetchType" @change="fetchTypeChange" placeholder="请选择消费者用户类型" :disabled="operationState==2">
            <el-option label="定时服务用户" :value="0"></el-option>
            <el-option label="请求服务用户" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="消费者用户：" prop="consumerId" >
          <el-select no-data-text="无可用消费者用户" v-model="form.consumerId"  placeholder="请选择消费者用户" :disabled="operationState==2">
            <el-option :label="item.consumerName" :value="item.consumerId" v-for="(item,index) in ConsList" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="信源类型：" prop="providerType" >
          <el-select v-model="form.providerType" @change="providerTypeChange" placeholder="请选择信源类型" :disabled="operationState==2">
            <el-option label="链" value="EOS"></el-option>
            <el-option label="http" value="HTTP"></el-option>
            <el-option label="数据库" value="DB"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="信源用户：" prop="providerId" >
          <el-select no-data-text="无可用信源用户" @change="providerIdChange" v-model="form.providerId" placeholder="请选择信源用户" :disabled="operationState==2">
            <el-option :label="item.providerName" :value="item.providerId" v-for="(item,index) in ProList" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参数：" style="padding-bottom: 68px" v-if="form.providerType=='DB'" >
          <el-input v-model="form.parameter" placeholder="请输入" type="textarea" :disabled="operationState==2"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer" v-if="operationState!=2">
    <el-button @click="Refresh();Visible = false">取 消</el-button>
    <el-button type="primary" @click="submitForm" :loading="upLoading">确 定</el-button>
  </span>
  </el-dialog>

</template>

<script>

import * as api from "../api";

export default {
  name: "newConsumerDialog",
  components: {},
  data() {
    return {
      upLoading:false,
      Visible:false,
      SmartContractVisible:false,
      form:{
        fetchType:null,
        "consumerId": "",
        "createTime": "",
        "message": "",
        "oracleEnable": null,
        "oracleId": "",
        "tempId": "",
        "oracleName": "",
        "oracleStatus": null,
        "parameter": "",
        "providerType": "",
        "providerId": "",
        dataId:""
      },
      title:null,
      operationState:0, //0 1 2
      rules: {
        fetchType: [
          {required: true, message: '请输入', trigger: 'change'},
        ],
        oracleName: [
          {required: true, message: '请输入', trigger: 'change'},
          {pattern: /[^:/\\?*"<>|;]/g, message: '不能输入 :/\\?*"<>|;'}
        ],
        tempId: [
          {required: true, message: '请输入', trigger: 'change'},
        ],
        consumerId: [
          {required: true, message: '请输入', trigger: 'change'},
        ],
        providerType: [
          {required: true, message: '请输入', trigger: 'change'},
        ],
        providerId: [
          {required: true, message: '请输入', trigger: 'change'},
        ],

      },
      MachineTempList:[],
      ConsList:[],
      ProList:[]
    }
  },
  watch: {
  },
  mounted() {
  },
  methods: {
    btKeyUp(e) {
      e.target.value = e.target.value.replace(/[:/\\?*"<>/|;]/g,"");
    },
    providerIdChange(id){
      this.ProList.map(ele=>{
        if(ele.providerId==id){
          this.form.dataId = ele.dataId
        }
      })
    },
    fetchTypeChange(){
      this.form.providerType = null
      this.form.consumerId = null
      this.form.providerId =null
      this.form.consumerName = null
      this.form.dataId = null
      this.getConsList()
      this.getProList()
    },
    providerTypeChange(){
      this.getProList()
      this.form={...this.form,
        "providerId": "",
        parameter:null
      }
    },
    changeOracleModel(){
      this.ConsList=[]
      this.ProList=[]
      this.form.consumerId = ''
      this.form.fetchType = null
      this.getProList()
      this.form={...this.form,
        "consumerId": "",
        "createTime": "",
        "message": "",
        "oracleEnable": null,
        "oracleStatus": null,
        "parameter": "",
        "providerType": "",
        "providerId": "",
        dataId:""
      }
      if(this.form.tempId&&this.form.fetchType){
        this.form.consumerId = null
        this.getConsList()
      }
    },
    open(){
      // this.operationState==0?this.Refresh():null
      if(this.operationState==0){
        this.Refresh()
        this.title='新建预言机'
      }else if(this.operationState==1){
        this.title='编辑预言机'
        this.getOracleMachineDetails()
      }else {
        this.title='查看预言机'
        this.getOracleMachineDetails()
      }
      this.getMachineTempList()
      setTimeout(()=>{

      },1500)
    },
    //获取数据用户列表
    getMachineTempList(){
      api.getMachineTempList(
          {
            "filter": {},
            "order": "",
            "page": 0,
            "rows": 0,
            "sort": "",
            "tempName": ""
          }
      ).then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.MachineTempList=res.result.rows
      })
    },
    //获取消费者用户列表
    getConsList(val=false){
      if(this.form.tempId&&this.form.fetchType||this.form.fetchType==0){
        api.getConsList(
            {
              tempId:this.form.tempId,
              fetchType:this.form.fetchType
            }
        ).then(res=>{
          if(res.code!=0) return this.$message.warning(res.msg)
          this.ConsList = []
          this.ConsList=res.result
          if(this.operationState!=0&&val){
            this.ConsList.push({consumerId:this.form.consumerId,consumerName:this.form.consumerName})
          }

        })
      }
    },
    //获取信源用户列表
    getProList(val=false){
      if(this.form.providerType&&this.form.tempId&&this.form.fetchType||this.form.fetchType==0){
        api.getProList(
            {
              providerType:this.form.providerType,
              tempId:this.form.tempId,
              fetchType:this.form.fetchType
            }
        ).then(res=>{
          if(res.code!=0) return this.$message.warning(res.msg)
          this.ProList=res.result
          if(val){
            this.ProList.push({providerId:this.form.providerId,providerName:this.form.providerName})
          }
        })
      }

    },
    getOracleMachineDetails(){
      api.getOracleMachineDetails({oracleId:this.form.oracleId}).then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.form = res.result
        this.getConsList(true)
        this.getProList(true)
      })
    },
    Refresh(){
      this.MachineTempList=[]
          this.ConsList=[]
          this.ProList=[]
      this.form={
        fetchType:null,
        consumerId:null,
        "consumerName": "",
        "createTime": "",
        "message": "",
        "oracleEnable": null,
        "oracleId": "",
        "tempId": "",
        "oracleName": "",
        "oracleStatus": null,
        "parameter": "",
        "providerType": "",
        "providerId": "",
        dataId:""
      }
    },
    submitForm() {
      this.$refs['newConsumerForm'].validate((valid) => {
        if (valid) {
          if(this.operationState==0){
            this.addOracleMachine()
          }else if(this.operationState==1){
            this.updateOracleMachine()
          }

        } else {
          return false;
        }
      });
    },
    addOracleMachine(){
      if(this.upLoading) return
      this.upLoading = true
      api.addOracleMachine(this.form)
          .then(res=>{
            this.upLoading = false
            if(res.code!=0) return this.$message.warning(res.msg)
        this.$message.success(res.msg)
        this.Visible=false
        this.$emit('Refresh')
      }).catch(err=>{
        this.upLoading = false
      })
    },
    updateOracleMachine(){
      if(this.upLoading) return
      this.upLoading = true
      api.updateOracleMachine(this.form)
          .then(res=>{
            this.upLoading = false
            if(res.code!=0) return this.$message.warning(res.msg)
        this.$message.success(res.msg)
        this.Visible=false
        this.$emit('Refresh')
      }).catch(err=>{
        this.upLoading = false
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleClose(done) {

      done();

    }
  },
  created() {

  }
}
</script>

<style lang="less" scoped>
.new_consumer-dialog{

}
</style>
