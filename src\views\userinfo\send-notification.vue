<!--
    发送通知弹框
-->

<template>
  <Modal v-model='visible' title='发送通知' :mask-closable="false" width="600">
    <Form
      ref='formValidate'
      :model='formValidate'
      :rules='ruleValidate'
      :label-width='100'
      hide-required-mark
    >
      <FormItem label='接收人：' prop='receiverIds'>
        <treeselect
          v-model='value'
          :multiple='true'
          :options='tenantList'
          placeholder='请选择接收人'
          @close="closeSelect"
          noChildrenText='无可选项'
          noResultsText='无匹配项'
          noOptionsText='无可选项'
        />
        <span v-if="showRec" style="color:red">接收人不能为空</span>
      </FormItem>
      <FormItem label='通知内容：' prop='noticeMessage'>
        <Input
          maxlength='200'
          show-word-limit
          v-model='formValidate.noticeMessage'
          type='textarea'
          :autosize='{ minRows: 3, maxRows: 7 }'
          placeholder='请填写通知内容'
        ></Input>
      </FormItem>
      <FormItem label='通知类型：'> <span style="color: #999;">待阅</span> </FormItem>
      <FormItem label='发送时间：'> <span style="color: #999;">立即发送</span> </FormItem>
    </Form>
    <div slot='footer'>
      <Button @click="handleReset('formValidate')">取消</Button>
      <Button
        @click="handleSubmit('formValidate')"
        type='primary'
        :loading='btnLoading'
        >发送</Button
      >
    </div>
    <Modal v-model='visibleResult' title='发送通知' :closable="false" :mask-closable="false">
      <div class='result-wrap'>
        <Icon v-if='isSuccess' type='ios-checkmark-circle' size="60"/>
        <Icon v-else type='ios-close-circle' class='red' size="60"/>
        <div v-if='isSuccess' class='success'>发送成功</div>
        <div v-else class='error'>
          <div class="success" style="text-align:center">发送失败</div>
          <div>由于{{ errorReason }}发送失败，请重试。</div>
        </div>
      </div>
      <div slot='footer'>
        <Button
          v-if='!isSuccess'
          @click='visibleResult = false;visible = false;'
          >取消</Button
        >
        <Button
          v-if='!isSuccess'
          @click="visibleResult = false"
          type='primary'
          :loading='btnLoading'
          >重新发送</Button
        >
        <Button
          v-else
          @click='clickComplete'
          type='primary'
          >完 成</Button
        >
      </div>
    </Modal>
  </Modal>
</template>
<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { tenantMembers, newMsg } from '@/api/data'

export default {
  components: {
    Treeselect
  },
  props: {
    tas: {
      type: Number
    },
    roleId: {
      type: String
    }
  },
  data () {
    return {
      noticeType: 'WAIT_READ',
      btnLoading: false,
      tenantList: [], // 租户列表
      flag: 'tenant',
      arr: [],
      isSuccess: false, // 是否提交成功
      errorReason: '', // 失败原因
      visibleResult: false, // 提交结果弹框
      showRec: false,
      value: [],
      visible: false,
      formValidate: {
        noticeMessage: ''
      },
      ruleValidate: {
        noticeMessage: [
          {
            required: true,
            message: '请填写通知内容',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    clickComplete () {
      this.visibleResult = false
      this.visible = false
      if (this.tas === 3) {
        this.$emit('getNoticeManageData', 'WAIT_READ', 0, '', 'CUSTOM')
      }
    },
    closeSelect () {
      if (this.value.length === 0) {
        this.showRec = true
      } else {
        this.showRec = false
      }
    },
    /**
     * @点击提交按钮
     */
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    handleSubmit (name) {
      // let that = this
      this.$refs[name].validate((valid) => {
        if (this.value.length === 0) {
          this.msgInfo('warning', '存在字段为空，请检查！', true)
          this.showRec = true
          return false
        }
        if (valid) {
          let arr = []
          let tenantList = this.tenantList
          let parentArr = []
          let parentChildrenArr = []
          tenantList.forEach((tenant) => {
            parentArr.push(tenant.id)
            parentChildrenArr.push(tenant.children || [])
          })
          this.value.forEach((item) => {
            if (parentArr.indexOf(item) >= 0) {
              parentChildrenArr[parentArr.indexOf(item)].forEach((user) => {
                arr.push(user.id)
              })
            } else {
              arr.push(item)
            }
          })
          this.handleSubmitData(arr)
        }
      })
    },
    /**
     * @提交
     * receiverIds 接收人数组
     */
    handleSubmitData (receiverIds) {
      this.btnLoading = true
      newMsg(
        receiverIds,
        this.formValidate.noticeMessage,
        this.noticeType
      ).then((res) => {
        if (res.code === '00000') {
          this.isSuccess = true // 提交成功
          this.btnLoading = false // 按钮转圈停止
          this.visibleResult = true // 开启提交结果弹框
          this.errorReason = ''// 失败原因
        } else {
          this.isSuccess = false // 提交失败
          this.btnLoading = false // 按钮转圈停止
          this.visibleResult = true // 开启提交结果弹框
          this.errorReason = res.message // 失败原因
        }
      }).catch((err) => {
        this.isSuccess = false // 提交失败
        this.btnLoading = false // 按钮转圈停止
        this.visibleResult = true // 开启提交结果弹框
        this.errorReason = err.message || ''// 失败原因
      })
    },
    /**
     * @取消
     */
    handleReset (name) {
      this.$nextTick(() => {
        this.$refs[name].resetFields()
      })
      this.visible = false
    },
    init () {
      this.$nextTick(() => {
        this.$refs['formValidate'].resetFields()
      })
      this.value = []
      this.showRec = false
    },
    /**
     * @获取接收人列表
     */
    getTenantMembersList () {
      tenantMembers().then((res) => {
        if (res.code !== '00000') this.msgInfo('warning', res.message, true)
        else {
          this.tenantList = res.data.map((item) => {
            let childrenArr =
              item.userList &&
              item.userList.map((chil) => {
                return {
                  id: chil.userId,
                  label: chil.userLoginId
                }
              })
            return {
              id: item.tenantId + this.flag,
              label: item.tenantName,
              isDisabled: item.userList.length === 0,
              children: childrenArr || []
            }
          })
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  created () {
    if (this.roleId === '1' || this.roleId === '2') {
      this.getTenantMembersList()
    }
  },
  watch: {
  }
}
</script>

<style lang='less' scoped>
.result-wrap {
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  i {
    font-size: 100px;
    color: #52c41a;
    &.red {
      color: #ff1a2e;
    }
  }
  div {
    padding: 5px;
    &.success {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
    &.error {
      font-size: 12px;
      color: #999;
    }
  }
}
</style>
