<template>
  <div class="tinymce-editor" style="z-index: 999999">
    <editor v-model="myValue" :init="init" :disabled="disabled" @onClick="onClick" @onBlur="onBlur" :key="tinymceFlag" style="z-index: 999999">
    </editor>
  </div>
</template>
<script>
// import tinymce from 'tinymce/tinymce'
// import Editor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver'
import 'tinymce/icons/default'
// 编辑器插件plugins
import 'tinymce/plugins/table'// 插入表格插件
import 'tinymce/plugins/lists'// 列表插件
import 'tinymce/plugins/wordcount'// 字数统计插件
import 'tinymce/plugins/link'
// import 'tinymce/plugins/autoresize'
import 'tinymce/plugins/hr'
import 'tinymce/plugins/textpattern'
export default {
  components: {
    Editor
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    // 基本路径，默认为空根目录，如果你的项目发布后的地址为目录形式，
    // 即abc.com/tinymce，baseUrl需要配置成tinymce，不然发布后资源会找不到
    baseUrl: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    plugins: {
      type: [String, Array],
      default: 'table lists wordcount link textpattern hr'
    },
    toolbar: {
      type: Array,
      default () {
        return ['undo redo fontsizeselect fontselect bold italic underline strikethrough alignleft aligncenter alignright alignjustify  removeformat', 'lists bullist numlist outdent indent link styleselect table hr forecolor backcolor']
      }
    }
  },
  data () {
    return {
      tinymceFlag: 1,
      init: {
        language_url: `${this.baseUrl}/tinymce/langs/zh_CN.js`,
        language: 'zh_CN',
        skin_url: `${this.baseUrl}/tinymce/skins/ui/oxide`,
        content_css: `${this.baseUrl}/tinymce/skins/content/default/content.css`,
        // skin_url: `${this.baseUrl}/tinymce/skins/ui/oxide-dark`, // 暗色系
        // content_css: `${this.baseUrl}/tinymce/skins/content/dark/content.css`, // 暗色系
        height: 300,
        plugins: this.plugins,
        toolbar: this.toolbar,
        branding: false,
        menubar: false,
        selector: 'textarea', // change this value according to your HTML
        autoresize_on_init: true,
        content_style: 'p {margin: 0px; border:0px ;}',
        fontsize_formats: '8px 10px 12px 14px 18px 24px 36px'
        // 此处为图片上传处理函数，这个直接用了base64的图片形式上传图片，
        // images_upload_handler: (blobInfo, success, failure) => {
        //   const img = 'data:image/jpeg;base64,' + blobInfo.base64()
        //   success(img)
        // }
      },
      myValue: this.value
    }
  },
  mounted () {
    this.tinymceFlag++
    // tinymce.init({})
  },
  methods: {
    onClick (e) {
      this.$emit('onClick', e, tinymce)
    },
    // 可以添加一些自己的自定义事件，如清空内容
    clear () {
      this.myValue = ''
    },
    onBlur (e) {
      this.$emit('onBlur', e, tinymce)
    }
  },
  watch: {
    value (newValue) {
      this.myValue = newValue
    },
    myValue (newValue) {
      this.$emit('input', newValue)
    }
  },
  beforeDestroy () {
    // 销毁组件前销毁编辑器
    this.tinymce && this.tinymce.destroy()
  },
  activated () {
    // 每次都给编辑器改变不同的key值使其每次切换页面都重新加载
    this.tinymceFlag++
  },
  deactivated () {
    this.tinymce.destroy()
  }
}
</script>
<style lang="less" scoped>
</style>
