@import "./variables.less";
//比例
@ratio:1;
//字体
@font-family:<PERSON>;
//字体颜色
/*查看字体颜色*/
@view-color:#999;
@table-th-color:#031129;
@table-td-color:#333;
@inputTip-color:#bbb;
// 弹窗样式
@default-width:@ratio*600px;//弹框默认宽度
@max-width:@ratio*750px;//弹框最大宽度
@min-width:@ratio*400px;//弹框最小宽度
@min-height:@ratio*232px;//弹框最小高度
@modal-color:#333;//字体颜色
@modal-placeholder:#bbb;

@border1px:1.5PX;
/*滚动条样式重置*/
/*滚动条样式*/

　
//
//　　.innerbox::-webkit-scrollbar-thumb{/*滚动条里面小方块*/
//
//  　　border-radius:5px;
//
//  　　-webkit-box-shadow:inset005pxrgba(0,0,0,0.2);
//
//  　　background:rgba(0,0,0,0.2);
//
//  　　}
//
//　　.innerbox::-webkit-scrollbar-track{/*滚动条里面轨道*/
//
//  　　-webkit-box-shadow:inset005pxrgba(0,0,0,0.2);
//
//  　　border-radius:0;
//
//  　　background:rgba(0,0,0,0.1);
//
//  　　}

::-webkit-scrollbar {         // 纵向滚动条和横向滚动条宽度
  width: 10px;
  height: 10px;
}
::-webkit-scrollbar-thumb {         // 滚动条背景条样式
  background: rgba(216,216,216,0.60);
  border-radius: 6px;
}
::-webkit-scrollbar-track {            // 滚动条按钮样式

}
/deep/
.el-table__body-wrapper::-webkit-scrollbar {         // 纵向滚动条和横向滚动条宽度
  width: 10px;
  height: 10px;
}
/deep/
.el-table__body-wrapper::-webkit-scrollbar-thumb {         // 滚动条背景条样式
  background: rgba(216,216,216,0.60);
  border-radius: 6px;
}
/*取消 确定按钮 基本样式*/
.modal-btn-primary(@width:78px,@height:46px){
  min-width: @width;
  height: @height;
  border-radius: 2px;
  cursor:pointer;
  font-size:@font2;
}
.cancel-btn{
  .modal-btn-primary();
  color: #555555;
  background: #FFFFFF;
  border: 1Px solid #D9D9D9;
}
.btn-after {
  box-shadow: none;
  &:hover {
    &::after{
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-radius: 3px;
      background-color: rgba(255,255,255,0.1);
    }
  }
  &:active {
    &::after{
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      border-radius: 3px;
      background-color: rgba(0,0,0,0.1);
    }
  }
}
.sure-btn{
  //margin-left:8px;
  background: #3D73EF;
  position: relative;
  .btn-after()
}
.iview-delete-btn {
  margin-left:8px;
  background: #FA5151;
}
.border-btn {
  margin-left:8px;
  border:1px solid#3D73EF;
  background-color: #fff;
  color: #3D73EF;
  position: relative;
  &:hover,&:active {
    color: #fff;
    background-color:#3D73EF;
  }
  .btn-after()

}
/*重置button间距*/
//.el-button--medium{padding:0}  必须注释 要不其他按钮会被影响
// .cancel-btn{@include cancel-btn}
// .sure-btn{@include sure-btn}
// .border-btn{@include border-btn}
// .iview-delete-btn{@include iview-delete-btn}
/*diabled input style*/
/deep/ .fbIpt .el-input.is-disabled .el-input__inner {
  border: 0;
  background-color: transparent;
  color: #333;
  cursor: default;
  padding: 0 0;
  font-size: @font2;
  font-weight: normal;
}
/*重置checkbox 样式*/
/deep/
.el-checkbox__inner{
  width: 12px;
  height: 12px;
}
/deep/
.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
  background-color: @primary !important;
  border-color: @primary !important;
}
/deep/
.el-checkbox__input.is-indeterminate span.el-checkbox__inner{
  background-color: #fff !important;
  border-color: @primary !important;
}
/deep/
.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  content: '';
  position: absolute;
  display: block;
  background-color: @primary;
  height: 7px;
  -webkit-transform: scale(1);
  transform: scale(1);
  left: 2px;
  right: 0;
  top: 2px;
  width: 7px;
  transform: translate(-0.3Px,-0.5Px);
}
/deep/
.el-checkbox__input .el-checkbox__inner::after{
  left:3px;
  top:0;
}
  /*重置select 颜色*/
/deep/
.el-select .el-input.is-focus .el-input__inner{
  border-color:@primary;
}
/deep/
.el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
  background-color: #F5F8FF;
}
/*弹框遮罩位置*/
.alertBox {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index:1002;
}
/*弹框相对位置*/
.alertBox .addTissue {
  &.max-width{
    width: @max-width;
  }
  position: absolute;
  width: @default-width;
  max-width:@max-width;
  min-width:@min-width;
  // max-height:89%;
  //min-height:@min-height;

  background: #fff;
  // margin:131px auto;
  border-radius: 4px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
/*弹框标题*/
.el-icon-cursor{
  cursor:pointer;
}
.alertTop {
  .tit {
    font-size: @font1;
    //font-weight: bold;
    color:@color;
    .el-icon-close{
      line-height:@ratio*50px;
      font-size: @ratio*18px;
    }
  }
  padding:0 @ratio*20px;
  height: @ratio*50px;
  line-height:@ratio*50px;
  border-bottom:@border;
}
/*弹框主体内容*/
/deep/
.el-table td, .el-table th{ //表格checkbox 居中
  text-align:center!important;
  padding:4px 0;
}
.alert_box{
  max-height: calc(89vh - 144px);
  overflow:hidden;
  overflow-y:auto;
  /deep/ .el-form-item__label {
    width:140px;
    //margin-top: 4px;
    line-height:@ratio*32px;
    font-size: @font2;
    color: #333;
    font-weight: normal;
    padding-right:@ratio*20px;
    //overflow:hidden;
    //text-overflow:ellipsis;
    //white-space:nowrap;
    //font-family: Microsoft YaHei;
    // padding: 0px 20px 0px 0px;
  }
  .flex-align-item.view{
    margin-bottom:@ratio*25px!important;
    /deep/
    .tit{
      width:140px;
      text-align:right;
      padding-right:@ratio*20px;
      font-size: @font2;
      color:@view-color;
    }
    .titValue{
      font-size: @font2;
      color:@modal-color;
    }
  }
  /deep/
  .el-form-item__content{
    //width:429px;/
    line-height:32px;
    margin-left:140px;
    //&>div{
    //  width:100%;
    //}
  }
  /deep/ .el-form-item {
    margin-bottom: 0;
  }
  &.fbIpt .evertModule{
    //margin-bottom:14px!important;//查看弹框间距减半
  }
  .selectBox {  // 弹框内边距
    padding: 16px 16px 25px;
    color: @modal-color;
    font-size:@font2;
    .evertModule {//每行表单
      //width:80%;
      //display:flex;
      align-items: center;
      margin-bottom:30px;
      //font-size: 12px;
      color: @modal-color;
      .el-form-item{
        width:100%;
      }
      .flex_sy {
        margin-left: 20px;
        flex: 1;
      }
    }
    >.evertModule:last-child{
      margin-bottom:0
    }
    /deep/
    .el-form-item__content{
      //width:429px;
      line-height:32px;
      width:@ratio*300px;;
    }
    .el-table{
      //width:429px;
    }
      //重置 input select 宽高
     /deep/  .el-input__inner {
      height: @ratio*32px;
       //width:429px;
       line-height:@ratio*32px;
      font-size: @font2;
      color: @modal-color;
      font-weight: normal;
    }
    /deep/
    .el-input--medium .el-textarea__inner{
      font-size: @font2;
    }
     /deep/ .el-select {
      width: 100%;
      height: @ratio*32px;
    }

    //校验信息提示
    /deep/ .el-form-item__error {
      color: #fc4f1d !important;
      font-size:@font2;
    }
    //重置upload 样式
    /deep/
    .upload-demo{
      width:100%;
      &.isred {
        .el-upload {
          .el-upload-dragger {
            border: 1Px dashed #F56C6C !important;
          }
        }
      }
      .el-upload{
        width:100%;
        .el-upload-dragger{
          width:100%;
          border: 1Px dashed #d9d9d9;
          &:hover {
            border: 1Px dashed #409EFF;
          }
          .el-icon-upload{
            &::before{
              content:''
            }
            width: @ratio*39px;
            height: @ratio*32px;
            margin:30px 0 0;
            //background: url("../../assets/image/upload.png") no-repeat;
            background-size:100%;
          }
          .uploadText{
            color:@primary;
          }
        }
      }
      .el-upload-list__item {
        margin-top: 0px !important;
      }
      .el-upload-list__item:hover{
        background-color: #F5F8FF;
      }
      .el-upload-list__item .el-icon-close {
        color: #337DFF;
      }
      .uploadImg {
        margin-top: 20px;
      }
    }



    /deep/ .el-upload__text {
      padding-top:opx;
      color:@modal-placeholder
    }
  }
}
/*弹框底部*/
.btn-row {
  //height:59px;
  padding:12px 18px;
  text-align:right;
  border-top:@border;
}

@confirm-max-width:@ratio*500px;
@confirm-default-width:@ratio*365px;
@confirm-default-maxWidth:@ratio*500px;

/*确认弹框*/

.confirmBox{
  //标题
  .noUn {
    // font-size: 20px;
    font-size:@font1;
    color: #555;
    font-weight: bolder;
    vertical-align: middle;
  }
  .title {
    font-size:@font1;
    color: #555;
    vertical-align: middle
  }
  .iconImage {
    // width: 28px;
    // height: 28px;
    width: 16px;
    height: 16px;
    margin-right: 14px;
    vertical-align: middle;
  }
  //
  .errmessage {
    margin-left:30px;
    margin-top: 14px;
    // font-size: 17px;
    font-size: @font2;
    color: #555555;
    letter-spacing: 0;
    line-height:25px;
  }
  .errmessage.next {
    margin-top: 8px;
  }
  /*重置icon样式*/
  .el-icon-warning{
    color: #FE983D;
    margin-right: 14px;
    // font-size: 28px;
    font-size: 16px;
  }
  .el-icon-info {
    color: #337DFF;
    margin-right: 14px;
    // font-size: 28px;
    font-size: 16px;
  }
  .el-icon-success {
    color: #10C038;
    margin-right: 14px;
    // font-size: 28px;
    font-size: 16px;
  }
  .el-icon-error {
    color: #F04134;
    margin-right: 14px;
    // font-size: 28px;
    font-size: 16px;
  }
  .addTissue{
    padding:43px 43px 29px;
    width:auto;
    min-width:@confirm-default-width;
    max-width:@confirm-default-maxWidth ;
    .delText{
      margin-bottom:24px;
      font-size:@font2;
      color:@modal-color;
    }
    .confirmBottom{
     text-align:right;
    }
  }
  //提示是一行的
  .notitle {
    display: flex;
    align-items: center;
    .errmessage {
      margin: 0;
    }
  }
}
/*重置el-table*/
/deep/ .el-table th{
  background: #FBFBFD !important;
  padding: 0 !important;
}
/deep/
.el-table th, .el-table tr{
  //line-height:40px ;
}
.el-textarea /deep/.el-textarea__inner {
  word-break: keep-all !important;
}
/deep/.el-table .el-table__empty-text {
  font-size: @font2;
  width: 100% !important;
  text-align: left;
  padding-left:@ratio*45px;
}
/deep/ .el-table th>.cell{
  color:@table-th-color !important;
  font-size: @font2 !important;
  font-weight: normal !important;
  text-align: left!important;
  border-right: @border1px solid #fff;
  padding-top: 2px;
  padding-bottom:2px;
}
/deep/ .el-table th:first-child > .cell {
  padding-left: 14px !important;
}
/deep/ .el-table th:nth-last-child(2) > .cell {
  border-right: none;
}

/deep/
.el-table td div {
  text-align: left;
  font-size:@font2;
  color:@table-td-color;
}

/deep/ .el-table__body tr:hover > td{
  background-color: #F5F8FF  !important;
}
/deep/ .el-table .cell{
  overflow: unset;
}

/deep/ .el-table th>.cell{
  // padding-left:15px !important;
  text-align:center;
}
.uploadSvg.svg-icon{
  width: 56px;
  //height: 46px;
  margin-top:31px;
}
/*用户输入提示*/
.userTip{
  position: absolute;
  left: 0;
  top: 100%;
  color:@inputTip-color;
  font-size:@font2;
  // line-height:20PX;
  line-height: 26px;
  white-space: nowrap;
}
.userTipSpecial{
  color:@inputTip-color;
  font-size:@font2;
}
.tip-size{
  font-size:@font2;
}
.form {
  width: 100%;
  margin-top:0!important;
  padding:16px;
  .el-form {
    padding: 0 30px!important;
    font-size: @font2;
    .el-form-item {
      margin-bottom: 0;
    }
    .el-input /deep/.el-input__inner {
      padding: 24px 0 24px 25px;
      border-left: none;
      font-size: @font2;
    }
    .el-textarea /deep/.el-textarea__inner {
      padding-top: 10px;
      padding-left: @font2;
    }
    .el-form-item /deep/.el-form-item__label {
      /*text-align: left;*/
      /*font-size: 17px;*/
      /*padding-top: 6px;*/
      /*font-weight: 400;*/
      /*color: #333333;*/
    }
    .el-input,
    .el-textarea {
      width: 420px;
    }
    .el-table {
      width: 420px;
      font-size: @font2;
      border: 2px solid #e7ecef;
    }
    .el-table::before {
      height: 0px !important;
    }
    .el-table /deep/ td {
      border-bottom: 0px !important;
    }
    .el-table /deep/ .el-table-column--selection {
      text-align: left !important;
    }
    .el-table /deep/ tr:hover > td {
      background-color: #f0ffff !important;
    }
    .el-table /deep/ th.gutter {
      background: #f2f7fa !important;
    }
    .el-table /deep/ .warning-row {
      background-color: #f0ffff !important;
    }
    //#F04134
    .el-form-item.is-error /deep/ .el-form-item__error {
      color: #fc4f1d !important;
      font-size: @font2;
    }
    /deep/ .el-checkbox__input.is-checked .el-checkbox__inner {
      border-color: #46b8c9;
      background: #46b8c9;
    }
    /deep/ .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      border-color: #46b8c9;
      background: #46b8c9;
    }
    /deep/ .el-form-item.is-required .el-form-item__label:before,
    /deep/ .el-icon-upload:before {
      content: "";
    }
  }
  .evertModule {
    display: flex;
    align-items: center;
    margin-bottom: 40px;
    font-size: @font2;
    font-weight: normal;
    color: #333333;
  }
  .time {
    position: relative;
    text-align: center;
    margin-bottom:0!important;
    height:72px;
    /deep/.el-input {
      height: 50px;
    }
    .subtraction {
      position: absolute;
      top: 0;
      left: @ratio*20px;
      width: @ratio*30px;
      height: @ratio*32px;
      border: @ratio*1Px solid #d9d9d9;
      color: #333;
      font-size:@font2;
      z-index: 2;
      cursor: pointer;
      line-height: @ratio*28px;
      border-top-left-radius:  @ratio*2px;
      border-bottom-left-radius:  @ratio*2px;

    }
    .addition {
      position: absolute;
      top: 0;
      right: @ratio*130px;
      width: @ratio*30px;
      height: @ratio*32px;
      border: @ratio*1Px solid #d9d9d9;
      color: #333;
      font-size: @font2;
      z-index: 2;
      cursor: pointer;
      line-height: @ratio*28px;
      border-top-right-radius: @ratio*2px;
      border-bottom-right-radius: @ratio*2px;
    }
    .error {
      position: absolute;
      left: @ratio*140px;
      top: @ratio*46px;
      // bottom: -40px;
      // font-size:17px;
      font-size:@font2;
      color: #999999;
    }
    .el-input {
      box-sizing: border-box;
      width: @ratio*220px;
      text-align: center;
      height: @ratio*30px;
      margin-right: @ratio*10px;
      padding-left: @ratio*30px;
      padding-right: @ratio*30px;
      width: @ratio*155px !important;
      /deep/ .el-input__inner{
        padding: 0 !important;
        height: @ratio*32px;
        text-align: center;
        border: @ratio*1Px solid #d9d9d9;
        border-left: none;
        border-right: none;
        border-top-color: #d9d9d9;
      }
    }
    /deep/ .el-form-item__error {
      top: 106%;
      font-size: @font2;
      color: #fc4f1d !important;
    }

    .text {
      // font-size: 17px;
      font-size:@font2;
      color: #333333;
    }
  }

}
//loading
.BoxLoading {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10000;
}
.BoxLoading /deep/ .el-loading-mask {
  background: rgba(0, 0, 0, 0.4) !important;
}
.BoxLoading /deep/ .el-loading-spinner .circular {
  width: 60px !important;
  height: 60px !important;
}
.BoxLoading /deep/ .el-loading-spinner .path {
  stroke: #fff !important;
}
.BoxLoading /deep/ .el-loading-spinner .el-loading-text {
  color: #fff !important;
  font-size: @font2;
  font-weight: bold;
}
//返回框
.back {
  display: inline-block;
  border: 1px solid #D9D9D9;
  border-radius:3px ;
  text-align: right;
  cursor: pointer;
  color: #555;
  font-size: 14px;
  position: relative;
  margin-top: 20px;
  padding:4px 10px 4px 18px;
  margin-right: 5px;
  &::before{
    content: "";
    position: absolute;
    width: 8px;
    height: 1Px;
    background: #555;
    left:5px;
    top: 11px;
    -webkit-transform: rotate(
    -43deg
    );
        -ms-transform: rotate(-43deg);
        transform: rotate(
    -43deg
    );
  }
  &::after{
    content: "";
    position: absolute;
    width: 8px;
    height: 1Px;
    background: #555;
    left: 5px;
    top: 16px;
    -webkit-transform: rotate(
    46deg
    );
        -ms-transform: rotate(46deg);
        transform: rotate(
    46deg
    );
  }
  &:hover {
    color: #3D73EF;
    background-color: #fff;
    border-color: #3D73EF
  }
  
}
.back:hover {
  &::after,&::before {
    background-color: #57a3f3;
  }
}
.statuspopover {
  display: block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

