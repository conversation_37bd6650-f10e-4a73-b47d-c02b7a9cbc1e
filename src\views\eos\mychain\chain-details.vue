<template>
  <div class="chain">
    <!-- <div v-if="bg" style="background-color: #ccc;width: 100%;position:absolute;height: 100%;opacity: 0.5;z-index: 1;">
    </div> -->
    <Collapse v-model="panelValue" simple name="mainpanel">
      <Panel name="1" style="background:#ECEFFC;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        基础信息
        <p slot="content" class="basetext">
          <span>名称：{{ arrDetails.chainAccountName }}</span>
          <span>类型：{{ arrDetails.accountType }}</span>
          <span>链账户归属公司：{{ arrDetails.accountCompanyName }}</span>
          <span>创建时间：{{ arrDetails.createTime }}</span>
          <span>更新时间：{{ arrDetails.updateTime }}</span>
        </p>
        <p slot="content" class="basetext">
          <span>描述：{{ arrDetails.description }}</span>
        </p>
        <p slot="content" class="basetext" :style="this.statusdetails==='上链失败'||this.statusdetails==='上链成功'?'display:none':'display:block'">
          <span>状态：{{ arrDetails.status }}</span>
        </p>
        <!-- <p slot="content" class="basetext">

        </p> -->
        <div v-if="this.infodetail!==null" slot="content">

          <div slot="content" class="basetext" v-if="this.statusdetails==='上链失败'">
            <p v-if="infodetail.code===null">
              <span>状态：{{ arrDetails.status }}</span>
              <span style="line-height:30px">失败原因：{{infodetail.errorInfo}}</span>
            </p>
            <p v-else>
              <span>状态：{{ arrDetails.status }}</span>
              <span style="line-height:30px">编码信息：{{infodetail.code}}</span>
              <span style="line-height:30px">失败原因：{{infodetail.what}}</span>
              <span style="line-height:30px">方法：{{infodetail.name}}</span>
            </p>

          </div>
          <div slot="content" class="basetext" v-else-if="this.statusdetails==='上链成功'">
            <!-- <span style="line-height:30px" v-if="infodetail.transactionId===null">交易ID：</span> -->
            <span>状态：{{ arrDetails.status }}</span>
            <span style="line-height:30px;">交易ID：{{infodetail.transactionId}}</span>
          </div>
        </div>

        <p slot="content" class="basetext" v-if="arrDetails.accountType==='普通链账户'" style="display:flex">
          <span>是否使用内存：{{arrDetails.useMemory==='y'?'是':'否'}}</span>
          <span v-if="arrDetails.useMemory==='y'">使用内存量：{{arrDetails.useMemoryAmount}}&nbsp;Byte</span>
          <span style="display: block;width: 50%;word-break:break-all;white-space: pre-wrap;">理由：{{arrDetails.reason}}</span>
        </p>
      </Panel>
      <Panel name="2" style="background:#ECEFFC;" v-if="arrDetails.accountType==='普通链账户'">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        资源信息
        <p slot="content" class="basetext">
        <Table stripe :columns="columnsResource" :data="dataResource">
          <template slot-scope="{ row, index }" slot="action">
            <Button type="text" size="small" :disabled="hasEditPermission" :style="buttonStyle" @click="resourcemodal(row)">资源申请</Button>
          </template>
        </Table>
        </p>
      </Panel>
      <Panel name="3" style="background:#ECEFFC; display:block" v-if="arrDetails.accountTypeKey === 'NORMAL'" v-show="isshow.chainpower">
        <div>
          <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
          账户权限
        </div>

        <div slot="content" class="basetext">
          <div style="margin-bottom:10px">注：owner和active权限不可用，需新建一个默认权限</div>
          <Table stripe :columns="columnsPermission" :data="arrDetails.accountPermissionVOs">

            <template slot-scope="{ row, index }" slot="action">
              <Button v-if="role==1" type="text" :disabled="hasEditPermission" size="small" :style="buttonStyle" @click="privatemodal(row)">私钥下载</Button>
              <Tooltip v-else content="请在移动办公群联系平台管理员获取私钥" placement="top" :transfer='true' max-width='300px'>
                <Button type="text" size="small" :disabled="true" style="marginRight: 8px; color:#c5c8ce;border:1px solid #c5c8ce" @click="privatemodal(row)">私钥下载</Button>
              </Tooltip>

              <Button type="text" size="small" :disabled="(row.status === '上链失败' || row.status === '待上链')?false:true" :style="(row.status === '上链失败' || row.status === '待上链') ? 'color:#3D73EF;border:1px solid #3D73EF' : 'color:#c5c8ce;border:1px solid #c5c8ce'" @click="deployment(row)">重新上链</Button>
              <!-- <Button v-if="row.status === '上链失败' || row.status === '待上链'" type="text" size="small" style="color:#3D73EF;border:1px solid #3D73EF" @click="deployment(row)">重新上链</Button>
            <Tooltip v-else content="请在移动办公群联系平台管理员获取私钥" placement="top" :transfer='true' max-width='300px'>
              <Button type="text" size="small" :disabled="true" :style="'color:#c5c8ce;border:1px solid #c5c8ce'" @click="deployment(row)">重新上链</Button>
            </Tooltip> -->

              <!-- <Button type="text" size="small" style="marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF" @click="privatemodal(row)">私钥下载</Button>
            <Button type="text" size="small" style="(row.status === '上链失败' || row.status === '待上链') ? 'color:#3D73EF;border:1px solid #3D73EF' : 'color:#c5c8ce;border:1px solid #c5c8ce'" @click="deployment(row)">重新上链</Button>
            <Tooltip content="请在移动办公群联系平台管理员获取私钥" placement="top" :transfer='true'>

              <Button type="text" size="small" :disabled="(row.status === '上链失败' || row.status === '待上链')?false:true" :style="(row.status === '上链失败' || row.status === '待上链') ? 'color:#3D73EF;border:1px solid #3D73EF' : 'color:#c5c8ce;border:1px solid #c5c8ce'" @click="deployment(row)">重新上链</Button>
            </Tooltip> -->
            </template>
          </Table>
          <Button @click="addRight" icon="md-add" type="success" :disabled="hasEditPermission" ghost>新建权限</Button>
          <!-- <Button @click="addChainDeploy" icon="md-link" style="float:right" type="primary" ghost>一键部署</Button> -->
        </div>
      </Panel>
      <Panel name="4" style="background:#ECEFFC; display:block;" v-if="arrDetails.accountTypeKey === 'CONTRACT'" v-show="!isshow.chainpower">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        智能合约
        <div slot="content" class="basetext">
          <div style="text-align: center" v-show="!isshow.contractpower">
            <img src="@/assets/img/null.png">
            <p style="text-align: center">该链账户还没有部署合约！<br><a @click="handleClick('contract_table')">查看合约列表</a></p>
          </div>
          <div v-if="isshow.contractpower" v-show="isshow.contractpower">
            <Split v-model="splitvalue" class="demo-split">
              <div slot="left">
                <span style="line-height: 36px;">应用名称：<a style="cursor:pointer" @click="clickToContractDetail">{{ contractArr.contractReadableName }}</a></span><br>
                <span style="line-height: 36px;">部署时间：{{ contractArr.deployTime }}</span><br>
                <span style="line-height: 36px;">合约版本：{{ contractArr.uploadVersion }}</span><br>
                <span style="line-height: 36px;">审批人：{{ contractArr.auditUserName }}</span><br>
                <span style="line-height: 36px;">应用简介：{{ contractArr.brief }}</span><br>
              </div>
              <div slot="right" class="demo-split-pane">
                <p style="position:relative;z-index:999;">
                  <b>合约调用量</b>
                  <span class="select-area1" style="float:right;margin-right:12%; ">
                    <b>时间范围</b>
                    <Select v-model="timeScope1" style="width:90px;margin-left:5px;" @on-change="getAccountNameAnalysisList">
                      <Option v-for="item in timeScopeList" :value="item.key" :key="item.key">{{ item.value }}</Option>
                    </Select>
                  </span>
                </p>
                <div v-if="areasData && areasData.length > 0">
                  <Areas areasHeight="240px" areasWidth="94%" :areasData="areasData" :areasXaxis="areasXaxis" style="margin-top:-15px;" :xTitle="''" :yTitle="''"></Areas>
                  <a @click="isshow.modaldata=true" style="float: right;white-space: nowrap;position: relative;top: -140px;font-size:1vw;">
                    <Icon type="ios-arrow-dropright" />更多数据
                  </a>
                </div>
                <div v-else style="margin-right: 18%;margin-top:-30px;">
                  <img :src="imagesurl" style="display: block;position: relative;left: 50%;transform: translate(-120px);">
                  <p style="text-align:center;color:#ccc">暂无数据</p>
                </div>
              </div>
            </Split>
            <span style="line-height: 30px;">action列表:
              <Tooltip max-width="200" content="只有被绑定的普通链账户才能对智能合约发起交易，指定的普通链账户只有在此配置权限后可执行该action。">
                <Icon type="md-help-circle" style="font-size:16px;" />
              </Tooltip>
            </span>
            <p>
              <Collapse simple accordion name="contentpanel" @on-change="getLinkList" v-model="actionValue" style="margin-left:10px;">
                <Panel v-for="(item,index) in contractArr.actionTypeList" :name="item" :key="index">
                  {{ item }}
                  <p slot="content" class="basetext">
                  <Table stripe :columns="columns" :data="contractArrList"></Table>
                  <Button @click="addBundleAccount" icon="md-add" type="success" :disabled="hasEditPermission" ghost>新增绑定链账户</Button>

            </p>
      </Panel>
    </Collapse>
    </p>
  </div>
  </div>
  </Panel>
  <Panel name="5" style="background:#ECEFFC;" v-if="arrDetails.accountTypeKey === 'CONTRACT'" v-show="!isshow.chainpower" class="panel-class">
    <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
    运维信息
    <div slot="content" style="padding-top: 20px;">
      <div v-show="isshow.formgroup" :key="transferKey1">
        <p style="margin:0 26px;line-height:30px">
          合约类型：{{ arrDetails.ops.contractTypeDesc }}
          <Button style="position: relative;float: right;z-index:999;" :disabled="hasEditPermission" @click.native="showEdit" type="primary">编辑</Button>
        </p>
        <p style="margin:0 26px;line-height:30px"> TPS预估：{{ arrDetails.ops.tps }} </p>
        <p style="margin:0 26px;line-height:30px"> 运维联系人：
          <span v-if="arrDetails.ops.opsLinkman.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ arrDetails.ops.opsLinkman.tenantName }}</span>
          <span v-if="arrDetails.ops.opsLinkman.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ arrDetails.ops.opsLinkman.name }}</span>
          <span v-if="arrDetails.ops.opsLinkman.phone"><i class="ri-smartphone-line"></i>{{ arrDetails.ops.opsLinkman.phone }}</span>
        </p>
        <p style="margin:0 26px;line-height:30px"> 需求联系人：
          <span v-if="arrDetails.ops.demandSide.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ arrDetails.ops.demandSide.tenantName }}</span>
          <span v-if="arrDetails.ops.demandSide.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ arrDetails.ops.demandSide.name }}</span>
          <span v-if="arrDetails.ops.demandSide.phone"><i class="ri-smartphone-line"></i>{{ arrDetails.ops.demandSide.phone }}</span>
        </p>
        <p style="margin:0 26px;line-height:30px">
          <span style="vertical-align: top;line-height:30px">调用联系人：</span>
        <ul style="display:inline-block;line-height:30px;margin-left:4px;">
          <li v-for="item in arrDetails.ops.caller" :key="item.name + Math.random()">
            <span v-if="item.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ item.tenantName }}</span>
            <span v-if="item.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ item.name }}</span>
            <span v-if="item.phone"><i class="ri-smartphone-line"></i>{{ item.phone }}</span>
          </li>
        </ul>
        </p>
      </div>
      <div :key="transferKey1 + 's'" v-show="!isshow.formgroup">
        <Button style="position: relative;float: right;z-index:999;" @click.native="getOps">取消</Button>
        <Button style="position: relative;float: right;margin: 0 10px;z-index:999;" @click.native="editOps" type="primary">保存</Button>
        <FormGroup style="margin-left: -50px;" ref="childMethod" :pastOps="JSON.stringify(arrDetails.ops)"></FormGroup>
      </div>
    </div>
  </Panel>

  <!-- 新建合约链账户 -->
  <Panel name="6" style="background:#ECEFFC; " v-if="arrDetails.accountTypeKey === 'NORMAL'">

    <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
    合约链账户绑定
    <div slot="content" class="basetext">
      <div style="margin-bottom:10px">注：仅上链成功的普通链账户可绑定合约</div>
      <Table stripe :columns="columnsContract" :data="columnsContractArr">

      </Table>
      <Button @click="addContract" icon="md-add" :disabled="hasEditPermission" type="success" ghost v-if="this.statusdetails==='上链成功'">新增绑定链账户</Button>

      <!-- <Tooltip v-else content="仅上链成功的普通链账户可绑定合约" placement="top" :transfer='true' max-width='300px'> -->
      <Button v-else type="text" :disabled="true" icon="md-add" :style="'color:#c5c8ce;border:1px solid #c5c8ce'" ghost>新增绑定链账户</Button>
      <!-- </Tooltip> -->
      <!-- <Button @click="addChainDeploy" icon="md-link" style="float:right" type="primary" ghost>一键部署</Button> -->
    </div>
  </Panel>

  <Panel name="7" style="background:#ECEFFC; ">
    <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
    最近操作
    <p slot="content" class="basetext">
      <span>{{ arrDetails.updateTime }} ：{{ arrDetails.recentActions }}</span>
    </p>
  </Panel>
  </Collapse>
  <Modal v-model="isshow.modalpower" title="新建权限" :draggable="true" sticky :mask-closable="false">
    <Form ref="permission" :rules="permissionRule" :model="permission" :label-width="150" @submit.native.prevent>
      <FormItem label="权限名称：" prop="permissionName">
        <Tooltip max-width="200" content="权限长度支持1-12位,仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字" style="margin-left: -18px;">
          <Icon type="md-help-circle" style="font-size:16px;" />
        </Tooltip>
        <Input v-model="permission.permissionName" :maxlength="12" show-word-limit placeholder="请填写" style="width:300px;" />
      </FormItem>
      <FormItem label="父权限：" prop="parentPermissionName">
        <Select v-model="permission.parentPermissionName" placeholder="请选择" style="width:300px;">
          <Option v-for="item in arrDetails.accountPermissionVOs" v-show="item.permissionName !== 'owner'" :value="item.permissionName" :key="item.permissionName">{{ item.permissionName }}</Option>
        </Select>
      </FormItem>
    </Form>
    <Form ref="permissionKey" :rules="permissionKeyRule" :model="permission" :label-width="150">
      <FormItem label="密钥生成方式：">
        <i-switch v-model="permission.autoGenerateKeyPair" @on-change="change" size="large">
          <span slot="open">自动</span>
          <span slot="close">手动</span>
        </i-switch>
        <Tooltip max-width="200" v-if="!permission.autoGenerateKeyPair" content="在钱包节点使用cleos create key --to-console命令创建密钥对">
          <Icon type="md-help-circle" style="font-size:16px;" />
        </Tooltip>
      </FormItem>
      <FormItem label="公钥：" v-show="!permission.autoGenerateKeyPair" prop="publicKey">
        <Input v-model="permission.publicKey" :maxlength="53" show-word-limit placeholder="请填写" style="width:300px;" />
      </FormItem>
      <FormItem label="私钥：" v-show="!permission.autoGenerateKeyPair" prop="privateKey">
        <Input v-model="permission.privateKey" :maxlength="51" show-word-limit placeholder="请填写" style="width:300px;" />
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="cancelPower('permission')">取消</Button>
      <Button type="primary" @click="okPower('permission')">确定</Button>
    </div>
    <!-- <div style="line-height:50px;margin-left:30px;">
        <span class="tdstyle">权限名称：
          <Tooltip max-width="200" content="权限长度支持1-12位,仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字" style="margin-left: -8px;">
            <Icon type="md-help-circle" style="font-size:16px;" />
          </Tooltip>
        </span>
        <Input v-model="permission.permissionName" :maxlength="12" show-word-limit placeholder="请填写" style="width:300px;" />
      </div>
      <div style="line-height:50px;margin-left:30px;">
        <span class="tdstyle">父权限：</span>
        <Select v-model="permission.parentPermissionName" placeholder="请选择" style="width:300px;" >
          <Option v-for="item in arrDetails.accountPermissionVOs" v-show="item.permissionName !== 'owner'" :value="item.permissionName" :key="item.permissionName">{{ item.permissionName }}</Option>
        </Select>
      </div>
      <div style="line-height:50px;margin-left:30px;">
        <span class="tdstyle">密钥生成方式：</span>
        <i-switch v-model=" permission.autoGenerateKeyPair" @on-change="change" size="large">
          <span slot="open">自动</span>
          <span slot="close">手动</span>
        </i-switch>
        <div style="line-height:50px;" v-show="!permission.autoGenerateKeyPair">
          <span class="tdstyle">公钥：</span>
          <Input v-model="permission.publicKey" placeholder="请填写" style="width:300px;" />
        </div>
        <div style="line-height:50px;" v-show="!permission.autoGenerateKeyPair">
          <span class="tdstyle">私钥：</span>
          <Input v-model="permission.privateKey" placeholder="请填写" style="width:300px;" />
        </div>
      </div> -->
  </Modal>
  <Modal v-model="isshow.modal" title="新增绑定链账户" :draggable="true" sticky :mask-closable="false" style="background-color:#fff" width="550">
    <Form :label-width="140" ref="formItem" :model="formItem" :rules="formItemRule">
      <FormItem label="链账户名称：" prop="normalAccountId" style="margin-bottom:10px">

        <Select v-model="formItem.normalAccountId" placeholder="请选择" style="width:350px;" @on-change="addAccountList" :disabled="isshow.modalContract">
          <Option v-for="item in accountList" :value="`${item.normalAccountId}`" :key="item.normalAccountId">{{ item.normalAccountName }}</Option>
        </Select>
        <!-- <Input v-model="normalAccount" placeholder="" style="width:350px;" :disabled="true" v-else /> -->

        <!-- <p style="font-size:12px;margin-top:10px;"><span>没有合适的链账户，可先</span><router-link to="/new_user">新建链账户</router-link></p> -->
      </FormItem>
      <p style="font-size:12px;padding:0px 0 10px 140px;" v-if="!isshow.modalContract"><span>没有合适的链账户，可先</span>
        <router-link to="/new_user">新建链账户</router-link>
      </p>
      <FormItem label="合约功能名称：">
        <Input v-model="actionValues" placeholder="" style="width:350px;" :disabled="true" />
      </FormItem>
      <FormItem label="链账户配置权限：" prop="checkAllGroup">
        <RadioGroup v-model="formItem.checkAllGroup">
          <Radio v-for="item in permissionList" :key="item.id" :label="`${item.permissionId}`"> {{ item.permissionName }} </Radio>
        </RadioGroup>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="cancel('formItem')">取消</Button>
      <Button type="primary" @click="ok('formItem')">确定</Button>
    </div>
  </Modal>

  <Modal v-model="isshow.modalpassword" title="密码验证" :draggable="true" sticky :mask-closable="false">
    <Form :label-width="140" ref="passwordRef" :model="passwordItem" :rules="passwordRule" @submit.native.prevent>
      <FormItem prop="password" label="登录密码：">
        <Input v-model="passwordItem.password" placeholder="请输入密码" style="width:300px;" type="password" @keyup.enter.native="okPassword('passwordRef')" />
      </FormItem>
    </Form>
    <!-- <div style="line-height:50px;margin-left:30px;">登录密码：
        <Input v-model="password" placeholder="请输入密码" style="width:300px;" type="password"/>
      </div> -->
    <div slot="footer">
      <Button type="text" @click="celPassword">取消</Button>
      <Button type="primary" @click="okPassword('passwordRef')">确定</Button>
    </div>
  </Modal>
  <!-- <Modal v-if="isshow.contractpower" v-model="isshow.modaldata" class="analysisModal" width="70vh" title="智能合约数据" footer-hide :mask-closable="false">
    <ContractAnalysis :chainId="chainId" :chainAccountId="chainAccountId" :chainAccountName="chainAccountName" :timeScopeList="timeScopeList" :chainName="chainName"></ContractAnalysis>
  </Modal> -->
  <Modal width="600px" v-model="zyModal" title="资源申请" :draggable="true" sticky :mask-closable="false" @on-cancel="cancelresource('resource')">
    <div>
      <p style="margin-bottom:1%">申请规则:</p>
      <p style="margin-bottom:1%">(注*:请按照申请规则对需要申请的资源进行计算及申请原因填写)</p>
      <p style="margin-bottom:2%">计算公式: RAM=单条数据大小*单日数据上链条数数据保存天数(计算时自行转换单位)</p>
      <p style="color:#8C8C8C">示例:&nbsp;如: &nbsp;每条数据大小1K,每天上链2000条，需保存1个月的数据(转化MB单位如下所示)</p>
      <p style="margin-left: 11%;color:#8C8C8C;margin-bottom:1%">1*2000* (1*30) =60000KB=58 .59375MB≈60MB</p>
    </div>
    <Form :label-width="140" ref="resource" :model="resource" :rules="resourceRef" @submit.native.prevent>
      <FormItem prop="ram" label="RAM分配：">
        <Input v-model="resource.ram" placeholder="请按要求计算并填写需要分配的RAM资源" :maxlength="17" show-word-limit style="width:310px;" />&nbsp;&nbsp;<Button @click="btn_select">{{this.btn_title}}</Button>
      </FormItem>
      <FormItem prop="reason" label="申请原因：">
        <Input v-model="resource.reason" placeholder="请输入申请原因" style="width:360px;" type="textarea" :maxlength="300" show-word-limit :autosize="{minRows: 3,maxRows: 5}" />
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="cancelresource('resource')">取消</Button>
      <Button type="primary" @click="okresource('resource')">确定</Button>
    </div>
  </Modal>
  <Modal v-if="isshow.contractpower" v-model="isshow.modaldata" class="analysisModal" width="70vh" title="智能合约数据" footer-hide :mask-closable="false">
    <ContractAnalysis :chainId="chainId" :chainAccountId="chainAccountId" :chainAccountName="chainAccountName" :timeScopeList="timeScopeList" :chainName="chainName"></ContractAnalysis>
  </Modal>

  <!-- 新建合约链账户弹框 -->
  <Modal width="800px" v-model="isshow.modalContract" title="合约链账户绑定" :draggable="true" sticky :mask-closable="false" @on-cancel="getmodalContract">
    <div>
      <span>
        合约链账户绑定
      </span>
      <Select v-model="operationType" style="width: 180px" placeholder="请选择链账户类型" @on-change='getChangeChain'>
        <Option v-for="(item,index) in operationTypeArr" :value="item.value" :key="item.value">{{ item.label }}</Option>
      </Select>
      <Select v-model="operationName" style="width: 180px" :label-in-value='true' placeholder="请选择要绑定的合约链账户" @on-change='getChangeChainName'>
        <Option v-for="(item,index) in operationNameArr" :value="item.chainAccountId" :key="item.chainAccountId">{{ item.chainAccountName }}</Option>
      </Select>
    </div>
    <div v-show="showContent">
      <div>
        <!-- <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b> -->
        <p style="margin-top:10px">
          <b>合约链账户基本信息:</b>
        </p>

        <p slot="content" class="basetext">
          <span>名称：{{ arrDetailsModal.chainAccountName }}</span>
          <span>类型：{{ arrDetailsModal.accountType }}</span>
          <span>创建时间：{{ arrDetailsModal.createTime }}</span>
          <span>更新时间：{{ arrDetailsModal.updateTime }}</span>
        </p>
        <p slot="content" class="basetext">
          <span>描述：{{ arrDetailsModal.description }}</span>
        </p>
        <p slot="content" class="basetext" :style="this.statusdetails==='上链失败'||this.statusdetails==='上链成功'?'display:none':'display:block'">
          <span>状态：{{ arrDetailsModal.status }}</span>
        </p>
        <!-- <p slot="content" class="basetext">

        </p> -->
        <div v-if="this.infodetailModal!==null" slot="content">

          <div slot="content" class="basetext" v-if="this.statusdetails==='上链失败'">
            <p v-if="infodetailModal.code===null">
              <span>状态：{{ arrDetailsModal.status }}</span>
              <span style="line-height:30px">失败原因：{{infodetailModal.errorInfo}}</span>
            </p>
            <p v-else>
              <span>状态：{{ arrDetailsModal.status }}</span>
              <span style="line-height:30px">编码信息：{{infodetailModal.code}}</span>
              <span style="line-height:30px">失败原因：{{infodetailModal.what}}</span>
              <span style="line-height:30px">方法：{{infodetailModal.name}}</span>
            </p>

          </div>
          <div slot="content" class="basetext" v-else-if="this.statusdetails==='上链成功'">
            <!-- <span style="line-height:30px" v-if="infodetail.transactionId===null">交易ID：</span> -->
            <span>状态：{{ arrDetailsModal.status }}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <span style="line-height:30px;">交易ID：{{infodetailModal.transactionId}}</span>
          </div>
        </div>
      </div>
      <!-- <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b> -->

      <p style="margin-top:10px">
        <b>智能合约基本信息:</b>
      </p>
      <div>
        <!-- <div style="text-align: center" v-show="!isshow.contractpower">
        <img src="@/assets/img/null.png">
        <p style="text-align: center">该链账户还没有部署合约！<br><a @click="handleClick('contract_table')">查看我的合约列表</a></p>
      </div> -->
        <div>
          <div>
            <span style="line-height: 36px;">应用名称：<a style="cursor:pointer" @click="clickToContractDetailModal">{{ contractArr1.contractReadableName }}</a></span><br>
            <span style="line-height: 36px;">部署时间：{{ contractArr1.deployTime }}</span><br>
            <span style="line-height: 36px;">合约版本：{{ contractArr1.uploadVersion }}</span><br>
            <span style="line-height: 36px;">审批人：{{ contractArr1.auditUserName }}</span><br>
            <span style="line-height: 36px;">应用简介：{{ contractArr1.brief }}</span><br>
          </div>
          <!-- <div slot="right" class="demo-split-pane">
            <p style="position:relative;z-index:999;">
              <b>合约调用量</b>
              <span class="select-area1" style="float:right;margin-right:12%; ">
                <b>时间范围</b>
                <Select v-model="timeScope1" style="width:90px;margin-left:5px;" @on-change="getAccountNameAnalysisList">
                  <Option v-for="item in timeScopeList" :value="item.key" :key="item.key">{{ item.value }}</Option>
                </Select>
              </span>
            </p>
            <div v-if="areasData && areasData.length > 0">
              <Areas areasHeight="240px" areasWidth="94%" :areasData="areasData" :areasXaxis="areasXaxis" style="margin-top:-15px;" :xTitle="''" :yTitle="''"></Areas>
              <a @click="isshow.modaldata=true" style="float: right;white-space: nowrap;position: relative;top: -140px;font-size:1vw;">
                <Icon type="ios-arrow-dropright" />更多数据
              </a>
            </div>
            <div v-else style="margin-right: 18%;margin-top:-30px;">
              <img :src="imagesurl" style="display: block;position: relative;left: 50%;transform: translate(-120px);">
              <p style="text-align:center;color:#ccc">暂无数据</p>
            </div>
          </div> -->
          <span style="line-height: 30px;">action列表:
            <Tooltip max-width="200" content="只有被绑定的普通链账户才能对智能合约发起交易，指定的普通链账户只有在此配置权限后可执行该action。">
              <Icon type="md-help-circle" style="font-size:16px;" />
            </Tooltip>
          </span>
          <p>
            <Collapse simple accordion name="contentpanel" @on-change="getLinkList1" v-model="actionValueModal" style="margin-left:10px;">
              <Panel v-for="(item,index) in contractArr1.actionTypeList" :name="item" :key="index">
                {{ item }}
                <p slot="content" class="basetext">
                <Table stripe :columns="columnsModal" :data="contractArrList1"></Table>
                <Button @click="chooseBundleAccount" icon="md-add" type="success" ghost>选择权限</Button>
          </p>
          </Panel>
          </Collapse>
          </p>
        </div>
      </div>
    </div>

    <div slot="footer">
      <Button type="text" @click="getmodalContract">取消</Button>
      <!-- <Button type="primary" @click="okContract('resource')">确定</Button> -->
    </div>
  </Modal>
  <!-- 金库 -->
  <!-- /^(?!(.)\1{4,}|([a-zA-Z0-9])\2{2,})[\u4e00-\u9fa5]{3,}$/ -->
  <!-- <Modal v-model="vault" width="800px" title="金库审批" @on-ok="handleSubmit" @on-cancel="handleReset">
    <Alert show-icon>当前操作已触发金库模式，请选择审批方式和审批人进行审批，当前窗口将在 {{times}}后关闭。</Alert>
    <Form ref="formCustom" :model="formCustom" :rules="ruleCustom" :label-width="120">

      <FormItem label="选择审批人" prop="approver">
        <Select v-model="formCustom.approver" placeholder="Select your city">
          <Option value="beijing">New York</Option>
          <Option value="shanghai">London</Option>
          <Option value="shenzhen">Sydney</Option>
        </Select>
        <CheckboxGroup v-model="formCustom.checkbox">
          <Checkbox label="保存为常用审批人"></Checkbox>
        </CheckboxGroup>

        <Table stripe :columns="vaultColumns" :data="dataVault">
          <template slot-scope="{ row, index }" slot="action">
            <Button type="text" size="small" style="marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF;" @click="resourcemodal(row)">资源申请</Button>
          </template>
        </Table>

      </FormItem>

      <FormItem label="选择申请场景" prop="scene">
        <Select v-model="formCustom.scene" placeholder="Select your city">
          <Option value="beijing">New York</Option>
          <Option value="shanghai">London</Option>
          <Option value="shenzhen">Sydney</Option>
        </Select>
      </FormItem>
      <FormItem label="选择申请原因" prop="cause">
        <Select v-model="formCustom.cause" placeholder="Select your city">
          <Option value="beijing">New York</Option>
          <Option value="shanghai">London</Option>
          <Option value="shenzhen">Sydney</Option>
        </Select>
      </FormItem>
      <FormItem label="申请原因" prop="desc">

        <Input v-model="formCustom.desc" type="textarea" :autosize="{minRows: 5,maxRows: 8}" :minlength="10" :maxlength="60" placeholder="提示:至少包含3个汉字，且长度为10~60个字符，禁止连续输入5个相同字符或3个连续键盘字符"></Input>

        <CheckboxGroup v-model="formCustom.checkbox">
          <Checkbox label="保存为常用申请原因"></Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="审批方式" prop="desc">
        <Button type="primary" size="small" @click="resourcemodal()">短信密钥</Button>
        <Button type="text" size="small" @click="resourcemodal()">短信网关</Button>

      </FormItem>

    </Form>
    <div slot="footer">
      <Button type="primary" @click="handleSubmit('formCustom')">Submit</Button>
      <Button @click="handleReset('formCustom')" style="margin-left: 8px">Reset</Button>
    </div>
  </Modal> -->
  </div>
</template>
<script>
import { configurationResources, getPermission } from '@/api/contract'
import { getChainSele, getActionCon, getListByNormal } from '@/api/dashborad'
import { localRead } from '@/lib/util'
import { getChainTableDetails, addChainPower, addChainDeploy, getPrivateKey, getContractInfo, getContractInfoList, getNormalAccountList, getPermissionList, contractUnbundling, contractbundling, getContractOps, editOpsData, getTimeScope, getAccountNameAnalysis } from '@/api/data'

import Areas from '_c/areas'
import FormGroup from '_c/form-group'
import ContractAnalysis from '../mycontract/contract-analysis.vue'
import { mapGetters } from 'vuex'
import { isPowerAccount } from '../../../lib/check'

// 使用全局变量存储窗口引用，避免Vue响应式系统处理窗口对象
let globalTargetWin = null;

let unitConversion = {
  Byte (value) { return value * 1 },
  KB (value) { return value / 1024 },
  MB (value) { return value / 1024 / 1024 },
  GB (value) { return value / 1024 / 1024 / 1024 }
}
let unitConversion1 = {
  Byte (value) { return value * 1 },
  KB (value) { return value * 1024 },
  MB (value) { return value * 1024 * 1024 },
  GB (value) { return value * 1024 * 1024 * 1024 }
}
export default {
  name: 'chain_details',
  components: { Areas, ContractAnalysis, FormGroup },
  data () {
    const validateAccount = (rule, value, callback) => {
      if (!isPowerAccount(value)) {
        callback(new Error('仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字'))
      } else {
        callback()
      }
    }
    //
    return {
      times: 3,
      formCustom: {
        approver: '',
        scene: '',
        cause: '',
        desc: ""
      },
      ruleCustom: {
        approver: [
          { required: true, message: 'Please input passwd', trigger: 'blur' }
        ],
        scene: [
          { required: true, message: 'Please input passwd', trigger: 'blur' }
        ],
        cause: [
          { required: true, message: 'Please input passwd', trigger: 'blur' }
        ],
        desc: [
          { required: true, message: '请输入正确的描述', trigger: 'blur', pattern: /^(?!(.)\1{4,}|([a-zA-Z0-9])\2{2,})[\u4e00-\u9fa5]{3,}$/ }
        ],

      },
      ownerTenantId: this.$route.query.ownerTenantId ? this.$route.query.ownerTenantId : '', // 租户id
      tenantNamezh: this.$route.query.tenantName ? this.$route.query.tenantName : '', // 租户名称
      btn_title: 'MB',
      zyModal: false, // 资源申请
      statusdetails: this.$route.query.status ? this.$route.query.status : '',
      timer: null,
      transferKey: 0,
      transferKey1: 0,
      splitvalue: 0.55,
      pageParam: { pageSize: 60, pageIndex: 1 },
      panelValue: ['1', '2', '3', '4', '5', '6', '7'],
      imagesurl: require('@/assets/img/null.png'),
      formItem: { normalAccountId: '', normalAccountName: '', checkAllGroup: '' },
      chainId: this.$route.query.chainId ? this.$route.query.chainId : 0,
      chainName: this.$route.query.chainName ? this.$route.query.chainName : '',
      chainAccountId: this.$route.query.chainAccountId ? this.$route.query.chainAccountId : 0,
      chainAccountUuid: this.$route.query.chainAccountUuid ? this.$route.query.chainAccountUuid : '',
      chainAccountName: this.$route.query.chainAccountName ? this.$route.query.chainAccountName : '',
      isshow: { modal: false, modalpower: false, modalpassword: false, chainpower: true, contractpower: false, formgroup: false, modaldata: false, modalContract: false, },
      checkobj: { indeterminate: true, checkAll: false, checkAllGroup: '' },
      permission: { permissionName: '', parentPermissionName: '', publicKey: '', privateKey: '', autoGenerateKeyPair: true, writtenToChain: '' },
      passwordItem: { password: '' },
      actionValue: '',
      actionValueModal: '',
      actionValues: '',
      accountList: [],
      caller: [],
      permissionList: [],
      arrDetails: {},
      arrDetailsModal: {},
      infodetail: {},
      infodetailModal: {},
      ops: {
        'contractType': '',
        'contractTypeDesc': '',
        'tps': '',
        'opsLinkman': { 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' },
        'demandSide': { 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' },
        'caller': [{ 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }]
      },
      columnsPermission: [
        { title: '父权限', key: 'parentPermissionName', tooltip: true },
        { title: '权限', key: 'permissionName', tooltip: true },
        { title: '生成方式', key: 'autoGenerateKeyPair', tooltip: true },
        { title: '公钥', key: 'publicKey', width: 320 },
        { title: '权限状态', key: 'status', tooltip: true },
        {
          slot: 'action',
          title: '操作',
          align: 'left'
          // render: (h, params) => {
          //   return h('div', [
          //     h('Button', {
          //       props: { type: 'text', size: 'small' }, //, disabled: true
          //       style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
          //       on: { click: () => {
          //         this.permission.permissionName = params.row.permissionName
          //         this.isshow.modalpassword = true
          //         this.passwordItem.password = ''
          //         this.$nextTick(() => {
          //           this.$refs['passwordRef'].resetFields()
          //         })
          //       } }
          //     }, '私钥下载'),
          //     h('Button', {
          //       props: { type: 'text', size: 'small' }, //, disabled: true
          //       style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
          //       on: { click: () => {
          //         // this.permission.permissionName = params.row.permissionName
          //         // this.isshow.modalpassword = true
          //         // this.passwordItem.password = ''
          //         // this.$nextTick(() => {
          //         //   this.$refs['passwordRef'].resetFields()
          //         // })
          //       } }
          //     }, '重新部署')
          //   ])
          // }
        }
      ],
      columnsContract: [
        { title: '合约链账户', key: 'contractAccountName', tooltip: true },
        { title: '应用名称', key: 'contractReadableName', tooltip: true },
        { title: '合约方法', key: 'action', tooltip: true },
        { title: '普通链账户权限', key: 'linkPermission', width: 320 },
        // { title: '绑定时间', key: 'updateTime', width: 320 },
        { // key: 'action',
          title: '操作',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                style: this.buttonStyle,
                on: {
                  click: () => {
                    console.log('解绑===>', params)
                    console.log(JSON.parse(this.columnsContractArr[params.index].contractAccountId));
                    console.log(this.columnsContractArr[params.index].normalAccountId);
                    console.log(this.columnsContractArr[params.index].action);
                    contractUnbundling(this.chainId, JSON.parse(this.columnsContractArr[params.index].contractAccountId), this.columnsContractArr[params.index].normalAccountId, this.columnsContractArr[params.index].action).then(res => {
                      if (res.code === '00000') {
                        this.msgInfo('success', res.message)
                        this.timer = setTimeout(() => {
                          // this.getLinkList(this.actionValue)
                          this.getContractList()
                        }, 2 * 1000)
                      } else if (res.code === '500') {
                        this.msgInfo('error', res.message, true)
                      } else this.msgInfo('info', res.message)
                    }).catch(error => {
                      // console.log('contractUnbundling.error===>', error)
                      this.msgInfo('error', error.message, true)
                    })
                  }
                }
              }, '解绑')
            ])
          }
        }
      ],
      columnsContractArr: [],
      contractArr: {},
      contractArr1: {},

      contractArrList: [],
      contractArrList1: [],
      columns: [
        { title: '普通链账户', key: 'normalAccountName', tooltip: true },
        { title: '权限', key: 'linkPermission', tooltip: true },
        { // key: 'action',
          title: '操作',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                style: this.buttonStyle,
                on: {
                  click: () => {
                    // console.log('解绑===>', params)
                    contractUnbundling(this.chainId, JSON.parse(this.chainAccountId), this.contractArrList[params.index].normalAccountId, this.actionValue[0]).then(res => {
                      if (res.code === '00000') {
                        this.msgInfo('success', res.message)
                        this.timer = setTimeout(() => {
                          this.getLinkList(this.actionValue)
                        }, 2 * 1000)
                      } else if (res.code === '500') {
                        this.msgInfo('error', res.message, true)
                      } else this.msgInfo('info', res.message)
                    }).catch(error => {
                      // console.log('contractUnbundling.error===>', error)
                      this.msgInfo('error', error.message, true)
                    })
                  }
                }
              }, '解绑')
            ])
          }
        }
      ],
      columnsModal: [
        { title: '普通链账户', key: 'normalAccountName', tooltip: true },
        { title: '权限', key: 'linkPermission', tooltip: true },
        { // key: 'action',
          title: '操作',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    // console.log('解绑===>', params)
                    contractUnbundling(this.chainId, JSON.parse(this.operationName), this.contractArrList1[params.index].normalAccountId, this.actionValueModal[0]).then(res => {
                      if (res.code === '00000') {
                        this.msgInfo('success', res.message)
                        this.timer = setTimeout(() => {
                          this.getLinkList1(this.actionValueModal)
                        }, 2 * 1000)
                      } else if (res.code === '500') {
                        this.msgInfo('error', res.message, true)
                      } else this.msgInfo('info', res.message)
                    }).catch(error => {
                      // console.log('contractUnbundling.error===>', error)
                      this.msgInfo('error', error.message, true)
                    })
                  }
                }
              }, '解绑')
            ])
          }
        }
      ],
      timeScope1: '',
      timeScopeList: [],
      contractNameList: [],
      areasData: [],
      areasXaxis: [],
      formItemRule: {
        normalAccountId: [{ required: true, message: '请选择', trigger: 'change' }],
        checkAllGroup: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      permissionRule: {
        permissionName: [{ required: true, message: '不能为空', trigger: 'blur' },
        { required: true, trigger: 'blur', validator: validateAccount }],
        parentPermissionName: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      permissionKeyRule: {
        publicKey: [{ required: true, message: '不能为空', trigger: 'blur' },
        { type: 'string', pattern: /^[a-zA-Z0-9]{53}$/, message: '格式有误,长度必须为53位,a-zA-Z0-9', trigger: 'blur' }],
        privateKey: [{ required: true, message: '不能为空', trigger: 'blur' },
        { type: 'string', pattern: /^[a-zA-Z0-9]{51}$/, message: '格式有误,长度必须为51位,a-zA-Z0-9', trigger: 'blur' }]
      },
      passwordRule: {
        password: [{ required: true, message: '不能为空', trigger: 'blur' }]
      },

      columnsResource: [
        {
          title: '单位',
          key: '',
          render: (h, { row }) => {
            // console.log('**************', this)
            return <div>
              {
                <div>
                  <Select style="width:100px" value={row.changname} transfer
                    on={
                      {
                        'on-change': (info) => {
                          // this.change(info, row)
                          console.log('change', row)
                          this.dataResource = [{ ramQuota: unitConversion[info](this.dataResource1[0].ramQuota), ramUsage: unitConversion[info](this.dataResource1[0].ramUsage), odd: unitConversion[info](this.dataResource1[0].odd), proportion: row.proportion, changname: info }]
                        }
                      }
                    }
                  >
                    {
                      this.unitList.map((item) => {
                        return <Option value={item.value} >{item.lable}</Option>
                      })
                    }
                  </Select>
                </div>
              }

            </div>
          }

        },
        { title: 'RAM总量', key: 'ramQuota', tooltip: true },
        { title: 'RAM已用量', key: 'ramUsage', tooltip: true },
        { title: 'RAM剩余量', key: 'odd', tooltip: true },
        { title: 'RAM使用占比', key: 'proportion', tooltip: true },
        {
          slot: 'action',
          title: '操作',
          align: 'left'
        }
      ],
      dataResource: [],
      dataResource1: [],
      resource: { ram: '', reason: '' },
      resourceRef: {
        ram: [{ required: true, message: '请按要求输入需要申请的RAM信息', trigger: 'blur' }, { required: true, trigger: 'blur', pattern: /^([1-9]\d*)$/, message: '不能为0且必须为1-9纯数字' }],
        reason: [{ required: true, message: '请输入申请原因', trigger: 'blur' }]
      },
      unitList: [{ value: 'MB', lable: 'MB' }, { value: 'KB', lable: 'KB' }, { value: 'GB', lable: 'GB' }, { value: 'Byte', lable: 'Byte' }],
      role: '',
      operationTypeArr: [
        {
          value: 'OWN_CONTRACT',
          label: '本租户'
        },
        {
          value: 'SHARE_CONTRACT',
          label: '其他租户(共享给我的)'
        },

      ],
      operationType: '',
      operationName: '',
      operationNameLabel: '',
      operationNameArr: [],
      showContent: false,
      receivedTenantId: '',
      shareRecordId: '',
      vault: true,
      vaultColumns: [
        { title: '账号', key: 'ramQuota', tooltip: true },
        { title: '姓名', key: 'ramQuota', tooltip: true },
        { title: '归属组值', key: 'ramQuota', tooltip: true },
        { title: '手机号', key: 'ramQuota', tooltip: true },
        { title: '账号状态', key: 'ramQuota', tooltip: true },
        { title: '操作', slot: 'action', align: 'left' },
      ],
      dataVault: [
        {
          ramQuota: '1'
        }
      ],
      bg: false,
      targetWin: '',
      funcName: '',
      treasuryToken: '',
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  computed: {
    ...mapGetters(['getDict']),

    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }

    },
  },
  methods: {
    showDiv () {
      this.bg = true
    },
    closeDiv () {
      this.bg = false
    },
    handleSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$Message.success('Success!');
        } else {
          this.$Message.error('Fail!');
        }
      })
    },
    handleReset (name) {
      this.$refs[name].resetFields();
    },
    btn_select () {
      switch (this.btn_title) {
        case 'KB': this.btn_title = 'Byte'
          break
        case 'MB': this.btn_title = 'GB'
          break
        case 'GB': this.btn_title = 'KB'
          break
        case 'Byte': this.btn_title = 'MB'
          break
      }
    },
    getmodalContract () {
      this.isshow.modalContract = false
      this.getContractList()
    },
    getChangeChain (e) {
      this.showContent = false;
      if (e) {
        this.operationType = e
        this.operationName = ''
        this.operationNameArr = [],
          this.getChainSelection()
      }

    },
    getChangeChainName (e) {
      if (e) {
        this.actionValueModal = ''
        let data = this.operationNameArr.filter(item => {
          return item.chainAccountId == e.value
        })
        this.operationNameLabel = e.label
        if (data.length > 0) {
          this.receivedTenantId = data[0].receivedTenantId
          this.shareRecordId = data[0].shareRecordId

        }
        this.getChainSelecName()
      }

    },

    getChainSelection () {
      let data = {
        contractSource: this.operationType
      }
      getChainSele(this.chainId, data).then(res => {
        if (res.code === '00000') {
          this.operationNameArr = res.data
          // res.data.map(item => {
          //   this.operationNameArr.push({
          //     value: item.chainAccountId,
          //     label: item.chainAccountName,
          //   })
          // })
        } else {
          this.showContent = false
          this.msgInfo('warning', res.message, true)
        }
      }).catch(error => {
        this.showContent = false
        this.msgInfo('error', error.message, true)
      })
    },
    getChainSelecName () {
      getChainTableDetails(this.chainId, this.operationNameLabel).then(res => {
        if (res.code === '00000') {
          this.arrDetailsModal = res.data
          this.infodetailModal = res.data.message
          this.showContent = true
        } else {
          this.showContent = false
          this.msgInfo('warning', res.message, true)
        }
      }).catch(error => {
        this.showContent = false
        this.msgInfo('error', error.message, true)
      })
    },
    // 资源申请弹框
    resourcemodal () {
      this.zyModal = true
    },
    // 申请确定
    okresource (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let applySourceMsg = this.resource.ram + this.btn_title
          let byte = unitConversion1[this.btn_title](this.resource.ram)
          // console.log(byte)
          let ramdata = {
            applySourceMsg: applySourceMsg,
            chainId: this.chainId,
            chainAccountName: this.chainAccountName,
            applySize: byte, // 申请大小
            applyReason: this.resource.reason, // 申请理由
            description: this.arrDetails.description, // 合约描述
            ownerTenantId: this.ownerTenantId, // 租户id
            tenantName: this.tenantNamezh,// 租户名称
            chainAccountId: this.chainAccountId
          }
          configurationResources(ramdata).then(res => {
            if (res.code !== '00000') {
              if (res.code === '500') {
                this.msgInfo('error', res.message, true)
              } else {
                this.msgInfo('warning', res.message, true)
                this.zyModal = true
              }
            } else {
              this.msgInfo('success', res.message)
              this.cancelresource()
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
          // his.msgInfo('success', '新建成功！', true)
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },
    // 申请取消
    cancelresource (name) {
      this.zyModal = false
      // this.$refs[name].resetFields()
      this.$nextTick(() => {
        this.$refs['resource'].resetFields()
      })
    },
    // 私钥
    privatemodal (row) {
      this.permission.permissionName = row.permissionName
      if (localStorage.getItem('userSource') == 0) {
        this.isshow.modalpassword = true
      } else {
        getPrivateKey(this.chainId, this.arrDetails.chainAccountId, this.passwordItem.password = '', this.permission.permissionName).then(res => {
          // console.log('getPrivateKey===>', res)
          if (res.code === '00000') {
            localStorage.setItem('privateKey', res.data)
            alert('私钥（请自行保存）：' + res.data, 'warning')
            this.isshow.modalpassword = false
          } else if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else if (res.code === 'A0314') {
            this.showBank()
          } else this.msgInfo('warning', res.message, true)
        }).catch(error => {
          // console.log('getPrivateKey.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }
      this.passwordItem.password = ''
      this.$nextTick(() => {
        this.$refs['passwordRef'].resetFields()
      })
    },
    // 重新部署
    deployment (row) {
      addChainDeploy(this.chainId, this.arrDetails.chainAccountId, row.id).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else this.msgInfo('info', res.message)
      }).catch(error => {
        // console.log('addChainDeploy.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    // 新建权限
    addRight () {
      this.isshow.modalpower = true
      this.init()
      this.$nextTick(() => {
        this.$refs['permission'].resetFields()
      })
      this.$nextTick(() => {
        this.$refs['permissionKey'].resetFields()
      })
    },
    addContract () {

      this.isshow.modalContract = true
      this.showContent = false
      this.operationType = ''
      this.operationNameArr = []
      this.operationName = ''

    },
    // 新增绑定链账户
    addBundleAccount () {
      this.isshow.modal = true
      this.init()
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      // this.formItem.normalAccountId = this.accountList[0].normalAccountId
    },
    chooseBundleAccount () {
      this.$nextTick(() => {
        this.$refs.formItem.fields.forEach((e) => {
          if (e.prop == 'checkAllGroup') {
            e.resetField()
          }
        })
      })
      this.isshow.modal = true
      this.accountList = [
        {
          normalAccountName: this.arrDetails.chainAccountName,
          normalAccountId: this.chainAccountId,
        }
      ]
      this.formItem.normalAccountId = this.chainAccountId + ''
      this.addAccountListModal(this.formItem.normalAccountId)
      // getNormalAccountList(this.chainId, this.operationName, this.actionValueModal[0], this.operationType, this.shareRecordId, this.receivedTenantId, this.chainAccountId).then(res => {
      //   if (res.code !== '00000') {
      //     if (res.code === '500') {
      //       this.msgInfo('error', res.message, true)
      //     } else {
      //       this.msgInfo('warning', res.message, true)
      //     }
      //   } else {
      //     if (res.data.length > 0) {
      //       this.accountList = res.data
      //       this.formItem.normalAccountId = this.accountList[0].normalAccountId + ''
      //       this.addAccountListModal(this.formItem.normalAccountId)
      //     } else {
      //       this.accountList = [
      //         {
      //           normalAccountName: this.arrDetails.chainAccountName,
      //           normalAccountId: this.chainAccountId,
      //         }
      //       ]
      //       this.formItem.normalAccountId = this.chainAccountId
      //     }

      //   }
      // }).catch(error => {
      //   // console.log('contractUnbundling.error===>', error)
      //   this.msgInfo('error', error.message, true)
      // })
      // this.formItem.normalAccountId = this.accountList[0].normalAccountId + ''
      // this.addAccountList(true)
    },
    init () {
      // this.actionValue = ''
      this.passwordItem.password = ''
      this.permissionList = []
      this.formItem = { normalAccountId: '', checkAllGroup: '' }
      this.checkobj = { indeterminate: true, checkAll: false, checkAllGroup: '' }
      this.permission = { permissionName: '', parentPermissionName: '', publicKey: '', privateKey: '', autoGenerateKeyPair: true, writtenToChain: '' }
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    handleClick (pagename) {
      if (this.formItem.normalAccountId) {
        // console.log(this.chainId, this.formItem.normalAccountId)
        this.$router.push({
          name: 'chain_details',
          params: {
            chainId: this.chainId,
            chainAccountId: this.formItem.normalAccountId
          }
        })
      } else this.$router.replace({ name: pagename })
    },
    // handleCheckAll () {
    //   if (this.checkobj.indeterminate) this.checkobj.checkAll = false
    //   else this.checkobj.checkAll = !this.checkobj.checkAll
    //   this.checkobj.indeterminate = false
    //   if (this.checkobj.checkAll) {
    //     let newarr = []
    //     for (let i = 0; i < this.permissionList.length; i++) newarr.push(this.permissionList[i].permissionId)
    //     this.checkobj.checkAllGroup = newarr
    //   } else this.checkobj.checkAllGroup = []
    // },
    // checkAllGroupChange (data) {
    //   if (data.length === this.permissionList.length) {
    //     this.checkobj.indeterminate = false
    //     this.checkobj.checkAll = true
    //   } else if (data.length > 0) {
    //     this.checkobj.indeterminate = true
    //     this.checkobj.checkAll = false
    //   } else {
    //     this.checkobj.indeterminate = false
    //     this.checkobj.checkAll = false
    //   }
    // },
    showEdit () {
      setTimeout(() => {
        this.$refs.childMethod.getOps()
      }, 0)
      this.isshow.formgroup = false
    },
    // addChainDeploy (index) {
    //   addChainDeploy(this.chainId, this.arrDetails.chainAccountId).then(res => {
    //     if (res.code !== '00000') this.msgInfo('warning', res.message, true)
    //     else this.msgInfo('info', res.message)
    //   }).catch(error => {
    //     // console.log('addChainDeploy.error===>', error)
    //     this.msgInfo('error', error.message, true)
    //   })
    // },
    addAccountList (val) {
      if (val) {
        getPermissionList(this.chainId, this.arrDetails.chainAccountId, this.actionValue[0], this.formItem.normalAccountId).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else this.permissionList = res.data
        }).catch(error => {
          // console.log('getPermissionList.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }
    },
    addAccountListModal (val) {
      if (val) {
        getPermissionList(this.chainId, this.arrDetailsModal.chainAccountId, this.actionValueModal[0], this.formItem.normalAccountId).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else this.permissionList = res.data
        }).catch(error => {
          // console.log('getPermissionList.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }
    },
    ok () {
      this.$refs['formItem'].validate((valid) => {
        if (valid) {
          let FormData = {
            'chainAccountId': this.isshow.modalContract ? JSON.parse(this.operationName) : JSON.parse(this.chainAccountId),
            'action': this.isshow.modalContract ? this.actionValueModal[0] : this.actionValue[0],
            'normalAccountId': this.formItem.normalAccountId,
            'permissionIdList': [this.formItem.checkAllGroup],
            'shareRecordId': this.shareRecordId,
            'receivedTenantId': this.receivedTenantId,
            'contractSource': this.operationType
          }
          // console.log('FormData:', FormData)
          contractbundling(this.chainId, FormData).then(res => {
            if (res.code !== '00000') {
              if (res.code === '500') {
                this.msgInfo('error', res.message, true)
              } else {
                this.msgInfo('warning', res.message, true)
              }
            } else {
              this.msgInfo('success', res.message, true)

              this.timer = setTimeout(() => {
                this.isshow.modalContract ? this.getLinkList1(this.actionValueModal) : this.getLinkList(this.actionValue)
              }, 2 * 1000)
              this.isshow.modal = false
            }
          }).catch(error => {
            // console.log('contractUnbundling.error===>', error)
            this.msgInfo('error', error.message, true)
          })
        }
      })
    },
    okPower (val) {
      let power = false
      this.$refs[val].validate((valid) => {
        if (valid) {
          power = valid
        }
      })

      if (!this.permission.autoGenerateKeyPair) {
        this.$refs['permissionKey'].validate((validKey) => {
          if (validKey && power) {
            let data = {
              publicKey: this.permission.publicKey,
              privateKey: this.permission.privateKey
            }
            getPermission(data).then(res => {
              if (res.code === '00000') {
                this.postAddChainPower()
              } else {
                this.msgInfo('error', res.message, true)
              }
            }).catch(error => {
              this.msgInfo('error', error.message, true)
            })
          }
        })
      } else if (power) {
        this.permission.publicKey = ''
        this.permission.privateKey = ''
        this.postAddChainPower()
      }
    },
    postAddChainPower () {
      addChainPower(this.chainId, this.chainAccountName, this.permission).then(res => {
        if (res.code === '00000') {
          this.arrDetails.accountPermissionVOs = this.arrDetails.accountPermissionVOs.concat(res.data)
          this.isshow.modalpower = false
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else this.msgInfo('warning', res.message, true)
      }).catch(error => {
        // console.log('addChainPower.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    cancelPower () {
      this.init()
      this.isshow.modalpower = false
    },
    cancel () {
      this.init()
      this.isshow.modal = false
    },
    celPassword () {
      this.isshow.modalpassword = false
      // this.closeDiv()
    },
    okPassword (val) {

      this.$refs[val].validate((valid) => {
        if (valid) {
          getPrivateKey(this.chainId, this.arrDetails.chainAccountId, this.passwordItem.password, this.permission.permissionName).then(res => {
            // console.log('getPrivateKey===>', res)
            if (res.code === '00000') {
              // this.showDiv()
              localStorage.setItem('privateKey', res.data)
              alert('私钥（请自行保存）：' + res.data, 'warning')
              this.isshow.modalpassword = false
            } else if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else if (res.code === 'A0314') {
              this.showBank()
            } else this.msgInfo('warning', res.message, true)
          }).catch(error => {
            // console.log('getPrivateKey.error===>', error)
            this.msgInfo('error', error.message, true)
          })
        }
      })
    },
    change (val) {
      if (val) {
        this.arrDetails.autoGenerateKeyPair = val
        // this.permission.publicKey = '/'
        // this.permission.privateKey = '/'
      }
    },
    getAccountList () {
      if (this.actionValue[0]) { // console.log('this.actionValue[0]:', this.actionValue)
        getNormalAccountList(this.chainId, this.chainAccountId, this.actionValue[0]).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {
            this.accountList = res.data

          }
        }).catch(error => {
          // console.log('contractUnbundling.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }
    },
    getLinkList (name) {
      // console.log('getLinkList~~', this.chainId, this.chainAccountId, name, name.length)
      if (name[0]) {
        getContractInfoList(this.chainId, this.chainAccountId, name[0]).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {

            this.contractArrList = res.data
            this.getAccountList()
          }
        }).catch(error => {
          console.log('getContractInfoList.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }
    },
    getLinkList1 (name) {
      // console.log('getLinkList~~', this.chainId, this.chainAccountId, name, name.length)
      if (name[0]) {
        getContractInfoList(this.chainId, this.operationName, name[0], this.operationType, this.shareRecordId, this.receivedTenantId, this.chainAccountId).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {

            this.contractArrList1 = res.data
            this.getAccountList1()
          }
        }).catch(error => {
          console.log('getContractInfoList.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }
    },
    getAccountList1 () {
      if (this.actionValue[0]) { // console.log('this.actionValue[0]:', this.actionValue)
        getNormalAccountList(this.chainId, this.chainAccountId, this.actionValue[0]).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {
            this.accountList = res.data

          }
        }).catch(error => {
          // console.log('contractUnbundling.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }
    },
    getOps () {
      getContractOps(this.chainId, this.chainAccountId).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else if (res.data.ops) {
          if (this.isshow.formgroup) this.isshow.formgroup = false
          else this.isshow.formgroup = true
          this.arrDetails.ops = res.data.ops
          ++this.transferKey1
          // console.log(JSON.stringify(res.data.ops))
        } else this.$refs.childMethod.getOps()
      }).catch(error => {
        // console.log('getContractOps.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    editOps () {
      if (this.$refs.childMethod.getOps()) {
        editOpsData(this.chainId, this.chainAccountId, this.$refs.childMethod.getOps()).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else this.getOps()
        }).catch(error => {
          // console.log('editContractOps.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }
    },
    getTimeScopeList () {
      // 查询所有时间范围
      this.$store.dispatch('getOptions', 'TIME_SCOPE')
        .then((result) => {
          if (Object.prototype.toString.call(result) === '[object Boolean]') {
            this.timeScopeList = this.getDict.TIME_SCOPE
          } else {
            this.timeScopeList = result
          }
          // console.log('this.getDict.TIME_SCOPE:', this.getDict.TIME_SCOPE, result, Object.prototype.toString.call(result))
          if (this.timeScopeList && this.timeScopeList[0].value) {
            this.timeScope1 = this.timeScopeList[0].key
            this.getAccountNameAnalysisList()
          }
        })
        .catch(err => { this.msgInfo('error', err.message, true) })
    },
    getAccountNameAnalysisList () {
      getAccountNameAnalysis(this.chainId, [this.chainAccountName], this.timeScope1).then(res => {
        if (res.code !== '00000') {
          this.areasData = []
          // this.msgInfo('warning', res.message, true)
        } else {
          this.areasData = res.data.series
          this.areasXaxis = res.data.xaxis
        }
      }).catch(error => {
        // console.log('getAccountNameAnalysisList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    clickToContractDetail () {
      if (this.contractArr.contractId) {
        this.$router.push({
          name: 'contract_details',
          params: { contractId: this.contractArr.contractId }
        })
      } else {
        this.msgInfo('warning', '智能合约ID不存在，请检查', true)
      }
    },
    clickToContractDetailModal () {
      if (this.contractArr1.contractId) {
        this.$router.push({
          name: 'contract_details',
          params: { contractId: this.contractArr1.contractId }
        })
      } else {
        this.msgInfo('warning', '智能合约ID不存在，请检查', true)
      }
    },
    getContractList () {
      getListByNormal(this.chainId, this.chainAccountId).then(res => {
        if (res.code === '00000') {
          this.columnsContractArr = res.data
        } else {
          this.msgInfo('warning', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // ok () {
    //   this.$Message.info('Clicked ok');
    // },
    // cancel () {
    //   this.$Message.info('Clicked cancel');
    // }
    showBank (func) {
      this.funcName = func;
      var iWidth = 700; //模态窗口宽度
      var iHeight = 450;//模态窗口高度
      var iTop = (window.screen.height - iHeight - 100) / 2;
      var iLeft = (window.screen.width - iWidth) / 2;
      var winOption = 'height=' + iHeight + ',innerHeight=' + iHeight + ',width=' + iWidth + ',innerWidth=' + iWidth + ',top=' + iTop + ',left=' + iLeft + ',toolbar=no,menubar=no,scrollbars=no,resizeable=no,location=no,status=no';

      var obj = new Object();
      obj.operCode = localStorage.getItem("privateEncrypt");
      obj.mainLoginName = "";
      obj.subLoginName = localStorage.getItem("userNameEncrypt")
      obj.appCode = "JTNGCMBAAS";
      obj.sessionId = sessionStorage.getItem("session");
      obj.serverIp = window.location.host.split(":")[0]
      obj.serverPort = window.location.port;
      obj.checkSessionUrl = "/";
      obj.svcNum = "";
      obj.operContent = "";

      // var returnValue;
      //增加浏览器的判断，ie走if原有逻辑，非ie走else逻辑,通过遮罩层实现。
      var a1 = navigator.userAgent;
      var yesIE = a1.search(/Trident/i);
      if (window.ActiveXObject || window.attachEvent || yesIE > 0) { //IE
        // var returnValue = window.showModalDialog("b.html?id=" + new Date(), obj, "dialogHeight:" + iHeight + "px; dialogWidth:" + iWidth + "px; toolbar:no; menubar:no; titlebar:no; scrollbars:yes; resizable:no; location:no; status:no;left:" + iLeft + "px;top:" + iTop + "px;");
        var me = new Object();
        // me.data = returnValue;
        this.receiveMsg(me);
      } else {  //非IE
        this.showDiv();//显示遮罩层
        this.openWindowWithPostRequest(iWidth, iHeight, iTop, iLeft, winOption, obj);
        if (window.addEventListener) {
          //为window注册message事件并绑定监听函数
          window.addEventListener('message', this.receiveMsg, false);
        } else {
          window.attachEvent('message', this.receiveMsg);
        }
      }
    },
    openWindowWithPostRequest (iWidth, iHeight, iTop, iLeft, winOption, obj) {
      var winName = "sWindow";
      var winURL = "http://api.it4a.cmit.cmcc:7081/uac/web3/jsp/goldbank/goldbank3!goldBankIframeAction.action";//应用侧对应后台服务action
      var form = document.createElement("form");
      form.setAttribute("method", "post");
      form.setAttribute("action", winURL);
      form.setAttribute("target", winName);
      for (var i in obj) {
        if (obj.hasOwnProperty(i)) {
          var input = document.createElement('input');
          input.type = 'hidden';
          input.name = i;
          input.value = obj[i];
          form.appendChild(input);
        }
      }
      document.body.appendChild(form);
      //打开地址，刚开始时，打开一个不存在的地址，这样才有返回值
      globalTargetWin = window.open("", winName, winOption);
      form.target = winName;
      form.submit();
      document.body.removeChild(form);
      if (window.focus) {
        globalTargetWin.focus();
      }
    },
    //接收返回值后处理函数
    receiveMsg (e) {
      console.log(e, 'e');
      // returnValue = e.data;

      // 确保 e.data 是字符串类型
      if (e.data && typeof e.data === 'string') {
        var dataStatus = e.data.split("#")
        const statusMessageMap = {
          '-3': { message: '金库应急开启中，允许业务继续访问', allowAccess: true },
          '-2': { message: '金库场景或元业务未开启，允许业务继续访问', allowAccess: true },
          '-1': { message: '直接关闭窗口，未申请审批，不允许业务继续访问', allowAccess: false },
          '1': { message: '审批通过，允许业务继续访问', allowAccess: true },
          '0': { message: '审批不通过，不允许业务继续访问', allowAccess: false },
          '2': { message: '超时，允许业务继续访问', allowAccess: true },
          '3': { message: '超时，不允许业务继续访问', allowAccess: false },
          '4': { message: '出现错误或异常（包括数据异常），不允许业务继续访问', allowAccess: false },
          '5': { message: '未配置策略，允许业务继续访问', allowAccess: true },
          '6': { message: '未配置策略，不允许继续访问', allowAccess: false }
        };
        const status = dataStatus[0];
        const statusInfo = statusMessageMap[status];
        console.log(statusInfo)
        if (statusInfo) {
          if (statusInfo.allowAccess) {
            // 允许业务继续访问，继续请求接口
            // 这里调用你继续请求接口的函数
            getPrivateKey(this.chainId, this.arrDetails.chainAccountId, this.passwordItem.password, this.permission.permissionName, e.data).then(res => {
              // console.log('getPrivateKey===>', res)
              if (res.code === '00000') {
                localStorage.setItem('privateKey', res.data)
                alert('私钥（请自行保存）：' + res.data, 'warning')
                this.isshow.modalpassword = false
              } else if (res.code === '500') {
                this.msgInfo('error', res.message, true)
              } else if (res.code === 'A0314') {
                this.showBank()
              } else this.msgInfo('warning', res.message, true)
            }).catch(error => {
              // console.log('getPrivateKey.error===>', error)
              this.msgInfo('error', error.message, true)
            })

          } else {
            // 不允许业务继续访问，弹出提示框
            this.msgInfo('warning', statusInfo.message, true);
          }
        }
      } else {
        console.error('e.data 不是字符串类型或为空', e.data);
        // this.msgInfo('error', '金库返回数据格式错误', true);
      }

      // alert("returnValue1111111===" + e.data);

      /**
         *
         *在这里处理业务，执行回调函数
         */
      // if (returnValue != 'undefined' && returnValue != '') {
      //   eval(funcName);
      // }
      if (globalTargetWin != null) {
        globalTargetWin.close();
        // this.closeDiv();//关闭遮罩层
      }
    },


    // //回调测试函数
    test1 () {
      alert("test1");
    }

  },
  watch: {
    arrDetails: {
      handler () {
        if (this.arrDetails.accountTypeKey === 'CONTRACT') {
          this.isshow.chainpower = false
          getContractInfo(this.chainId, this.chainAccountId).then(res => {
            if (res.code === '00000') {
              this.contractArr = res.data
              if (res.data.isDelopyContract !== 0) this.isshow.contractpower = true
            } else if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else this.msgInfo('warning', res.message, true)
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        } else this.isshow.chainpower = true
      },
      deep: true,
      immediate: false
    },
    actionValue: {
      handler () {
        if (typeof this.actionValue !== 'string') {
          this.actionValues = this.actionValue[0]
        } else {
          this.actionValues = this.actionValue
        }
      },
      deep: true,
      immediate: true
    },
    actionValueModal: {
      handler () {
        if (typeof this.actionValueModal !== 'string') {
          this.actionValues = this.actionValueModal[0]
        } else {
          this.actionValues = this.actionValueModal
        }
      },
      deep: true,
      immediate: true
    },
    operationName: {
      handler () {
        if (this.operationName) {
          getContractInfo(this.chainId, this.operationName).then(res => {
            if (res.code === '00000') {
              this.contractArr1 = res.data
              // if (res.data.isDelopyContract !== 0) this.isshow.contractpower = true
            } else if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else this.msgInfo('warning', res.message, true)
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        }

      },
      deep: true,
      immediate: false
    },
  },
  beforeDestroy () {
    window.removeEventListener('message', this.receiveMsg, false);

    // 确保关闭全局窗口引用
    if (globalTargetWin) {
      try {
        globalTargetWin.close();
      } catch (e) {
        console.error('关闭窗口失败', e);
      }
      globalTargetWin = null;
    }

    clearInterval(this.timer);
    this.timer = null;
  },
  mounted () {
    console.log(localRead('privateEncrypt'));
    // console.log(this.$route.query.chainId)
    if (this.chainId && this.chainAccountName) {
      getChainTableDetails(this.chainId, this.chainAccountName).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.arrDetails = res.data
          this.dataResource1 = res.data.ramQuota ? [{ ramQuota: this.arrDetails.ramQuota, ramUsage: this.arrDetails.ramUsage, odd: this.arrDetails.odd, proportion: this.arrDetails.proportion, changname: 'MB' }] : []
          this.dataResource = res.data.ramQuota ? [{ ramQuota: unitConversion['MB'](this.arrDetails.ramQuota), ramUsage: unitConversion['MB'](this.arrDetails.ramUsage), odd: unitConversion['MB'](this.arrDetails.odd), proportion: this.arrDetails.proportion, changname: 'MB' }] : []
          this.infodetail = res.data.message
          this.chainAccountId = res.data.chainAccountId ? res.data.chainAccountId : 0
          this.chainAccountUuid = res.data.chainAccountUuid ? res.data.chainAccountUuid : ''
          this.chainAccountName = res.data.chainAccountName ? res.data.chainAccountName : ''
          if (this.arrDetails.ops) this.isshow.formgroup = true
          else {
            // console.log(this.ops)
            this.arrDetails.ops = this.ops
          }
          if (this.arrDetails.accountTypeKey === 'CONTRACT') this.getTimeScopeList()
        }
      }).catch(error => {
        // console.log('getChainTableDetails.error===>', error)
        this.msgInfo('error', error.message, true)
      })

      // getActionCon(this.chainId, this.$route.query.chainAccountId).then(res => {
      //   if (res.code === '00000') {
      //     this.columnsContractArr = res.data
      //   } else {
      //     this.msgInfo('warning', res.message, true)
      //   }
      // }).catch(error => {
      //   this.msgInfo('error', error.message, true)
      // })
      this.getContractList()
    }

    this.role = localRead('roleId')

    // if (this.vault) {
    //   let timeR = setInterval(() => {
    //     this.times--
    //     if (this.times == 1) {
    //       clearInterval(timeR)
    //       this.vault = false
    //     }
    //   }, 1000);
    // }

  },
  destroyed () {
    clearInterval(this.timer)
    this.timer = null
  }
}
</script>
<style lang="less" scoped>
/deep/.ivu-menu-submenu-title {
  background: #fff !important;
}
/deep/.ivu-menu,
.ivu-menu-dark,
.ivu-menu-vertical,
.ivu-menu-opened,
.ivu-menu-submenu-title {
  background: #fff !important;
}
.ivu-menu-vertical.ivu-menu-light:after {
  background: #fff;
}
.tdstyle {
  display: inline-block;
  width: 100px;
  text-align: right;
}
.sizeTitle {
  font-weight: bold;
  font-size: 16px;
}
.size {
  font-weight: bold;
  font-size: 15px;
}
.chain {
  margin: -16px;
  height: 100%;
  .basetext {
    padding-top: 20px;
    span {
      text-align: left;
      margin: 0 26px;
      line-height: 20px;
      word-break: break-all;
    }
  }

  .demo-split {
    height: 200px;
  }
  .title {
    .size;
  }
  .bs {
    text-indent: 10px;
    line-height: 15px;
    border-left: 5px solid #3d73ef;
    margin-bottom: 15px;
  }
}
.basetext {
  padding-top: 10px;
  span {
    text-align: left;
    margin: 0 10px;
    line-height: 20px;
    word-break: break-all;
  }
}
.select-area1 {
  /deep/.ivu-select-selection {
    border: 1px solid #4b98eb;
  }
  /deep/.ivu-select-input {
    color: #57a3f3;
  }
  /deep/.ivu-icon-ios-arrow-down:before {
    color: #57a3f3;
  }
}
.panel-class {
  i {
    vertical-align: -0.15em;
  }
}
.analysisModal /deep/.ivu-modal-body {
  background-color: #eff0f4;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.ivu-split-trigger-con {
  display: none;
}
/deep/.ivu-card {
  background: #f2f6fd;
}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
/deep/.ivu-collapse[name="mainpanel"] {
  border: none;
}
/deep/.ivu-collapse[name="mainpanel"] > .ivu-collapse-item {
  border: none;
  border-radius: 5px 5px;
  margin-bottom: 10px;
}
</style>
