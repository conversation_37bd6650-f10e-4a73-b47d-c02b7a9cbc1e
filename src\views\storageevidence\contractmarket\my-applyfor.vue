<template>
  <div>
    <div>
      <h2>合约信息</h2>
     <p  class="basetext">
           <span>合约包名称：{{previewData.contractBagName}}</span>
        </p>
          <p  class="basetext">
            <span>链类型：{{previewData.chainType}}</span>
        </p>
          <p  class="basetext">
          <span>合约语言：{{previewData.contractLanguage}}</span>
        </p>
           <p  class="basetext">
          <span>适用场景信息：{{previewData.applicaSecene}}</span>
        </p>
         <p  class="basetext">
 <span>版本信息:</span>
   <edit-table-mul :height="200" style="width: 900px;" border :columns="VersionTitle" v-model="VersionData" ></edit-table-mul>
    </p>
    </div>
    <!--  -->
     <div v-if="this.upstatus==='off'">
      <h2>申请下架说明</h2>
<p class="mandatory">下架原因：<Input v-model="value17" maxlength="30" show-word-limit type="textarea" placeholder="请填写申请下架原因" style="width: 600px" /></p>
    </div>
   <div v-else>
      <h2>恢复上架说明</h2>
<p class="mandatory">上架原因：<Input v-model="value17" maxlength="30" show-word-limit type="textarea" placeholder="请填写申请上架原因" style="width: 600px" /></p>
    </div>

    <div class="applyfor_btn">
        <Button type="primary" style="margin-right: 3%;" @click="SubmitApplyfor">提交审核</Button>
        <Button @click="back">返回</Button>
    </div>

    <!-- 查询合约链码 -->
 <Modal v-model="chaincode" title="查询合约链码" width='900px'>
      <p style="margin-bottom:20px">上传版本号：{{this.title}}</p>
      <Collapse simple @on-change="colldata" accordion>
        <Panel :name="transferKey1" :key="transferKey1">
          {{transferKey1}}
          <p slot="content">
            <textarea class="textarea-style" v-html="CollContent.fileContent" readonly @scroll="handScroll($event, 'cpp')"></textarea>
          </p>
        </Panel>
        <Panel :name="item" v-for="item in filesHpp" :key='item'>
          {{item}}
          <p slot="content">
            <textarea class="textarea-style" v-html="CollContent.fileContent" readonly @scroll="handScroll($event, 'hpp')"></textarea>
          </p>
        </Panel>

      </Collapse>
    </Modal>
        <!-- 详情弹框 -->
       <!-- <Modal
        v-model="versionmodal"
        title="版本详情"
        width='700px'
        >
        <div>
   <div  class="detailModalInfo">
          <h3>运维信息 </h3>
        </div>
           <p  class="detailModal">
          合约类型：<span> {{versionDetails.contractTypeDesc}}</span>
        </p>
           <p  class="detailModal">
          TPS预估：<span> {{versionDetails.tps}}</span>
        </p>
        </div>
        <div class="versionDetailtwo">
           <ul class="pending_ui" v-for="item in opsLinkman" :key="item.id">
        <li> 运维联系人：<span  v-if="item.tenantName"><i class="ri-organization-chart"></i>{{item.tenantName}}</span> </li>
           <li> <span v-if="item.name"><i class="ri-user-line"></i>{{item.name}}</span> </li>
           <li><span v-if="item.phone"><i class="ri-smartphone-line"></i>{{item.phone}}</span> </li>
         </ul>
         <ul class="pending_ui">
          <li> 需求联系人：<span v-if="demandSide.tenantName"><i class="ri-organization-chart"></i>{{demandSide.tenantName}}</span> </li>
          <li> <span v-if="demandSide.name"><i class="ri-user-line"></i>{{demandSide.name}}</span> </li>
          <li><span v-if="demandSide.phone"><i class="ri-smartphone-line"></i>{{demandSide.phone}}</span> </li>
        </ul>

     <ul class="pending_ui" v-for="item in callData" :key="item.id">
            <li>调用联系人： <span v-if="item.tenantName"><i class="ri-organization-chart"></i>{{item.tenantName}}</span> </li>
          <li> <span v-if="item.name"><i class="ri-user-line"></i>{{item.name}}</span> </li>
          <li><span v-if="item.phone"><i class="ri-smartphone-line"></i>{{item.phone}}</span> </li>
        </ul>
        </div>

    </Modal> -->
  </div>
</template>
<script>
import EditTableMul from '_c/edit-table-mul'
import { ContractOwnerPreview, getContractChaincode, MyApplyFor } from '@/api/data'
export default {
  components: {
    EditTableMul
  },
  data () {
    return {
      callData: [],
      upstatus: '', // 判断上架下架
      value17: '', // 申请原因
      versionDetails: [], // 版本详情信息
      versionmodal: false, // 版本详情弹框
      opsLinkman: [], // 运维联系人
      demandSide: {}, // 需求联系人
      chaincode: false, // 查询合约链码弹框
      previewData: [], // 合约信息数组
      VersionTitle: [

        {
          title: '版本号',
          key: 'uploadVersion',
          with: 180
        },
        {
          title: 'cpp文件名',
          key: 'cppFileName'

        },
        {
          title: 'hpp文件名',
          key: 'hppFileNames',
          tooltip: true,
          render: (h, params) => {
            return h('div', params.row.hppFileNames.join(','))
          }

        },
        {
          title: '操作',
          key: 'action',
          width: 180,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              // h(
              //   'Button',
              //   {
              //     props: {
              //       type: 'text',
              //       size: 'small'
              //     },
              //     style: {
              //       marginRight: '8px',
              //       color: '#3D73EF',
              //       border: '1px solid #3D73EF'
              //     },
              //     on: {
              //       click: () => {
              //         this.detailModal(params.index)
              //       }
              //     }
              //   },
              //   '详情'
              // ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      // 合约链码
      transferKey1: '',
      CollContent: {},
      VersionData: [],
      codeData: {},
      filesHpp: [],
      title: '',
      //
      contractId: '', // 合约id
      sort: ''// 次数
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, top: 300 }) },
    back () {
      this.$router.push({
        name: 'contract_market'
      })
    },
    // 点击详情
    // detailModal (index) {
    //   // console.log(this.VersionData[index].uploadVersion)
    //   let uploadVersion = this.VersionData[index].uploadVersion
    //   let contractId = this.VersionData[index].contractId
    //   getAccountOpsDTOMessage(uploadVersion, contractId).then(res => {
    //     this.callData = res.data.caller
    //     this.versionDetails = res.data
    //     this.opsLinkman = [res.data.opsLinkman]
    //     this.demandSide = res.data.demandSide
    //     // console.log(this.opsLinkman)
    //   })
    //   this.versionmodal = true
    // },
    // 点击文件源码
    fileModal (params) {
      this.chaincode = true
      this.title = params.row.uploadVersion
      this.transferKey1 = params.row.cppFileName
      this.filesHpp = params.row.hppFileNames
      this.codeData = {
        contractId: params.row.contractId,
        uploadVersion: params.row.uploadVersion,
        fileName: params.row.cppFileName,
        fileId: params.row.id,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
    },
    colldata (key) {
      // console.log(this.codeData)
      // console.log(key)
      if (key[0]) {
        let fileName = key[0]
        let contractId = this.codeData.contractId
        let uploadVersion = this.codeData.uploadVersion
        let pageParam = {
          pageSize: 1,
          pageIndex: 10
        }

        getContractChaincode(contractId, uploadVersion, fileName, pageParam).then(res => {
          if (res.code === '00000') {
            this.CollContent = res.data
            // console.log(this.CollContent)
          } else {
            this.msgInfo('error', res.message, true)
          }
        }).catch((error) => {
          this.msgInfo('error', error.message, true)
        })
      }
    },
    // 滚动
    handScroll (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        // console.log('到底了', fileType, this.codeTotalPages[fileType])
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    // 请求
    getTablist () {
      this.contractId = this.$route.params.contractId
      this.sort = this.$route.params.sort
      ContractOwnerPreview(this.contractId, this.sort).then(res => {
        if (res.code === '00000') {
          this.previewData = res.data
          // console.log()
          let versionData = res.data.records.map(item => {
            return {
              ...item,
              hppFileNames: item.hppFileNames ? item.hppFileNames : []
            }
          })
          this.VersionData = versionData
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    SubmitApplyfor () {
      if (this.value17 === '') {
        this.$Message.info('原因不能为空!')
      } else {
        let appforInfo = {
          contractBagId: this.previewData.id, // 合约包id
          contractBagName: this.previewData.contractBagName,
          applyType: this.upstatus,
          applyReason: this.value17
        }
        MyApplyFor(appforInfo).then(res => {
        // console.log(res.data)
          if (res.code === '00000') {
            this.$router.push({
              name: 'contract_market'
            })
            this.$Message.success('提交审核成功!')
            this.value17 = ''
          } else {
            this.msgInfo('error', res.message, true)
          }
        }).catch(error => {
          this.msgInfo('error', error.message, true)
        })
      }
    }
  },
  mounted () {
    // console.log(this.$route.params.status)
    this.upstatus = this.$route.params.status
    this.getTablist()
  }
}
</script>
<style lang="less" scoped >
.versionDetailtwo{
  i{
    vertical-align: -0.15em
  }
  padding: 2%;
  // margin-top: 3%;
  // margin: 2%;
  .pending_ui{
    margin-top: 2%;
  }
}
.detailModalInfo{
margin-bottom: 2%;
}
// 按钮
.applyfor_btn{
margin-top: 2%;
margin-left: 3%;
}
//
  .mandatory::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;

}
// 详情弹框
.detailModal{
  span{
  margin-left: 5%;
}
}
.pending_ui{
display: flex;

li:nth-child(2){
  margin-left: 2%;
}
li:nth-child(3){
  margin-left: 2%;
}
}
 .basetext{
    display: flex;
    padding-top: 20px;
    span{text-align: left;margin: 0 26px;line-height: 20px;word-break: break-all;}
  }
  // 滚动条
.textarea-style {
  width: 820px;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
</style>
