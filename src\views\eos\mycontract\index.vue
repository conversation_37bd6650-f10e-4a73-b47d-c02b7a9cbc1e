<template>
  <div class="contract_index">
  <keep-alive v-if="currentTab==='contract_table'" >
    <ContractHome />
  </keep-alive>
  <keep-alive :exclude="excludeArr" v-else>
    <router-view />
  </keep-alive>
  </div>
</template>

<script>
import ContractHome from './contract-home.vue'
export default {
  name: 'contract_index',
  components: {
    ContractHome
  },
  data () {
    return {
      //
      excludeArr: ['contract_details', 'sharecontract_details']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
<style lang="less">
.ivu-modal-confirm-body{
  padding-left:0
}
</style>
