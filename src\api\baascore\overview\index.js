import axios from '../../index'
// const BASEURL = '/baasapi/baascore'
const BASEURL = '/cmbaas/portal/fabric/CommonAPI'
// 获取服务列表
export function getChainList (query) {
  return axios.request({
    //  url:BASEURL + '/chain/getChainList',
    url: BASEURL + '?msgType=chain%23getChainList',
    method: 'get',
    params: query
  })
}
// 查询链统计信息
export function getChainStatistics (query) {
  return axios.request({
    url: BASEURL + '?msgType=chain%23getChainStatistics',
    method: 'get',
    params: query
  })
}
// 查询集群信息
export function getClusterList (query) {
  return axios.request({
    url: BASEURL + '?msgType=chain%23getClusterList',
    method: 'get',
    params: query
  })
}
// 获取链状态
export function getChainState (query) {
  return axios.request({
    url: BASEURL + '?msgType=chainMonitor%23getChainState',
    method: 'get',
    params: query
  })
}

// 链节点
export function getChainMonitor (query) {
  return axios.request({
    url: BASEURL + '?msgType=chainMonitor%23getChainMonitor',
    method: 'get',
    params: query
  })
}
// 检查链的个数限制
export function checkChainLimit (obj) {
  let data = {
    msgType: 'chain#checkChainLimit',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
// 链节点资源选择节点
// export function getChainNodeNameList(query) {
//   return axios.request({
//       url: BASEURL + '/chainOrg/getChainNodeNameList',
//       method: 'get',
//       params: query
//   })
// }
