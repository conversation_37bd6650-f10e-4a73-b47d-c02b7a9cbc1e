<!--
  aturun
  编辑注册
  2021/10/21

-->
<template>
  <el-dialog
      class="dialog_sty edit_registration"
      title="编辑注册"
      :visible.sync="Visible"
      width="700px"
      :modal="Visible"
      :close-on-click-modal="false"
      destroy-on-close
      @opened="open">
    <div class="dialog_content">
<!--      <pre>{{ JSON.stringify(textInfo, null, 4)  }}</pre>-->
      <el-table
          :data="tableData"
          style="width: 100%"
          height="500px"
          stripe
          ref="multipleTable"
          @selection-change="handleSelectionChange">
        <el-table-column
            type="selection"
            width="55">
        </el-table-column>
        <el-table-column label="关联模版" prop="tempName" width="120px"></el-table-column>
        <el-table-column prop="chainName" label="关联链" width="150px"></el-table-column>
        <el-table-column
            label="状态"
            width="150">
          <template slot-scope="scope">
              <el-tag
                  size="medium"
                  class="tag_succ"
                  v-if="scope.row.regStatus==0">注册成功</el-tag>
              <el-tag
                  size="medium"
                  class="tag_err"
                  v-else-if="scope.row.regStatus==1">注册失败</el-tag>
            <el-tag
                size="medium"
                class="tag_war"
                v-else-if="scope.row.regStatus==2">注销</el-tag>
          </template>
        </el-table-column>
        <el-table-column
            label="创建时间"
        >
          <template slot-scope="scope">
            {{setDates(scope.row.createTime)}}
          </template>
        </el-table-column>
      </el-table>

    </div>
        <span slot="footer" class="dialog-footer">
           <el-button type="primary" @click="addTemp">添加模版</el-button>
          <el-button type="primary" @click="handleRegister">重试注册</el-button>
          <el-button type="primary" @click="handleCancellation">注 销</el-button>
           <el-button type="danger" @click="handleDelete">删 除</el-button>
           <el-button @click="Visible = false">取 消</el-button>
      </span>
    <addTempDialog ref="addTempDialogRef"  @Refresh="Refresh"></addTempDialog>
  </el-dialog>
</template>

<script>
import * as api from "../api";
import {delProviderTemplate, getOracleTempList, getProviderRegisterList, logoutTemp, regAgainTemp} from "../api";
import {getFormatDates} from "@/utils/atuUtils";
import addTempDialog from './addTempDialog'
export default {
  name: "seeTransactionInfo",
  components: {
    addTempDialog
  },
  props:[],
  data() {
    return {
      tableTitle:{dd:'ff',gg:'ftt'},
      tableData:[],
      Visible:false,
      busId:null,
      textInfo:{
      },
      loading:false,
      text:'',
      multipleSelection:[],
      fieldName:{},
      rules: {
        name: [
          {required: true, message: '请输入用户名称', trigger: 'blur'},
        ],
      },
      providerId:''
    }
  },
  watch: {},
  created() {

  },
  mounted() {
  },
  methods: {
    open(){
      this.getProviderRegisterList()
    },
    setDates(val){
      return getFormatDates(val,'yyyy-mm-dd MM:mm:ss')
    },
    addTemp(){
      // if(this.multipleSelection.length==0) return this.$message.warning('请选择')
      // let tempList = []
      // this.multipleSelection.map(item=>{
      //   tempList.push(item.tempId)
      // })
      this.$refs.addTempDialogRef.providerId=this.providerId

     this.$refs.addTempDialogRef.Visible=true
    },
    handleRegister() {
      if(this.multipleSelection.length==0) return this.$message.warning('请选择')
      let tempList = []
      let IsLogout = false
      this.multipleSelection.map(item=>{
        tempList.push(item.tempId)
        if(item.regStatus==0){
          IsLogout = true
        }
      })
      if(IsLogout) return this.$message.warning('已注册成功，不能重新注册')
      if(this.loading) return
      this.loading = true
      api.regAgainTemp({
        "providerId": this.providerId,
        "tempList": tempList
      }).then(res=>{
        this.loading = false
        if(res.code!=0) return this.$message.warning(res.msg)
        this.$confirm('信源模板注册成功', '注册成功', {
          confirmButtonText: '确定',
          showClose:false,
          showCancelButton:false
        }).then(() => {
        }).catch(() => {
        });
        this.Refresh()
      }).catch(err=>{
        this.loading = false
        this.$confirm('信源模板注册失败，请稍后重试!\n' +
            '失败原因：\n' +
            '\n' +
            err, '注册失败', {
          confirmButtonText: '确定',
          showClose:false,
          showCancelButton:false
        }).then(() => {
        }).catch(() => {
        });
      })
    },
    // 删除
    handleDelete() {
      if(this.multipleSelection.length==0) return this.$message.warning('请选择')
      let tempList = []
      let IsLogout = false
      this.multipleSelection.map(item=>{
        tempList.push(item.tempId)
        if(item.regStatus==0){
          IsLogout = true
        }
      })
      if(IsLogout) return this.$message.warning('成功信源模板无法删除，请选择其他')
      this.$confirm('此操作将删除已选信源模板, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type:'warning',
        showClose:false
      }).then(() => {
        if(this.loading) return
        this.loading = true
        api.delProviderTemplate({
            "providerId": this.providerId,
            "tempList": tempList
          }
        ).then(res=>{
          this.loading = false
          if(res.code!=0) return this.$message.warning(res.msg)
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.Refresh()
        })
      }).catch(() => {
        this.loading = false
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    // 注销
    handleCancellation() {
      if(this.multipleSelection.length==0) return this.$message.warning('请选择')
      let tempList = []
      let IsLogout = false
      this.multipleSelection.map(item=>{
        tempList.push(item.tempId)
        if(item.regStatus==2){
          IsLogout = true
        }
      })
      if(IsLogout) return this.$message.warning('已注销，请选择其他')
      this.$confirm('此操作将注销该信源模板, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showClose:false
      }).then(() => {
        if(this.loading) return
        this.loading = true
        api.logoutTemp({
          "providerId": this.providerId,
          "tempList": tempList
        }
        ).then(res=>{
          this.loading = false
          if(res.code!=0) return this.$message.warning(res.msg)
          this.$message({
            type: 'success',
            message: '注销成功!'
          });
          this.Refresh()
        })
      }).catch(() => {
        this.loading = false
        this.$message({
          type: 'info',
          message: '已取消注销'
        });
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    Refresh(){
      this.getProviderRegisterList()
    },
    getProviderRegisterList(){
      api.getProviderRegisterList(this.providerId).then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.tableData = res.result
      })
    },
  },

}
</script>

<style lang="less" scoped>
.edit_registration{
  .dialog_content{
    height:500px;
    //overflow-x: scroll;
  }
}
</style>
