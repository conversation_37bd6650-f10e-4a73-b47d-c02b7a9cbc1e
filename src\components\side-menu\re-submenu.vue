<template>
  <Submenu :name="parent.name">
    <template slot="title">
      <div v-if="parent.selfIcon" class="menu-img-container">
        <img class="menu-img" :style="divStyle" :src="parent.selfIcon" alt="">
      </div>
      <Icon v-else :type="parent.icon" />
      {{ parent.meta.title }}
    </template>
    <template v-for="item in parent.children">
        <re-submenu
          v-if="item.children && item.showChild"
          :key="`menu_${item.name}`"
          :name="item.name"
          :parent="item"
        >
        </re-submenu>
        <menu-item v-else-if="!item.hidden" :key="`menu_${item.name}`" :name="item.name" class="child-item">
          <div v-if="item.selfIcon" class="menu-img-container" style="margin-right:5px"> <img class="menu-img" :src="item.selfIcon" alt=""></div>
            <span v-if="item.selfIcon">{{ item.meta.title }}</span>
            <span v-else style="margin-left:10px;">{{ item.meta.title }}</span>
        </menu-item>
      </template>
  </Submenu>
</template>

<script>
export default {
  name: 'ReSubmenu',
  props: {
    parent: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      divStyle: {
        width: this.parent.width ? this.parent.width : '20px',
        verticalAlign: '-0.2em'
      }
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.child-item:hover{
  background: #2d8cf0;
}
</style>
