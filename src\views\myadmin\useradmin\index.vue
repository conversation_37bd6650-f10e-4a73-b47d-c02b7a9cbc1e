<template>
  <div class="user_index">
    <keep-alive v-if="currentTab === 'user_admin'">
      <UserAdmin />
    </keep-alive>
    <keep-alive :exclude="excludeArr" v-else>
    <router-view />
    </keep-alive>
  </div>
</template>

<script>
import UserAdmin from './user-admin.vue'
export default {
  name: 'user_index',
  components: {
    UserAdmin
  },
  data () {
    return {
      excludeArr: ['user_log']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
