import axios from '../../index'
// const BASEURL = '/baasapi/baascore'
const BASEURL = '/cmbaas/portal/fabric/CommonAPI'
// 获取某个通道的统计信息（节点、合约、块、交易）
export function getChannelStatistics (query) {
  return axios.request({
    // url:BASEURL + '/channel/getChannelStatistics',
    url: BASEURL + '?msgType=channel%23getChannelStatistics',
    method: 'get',
    params: query
  })
}
// 通道里的节点列表
export function getChannelPeerList (query) {
  return axios.request({
    // url:BASEURL + '/channel/getChannelPeerList',
    url: BASEURL + '?msgType=channel%23getChannelPeerList',
    method: 'get',
    params: query
  })
}
// 获取某个通道的区块分页列表
export function getChannelBlockListPage (query) {
  return axios.request({
    // url:BASEURL + '/channel/getChannelBlockListPage',
    url: BASEURL + '?msgType=channel%23getChannelBlockListPage',
    method: 'get',
    params: query
  })
}
// 获取通道里某个区块详细信息
export function getChannelBlockDetail (query) {
  return axios.request({
    // url:BASEURL + '/channel/getChannelBlockDetail',
    url: BASEURL + '?msgType=channel%23getChannelBlockDetail',
    method: 'get',
    params: query
  })
}
// 获取某个通道的交易分页列表
export function getChannelTransactionListPage (query) {
  return axios.request({
    // url:BASEURL + '/channel/getChannelTransactionListPage',
    url: BASEURL + '?msgType=channel%23getChannelTransactionListPage',
    method: 'get',
    params: query
  })
}
// 获取某个交易详情
export function getChannelTransactionDetail (query) {
  return axios.request({
    // url:BASEURL + '/channel/getChannelTransactionDetail',
    url: BASEURL + '?msgType=channel%23getChannelTransactionDetail',
    method: 'get',
    params: query
  })
}
// 获取某条链的组织和节点名列表
export function getOrgPeerList (query) {
  return axios.request({
    // url:BASEURL + '/channel/getOrgPeerList',
    url: BASEURL + '?msgType=channel%23getOrgPeerList',
    method: 'get',
    params: query
  })
}
// 创建通道
export function createChannel (obj) {
  // return axios.request({
  //   url:BASEURL + '/channel/createChannel',
  //   method: 'post',
  //   data: obj
  // })
  let data = {
    msgType: 'channel#createChannel',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
// 通道添加新成员 joinPeerToChannel
export function joinPeerToChannel (obj) {
  // return axios.request({
  // url:BASEURL + '/channel/joinPeerToChannel',
  // method: 'post',
  // data: obj
  // })
  let data = {
    msgType: 'channel#joinPeerToChannel',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
// 获取某个通道的区块打包时间信息 getChannelBlockConfig
export function getChannelBlockConfig (query) {
  return axios.request({
    // url:BASEURL + '/channel/getChannelBlockConfig',
    url: BASEURL + '?msgType=channel%23getChannelBlockConfig',
    method: 'get',
    params: query
  })
}
// 设置区块打包时间
export function setChannelBlockBatchTimeout (obj) {
  // return axios.request({
  // url:BASEURL + '/channel/setChannelBlockBatchTimeout',
  // method: 'post',
  // data: obj
  // })
  let data = {
    msgType: 'channel#setChannelBlockBatchTimeout',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
// 获取没有加入某个通道的所有节点列表
export function getUnjoinedPeerListOfChannel (query) {
  return axios.request({
    // url:BASEURL + '/channel/getUnjoinedPeerListOfChannel',
    url: BASEURL + '?msgType=channel%23getUnjoinedPeerListOfChannel',
    method: 'get',
    params: query
  })
}
// 获取背书策略和组织节点信息
export function getChaincodeEndorseRule (query) {
  return axios.request({
    // url:BASEURL + '/chaincode/getChaincodeEndorseRule',
    url: BASEURL + '?msgType=chaincode%23getChaincodeEndorseRule',
    method: 'get',
    params: query
  })
}

// 获取token列表
export function getApiUserList (query) {
  return axios.request({
    // url:BASEURL + '/apiuser/getApiUserList',
    url: BASEURL + '?msgType=apiuser%23getApiUserList',
    method: 'get',
    params: query
  })
}
// refreshApiUserId
export function refreshApiUserId (query) {
  return axios.request({
    // url:BASEURL + '/apiuser/refreshApiUserId',
    url: BASEURL + '?msgType=apiuser%23refreshApiUserId',
    method: 'get',
    params: query
  })
}
// 判断通道名称是否已存在
export function checkChannelName (query) {
  return axios.request({
    // url:BASEURL + '/apiuser/refreshApiUserId',
    url: BASEURL + '?msgType=channel%23checkChannelName',
    method: 'get',
    params: query
  })
}
// 检查通道个数限制
export function checkChannelLimit (query) {
  return axios.request({
    // url:BASEURL + '/apiuser/refreshApiUserId',
    url: BASEURL + '?msgType=channel%23checkChannelLimit',
    method: 'get',
    params: query
  })
}
