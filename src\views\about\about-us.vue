<template>
  <div class="box">
    <div class="background">
<img src="../../assets/img/guanyuwomen.png" alt="">
    </div>
    <div class="content">
<h2>关于我们</h2>
<div>
   <ul class="aboutText">
            <li>
              <h3 class="aboutTitle">产品简介：</h3>
 <span>CMBaaS (China Mobile Blockchain as aService中国移动区块链服务平台)是中国移动的一个区块链网络项目，用来为省专公司内部提供区块链服务，提供在3秒内交易不可逆的性能，为更多、更广泛的业务需求提供基础支撑。
</span>
            </li>
              <li>
              <h3 class="aboutTitle">产品背景：</h3>
              <span>CMBaaS平台经过前期的研发和探索,已上线咪咕订购关系- -致性应用、互联网统- -认证应用、跨区结算应用、国际漫游应用、转售号卡资源管理应用等多个生产业务，成为技术中台区块链领域的重要组成部分,并在2020年启动了全国推广。</span><br>
              <span>随着平台的推广与技术中台的发展，来自省公司，专业公司的应用需求与日剧增，对平台能力提出了更高的要求，例如合约开发部署效率有待提升、交互体验有待升级、国产化程度有待提升等问题。</span><br>
              <span>为满足业务需求，提高平台服务质量，提升平台工作效率，急需-套能够通过前端可视化形式实现复 杂业务操作的平台，CMBaaS控制台因此诞生.</span>
            </li>
              <li>
              <h3 class="aboutTitle">产品愿景：</h3>
              <span>区块链产品将以CMBaaS平台为核心，践行标准化、定制化并行的推广路径，为客户提供高水准
              </span><br>
              <span>
区块链服务，并提升中国移动在区块链行业话语权。使用先进的信息化手段赋能外部党政军企业务DICT转型。
              </span>
            </li>
          </ul>
</div>
    </div>
    <!-- <div class="footer">
      <p>版本号：{{version}}</p>
    </div> -->
  </div>
</template>
<script>
import { version } from '../../../static/config.json'
export default {
  data () {
    return {
      version: '' // 版本号
    }
  },
  mounted () {
    // 引入版本号

    this.version = version
  }
}
</script>
<style lang="less" scoped>

.box{

  .background{
  width: 100%;
// background-image: url('../../assets/img/guanyuwomen.png');
img{
  width: 102%;
  position: relative;
  top: -16px;
  left: -1%;

}
}
.content{
  padding: 1%;
  width: 100%;
  // border: 1px solid red;
  h2{
    text-align: center;
  }
  .aboutText{
  li{
    // display: flex;
    margin: 20px 0;
    .aboutTitle{
      text-align-last: justify;
      width: 100px;
    }

  }
}
}
.footer{
  margin-top: 10%;
  p{
text-align: center;
margin-right: 1%;
  }
}
}

</style>
