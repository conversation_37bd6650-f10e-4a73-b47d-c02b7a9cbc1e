<template>
  <div>
    <h2 class="cz_details_toview">查看存证</h2>
    <hr />
    <!-- 内容 -->
    <div class="cz_details_center">
      <th>
        <tr>
          <td class="justify">登录用户:</td>
          <td>{{ this.content.userLoginId }}</td>
        </tr>
        <tr>
          <td class="justify">用户姓名:</td>
          <td>{{ this.content.realName?this.content.realName:'无' }}</td>
        </tr>
        <tr>
          <td class="justify">组织名称:</td>
          <td>{{ this.content.organization }}</td>
        </tr>
        <tr>
          <td class="justify">存证号:</td>
          <td>
            {{ this.content.code }}
          </td>
        </tr>
        <tr>
          <td class="justify">存证名称:</td>
          <td>
            {{ this.content.title }}
          </td>
        </tr>
        <tr>
          <td class="justify">存证类型:</td>
          <td>
            {{ this.content.type }}
          </td>
        </tr>
        <tr>
          <td class="justify">存证内容:</td>
          <td>{{ this.content.info }}</td>
        </tr>
        <tr>
          <td class="justify">区块链类型:</td>
          <td>{{this.content.chainType=='1'?'EOS':'Chainmaker'}}</td>
        </tr>
        <tr v-if="content.blockNum!=='0'">
          <td class="justify">区块高度:</td>
          <td>{{ this.content.blockNum }}</td>
        </tr>
        <tr>
          <td class="justify">存 证 I D:</td>
          <td>{{ this.content.blockAdress }}</td>
        </tr>
        <tr>
          <td class="justify">IPFS哈希:</td>
          <td>{{ this.content.ipfsAdress }}</td>
        </tr>
      </th>
    </div>
    <div class="cz_details_btn">
      <Button type="primary" @click="out">关闭</Button>
      <Button type="primary" class="down" @click="down" v-if="this.content.type === '文件'">下载</Button>
    </div>
  </div>
</template>
<script>
import { downloadFile } from '@/api/data'
export default {
  name: 'Details',
  data () {
    return {
      survivalId: '',
      content: {}
    }
  },
  methods: {
    details () {
      let curContent // 当前内容
      const { content } = this.$route.params
      if (content) {
        curContent = content
        localStorage.setItem('deinfo', JSON.stringify(content))
      } else {
        curContent = JSON.parse(localStorage.getItem('deinfo'))
      }
      this.content = curContent
    },
    out () {
      this.$router.push({ path: '/survival_direct' })
    },
    // 文件下载
    down () {
      downloadFile(this.content.ipfsAdress).then((res) => {
        var blob = new Blob([res])
        var downloadElement = document.createElement('a')
        var href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = this.content.info
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  created () {
    this.details()
  }
}
</script>

<style lang="less" scoped>
.cz_details_toview {
  margin-left: 57px;
  margin-top: 10px;
  margin-bottom: 21px;
}

.cz_details_center {
  width: 100%;
  margin-top: 20px;
  margin-left: 47px;
  tr {
    margin: 1.5rem auto;
    height: 1.5rem;
    line-height: 1.5rem;
    .justify {
      display: inline-block;
      vertical-align: top;
      width: 100%;
      text-align: justify;
    }
    .justify::after {
      content: "";
      display: inline-block;
      width: 100%;
      overflow: hidden;
      height: 0;
    }
    td:nth-child(2) {
      padding-left: 15px;
      text-align: left;
      font-weight: 500;
    }
  }
}
.cz_details_btn {
  margin: auto;
  width: 400px;
  .down {
    margin-left: 20%;
  }
}
</style>
