<template>
  <div>
    <div class="login">
      <h1>
        <img :src="logoUrl"><br>
        <!-- <b>CMBaaS</b> -->
        <span style="font-size:18px;">中移动CMBaaS —— 同心共筑，"链"接未来</span>
      </h1>
      <card style="width:440px;">
        <div style="margin:10px 0 25px 0;">
          <span style="font-size:20px;font-weight: bold;padding-left:24px;">登录</span>
        </div>
        <div style="margin:10px 24px">
          <Form ref="loginData" :model="loginData" :rules="loginDataValidate">
            <FormItem prop="userLoginId">
              <Input prefix="ios-contact" placeholder="请输入用户名" style="width:360px;opacity:.9;" v-model="loginData.userLoginId" />
            </FormItem>
            <FormItem v-if="flag" prop="password">
              <Input type="password" prefix="md-key" placeholder="请输入密码" style="width:360px;opacity:.9;" v-model="loginData.password">
              <!-- <Icon @click="handlePassword" type="ios-eye-off-outline" size="22" slot="suffix"/> -->
              <i class="ri-eye-close-line" slot="suffix" @click="handlePassword"></i>
              </Input>
            </FormItem>
            <FormItem v-else prop="password">
              <Input type="text" prefix="md-key" placeholder="请输入密码" style="width:360px;opacity:.9;" v-model="loginData.password">
              <!-- <Icon @click="handlePassword" type="ios-eye-outline" size="22" slot="suffix"/> -->
              <i class="ri-eye-line" slot="suffix" @click="handlePassword"></i>
              </Input>
            </FormItem>
            <FormItem>
              <Button type="primary" :loading="loading" icon="ios-power" style="width:360px;" @click="handleSubmit('loginData')">
                <span v-if="!loading">登录</span>
                <span v-else>Loading...</span>
              </Button>
              <p style="padding:10px 0px" v-show="isNoAuthFalg">
                <router-link to="/forget_pwd" style="float:left;">忘记密码？</router-link>
                <router-link to="/register">没有账号？马上注册</router-link>
              </p>
            </FormItem>
          </Form>
        </div>
      </card>
    </div>
    <div class="contact">
      <Row>
        <Col span="10">
        <div>联系电话:</div>
        </Col>
        <Col span="14" v-show="!phone">
        <div>13681445177</div>
        <div>13811131410</div>
        </Col>
        <Col span="14" v-show="phone">
        <div v-for="item in phone">{{item}}</div>
        </Col>
      </Row>
    </div>
    <div class="footer-copyright">

      <p style="margin-left:30%">{{ versionData.version  }}</p>
      <span>{{ versionData.copyright }}</span>
    </div>
    <canvas class="cash" id="" :style='"background:url("+images+") no-repeat;background-size:100% 100%;"'></canvas>
    <Modal v-model="tipModal" :closable="false">
      <div style="text-align:left;font-size:18px;font-weight:bold;">
        <Icon type="ios-close-circle" size="23" style="color:red;"></Icon>
        <span style="margin-left:5px;">提示</span>
      </div>
      <div style="text-align:left;padding:10px 0 10px 30px">
        <p>{{this.tip.title}}</p>
        <p>{{this.tip.desc}}</p>
      </div>
      <div slot="footer">
        <Button @click="tipCancel">取消</Button>
        <Button type="primary" @click="tipConfirm">确定</Button>
      </div>
    </Modal>
    <!-- 跳转4a -->
    <Modal v-model="tipModalA" :closable="false">
      <div style="text-align:left;font-size:18px;font-weight:bold;">
        <Icon type="ios-close-circle" size="23" style="color:red;"></Icon>
        <span style="margin-left:5px;">提示</span>
      </div>
      <div style="text-align:left;padding:10px 0 10px 30px">
        <p>{{this.tip.title}}</p>
        <p>{{this.tip.desc}}</p>
      </div>
      <div slot="footer">
        <!-- <Button @click="tipCancel">取消</Button> -->
        <Button type="primary" @click="tipConfirmA">确定</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
// import { starsNest } from '@/lib/starts'
import { localSave, localRead } from '@/lib/util'
import { loginSysConfig } from '@/api/user'
import { getconfig } from '@/api/contract'
import { encryptedData, decryptData } from '@/lib/encrypt'
import { newDate } from '../../static/config.json'
export default {
  name: 'login_page',
  data () {
    return {
      loading: false,
      flag: true,
      isNoAuthFalg: true, // true显示正常，（没调用auth），false相反
      loginData: {
        userLoginId: '',
        password: ''
      },
      loginDataValidate: {
        userLoginId: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
        password: [{ required: true, message: '密码不能为空', trigger: 'blur' }]
      },
      images: require('@/assets/img/bg0.png'),
      tipModal: false,
      tipModalA: false,
      tip: {},
      phone: [],
      newDate: '',
      versionData: {},
      logoUrl: ''
    }

  },
  mounted () {
    this.getpublicKey()
    this.getphone()
    const storedVersionData = JSON.parse(sessionStorage.getItem('versionData')) || {};
    this.versionData = {
      version: storedVersionData.version || '产品版本：1.5.0',
      copyright: storedVersionData.copyright || 'Copyright © 2022 中移信息技术有限公司',

    };
    this.logoUrl = storedVersionData.logoUrl || '/data/iconLogin.png';

  },
  methods: {
    ...mapActions([
      'login',
      'updateUserLoginId',
      'updateFaceUrl',
      'updateHas',
      'updateCur',
      'updateTas',
      'resetRouterState'
    ]),
    getpublicKey () {

      let name = 'RSA_PUBLIC_KEY'
      getconfig(name).then((res) => {

        sessionStorage.setItem('RSA_PUBLIC_KEY', res.data.value)
        this.savepublicKey(decryptData(res.data.value))
      })
      getconfig('RSA_PRIVATE_KEY').then((res) => {
        sessionStorage.setItem('RSA_PRIVATE_KEY', res.data.value)
        this.savepublicKey(decryptData(res.data.value))
      })
    },
    getphone () {
      let name = 'CONTACT_PHONE'
      getconfig(name).then((res) => {
        this.phone = res.data.value.split(',')
      })
    },
    // 保存系统公钥
    savepublicKey (val) {
      this.$store.commit('SAVE_PUBLICKEY', val)

      // console.log("公钥",this.$store.state.publicKey)
    },
    saveFaceUrl () {
      localSave('faceUrl', '')
      this.updateFaceUrl('')
    },
    handleSubmit (name) {
      // console.log(encryptedData(this.loginData.password,this.$store.state.publicKey),'_________')
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.loading = true
          this.login({
            userLoginId: this.loginData.userLoginId,
            password: encryptedData(this.loginData.password, this.$store.state.publicKey)
          }).then((res) => {
            this.updateUserLoginId(this.loginData.userLoginId)
            if (res.data.faceUrl) {
              const faceUrl = res.data.faceUrl
              this.$http.get(faceUrl).then(res => {
                if (res.status === 200) {
                  localSave('faceUrl', faceUrl)
                  this.updateFaceUrl(faceUrl)
                }
              }).catch(error => {
                this.msgInfo('error', error.message, true)
                // console.log(error.message + ',图片获取失败！！')
                this.saveFaceUrl()
              })
            } else {
              this.saveFaceUrl()
            }
            localSave('userSource', res.data.userSource)
            const publicKey = res.data.publicKey
            localSave('publicKey', publicKey)
            this.updateCur(0)
            this.updateTas(1)
            this.loading = true
            const fabricOpen = res.data.fabricOpen
            localSave('fabricOpen', fabricOpen)
            this.$router.push({
              name: 'dashboard'
            })
          }).catch(error => {
            this.loading = false
            if (error.code === 'A0119') {
              this.tipModal = true
              this.tip.title = error.message
              this.tip.desc = '是否跳转到重置密码？'
            } else if (error.code === 'A0123') {
              this.tipModalA = true
              this.tip.title = error.message
            } else {
              alert(error.message)
            }
          })
        }
      })
    },
    handlePassword () {
      this.flag = !this.flag
    },
    tipConfirm () {
      this.tipModal = false
      this.$router.push({
        name: 'forget_pwd'
      })
    },
    tipConfirmA () {
      this.tipModal = false
      let url4A = localRead('url4A')
      window.location.href = url4A
    },
    tipCancel () {
      this.tipModal = false
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({ background: true, closable: closable, content: content })
    },
    getSystem () {
      loginSysConfig().then(res => {
        // console.log('loginSysConfig===>', res)
        if (res.code !== '00000') this.msgInfo('warning', res.message, true)
        else {
          localSave('url4A', res.data.url)
          if (res.data.showUserInfo && res.data.showUserInfo === '1') {
            this.isNoAuthFalg = true
          } else {
            this.isNoAuthFalg = false
          }
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
        // console.log('loginSysConfig.error===>', error)
      })
    },

  },
  created () {
    this.newDate = newDate
    let that = this
    localStorage.clear()
    // if (this.$store.getters.hasGetRules) {
    this.resetRouterState()
    this.$store.commit('REMOVE_ALLTAB')
    // }
    this.getSystem()
    document.onkeydown = function (e) {
      e = window.event || e
      if (that.$route.path === '/login' && (e.code === 'Enter' || e.code === 'enter')) {
        that.handleSubmit('loginData')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.cash {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000c17;
}
div.login {
  display: grid;
  position: absolute;
  left: 50%;
  top: 45%;
  transform: translate(-50%, -50%);
  z-index: 100;
  opacity: 0.8;
  h1 {
    margin: 10px;
    b {
      text-align: center;
      margin: 0 10px;
      color: #fff;
    }
    span {
      font-size: small;
      color: #ccc;
    }
  }
  p {
    text-align: right;
    padding: 10px 0;
  }
}
.contact {
  position: fixed;
  bottom: 10px;
  right: 5px;
  margin-right: 5px;
  font-size: 12px;
  color: #a5a4bf;
  z-index: 200;
}
</style>
