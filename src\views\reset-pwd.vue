<template>
  <div>
    <div class="forgetpwd">
      <h1>
        <img :src="logoUrl"><br>
        <!-- <b>CMBaaS</b> -->
        <span style="font-size:18px;">中移动CMBaaS —— 同心共筑，"链"接未来</span>
      </h1>
      <card style="width:440px">
        <div style="text-align:center;margin-top:10px;">
          <span style="font-size:20px;font-weight:bold;">重置密码</span><br />
          <Avatar size="small" style="background:#39f295;margin:25px 5px;">√</Avatar>
          <span>邮箱验证</span>
          <span style="color:#C7C7C7;"> ———————————— </span>
          <Avatar size="small" style="background:#2d8cf0;margin-right:5px;">2</Avatar>
          <span style="font-weight:bold;">设置新密码</span>
        </div>
        <div style="margin:10px 19px;">
          <Form ref="formData" :model="formData" :rules="formDataRule">
            <FormItem v-if="flag" prop="password">
              <Input type="password" prefix="ios-key" placeholder="新密码（8-20位）" style="width:370px;" v-model="formData.password">
              <!-- <Icon @click="handlePassword" type="ios-eye-off-outline" size="22" slot="suffix"/> -->
              <i class="ri-eye-close-line" slot="suffix" @click="handlePassword"></i>
              </Input>
            </FormItem>
            <FormItem v-else prop="password">
              <Input type="text" prefix="ios-key" placeholder="新密码（8-20位）" style="width:370px;" v-model="formData.password">
              <!-- <Icon @click="handlePassword" type="ios-eye-outline" size="22" slot="suffix"/> -->
              <i class="ri-eye-line" slot="suffix" @click="handlePassword"></i>
              </Input>
            </FormItem>
            <FormItem v-if="flag" prop="rePassword" style="padding-top:18px;">
              <Input type="password" prefix="ios-key-outline" placeholder="确认密码（8-20位）" style="width:370px;" v-model="formData.rePassword">
              <!-- <Icon @click="handlePassword" type="ios-eye-off-outline" size="22" slot="suffix"/> -->
              <i class="ri-eye-close-line" slot="suffix" @click="handlePassword"></i>
              </Input>
            </FormItem>
            <FormItem v-else prop="rePassword" style="padding-top:18px;">
              <Input type="text" prefix="ios-key-outline" placeholder="确认密码（8-20位）" style="width:370px;" v-model="formData.rePassword">
              <!-- <Icon @click="handlePassword" type="ios-eye-outline" size="22" slot="suffix"/> -->
              <i class="ri-eye-line" slot="suffix" @click="handlePassword"></i>
              </Input>
            </FormItem>
            <FormItem style="padding-top:18px;">
              <Button type="primary" @click="handleSubmit" style="width:370px;">完成</Button>
              <p style="padding-top:10px;text-align:right">
                <router-link to="/login">返回登录</router-link>
              </p>
            </FormItem>
          </Form>
        </div>
      </card>
    </div>
    <div class="footer-copyright">
      <p style="margin-left:30%">{{ versionData.version  }}</p>
      <span>{{ versionData.copyright }}</span>
    </div>
    <canvas class="cash" id="canv" :style='"background:url("+images+") no-repeat;background-size:100% 100%;"'></canvas>
  </div>
</template>

<script>
// import { mapActions } from 'vuex'
// import { starsNest } from '@/lib/starts'
import { enterNewPassword } from '@/api/data'
import { isPassword } from '../lib/check'
import { encryptedData, decryptData } from '@/lib/encrypt'
import { getconfig } from '@/api/contract'
export default {
  name: 'reset_pwd',
  data () {
    const validatePassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.formData.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    const validatePasswordRule = (rule, value, callback) => {
      if (!isPassword(value)) {
        callback(new Error('至少包含一位大小写字母、数字，特殊字符只能包含!#$%且至少一位'))
      } else {
        callback()
      }
    }
    return {
      flag: true,
      // reflag: true,
      formData: {
        password: '',
        rePassword: '',
        userLoginId: this.$route.params.userLoginId ? this.$route.params.userLoginId : '',
        email: this.$route.params.email ? this.$route.params.email : ''
      },
      formDataRule: {
        password: [
          { required: true, min: 8, message: '密码不能少于8位', trigger: 'blur' },
          { max: 20, message: '密码不能多于20位', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validatePasswordRule }
        ],
        rePassword: [
          { required: true, min: 8, message: '密码不能少于8位', trigger: 'blur' },
          { max: 20, message: '密码不能多于20位', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validatePassword }
        ]
      },
      images: require('@/assets/img/bg1.png'),
      versionData: '',
      logoUrl: ''
    }
  },
  mounted () {
    // starsNest()
    this.$nextTick(() => {
      this.$refs['formData'].resetFields()
    })
    this.init()
    // this.$Message.config({
    //   top: 300,
    //   duration: 2
    // })
    this.getpublicKey()
    const storedVersionData = JSON.parse(sessionStorage.getItem('versionData')) || {};
    this.versionData = {
      version: storedVersionData.version || '产品版本：1.5.0',
      copyright: storedVersionData.copyright || 'Copyright © 2022 中移信息技术有限公司',

    };
    this.logoUrl = storedVersionData.logoUrl || '/data/iconLogin.png';
  },
  methods: {
    // ...mapActions([
    //   'register'
    // ]),
    getpublicKey () {
      let name = 'RSA_PUBLIC_KEY'
      getconfig(name).then((res) => {
        this.savepublicKey(decryptData(res.data.value))
      })
    },
    // 保存系统公钥
    savepublicKey (val) {
      this.$store.commit('SAVE_PUBLICKEY', val)

      // console.log("公钥",this.$store.state.publicKey)
    },
    init () {
      this.formData = {
        password: '',
        rePassword: '',
        userLoginId: this.$route.params.userLoginId ? this.$route.params.userLoginId : '',
        email: this.$route.params.email ? this.$route.params.email : ''
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    handleSubmit () {
      // console.log('this.formdata:', this.formData)
      this.$refs['formData'].validate((valid) => {
        if (this.formData.userLoginId && this.formData.email) {
          if (valid) {
            // this.formData.password = encryptedData(this.formData.password,this.$store.state.publicKey)
            let params = {
              userLoginId: this.formData.userLoginId,
              // email: this.formData.email,
              email: encryptedData(this.formData.email, this.$store.state.publicKey),
              password: encryptedData(this.formData.password, this.$store.state.publicKey)
            }
            enterNewPassword(params).then(res => {
              if (res.code === '00000') {
                this.msgInfo('success', this.formData.userLoginId + res.message, true)
                this.$router.push({
                  name: 'login'
                })
              } else {
                this.msgInfo('error', res.message, true)
              }
            }).catch(error => {
              console.log('enterNewPassword-error:', error)
              this.msgInfo('error', error.message, true)
            })
          }
        } else {
          this.msgInfo('warning', '请先输入用户名和邮箱,即将跳转邮箱验证', true)
          this.$router.push({
            name: 'forget_pwd'
          })
        }
      })
    },
    handlePassword () {
      this.flag = !this.flag
    }
  }
}
</script>

<style lang="less" scoped>
.cash {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000c17;
}
div.forgetpwd {
  display: grid;
  position: absolute;
  left: 50%;
  top: 46%;
  transform: translate(-50%, -50%);
  z-index: 100;
  opacity: 0.8;
  h1 {
    margin: 10px;
    b {
      text-align: center;
      margin: 0 10px;
      color: #fff;
    }
    span {
      font-size: small;
      color: #ccc;
    }
  }
  input {
    //margin-bottom: 10px;
    opacity: 0.9;
  }
  .line {
    height: 1px;
    width: 133px;
    background: rgba(255, 255, 255, 1);
    border: none;
    border-top: 1px solid #555555;
  }
  /deep/.ivu-card-body {
    padding: 16px 16px 0 16px;
  }
}
</style>
