<template>
  <div class="page">
    <div class="content" v-show="!isShowObj.isShowMember && !isShowObj.isShowChainCodes && !isShowObj.isShowBlockList && !isShowObj.isShowTransList">
      <div>
        <span class="back"  @click="goback">返回</span>
        <span>{{detail.ChannelName}}</span>
      </div>
      <!-- <div class="title">
        <div class="left">
          <span class="infotext" @click="goChannelMgr">通道信息列表</span>
          <img :src="arrowIcon" class="icon" />
          <span class="name">{{ detail.ChannelName }}通道详情</span>
        </div>
      </div> -->
      <SpaceLayout top="20" paddingX="0" paddingY="0">
        <div slot="padding">
          <ul class="imglist">
            <li v-for="item in imageList" :key="item.name">
              <img :src="item.liImgUrl" class="liImage" />
              <img :src="item.iconImgUrl" class="iconImage" />
              <span class="number">{{ item.number }}</span>
              <span class="name">
                {{ item.name }}
              </span>
            </li>
          </ul>
        </div>
      </SpaceLayout>
      <!--通道token信息-->
      <div>
        <div class="title">
          <div class="left">
            <img :src="infoIcon" class="infoIcon" />
            <span class="infotext detail">Token信息</span>
          </div>
        </div>
        <SpaceLayout top="20" paddingX="0" paddingY="0">
          <div slot="padding">
            <div class="table-wrapper">
              <el-row class="nav-box">
                <el-col :span="5"><div class="">通道组织</div></el-col>
                <!-- <el-col :span="7"><div class="">Token ID</div></el-col>
                <el-col :span="6"><div class="">生效日期</div></el-col> -->
                <el-col :span="19"><div class="">操作</div></el-col>
              </el-row>
              <div class="none" v-if="showToken.length == 0">
                <i class="el-icon-loading" v-if="showTokenText == '数据请求中...'"></i>
                <!-- <svg-icon icon-class="table-empty" v-else/> -->
                {{showTokenText}}
              </div>
              <div class="nan-item" v-for="(item,index) in showToken" :key="index">
                <el-row  class="nav-box">
                  <el-col :span="5"><div class=""><span>{{item.orgname}}</span></div></el-col>
                  <!-- <el-col :span="7"><div class=""><span>{{item.apiUserid}}</span></div></el-col>
                  <el-col :span="6">
                    <div class="">
                      <span>{{dateFormat('YYYY-mm-dd HH:MM:SS',item.insertTime)}}</span>
                    </div>
                  </el-col> -->
                  <el-col :span="19">
                    <span class="handle-btn" @click="refreshApiUserId(item.id)">生成Token</span>
                    <!-- <div v-if="item.status == 0" class="tag gray">失效</div>
                    <div v-if="item.status == 1" class="tag green">有效</div> -->
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </SpaceLayout>
      </div>
      <!--通道节点信息-->
      <div>
        <div class="title">
          <div class="left">
            <img :src="infoIcon" class="infoIcon" />
            <span class="infotext detail">节点信息</span>
          </div>
          <div class="bottonList">
            <!-- <el-button type="primary" class="green-btn btn-bt" @click="getOrgs">添加成员</el-button> -->
            <div class="right">
              <div class="moreWrap" v-show="memberArray.length > 4" @click="goMember">
                <span class="more">更多
                  <i class="el-icon-arrow-right"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
        <SpaceLayout top="20" paddingX="0" paddingY="0">
          <div slot="padding">
            <!-- <el-button type="primary" class="green-btn btn-bt" @click="getOrgs">添加成员</el-button> -->

            <div class="table-wrapper">
              <el-row class="nav-box">
              <el-col :span="6"><div class="">节点名称</div></el-col>
              <el-col :span="5"><div class="">组织名称</div></el-col>
              <el-col :span="10"><div class="">域名</div></el-col>
              <el-col :span="3"><div class="">版本</div></el-col>
            </el-row>
              <div class="none" v-if="showMemberArray.length == 0">
              <i class="el-icon-loading" v-if="showMemberArrayText == '数据请求中...'"></i>
                <!-- <svg-icon icon-class="table-empty" v-else/> -->
                {{showMemberArrayText}}
            </div>
              <div class="nan-item" v-for="(item,memberindex) in showMemberArray" :key="memberindex">
            <el-row class="nav-box">
              <el-col :span="6">
                <div class="">
                  <span>
<!--                    <el-popover trigger="hover" placement="top">-->
                      <p>{{ item.Peer }}</p>
<!--                      <div slot="reference" class="name-wrapper">-->
<!--                        <span class="text" slot="reference">{{-->
<!--                          getName(item.Peer)-->
<!--                        }}</span>-->
<!--                      </div>-->
<!--                    </el-popover>-->
                  </span>
                </div>
              </el-col>
              <el-col :span="5"><div class=""><span>{{(item.Name)}}</span></div></el-col>
              <el-col :span="10"><div class=""><span>{{item.Name +'.'+item.Peer+'.fabric.com'}}</span></div></el-col>
              <el-col :span="3"><div class="">{{channelObject.FabricVersion}}</div></el-col>
            </el-row>
          </div>
            </div>
          </div>
        </SpaceLayout>
      </div>
      <!-- 通道合约信息 -->
      <div>
        <div class="title">
          <div class="left">
            <img :src="infoIcon" class="infoIcon" />
            <span class="infotext detail">合约信息</span>
          </div>
          <!-- v-show="chainCodes.length > 4"  -->
          <div class="right" >
            <div class="moreWrap" v-show="chainCodes.length > 4" @click="goChainCodes">
              <span class="more">更多
                <i class="el-icon-arrow-right"></i>
              </span>
            </div>
          </div>
        </div>
        <SpaceLayout top="20" paddingX="0" paddingY="0">
          <div slot="padding">
            <div class="table-wrapper">
          <el-row class="nav-box">
            <el-col :span="4"><div class="">合约名称</div></el-col>
            <el-col :span="5"><div class="">版本</div></el-col>
            <el-col :span="5"><div class="">背书组织</div></el-col>
            <el-col :span="5"><div class="">背书策略</div></el-col>
            <el-col :span="5"><div class="">状态</div></el-col>
          </el-row>
          <div class="none" v-if="showChainCodes.length == 0">
            <i class="el-icon-loading" v-if="showChainCodesText == '数据请求中...'"></i>
            <!-- <svg-icon icon-class="table-empty" v-else/> -->
            {{showChainCodesText}}
          </div>
          <div class="nan-item" v-for="(item,chainIndex) in showChainCodes" :key="chainIndex">
          <el-row  class="nav-box">
              <el-col :span="4"><div class=""><span>{{item.Name}}</span></div></el-col>
              <el-col :span="5"><div class=""><span>{{item.Version}}</span></div></el-col>
              <el-col :span="5">
                <div class="">
                  <div v-for="(citem, cindex) in item.Endorse" :key="cindex">
                    <div v-for="(orgs, orgindex) in citem.Orgs" :key="orgindex">
                      <span>
                        {{ orgs.Name }}
                      </span>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :span="5">
                <div class="">
                  <div v-for="(citem, ccindex) in item.Endorse" :key="ccindex">
                    <span v-if="citem.Rule == 'all'"> 全部同意 </span>
                    <span v-if="citem.Rule == 'part'">
                      {{ "部分同意 " + citem.AgreeCount + "个" }}
                    </span>
                  </div>
                </div>
              </el-col>
             <el-col :span="5">
               <div v-if="item.Status == 'unInstalled'" class="tag red">待安装</div>
               <div v-if="item.Status == 'inited'" class="tag green">可调用</div>
               <div v-if="item.Status == 'installed'" class="tag blue">待初始化</div>
             </el-col>
            </el-row>
          </div>
        </div>
          </div>
        </SpaceLayout>
      </div>
      <!-- 通道区块信息 -->
      <div>
        <div class="title">
          <div class="left">
            <img :src="infoIcon" class="infoIcon" />
            <span class="infotext detail">区块信息</span>
          </div>
          <div class="right" v-show="blockList.length > 4" @click="goBlockList">
            <div class="moreWrap">
              <span class="more">更多
                <i class="el-icon-arrow-right"></i>
              </span>
            </div>
          </div>
        </div>
        <SpaceLayout top="20" paddingX="0" paddingY="0">
          <div slot="padding">
            <div class="table-wrapper">
          <el-row class="nav-box">
            <el-col :span="3"><div class="">区块号</div></el-col>
            <el-col :span="3"><div class="">交易数量</div></el-col>
            <el-col :span="3"><div class="">区块高度</div></el-col>
            <el-col :span="5"><div class="">生成时间</div></el-col>
           <!-- <el-col :span="3"><div class="">打包时间（ms）</div></el-col>-->
            <el-col :span="5"><div class="">区块Hash</div></el-col>
            <el-col :span="5"><div class="">前区块Hash</div></el-col>
          </el-row>
          <div class="none" v-if="showBlockList.length == 0">
             <i class="el-icon-loading" v-if="showBlockListText == '数据请求中...'"></i>
            <!-- <svg-icon icon-class="table-empty" v-else/> -->
            {{showBlockListText}}
          </div>
          <div class="nan-item" v-for="item in showBlockList" :key="item.BlockId">
          <el-row  class="nav-box">
              <el-col :span="3"><div class=""><span>{{item.BlockId}}</span></div></el-col>
              <el-col :span="3"><div class=""><span>{{item.TxCount}}</span></div></el-col>
              <el-col :span="3"><div class=""><span>{{BlockHeight}}</span></div></el-col>
              <el-col :span="5"><div class=""><span>{{dateFormat('YYYY-mm-dd HH:MM:SS',item.TimeStamp)}}</span></div></el-col>
             <!-- <el-col :span="3"><div class=""><span>{{item.ElapseTime}}</span></div></el-col>-->
              <el-col :span="5">
                <div class="">
                  <span class="">
                    <el-popover trigger="hover" placement="top">
                      <p>{{ item.BlockHash }}</p>
                      <div slot="reference" class="name-wrapper">
                        <span class="text" slot="reference">{{item.BlockHash}}</span>
                      </div>
                    </el-popover>
                  </span>
                </div>
              </el-col>
              <el-col :span="5">
                <div class="">
                  <span class="">
                    <el-popover trigger="hover" placement="top">
                      <p>{{ item.PrevHash }}</p>
                      <div slot="reference" class="name-wrapper">
                         <span class="text" slot="reference">{{item.PrevHash}}</span>
                      </div>
                    </el-popover>
                  </span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
          </div>
        </SpaceLayout>
      </div>
      <!-- 通道交易信息 -->
      <div>
        <div class="title">
          <div class="left">
            <img :src="infoIcon" class="infoIcon" />
            <span class="infotext detail">交易信息</span>
          </div>
          <div class="right" v-if="transList.length > 4" @click="goTransList">
            <div class="moreWrap">
              <span class="more">更多
                <i class="el-icon-arrow-right"></i>
              </span>
              <!--<img :src="moreIcon" class="moreIcon" />-->
            </div>
          </div>
        </div>
        <SpaceLayout top="20" paddingX="0" paddingY="0">
          <div slot="padding">
            <div class="table-wrapper">
          <el-row class="nav-box">
            <el-col :span="3"><div class="">交易号</div></el-col>
            <el-col :span="2"><div class="">所属区块</div></el-col>
            <el-col :span="5"><div class="">交易类型</div></el-col>
            <el-col :span="4"><div class="">交易时间</div></el-col>
            <el-col :span="3"><div class="">合约名称</div></el-col>
            <el-col :span="4"><div class="">交易参数</div></el-col>
            <el-col :span="3"><div class="">交易状态</div></el-col>
          </el-row>
          <div class="none" v-if="showTransList.length == 0">
           <i class="el-icon-loading" v-if="showTransListText == '数据请求中...'"></i>
            <!-- <svg-icon icon-class="table-empty" v-else/> -->
            {{showTransListText}}
          </div>
          <div class="nan-item" v-for="(item,tranIndex) in showTransList" :key="tranIndex">
            <el-row  class="nav-box">
                <el-col :span="3">
                  <div class="">
                    <span class="">
                      <el-popover trigger="hover" placement="top">
                        <p>{{ item.TxId }}</p>
                        <div slot="reference" class="name-wrapper">
                          <span class="text textLeft" slot="reference">{{item.TxId}}</span>
                        </div>
                      </el-popover>
                    </span>
                  </div>
                </el-col>
                <el-col :span="2"><div class=""><span>{{item.AttachBlockId}}</span></div></el-col>
                <el-col :span="5"><div class=""><span>{{item.Type}}</span></div></el-col>
                <el-col :span="4"><div class=""><span>{{dateFormat('YYYY-mm-dd HH:MM:SS',item.TimeStamp)}}</span></div></el-col>
                <el-col :span="3"><div class=""><span class="">{{item.ChainCodeName}}</span></div> </el-col>
                <el-col :span="4">
                  <div class="">
                    <span class="baseShow" v-html="getArgs(item.Args)">
                      <!-- {{ getArgs(item.Args)}} -->
                    </span>
                  </div>
                </el-col>
              <el-col :span="3">
                <div v-if="item.Status&&item.Status.length<=14">{{item.Status}}</div>
                <el-popover trigger="hover" placement="top" v-else>
                  <p>{{item.Status}}</p>
                  <div slot="reference" class="name-wrapper">
                    <div class="statuspopover">{{item.Status}}</div>
                  </div>
                </el-popover>
              </el-col>
            </el-row>
          </div>
        </div>
          </div>
        </SpaceLayout>
      </div>
    </div>
    <!-- 添加成员弹框 -->
    <transition name="fade">
      <div class="alertBox" v-if="isShowOrgs">
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">添加成员
              <i class="el-icon-cursor el-icon-close fr" @click="deselect"></i>

            </div>
          </div>
          <div class="alert_box">
            <el-form
              ref="orgsForm"
              :model="orgsForm"
              :rules="orgsRules"
              label-width=""
            >
              <div class="selectBox">
                <div class="evertModule">
                  <el-form-item label="组织节点：" prop="Orgs">
                    <el-table
                      ref="multipleTable1"
                      :data="addOrgsList"
                      tooltip-effect="dark"
                      max-height="210"
                      :header-cell-style="headClass"
                      :cell-style="rowClass"
                      :row-class-name="tableRowClassName"
                      @selection-change="handleSelectionAddChange"
                    >
                      <el-table-column type="selection" width="40">
                      </el-table-column>
                      <el-table-column label="组织名称" prop="Name">
                      </el-table-column>
                      <el-table-column prop="Peer" label="节点名称">
                        <template slot-scope="scope">
                          {{ getName(scope.row.Peer) }}
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <div class="btn-row">
            <Button type="primary" class="sure-btn" @click="addOrgs()">添加</Button>
            <Button type="text" class="border-btn" @click="deselect">取消</Button>
          </div>
        </div>
      </div>
    </transition>
    <!-- token信息弹窗 -->
    <transition name="fade">
      <div class="alertBox confirmBox" v-if="isShowToken">
        <div class="addTissue">
          <div class="delText">
            <!-- <i class="el-icon-info"></i> -->
            <img src="../../../../assets/image/el-info.png" class="iconImage">
            <span class="noUn">Token ID</span>
            <div class="errmessage">TokenID为{{refreshToken}}</div>
            <div class="errmessage next">请妥善保存Token ID。Token ID一旦丢失，需要重新生成</div>
          </div>
          <div class="confirmBottom">
            <!-- <el-button class="sure-btn blue" @click="clickIsKnow">知道了</el-button> -->
            <Button type="primary" class="sure-btn" @click="clickIsKnow">知道了</Button>
          </div>
        </div>
      </div>
    </transition>
    <div v-if="loading" class="BoxLoading" v-loading="loading" element-loading-text='添加中'></div>
    <!-- <router-view></router-view> -->
    <Member v-if="isShowObj.isShowMember" :detail="detail" @getisShowMember="getisShowMember" @getIsShowDetial="getIsShowDetial"></Member>
    <ChainCodes v-if="isShowObj.isShowChainCodes" :detail="detail" @getisShowChainCodes="getisShowChainCodes" @getIsShowDetial="getIsShowDetial"></ChainCodes>
    <BlockList v-if="isShowObj.isShowBlockList" :detail="detail" @getisShowBlockList="getisShowBlockList" @getIsShowDetial="getIsShowDetial"></BlockList>
    <TransList v-if="isShowObj.isShowTransList" :detail="detail" @getisShowTransList="getisShowTransList" @getIsShowDetial="getIsShowDetial"></TransList>
    <!-- <countDown v-if="isShowIcon" :state="countState" :countTime="countTime" :text="countText" @getCountDown="getCountDown"></countDown> -->
  </div>
</template>

<script>
import
   {
    getChannelStatistics,getChannelPeerList,
    getChannelBlockListPage,getChannelBlockDetail,
    getChannelTransactionListPage,getChannelTransactionDetail,
    getChaincodeEndorseRule,getUnjoinedPeerListOfChannel,joinPeerToChannel,
    getApiUserList,refreshApiUserId
    }
 from '@/api/baascore/channelMgr'
 import {getChannelChaincodeList} from '@/api/baascore/agreement';
import SpaceLayout from '@/components/SpaceLayout'
import Member from './member'
import ChainCodes from './chainCodes'
import BlockList from './blockList'
import TransList from './transList'
import { diffNumber } from "@/utils/validate";
export default {
  props: {
    detail: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  components: {
    Member,
    ChainCodes,
    BlockList,
    TransList,
    SpaceLayout
  },
  data() {
    return {
      countState: "",
      countTime: 2,
      countText: "",
      isShowIcon: false,
      showTokenText: "数据请求中...",
      showChainCodesText: "数据请求中...",
      showMemberArrayText: "数据请求中...",
      showBlockListText: "数据请求中...",
      showTransListText: "数据请求中...",
      closeImg: require("@/assets/image/close.png"),
      infoIcon: require("@/assets/chainManage_images/overview/infoIcon.png"),
      moreIcon: require("@/assets/chainManage_images/overview/moreIcon.png"),
      arrowIcon: require("@/assets/chainManage_images/overview/arrow.png"),
      imageList: [
        {
          liImgUrl: require("@/assets/chainManage_images/overview/libg1.png"),
          iconImgUrl: require("@/assets/chainManage_images/overview/icon1.png"),
          number: "",
          name: "节点数量",
        },
        {
          liImgUrl: require("@/assets/chainManage_images/overview/libg3.png"),
          iconImgUrl: require("@/assets/chainManage_images/overview/icon3.png"),
          number: "",
          name: "合约数量",
        },
        {
          liImgUrl: require("@/assets/chainManage_images/overview/libg4.png"),
          iconImgUrl: require("@/assets/chainManage_images/overview/icon4.png"),
          number: "",
          name: "区块高度",
        },
        {
          liImgUrl: require("@/assets/chainManage_images/overview/libg5.png"),
          iconImgUrl: require("@/assets/chainManage_images/overview/icon5.png"),
          number: "",
          name: "交易数量",
        },
      ],
      channelObject: {}, //
      showToken: [], //token信息
      memberArray: [], //成员信息
      showMemberArray: [], //展示的成员信息
      chainCodes: [], // 合约
      showChainCodes: [],
      blockList: [], // 区块列表
      BlockHeight: "",
      showBlockList: [],
      transList: [], // 交易
      showTransList: [],
      addOrgsList: [],
      orgsForm: {
        Orgs: [],
      },
      orgsRules: {
        Orgs: [{ required: true, message: "请添加组织节点", trigger: "blur" }],
      },
      selectRow: [],
      isShowOrgs: false,
      isShowObj:{
        isShowMember: false,
        isShowChainCodes: false,
        isShowBlockList: false,
        isShowTransList: false,
      },
      loading: false,
      refreshToken:'',
      isShowToken:false,
      // detail:{
      //   ChannelName: "",
      //   ServiceId: "",
      // },
     // isShowDetial:false,
    };
  },
  computed: {
    getChainCodeStatus(status) {
      return function (status) {
        switch (status) {
          case "unInstalled":
            return "已发布待安装";
          case "installed":
            return "已安装待初始化";
          case "inited":
            return "已初始化";
        }
      };
    },
    dateFormat(fmt, date) {
      return function (fmt, date) {
        date = new Date(date);
        let ret;
        const opt = {
          "Y+": date.getFullYear().toString(), // 年
          "m+": (date.getMonth() + 1).toString(), // 月
          "d+": date.getDate().toString(), // 日
          "H+": date.getHours().toString(), // 时
          "M+": date.getMinutes().toString(), // 分
          "S+": date.getSeconds().toString(), // 秒
          // 有其他格式化字符需求可以继续添加，必须转化成字符串
        };
        for (let k in opt) {
          ret = new RegExp("(" + k + ")").exec(fmt);
          if (ret) {
            fmt = fmt.replace(
              ret[1],
              ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
            );
          }
        }
        return fmt;
      };
    },
    getName(name) {
      return function (name) {
        if (name.indexOf("-") != -1) {
          var strList = name.split("-");
          name = strList[0] + "-" + strList[1];
          return name;
        } else {
          return name;
        }
      };
    },
    getArgs(args) {
      return function (args) {
        let arr = []
        if(args) {
          args.forEach(item =>{
            item = item.replaceAll(" ",'&nbsp;')
            arr.push(item)
          })
        }else {
          arr = args
        }
        return arr
      }
    }
  },
  watch: {
    isShowObj:{
      handler(newvalue) {
        let isRe = false
        for (let key in newvalue) {
            let element = newvalue[key];
            if(element) {
              isRe = true
            }
        }
        if(!isRe) {
          this.getChannelPeerList();
          this.getChannelChaincodeList();
          this.getChannelBlockListPage();
          this.getChannelTransactionListPage();
          this.getTokenList();
        }
      },
      deep:true
    }
    // chainItem: {
    //   handler(newvalue, oldvalue) {
    //     if (newvalue.Id != oldvalue.Id) {
    //       this.$router.push({
    //         path:'/chainManage/channelMgr'
    //       })
    //     }
    //   },
    //   deep: true,
    // },
    // isShowBackDetail: {
    //   handler(newvalue, oldvalue) {
    //     if(newvalue == 1) {
    //       this.$emit("getIsShowDetial", false);
    //       this.$store.dispatch("getIsShowBackDetail", 0)
    //     }
    //     if(newvalue == 2) {
    //       this.isShowMember = this.isShowChainCodes = this.isShowBlockList = this.isShowTransList = false
    //       this.$store.dispatch("getIsShowBackDetail", 0)
    //     }
    //   },
    //   deep: true,
    // },
  },
  mounted() {
    //this.detail = JSON.parse(sessionStorage.getItem('detail'))
    if(this.detail) {
      //if(this.$route.path == '/chainManage/channelMgr/detail'){
       // this.$route.meta.title = this.detail.ChannelName + '通道详情'
      //  this.isShowDetial = false
        this.getChannelPeerList();
        this.getChannelChaincodeList();
        this.getChannelBlockListPage();
        this.getChannelTransactionListPage();
        this.getTokenList();
      //}else {
      //  this.isShowDetial = true
      //}
      document.body.scrollTop = 0;
      document.documentElement.scrollTop = 0;
    }
    // else {
    //   this.$router.push({
    //     path:'/chainManage/channelMgr'
    //   })
    // }

  },
  methods: {
    goback() {
      this.$emit("getIsShowDetial", false);
    },
    clickIsKnow() {
      this.isShowToken = false
    },
    refreshApiUserId(id) {
      var params = {
        id
      }
      refreshApiUserId(params).then(res =>{
        if(res.code == 200) {
          this.refreshToken = res.data.token
          this.isShowToken = true
        }else {
          this.$message.error(res.message)
        }
      })
    },
    getCountDown(type) {
      this.isShowIcon = false;
    },
    goMember() {
      // var list = []
      // this.breadcrumbList.push({
      //   title: '通道成员信息'
      // })
      // list = this.breadcrumbList
      // this.$store.dispatch("getBreadcrumbList", list).then(() =>{
      //   this.isShowMember = true;
      // })
      // this.$router.push({
      //   path:'/chainManage/channelMgr/detail/channelPeer',
      // })
      this.isShowObj.isShowMember = true;
    },
    goChainCodes() {
      // var list = []
      // this.breadcrumbList.push({
      //   title: '通道合约信息'
      // })
      // list = this.breadcrumbList
      // this.$store.dispatch("getBreadcrumbList", list).then(() =>{
      //   this.isShowChainCodes = true;
      // })
      // this.$router.push({
      //   path:'/chainManage/channelMgr/detail/channelChaincode',
      // })
      this.isShowObj.isShowChainCodes = true;
    },
    goBlockList() {
      // var list = []
      // this.breadcrumbList.push({
      //   title: '通道区块信息'
      // })
      // list = this.breadcrumbList
      // this.$store.dispatch("getBreadcrumbList", list).then(() =>{
      //   this.isShowBlockList = true;
      // })
      // this.$router.push({
      //   path:'/chainManage/channelMgr/detail/channelBlockList',
      // })
      this.isShowObj.isShowBlockList = true;
    },
    goTransList() {
      // var list = []
      // this.breadcrumbList.push({
      //   title: '通道交易信息'
      // })
      // list = this.breadcrumbList
      // this.$store.dispatch("getBreadcrumbList", list).then(() =>{
      //   this.isShowTransList = true;
      // })
      // this.$router.push({
      //   path:'/chainManage/channelMgr/detail/channelTransactionList',
      // })
      this.isShowObj.isShowTransList = true;
    },
    getIsShowDetial(type) {
      this.$emit("getIsShowDetial", type);
    },
    getisShowMember(type) {
      this.isShowObj.isShowMember = type;
    },
    getisShowChainCodes(type) {
      this.isShowObj.isShowChainCodes = type;
    },
    getisShowBlockList(type) {
      this.isShowObj.isShowBlockList = type;
    },
    getisShowTransList(type) {
      this.isShowObj.isShowTransList = type;
    },
    goChannelMgr() {
      this.$emit("getIsShowDetial", false);
    },
    deselect() {
      this.isShowOrgs = false;
    },
    //表格数据居中显示
    headClass() {
      return "text-align:center;background:#F2F7FA;padding:4px 0 ;";
    },
    rowClass() {
      return "text-align:left";
    },
    tableRowClassName({ row, rowIndex }) {
      let color = "";
      this.selectRow.forEach((r, i) => {
        if (r.Peer === row.Peer) {
          color = "warning-row";
        }
      });
      return color;
    },
    //添加组织
    handleSelectionAddChange(val) {
      this.selectRow = [];
      this.selectRow = val;
      var list = val;
      var orgs = [];
      var tempArr = [];
      for (let i = 0; i < list.length; i++) {
        if (tempArr.indexOf(list[i].Name) === -1) {
          orgs.push({
            Name: list[i].Name,
            Peers: [list[i].Peer],
          });
          tempArr.push(list[i].Name);
        } else {
          for (let j = 0; j < orgs.length; j++) {
            if (orgs[j].Name == list[i].Name) {
              orgs[j].Peers.push(list[i].Peer);
              break;
            }
          }
        }
      }
      this.orgsForm.Orgs = orgs;
    },
    //添加成员
    getOrgs() {
      var ServiceId = this.detail.ServiceId;
      var ChannelName = this.detail.ChannelName;
      var params = {
        ServiceId,
        ChannelName,
      };
      getUnjoinedPeerListOfChannel(params).then((res) => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if (res.data && res.data.Orgs) {
            var data = res.data.Orgs;
            var arr = [];
            var obj = {};
            var isHavePeers = false;
            data.forEach((item) => {
              if (item.Name && item.Peers && item.Peers.length > 0) {
                item.Peers.forEach((citem, cindex) => {
                  obj = {
                    Name: item.Name,
                    Peer: item.Peers[cindex],
                  };
                  arr.push(obj);
                  isHavePeers = true;
                });
              }
            });
            if (isHavePeers) {
              this.addOrgsList = arr;
              this.isShowOrgs = true;
            } else {
              this.isShowOrgs = false;
              // this.isShowIcon = true
              // this.countState = 'full'
              // this.countText = '通道成员节点数量已满,无法添加新成员！'
              this.$message.warning("通道成员节点数量已满,无法添加新成员");
            }
          }
        } else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '添加节点失败，请重试！'
          this.$message.error("添加节点失败，请重试！");
        }
      });
    },
    addOrgs() {
      this.$refs.orgsForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          var ServiceId = this.detail.ServiceId;
          var Orgs = this.orgsForm.Orgs;
          var ChannelName = this.detail.ChannelName;
          var data = {
            // msgType:'joinPeerToChannel',
            //params:{
            ChannelName,
            Orgs,
            ServiceId,
            // }
          };
          joinPeerToChannel(data)
            .then((res) => {
              if (res.code == 200) {
                // this.isShowIcon = true
                // this.countState = 'success'
                // this.countText = '添加成功！'
                this.$message.success("添加成功！");
                this.getChannelPeerList();
                this.getChannelChaincodeList();
                this.getChannelBlockListPage();
                this.getChannelTransactionListPage();
                this.getTokenList();
                this.isShowOrgs = false;
                this.loading = false;
              }else if(res.code == 4230) {
                this.isShowOrgs = true;
                this.loading = false;
                this.$message.error("添加成员操作失败，请稍后再试！");
              }
              else {
                this.isShowOrgs = true;
                this.loading = false;
                // this.isShowIcon = true
                // this.countState = 'error'
                // this.countText = '添加成员失败,请重新添加！'
                this.$message.error("添加成员失败,请重新添加!");
              }
            })
            .catch((err) => {
              this.isShowOrgs = true;
              this.loading = false;
              //this.$message.error("网络异常,请检查网络");
            });
        } else {
          return false;
        }
      });
    },
    //获取token
    getTokenList() {
      var ServiceId = this.detail.ServiceId;
      // let curChain = JSON.parse(sessionStorage.getItem("chainItem"));
      // var chainID = curChain.Id;
      var channelName = this.detail.ChannelName;
      var params = {
        chainID:ServiceId,
        channelName
      };
      getApiUserList(params).then((res) => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          this.showToken=res.data
          if (this.showToken.length===0){
            this.showTokenText='暂无数据'
          }
        } else {
          this.showTokenText='暂无数据'
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      });
    },
    //获取某个通道的统计信息（节点、合约、块、交易）
    getChannelStatistics() {
      var ServiceId = this.detail.ServiceId;
      var ChannelName = this.detail.ChannelName;
      var params = {
        ServiceId,
        ChannelName,
      };
      getChannelStatistics(params).then((res) => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          var data = res.data;
          this.imageList.forEach((item) => {
            switch (item.name) {
              case "节点数量":
                item.number = data.Peer;
                break;
              case "合约数量":
                item.number = diffNumber(this.chainCodes.length);
                break;
              case "区块高度":
                item.number = diffNumber(data.Block);
                break;
              case "交易数量":
                item.number = diffNumber(data.Trans);
                break;
            }
          });
        } else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      });
    },
    //通道里的节点列表
    getChannelPeerList() {
      var ServiceId = this.detail.ServiceId;
      var ChannelName = this.detail.ChannelName;
      var params = {
        ServiceId,
        ChannelName,
      };
      getChannelPeerList(params).then((res) => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if (res.data && res.data.Orgs) {
            this.channelObject = res.data;
            var data = res.data.Orgs;
            var arr = [];
            var obj = {};
            data.forEach((item) => {
              if (item.Name && item.Peers && item.Peers.length > 0) {
                item.Peers.forEach((citem, cindex) => {
                  obj = {
                    Name: item.Name,
                    Peer: item.Peers[cindex],
                  };
                  arr.push(obj);
                });
              }
            });
            this.memberArray = arr;
            this.showMemberArray = this.memberArray.slice(0, 4);
            if (this.showMemberArray.length == 0) {
              this.showMemberArrayText = "暂无数据";
            }
          } else {
            this.showMemberArrayText = "暂无数据";
          }
        }
        if (res.code != 200) {
          this.showMemberArrayText = "暂无数据";
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      });
    },
    //合约列表
    getChannelChaincodeList() {
      this.chainCodes = [];
      var ServiceId = this.detail.ServiceId;
      var ChannelName = this.detail.ChannelName;
      var params = {
        ServiceId,
        ChannelName,
      };
      getChannelChaincodeList(params).then((res) => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if (res.data && res.data.ChainCodes) {
            var data = res.data.ChainCodes;
            data.forEach((item) => {
              if (item.Status == "inited") {
                this.chainCodes.push(item);
              }
            });
            this.showChainCodes = this.chainCodes.slice(0, 4);
            if (this.showChainCodes.length == 0) {
              this.showChainCodesText = "暂无数据";
            }
            if (this.showChainCodes.length > 0) {
              this.showChainCodes.forEach((item, index) => {
                var ServiceId = this.detail.ServiceId;
                var ChannelName = this.detail.ChannelName;
                var params = {
                  ServiceId,
                  ChannelName,
                  ChainCodeName: item.Name,
                  Version: item.Version,
                };
                getChaincodeEndorseRule(params).then((res) => {
                  if (res.code == 200) {
                    // this.isShowIcon = true
                    // this.countState = 'success'
                    // this.countText = '请求成功！'
                    this.$set(this.showChainCodes[index], "Endorse", res.data);
                  } else {
                    // this.isShowIcon = true
                    // this.countState = 'error'
                    // this.countText = '数据获取失败，请重新加载！'
                    this.$message.error("数据获取失败，请重新加载！");
                  }
                });
              });
            }
          } else {
            this.showChainCodesText = "暂无数据";
          }
        }
        if (res.code != 200) {
          this.showChainCodesText = "暂无数据";
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
        this.getChannelStatistics();
      });
    },
    //区块分页列表
    getChannelBlockListPage() {
      var ServiceId = this.detail.ServiceId;
      var ChannelName = this.detail.ChannelName;
      var params = {
        ServiceId,
        ChannelName,
        // PageSize:10,
        // Index:1,
      };
      getChannelBlockListPage(params).then((res) => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if (res.data && res.data.Blocks) {
            this.blockList = res.data.Blocks;
            this.BlockHeight = res.data.Count;
            //this.blockList = this.blockList.reverse();
            this.showBlockList = this.blockList.slice(0, 4);
            if (this.showBlockList.length == 0) {
              this.showBlockListText = "暂无数据";
            }
            this.showBlockList.forEach((item, index) => {
              if (item.BlockId || item.BlockId === 0) {
                var object = {
                  ServiceId,
                  ChannelName,
                  BlockId: item.BlockId,
                };
                getChannelBlockDetail(object).then((res) => {
                  if (res.code == 200) {
                    this.$set(
                      this.showBlockList[index],
                      "PrevHash",
                      res.data.PrevHash
                    );
                  } else {
                    // this.isShowIcon = true
                    // this.countState = 'error'
                    // this.countText = '数据获取失败，请重新加载！'
                    this.$message.error("数据获取失败，请重新加载！");
                  }
                });
              }
            });
          } else {
            this.showBlockListText = "暂无数据";
          }
        }
        if (res.code != 200) {
          this.showBlockListText = "暂无数据";
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      });
    },
    //交易信息
    getChannelTransactionListPage() {
      var ServiceId = this.detail.ServiceId;
      var ChannelName = this.detail.ChannelName;
      var params = {
        ServiceId,
        ChannelName,
        // PageSize:10,
        // Index:1,
      };
      getChannelTransactionListPage(params).then((res) => {
        if (res.code == 200) {
          if (res.data && res.data.Transactions) {
            this.transList = res.data.Transactions;
            //this.transList = this.transList.reverse();
            this.showTransList = this.transList.slice(0, 4);
            if (this.showTransList.length == 0) {
              this.showTransListText = "暂无数据";
            }
            this.showTransList.forEach((item, index) => {
              if (item.TxId || item.TxId === 0) {
                var obj = {
                  ServiceId,
                  ChannelName,
                  TxId: item.TxId,
                };
                getChannelTransactionDetail(obj).then((res) => {
                  if (res.code == 200) {
                    // this.isShowIcon = true
                    // this.countState = 'success'
                    // this.countText = '请求成功！'
                    this.$set(this.showTransList[index], "Type", res.data.Type);
                    this.$set(
                      this.showTransList[index],
                      "Status",
                      res.data.Status
                    );
                    this.$set(
                      this.showTransList[index],
                      "ChainCodeName",
                      res.data.ChainCodeName
                    );
                    this.$set(this.showTransList[index], "Args", res.data.Args);
                  } else {
                    // this.isShowIcon = true
                    // this.countState = 'error'
                    // this.countText = '数据获取失败，请重新加载！'
                    this.$message.error("数据获取失败，请重新加载！");
                  }
                });
              }
            });
          } else {
            this.showTransListText = "暂无数据";
          }
        }
        if (res.code != 200) {
          this.showTransListText = "暂无数据";
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/less" lang="less" scoped>
@import "../../../../styles/common/modal.less";
.page {
  width: 100%;
  height: 100%;
  //margin-top: -20px;
  .content {
    //padding-bottom: 50px;
    .title {
      margin-top: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .infoIcon {
        width: 3px;
        height: 14px;
        margin-right:5px;
        vertical-align: middle;
      }
      .infotext {
        // font-size: 22px;
        font-size: 14px;
        color: #333333;
        vertical-align: middle;
        cursor: pointer;
        &.detail {
          // font-size: 22px;
          font-size: 14px;
          color: #333333;
         // margin-left: 8px;
        }
      }
      .icon {
        width: 22px;
        height: 22px;
        margin: 0 24px;
      }
      .name {
        // font-size: 20px;
        font-size: 14px;
        font-weight: bold;
        color: #333333;
        cursor: default;
      }
      .left {
        display: flex;
        align-items: center;
      }
      .right {
        display: flex;
        align-items: center;
        .moreWrap {
          cursor: pointer;
          color: #555;
          width: 84px;
          height: 30px;
          background: #fff;
          text-align: center;
          line-height: 30px;
          color:#337DFF;
        }
        .add {
          width: 120px;
          height: 46px;
          // font-size: 18px;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
          text-align: center;
          line-height: 46px;
          cursor: pointer;
          background: #337dff;
          border-radius: 4px;
          margin-right: 32px;
        }
        .more {
          // font-size: 17px;
          font-size: 14px;
          vertical-align: 2px;
          color: #337DFF;
        }
        .moreIcon {
          width: 22px;
          height: 22px;
          // margin-left: 8px;
          vertical-align: middle;
        }
      }
    }
    .imglist {
      padding: 0;
      list-style: none;
      //margin-top: 40px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      li {
        width: 23%;
        height: 120px;
        position: relative;
        .liImage {
          width: 100%;
          height: 120px;
        }
        .iconImage {
          width: 70px;
          height: 70px;
          position: absolute;
          top: 25px;
          left: 20px;
        }
        .number {
          position: absolute;
          top: 25px;
          right: 32px;
          // font-size: 58px;
          font-size: 32px;
          color: #ffffff;
        }
        .name {
          position: absolute;
          bottom: 25px;
          right: 32px;
          // font-size: 20px;
          font-size: 14px;
          color: #ffffff;
        }
      }
    }
    .none {
      text-align: center;
      height: 56px;
      color: #666;
      background: #fff;
      line-height: 56px;
      // font-size: 18px;
      font-size: 14px;
    }
    .nav-box {
      color: #999999;
      // font-size: 18px;
      font-size: 14px;
      box-sizing: border-box;
      /*margin-top:20px;*/
    }
    .nav-box /deep/ .el-col {
      text-align: center;
      color: #999;
      // font-size: 18px;
      font-size: 14px;
    //  line-height: 36px;
    }
    .nav-box .el-col div span{
      /*cursor: default;*/
      word-break: break-all;
    }
    .nan-item .nav-box:hover {
      -moz-box-shadow: 2px 2px 6px #bbd1e6, -2px -2px 6px #bbd1e6;
      -webkit-box-shadow: 2px 2px 6px #bbd1e6, -2px -2px 6px #bbd1e6;
      box-shadow: 2px 2px 6px #bbd1e6, -2px -2px 6px #bbd1e6;
    }
    .nan-item .nav-box {
      // font-size: 16px;
      font-size: 14px;
      box-sizing: border-box;
      background: #fff;
      display: flex;
      align-items: center;
    }
    .nan-item .nav-box /deep/ .el-col {
      color: #666666;
    }
    .nan-item .nav-box .el-col div span.call {
      display: inline-block;
      width: 102px;
      height: 36px;
      border-radius: 4px;
      text-align: center;
      line-height: 36px;
      background: #00ada2;
      // font-size: 16px;
      font-size: 14px;
      color: #ffffff;
    }
    .nan-item .nav-box .el-col div span.text {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      /*padding: 0 40px;*/
    }
    .nan-item .nav-box .el-col div span.baseShow {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 0;
    }
    .nan-item .nav-box .el-col div span.baseShow:hover {
      word-wrap: break-word;
      word-break: normal;
      white-space: normal;
    }
    .nan-item .nav-box .el-col div span.textLeft {
      /*padding-left: 36px;*/
    }
  }

  .BoxLoading {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10000;
  }
  .BoxLoading /deep/ .el-loading-mask {
    background: rgba(0, 0, 0, 0.2) !important;
  }
  .BoxLoading /deep/ .el-loading-spinner .circular {
    width: 60px !important;
    height: 60px !important;
  }
  .BoxLoading /deep/ .el-loading-spinner .path {
    stroke: #fff !important;
  }
  .BoxLoading /deep/ .el-loading-spinner .el-loading-text {
    color: #fff !important;
    // font-size: 24px;
    font-size: 14px;
    font-weight: bold;
  }
  .bottonList{
    display: flex;
    align-items: center;
  }
}
</style>
