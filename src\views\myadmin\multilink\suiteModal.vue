<template>
    <Modal
        v-model="visible"
        title="开发套件配置"
        @on-cancel="$refs['form'].resetFields()"
        :mask-closable="false"
        >
        <Form ref="form" :model="form" :rules="rules" :label-width="120" class="hyperion-form">
            <FormItem label="服务IP：" prop="IPAddress">
                <Input v-model="form.IPAddress" placeholder="请输入服务IP" />
            </FormItem>
            <FormItem label="服务端口：" prop="servicePort">
                <Input v-model="form.servicePort" placeholder="请输入服务端口" />
            </FormItem>
        </Form>
        <span slot="footer">
            <Button type="text" @click="cancel">取 消</Button>
            <Button type="primary" @click="submitData">提 交</Button>
        </span>
        <Modal v-model="modal" width="360" :closable="false">
            <div class="success-wrap" v-if="isSuccess">
                确定退出吗？编辑内容将不会保留
            </div>
            <div class="error-wrap" v-else>
                <Icon type="ios-close-circle" />
                <span>提交失败</span>
                <p>{{errMsg}}</p>
            </div>
            <div slot="footer">
                <Button  size="small" v-if="isSuccess" @click="modal=false">取消</Button>
                <Button type="primary" size="small" @click="childModelOk(isSuccess)">确认</Button>
            </div>
        </Modal>
    </Modal>
</template>
<script>
// import { hyperioncConfig, hyperioncConfigQuery } from '@/api/data'
export default {
  props: {
    chainId: String
  },
  data () {
    return {
      visible: false,
      modal: false,
      errMsg: '',
      isSuccess: false, // 子弹框样式 false=>确定退出吗？编辑内容将不会保留 true=>提交失败
      form: {
        servicePort: '',
        IPAddress: ''
      },
      rules: {
        servicePort: [
          { required: true, message: '请输入服务端口', trigger: 'blur' }
        ],
        IPAddress: [
          { required: true, message: '请输入服务IP', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /**
     * 提交数据
     */
    submitData () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // let params = {
          //   ...this.form,
          //   chainId: this.chainId
          // }
          // hyperioncConfig(params).then(res => {
          //   if (res.code === '00000') {
          //     this.msgInfo('success', res.message, true)
          //     this.visible = false
          //   } else {
          //     this.errMsg = res.message
          //     this.isSuccess = false
          //     this.modal = true
          //   }
          // }).catch((err) => {
          //   this.errMsg = err.message
          //   this.isSuccess = false
          //   this.modal = true
          //   // this.msgInfo('error', err.message, true)
          // })
        }
      })
    },
    cancel () {
      console.log(111)
      // if (!this.form.hyperionName && !this.form.IPAddress) {
      //   this.$refs['form'].resetFields()
      //   this.visible = false
      // } else {
      //   this.isSuccess = true
      //   this.modal = true
      // }
    },
    childModelOk (isSuccess) {
      console.log(111)
      // if (isSuccess) {
      //   this.modal = false
      //   this.visible = false
      //   this.$refs['form'].resetFields()
      // } else {
      //   this.modal = false
      // }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    /**
     * 查询hyperion配置接口
     */
    getSuiteConfigQuery (chainId) {
      console.log(chainId)
      // hyperioncConfigQuery(chainId).then(res => {
      //   if (res.code === '00000') {
      this.visible = true
      //     this.form = res.data ? res.data : {}
      //   } else {
      //     if (res.code === 'A1401') {
      //       this.visible = true
      //     } else {
      //       this.msgInfo('error', res.message, true)
      //     }
      //   }
      // }).catch((err) => {
      //   this.msgInfo('error', err.message, true)
      // })
    }
  }
}
</script>

<style lang="less" scoped>
.hyperion-form{
    padding:10px 20px 0 10px
}
.success-wrap{
    padding: 30px 30px 40px 20px
}
.error-wrap{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 25px;
    &>i{
        font-size: 60px;
        color: red;
    }
    &>span{
        margin-top: 10px;
        font-size: 20px;
        font-weight: bold;
        color: #333;
    }
     &>p{
        margin-top: 5px;
        font-size: 14px;
        color: #999;
    }
}
</style>
