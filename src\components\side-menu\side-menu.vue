<template>
  <div class="side-menu-wrapper">
    <slot>
      <h1 v-if="!collapsed"><img :src="logoUrl" style="width: 156px;height: 35px;"><!-- <b v-show="!collapsed">CMBaaS</b> -->
      </h1>
      <h1 v-else class="img-h1"><img style="width:36px" :src="logoThnUrl"></h1>
    </slot>
    <Menu ref="menu" :active-name="$route.name" :open-names="openNames" v-show="!collapsed" width="auto" theme="dark" @on-select="handleSelect">
      <template v-for="item in list">
        <!-- v-if="item.children && item.children.length >= 1 && item.name !== 'dashboard' && item.name !== 'user_statistics'" -->
        <re-submenu v-if="item.children && item.children.length >= 1 && !singleList.includes(item.name)" :key="`menu_${item.name}`" :name="item.name" :parent="item">
        </re-submenu>
        <menu-item v-else-if="!item.hidden" :key="`menu_${item.name}`" :name="item.name">
          <div v-if="item.selfIcon" class="menu-img-container" style="margin-right:4px"> <img style="width:20px;vertical-align: -0.2em;" class="menu-img" :src="item.selfIcon" alt=""></div>
          <Icon v-else :type="item.icon" />
          {{ item.meta.title }}
        </menu-item>
      </template>
    </Menu>
    <div v-show="collapsed" class="drop-wrapper">
      <template v-for="item in list.filter(val => !val.hidden)">
        <re-dropdown @on-select="handleSelect" v-if="item.children" :show-title="false" icon-color="#fff" :key="`drop_${item.name}`" :parent="item"></re-dropdown>
        <Tooltip v-else transfer :content="item.title" placement="right" :key="`drop_${item.name}`">
          <span @click="handleClick(item.name)" class="drop-menu-span">
            <div v-if="item.selfIcon" class="menu-img-container"> <img class="menu-img" width="17px" :src="item.selfIcon" alt=""></div>
            <Icon v-else :type="item.icon" color="#fff" :size="20"></Icon>
          </span>
        </Tooltip>
      </template>
    </div>
  </div>
</template>

<script>
import ReSubmenu from './re-submenu.vue'
import ReDropdown from './re-dropdown.vue'
import { mapState } from 'vuex'
import { getOpenArrByName } from '@/lib/util'
export default {
  name: 'SideMenu',
  components: {
    ReSubmenu,
    ReDropdown
  },
  props: {
    collapsed: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState({
      routers: state => state.router.routers
    }),
    openNames () {
      return getOpenArrByName(this.$route.name, this.routers)
    }
  },
  watch: {
    openNames () {
      this.$nextTick(() => {
        this.$refs.menu.updateOpened()
      })
    }
  },
  data () {
    return {
      singleList: ['dashboard', 'menu_server_manage', 'about_index', 'ipfs_network','menu_view_manage'],
      // singleList: ['dashboard', 'application_center', 'about_index', 'ipfs_network'],
      logoUrl: '',
      logoThnUrl: ''
    }
  },
  methods: {
    handleSelect (name) {
      this.$emit('on-parent-select', name)
      this.$router.push({
        name
      })
    },
    handleClick (name) {
      // console.log(name)
    }
  },
  mounted () {
    const versionData = JSON.parse(sessionStorage.getItem('versionData')) || {};
    this.logoUrl = versionData.logoUrl || '/data/iconLogin.png';
    this.logoThnUrl = versionData.logoThnUrl || '/data/logo.png';


  }
}
</script>

<style lang="less" >
.ivu-menu,
.ivu-menu-dark,
.ivu-menu-vertical,
.ivu-menu-opened,
.ivu-menu-submenu-title {
  background-color: #111945 !important;
  white-space: nowrap;
}
.ivu-menu-item,
.ivu-menu-submenu-title {
  &:hover {
    .menu-img {
      opacity: 1;
    }
  }
}
.ivu-menu-opened {
  background: #2d8cf0 !important;
}
.menu-img-container {
  display: inline-block;
  vertical-align: -0.135em;
  text-align: center;
  margin-right: 8px;
  // vertical-align: sub;
  .menu-img {
    width: 17px;
    opacity: 0.7;
  }
}
.ivu-menu-item:active {
  background: #2d8cf0;
}

.img-h1 {
  display: flex;
  align-items: center;
  height: 56px;
  justify-content: center;
}
.side-menu-wrapper {
  width: 100%;
  h1 {
    img {
      margin: 6px 12px;
      vertical-align: bottom;
    }
    background-color: #202751;
    color: #ffffff;
    line-height: 52px;
  }
  .ivu-tooltip,
  .drop-menu-span {
    display: block;
    width: 100%;
    text-align: center;
    padding: 5px 0;
  }
  .drop-wrapper > .ivu-dropdown {
    display: block;
    padding: 5px;
    margin: 0 auto;
  }
  .drop-wrapper > .ivu-dropdown:hover > .ivu-select-dropdown {
    position: fixed !important;
  }
}
</style>
<style lang="less" scoped>
.ivu-menu-submenu-title {
  white-space: normal;
  padding-right: 35px !important;
  position: relative;
  .menu-img-container {
    float: left;
  }
  .ivu-icon.ivu-icon-ios-arrow-down.ivu-menu-submenu-title-icon {
    position: absolute;
    right: 12px;
    top: 25px;
  }
}
</style>
