<template>
  <div class="browser_index">
    <Browser v-if="currentTab==='browser_index'" />
    <router-view v-else/>
  </div>
</template>

<script>
import Browser from './browser.vue'
export default {
  name: 'browser_index',
  components: {
    <PERSON>rows<PERSON>
  },
  data () {
    return {
      excludeArr: ['browser_block', 'browser_trade', 'browser_chain']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
  },
  mounted () {}
}
</script>

<style lang="less" scoped>
.browser_index{
 //
}
</style>
