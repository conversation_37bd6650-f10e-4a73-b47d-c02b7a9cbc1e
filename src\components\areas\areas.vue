<template>
  <div class="areasBox">
      <div ref="areas" class="areas" :style="{width:areasWidth,height: areasHeight}">
      </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
const option = {
  title: [{
    text: '',
    x: 'center',
    y: 'bottom',
    padding: 10,
    textAlign: 'center',
    textStyle: {
      fontSize: 12,
      fontStyle: 'normal',
      fontWeight: 'normal'
    }
  },
  {
    text: '',
    x: 'left',
    y: 'center',
    textAlign: 'center',
    textStyle: {
      fontSize: 12,
      fontStyle: 'normal',
      fontWeight: 'normal'
    }
  },
  {
    text: '',
    x: 'left',
    y: 'top',
    textAlign: 'left',
    textStyle: {
      fontSize: 12,
      color: '#3c4858'
    }
  }],
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true
    },
    boundaryGap: true
  },
  yAxis: {
    type: 'value',
    splitLine: {
      show: true
    },
    axisLine: {
      show: false,
      onZero: false
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
      crossStyle: {
        color: '#000'
      },
      lineStyle: {
        type: 'dashed'
      }
    }
  },
  grid: {
    left: '12%'
  },
  series: [{
    name: '调用量',
    type: 'line',
    symbolSize: 10,
    itemStyle: {
      color: '#3A84FF',
      emphasis: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        textColor: '#000'
      },
      normal: {
        color: '#3A84FF',
        label: {
          show: true,
          textStyle: { color: '#000', fontWeight: 100, fontSize: 14, lineHeight: 14 },
          formatter: function (val) {
            return val.value
          }
        },
        lineStyle: {
          color: '#3A84FF'
        }
      }
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0, color: 'rgba(58,132,255, 0.5)' // 0% 处的颜色
        }, {
          offset: 1, color: 'rgba(81, 135, 223, 0.6)' // 100% 处的颜色
        }],
        global: false // 缺省为 false
      }
    }
  }],
  animation: true,
  animationDelay: 100,
  animationThreshold: 250,
  animationDuration: 2000,
  animationDurationUpdate: 2000,
  animationEasing: 'ExponentialOut',
  // animationEasingUpdate: 'ExponentialOut',
  hoverLayerThreshold: 3000,
  hoverAnimationDuration: 3000
}
export default {
  name: 'areas',
  props: {
    areasData: {
      type: Array,
      default () {
        return []
      }
    },
    areasHeight: {
      type: String,
      default: '360px'
    },
    areasWidth: {
      type: String,
      default: '580px'
    },
    areasXaxis: {
      type: Array,
      default () {
        return []
      }
    },
    xTitle: {
      type: String,
      default: '时间'
    },
    yTitle: {
      type: String,
      default: '调用量/次'
    },
    areasTitle: {
      type: String,
      default: ''
    },
    contractName: {
      type: String,
      default: ''
    },
    padValue: {
      type: Number,
      default: 15
    }
  },
  data () {
    return {
    }
  },
  methods: {
    initAreasData () {
      option.title[0].text = this.xTitle
      option.title[1].text = this.yTitle.replace(/\s/g, '').replace(/(.{1})/g, '$1\n')
      option.title[1].padding = this.padValue
      option.title[2].text = this.areasTitle
      option.series[0].data = this.areasData
      option.series[0].name = this.contractName
      option.xAxis.data = this.areasXaxis
      this.mychart.setOption(option, true)
      window.addEventListener('resize', () => {
        this.mychart.resize()
      })
    }
  },
  mounted () {
    this.mychart = echarts.init(this.$refs.areas)
    this.initAreasData()
  },
  watch: {
    areasData: {
      handler (newVal, oldVal) {
        this.areaData = newVal
        this.initAreasData()
      },
      deep: true,
      immediate: false
    },
    contractName: {
      handler (newVal, oldVal) {
        this.contractName = newVal
        this.initAreasData()
      },
      deep: true,
      immediate: false
    },
    areasXaxis: {
      handler (newVal, oldVal) {
        this.areasXaxis = newVal
        this.initAreasData()
      },
      deep: true,
      immediate: false
    }
  }
}
</script>
<style lang="less" scoped>
.areasBox{
  height:100%;
  width:100%;
  color:rgba(81, 135, 223, 0);
}
</style>
