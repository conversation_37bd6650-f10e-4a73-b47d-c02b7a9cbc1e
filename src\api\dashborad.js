import axios from './index'

// 节点状态

export const getNodeStatus = (chainId) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/node/num/' + chainId,
    method: 'GET'
  })
}


// 近七天交易量

export const getBusiness = (chainId, ONE_WEEK) => {
  return axios.request({
    url: '/cmbaas/statistical/contract/' + chainId + '/allTrans/history/' + ONE_WEEK,
    method: 'GET'
  })
}

// 我的链账户
export const getMyChain = (chainId, data) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + chainId + '/accountResource/list',
    method: 'POST',
    data: data
  })
}

//链账户资源消耗
export const getChainDetail = (chainId, chainAccontId) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + chainId + '/accountResource/detail/' + chainAccontId,
    method: 'GET'
  })
}
// 链账户资源里面的资源剩余列表

export const getResidue = (data) => {
  return axios.request({
    url: '/cmbaas/chain/Resourse/admin/selectAnlayBatchByPage',
    method: 'POST',
    data: data
  })
}
// 链账户资源里面的资源变更日志

export const getChange = (data) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/admin/recordOfResource',
    method: 'POST',
    data: data
  })
}
// 我的链账户资源里面列表
export const getMyResidue = (data) => {
  return axios.request({
    url: '/cmbaas/chain/Resourse/selectAnlayBatchByPage',
    method: 'POST',
    data: data
  })
}
// 链账户资源里面的资源变更日志

export const getMyChange = (data) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/recordOfResource',
    method: 'POST',
    data: data
  })
}

// 合约链账户绑定下拉框
export const getChainSele = (chainId, data) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + chainId + '/accountByNormal',
    method: 'POST',
    data: data
  })
}

// 获取合约action的绑定记录
export const getActionCon = (chainId, chainAccountId) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + chainId + '/action/link/listByNormalAccount/' + chainAccountId,
    method: 'GET',
  })
}

// 合约链账户绑定

export const getListByNormal = (chainId, normalAccountId) => {
  const data = new FormData()
  data.append('normalAccountId', normalAccountId)
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + chainId + '/permission/listByNormal',
    data: data,
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

//  链账户资源管理
export const getImportNortalChainData = (file) => {
  const data = new FormData()
  data.append('tenantId', file.tenantId)
  data.append('file', file.file)
  return axios.request({
    url: '/cmbaas/chain/import/importNortalChainData',
    data: data,
    method: 'POST',
    // timeout: 10 * 60 * 1000,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 链账户资源管理下载

export const getExportMainChainData = (beginTime, treasuryToken) => {
  const data = new FormData()
  data.append('beginTime', beginTime)
  data.append('treasuryToken', treasuryToken)
  return axios.request({
    url: '/cmbaas/chain/export/exportMainChainData',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}