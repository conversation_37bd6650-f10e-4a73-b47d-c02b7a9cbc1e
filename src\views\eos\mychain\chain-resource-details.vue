<template>
  <div class="chain">
    <Collapse v-model="panelValue" simple name="mainpanel">

      <Panel name="1" style="background:#ECEFFC;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        基础信息
        <p slot="content" class="basetext">
          <span>名称：{{ arrDetails.chainAccountName }}</span>
          <span>类型：{{ arrDetails.accountType }}</span>
          <span>创建时间：{{ arrDetails.createTime }}</span>
          <span>更新时间：{{ arrDetails.updateTime }}</span>
        </p>
        <p slot="content" class="basetext">
          <span>描述：{{ arrDetails.description }}</span>
        </p>
        <p slot="content" class="basetext" :style="this.statusdetails==='上链失败'||this.statusdetails==='上链成功'?'display:none':'display:block'">
          <span>状态：{{ arrDetails.status }}</span>
        </p>
        <!-- <p slot="content" class="basetext">

        </p> -->
        <div v-if="this.infodetail!==null" slot="content">

          <div slot="content" class="basetext" v-if="this.statusdetails==='上链失败'">
            <p v-if="infodetail.code===null">
              <span>状态：{{ arrDetails.status }}</span>
              <span style="line-height:30px">失败原因：{{infodetail.errorInfo}}</span>
            </p>
            <p v-else>
              <span>状态：{{ arrDetails.status }}</span>
              <span style="line-height:30px">编码信息：{{infodetail.code}}</span>
              <span style="line-height:30px">失败原因：{{infodetail.what}}</span>
              <span style="line-height:30px">方法：{{infodetail.name}}</span>
            </p>

          </div>
          <div slot="content" class="basetext" v-else-if="this.statusdetails==='上链成功'">
            <span>状态：{{ arrDetails.status }}</span>
            <span style="line-height:30px;">交易ID：{{infodetail.transactionId}}</span>
          </div>
        </div>

      </Panel>
      <Panel name="2" style="background:#ECEFFC;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        资源剩余
        <p slot="content" class="basetext" style="display: flex;">

          <span class="btn_title">日期</span>
          <DatePicker format="yyyy-MM-dd" type="daterange" :editable='false' placeholder="开始日期~结束日期" style="width: 220px" @on-change="timeout_click" v-model="time"></DatePicker>
          <Button type="primary" class="btn_search" icon="ios-search" @click.native="input_residue">查询</Button>
          <Button type="primary" ghost icon="md-sync" class="btn_search" @click.native="reset">重置</Button>
        </p>
        <p slot="content" class="basetext">
        <Table stripe :columns="columnsResource" :data="dataResource">
          <template slot-scope="{ row, index }" slot="action">
            <Button type="text" size="small" style="marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF;" @click="resourcemodal(row)">资源申请</Button>
          </template>
        </Table>
        <Page :total="PageParam.pagetotal" :current.sync="PageParam.pageIndex" @on-change="pageChange1" :page-size="PageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange1" style="text-align:right;" />
        </p>
      </Panel>
      <Panel name="3" style="background:#ECEFFC;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        资源变更日志
        <p slot="content" class="basetext" style="display: flex;">

          <span class="btn_title">操作时间</span>
          <DatePicker format="yyyy-MM-dd" type="daterange" :editable='false' placeholder="开始日期~结束日期" style="width: 220px" @on-change="timeout_click2" v-model="time2"></DatePicker>

          <span class="btn_title">操作类型</span>

          <Select v-model="operationType" style="width: 180px" placeholder="请选择操作类型">
            <Option v-for="(item,index) in cityList1" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
          <Button type="primary" class="btn_search" icon="ios-search" @click.native="input_change">查询</Button>
          <Button type="primary" ghost icon="md-sync" class="btn_search" @click.native="reset2">重置</Button>
        </p>
        <p slot="content" class="basetext">
        <Table stripe :columns="columnsContract" :data="columnsContractArr">
          <!-- <template slot-scope="{ row, index }" slot="action">
            <Button type="text" size="small" style="marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF;" @click="resourcemodal(row)">资源申请</Button>
          </template> -->
        </Table>
        <Page :total="PageParam2.pagetotal" :current.sync="PageParam2.pageIndex" @on-change="pageChange2" :page-size="PageParam2.pageSize2" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange2" style="text-align:right;" />
        </p>
      </Panel>

    </Collapse>

  </div>
</template>
<script>
import { getMyResidue, getMyChange } from '@/api/dashborad'
import { localRead } from '@/lib/util'
import { getChainTableDetails, getContractInfo, contractUnbundling, } from '@/api/data'

import Areas from '_c/areas'
import FormGroup from '_c/form-group'
import ContractAnalysis from '../mycontract/contract-analysis.vue'
import { mapGetters } from 'vuex'
import { isPowerAccount } from '../../../lib/check'
let unitConversion = {
  Byte (value) { return value * 1 },
  KB (value) { return value / 1024 },
  MB (value) { return value / 1024 / 1024 },
  GB (value) { return value / 1024 / 1024 / 1024 }
}

export default {
  name: 'chain_details',
  components: { Areas, ContractAnalysis, FormGroup },
  data () {

    return {
      btn_title: 'MB',
      zyModal: false, // 资源申请
      statusdetails: this.$route.query.status ? this.$route.query.status : '',
      PageParam: { pageSize: 10, pageIndex: 1, pagetotal: 0 },
      PageParam2: { pageSize: 10, pageIndex: 1, pagetotal: 0 },
      panelValue: ['1', '2', '3', '4', '5'],
      chainId: this.$route.query.chainId ? this.$route.query.chainId : 0,
      chainAccountName: this.$route.query.chainAccountName ? this.$route.query.chainAccountName : '',
      isshow: { modal: false, modalpower: false, modalpassword: false, chainpower: true, contractpower: false, formgroup: false, modaldata: false, modalContract: false, },
      actionValue: '',
      actionValues: '',
      arrDetails: {},
      infodetail: {},
      columnsContract: [
        { title: '链账户名称', key: 'chainAccountName', tooltip: true, },
        { title: '链名称', key: 'chainName', tooltip: true },
        { title: '操作对象', key: 'resourceType', tooltip: true },
        { title: '资源变化量', key: 'resourceSize', },
        { title: '操作类型', key: 'sourceType', },
        { title: '操作时间', key: 'updateTime', tooltip: true },
      ],
      columnsContractArr: [],
      contractArr: {},
      contractArrList: [],



      columnsResource: [

        { title: '链账户名称', key: 'chainAccountName', tooltip: true },
        { title: '链名称', key: 'chainName', tooltip: true },
        { title: 'RAM(Byte)', key: 'residueRam', tooltip: true },
        { title: 'NET(Byte)', key: 'residueNet', tooltip: true },
        { title: 'CPU(Us)', key: 'residueCpu', tooltip: true },
        { title: '日期', key: 'time', tooltip: true },

      ],
      dataResource: [],
      dataResource1: [],

      // 上链时间
      chain_time: '',
      chain_time2: '',
      time: '',
      time2: '',
      cityList1: [
        {
          value: 0,
          label: '链账户资源申请'
        },
        {
          value: 1,
          label: '管理员审批通过'
        },
        {
          value: 2,
          label: '管理员审批不通过'
        },
        {
          value: 3,
          label: '管理员手动分配'
        },
      ],
      operationType: ''
    }
  },
  computed: {
    ...mapGetters(['getDict'])
  },
  methods: {
    // 输入框值
    input_residue () {

      this.getTablist() // 获取表格列表
    },
    input_change () {

      this.getChangeList() // 获取表格列表
    },
    // 重置
    reset () {
      this.time = ''
      this.chain_time = []
      this.PageParam = { pageIndex: 1, pageSize: 10 }

      this.getTablist()
    },
    timeout_click (e) {
      this.chain_time = e
    },
    // 重置
    reset2 () {
      this.time2 = ''
      this.chain_time2 = []
      this.operationType = ''
      this.PageParam2 = { pageIndex: 1, pageSize: 10 }

      this.getChangeList()
    },
    timeout_click2 (e) {
      this.chain_time2 = e
    },
    getTablist () {
      let data = {
        chainAccountId: this.$route.query.chainAccountId,
        beginTime: this.chain_time[0] || '',
        endTime: this.chain_time[1] || '',
        pageParam: this.PageParam,
        sourceType: ''
      }
      getMyResidue(data).then(res => {
        if (res.code === '00000') {
          this.dataResource = res.data.records
          this.PageParam.pagetotal = res.data.total
        } else {
          this.msgInfo('warning', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getChangeList () {
      let data = {
        chainAccountId: this.$route.query.chainAccountId,
        beginTime: this.chain_time2[0] || '',
        endTime: this.chain_time2[1] || '',
        pageParam: this.PageParam2,
        sourceType: this.operationType
      }
      getMyChange(data).then(res => {
        if (res.code === '00000') {
          let dataType = {
            0: '链账户资源申请',
            1: '管理员审批通过',
            2: '管理员审批不通过',
            3: '管理员手动分配'
          }
          let data = res.data.records.map(item => {
            return {
              ...item,
              sourceType: dataType[item.sourceType]
            }


          })
          this.columnsContractArr = data
          this.PageParam2.pagetotal = res.data.total
        } else {
          this.msgInfo('warning', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    pageChange1 (index) {
      this.PageParam.pageIndex = index
      this.getTablist()
    },
    pageSizeChange1 (index) {
      this.PageParam.pageSize = index
      this.getTablist()
    },

    pageChange2 (index) {
      this.PageParam2.pageIndex = index
      this.getChangeList()
    },
    pageSizeChange2 (index) {
      this.PageParam2.pageSize = index
      this.getChangeList()
    },
    init () {
      // this.actionValue = ''
      this.passwordItem.password = ''
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },

  },
  watch: {
    arrDetails: {
      handler () {
        if (this.arrDetails.accountTypeKey === 'CONTRACT') {
          this.isshow.chainpower = false
          getContractInfo(this.chainId, this.chainAccountId).then(res => {
            if (res.code === '00000') {
              this.contractArr = res.data
              if (res.data.isDelopyContract !== 0) this.isshow.contractpower = true
            } else if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else this.msgInfo('warning', res.message, true)
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        } else this.isshow.chainpower = true
      },
      deep: true,
      immediate: false
    },

  },
  mounted () {
    // console.log(this.$route.query.chainId)
    if (this.chainId && this.chainAccountName) {
      getChainTableDetails(this.chainId, this.chainAccountName).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.arrDetails = res.data
          // this.dataResource1 = res.data.ramQuota ? [{ ramQuota: this.arrDetails.ramQuota, ramUsage: this.arrDetails.ramUsage, odd: this.arrDetails.odd, proportion: this.arrDetails.proportion, changname: 'MB' }] : []
          // this.dataResource = res.data.ramQuota ? [{ ramQuota: unitConversion['MB'](this.arrDetails.ramQuota), ramUsage: unitConversion['MB'](this.arrDetails.ramUsage), odd: unitConversion['MB'](this.arrDetails.odd), proportion: this.arrDetails.proportion, changname: 'MB' }] : []
          this.infodetail = res.data.message
          this.chainAccountId = res.data.chainAccountId ? res.data.chainAccountId : 0
          this.chainAccountUuid = res.data.chainAccountUuid ? res.data.chainAccountUuid : ''
          this.chainAccountName = res.data.chainAccountName ? res.data.chainAccountName : ''
          if (this.arrDetails.ops) this.isshow.formgroup = true
          else {
            // console.log(this.ops)
            this.arrDetails.ops = this.ops
          }
          // if (this.arrDetails.accountTypeKey === 'CONTRACT') this.getTimeScopeList()
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
      this.getTablist()
      this.getChangeList()
    }
  },

}
</script>
<style lang="less" scoped>
/deep/.ivu-menu-submenu-title {
  background: #fff !important;
}
/deep/.ivu-menu,
.ivu-menu-dark,
.ivu-menu-vertical,
.ivu-menu-opened,
.ivu-menu-submenu-title {
  background: #fff !important;
}
.ivu-menu-vertical.ivu-menu-light:after {
  background: #fff;
}
.tdstyle {
  display: inline-block;
  width: 100px;
  text-align: right;
}
.sizeTitle {
  font-weight: bold;
  font-size: 16px;
}
.size {
  font-weight: bold;
  font-size: 15px;
}
.chain {
  margin: -16px;
  height: 100%;
  .basetext {
    padding-top: 20px;
    span {
      text-align: left;
      margin: 0 26px;
      line-height: 20px;
      word-break: break-all;
    }
  }
  .demo-split {
    height: 200px;
  }
  .title {
    .size;
  }
  .bs {
    text-indent: 10px;
    line-height: 15px;
    border-left: 5px solid #3d73ef;
    margin-bottom: 15px;
  }
}
.select-area1 {
  /deep/.ivu-select-selection {
    border: 1px solid #4b98eb;
  }
  /deep/.ivu-select-input {
    color: #57a3f3;
  }
  /deep/.ivu-icon-ios-arrow-down:before {
    color: #57a3f3;
  }
}
.panel-class {
  i {
    vertical-align: -0.15em;
  }
}
.analysisModal /deep/.ivu-modal-body {
  background-color: #eff0f4;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.ivu-split-trigger-con {
  display: none;
}
/deep/.ivu-card {
  background: #f2f6fd;
}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
/deep/.ivu-collapse[name="mainpanel"] {
  border: none;
}
/deep/.ivu-collapse[name="mainpanel"] > .ivu-collapse-item {
  border: none;
  border-radius: 5px 5px;
  margin-bottom: 10px;
}
.btn_title {
  // width: 80px;
  height: 33px !important;
  text-align: center !important;
  line-height: 33px !important;
  display: inline-block !important;
}
.btn_search {
  margin-left: 10px;
}
</style>
