// 运维监控,概览页echarts后台返回数据处理
import * as echarts from 'echarts'
import { zxOption, ybpOption } from '@/utils/zyEcharts'
import { getYMDHMS } from '@/utils/validate'
let zxt = {
  data () {
    return {
      upMaxVal: 0, // 上传最大值
      downMaxVal: 0// 下载最大值
    }
  },
  created () {
  },
  methods: {
    // 纵坐标取整问题
    compute (val) {
      if (val <= 4) {
        return 4
      } else if (val > 4 && val <= 8) {
        return 8
      } else if (val > 8 && val <= 10) {
        return 12
      } else if (val > 10 && val < 100) {
        let getNew = val.toString().substr(0, 1)
        return parseInt(getNew) + 1 + '0'
      } else if (val > 100 && val < 1000) {
        let getNew = val.toString().substr(0, 1)
        return parseInt(getNew) + 1 + '00'
      } else if (val > 1000) {
        let getNew = val.toString().substr(0, 1)
        return parseInt(getNew) + 1 + '000'
      } else {
        return val
      }
    },
    // 仪表盘数据过滤
    filtYbp (GetName, item, list, index, dw, num) {
      if (GetName == 'cpu') {
        let cpuLastArry = Number(item.Values[item.Values.length - 1][1])
        dw[index].unit = '(单位：%)'
        list.push(cpuLastArry.toFixed(1))
      } else if (GetName == 'memory') {
        let memorLastArry = Number(item.Values[item.Values.length - 1][1])
        dw[index].unit = '(单位：%)'
        list.push(memorLastArry.toFixed(1))
      } else if (GetName == 'storage') {
        let stoLastArry = Number(item.Values[item.Values.length - 1][1])
        let stoLast = '(单位：%)'
        dw[index].unit = stoLast
        list.push(stoLastArry.toFixed(1))
      } else if (GetName == 'network_upload_rate') {
        let netLastArry =
                    num == 0
                      ? Number(item.Values[item.Values.length - 1][1]) * 8 * this.zjNum
                      : Number(item.Values[item.Values.length - 1][1]) * 8
        let getNew = 0
        let netLast = ''
        if (netLastArry > 1000) {
          if (netLastArry / 1024 > 1000) {
            getNew = netLastArry / 1024 / 1024
            netLast = '(单位：Mb/s)'
            if (getNew > 100 && getNew < 1000) {
              getNew = getNew / 10
              netLast = '(单位：10Mb/s)'
            }
            if (getNew > 1000) {
              getNew = getNew / 1024
              netLast = '(单位：Gb/s)'
            }
          } else {
            getNew = netLastArry / 1024
            netLast = '(单位：kb/s)'
            if (getNew > 100 && getNew < 1000) {
              getNew = getNew / 10
              netLast = '(单位：10kb/s)'
            }
          }
        } else if (netLastArry > 100 && netLastArry <= 1000) {
          getNew = netLastArry / 1024
          netLast = '(单位：kb/s)'
        } else {
          netLast = '(单位：b/s)'
          getNew = netLastArry
        }
        dw[index].unit = netLast
        list.push(getNew.toFixed(1))
      } else if (GetName == 'network_download_rate') {
        let netDownLastArry =
                    num == 0
                      ? Number(item.Values[item.Values.length - 1][1]) * 8 * this.zjNum
                      : Number(item.Values[item.Values.length - 1][1]) * 8
        let getNew = 0
        let netDownLast = ''
        if (netDownLastArry > 1000) {
          if (netDownLastArry / 1024 > 1000) {
            getNew = netDownLastArry / 1024 / 1024
            netDownLast = '(单位：Mb/s)'
            if (getNew > 100 && getNew < 1000) {
              getNew = getNew / 10
              netDownLast = '(单位：10Mb/s)'
            }
            if (getNew > 1000) {
              getNew = getNew / 1024
              netDownLast = '(单位：Gb/s)'
            }
          } else {
            getNew = netDownLastArry / 1024
            netDownLast = '(单位：kb/s)'
            if (getNew > 100 && getNew < 1000) {
              getNew = getNew / 10
              netDownLast = '(单位：10kb/s)'
            }
          }
        } else if (netDownLastArry > 100 && netDownLastArry <= 1000) {
          getNew = netDownLastArry / 1024
          netDownLast = '(单位：kb/s)'
        } else {
          getNew = netDownLastArry
          netDownLast = '(单位：b/s)'
        }
        dw[index].unit = netDownLast
        list.push(getNew.toFixed(1))
      }
    },
    // 链节点资源echarts
    ljdChart () {
      // 链节点资源
      if (document.getElementById('zxCpu')) {
        let zxCpuEchart = echarts.init(document.getElementById('zxCpu'))
        let zxNcEchart = echarts.init(document.getElementById('zxNc'))
        let zxCcEchart = echarts.init(document.getElementById('zxCc'))
        let zxScEchart = echarts.init(document.getElementById('zxSc'))
        zxCpuEchart.setOption(zxOption, true)
        zxNcEchart.setOption(zxOption, true)
        zxCcEchart.setOption(zxOption, true)
        zxScEchart.setOption(zxOption, true)
        zxCpuEchart.setOption({
          title: {
            subtext: '(单位：%)',
            text: '{A|' + 'CPU使用率}',
            align: 'left',
            textStyle: {
              rich: {
                A: {
                  height: 16,
                  width: 4,
                  lineHeight: 10,
                  verticalAlign: 'middle',
                  color: 'black',
                  fontSize: 14
                }
              }

            }
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            formatter: params => {
              let data =
                                '<span>' +
                                params[0].seriesName +
                                '：' +
                                params[0].value.toFixed(2) +
                                '%' +
                                '</span> </br>' +
                                '<span>' +
                                this.tooltipList[params[0].dataIndex]
              '</span> '
              return data
            }
          },
          xAxis: {
            data: this.ljXCpulist
          },
          yAxis: {
            // splitNumber:4,
            // min:0,
            // max:1,
            min: 0,
            max: Math.ceil(Math.max(...this.ljCpuList)),
            interval: Math.ceil(Math.max(...this.ljCpuList)) != 0 ? (Math.ceil(Math.max(...this.ljCpuList)) / 4) : 1 / 4
            // interval: (Math.ceil(Math.max(...this.ljCpuList)) / 4),
          },
          series: [
            {
              name: 'CPU使用率',
              data: this.ljCpuList,
              type: 'line',
              smooth: false
            }
          ]
        })
        zxNcEchart.setOption({
          title: {
            subtext: '(单位：' + this.ncMax + ')',
            text: '{A|' + '内存使用量}',
            align: 'left',
            textStyle: {
              rich: {
                A: {
                  height: 16,
                  width: 4,
                  lineHeight: 10,
                  verticalAlign: 'middle',
                  color: 'black',
                  fontSize: 14
                }
              }

            }
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            formatter: params => {
              let data =
                                '<span>' +
                                params[0].seriesName +
                                '：' +
                                params[0].value.toFixed(2) +
                                this.ncMax +
                                '</span> </br>' +
                                '<span>' +
                                this.tooltipList[params[0].dataIndex]
              '</span> '
              return data
            }
          },
          xAxis: {
            data: this.ljXNclist
          },
          yAxis: {
            min: 0,
            max: this.compute(Math.ceil(Math.max(...this.ljNcList))),
            // max: Math.ceil(Math.max(...this.ljNcList)),
            interval: this.compute(Math.ceil(Math.max(...this.ljNcList))) / 4,
            // splitNumber:4,
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: [
            {
              type: 'line', // 增加type字段
              name: '内存使用量',
              data: this.ljNcList,
              itemStyle: {
                normal: {
                  color: '#11d1ec',
                  lineStyle: {
                    // 线的颜色
                    color: '#11d1ec'
                  }
                }
              }
            }
          ]
        })
        zxCcEchart.setOption({
          title: {
            subtext: '(单位：' + this.ccMax + ')',
            text: '{A|' + '存储使用量}',
            align: 'left',
            textStyle: {
              rich: {
                A: {
                  height: 16,
                  width: 4,
                  lineHeight: 10,
                  verticalAlign: 'middle',
                  color: 'black',
                  fontSize: 14
                }
              }

            }
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            formatter: params => {
              let data =
                                '<span>' +
                                params[0].seriesName +
                                '：' +
                                params[0].value.toFixed(2) +
                                this.ccMax +
                                '</span> </br>' +
                                '<span>' +
                                this.tooltipList[params[0].dataIndex]
              '</span>'
              return data
            }
          },
          xAxis: {
            data: this.ljXCclist
          },
          yAxis: {
            min: 0,
            max: this.compute(Math.ceil(Math.max(...this.ljCcList))),
            interval: this.compute(Math.ceil(Math.max(...this.ljCcList))) / 4,
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: [
            {
              type: 'line', // 增加type字段
              name: '存储使用量',
              data: this.ljCcList,
              itemStyle: {
                normal: {
                  color: '#2193B0',
                  lineStyle: {
                    // 线的颜色
                    color: '#2193B0'
                  }
                }
              }
            }
          ]
        })
        zxScEchart.setOption({
          title: {
            subtext: Math.max(...this.ljDownLoadList) > Math.max(...this.ljUpdateList) ? '(单位：' + this.xzMax + ')' : '(单位：' + this.scMax + ')',
            text: '{A|' + '上传/下载速率}',
            align: 'left',
            textStyle: {
              rich: {
                A: {
                  height: 16,
                  width: 4,
                  lineHeight: 10,
                  verticalAlign: 'middle',
                  color: 'black',
                  fontSize: 14
                }
              }

            }
          },
          tooltip: {
            formatter: params => {
              let data =
                                '<span>' +
                                params[0].seriesName +
                                '：' +
                                params[0].value.toFixed(2) +
                                this.scMax +
                                '</span>' + '<br />' +
                                '<span>' +
                                params[1].seriesName +
                                '：' +
                                params[1].value.toFixed(2) +
                                this.xzMax +
                                '</span>' + '<br />' +
                                '<span>' +
                                this.tooltipList[params[0].dataIndex]
              '</span>'
              return data
            }
          },
          xAxis: {
            data: this.ljXUplist
          },
          legend: {
            // icon: "circle",
            x: 'right', // 可设定图例在左、右、居中
            y: 'top', // 可设定图例在上、下、居中
            // 可设定图例[距上方距离，距右方距离，距下方距离，距左方距离]
            data: ['上传速率', '下载速率'],
            itemHeight: 6, // 改变圆圈大小
            // itemWidth: 14, //图标宽
            // itemHeight: 10, //图标高
            itemGap: 40, // 间距/
            formatter: function (name) {
              return name
            },
            textStyle: {
              color: '#666',
              // marginLeft:'-120px',
              fontSize: 14
            }
          },
          yAxis: {
            min: 0,
            max: Math.max(...this.ljDownLoadList) > Math.max(...this.ljUpdateList) ? this.compute(Math.ceil(Math.max(...this.ljDownLoadList))) : this.compute(Math.ceil(Math.max(...this.ljUpdateList))),
            interval: Math.max(...this.ljDownLoadList) > Math.max(...this.ljUpdateList) ? this.compute(Math.ceil(Math.max(...this.ljDownLoadList))) / 4 : this.compute(Math.ceil(Math.max(...this.ljUpdateList))) / 4,
            axisLabel: {
              formatter: Math.max(...this.ljDownLoadList) > Math.max(...this.ljUpdateList) ? '{value}' : '{value}'
            }
          },
          series: [
            {
              type: 'line', // 增加type字段
              name: '上传速率',
              data: this.ljUpdateList,
              itemStyle: {
                normal: {
                  color: '#9D6FF9',
                  lineStyle: {
                    // 线的颜色
                    color: '#9D6FF9'
                  }
                }
              }
            },
            {
              type: 'line', // 增加type字段
              name: '下载速率',
              data: this.ljDownLoadList,
              itemStyle: {
                normal: {
                  color: '#F2994A',
                  lineStyle: {
                    // 线的颜色
                    color: '#F2994A'
                  }
                }
              }
            }
          ]
        })
      }
    },
    ljdChart2 () {
      // 链节点资源
      if (document.getElementById('zxCpu')) {
        let zxCpuEchart = echarts.init(document.getElementById('zxCpu2'))
        let zxNcEchart = echarts.init(document.getElementById('zxNc2'))
        let zxCcEchart = echarts.init(document.getElementById('zxCc2'))
        let zxScEchart = echarts.init(document.getElementById('zxSc2'))
        zxCpuEchart.setOption(zxOption, true)
        zxNcEchart.setOption(zxOption, true)
        zxCcEchart.setOption(zxOption, true)
        zxScEchart.setOption(zxOption, true)
        zxCpuEchart.setOption({
          title: {
            subtext: '(单位：%)',
            text: '{A|' + 'CPU使用率}',
            align: 'left',
            textStyle: {
              rich: {
                A: {
                  height: 16,
                  width: 4,
                  lineHeight: 10,
                  verticalAlign: 'middle',
                  color: 'black',
                  fontSize: 14
                }
              }

            }
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            formatter: params => {
              let data =
                                '<span>' +
                                params[0].seriesName +
                                '：' +
                                params[0].value.toFixed(2) +
                                '%' +
                                '</span> </br>' +
                                '<span>' +
                                this.tooltipList[params[0].dataIndex]
              '</span> '
              return data
            }
          },
          xAxis: {
            data: this.ljXCpulist
          },
          yAxis: {
            // splitNumber:4,
            min: 0,
            max: Math.ceil(Math.max(...this.ljCpuList)),
            interval: Math.ceil(Math.max(...this.ljCpuList)) != 0 ? (Math.ceil(Math.max(...this.ljCpuList)) / 4) : 1 / 4
            // interval: (Math.ceil(Math.max(...this.ljCpuList)) / 4),
          },
          series: [
            {
              name: 'CPU使用率',
              data: this.ljCpuList,
              type: 'line',
              smooth: false
            }
          ]
        })
        zxNcEchart.setOption({
          title: {
            subtext: '(单位：' + this.ncMax + ')',
            text: '{A|' + '内存使用量}',
            align: 'left',
            textStyle: {
              rich: {
                A: {
                  height: 16,
                  width: 4,
                  lineHeight: 10,
                  verticalAlign: 'middle',
                  color: 'black',
                  fontSize: 14
                }
              }

            }
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            formatter: params => {
              let data =
                                '<span>' +
                                params[0].seriesName +
                                '：' +
                                params[0].value.toFixed(2) +
                                this.ncMax +
                                '</span> </br>' +
                                '<span>' +
                                this.tooltipList[params[0].dataIndex]
              '</span> '
              return data
            }
          },
          xAxis: {
            data: this.ljXNclist
          },
          yAxis: {
            min: 0,
            max: this.compute(Math.ceil(Math.max(...this.ljNcList))),
            // max: Math.ceil(Math.max(...this.ljNcList)),
            interval: this.compute(Math.ceil(Math.max(...this.ljNcList))) / 4,
            // splitNumber:4,
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: [
            {
              type: 'line', // 增加type字段
              name: '内存使用量',
              data: this.ljNcList,
              itemStyle: {
                normal: {
                  color: '#11d1ec',
                  lineStyle: {
                    // 线的颜色
                    color: '#11d1ec'
                  }
                }
              }
            }
          ]
        })
        zxCcEchart.setOption({
          title: {
            subtext: '(单位：' + this.ccMax + ')',
            text: '{A|' + '存储使用量}',
            align: 'left',
            textStyle: {
              rich: {
                A: {
                  height: 16,
                  width: 4,
                  lineHeight: 10,
                  verticalAlign: 'middle',
                  color: 'black',
                  fontSize: 14
                }
              }

            }
          },
          tooltip: {
            show: true,
            trigger: 'axis',
            formatter: params => {
              let data =
                                '<span>' +
                                params[0].seriesName +
                                '：' +
                                params[0].value.toFixed(2) +
                                this.ccMax +
                                '</span> </br>' +
                                '<span>' +
                                this.tooltipList[params[0].dataIndex]
              '</span>'
              return data
            }
          },
          xAxis: {
            data: this.ljXCclist
          },
          yAxis: {
            min: 0,
            max: this.compute(Math.ceil(Math.max(...this.ljCcList))),
            interval: this.compute(Math.ceil(Math.max(...this.ljCcList))) / 4,
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: [
            {
              type: 'line', // 增加type字段
              name: '存储使用量',
              data: this.ljCcList,
              itemStyle: {
                normal: {
                  color: '#2193B0',
                  lineStyle: {
                    // 线的颜色
                    color: '#2193B0'
                  }
                }
              }
            }
          ]
        })
        zxScEchart.setOption({
          title: {
            subtext: Math.max(...this.ljDownLoadList) > Math.max(...this.ljUpdateList) ? '(单位：' + this.xzMax + ')' : '(单位：' + this.scMax + ')',
            text: '{A|' + '上传/下载速率}',
            align: 'left',
            textStyle: {
              rich: {
                A: {
                  height: 16,
                  width: 4,
                  lineHeight: 10,
                  verticalAlign: 'middle',
                  color: 'black',
                  fontSize: 14
                }
              }

            }
          },
          tooltip: {
            formatter: params => {
              let data =
                                '<span>' +
                                params[0].seriesName +
                                '：' +
                                params[0].value.toFixed(2) +
                                this.scMax +
                                '</span>' + '<br />' +
                                '<span>' +
                                params[1].seriesName +
                                '：' +
                                params[1].value.toFixed(2) +
                                this.xzMax +
                                '</span>' + '<br />' +
                                '<span>' +
                                this.tooltipList[params[0].dataIndex]
              '</span>'
              return data
            }
          },
          xAxis: {
            data: this.ljXUplist
          },
          legend: {
            // icon: "circle",
            x: 'right', // 可设定图例在左、右、居中
            y: 'top', // 可设定图例在上、下、居中
            // 可设定图例[距上方距离，距右方距离，距下方距离，距左方距离]
            data: ['上传速率', '下载速率'],
            itemHeight: 6, // 改变圆圈大小
            // itemWidth: 14, //图标宽
            // itemHeight: 10, //图标高
            itemGap: 40, // 间距/
            formatter: function (name) {
              return name
            },
            textStyle: {
              color: '#666',
              // marginLeft:'-120px',
              fontSize: 14
            }
          },
          yAxis: {
            min: 0,
            max: Math.max(...this.ljDownLoadList) > Math.max(...this.ljUpdateList) ? this.compute(Math.ceil(Math.max(...this.ljDownLoadList))) : this.compute(Math.ceil(Math.max(...this.ljUpdateList))),
            interval: Math.max(...this.ljDownLoadList) > Math.max(...this.ljUpdateList) ? this.compute(Math.ceil(Math.max(...this.ljDownLoadList))) / 4 : this.compute(Math.ceil(Math.max(...this.ljUpdateList))) / 4,
            axisLabel: {
              formatter: Math.max(...this.ljDownLoadList) > Math.max(...this.ljUpdateList) ? '{value}' : '{value}'
            }
          },
          series: [
            {
              type: 'line', // 增加type字段
              name: '上传速率',
              data: this.ljUpdateList,
              itemStyle: {
                normal: {
                  color: '#9D6FF9',
                  lineStyle: {
                    // 线的颜色
                    color: '#9D6FF9'
                  }
                }
              }
            },
            {
              type: 'line', // 增加type字段
              name: '下载速率',
              data: this.ljDownLoadList,
              itemStyle: {
                normal: {
                  color: '#F2994A',
                  lineStyle: {
                    // 线的颜色
                    color: '#F2994A'
                  }
                }
              }
            }
          ]
        })
      }
    },
    // 过滤数据
    // 过滤连接点资源数据
    filterZx (GetTitleName, item) {
      this.tooltipList = []
      if (GetTitleName == 'cpu_usage_rate') {
        // cpu使用率
        this.ljXCpulist = []
        this.ljCpuList = []
        item.Values.forEach((itm, index) => {
          this.ljXCpulist.push(getYMDHMS(itm[0], 1))
          this.tooltipList.push(getYMDHMS(itm[0], 2))
          this.ljCpuList.push(Number(itm[1]))
        })
      } else if (GetTitleName == 'memory_usage') {
        // 内存使用量
        this.ljXNclist = []
        let syl = []
        let max = 0 // 最大值
        item.Values.forEach((itm, index) => {
          this.ljXNclist.push(getYMDHMS(itm[0], 1))
          this.tooltipList.push(getYMDHMS(itm[0], 2))
          syl.push(Number(itm[1]))
        })
        max = Math.max(...syl)
        this.commonFilter(syl, max, 1)
      } else if (GetTitleName == 'network_receive_rate') {
        // 网卡下载速率
        this.ljXDolist = []
        let syl = []
        let max = 0
        item.Values.forEach((itm, index) => {
          this.ljXDolist.push(getYMDHMS(itm[0], 1))
          this.tooltipList.push(getYMDHMS(itm[0], 2))
          syl.push(Number(itm[1] * 8))
        })
        max = Math.max(...syl)
        this.downMaxVal = max
        this.commonFilter(syl, max, 2)
      } else if (GetTitleName == 'network_transmit_rate') {
        // 网卡上传速率
        this.ljXUplist = []
        let syl = []
        let max = 0
        item.Values.forEach((itm, index) => {
          this.ljXUplist.push(getYMDHMS(itm[0], 1))
          this.tooltipList.push(getYMDHMS(itm[0], 2))
          syl.push(Number(itm[1]) * 8)
        })
        max = Math.ceil(Math.max(...syl))
        this.upMaxVal = max
        this.commonFilter(syl, max, 3)
      } else if (GetTitleName == 'storage_usage') {
        // 存储使用量
        this.ljXCclist = []
        let syl = []
        let max = 0
        item.Values.forEach((itm, index) => {
          this.ljXCclist.push(getYMDHMS(itm[0], 1))
          this.tooltipList.push(getYMDHMS(itm[0], 2))
          syl.push(Number(itm[1]))
        })
        max = Math.max(...syl)
        this.commonFilter(syl, max, 4)
      }
    },
    commonFilter (syl, max, Index) {
      // this.xzMax=this.scMax='';
      let everyList = []
      let setUnit = ''
      let qq = 1000
      let q = 1024
      let tb = max / 1024 / 1024 / 1024 / 1024
      let gb = max / 1024 / 1024 / 1024
      let mb = max / 1024 / 1024
      let kb = max / 1024
      if (gb > qq) {
        syl.forEach(itm => {
          everyList.push(itm / 1024 / 1024 / 1024 / 1024)
          setUnit = 'T'
        })
      } else if (mb > qq) {
        syl.forEach(itm => {
          everyList.push(itm / 1024 / 1024 / 1024)
          setUnit = 'GB'
        })
      } else if (kb > qq) {
        syl.forEach(itm => {
          everyList.push(itm / 1024 / 1024)
          setUnit = 'MB'
        })
      } else if (max > qq) {
        syl.forEach(itm => {
          everyList.push(itm / 1024)
          setUnit = 'kb'
        })
      } else {
        everyList = syl
        setUnit = 'b'
      }
      if (Index == 1) {
        this.ljNcList = everyList
        this.ncMax = setUnit
      } else if (Index == 2) {
        this.ljDownLoadList = everyList
        this.xzMax = setUnit + '/s'
      } else if (Index == 3) {
        this.ljUpdateList = everyList
        this.scMax = setUnit + '/s'
      } else if (Index == 4) {
        this.ljCcList = everyList
        this.ccMax = setUnit
      }
      if (this.xzMax == 'T/s' && this.scMax == 'b/s') {
        this.ljUpdateList = everyList.map(item => {
          return item / 1024 / 1024 / 1024 / 1024
        })
        this.scMax = 'T/s'
      } else if (this.xzMax == 'T/s' && this.scMax == 'kb/s') {
        this.ljUpdateList = everyList.map(item => {
          return item / 1024 / 1024 / 1024
        })
        this.scMax = 'T/s'
      } else if (this.xzMax == 'T/s' && this.scMax == 'MB/s') {
        this.ljUpdateList = everyList.map(item => {
          return item / 1024 / 1024
        })
        this.scMax = 'T/s'
      } else if (this.xzMax == 'T/s' && this.scMax == 'GB/s') {
        this.ljUpdateList = everyList.map(item => {
          return item / 1024
        })
        this.scMax = 'T/s'
      } else if (this.xzMax == 'GB/s' && this.scMax == 'b/s') {
        this.ljUpdateList = everyList.map(item => {
          return item / 1024 / 1024 / 1024
        })
        this.scMax = 'GB/s'
      } else if (this.xzMax == 'GB/s' && this.scMax == 'kb/s') {
        this.ljUpdateList = everyList.map(item => {
          return item / 1024 / 1024
        })
        this.scMax = 'GB/s'
      } else if (this.xzMax == 'GB/s' && this.scMax == 'MB/s') {
        this.ljUpdateList = everyList.map(item => {
          return item / 1024
        })
        this.scMax = 'GB/s'
      } else if (this.xzMax == 'MB/s' && this.scMax == 'b/s') {
        this.ljUpdateList = everyList.map(item => {
          return item / 1024 / 1024
        })
        this.scMax = 'MB/s'
      } else if (this.xzMax == 'MB/s' && this.scMax == 'kb/s') {
        this.ljUpdateList = everyList.map(item => {
          return item / 1024
        })
        this.scMax = 'MB/s'
      } else if (this.xzMax == 'kb/s' && this.scMax == 'b/s') {
        this.ljUpdateList = everyList.map(item => {
          return item / 1024
        })
        this.scMax = 'kb/s'
      }

      if (this.scMax == 'T/s' && this.xzMax == 'b/s') {
        this.ljDownLoadList = everyList.map(item => {
          return item / 1024 / 1024 / 1024 / 1024
        })
        this.xzMax = 'T/s'
      } else if (this.scMax == 'T/s' && this.xzMax == 'kb/s') {
        this.ljDownLoadList = everyList.map(item => {
          return item / 1024 / 1024 / 1024
        })
        this.xzMax = 'T/s'
      } else if (this.scMax == 'T/s' && this.xzMax == 'MB/s') {
        this.ljDownLoadList = everyList.map(item => {
          return item / 1024 / 1024
        })
        this.xzMax = 'T/s'
      } else if (this.scMax == 'T/s' && this.xzMax == 'GB/s') {
        this.ljDownLoadList = everyList.map(item => {
          return item / 1024
        })
        this.xzMax = 'T/s'
      } else if (this.scMax == 'GB/s' && this.xzMax == 'b/s') {
        this.ljDownLoadList = everyList.map(item => {
          return item / 1024 / 1024 / 1024
        })
        this.xzMax = 'GB/s'
      } else if (this.scMax == 'GB/s' && this.xzMax == 'kb/s') {
        this.ljDownLoadList = everyList.map(item => {
          return item / 1024 / 1024
        })
        this.xzMax = 'GB/s'
      } else if (this.scMax == 'GB/s' && this.xzMax == 'MB/s') {
        this.ljDownLoadList = everyList.map(item => {
          return item / 1024
        })
        this.xzMax = 'GB/s'
      } else if (this.scMax == 'MB/s' && this.xzMax == 'b/s') {
        this.ljDownLoadList = everyList.map(item => {
          return item / 1024 / 1024
        })
        this.xzMax = 'MB/s'
      } else if (this.scMax == 'MB/s' && this.xzMax == 'kb/s') {
        this.ljDownLoadList = everyList.map(item => {
          return item / 1024
        })
        this.xzMax = 'MB/s'
      } else if (this.scMax == 'kb/s' && this.xzMax == 'b/s') {
        this.ljDownLoadList = everyList.map(item => {
          return item / 1024
        })
        this.xzMax = 'kb/s'
      }
    }
  }
}
export default zxt
