<template>
    <Modal
        v-model="visible"
        width="620px"
        title="字段样例"
        :footer-hide="true"
        >
        <div class="wrap">
            <div class="code-wrap">
                <div>{"ES_HOST":"************ , ES_PORT":"9200"}</div>
                <div>"elasticsearch": {</div>
                <div>"protocol": "http" ,</div>
                <div>"host": "************:9200",</div>
                <div>"ingest_nodes": ["************:9200"],</div>
                <div>"user": "elastic",</div>
                <div>"pass": password"</div>
                <div>},</div>
            </div>
            <div class="field-desc">
                <span>字段逐一说明：</span>
                <div v-for="item in fieldDescription" :key="item.key" class="field">
                    <div class="field-title">
                        {{item.title}}
                    </div>
                    ：
                    <div class="field-desc">
                        {{item.desc}}
                    </div>
                </div>

            </div>
        </div>
    </Modal>
</template>
<script>
export default {
  data () {
    return {
      visible: false,
      fieldDescription: [{
        key: 'F1',
        title: 'protocol',
        desc: '协议，用于连接到 Elasticsearch的（默认：http）。例中的协议为http'
      }, {
        key: 'F2',
        title: 'host',
        desc: '主机地址或者域名。例中的主机为************:9200'
      }, {
        key: 'F3',
        title: 'ingest_nodes',
        desc: '节点地址，至少一个。例中的节点为["************:9200"]'
      }, {
        key: 'F4',
        title: 'user',
        desc: '用户名，在 Elasticsearch 配置时定义的。例中的用户名为elastic'
      }, {
        key: 'F5',
        title: 'pass',
        desc: '密码，在 Elasticsearch 配置时定义的。例中的密码为password'
      }]
    }
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
.wrap{
    padding:10px 25px 10px;
    .code-wrap{
        background-color:#eee;
        margin-bottom: 20px;
        padding: 15px 40px;
    }
    .field-desc{
        &>span{
            font-size: 16px;
            font-weight: bold;
        }
        .field{
            display: flex;  
            margin-top: 10px;  
            .field-title{
                background-color: #eee;
                padding: 2px 10px;
            }
        }
    }
}
</style>
