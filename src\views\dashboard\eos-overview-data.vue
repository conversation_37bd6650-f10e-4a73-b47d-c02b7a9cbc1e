<template>
  <div ref="imgSizeBox">
    <Card :padding="0" style="background: #f2f6fd;border:none">
      <!-- <p class="title"><span class="bs"></span>平台概览</p> -->

      <Row :gutter="18" style="padding-top:10px;margin-left:5px;padding-bottom:10px;">
        <i-col :xs="12" :md="12" :lg="6" style="padding-left:0;padding-right:0">
          <div class="content-con">
            <img class="imgs" :src="back1">
            <div class="content-title">
              <p class="count-title" :style="inforCardData[0].params.unit ? '' : ''">{{ inforCardData[0].title }}</p>
              <span class="count-to-number">{{inforCardData[0].count}}</span>
              <!-- <div class="tip" :style="getWidth(inforCardData[0].params.val)" v-if="inforCardData[0].params.unit">
                <div class="inner" style="background-color:#34cab4">
                </div>
                <div class="inner1">
                  <span>{{inforCardData[0].params.unit}}</span>
                </div>
              </div> -->
            </div>
          </div>
        </i-col>
        <i-col :xs="12" :md="12" :lg="6" style="padding-left:0;padding-right:0">
          <div class="content-con">
            <img class="imgs" :src="back2">
            <div class="content-title">
              <p class="count-title" :style="inforCardData[1].params.unit ? '' : ''">{{ inforCardData[1].title }}</p>
              <span class="count-to-number">{{inforCardData[1].count}}</span>
              <!-- <div class="tip" :style="getWidth(inforCardData[1].params.val)" v-if="inforCardData[1].params.unit">
                <div class="inner" style="background-color:#66698e">
                </div>
                <div class="inner1">
                  <span>{{inforCardData[1].params.unit}}</span>
                </div>
              </div> -->
            </div>
          </div>
        </i-col>
        <i-col :xs="12" :md="12" :lg="6" style="padding-left:0;padding-right:0">
          <div class="content-con">
            <img class="imgs" :src="back3">
            <div class="content-title">
              <p class="count-title" :style="inforCardData[2].params.unit ? '' : ''">{{ inforCardData[2].title }}</p>
              <span class="count-to-number">{{inforCardData[2].count}}</span>
              <!-- <div class="tip" :style="getWidth(inforCardData[2].params.val)" v-if="inforCardData[2].params.unit">
                <div class="inner">
                </div>
                <div class="inner1">
                  <span>{{inforCardData[2].params.unit}}</span>
                </div>
              </div> -->
            </div>
          </div>
        </i-col>
        <i-col :xs="12" :md="12" :lg="6" style="padding-left:0;padding-right:0">
          <div class="content-con">
            <img class="imgs" :src="back4">
            <div class="content-title">
              <p class="count-title" :style="inforCardData[3].params.unit ? '' : ''">{{ inforCardData[3].title }}</p>
              <span class="count-to-number">{{inforCardData[3].count}}</span>
              <!-- <div class="tip" :style="getWidth(inforCardData[3].params.val)" v-if="inforCardData[3].params.unit">
                <div class="inner" style="background-color:#92a0c1">
                </div>
                <div class="inner1">
                  <span>{{inforCardData[3].params.unit}}</span>
                </div>
              </div> -->
            </div>
          </div>
        </i-col>
      </Row>
    </Card>
    <Card :padding="0" style="margin-top:12px; background: #f2f6fd;">
      <!-- 节点状态 -->
      <Row justify="space-between" :gutter="24">
        <Col style=" background: #fff;padding:20px 20px 0 20px; box-sizing: border-box;margin-left: 12px;width: 24%;">
        <div class="map-wrap">
          <div class="map-title">
            <div class="eos">
              节点状态
            </div>

          </div>
          <div class="box" ref="annulusRef" id="annulus"></div>
          <div style="text-align:right;">
            <b>网络状态:</b>
            <span v-if="normalType">
              正常
              <Icon type="ios-radio-button-on" style="color:#52c41a" />
            </span>
            <span v-else>
              异常
              <Icon type="ios-radio-button-on" style="color:#FF4D4F" />
            </span>
          </div>
        </div>
        </Col>
        <!--交易详情 -->
        <Col style="width: 74.8%;">
        <div class="map-wrap" style="background: #fff;padding:20px 0 20px 20px; box-sizing: border-box;height:100%">
          <div class="map-title">
            <div class="eos">
              <div>交易历史</div>
              <div class="eosMaor" @click="getMore">查看更多》</div>
            </div>
          </div>

          <div v-show="transferDataCharts" class="boxLine" ref="lineRef" id="linsE"></div>
          <div class="null" v-show='!transferDataCharts'>暂无数据</div>
        </div>
        </Col>
      </Row>
    </Card>

  </div>
</template>

<script>
import { getBrowserMain } from '@/api/data'
import { getBusiness, getNodeStatus } from '@/api/dashborad'
import { transferData, transferVal } from '@/lib/transformUnit'
import * as echarts from 'echarts'

import { numberToCurrencyNo } from '@/lib/transformUnit'
export default {
  props: ['chainId'],
  name: 'overview-data',
  components: {
  },
  data () {
    return {
      timer: null,
      left: 36,
      color: '',
      back1: require('@/assets/img/dashboard/one.png'),
      back2: require('@/assets/img/dashboard/two.png'),
      back3: require('@/assets/img/dashboard/there.png'),
      back4: require('@/assets/img/dashboard/four.png'),
      back5: require('@/assets/img/dashboard/jiaoyi.png'),
      back6: require('@/assets/img/dashboard/yingyong.png'),
      inforCardData: [
        // { title: '引擎', count: 0, unit: '条', unitVal: '', params: { unit: '', val: 0 } },
        // { title: '节点数', count: 0, unit: '个', unitVal: '', params: { unit: '', val: 0 } },
        // { title: '用户数', count: 0, unit: '个', unitVal: '', params: { unit: '', val: 0 } },
        // { title: '智能合约', count: 0, unit: '个', unitVal: '', params: { unit: '', val: 0 } },
        // { title: '交易数', count: 0, unit: '条', unitVal: '', params: { unit: '', val: 0 } },
        // { title: '应用数', count: 0, unit: '个', unitVal: '', params: { unit: '', val: 0 } }

        { title: '区块高度', count: 0, unit: '', unitVal: '', params: { unit: '', val: 0 } },
        { title: '交易总数', count: 0, unit: '', unitVal: '', params: { unit: '', val: 0 } },
        { title: '智能合约数', count: 0, unit: '', unitVal: '', params: { unit: '', val: 0 } },
        { title: '链账户数', count: 0, unit: '', unitVal: '', params: { unit: '', val: 0 } },

      ],

      option: {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return params.name
          }
        },
        legend: {
          itemWidth: 6,
          itemHeight: 6,
          icon: 'circle',
          x: 'center',
          y: 'center',
          orient: 'vertical', //设置图例排列纵向显示
          align: 'left', //设置图例中文字位置在icon标识符的右侧
          // left: '50%',
          top: '50%',
          itemGap: 10, //设置图例之间的间距
          padding: [0, 0, 0, 0], //设置图例与圆环图之间的间距
        },
        color: ['#52C41A', '#FF4D4F',],
        title: {
          text: '',
          // 副标题
          // subtext: `正常:100 \n 异常:10`,
          // 主副标题间距
          // itemGap: 40,
          x: 'center',
          y: 'center',
          top: '30%',
          // 主标题样式
          textStyle: {
            fontSize: '20',
            color: 'black'
          },
          // 副标题样式
          subtextStyle: {
            fontSize: '20',
            fontWeight: '800',
            color: 'black'
          }
        },
        series: [
          {
            // name: 'Access From',
            type: 'pie',
            radius: ['80%', '90%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 16,
                fontWeight: 'bold'
              }
            },

            labelLine: {
              show: false
            },
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            data: [

            ]
          }
        ],



      },
      optionLine: {
        grid: {
          left: '12%',   // 左边距
          right: '0',  // 右边距
          top: '15%',    // 上边距
          bottom: '10%'  // 下边距
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            return params[0].name + '<br/>' + '交易：' + numberToCurrencyNo(params[0].value);
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          axisTick: {
            alignWithLabel: true
          },
          data: []
        },
        yAxis: {
          type: 'value',
          name: '单位（笔）',
          axisLine: {
            show: false,
            onZero: false
          }
        },
        series: [
          {
            data: [],
            type: 'line',
            smooth: true,
            itemStyle: {
              color: '#519FF2'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(213, 226, 253,0.8)'
                },
                {
                  offset: 1,
                  color: 'rgba(213, 226, 253,0.3)'
                }
              ])
            },
            // lineStyle: {
            //   // 线条加阴影
            //   // 设置阴影颜色
            //   shadowColor: '#3D5DF4',
            //   // 设置阴影沿x轴偏移量为10
            //   shadowOffsetX: 10,
            //   // 设置阴影沿y轴偏移量为10
            //   shadowOffsetY: 10,
            //   // 设置阴影的模糊大小
            //   shadowBlur: 8,
            //   // 设置线条渐变色
            //   // color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            //   //   { offset: 0, color: '#94C2FD00' },
            //   //   { offset: 0.2, color: '#3D5DF4' },
            //   //   { offset: 1, color: '#5B8FF900' }
            //   // ])
            // },

            // areaStyle: {
            //   normal: {
            //     color: {
            //       x: 0,
            //       y: 0,
            //       x2: 0,
            //       y2: 1,
            //       colorStops: [{
            //         offset: 0,
            //         color: "#519FF2" // 0% 处的颜色
            //       }, {
            //         offset: 0.7,
            //         color: "#519FF2" // 100% 处的颜色
            //       }],
            //       globalCoord: false // 缺省为 false
            //     }
            //   }
            // },
          }
        ],

      },
      mychart: '',
      mychartLine: '',
      normalType: false,
      transferDataCharts: false

    }
  },
  computed: {
  },
  methods: {
    getMore () {
      this.$router.push({
        name: 'browser_index',
      })
    },
    imgload () {
      if (this.$refs.imgSize) {
        this.$emit('getHeight', this.$refs.imgSize.offsetHeight)
      }
    },
    getWidth (val) {
      return `width: ${1.2 + val}vw`
    },
    getInfoCardData (count, i) {
      this.inforCardData[i].count = count
      this.inforCardData[i].unitVal = transferVal(count).unit + this.inforCardData[i].unit
      this.inforCardData[i].params.unit = transferData(this.inforCardData[i].count).unit
      this.inforCardData[i].params.val = transferData(this.inforCardData[i].count).val
    },

    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    // 查询平台概览数据
    getDashboardData (chainId) {
      if (chainId != 0) {
        getBrowserMain(chainId).then(res => {
          if (res.code === '00000') {
            this.getInfoCardData(res.data.blockHeight, 0)
            this.getInfoCardData(res.data.tradeCount, 1)
            this.getInfoCardData(res.data.contractCount, 2)
            this.getInfoCardData(res.data.accountCount, 3)

          } else {
            // this.getInfoCardData(0, 0)
            // this.getInfoCardData(0, 1)
            // this.getInfoCardData(0, 2)
            // this.getInfoCardData(0, 3)
            this.inforCardData = [
              { title: '区块高度', count: 0, unit: '', unitVal: '', params: { unit: '', val: 0 } },
              { title: '交易总数', count: 0, unit: '', unitVal: '', params: { unit: '', val: 0 } },
              { title: '智能合约数', count: 0, unit: '', unitVal: '', params: { unit: '', val: 0 } },
              { title: '链账户数', count: 0, unit: '', unitVal: '', params: { unit: '', val: 0 } },

            ],
              this.msgInfo('error', res.message, true)
          }
        }).catch(error => {
          this.inforCardData = [
            { title: '区块高度', count: 0, unit: '', unitVal: '', params: { unit: '', val: 0 } },
            { title: '交易总数', count: 0, unit: '', unitVal: '', params: { unit: '', val: 0 } },
            { title: '智能合约数', count: 0, unit: '', unitVal: '', params: { unit: '', val: 0 } },
            { title: '链账户数', count: 0, unit: '', unitVal: '', params: { unit: '', val: 0 } },

          ],
            this.msgInfo('error', error.message, true)
        })
      }

    },
    // 节点状态
    getNode (id) {
      getNodeStatus(id).then(res => {
        if (res.code === '00000') {
          this.option.title.text = res.data.nodeTotal + '个'
          let dataArr = [
            {
              name: '正常：' + res.data.enableNode,
              value: res.data.enableNode
            },
            {
              name: '异常：' + res.data.disableNode,
              value: res.data.disableNode
            }
          ]
          this.normalType = res.data.disableNode == '0'
          this.option.series[0].data = dataArr
          this.mychart.setOption(this.option)
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getBusinessFun (chainId) {
      if (chainId != 0) {
        getBusiness(chainId, 'ONE_WEEK').then(res => {
          if (res.code === '00000') {
            if (res.data.xaxis.length > 0 || res.data.series.length > 0) {
              let dataTime = res.data.xaxis.map(item => {
                return item.split(' ')[0]
              })
              this.optionLine.xAxis.data = dataTime
              this.optionLine.series[0].data = res.data.series[0].data
              this.transferDataCharts = true

              this.mychartLine.setOption(this.optionLine)
            } else {
              this.optionLine.xAxis.data = []
              this.optionLine.series[0].data = []
              this.mychartLine.setOption(this.optionLine)
              this.transferDataCharts = false
            }

          } else {
            this.optionLine.xAxis.data = []
            this.optionLine.series[0].data = []
            this.mychartLine.setOption(this.optionLine)
            this.transferDataCharts = false
            this.msgInfo('error', res.message, true)
          }
        }).catch(error => {
          this.optionLine.xAxis.data = []
          this.optionLine.series[0].data = []
          this.mychartLine.setOption(this.optionLine)
          this.transferDataCharts = false
          this.msgInfo('error', error.message, true)
        })
      }

    }
  },
  watch: {
    chainId: {
      handler (newVal, oldVal) {
        this.getNode(newVal)
        this.getBusinessFun(newVal)
        this.getDashboardData(newVal)
      },
      deep: true,
      immediate: true
    },

  },
  mounted () {
    window.onresize = () => {
      this.imgload()
    }


    this.mychart = echarts.init(this.$refs.annulusRef, null, { renderer: 'svg' }) // 获取mapbox盒子
    this.mychartLine = echarts.init(this.$refs.lineRef, null, { renderer: 'svg' }) // 获取mapbox盒子
    // this.getNode('347')
    // this.getBusinessFun('347')


    window.addEventListener('resize', () => {
      // 重新渲染图表
      this.mychart.resize();
      this.mychartLine.resize();
    });

  },

}
</script>

<style lang="less" scoped>
.contract-more {
  border-left: 4px solid #19c3a0;
}
.title {
  height: 18px;
  font-weight: bold;
  text-indent: 4px;
  line-height: 18px;
  font-size: 16px;
  font-family: "Microsoft YaHei";
  font-weight: bold;
  color: #333333;
  opacity: 1;
  margin-left: 30px;
  margin-top: 30px;
}
.bs {
  float: left;
  width: 6px;
  height: 18px;
  background: #19c3a0;
  opacity: 1;
  border-radius: 3px;
}
.common {
  float: left;
  display: table;
  text-align: center;
}
.size {
  width: 100%;
  height: 100%;
}
.middle-center {
  vertical-align: middle;
}
/deep/.ivu-col {
  // display: flex;
  // justify-content: center;
  padding-left: 20px;
}

.content-con {
  // float: left;
  display: flex;
  // justify-content: center;
  .middle-center;
  text-align: center;
  align-items: center;
  // position: relative;
  box-sizing: border-box;
  padding-left: 15px;
  margin-right: 20px;
  height: 120px;
  min-height: 150px;
  background: #fff;
  border-radius: 10px;
  .content-title {
    z-index: 2;
    // text-align: left;
    position: absolute;
    color: black;
    top: 60%;
    left: 45%;
    transform: translate(-25%, -50%);
    .count-title {
      font-size: 1vw;
      font-family: "Microsoft YaHei";
      font-weight: 400;
      color: black;
      // text-shadow: -1px -1px 1px rgba(103, 67, 10, 0.25);
      opacity: 0.8;
    }
    .count-to-number {
      font-size: 1.6vw;
      font-family: "D-DIN";
      font-weight: normal;
    }
    .tip {
      width: 90px;
      height: 1px;
      border-top: 1px solid #e2e1e1;
      margin-top: -0.5vw;
      opacity: 0.7;
      .inner {
        background-color: #f37441;
        width: 5px;
        height: 6px;
        border: 1px solid #e2e1e1;
        position: relative;
        left: 1vw;
        top: -4px;
        transform: rotate(45deg);
        border-right: 0px;
        border-bottom: 0px;
      }
      .inner1 {
        margin-top: -8px;
        font-size: 8px;
        transform: scale(0.8);
        color: #e2e1e1;
        margin-left: 0.4vw;
      }
    }
  }
}
.imgs {
  width: 5vw;
  // height: 180px;
  max-width: 100px;
  max-height: 100px;
  height: 5vw;
  // max-height:90%;
  // max-width:90%;
  // box-sizing: border-box;
  // padding-right: 10px;
}
.ivu-card-shadow {
  box-shadow: none !important;
}
/deep/.ivu-card:hover {
  box-shadow: none !important;
  .ivu-card.ivu-card-shadow:hover {
    box-shadow: none !important;
  }
}
.map-title {
  // margin: 10px 0 0 20px;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: calc(50% - 7.5px);
    left: 0;
    width: 6px;
    height: 16px;
    background: #19c3a0;
    opacity: 1;
    border-radius: 3px;
  }
  .eos {
    margin-left: 10px;
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    line-height: 21px;
    color: #333333;
    opacity: 1;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding-right: 20px;
    .eosMaor {
      color: cornflowerblue;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
    }
  }
}
.box {
  width: 25vh;
  height: 25vh;
  margin: 30px auto;
  color: #a3a3df;
}
.boxLine {
  width: 59vw;
  height: 300px;
  // margin: auto;
  color: #a3a3df;
}

.null {
  text-align: center;
  height: 300px;
  line-height: 300px;
}
</style>
