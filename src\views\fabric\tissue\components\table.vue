<template>
    <div>
        <div>
            <div class="node-list">
                <div  class="node-list-nav">
                    <div class="nan-item">
                        <!-- <el-row v-if="orderdata.Type == 'orderer'" class="nav-box">
                            <el-col :span="3"><div class="">
                                <el-popover trigger="hover" placement="top">
                                        <p>{{orderdata.Name}}</p>
                                        <div slot="reference" class="name-wrapper">
                                        <span slot="reference">{{checkName(orderdata.Name)}}</span>
                                        </div>
                                    </el-popover>
                                </div>
                                </el-col>
                            <el-col :span="2"><div class="">{{orderdata.ImageTag}}</div></el-col>
                            <el-col :span="4"><div class="">{{orderdata.CreateTime.slice(0,10)}}</div></el-col>
                            <el-col :span="2"><div class="">{{orderdata.NodeName}}</div></el-col>
                            <el-col :span="5">
                                <div class="">
                                    <div v-for="(data,idx) in orderdata.ExternalAccess" :key="idx">
                                        <div v-if="data.Ip && data.Port != '0'">
                                            {{data.Ip}}:{{data.Port}}
                                        </div>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="2"><div class="">{{orderdata.RestartCount}}</div></el-col>
                            <el-col :span="3">
                                <div :class="orderdata.Status == '0'? 'status' : orderdata.Status == '1'?'wait': 'waring'">
                                    <span>{{orderdata.Status | status}}</span>
                                </div>
                            </el-col>
                            <el-col v-if="orderdata.Type == 'orderer'" :span="3">
                                <div class="edit">
                                    <span v-for="(msg,idx) in operationArrs" :key="idx" @click="editClick(orderdata,msg.code)">{{msg.name}}</span>
                                    </div>
                            </el-col>
                            <el-col v-else :span="3">
                                <div class="operaBtn">
                                    <span v-for="(msg,idx) in operationArr" :key="idx" @click="editClick(orderdata,msg.code)">{{msg.name}}</span>
                                    </div>
                            </el-col>
                        </el-row> -->

                        <el-row v-if="orderdata.Type != 'orderer' && orderdata.Type != 'peer'" class="nav-box">
                            <el-col :span="6">
                              <div class="">
<!--                                <el-popover trigger="hover" placement="top">-->
                                        <p>{{orderdata.Name}}</p>
<!--                                        <div slot="reference" class="name-wrapper">-->
<!--                                        <span slot="reference">{{checkName(orderdata.Name)}}</span>-->
<!--                                        </div>-->
<!--                                    </el-popover>-->
                            </div>
                            </el-col>
                            <el-col :span="3"><div class="">{{orderdata.ImageTag}}</div></el-col>
                            <el-col :span="3"><div class="">{{orderdata.CreateTime.slice(0,10)}}</div></el-col>
                            <el-col :span="3"><div class="">{{orderdata.NodeName}}</div></el-col>
                           <!-- <el-col :span="3">
                                <div class="">
                                    <div v-for="(data,idx) in orderdata.ExternalAccess" :key="idx">
                                        <div v-if="data.Ip && data.Port != '0'">
                                            {{data.Ip}}:{{data.Port}}
                                        </div>
                                        &lt;!&ndash; {{data.Ip}}:{{data.Port}} &ndash;&gt;
                                    </div>
                                </div>
                            </el-col>-->
                            <el-col :span="2"><div class="">{{orderdata.RestartCount}}</div></el-col>
                            <el-col :span="3">
                                <div class="tag" :class="orderdata.Status == '0'? 'green' : orderdata.Status == '1'?'yellow': 'gray'">
                                  {{orderdata.Status | status}}
                                </div>
                            </el-col>
                             <el-col  :span="4">
                                <div class="edit">
                                    <!-- v-if="msg.code == '04'" -->
                                    <span class="handle-btn" v-for="(msg,idx) in operationArrs" :key="idx" @click="editClick(orderdata,msg.code)" >{{msg.name}}</span>
                                    </div>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { calcCostMixin } from '@/utils/mixin'
export default {
    mixins: [calcCostMixin],
    props: {
        orderdata: {
            type: Object,
            default() {
                return {}
            }
        },
        operationArr: {
            type: Array
        },
        operationArrs: {
            type: Array
        }
    },
    data () {
        return {

        }
    },
    methods: {
        // 编辑fun
        editClick (msg,code) {
            this.$emit('editData', msg, code)
        }
    },
    filters: {
        status(value) {
            if (!isNaN(value)) {
                let index = ['0','1','2'].indexOf(value)
                return index >= 0 && ['运行中','等待中','不可用'][index]
            }
            return value
        }
    }
}
</script>
<style lang="less" scoped>
@import "../../../../styles/tissue.less";
</style>
