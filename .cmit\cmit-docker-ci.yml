name: Docker Image CI

on:
  push:
    branches: ["master", "RC", "iteration-s13"]
  pull_request:
    branches: ["master", "RC", "iteration-s13"]

jobs:
  build:
    name: cmit-docker-ci
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: set env TAG
        run: echo "TAG=$(date +'%Y%m%d%H%M%S')" >> $GITHUB_ENV
      - name: docker login
        run: docker login artifactory.dep.devops.cmit.cloud:20101 -u ${{env.USER_NAME}} -p ${{env.PASSWORD}}
      - name: Build the Docker image
        run: docker build . --file ${{env.PROJECT_PATH}}/panzhou-ci.Dockerfile --label commit-id=${{env.GIT_COMMIT_ID}} --label branch=${{env.GIT_BRANCH}} --tag artifactory.dep.devops.cmit.cloud:20101/cmbaas_dev_2025/cmbaas-portal-ui:${{env.TAG}} --build-arg PROJECT_PATH=${{env.PROJECT_PATH}}
      - name: docker push
        run: docker push artifactory.dep.devops.cmit.cloud:20101/cmbaas_dev_2025/cmbaas-portal-ui:${{env.TAG}}
      - name: Remove local docker image
        run: docker image rm -f artifactory.dep.devops.cmit.cloud:20101/cmbaas_dev_2025/cmbaas-portal-ui:${{env.TAG}}
