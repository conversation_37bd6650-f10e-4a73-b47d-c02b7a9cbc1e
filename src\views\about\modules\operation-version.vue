<!--
    新增/编辑 版本
-->
<template>
  <Modal
    v-model="visible"
    :title="(isAdd ? '新增' : '编辑') + '迭代版本'"
    @on-ok="ok('form')"
    @on-cancel="cancel('form')"
    @on-visible-change="open"
  >
    <Form ref="form" :model="form" :rules="ruleValidate" :label-width="120">
      <FormItem label="版本名称：" prop="versionNum">
        <Input v-model="form.versionNum" placeholder="请输入" @on-blur="val=>{inputChange(val,'versionNum')}"></Input>
      </FormItem>
      <FormItem label="迭代时间：" prop="iterationDate">
        <DatePicker
          type="date"
          placeholder="请选择迭代时间"
          v-model="form.iterationDate"
          style="width: 100%"
        ></DatePicker>
      </FormItem>
      <FormItem label="迭代说明：" prop="iterationDesc">
        <Input
          @on-blur="(val)=>{inputChange(val,'iterationDesc')}"
          v-model="form.iterationDesc"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 5 }"
          placeholder="如：上线XXX功能"
          :maxlength="500"
          show-word-limit
        ></Input>
      </FormItem>
    </Form>
    <span slot="footer">
      <Button type="text" @click="cancel('form')">取 消</Button>
      <Button type="primary" @click="submitData('form')">提 交</Button>
    </span>
  </Modal>
</template>
<script>
import { addOrEditIterationVersion } from '@/api/data'
export default {
  watch: {},
  props: {
    editData: Object | String
  },
  data () {
    return {
      isAdd: true, // 是否新增
      visible: false,
      form: {
        versionNum: '',
        iterationDate: '',
        iterationDesc: ''
      },
      ruleValidate: {
        versionNum: [
          { required: true, message: '请输入版本名称！', trigger: 'blur' }
        ],
        iterationDate: [
          {
            required: true,
            type: 'date',
            message: '请选择迭代时间！',
            trigger: 'change'
          }
        ],
        iterationDesc: [
          { required: true, message: '请输入迭代说明！', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    submitData (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let date = new Date(this.form.iterationDate)
          let month = (date.getMonth() + 1) > 9 ? (date.getMonth() + 1) : '0' + (date.getMonth() + 1)
          let day = date.getDate() > 9 ? date.getDate() : '0' + date.getDate()
          let iterationDate = date.getFullYear() + '-' + month + '-' + day
          let params = {
            ...this.form,
            iterationDate
          }
          addOrEditIterationVersion(params)
            .then((res) => {
              if (res.code === '00000') {
                this.$emit('iterationVersionList')
                this.visible = false
                this.msgInfo('success', res.message, true)
              } else {
                this.dataList = []
                this.msgInfo('error', res.message, true)
              }
            })
            .catch((error) => {
              this.msgInfo('error', error.message, true)
            })
        }
      })
    },
    cancel (name) {
      this.visible = false
    },
    open (visable) {
      if (visable) {
        this.isAdd = this.editData === 'add'
        if (!this.isAdd) {
          this.form = { ...this.editData }
        } else {
          this.form = {
            id: '',
            versionNum: '',
            iterationDate: '',
            iterationDesc: ''
          }
        }
      } else {
        this.$refs['form'].resetFields()
      }
    },
    inputChange (e, name) {
      let value = e.currentTarget._value
      this.form[name] = value.trim()
    }
  }
}
</script>
