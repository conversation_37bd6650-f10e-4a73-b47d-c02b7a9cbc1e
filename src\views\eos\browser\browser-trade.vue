<template>
  <div class="trade">
      <div style="padding:20px 0px 40px 10px;">
        <div style="float:left;">
        <Select filterable :class="className" @on-open-change="selectClassName" v-model="chain.chainId" placeholder="选择目标链" @on-change="changeChain" style="width:280px">
            <Option v-for="item in chainIdList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
            <Option :value="chain.chainId" :label="chain.chainId"  :disabled="true" v-if="pageParam.pageIndex < pages && chainIdList.length>0" style="text-align:center">
              <span @mouseover="handleReachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
            </Option>
            <Option :value="chain.chainId" :label="chain.chainId"  :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
              <span style="font-size:8px;">已加载全部</span>
            </Option>
          </Select>
      </div>
      <div style="float:right;">
          <Input
            prefix="ios-search"
              type="text"
              style="width:400px;margin-right:-1px;"
              v-model="searchData"
              placeholder="区块高度/交易哈希/账户名称"
              @keyup.enter.native="browserBlur(searchData)">
          </Input>
          <Button type="primary" style="padding:2px;width:75px;height:31px;" @click="browserBlur(searchData)"> 搜索</Button>
      </div>
      </div>
      <div v-show="!isShow">
      <Row style="padding-top:15px;">
        <Col span="24">
          <Card class="back">
            <div class="title"><div class="bs"></div><div>交易信息</div></div>
            <p class="title-2">交易哈希:<span class="title-3">{{ trxId }}</span></p>
            <p class="title-2">区块高度:<span class="title-1" style="color:#3D73EF;">{{ blockNum }}</span></p>
            <p class="title-2">时间:<span class="title-1">{{ timestamp }}</span></p>
            <p class="title-2">计算资源(/us):<span class="title-1">{{ cpuUsage }}</span></p>
            <p class="title-2">网络资源(/byte):<span class="title-1">{{ netUsage }}</span></p>
          </Card>
        </Col>
      </Row>
      <Card style="margin-top:20px;">
        <div class="title"><div class="bs"></div><div>操作</div></div>
        <edit-table-mul :columns="columns" v-model="actionDetailData" :key="transferKey" style="margin:2px;"></edit-table-mul>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;padding-top:10px;"/>
      </Card>
    </div>
    <div v-show="isShow" class="show-style">
        <img class="imgs" :src="showUrl">
        <p class="msg-style">{{showMsg}}</p>
        <Button type="primary" style="padding:2px;height:31px;" @click="browserhome()">返回EOS浏览器</Button>
    </div>
  </div>
</template>

<script>
import EditTableMul from '_c/edit-table-mul'
import JsonViewer from 'vue-json-viewer'
import { getTradeInfo, getChainIdList } from '@/api/data'
import { isBlockNum, isAccount, isTrxId } from '@/lib/check'
import { mapActions } from 'vuex'
export default {
  name: 'browser_trade',
  components: {
    EditTableMul,
    JsonViewer
  },
  data () {
    return {
      copyable: { copyText: '复制', copiedText: '已复制' },
      searchData: '',
      hisPath: 'browser_index',
      goFlag: true,
      transferKey: 0,
      trxId: this.$route.query.trxId || '',
      blockNum: 0,
      timestamp: '',
      cpuUsage: 0,
      netUsage: 0,
      columns: [
        {
          title: 'action时间',
          key: 'actionTime',
          minWidth: 80
        },
        {
          title: 'action名称',
          key: 'actionName'
        },
        {
          title: '合约账户',
          key: 'contract'
        },
        {
          title: '授权信息',
          key: 'authorization'
        },
        {
          title: '数据',
          key: 'data',
          minWidth: 200,
          render: (h, params) => {
            if (params.row.data) {
              return h('div', [
                h(JsonViewer, {
                  props: {
                    value: params.row.data,
                    copyable: this.copyable,
                    expandDepth: 0
                  },
                  style: { background: 'transparent' }
                })
              ])
            }
          }
          // tooltip: true,
          // tooltipmaxwidth: 400
        }
      ],
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      actionDetailData: [],
      isShow: false,
      showUrl: require('@/assets/img/null.png'),
      showMsg: '',
      searchBack: '',
      chain: JSON.parse(this.$route.query.chain) || {
        chainId: 0,
        chainName: ''
      },
      className: 'select-style1',
      pageParam: { pageTotal: 0, pageSize: 60, pageIndex: 1 },
      pages: 0,
      chainIdList: [],
      imgUrl: require('@/assets/img/arrow.png'),
      size: 0,
      routeChain: JSON.parse(this.$route.query.chain) || {
        chainId: 0,
        chainName: ''
      }
    }
  },
  computed: {
    getHeight: function (value) {
      if (this.actionDetailData.length === 0) {
        return 88
      } else if (this.actionDetailData.length < 5) {
        return 88 + 48 * (this.actionDetailData.length - 1)
      } else {
        return 88 + 48 * 5
      }
    }
  },
  methods: {
    browserhome () {
      this.$router.push({
        name: 'browser_index'
      })
    },
    ...mapActions([
      'updateChain'
    ]),
    browserBlur (val) {
      if (isBlockNum(val)) {
        this.$router.push({
          name: 'browser_block',
          query: {
            blockNum: val,
            chain: JSON.stringify(this.chain),
            tag: true
          }
        })
      } else if (isAccount(val)) {
        this.$router.push({
          name: 'browser_chain',
          query: {
            accountName: val,
            chain: JSON.stringify(this.chain),
            tag: true
          }
        })
      } else if (isTrxId(val)) {
        this.trxId = val
        this.goFlag = false
        this.getTradeData(val)
      } else {
        if (val === '') {
          this.msgInfo('warning', '未输入任何查询信息，请检查！', true)
          this.showMsg = '未输入任何查询信息，请检查！'
        } else {
          this.msgInfo('warning', '输入信息有误，请检查！', true)
          this.showMsg = '输入信息有误，请检查！'
        }
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTradeData(this.trxId)
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTradeData(this.trxId)
    },
    getTradeData (value) {
      getTradeInfo(this.chain.chainId, value, this.tablePageParam.pageIndex, this.tablePageParam.pageSize).then(res => {
        if (res.code === '00000') {
          this.isShow = false
          this.actionDetailData = res.data.actions ? res.data.actions : []
          this.trxId = res.data.trxId ? res.data.trxId : '暂无记录'
          this.blockNum = res.data.blockNum ? res.data.blockNum : 0
          this.timestamp = res.data.timestamp ? res.data.timestamp : '暂无记录'
          this.cpuUsage = res.data.cpuUsage ? res.data.cpuUsage : 0
          this.netUsage = res.data.netUsage ? res.data.netUsage : 0
          this.tablePageParam.pagetotal = res.data.total
          ++this.transferKey
        } else {
          this.isShow = true
          if (res.code === 'A1001') {
            this.msgInfo('error', res.message, true)
            this.showMsg = '交易ID[' + value + ']，' + '该交易信息在[' + this.chain.chainName + ']上不存在！'
          } else {
            this.msgInfo('error', res.message, true)
            this.showMsg = '查询交易ID[' + value + ']，' + res.message
          }
        }
      }).catch(error => {
        this.isShow = true
        this.showMsg = error.message
        this.msgInfo('error', error.message, true)
      })
    },
    reback () {
      this.$router.push({
        name: 'browser_index'
      })
    },
    getChain (data) {
      this.chain = data
      if (this.trxId === '') {
        this.browserBlur(this.searchData)
      } else {
        this.browserBlur(this.trxId)
      }
    },
    selectClassName () {
      this.className = this.className === 'select-style1' ? 'select-style2' : 'select-style1'
    },
    handleReachBottom () {
      if (this.pageParam.pageIndex < this.pages) {
        // this.pageParam.pageSize += 3
        this.pageParam.pageIndex += 1
        // this.updatePageSize(this.pageParam.pageSize)
        this.getChainList(true)
      }
    },
    getChainList (flag) {
      getChainIdList(this.pageParam).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          if (flag) {
            let index = res.data.records.findIndex(item => {
              if (item.chainId === this.routeChain.chainId) {
                return true
              }
            })
            if (index !== -1) {
              res.data.records.splice(index, 1)
            }
            this.chainIdList.push.apply(this.chainIdList, res.data.records)
          } else {
            this.chainIdList = res.data.records && res.data.records.length > 0 ? res.data.records : []
            if (this.chain.chainId === 0 && this.chainIdList[0].chainId) {
              this.chain.chainId = this.chainIdList[0].chainId
              this.chain.chainName = this.chainIdList[0].chainName
            } else {
              this.pushChainId()
            }
          }
          this.pageParam = {
            pageTotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          this.size = this.chainIdList.length
          this.pages = res.data.pages
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    pushChainId () {
      let flag = false
      for (let i in this.chainIdList) {
        if (this.chainIdList[i].chainId === this.routeChain.chainId) {
          flag = true
          break
        }
      }
      if (!flag) {
        this.chainIdList.push(this.routeChain)
      }
    },
    changeChain (val) {
      if (this.chain.chainId !== 0) {
        this.getChainName(this.chain.chainId)
        this.getTradeData(this.trxId)
      }
    },
    getChainName (value) {
      for (let item in this.chainIdList) {
        if (this.chainIdList[item].chainId === value) {
          this.chain.chainName = this.chainIdList[item].chainName
        }
      }
    }
  },
  mounted () {
    if (this.$route.query.trxId && this.$route.query.chain) {
      if (this.$route.query.tag) {
        this.searchData = this.$route.query.trxId
      }
      this.getChainList()
      this.getTradeData(this.$route.query.trxId)
    } else {
      this.reback()
    }
  },
  beforeRouteEnter (to, from, next) {
    if (from.name) {
      next(vm => {
        vm.hisPath = from.name
      })
    } else {
      next('/browser_index')
    }
  },
  destroyed () {
    this.updateChain(this.chain)
  }
}
</script>

<style lang="less" scoped>
.size{
  font-weight: bold;
  font-size:16px;
}
.trade{
  height:100%;
  .title{
    margin:10px 0 15px 10px;
    .size;
    height:18px;;
    font-family: 'Microsoft YaHei';
    line-height: 18px;
    color: #333333;
    vertical-align: middle;
  }
  .bs{
    float:left;
    width: 6px;
    height: 18px;
    background: #19C3A0;
    opacity: 1;
    border-radius: 3px;
    margin-right:6px
   }
  .title-2{
      font-size:14px;
       margin:10px 0 10px 10px;
       font-weight:300;
       color:#9B9B9B;
     }
  .title-3{
      margin-left:10px;
      font-size:1.5vw;
      font-weight:bold;
      color:#11173D;
      font-family: 'Microsoft YaHei';
      vertical-align: middle;
      }
  .title-1{
      font-size:14px;
      margin-left:10px;
      font-weight:300;
      color:#333333;
  }
  .back{
    background-image:url('../../../assets/img/browser/trade.png');
    background-repeat:no-repeat;
    background-size:100% 100%;
  }
  .show-style{
  display: table;
  text-align: center;
  vertical-align: middle;
  margin:0 auto;
  position: relative;
  padding:8%;
  .msg-style{
    color:#b7b8b9;
    font-size:12px;
  }
}
}
</style>
<style lang="less" scoped>
/deep/.select-style1{
   .ivu-select-arrow{
     padding:9px;
     margin-right:-9px;
     background-color: #57a3f3;
     color: #fff;
     border-radius: 0 5px 5px 0;
     transition: none;
   }
   .ivu-icon-ios-arrow-down:before{
     color:#fff;
   }
}
/deep/.select-style2{
   .ivu-select-arrow{
     padding:9px;
     margin-right:-9px;
     background-color: #57a3f3;
     color: #fff;
     border-radius: 5px 0px 0px 5px;
     transition: none;
   }
   .ivu-icon-ios-arrow-down:before{
     color:#fff;
   }
}
/deep/.ivu-input{
  border-radius: 4px 0 0 4px;
}
/deep/.ivu-btn{
   border-radius: 0 4px 4px 0;
}
</style>
