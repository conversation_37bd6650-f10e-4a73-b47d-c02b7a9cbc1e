<template>
  <div class="analysis">
    <div style="margin-bottom:16px;">
      <h3 style="line-height: 36px;padding:10px 20px;">交易数据</h3>
      <p style="margin: 0 2%;">
        <span style="display: block;float:left;background-color: rgb(230, 247, 255);min-height: 60px;line-height:30px;border-left: 5px solid #3D73EF;padding:0 45px 0 10px;">
          <span style="font-size:12px;">交易次数</span><br />
          <span style="font-size:24px;font-weight:bold;">{{total}}</span>
        </span>
        <span style="min-height: 60px;line-height:60px;margin-left:40px;">
          <span class="time-scope" style="margin-right:5px;font-weight:bold">时间范围</span>
          <Select v-model="timeScope" style="width:90px;" filterable @on-change="changeTimeScope">
            <Option v-for="item in timeScopeList" :value="item.key" :key="item.key">{{ item.value }}</Option>
          </Select>
        </span>
      </p>
      <div style="margin: 10px 2%;">
        <p><b style="line-height:36px;">最近交易</b><a @click="handleClick" style="float:right;">更多></a> </p>
        <edit-table-mul style="margin: 10px 0;" :columns="columns" v-model="tableData" :key="transferKey"></edit-table-mul>
      </div>
      <div style="margin:0 2%;">
        <b style="line-height:36px;">资源使用情况</b>
        <ul style="display: flex;">
          <li style="width:30%;margin-right:30px;">
            <p><span style="margin-right:20px;color:#ccc;">CPU</span>{{resourcesObj.cpuUsed}} / {{resourcesObj.cpuMax}} μs</p>
            <Progress stroke-color="#34c9ca" :percent="Math.ceil(resourcesObj.cpuUsed / resourcesObj.cpuMax * 100)" hide-info />
          </li>
          <li style="width:30%;margin-right:30px;">
            <p><span style="margin-right:20px;color:#ccc;">RAM</span>{{resourcesObj.ramUsage}} / {{resourcesObj.ramQuota}} Byte</p>
            <Progress stroke-color="#1890ff" :percent="Math.ceil(resourcesObj.ramUsage / resourcesObj.ramQuota * 100)" hide-info />
          </li>
          <li style="width:30%;margin-right:30px;">
            <p><span style="margin-right:20px;color:#ccc;">NET</span>{{resourcesObj.netUsed}} / {{resourcesObj.netMax}} Byte</p>
            <Progress stroke-color="#f0b90b" :percent="Math.ceil(resourcesObj.netUsed / resourcesObj.netMax * 100)" hide-info />
          </li>
        </ul>
      </div>
    </div>
    <div>
      <Row style="padding:20px 20px;">
        <Col offset="1">
        <h3 style="line-height: 36px;">合约调用数据</h3>
        </Col>
      </Row>
      <Row style="padding:10px 20px 0 20px;">
        <Col span="4" offset="1" class="selectStyle2"><b>合约调用量</b></Col>
        <Col span="6" offset="12" class="selectStyle2">
        <b>时间范围</b>
        <Select v-model="timeScope1" style="width:90px;margin-left:5px;" filterable @on-change="getAccountNameAnalysisList">
          <Option v-for="item in timeScopeList" :value="item.key" :key="item.key">{{ item.value }}</Option>
        </Select>
        </Col>
      </Row>
      <Row>
        <div class="areaClass">
          <Areas v-if="areasData && areasData.length > 0" areasHeight="50vh" areasWidth="120vh" :areasData="areasData" :areasXaxis="areasXaxis" :padValue="40"></Areas>
          <div class="areaNone" v-else>
            <img :src="imagesurl" style="margin: 0 auto;display: block;">
            <p style="text-align:center">暂无数据</p>
          </div>
        </div>
      </Row>
      <Row style="padding:15px 0 0 20px;">
        <Col offset="1" span="4"><b>合约调用单位</b></Col>
        <Col offset="7" span="5">
        <Select class="border-none" v-model="contractName" style="width:160px;" filterable @on-change="getCallerDetail([contractName])">
          <Option v-for="item in pieData" :value="item.name" :key="item.name">{{ item.name + '调用量' }}</Option>
        </Select>
        </Col>
        <Col span="6" offset="1">
        <span><b>时间范围</b></span>
        <Select v-model="timeScope2" style="width:90px;margin-left:5px;" filterable @on-change="getContractCaller">
          <Option v-for="item in timeScopeList" :value="item.key" :key="item.key">{{ item.value }}</Option>
        </Select>
        </Col>
      </Row>
      <Row>
        <div class="left-area">
          <Pie ref="pie" :pieData="pieData" pieHeight="50vh" pieWidth="65vh" :type="1" v-if="pieData && pieData.length > 0" @hoverEvent="changeContractName"></Pie>
          <div v-else>
            <img :src="imagesurl" style="margin: 0 auto;display: block;">
            <p style="text-align:center">暂无数据</p>
          </div>
        </div>
        <div class="right-area">
          <Areas :areasData="areasData1" :areasXaxis="areasXaxis1" areasHeight="50vh" areasWidth="64vh" v-if="areasData1 && areasData1.length > 0" :padValue="10"></Areas>
          <div v-else>
            <img :src="imagesurl" style="margin: 0 auto;display: block;">
            <p style="text-align:center">暂无数据</p>
          </div>
        </div>
      </Row>
    </div>
  </div>
</template>

<script>
import { getResourcesObj, getRecentList, getAccountNameAnalysis, getContractCallerList, getContractCallerDetail } from '@/api/data'

import Pie from '_c/pie'
import Areas from '_c/areas'
import EditTableMul from '_c/edit-table-mul'
import { mapGetters } from 'vuex'

export default {
  name: 'contract_analysis',
  components: { Pie, Areas, EditTableMul },
  props: ['chainId', 'chainAccountId', 'chainAccountName', 'timeScopeList', 'chainName'],
  computed: {
    ...mapGetters(['getDict'])
  },
  data () {
    return {
      transferKey: 0,
      timeScope: '',
      timeScope1: '',
      timeScope2: '',
      imagesurl: require('@/assets/img/null.png'),
      resourcesObj: {
        // 'accountId': 1, 'accountName': 'acct1', 'cpuUsed': 10, 'cpuMax': 20, 'netUsed': 30, 'netMax': 40, 'ramUsage': 50, 'ramQuota': 60
      },
      tableData: [
        // { 'timestamp': '2021-04-26 18:30:11', 'action': 'hi', 'caller': 'test2', 'permission': 'perm', 'data': { 'proxy': '', 'producers': ['produceri'], 'voter': 'produceri' } },
        // { 'timestamp': '2021-04-26 18:30:11', 'action': 'hi', 'caller': 'test2', 'permission': 'perm', 'data': { 'proxy': '', 'producers': ['produceri'], 'voter': 'produceri' } }
      ],
      columns: [
        { key: 'timestamp', title: '时间', tooltip: true },
        { key: 'action', title: 'action名称', tooltip: true },
        { key: 'caller', title: '调用者', tooltip: true },
        { key: 'permission', title: '权限', tooltip: true },
        { key: 'data', title: '数据', tooltip: true }
      ],
      total: 0,
      areasData: [],
      areasXaxis: [],
      areasData1: [],
      areasXaxis1: [],
      pieData: [],
      contractName: '',
      chain: {
        chainId: 0,
        chainName: ''
      }
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    handleClick () { this.$router.push({ name: 'browser_chain', query: { accountName: this.chainAccountName, chain: JSON.stringify(this.chain) } }) },
    getAccountNameAnalysisList (value) {
      getAccountNameAnalysis(this.chainId, [this.chainAccountName], this.timeScope1).then(res => {
        if (res.code !== '00000') {
          this.areasData = []
          this.msgInfo('warning', res.message, true)
        } else {
          this.total = res.data.total
          this.areasData = res.data.series
          this.areasXaxis = res.data.xaxis
        }
      }).catch(error => {
        // console.log('getAccountNameAnalysisList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    getContractCaller () {
      getContractCallerList(this.chainId, this.chainAccountName, this.timeScope2).then(res => {
        if (res.code !== '00000') this.msgInfo('warning', res.message, true)
        else {
          if (res.data.callerList) {
            this.pieData = res.data.callerList
            if (this.pieData[0].name) {
              this.contractName = this.pieData[0].name
              this.contractNameList = this.pieData[0].nameList
              this.getCallerDetail()
            }
          } else {
            this.pieData = []
            this.contractNameList = []
            this.areasData1 = []
          }
        }
      }).catch(error => {
        // console.log('getContractCaller.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    getCallerDetail (contractNameList = this.contractNameList) {
      getContractCallerDetail(this.chainId, this.chainAccountName, contractNameList, this.timeScope2).then(res => {
        if (res.code !== '00000') {
          this.areasData1 = []
          // this.msgInfo('warning', res.message, true)
        } else {
          this.areasData1 = res.data.series
          this.areasXaxis1 = res.data.xaxis
        }
      }).catch(error => {
        console.log('getCallerDetail.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    getTimeScopeList () {
      // 查询所有时间范围
      // this.$store.dispatch('getOptions', 'TIME_SCOPE')
      //   .then((result) => {
      //     if (Object.prototype.toString.call(result) === '[object Boolean]') {
      //       this.timeScopeList = this.getDict.TIME_SCOPE
      //     } else {
      //       this.timeScopeList = result
      //     }
      //     if (this.timeScopeList && this.timeScopeList[0].value) {
      //       this.timeScope = this.timeScopeList[0].key
      //       this.timeScope1 = this.timeScopeList[0].key
      //       this.timeScope2 = this.timeScopeList[0].key
      //       this.getAccountNameAnalysisList()
      //       this.getContractCaller()
      //     }
      //   })
      //   .catch(err => { console.log(err, 'err') })
      // 组件里传递过来的数据不能变更
      if (this.timeScopeList) {
        if (this.timeScopeList.length > 0) {
          this.timeScope = this.timeScopeList[0].key
          this.timeScope1 = this.timeScopeList[0].key
          this.timeScope2 = this.timeScopeList[0].key
          this.getAccountNameAnalysisList()
          this.getContractCaller()
        }

      }
      // console.log('getTimeScopeList===>', this.timeScopeList)
      // if (this.timeScopeList[0].timeScopeDesc) {
      //   this.timeScope = this.timeScopeList[0].timeScope
      //   this.timeScope1 = this.timeScopeList[0].timeScope
      //   this.timeScope2 = this.timeScopeList[0].timeScope
      //   this.getAccountNameAnalysisList()
      //   this.getContractCaller()
      // }
    },
    getResources () {
      getResourcesObj(this.chainId, this.chainAccountId).then(res => {
        // console.log('getResources===>', res)
        if (res.code !== '00000') { this.msgInfo('warning', '合约调用量获取失败，需安装或检查数据导出组件配置，若有疑问请联系客服人员', true) } else {
          this.resourcesObj = res.data
          this.getRecent()
        }

      }).catch(error => {
        console.log('getResources.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    getRecent () {
      getRecentList(this.chainId, this.chainAccountId).then(res => {
        if (res.code !== '00000') this.msgInfo('warning', '合约调用量获取失败，需安装或检查数据导出组件配置，若有疑问请联系客服人员', true)
        else this.tableData = res.data.recentTrades
      }).catch(error => {
        console.log('getResources.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    changeContractName (data) {
      this.contractName = data.name
      this.getCallerDetail([this.contractName])
    },
    changeTimeScope () {
      getAccountNameAnalysis(this.chainId, [this.chainAccountName], this.timeScope).then(res => {
        if (res.code !== '00000') {
          this.total = 0
          this.msgInfo('warning', res.message, true)
        } else {
          this.total = res.data.total
        }
      }).catch(error => {
        // console.log('getAccountNameAnalysisList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    }
  },
  mounted () {
    this.getTimeScopeList()
    this.getResources()
    this.chain = { chainId: this.chainId, chainName: this.chainName }
  }
}
</script>

<style lang="less">
.analysis > div {
  background-color: #fff;
  padding-bottom: 20px;
  border-radius: 6px;
  .ivu-select-selection {
    border: 1px solid #4b98eb;
  }
  .ivu-select-input {
    color: #57a3f3;
  }
  .ivu-icon-ios-arrow-down:before {
    color: #57a3f3;
  }
  .border-none .ivu-select-selection {
    border: none !important;
  }
}
.middle-center {
  margin: 0 auto;
  vertical-align: middle;
}
.areaClass {
  text-align: center;
  display: table;
  .middle-center;
  /deep/.areas {
    .middle-center;
  }
}
.commom {
  text-align: center;
  display: table;
}
.left-area {
  float: left;
  .commom;
  width: 50%;
  height: 50%;
  /deep/.pie {
    .middle-center;
  }
}
.right-area {
  float: right;
  height: 50%;
  padding-left: 10px;
  .commom;
  width: 50%;
  /deep/.areas {
    .middle-center;
  }
}
</style>
