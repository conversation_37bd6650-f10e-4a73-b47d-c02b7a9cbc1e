<template>
  <Modal v-model="visible" title="Elasticsearch 配置" :mask-closable="false" @on-cancel="onCancelModal" :styles="{top: '30px'}">
    <Form ref="form" :model="form" :rules="rules" :label-width="120">
      <FormItem label="协议：" prop="protocol">
        <Input v-model="form.protocol" placeholder="请输入协议,如http或https!"></Input>
      </FormItem>
      <FormItem label="主机：" prop="host">
        <Input v-model="form.host" placeholder="请输入主机地址或者域名!"></Input>
      </FormItem>
      <FormItem label="节点：" prop="ingestNodes">
        <Input v-model="form.ingestNodes" placeholder="多个节点请使用英文逗号分割，如：1, 2！"></Input>
      </FormItem>
      <FormItem label="用户名：" prop="user">
        <Input v-model="form.user" placeholder="请输入用户名!"></Input>
      </FormItem>
      <FormItem label="密码：" prop="pass" v-show="passShow">
        <Input v-model="form.pass" placeholder="请输入密码!" type="password"></Input>
      </FormItem>
      <FormItem label="确认密码：" prop="passCheck" v-show="passShow">
        <Input v-model="form.passCheck" placeholder="请输入确认密码!" type="password"></Input>
      </FormItem>
    </Form>
    <Form v-show="!passShow" ref="formPass" :model="formPass" :rules="passRules" :label-width="120">
      <FormItem label="是否修改密码：">
        <i-switch v-model="formPass.modifyPass" @on-change="change" size="large">
          <span slot="open">是</span>
          <span slot="close">否</span>
        </i-switch>
      </FormItem>
      <FormItem label="密码：" prop="pass" v-show="formPass.modifyPass">
        <Input v-model="formPass.pass" placeholder="请输入密码!" type="password"></Input>
      </FormItem>
      <FormItem label="确认密码：" prop="passCheck" v-show="formPass.modifyPass">
        <Input v-model="formPass.passCheck" placeholder="请输入确认密码!" type="password"></Input>
      </FormItem>
    </Form>
    <div class="sample-wrap">
      <span @click="$refs.fieldSample.visible = true">如有疑问？查看字段样例</span>
      <Button type="primary" ghost @click="sampleHandle">连接测试</Button>
    </div>
    <Modal v-model="modal" width="360" :closable="false">
      <div class="success-wrap" v-if="isSuccess === 'cancel'">
        确定退出吗？编辑内容将不会保留
      </div>
      <div class="error-wrap" v-else-if="isSuccess === 'error'">
        <Icon type="ios-close-circle" />
        <span>连接测试不通过</span>
        <p>{{errorMsg}}</p>
      </div>
      <div class="error-wrap" v-else-if="isSuccess === 'success'">
        <Icon type="ios-checkmark-circle" style="color:#19be6b" />
        <span>连接测试成功</span>
      </div>
      <div slot="footer">
        <Button size="small" v-if="isSuccess === 'cancel' " @click="modal=false">取消</Button>
        <Button type="primary" size="small" @click="childModelOk(isSuccess)">确认</Button>
      </div>
    </Modal>
    <span slot="footer">
      <Button type="text" @click="cancel">取 消</Button>
      <Button type="primary" @click="submitData">提 交</Button>
    </span>
    <field-sample ref="fieldSample" />
  </Modal>
</template>
<script>
import FieldSample from './fieldSample.vue'
import { upsert, connectionTest, configQuery } from '@/api/data'
import { encryptedData } from '@/lib/encrypt'
export default {
  props: {
    chainId: String
  },
  components: {
    FieldSample
  },
  data () {
    let that = this
    const passCheck = (rule, value, callback) => {
      if (that.form.pass !== value) {
        callback(new Error('两次输入密码不一致！'))
      } else {
        callback()
      }
    }
    const passCheck2 = (rule, value, callback) => {
      if (that.formPass.pass !== value) {
        callback(new Error('两次输入密码不一致！'))
      } else {
        callback()
      }
    }
    const host = (rule, value, callback) => {
      // let regIp =  /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/
      // let reg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/
      if (!value) {
        callback(new Error('请输入主机地址或者域名'))
      } else {
        callback()
      }
      // else if(regIp.test(value)){
      //     callback();
      // }else{
      //      callback('请输入ip地址+端口号！');
      // }
    }
    return {
      passShow: true,
      visible: false,
      modal: false,
      isSuccess: 'cancel', // cancel 有内容时点击取消 success/error 链接测试是否通过
      pass: '',
      form: {
        passCheck: '',
        chainId: '', // 链idlong
        protocol: '', // 请求协议：http、https
        host: '', // 主机地址或者域名
        ingestNodes: '', // 节点地址，至少一个
        user: '', // 用户名
        pass: '', // 密码，
        id: null
      },
      formPass: {
        pass: '',
        passCheck: '',
        modifyPass: false
      },
      errorMsg: '请检查所填消息',
      rules: {
        protocol: [
          { required: true, message: '请输入协议,如http或https', trigger: 'blur' }
        ],
        host: [
          { required: true, message: '请输入主机地址或者域名,如：************:9200', trigger: 'blur' },
          { validator: host, trigger: 'blur' }
        ],
        ingestNodes: [
          { required: true, message: '请输入节点，多个节点请使用英文逗号分割，如：1, 2', trigger: 'blur' }
        ],
        user: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        pass: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        passCheck: [
          { required: true, message: '请输入确认密码', trigger: 'blur' },
          { validator: passCheck, trigger: 'blur' }
        ]
      },
      passRules: {
        pass: [
          { required: true, message: '请输入密码!', trigger: 'blur' }
        ],
        passCheck: [
          { required: true, message: '请输入确认密码!', trigger: 'blur' },
          { validator: passCheck2, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /**
         * 提交
         */
    submitData () {
      let params = {}
      if (!this.formPass.modifyPass && !this.passShow) {
        this.form.pass = this.pass
        this.form.passCheck = this.pass
      }
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.formPass.modifyPass) {
            this.$refs['formPass'].validate((valid) => {
              if (valid) {
                params = {
                  protocol: this.form.protocol,
                  host: this.form.host,
                  userName: this.form.user,
                  chainId: this.chainId,
                  ingestNodes: this.form.ingestNodes,
                  pass: encryptedData(this.formPass.pass, localStorage.publicKey),
                  modifyPass: true,
                  id: this.form.id
                }
                this.updateData(params)
              }
            })
          } else {
            let params = {
              protocol: this.form.protocol,
              host: this.form.host,
              userName: this.form.user,
              chainId: this.chainId,
              ingestNodes: this.form.ingestNodes,
              pass: this.form.pass === this.pass ? '' : encryptedData(this.form.pass, localStorage.publicKey),
              modifyPass: this.form.pass !== this.pass,
              id: this.form.id
            }
            this.updateData(params)
          }
        }
      })
    },
    updateData (params) {
      upsert(params).then((res) => {
        if (res.code === '00000') {
          this.msgInfo('success', res.message, true)
          this.visible = false
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((err) => {
        this.msgInfo('error', err.message, true)
      })
    },
    /** 密码判断以及加密 */
    encrypt () {
      if (!this.formPass.modifyPass) {
        this.form.pass = ''
        this.form.passCheck = ''
      }
    },
    /**
         * @点击取消
         * @isEmpty 判断form是否所有控件为空
         */
    cancel () {
      let isEmpty = false
      for (let item in this.form) {
        if (this.form[item]) {
          isEmpty = true
        }
      }
      if (!isEmpty) {
        this.$refs['form'].resetFields()
        this.visible = false
      } else {
        this.isSuccess = 'cancel'
        this.modal = true
      }
    },
    onCancelModal () {
      this.$refs['form'].resetFields()
      this.$refs['formPass'].resetFields()
    },
    // 子弹框点击确认
    childModelOk (isSuccess) {
      if (isSuccess === 'cancel') {
        this.modal = false
        this.visible = false
        this.$refs['form'].resetFields()
      } else {
        this.modal = false
      }
    },
    /**
         * @连接测试
         */
    sampleHandle () {
      let params = {}
      if (!this.formPass.modifyPass && !this.passShow) {
        this.form.pass = this.pass
        this.form.passCheck = this.pass
      }
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.formPass.modifyPass) {
            this.$refs['formPass'].validate((valid) => {
              if (valid) {
                params = {
                  protocol: this.form.protocol,
                  host: this.form.host,
                  ingestNodes: this.form.ingestNodes,
                  userName: this.form.user,
                  pass: encryptedData(this.formPass.pass, localStorage.publicKey)
                }
                this.postConnectionTest(params)
              }
            })
          } else {
            let params = {
              protocol: this.form.protocol,
              host: this.form.host,
              ingestNodes: this.form.ingestNodes,
              userName: this.form.user,
              pass: this.form.pass === this.pass ? this.form.pass : encryptedData(this.form.pass, localStorage.publicKey)
            }
            this.postConnectionTest(params)
          }
        }
      })
    },
    postConnectionTest (params) {
      connectionTest(params).then(res => {
        if (res.code === '00000') {
          this.isSuccess = 'success'
        } else {
          this.isSuccess = 'error'
          this.errorMsg = res.message
        }
        this.modal = true
      }).catch(error => {
        this.isSuccess = 'error'
        this.errorMsg = error.message
        this.modal = true
        // this.msgInfo('error', error.message, true)
      })
    },
    /**
         * 查询es配置
         */
    getConfigQuery (chainId) {
      configQuery(chainId).then(res => {
        if (res.code === '00000') {
          this.visible = true
          if (res.data === null) {
            this.passShow = true
          } else {
            this.passShow = false
            this.form = { ...res.data, passCheck: res.data.pass }
            this.pass = res.data.pass
          }
          this.formPass.modifyPass = false
        } else {
          if (res.code === 'A1405' && res.data === null) {
            this.visible = true
            this.passShow = true
          } else {
            this.msgInfo('error', res.message, true)
            this.visible = false
          }
        }
      }).catch(error => {
        this.visible = false
        this.msgInfo('error', error.message, true)
      })
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    change (val) {
      this.$refs['formPass'].resetFields()
      this.formPass.modifyPass = val
    }
  },
  watch: {
  }
}
</script>

<style lang="less" scoped>
.modal-wrap {
  padding: 20px 0;
  // .hyperion-form{
  //     padding:0 20px 0 10px
  // }
}
.success-wrap {
  padding: 30px 30px 40px 20px;
}
.error-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 25px;
  & > i {
    font-size: 60px;
    color: red;
  }
  & > span {
    margin-top: 10px;
    font-size: 20px;
    font-weight: bold;
    color: #333;
  }
  & > p {
    margin-top: 5px;
    font-size: 14px;
    color: #999;
  }
}
.sample-wrap {
  margin-left: 120px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  & > span {
    color: #2d8cf0;
    text-decoration: underline;
    cursor: pointer;
  }
  & > button {
    margin-top: 10px;
  }
}
</style>
