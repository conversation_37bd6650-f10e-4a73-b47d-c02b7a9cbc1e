<!--
  at<PERSON><PERSON>
  新建消费者用户
  2021/10/21
-->
<template>
  <el-dialog
      class="dialog_sty new_consumer-dialog"
      :title="title"
      :visible.sync="Visible"
      width="620px"
      :modal="true"
      @opened="open"
      destroy-on-close
      :close-on-click-modal="false"
      :before-close="handleClose">
    <div class="dialog_content">
      <div class="top_tabs">
        <div class="tab_item" :class="{'active':form.fetchType==0}" @click="setTabActive(0)">定时服务用户</div>
        <div class="tab_item" :class="{'active':form.fetchType==1}" @click="setTabActive(1)">请求服务用户</div>
      </div>
      <el-form :model="form" :rules="rules" ref="newConsumerForm" label-width="120px">
        <el-form-item label="用户名称：" prop="consumerName" >
          <el-tooltip placement="top" v-if="operationState==2&&form.consumerName.length>12">
            <div slot="content" class="dsfdg" >{{form.consumerName}}</div>
            <el-input show-word-limit maxlength="60" @keyup.native="btKeyUp" v-model="form.consumerName" placeholder="例如：xxxxxx" :disabled="operationState==2"></el-input>
          </el-tooltip>
          <el-input show-word-limit maxlength="60" v-else @keyup.native="btKeyUp" v-model="form.consumerName" placeholder="例如：xxxxxx" :disabled="operationState==2"></el-input>
        </el-form-item>
        <el-form-item label="所属链：" prop="chainName" >
          <el-select v-model="form.chainName" placeholder="请选择" @change="HttpChainListChange" :disabled="operationState==2||form.isBind==1">
            <el-option :label="item.chainName" :value="item.chainName" v-for="(item,index) in HttpChainList" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="普通链账户：" prop="generalAcc" >
          <el-select @visible-change="visibleChange" v-model="form.generalAcc" placeholder="请选择" @change="HttpUserListChange" :disabled="operationState==2||form.isBind==1">
            <el-option :label="item.chainAccountName" :value="item.chainAccountId" v-for="(item,index) in HttpUserList" :key="index"></el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item label="请求合约：" prop="requestContractName" :class="{'input-or-button':operationState!=2}" v-if="form.fetchType==1">-->
<!--          <el-input v-model="requestContractName" placeholder="请选择"></el-input>-->
<!--          <el-button type="primary" @click="selectConsumerContractName('requestContractName')" v-if="operationState!=2">选 择</el-button>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="请求表名：" prop="contractTable" v-if="form.fetchType==1">-->
<!--          <el-input maxlength="60" v-model="form.contractTable" placeholder="请输入" :disabled="operationState==2"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="请求用户：" prop="userAccount" v-if="form.fetchType==1">-->
<!--&lt;!&ndash;          <el-input maxlength="60" v-model="form.userAccount" placeholder="请输入" :disabled="operationState==2"></el-input>&ndash;&gt;-->
<!--          <el-autocomplete-->
<!--            :maxlength="60"-->
<!--            clearable-->
<!--            :disabled="operationState==2"-->
<!--            class="inline-input"-->
<!--            v-model="form.userAccount"-->
<!--            :fetch-suggestions="querySearch"-->
<!--            placeholder="请输入内容"-->
<!--            @select="handleSelect"-->
<!--          ></el-autocomplete>-->
<!--        </el-form-item>-->
        <el-form-item label="消费者合约：" prop="consumerContractName" :class="{'input-or-button':operationState==0||(operationState!=2&&form.isBind==0)}">
          <el-input v-model="consumerContractName" placeholder="请选择" disabled></el-input>
          <el-button type="primary" @click="selectConsumerContractName('consumerContractName')" v-if="operationState==0||(operationState!=2&&form.isBind==0)">选 择</el-button>
          <div class="text-info" @click="showConsumerContract" v-if="operationState==0||(operationState!=2&&form.isBind==0)">合约示例</div>
        </el-form-item>
        <el-form-item label="业务描述：" :style="{'padding-bottom: 68px':form.fetchType==1}">
          <el-input show-word-limit maxlength="200" :rows="4" v-model="form.memo" placeholder="请输入" type="textarea" :disabled="operationState==2"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer" v-if="operationState!=2">
    <el-button @click="Refresh();Visible=false">取 消</el-button>
    <el-button type="primary" @click="submitForm" :loading="upLoading">确 定</el-button>
  </span>
    <SmartContractDialog @setChoiceInfo="setChoiceInfo" ref="SmartContractRef"></SmartContractDialog>
    <RequestContractDialog @setChoiceInfo="setRequestChoiceInfo" ref="RequestContractRef"></RequestContractDialog>
  </el-dialog>

</template>

<script>
import * as api from '../api'
import SmartContractDialog from './SmartContractDialog'
import RequestContractDialog from './RequestContractDialog'
export default {
  name: "newConsumerDialog",
  components: {
    SmartContractDialog,
    RequestContractDialog
  },
  data() {
    return {
      IsInit:false,
      upLoading:false,
      title:null,
      Visible:false,
      SmartContractVisible:false,
      consumerType:null,
      chainAccount:null,
      requestContractName:null,
      consumerContractName:null,
      eosChainId:null,
      chainAccountId:null,
      form:{
        "consumerContractName": "",
        "consumerContractNameJson": "",
        "consumerId": "",
        "consumerName": "",
        "consumerType": "",
        "generalAcc": "",
        "generalAccJson": "",
        "memo": "",
        "versionNum": "",
        chainId:0,
        chainIdJson:'',
        chainName:'',
        fetchType:0,
        requestContractNameJson:null,
        requestContractName:null,
        contractTable:null,
        userAccount:null
      },
      operationState:0, //0 1 2
      rules: {
        contractTable: [
          {required: true, message: '请输入用户名称', trigger: 'change'},
        ],
        requestContractName: [
          {required: true, message: '请输入用户名称', trigger: 'change'},
        ],
        consumerName: [
          {required: true, message: '请输入用户名称', trigger: 'change'},
          {pattern: /[^:/\\?*"<>|;]/g, message: '不能输入 :/\\?*"<>|;'}
        ],
        chainName: [
          {required: true, message: '请选择所属链', trigger: 'change'},
        ],
        generalAcc: [
          {required: true, message: '请选择普通链账户', trigger: 'change'},
        ],
        consumerContractName: [
          {required: true, message: '请选择消费者合约', trigger: 'change'},
        ],
        userAccount: [
          {required: true, message: '请选择请求用户', trigger: 'change'},
        ],
      },
      HttpUserList:[

      ],
      HttpChainList:[],
    restaurants:[]
    }
  },
  watch: {
    // eosChainId(){
    //   if(this.operationState==0||this.operationState==1){
    //     this.form.generalAcc = null
    //     this.form.generalAccJson = null
    //   }
    //
    //   this.getEosUserList()
    // },

  },
  mounted() {
    this.restaurants = this.loadAll();
  },
  created() {
// for (let i=0;i<20;i++){
//   this.addDataConsumer()
// }
    this.getEosList()
  },

  methods: {
    visibleChange(e){
      if(e&&!this.form.chainName){
        this.$message.warning('请先选择所属链')
        return false
      }
    },
    btKeyUp(e) {
      e.target.value = e.target.value.replace(/[:/\\?*"<>|;]/g,"");
    },
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
      };
    },
    handleSelect(item) {
      console.log(item);
    },
    loadAll() {
      return [
        { "value": "楷神", "ss": "aaaa" },
        { "value": "华川", "ss": "ffff" },
        { "value": "雪梅", "ss": "dfsd" },
        { "value": "atu", "ss": "trrtrtrt" },
      ];
    },
    showConsumerContract(){
      this.$refs.SmartContractRef.operationState = 0
      this.$refs.SmartContractRef.Visible=true
    },
    setConsumerContractName(val){
      let value=val
      value=value.split(',')[1]+'('+value.split(',')[2]+')'
      return value
    },
    selectConsumerContractName(val){
      // console.log(this.form.generalAcc)
      if(!this.form.generalAcc) return this.$message('请先选择普通链账户')
      // sourceMode:null,//0请求合约1消费者合约
      if(val=='requestContractName'){
        this.$refs.RequestContractRef.operationState = 1
        this.$refs.RequestContractRef.eosChainId = this.eosChainId
        this.$refs.RequestContractRef.Visible=true
      }else if(val=='consumerContractName'){
        this.$refs.SmartContractRef.operationState = 1
        this.$refs.SmartContractRef.eosChainId = this.eosChainId
        this.$refs.SmartContractRef.chainAccountId = this.chainAccountId //chainAccountId
        this.$refs.SmartContractRef.Visible = true
      }


    },
    setTabActive(val){
      if(this.operationState!=0) return
      this.$refs.newConsumerForm.clearValidate()
      this.form.fetchType=val
    },
    setChoiceInfo(val){
      this.form.consumerContractName= val.ChoiceValue
      let arr=[]
      arr = val.ChoiceValue.split(',')
      this.consumerContractName = arr[1]+'('+arr[2]+')'
      // this.form.chainAcc = val.ChoiceForm.ContractChainAccount
      this.form.consumerContractNameJson = val.ChoiceForm.SmartContract
      this.$refs.newConsumerForm.clearValidate()
    },
    setRequestChoiceInfo(val){
      this.form.requestContractName= val.ChoiceValue
      let arr=[]
      arr = val.ChoiceValue.split(',')
      this.requestContractName = arr[1]+'('+arr[2]+')'
      // this.form.chainAcc = val.ChoiceForm.ContractChainAccount
      this.form.requestContractNameJson = val.ChoiceForm.SmartContract
    },
    HttpChainListChange(chainName){
      // this.form.chainIdJson = JSON.stringify(this.HttpChainList[index])
      this.form.generalAccJson = ''
      this.form.generalAcc = ''
      this.form.consumerContractName = ''
      this.consumerContractName = ''
      this.form.consumerContractNameJson = ''
      this.HttpChainList.map(ele=>{
        if(ele.chainName==chainName){
          this.form.chainIdJson = JSON.stringify(ele)
          this.eosChainId = ele.chainId
        }
      })
      this.getEosUserList()
      // console.log(this.form.consumerType);
    },
    HttpUserListChange(chainAccountId){
      this.form.consumerContractName = ''
      this.consumerContractName = ''
      this.form.consumerContractNameJson = ''
      this.chainAccountId = chainAccountId
      this.HttpUserList.map(ele=>{
        if(ele.chainAccountId==chainAccountId){
          this.form.generalAccJson = JSON.stringify(ele)
        }
      })
    },
    open(){

      if(this.operationState==0){
        this.title='新建消费者用户'
        this.Refresh()
      }else if(this.operationState==1){
        this.title='编辑消费者用户'
        this.getDataConsumerDetails()

      }else {
        this.title='查看消费者用户'
        this.getDataConsumerDetails()
      }
    },
    getEosList(){
      // 所属链：
      api.getEosList(
      {
        "chainName": "",
          "engineTypeList": [],
          "pageParam": {
            "pageIndex": 1,
            "pageSize": 100
      },
        "statusKey": ""
      }
      ).then(res=>{
        this.HttpChainList=res.data.records
      })


    },
    getEosUserList(){
      api.getEosUserList(
          this.eosChainId,
          {
            "accountType": "",
            "bizType": "",
            "chainAccountName": "",
            "contractId": null,
            "pageParam": {
              "pageIndex": 1,
              "pageSize": 100
            },
            "status": "",
            "tenantName": ""
          }
      ).then(res=>{
        let arr = res.data.records
        this.HttpUserList = []
        arr.map(item=>{
          if(item.accountTypeKey=='NORMAL'){
            this.HttpUserList.push(item)
          }
        })
      })
    },
    getDataConsumerDetails(){
      api.getDataConsumerDetails(
          {consumerId:this.form.consumerId}
      ).then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.form=res.result
        this.eosChainId = res.result.chainId
        this.chainAccount=res.result.generalAcc
        if(this.form.consumerContractName){
          this.consumerContractName = this.setConsumerContractName(this.form.consumerContractName)
        }
        if(this.form.requestContractName){
          this.requestContractName = this.setConsumerContractName(this.form.requestContractName)
        }
        // this.HttpChainListChange(this.form.chainName)
        this.getEosUserList()
      })
      this.Refresh()
    },
    Refresh(){

      this.IsInit = false
      this.consumerType=null
      this.chainAccount=null
      this.consumerContractName = null
      this.requestContractName = null
      this.form={
        consumerContractName: "",
        consumerContractNameJson: "",
        consumerId: "",
        consumerName: "",
        consumerType: "",
        generalAcc: "",
        generalAccJson: "",
        memo: "",
        versionNum: "",
        chainId:0,
        chainIdJson:'',
        chainName:'',
        fetchType:0,
        requestContractNameJson:null,
        requestContractName:null,
        contractTable:null,
      }
    },
    submitForm() {
      if(this.upLoading) return
      this.$refs['newConsumerForm'].validate((valid) => {
        if (valid) {
         if(this.operationState==0){
           this.addDataConsumer()

         }else if(this.operationState==1){
           this.updateDataConsumer()
         }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    updateDataConsumer(){
      if(this.upLoading) return
      this.upLoading = true
      api.updateDataConsumer(this.form).then(res=>{
        this.upLoading = false
        if(res.code!=0) return this.$message.warning(res.msg)
        this.$message.success(res.msg)
        this.Visible=false
        this.$emit('Refresh')
        this.Refresh()
      }).catch(err=>{
        this.upLoading = false
      })

    },
    addDataConsumer(){
      if(this.upLoading) return
      this.upLoading = true
      api.addDataConsumer(this.form).then(res=>{
        this.upLoading = false
        if(res.code!=0) return this.$message.warning(res.msg)
        this.$message.success(res.msg)
        this.Visible=false
        this.$emit('Refresh')
        this.Refresh()
      }).catch(err=>{
        this.upLoading = false
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleClose(done) {
      this.Refresh()
      done();

    }
  },

}
</script>

<style lang="less" scoped>
.new_consumer-dialog{

  .dialog_content{
    max-height: 600px;
    overflow-y: scroll;
  }
}
</style>
