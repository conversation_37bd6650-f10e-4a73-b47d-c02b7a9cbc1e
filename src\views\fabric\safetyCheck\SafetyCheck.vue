<template>
  <div class="safetycheck_page" ref="everyWid">
    <div class="sc_div">
      <div class="sc_div sc_half_top">
        <ul class="imglist">
          <li v-for="item in imageList" :key="item.name">
            <img :src="item.liImgUrl" class="liImage"/>
            <img :src="item.iconImgUrl" class="iconImage"/>
            <span class="number">{{ item.number }}</span>
            <span class="name">
              {{ item.name }}
            </span>
          </li>
        </ul>
      </div>
      <div class="sc_div sc_half_top">
        <div class="sc_half1">
          <div id="pie1" v-if="pie1Show"></div>
          <div class="none" v-else ref="noThing">
            <i
              class="el-icon-loading"
              v-if="pie1paddingText == '数据请求中...'"
            ></i>
            <svg-icon icon-class="table-empty" v-else/>
            {{ pie1paddingText }}
          </div>
        </div>
        <div class="sc_half1">
          <div id="pie2" v-if="pie2Show"></div>
          <div class="none" v-else ref="noThing">
            <i
              class="el-icon-loading"
              v-if="pie2paddingText == '数据请求中...'"
            ></i>
            <svg-icon icon-class="table-empty" v-else/>
            {{ pie2paddingText }}
          </div>
        </div>
      </div>
    </div>
    <div class="sc_div">
      <div class="sc_half">
        <div class="info m2" ref="everyWid" v-if="!lineShow">
          <div class="infoTitle">
            <!-- <img :src="infoIcon" class="infoIcon" /> -->
            <span class="infotext">捕获攻击趋势图</span>
          </div>
        </div>
        <div class="centerDiv">
          <div class="sc_echarts" id="line" v-if="lineShow"></div>
          <div class="none" v-else ref="noThing">
            <i
              class="el-icon-loading"
              v-if="linePaddingText == '数据请求中...'"
            ></i>
            <svg-icon icon-class="table-empty" v-else/>
            {{ linePaddingText }}
          </div>
        </div>
      </div>
      <div class="sc_half">
        <div class="info m2" ref="everyWid" v-if="!barShow">
          <div class="infoTitle">
            <!-- <img :src="infoIcon" class="infoIcon" /> -->
            <span class="infotext">攻击操作事件监控</span>
          </div>
        </div>
        <div class="centerDiv">
          <div class="sc_echarts" id="bar" v-if="barShow"></div>
          <div class="none" v-else ref="noThing">
            <i
              class="el-icon-loading"
              v-if="barPaddingText == '数据请求中...'"
            ></i>
            <svg-icon icon-class="table-empty" v-else/>
            {{ barPaddingText }}
          </div>
        </div>
      </div>
    </div>
    <div class="info">
      <div class="infoTitle">
        <img :src="infoIcon" class="infoIcon"/>
        <span class="infotext">攻击监控</span>
      </div>
    </div>
    <SpaceLayout>
      <div slot="padding">
        <el-form :inline="true" :model="formInline" class="form-inline sel-size left btn-bt">
          <el-form-item label="链接地址：">
            <el-input v-model="formInline.url" placeholder=""></el-input>
          </el-form-item>
          <el-form-item label="是否恶意：">
            <el-select
              v-model="formInline.is_malicious"
              placeholder="请选择"
            >
              <el-option value="0" label="全部"></el-option>
              <el-option value="1" label="恶意"></el-option>
              <el-option value="2" label="正常"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" class="blue-btn" @click="searchTable">查询</el-button>
          </el-form-item>
        </el-form>
        <div class="table-wrapper">
          <el-row class="nav-box">
            <el-col :span="4">
              <div>序号</div>
            </el-col>
            <el-col :span="6">
              <div>链接地址</div>
            </el-col>
            <el-col :span="6">
              <div>检测时间</div>
            </el-col>
            <el-col :span="6">
              <div>是否恶意</div>
            </el-col>
            <!-- <el-col :span="4">
              <div>检测类别</div>
            </el-col> -->
          </el-row>
          <template v-if="tableList.length > 0">
            <div class="nan-item" v-for="(tableData,index) in tableList" :key="index">
              <el-row class="nav-box">
                <el-col :span="4">
                  <div><span>{{((pageIndex-1)*pageSize)+index+1}}</span></div><!--必须包1层div，否则字体大小不受控，最里边是span-->
                </el-col>
                <el-col :span="6">
                  <div><span>{{tableData.link}}</span></div>
                </el-col>
                <el-col :span="6">
                  <div><span>{{tableData.time}}</span></div>
                </el-col>
                <el-col :span="6">
                  <div class="sa-tag" v-if="tableData.is_malicious==2">
                    <span class="green"></span>
                    <span>正常</span>
                  </div>
                  <div class="sa-tag" v-else-if="tableData.is_malicious==1">
                    <span class="red"></span>
                    <span>恶意</span>
                  </div>
                </el-col>
                <!-- <el-col :span="4">
                  <div><span>{{tableData.url_category}}</span></div>
                </el-col> -->
              </el-row>
            </div>
          </template>
          <div class="none" v-else>
            <i class="el-icon-loading" v-if="paddingText == '数据请求中...'"></i>
            <svg-icon icon-class="table-empty" v-else/>
            {{ paddingText }}
          </div>
        </div>
        <template v-if="tableList.length > 0">
<!--          <pagination-->
<!--            @toHandleSizeChange="handleSizeChange"-->
<!--            @toHandleCurrentChange="handleCurrentChange"-->
<!--            @toJumpFirstPage="jumpFirstPage"-->
<!--            @toJumpLastPage="jumpLastPage"-->
<!--            :fTotal="total"-->
<!--            :fBtnStartDisabled="btnStartDisabled"-->
<!--            :fBtnEndDisabled="btnEndDisabled"-->
<!--            :fPageIndex="pageIndex"-->
<!--            :fZys="zys"-->
<!--          ></pagination>-->
          <Page
            :total="total"
            :current.sync="pageIndex"
            @on-change="handleCurrentChange"
            :page-size="pageSize"
            :page-size-opts="[10,20,40,60,100]"
            show-total show-elevator show-sizer
            @on-page-size-change="handleSizeChange"
            style="text-align:right;"/>
        </template>
      </div>
    </SpaceLayout>

  </div>
</template>
<script>
  import echarts from "echarts";
  import SpaceLayout from '@/components/SpaceLayout';
  import forbidden from "@/utils/comMixin.js";
  import {getSafetyData} from "@/api/baascore/safetyCheck/index";
  import {getChainPodList} from "@/api/baascore/ywResource/index";

  export default {
    mixins: [forbidden],
    components: {
      SpaceLayout
    },
    data() {
      return {
        infoIcon: require("@/assets/chainManage_images/overview/infoIcon.png"),
        lineData: {
          xData: [],
          yData: []
        },
        barData: {
          yData: ["0", "0", "0"],
          xData: ["鼠标点击", "焦点切换", "键盘输入"]
        },
        pieData1: {
          data: [
            {value: 1048, name: '3306'},
            {value: 735, name: '443'},
            {value: 580, name: '其他'}
          ],
          color: ["#63fab7", "#0cbbfd", "#a7bfe8", "#6dd5ed", "#f2c94c", "#ff3a4c"]
        },
        pieData2: {
          data: [
            {value: 1048, name: 'http'},
            {value: 735, name: 'https'},
            {value: 580, name: 'MySQL'},
            {value: 484, name: '其他'}
          ],
          color: ["#63fab7", "#0cbbfd", "#a7bfe8", "#6dd5ed", "#f2c94c", "#ff3a4c"]
        },
        tableList: [],
        imageList: [
          {
            liImgUrl: require("@/assets/chainManage_images/overview/libg1.png"),
            iconImgUrl: require("@/assets/chainManage_images/overview/icon1.png"),
            number: "0",
            name: "资产数量"
          },
          {
            liImgUrl: require("@/assets/chainManage_images/overview/libg2.png"),
            iconImgUrl: require("@/assets/chainManage_images/overview/icon2.png"),
            number: "0",
            name: "服务数量"
          },
          {
            liImgUrl: require("@/assets/chainManage_images/overview/libg3.png"),
            iconImgUrl: require("@/assets/chainManage_images/overview/icon3.png"),
            number: "0",
            name: "风险数量"
          }
        ],
        formInline: {url: '', is_malicious: '2'},
        total: 0,
        pageSize: 10,
        pageIndex: 1,
        zys: 0, //判断总页数
        btnStartDisabled: false, //用来判断首页尾页按钮是否禁用
        btnEndDisabled: false, //用来判断首页尾页按钮是否禁用
        parentClientWidth: 0,
        pie1paddingText: "数据请求中...",
        pie1Show: false,
        pie2paddingText: "数据请求中...",
        pie2Show: false,
        linePaddingText: "数据请求中...",
        lineShow: false,
        barPaddingText: "数据请求中...",
        barShow: false,
        paddingText: "数据请求中...",
        type: ''
      };
    },
    mounted() {
      let type = JSON.parse(sessionStorage.getItem('chainItem')).chainType;
      if (type) {
        this.type = type
      }
      this.checkGM();
      this.checkSY();
      this.checkURL();
      this.parentClientWidth =
        this.$refs.everyWid.clientWidth / 2 -
        (this.$refs.everyWid.clientWidth / 2) * 0.08;
    },
    watch(){
    },
    methods: {
      drawLine(line, data) {
        this.$nextTick(() => {
          let setWid = {
            width: this.parentClientWidth
          };
          // if (document.getElementById(line)) {
          //   echarts.init(document.getElementById(line)).resize(setWid);
          // }
          let charts = echarts.init(document.getElementById(line))
          let zxOption = {
            title: {
              subtext: '(单位：次)',
              text: '捕获攻击趋势图',
              align: 'left',
              textStyle: {
                color: 'black',
                fontSize: 14,
                fontWeight: 'normal',
                // rich:{
                //     A:{
                //         height:16,
                //         width:6,
                //         verticalAlign:'middle',
                //         // backgroundColor:{
                //         //     image:require('../../../assets/chainManage_images/overview/infoIcon.png'),
                //         // },
                //         // fontWeight: 'normal', //标题颜色
                //         color: 'black',
                //         fontSize: 14,
                //         padding: [-2,-14,2, 12],
                //     }
                // },

              },
            },
            tooltip: {
              show: true,
              trigger: 'axis',
              backgroundColor: "rgba(255,255,255,1)",
              borderWidth: "1", //边框宽度设置1
              borderColor: "#eee", //设置边框颜色
              textStyle: {
                color: "#666" //设置文字颜色
              },
              axisPointer: {
                type: 'line',
                lineStyle: {
                  type: 'dashed'
                }
              },

              formatter: params => {
                let data =
                  "<span>" +
                  params[0].seriesName +
                  "：" +
                  params[0].value +
                  // this.ncMax +
                  "</span> </br>" +
                  "<span>" +
                  params[0].name
                "</span> ";
                return data;
                // let data = "<span>" + params[0].name + ":" + "</span></br> " + "<span>" + params[0].value.toFixed(2); +"</span>"
                // return data;
              }
            },
            grid: {
              left: '12%',
              right: '5%',
              bottom: '20%',
              top: '25%',
            },
            // dataZoom: [
            //     {
            //         show: true,
            //         realtime: true,
            //         height: 12,
            //         bottom: 10,
            //         start: 0,
            //         end: 100,
            //     }
            // ],
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: data.xData,
              axisTick: {
                show: true
              },
              axisLine: {
                lineStyle: {
                  color: '#EDEDED',
                  width: 1,//这里是为了突出显示加上的
                }
              },
              axisLabel: {
                show: true,
                interval: 0,//代表显示所有x轴标签显示
                textStyle: {
                  color: '#666',
                  fontSize: 12      //更改坐标轴文字大小
                },
                rotate: "25"
              }
            },
            yAxis: {
              type: 'value',
              // name:'(单位：次)',
              // nameTextStyle:{
              //   color:'#999'
              // },
              // splitNumber:4,
              minInterval: 1,
              // boundaryGap : [ 0, 1],
              // 坐标刻度线
              axisTick: {
                show: false
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: '#EDEDED',
                  width: 1,//这里是为了突出显示加上的
                }
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: ['#eee'],
                  width: 1,
                  type: 'dashed'
                }
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#666',
                  fontSize: 12      //更改坐标轴文字大小
                },
              }
            },
            series: [{
              name: "捕获攻击趋势图",
              type: 'line',
              // symbol: 'circle',
              data: data.yData,
              showSymbol: false,
              // symbolSize: 0,   //设定实心点的大小
              itemStyle: {
                normal: {
                  color: "#4383EC",
                  lineStyle: {  //线的颜色
                    color: '#4383EC'
                  },
                },
              },
            }]
          }
          charts.setOption(zxOption)
          window.addEventListener('resize', () => charts.resize(this.parentClientWidth), false)
        })
      },
      drawBar(bar, data) {
        this.$nextTick(() => {
          let myChart = echarts.init(document.getElementById(bar))
          let option = {
            tooltip: {
              trigger: 'axis',
              backgroundColor: "rgba(255,255,255,1)",
              borderWidth: "1", //边框宽度设置1
              borderColor: "#eee", //设置边框颜色
              textStyle: {
                color: "#666" //设置文字颜色
              },
              axisPointer: {
                type: 'line',
                lineStyle: {
                  type: 'dashed'
                }
              },
            },
            title: {
              subtext: '(单位：次)',
              text: '攻击操作事件监控',
              align: 'left',
              textStyle: {
                // rich:{
                //     A:{
                //         height:16,
                //         width:6,
                //         verticalAlign:'middle',
                //         backgroundColor:{
                //             image:require('../../../assets/chainManage_images/overview/infoIcon.png'),
                //         },
                //         // fontWeight: 'normal', //标题颜色
                //         color: 'black',
                //         fontSize: 14,
                //         padding: [-2,-14,2, 12],
                //     }
                // },
                color: 'black',
                fontSize: 14,
                fontWeight: 'normal',
              },
            },
            grid: {
              left: '12%',
              right: '5%',
              bottom: '20%',
              top: '25%',
            },
            xAxis: {
              type: 'category',
              boundaryGap: true,
              data: data.xData,
              axisTick: {
                show: true
              },
              axisLine: {
                lineStyle: {
                  color: '#EDEDED',
                  width: 1,//这里是为了突出显示加上的
                }
              },
              axisLabel: {
                show: true,
                interval: 0,//代表显示所有x轴标签显示
                textStyle: {
                  color: '#666',
                  fontSize: 12      //更改坐标轴文字大小
                }
              }
            },
            yAxis: {
              type: 'value',
              // 坐标刻度线
              minInterval: 1,
              boundaryGap: [0, 0.1],
              axisTick: {
                show: false
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: '#EDEDED',
                  width: 1,//这里是为了突出显示加上的
                }
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: ['#eee'],
                  width: 1,
                  type: 'dash'
                }
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#666',
                  fontSize: 12      //更改坐标轴文字大小
                },
              }
            },
            series: [
              {
                // name: '直接访问',
                type: 'bar',
                barWidth: '30%',
                // barWidth: '20px',
                itemStyle: {
                  normal: {
                    color: "#f2994a"
                    // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                    //     offset: 0,
                    //     color: 'rgba(0,244,255,1)' // 0% 处的颜色
                    // }, {
                    //     offset: 1,
                    //     color: 'rgba(0,77,167,1)' // 100% 处的颜色
                    // }], false),
                    // barBorderRadius: [30, 30, 30, 30],
                  }
                },
                data: data.yData
              }
            ]
          };
          myChart.setOption(option)
          // let setWid = {
          //   width: this.parentClientWidth
          // };
          // if (document.getElementById(bar)) {
          //   echarts.init(document.getElementById(bar)).resize(setWid);
          // }
          window.addEventListener('resize', () => myChart.resize(this.parentClientWidth), false)
        })
      },
      drawPie1(pie, data, title) {
        this.$nextTick(() => {
          document.getElementById(pie).style.width = this.parentClientWidth / 2 + 'px';
          let myChart = echarts.init(document.getElementById(pie))
          let option = {
            animation: true,
            title: {
              text: title,
              left: 'center',
              bottom: 15,
              textStyle: {
                //文字颜色
                color: 'black',
                //字体风格,'normal','italic','oblique'
                // fontStyle:'normal',
                //字体粗细 'normal','bold','bolder','lighter',100 | 200 | 300 | 400...
                fontWeight: 'normal',
                //字体大小
                fontSize: 14
              }
            },
            tooltip: {
              trigger: 'item',
              backgroundColor: "rgba(255,255,255,1)",
              borderWidth: "1", //边框宽度设置1
              borderColor: "#eee", //设置边框颜色
              textStyle: {
                color: "#666" //设置文字颜色
              },
              // formatter: "{a} <br/>{b} : {c} ({d}%)"
              formatter: "{b} : {c} ({d}%)"
            },
            series: [
              {
                type: 'pie',
                center: ['50%', '39%'],
                radius: ['45%', '65%'],
                color: data.color,
                startAngle: 135,
                hoverOffset: 5,
                avoidLabelOverlap: false,
                labelLine: {
                  normal: {
                    length: 0,
                    show: true
                  }
                },
                label: {
                  normal: {
                    // formatter: '{b|{b}}',
                    //   formatter(v) {
                    //     let text = v.name
                    //     return text.length < 6
                    //         ? text
                    //         : `${text.slice(0,6)}\n${text.slice(6)}`
                    // },
                    formatter(v) {
                      var text = v.name.toString()
                      if (text.length <= 6) {
                        return text;
                      } else {
                        return text = text.substring(0, 6) + '...'
                      }
                    },
                    backgroundColor: 'rgba(255, 147, 38, 0)',
                    borderColor: 'transparent',
                    borderRadius: 4,
                    rich: {
                      b: {
                        color: '#666',
                        fontSize: 12,
                      }
                    },
                    // formatter: '{a|{b}：}\n{b|{c}}',
                    // rich:{
                    //     a:{
                    //       color: '#666'
                    //     },
                    //     b:{
                    //       align: 'center'
                    //     }
                    // },
                    textStyle: {
                      color: '#666',
                      fontSize: 12
                    }
                  }
                },
                emphasis: {
                  label: {
                    show: true,
                    formatter: '{b|{b} : {d}%}',
                    backgroundColor: 'rgba(255, 147, 38, 0)',
                    borderColor: 'transparent',
                    borderRadius: 4,
                    rich: {
                      b: {
                        color: '#fff',
                        fontSize: 12,
                        lineHeight: 1
                      }
                    }
                  }
                },
                data: data.data
              }
            ]
          }
          myChart.setOption(option)
          window.addEventListener('resize', () => myChart.resize(this.parentClientWidth), false)
        })
      },
      searchTable() {
        this.pageIndex = 1;
        this.checkURL();
      },
      // 每页数量
      handleSizeChange(val) {
        this.tableList=[];
        this.pageSize = val;
        let lastPage = Math.ceil(this.total / this.pageSize);
        if (this.pageIndex > lastPage) {
          this.pageIndex = lastPage;
        }
        this.checkURL();
      },
      // 页码
      handleCurrentChange(val) {
        this.tableList=[];
        this.pageIndex = val;
        this.checkURL();
      },
      // 首页按钮
      jumpFirstPage(val) {
        this.pageIndex = val;
        this.handleCurrentChange(val);
      },
      // 尾页按钮
      jumpLastPage(val) {
        this.pageIndex = val;
        this.handleCurrentChange(this.pageIndex);
      },
      checkURL() {
        let chainId = JSON.parse(sessionStorage.getItem('chainItem')).Id;
        let userId = JSON.parse(sessionStorage.getItem('chainItem')).userId;
        //let userId = '5dc23f1a654045689bbd99ad97f159df'
        let form = {
          "interfaceType": "urldetect",
          "interfaceParam": {
            "interfaceName": "malice_link_search",
            "interfaceParamData": {
              "link_addr": this.formInline.url,
              "is_malicious": this.formInline.is_malicious,
              "page": this.pageIndex,
              "page_size": this.pageSize
            }
          }
        }
        this.tableList = [];
        this.paddingText='数据请求中...';
        getSafetyData({}, form).then(res => {
          if (res.code == 200) {
            if (!res.data.responseData) {
              this.paddingText = "暂无数据";
              return
            }
            var data = res.data.responseData;
            if (!data) {
              this.paddingText = "暂无数据";
              return
            }
            if (data.data.length > 0) {
              this.tableList = data.data;
              this.total = data.total_num;
              this.zys = Math.ceil(this.total / this.pageSize); //获取总页数
              // 调用混入方法判断首页尾页按钮禁用的方法
              this.forbidden(this.zys, this.pageIndex);
            } else {
              this.paddingText = "暂无数据";
            }
          } else {
            this.paddingText = "暂无数据";
          }
        })
        .catch(err=>{
          this.paddingText = "暂无数据";
        })
      },
      checkGM() {
        let chainId = JSON.parse(sessionStorage.getItem('chainItem')).Id;
        let userId = JSON.parse(sessionStorage.getItem('chainItem')).userId;
        let query = {
          ServiceId: chainId
        };
        let ipStr = '';
        let that = this;
        //获取iplist
        new Promise((resolve, reject) => {
          getChainPodList(query).then(res => {
            if (res.code == 200) {
              var data = res.data;
              var ipList = [];
              for (var i = 0; i < data.length; i++) {
                var arr = data[i].NodeInfo
                if (arr.length > 0) {
                  for (var n = 0; n < arr.length; n++) {
                    var ip = arr[n].ExternalIp
                    ipList.push(ip);
                  }
                }
              }
              let newArr = Array.from(new Set(ipList))
              if (newArr.length > 0) {
                ipStr = newArr.join(",")
              }
              resolve(ipStr);
            } else {
              that.$message.error("数据获取失败，请重新加载！");
              that.pie1Show = false;
              that.pie1paddingText = "暂无数据";
              that.pie2Show = false;
              that.pie2paddingText = "暂无数据"
            }
          });
        }).then(function (id) {
          let form = {
            "interfaceType": "fxjc",
            "interfaceParam": {
              "interfaceName": "fxjcipstatistics",
              "interfaceParamData": {
                "ip_list": ipStr
              }
            }
          }
          getSafetyData({chainId: chainId}, form).then(res => {
            if (!res.data.responseData||!res.data.responseData.result) {
              // that.$message.error("数据获取失败，请重新加载！");
              that.pie1Show = false;
              that.pie1paddingText = "暂无数据"
              that.pie2Show = false;
              that.pie2paddingText = "暂无数据"
              return
            }
            if (res.code == 200) {
              var data = res.data.responseData.result;
              that.imageList[0].number = data.count.assets;
              that.imageList[1].number = data.count.service;
              that.imageList[2].number = data.count.risk;
              if (data.port_distribution.length > 0) {
                that.pie1Show = true;
                var newPie = [];
                for (var i = 0; i < data.port_distribution.length; i++) {
                  newPie.push({name: data.port_distribution[i].port, value: data.port_distribution[i].count})
                }
                that.pieData1.data = newPie;
                that.drawPie1("pie1", that.pieData1, "端口分布")
              } else {
                that.pie1Show = false;
                that.pie1paddingText = "暂无数据"
              }
              if (data.service_distribution.length > 0) {
                that.pie2Show = true;
                var newPie1 = [];
                for (var i = 0; i < data.service_distribution.length; i++) {
                  newPie1.push({name: data.service_distribution[i].service, value: data.service_distribution[i].count})
                }
                that.pieData2.data = newPie1;
                that.drawPie1("pie2", that.pieData2, "服务分布")
              } else {
                that.pie2Show = false;
                that.pie2paddingText = "暂无数据"
              }
            } else {
              that.$message.error("数据获取失败，请重新加载！");
              that.pie1Show = false;
              that.pie1paddingText = "暂无数据"
              that.pie2Show = false;
              that.pie2paddingText = "暂无数据"
            }
          })
        })
      },
      checkSY() {
        let chainId = JSON.parse(sessionStorage.getItem('chainItem')).Id;
        let userId = JSON.parse(sessionStorage.getItem('chainItem')).userId;
        let that = this;
        let form1 = {
          "interfaceType": "gjybysy",
          "interfaceParam": {
            "interfaceName": "gjczsjtj",
            "interfaceParamData": {}
          }
        }
        getSafetyData({chainId: chainId}, form1).then(res => {
          if (res.code == 200) {
            that.barShow = true;
            let data = res.data.responseData;
            let arr = ["鼠标点击", "焦点切换", "键盘输入"]
            if (data['operDataList'].length == 0) {
              that.barData.xData = arr;
              that.barData.yData = ["0", "0", "0"];
              that.drawBar("bar", that.barData)
              return
            }
            if (data.operDataList.length > 0) {
              // that.barShow = true;
              var xData = data.operTypeList
              var yData = ["0", "0", "0"]
              that.barData.yData = data.operDataList;
              for (var i = 0; i < xData.length; i++) {
                if (xData[i] == 'mouse click') {
                  yData[0] = data.operDataList[i]
                } else if (xData[i] == 'focus switch') {
                  yData[1] = data.operDataList[i]
                } else if (xData[i] == 'keyboard press') {
                  yData[2] = data.operDataList[i]
                }
              }
              that.barData.xData = arr;
              that.barData.yData = yData;
              that.drawBar("bar", that.barData)
            } else {
              // that.barPaddingText = "暂无数据";
              // that.barShow = false;
              that.drawBar("bar", that.barData)
            }
          }
          // else{
          //   that.barPaddingText = "暂无数据";
          //   that.barShow = false;
          // }
        });
        let form2 = {
          "interfaceType": "gjybysy",
          "interfaceParam": {
            "interfaceName": "bhgjqst",
            "interfaceParamData": {}
          }
        }
        getSafetyData({chainId: chainId}, form2).then(res => {
          if (res.code == 200) {
            that.lineShow = true;
            let data = res.data.responseData
            if (!data['countData']) {
              // that.linePaddingText = "暂无数据";
              // that.lineShow = false;
              that.drawLine("line", that.lineData)
              return
            }
            if (data.countData.length > 0) {
              // that.lineShow = true;
              that.lineData.yData = data.countData;
              for (var i = 0; i < data.timeData.length; i++) {
                data.timeData[i] = data.timeData[i].replace(/-/g, "/");
              }
              that.lineData.xData = data.timeData;
              that.drawLine("line", that.lineData)
            } else {
              // that.linePaddingText = "暂无数据";
              // that.lineShow = false;
              that.drawLine("line", that.lineData)
            }
          }
          // else{
          //   that.linePaddingText = "暂无数据";
          //   that.lineShow = false;
          // }
        }).catch(err => {
          console.log(err)
        });
      }
    }
  }
</script>
<style rel="stylesheet/less" lang="less" scoped>
  .table-wrapper {
    .none {
      border-bottom: 1PX solid #E9EBEF !important;
    }
  }
  .ivu-page{
    margin-top:10px;
  }
  .safetycheck_page .sc_div {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 20px;
  }

  .safetycheck_page .sc_half {
    width: 49%;
    height: 290px;
    padding: 20px 20px;
    background: #ffffff;
    box-shadow: 0 0 20px 0 rgba(218, 218, 218, 0.6);
    border-radius: 4px;
    margin-bottom: 28px;
    position: relative;
  }

  .safetycheck_page .sc_half_top {
    width: 49%;
    height: 200px;
    padding: 20px 20px;
    background: #ffffff;
    box-shadow: 0 0 20px 0 rgba(218, 218, 218, 0.6);
    border-radius: 4px;
    margin-bottom: 10px;
    position: relative;
  }

  .safetycheck_page .sc_half1 {
    width: 49%;
    /*height: 240px;*/
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .safetycheck_page .centerDiv {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 250px;
  }

  .safetycheck_page #pie1, .safetycheck_page #pie2 {
    height: 180px;
  }

  .safetycheck_page .sc_echarts {
    width: 100%;
    height: 250px;
  }

  .safetycheck_page .imglist {
    list-style: none;
    padding: 25px 0px;
    margin: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .safetycheck_page .imglist li {
    width: 32%;
    height: 110px;
    position: relative;
    /* background: blue; */
  }

  .safetycheck_page .imglist li .liImage {
    width: 100%;
    height: 100%;
  }

  .safetycheck_page .imglist li .iconImage {
    width: 35%;
    position: absolute;
    top: 25px;
    left: 10%;
  }

  .safetycheck_page .imglist li .number {
    position: absolute;
    top: 12%;
    right: 5%;
    font-size: 32px;
    color: #ffffff;
  }

  .safetycheck_page .imglist li .name {
    position: absolute;
    bottom: 12%;
    right: 5%;
    color: #ffffff;
  }

  .safetycheck_page.table-wrapper {
    padding: 20px 20px;
    background: #ffffff;
  }

  .safetycheck_page .info {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }


  .safetycheck_page .info .infotext {
    color: #333333;
    vertical-align: middle;
    // margin-left: 7px;
  }

  .safetycheck_page {
    .none {
      text-align: center;
      line-height: 65px;
      color: #666666;
      border: 0
    }

    .form-inline {
      margin:0;

      /deep/
      .el-input__inner,.el-form-item__label {
        height: 32px;
        line-height: 32px;
      }
    }

    .infoIcon {
      width: 3px;
      height: 14px;
      vertical-align: middle;
      margin-right: 5px;
    }

    .infoTitle {
      line-height: 34px;
    }
  }
  .safetycheck_page .sa-tag {
    display: flex;
    align-items: center;
    .green,.red {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #15AD31;
      margin-right: 4px;
    }
    .red {
      background: #FA5151;
    }
  }
</style>
