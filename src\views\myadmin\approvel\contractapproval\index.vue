<template>
  <div class="shelves_approval">
    <keep-alive>
    <ApprovalHome v-if="currentTab==='shelves_approval'" />
  </keep-alive>
  <router-view v-if="currentTab!=='shelves_approval'"/>
  </div>
</template>

<script>
import ApprovalHome from './approval-home.vue'
export default {
  name: 'shelves_approval',
  components: {
    ApprovalHome
  },
  data () {
    return {
      // excludeArr: ['tem_modify']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
