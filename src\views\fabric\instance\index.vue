<template>
  <div class="instance_page">
    <div class="content">
      <div class="step">
        <span>Fabric联盟网络构建流程：</span>
        <span class="first">1.创建联盟实例</span>
        <img :src="rightImg" alt="">
        <span>2.创建通道</span>
        <img :src="rightImg" alt="">
        <span>3.部署合约</span>
        <img :src="rightImg" alt="">
        <span>4.部署应用</span>
      </div>
      <div class="crossLine"></div>
      <div class="chain_wrap">
        <div class="myChain">
          <img :src="infoIcon" alt="">
          <span>我的联盟</span>
        </div>
        <ul class="list">
          <li class="first" :class="{isOne:chainList.length == 0}" @click="goCreat">
            <div>
              <!-- <img :src="creatImg" alt=""> -->
              <span class="el-icon-plus icon"></span>
              <span>创建联盟实例</span>
            </div>
          </li>
          <li v-for="(item,index) in chainList" :key="index" :class="{active:operateIdx == index}" @click="go(item)">
            <div class="header">
              <!-- <span class="name" v-if="item.chainStatus == 1">
                <img :src="instanceGif" alt="" class="icon">
                <span>{{item.Name}}</span>
              </span> -->
              <span class="name">
                <img :src="instanceSuccess" alt="" class="icon" v-if="item.state == 2">
                <img :src="instanceError" alt="" class="icon" v-if="item.state == 6">
                <span class="text">
                  {{item.chainDisplayName}}
                </span>
              </span>
              <span class="status" :class="{green:item.TemplateType == 'KAFKA_CMRI',blue:item.TemplateType == 'SOLO_CMRI'}">
                {{item.TemplateType | getTemplateType}}
              </span>
            </div>
             <div
              :class="{point:true,disabled:!item.CanOperation}"
              @click.stop="isoperate(index,item.CanOperation)"
            >
              <div></div>
              <div></div>
              <div></div>
            </div>
            <div class="operate" v-if="index == operateIdx && isShow[index].isShow">
              <div @click.stop="showDelete(item,index)">删除</div>
            </div>
            <div class="line"></div>
            <div class="info">
              <div>
                <span class="left">组织数</span>
                <span class="right">{{item.orgs}}</span>
              </div>
              <div>
                <span class="left">通道数</span>
                <span class="right">{{item.Channel}}</span>
              </div>
              <div>
                <span class="left">创建时间</span>
                <span class="right">{{dateFormat('YYYY-mm-dd',item.CreateTime)}}</span>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <transition name="fade">
      <div class="alertBox confirmBox" v-if="isdeltissue">
        <div class="addTissue">
          <div class="delText">
            <img :src="warningImg" class="iconImage">
            <span class="noUn">是否删除该实例？</span>
            <div class="errmessage">实例 {{ instanceName }} 删除不可恢复。</div>
          </div>
          <div class="confirmBottom">
            <Button type="primary" class="sure-btn" @click="unDelete">确定</Button>
            <Button class="border-btn" @click="cancel">取消</Button>
          </div>
        </div>
      </div>
    </transition>
    <transition name="fade" v-if="isShowCreat">
      <div class="alertBox confirmBox">
        <div class="addTissue">
          <div class="delText notitle">
            <img :src="warningImg" class="iconImage">
            <div class="errmessage">当前资源不足，无法创建实例。</div>
          </div>
          <div class="confirmBottom">
            <Button type="primary" class="sure-btn" @click="cancel">知道了</Button>
          </div>
        </div>
      </div>
    </transition>
    <div
      v-if="loading"
      class="BoxLoading"
      v-loading="loading"
      element-loading-text="删除中..."
    ></div>
  </div>
</template>

<script>
import {getChainStatistics,getChainList,getClusterList,checkChainLimit} from "@/api/baascore/overview";
import {getChainPodList} from "@/api/baascore/ywResource";
  import {deleteChain} from "@/api/baascore/tissue";
export default {
  components: {
  },
  data () {
    return {
      infoIcon: require("@/assets/chainManage_images/overview/infoIcon.png"),
      rightImg:require("@/assets/image/rights.png"),
      creatImg:require("@/assets/image/creat.png"),
      instanceSuccess:require("@/assets/image/instanceSuccess.png"),
      instanceError:require("@/assets/image/instanceError.png"),
      instanceGif:require("@/assets/image/instanceGif.gif"),
      warningImg:require("@/assets/image/el-warning.png"),
      chainList:[],
      ServiceId:'',
      instanceName:'',
      operateIdx:-1,
      isShow:[],
      loading:false,
      isdeltissue:false,
      isShowCreat:false,
    }
  },
  computed: {
    dateFormat(fmt, date) {
      return function(fmt, date) {
       // date = Date.parse(date.replace(/-/g,"/"))
        date = new Date(date)
        let ret;
        const opt = {
            "Y+": date.getFullYear().toString(),        // 年
            "m+": (date.getMonth() + 1).toString(),     // 月
            "d+": date.getDate().toString(),            // 日
            "H+": date.getHours().toString(),           // 时
            "M+": date.getMinutes().toString(),         // 分
            "S+": date.getSeconds().toString()          // 秒
            // 有其他格式化字符需求可以继续添加，必须转化成字符串
        };
        for (let k in opt) {
            ret = new RegExp("(" + k + ")").exec(fmt);
            if (ret) {
                fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
            };
        };
        return fmt;
      }

    },
  },
  mounted () {
    this.getChainList()
  },
  methods: {
    //go我的联盟
    goCreat() {
      sessionStorage.clear()
      getClusterList().then(res =>{
        if(res.code == 200 && res.data) {
          var params = {
            ClusterName:res.data[0].ClusterName
          }
          checkChainLimit(params).then(res =>{
            if(res.code == 200) {
              this.$router.push({
                path: "/guide/step",
              });
            }else if(res.code == 4140) {
              this.isShowCreat = true
            }
            else{
              this.$message.error('数据获取失败，请重新加载！')
            }
          })
        }else {
          this.$message.error('数据获取失败，请重新加载！')
        }
      })
    },
    //获取链
    getChainList() {
      //solo--标准版 kafka--安全版 params
      this.chainList = [];
      getChainList().then(res =>{
        if(res.code == 200) {
          if(res.data && res.data.length >0) {
            res.data.forEach((item,index) =>{
              if(item.state == 2 || item.state == 6) {
                this.chainList.push(item)
              }
            })

            this.chainList.forEach((item,index) =>{
              this.isShow.push({isShow:false})
              if(item.Id) {
                var params = {
                  ServiceId: item.Id
                }
                getChainStatistics(params).then(res =>{
                  if(res.code == 200) {
                    this.$set(this.chainList[index],'Channel',res.data.Channel)
                  }else {
                    this.$message.error('数据获取失败，请重新加载！')
                  }
                })
                getChainPodList(params).then(res =>{
                  if(res.code == 200) {
                    this.$set(this.chainList[index],'orgs',res.data.length-1)
                  }else {
                    this.$message.error(res.message)
                  }
                })
              }
            })
          }
        }else {
          this.$message.error('数据获取失败，请重新加载！')
        }
      })
    },
    //删除显示
    isoperate(index,flag) {
      if(!flag){return}
      if(this.operateIdx != index && this.operateIdx > -1) {
        this.isShow[this.operateIdx].isShow = false
      }
      this.operateIdx = index;
      this.$set(this.isShow[index],'isShow',!this.isShow[index].isShow)
    },
    showDelete(item,index) {
      this.isdeltissue = true
      this.isShow[index].isShow = false
      this.ServiceId =  item.Id
      this.instanceName = item.chainDisplayName
    },
    cancel(){
      this.isdeltissue = false
      this.isShowCreat = false
    },
    //删除
    unDelete() {
      this.loading = true;
      var params = {
          ServiceId: this.ServiceId
        }
      deleteChain(params).then(res =>{
        this.loading = false;
        if (res.code == 200) {
            this.$message.success('删除成功！')
            this.isdeltissue = false;
            this.getChainList()
            this.operateIdx = -1;
          } else {
            this.$message.error("删除失败，请重试！");
          }
        }).catch(err => {
          this.loading = false;
        });
    },
    //链页面
    go(item){
      if(item.chainStatus == 1) {
        return
      }
      //item.Id = item.chainId
      this.$store.dispatch("getChainItem", item).then(() =>{
       // var list = [{title:'实例管理'},{title:item.Name}]
        //this.$store.dispatch("getBreadcrumbList", list).then(() =>{
          this.$router.push({
            path:'/chainManage/overview'
          })
        //})
      })

    },
  },
  filters:{
    getTemplateType(type) {
      switch(type) {
        case 'SOLO_CMRI' :
          return '标准版'
        case 'KAFKA_CMRI':
          return '安全版'
      }
    }
  }
}
</script>

<style rel="stylesheet/less" lang="less" scoped>
@import "../../../styles/common/modal.less";
.instance_page {
  .content {
    .step {
      span {
        // font-size: 20px;
        font-size: 14px;
        vertical-align: middle;
      }
      .first {
        margin-left: 30px;
      }
      img {
        width: 22px;
        height:19px;
        vertical-align: middle;
        margin:0 30px;
      }
    }
    .crossLine {
      background:#F7F7F7;
      width: 100%;
      height: 20px;
      position: absolute;
      left: 0;
      top: 50px;
    }
    .chain_wrap {
      margin-top:40px;
      .myChain {
        img {
          vertical-align: middle;
          width: 3px;
          height: 14px;
          margin-right:4px;
        }
        span {
          color:#333;
          // font-size:22px ;
          font-size: 14px;
          // margin-left: 10px;
          vertical-align: middle;
        }
      }
      .list {
        list-style: none;
        margin: 0;
        padding: 0;
        display: flex;
        flex-wrap: wrap;
        //justify-content: space-between;
        li {
          width: calc((100% - 20px * 3) / 4) ;
          margin-top: 10px;
          margin-left: 20px;
          position: relative;
          padding: 17px;
          cursor: pointer;
          height: 210px;
          border: 1px solid #e5e5e5;
          border-radius:8px;
          &:hover {
            border: 1px solid #337DFF;
            box-shadow:0px 0px 2px #337DFF;
          }
          &:active {
            border: 1px solid #337DFF;
            box-shadow:0px 0px 2px #337DFF;
            background:rgb(236, 239, 252);
          }
          &.active {
            border: 1px solid #337DFF;
            box-shadow:0px 0px 2px #337DFF;
            background:rgb(236, 239, 252);
          }
          &:nth-child(4n + 1) {
            margin-left:0;
          }
          &.first {
            margin-left:0px;
            display: flex;
            align-items: center;
            justify-content: center;
            >div {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              .icon {
                font-size: 28px;
                color: #15AD31;
                margin-bottom: 8px;
              }
            }
          }
          img {
            width: 14px;
            height: 14px;
            vertical-align: middle;
            margin-right: 4px;
          }
          span {
            color: #333;
            // font-size: 20px;
            font-size: 14px;
            vertical-align: middle;
          }
          .header {
            display: flex;
            align-items: center;
            width: 90%;
            .name {
              display: flex;
              align-items: center;
              .icon {
                width: 14px;
                height: 14px;
              }
              span.text {
                line-height: 12px;
                word-break: break-all;
              }
            }
            .status {
              margin-left: 5px;
              font-size: 14px;
              margin-right: 5px;
              min-width: 50px;
              height: 26px;
              border-radius: 3px;
              border-radius: 3px;
              text-align: center;
              line-height: 24px;
              &.yellow {
                background: #FF931D;
                border: 1Px solid #FF931D;
                color:#fff;
              }
              &.green {
                background: #15AD31;
                border: 1Px solid #15AD31;
                color:#fff ;
              }
              &.blue {
                background: #3D73EF;
                border: 1Px solid #3D73EF;
                color:#fff ;
              }
            }
          }
          .point {
            &.disabled{
              border-color:#C7C7C7;
              cursor:auto;
            }
            position: absolute;
            top: 19px;
            right: 12px;
            cursor: pointer;
            padding: 1Px 5px;
            width: 25px;
            height: 25px;
            border: 1px solid #3D73EF;
            border-radius: 50%;
          }
          .point div {
            width: 3px;
            height: 3px;
            border-radius: 50%;
            background: #3D73EF;
            margin-top: 3px;
            margin-left:5px ;
          }
          .point.disabled{
            div{
              background:#C7C7C7;
            }
          }
          .point div:nth-child(1) {
            /*margin-top: 4px;*/
          }
          .operate {
            position: absolute;
            width: 90px;
            right: 22px;
            top: 54px;
            border: 1px solid #e7ecef;
            box-shadow: 0px 5px 10px 0px rgba(196, 197, 199, 0.4);
            border-radius: 4px;
            z-index: 100;
            background: #fff;
          }
          .operate div {
            height: 40px;
            text-align: center;
            line-height: 40px;
            // font-size: 17px;
            font-size: 14px;
            color: #333;
            cursor: pointer;
          }
          .line {
            position: absolute;
            left: 0;
            width: 100%;
            height: 1px;
            margin: 16px 0;
            background: #e5e5e5;
          }
          .info {
            margin-top:38px;
            div {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin: 20px 0;
              &:last-child {
                margin-bottom:0 ;
              }
              span {
                // font-size: 17px;
                font-size: 14px;
              }
              .left {
                color: #ccc;
              }
              .right {
                color: #333;
              }
            }
          }
        }
      }
    }
  }
}
.ivu-btn {
  height:auto;
  padding:4px 15px;
}
</style>
