<!--
  aturun
  添加模板
  2021/10/21

-->
<template>
  <el-dialog
      class="dialog_sty add_temp_dialog"
      title="添加模板"
      :visible.sync="Visible"
      width="400px"
      :modal="false"
      destroy-on-close
      :close-on-click-modal="false"
      @opened="getOracleTempList">
    <div class="dialog_content">
     <div style="margin-left: 50px">预言机模板
       <el-select no-data-text="无可用预言机模板" v-model="tempList" multiple placeholder="请选择预言机模板" ref="selectRef" @change="change">
         <el-option :label="item.tempName" :value="item.tempId" v-for="(item,index) in MachineTempList" :key="index"></el-option>
       </el-select>
     </div>

    </div>
    <span slot="footer" class="dialog-footer">
    <el-button @click="Visible = false">取 消</el-button>
    <el-button type="primary" @click="submitForm">确 定</el-button>
  </span>
  </el-dialog>
</template>

<script>
import * as api from "../api";
import {addTemp, getOracleTempList} from "../api";

export default {
  name: "seeExceptionLog",
  components: {},
  props:[],
  data() {
    return {
      tempId:'',
      Visible:false,
      form:{
      },
      tempList:[],
      providerId:'',
      upLoading:false,
      MachineTempList:[],
      rules: {
        name: [
          {required: true, message: '请输入用户名称', trigger: 'blur'},
        ],
      }
    }
  },
  watch: {
  },
  created() {

  },
  mounted() {
  },
  methods: {
    change(item){
      if(this.tempList.length==this.MachineTempList.length){
        let select = this.$refs.selectRef
        select.blur();
      }
    },
    //获取数据用户列表
    getOracleTempList(){
      api.getOracleTempList(
          this.providerId
      ).then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.MachineTempList=res.result
      })
    },
    submitForm(){
      if (this.tempList.length==0) return this.$message.warning('请选择')
      if(this.upLoading) return
      this.upLoading = true
      api.addTemp(
          {
            "providerId": this.providerId,
            "tempList": this.tempList
          }
      ).then(res=>{
        this.upLoading = false
        if(res.code!=0) return this.$message.warning(res.msg)
        this.tempList = []
        this.Visible = false
        this.$emit('Refresh')
      }).catch(err=>{
        this.upLoading = false
      })

    },
  },

}
</script>

<style lang="less" scoped>
.add_temp_dialog{
  margin-top: 200px;
  .dialog_content{
    height: auto;
    span{
      white-space:nowrap;
    }
  }
}
</style>
