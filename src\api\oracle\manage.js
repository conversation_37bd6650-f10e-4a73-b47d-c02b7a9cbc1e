// import Vue from 'vue'
import axios from '../index'

// 环境
const BASE_URL = process.env.NODE_ENV === 'development' ? '/cmbaas' : '/cmbaas'

// post
export function postAction (url, parameter,params) {
  return axios.request({
    url: BASE_URL + url,
    method: 'post',
    data: parameter,
    params: params,
  })
}

// post method= {post | put}
export function httpAction (url, parameter, method) {
  return axios.request({
    url: BASE_URL + url,
    method: method,
    data: parameter
  })
}

// put
export function putAction (url, parameter) {
  return axios.request({
    url: BASE_URL + url,
    method: 'put',
    data: parameter
  })
}

// get
export function getAction (url, params,parameter) {
  return axios.request({
    url: BASE_URL + url,
    method: 'get',
    params: params,
    data: parameter
  })
}

// 获取文件流
export function getFileStreamAction (url, parameter) {
  return axios.request({
    url: BASE_URL + url,
    method: 'get',
    params: parameter,
    responseType: 'blob',
    headers: {
      isFileStream: 1
    }
  })
}

// 获取文件流
export function postFileStreamAction (url, parameter) {
  return axios.request({
    url: BASE_URL + url,
    method: 'post',
    data: parameter,
    responseType: 'blob',
    isFileStream: 1,
  })
}
// 获取文件流
export function getArraybufferAction (url, parameter) {
  return axios.request({
    url: BASE_URL + url,
    method: 'get',
    params: parameter,
    responseType:"blob",
    headers: {
      isFileStream: 1,
      "Content-Type": "application/octet-stream",
    }
  })
}


// deleteAction
export function deleteAction (url, parameter) {
  return axios.request({
    url: BASE_URL + url,
    method: 'delete',
    params: parameter
  })
}



// /**
//  * 下载文件 用于excel导出
//  * @param url
//  * @param parameter
//  * @returns {*}
//  */
export function downFile (url, parameter) {
  return axios({
    url: url,
    params: parameter,
    method: 'get',
    responseType: 'blob'
  })
}

// /**
//  * 下载文件
//  * @param url 文件路径
//  * @param fileName 文件名
//  * @param parameter
//  * @returns {*}
//  */
export function downloadFile (url, fileName, parameter) {
  return downFile(url, parameter).then((data) => {
    if (!data || data.size === 0) {
      Vue.prototype['$message'].warning('文件下载失败')
      return
    }
    if (typeof window.navigator.msSaveBlob !== 'undefined') {
      window.navigator.msSaveBlob(new Blob([data]), fileName)
    } else {
      let url = window.URL.createObjectURL(new Blob([data]))
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link) // 下载完成移除元素
      window.URL.revokeObjectURL(url) // 释放掉blob对象
    }
  })
}

// /**
//  * 文件上传 用于富文本上传图片
//  * @param url
//  * @param parameter
//  * @returns {*}
//  */
export function uploadAction (url, parameter) {
  return axios({
    url: BASE_URL + url,
    data: parameter,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data' // 文件上传
    }
  })
}

// /**
//  * 获取文件服务访问路径
//  * @param avatar
//  * @param subStr
//  * @returns {*}
//  */
// export function getFileAccessHttpUrl(avatar,subStr) {
//   if(!subStr) subStr = 'http'
//   if(avatar && avatar.startsWith(subStr)){
//     return avatar;
//   }else{
//     if(avatar &&　avatar.length>0 && avatar.indexOf('[')==-1){
//       return window._CONFIG['staticDomainURL'] + "/" + avatar;
//     }
//   }
// }
