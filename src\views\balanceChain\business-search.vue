<template>
  <div class="comp-wrap">
    <div class="title"><span class="bs"></span>交易查询</div>
    <p>
      <Input class='bt1 width-input' placeholder="请输入交易哈希查询" @on-enter="searchList" style="vertical-align:baseline;width:230px" v-model.trim="datainput" @keyup.native="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm,'')" />
      <Button class='bt1' icon="ios-search" type="primary" @click="searchList">查询</Button>
      <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting">重置</Button>
    </p>
    <div class="business_details" v-if="tranDataStatus">
      <Row>
        <Col span="4">
        <h4>服务名称</h4>
        {{tranData.scalePlatformResourceName}}
        </Col>
        <Col span="4">
        <h4>交易哈希</h4>
        {{tranData.transactionId}}
        </Col>
        <Col span="4">
        <h4>推送时间</h4>
        {{tranData.blockTimestamp}}
        </Col>
        <Col span="4">
        <h4>天平链返回交易哈希</h4>
        {{tranData.judicialTxid}}
        </Col>
        <Col span="4">
        <h4>上链时间</h4>
        {{tranData.updateTime}}
        </Col>
        <Col span="4">
        <h4>状态</h4>
        {{tranData.status}}
        </Col>
      </Row>
    </div>
    <div class="title"><span class="bs"></span>今日交易</div>
    <edit-table-mul :columns="columns" v-model="tableData"></edit-table-mul>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px" />
  </div>
</template>
<script>
import { listToDayScaleTransaction, getScalesByTransactionId } from '@/api/balance'
import EditTableMul from '_c/edit-table-mul'
export default {
  components: {
    EditTableMul
  },
  data () {
    return {
      datainput: '',
      columns: [
        { key: 'scalePlatformResourceName', title: '服务名称', tooltip: true },
        { key: 'transactionId', title: '交易哈希', tooltip: true },
        { key: 'onScaleTime', title: '推送时间', tooltip: true },
        { key: 'judicialTxid', title: '天平链返回交易哈希', tooltip: true },
        { key: 'obtainScaleHashTime', title: '上链时间', tooltip: true },
        {
          key: 'status', title: '状态', tooltip: true, minWidth: 70,
          // width: '200px',
          render: (h, params) => {
            const row = params.row
            const color = row.status === '未上链' ? '515a6e' : row.status === '上链成功' ? 'success' : row.status === '上链失败' ? 'error' : 'success'
            // const text = row.status === 1 ? 'Working' : row.status === 2 ? 'Success' : 'Fail';
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, row.status)
          }
        }

      ],
      tableData: [],
      tablePageParam: {
        pageSize: 10,
        pageIndex: 1,
        pagetotal: 0,
      },
      tranDataStatus: false,
      tranData: {
        blockTimestamp: '',
        updateTime: '',
        scalePlatformResourceName: '',
        transactionId: '',
        judicialTxid: '',
        status: '',
      }
    }
  },
  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    // 分页
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.businesslist()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.businesslist()
    },
    searchList () {
      if (this.datainput !== '') {
        getScalesByTransactionId(this.datainput).then(res => {
          if (res.code === '00000') {
            if (res.data != null) {
              this.tranData = {
                blockTimestamp: res.data.onScaleTime,
                updateTime: res.data.obtainScaleHashTime,
                scalePlatformResourceName: res.data.scalePlatformResourceName,
                transactionId: res.data.transactionId,
                judicialTxid: res.data.judicialTxid,
                status: res.data.status === 0 ? '上链失败' : res.data.status === 1 ? '上链成功' : '未上链',
              }
              this.tranDataStatus = true
            }

          } else {
            this.tranDataStatus = false
            this.msgInfo('error', res.message, true)
          }
        }).catch(error => {
          this.tranDataStatus = false
          this.msgInfo('error', error.message, true)
        })
      }

    },

    resetting () {
      this.tranDataStatus = false
      this.datainput = ''

      // getScalesByTransactionId(this.datainput).then(res => {
      //   console.log(res)
      // })
    },
    businesslist () {
      let datalist = {
        pageParam: {
          pageIndex: this.tablePageParam.pageIndex,
          pageSize: this.tablePageParam.pageSize
        }
      }
      listToDayScaleTransaction(datalist).then(res => {
        if (res.code === '00000') {
          let resdata = {
            '0': '上链失败',
            '1': '上链成功',
            '2': '未上链'

          }
          let data = res.data.records.map(item => {
            return {
              ...item,
              status: item.status ? resdata[item.status] : resdata[item.status]
            }
          })
          this.tableData = data
          this.tablePageParam.pagetotal = res.data.total
        } else {
          this.msgInfo('error', res.message, true)
        }
        //  0:失败 1:成功 2:未上链

      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  mounted () {
    this.businesslist()
  }
}
</script>
<style lang="less" scoped>
.comp-wrap {
  padding: 0 40px;
  box-sizing: border-box;
  .title {
    margin: 15px 0;
    font-size: 16px;
    font-weight: bold;
    height: 25px;
    line-height: 25px;
    vertical-align: middle;
  }
  .bs {
    float: left;
    width: 6px;
    height: 16px;
    background: #348eff;
    opacity: 1;
    border-radius: 3px;
    margin: 4px 5px 0 0;
  }
  .bt1 {
    margin-right: 10px;
  }
  .business_details {
    // border: 1px solid #000;
    height: 50px;
    box-sizing: border-box;
    // line-height: 50px;
    text-align: center;
    margin: 40px 0;
  }
  /deep/.ivu-tag-dot {
    border: 1px none #e8eaec !important;
    background: transparent !important;
  }
  /deep/.ivu-tag {
    font-size: inherit !important;
  }
  /deep/.ivu-col-span-4 {
    word-break: break-all;
    word-wrap: break-word;
    white-space: normal;
  }
}
</style>
