<template>
  <div>
    <div style="width：auto;margin-left: 10px;">
      <Card style="width: auto;">
        <!-- <p class="info-title" style="margin:10px 0 15px 10px;"><span class="bs">基本信息</span></p> -->
        <div class="info-title addflex">
          <div>
            <div class="bs"></div>
            <span>基本信息</span>
          </div>
        </div>
        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="140">
        <FormItem label="IPFS网络名称：">
            <span>{{detailinfo.ipfsName}}</span>
        </FormItem>
        <FormItem label="创建者：">
            <span>{{detailinfo.creatorName}}</span>
        </FormItem>
        <FormItem label="描述：" prop="desc">
            <Input style="width:500px;height:110px" v-model="detailinfo.ipfsBrief"   type="textarea" :rows="5"  disabled/>
        </FormItem>
        <FormItem label="资源池命名空间：" prop="desc">
             <span>{{detailinfo.namespaceName}}</span>
        </FormItem>
        <FormItem label="创建时间：">
            <span>{{detailinfo.createTime}}</span>
        </FormItem>
        <FormItem label="已组网节点：">
            <span>{{zwList}}</span>
        </FormItem>
    </Form>
      </Card>
    </div>
     <Card style="margin:20px 5px 10px 10px;">
      <div class="node" style="width：auto;margin:10px 5px 0px 10px;">
        <div class="info-title">
          <span class="bs"></span><span>节点</span>
          <Button ghost type="success" @click="add" icon="md-add" style="float:right;margin-top:-5px;">新增节点</Button>
        </div>
          <!-- <Spin v-else>
        <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
        <div>{{loadingMsg}}</div>
      </Spin> -->
        <div>
        <edit-table-mul :columns="columns" v-model="tableData" :key="transferKey" style="margin-top:20px;"></edit-table-mul>
<Spin size="large" fix v-if="spinShow">
    <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
</Spin>
        </div>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[5,10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;margin:10px 0;" />
      </div>
    </Card>
    <Modal :draggable="true" v-model="addModal" width="500" :title="alertTitle" :z-index="1000" sticky :mask-closable="false" :footer-hide='true'>
      <!-- <p v-show="alertTitle==='节点修改'">(当前剩余可用磁盘：{{resous.yuNodeDisk}}&nbsp;Gi,&nbsp;CPU：{{resous.yuNodeCpu}}&nbsp;m,&nbsp;内存：{{resous.yuNodeMemory}}&nbsp;Mi)</p> -->
        <Form ref="nodeConfigured" :model="nodeConfigured" :rules="noderuleValidate" :label-width="140">
        <FormItem label="节点名称：" prop="nodeName">
            <Input style="width:150px"  v-model="nodeConfigured.nodeName" placeholder="请输入节点名称" :disabled="alertTitle==='节点修改'"/>
        </FormItem>
        <FormItem label="硬盘：" prop="nodeDisk">
            <Input style="width:150px"  v-model="nodeConfigured.nodeDisk" placeholder="" />Gi
        </FormItem>
        <!-- <FormItem label="CPU最大限制值：" prop="nodeLimitCpu">
            <Input style="width:150px"  v-model="nodeConfigured.nodeLimitCpu" placeholder=""/>m
        </FormItem> -->
        <FormItem label="CPU：" prop="nodeCpu">
            <Input style="width:150px"  v-model="nodeConfigured.nodeCpu" placeholder="200~1000"/>m
        </FormItem>
  <!-- <FormItem label="内存最大限制值：" prop="nodeLimitMemory">
            <Input style="width:150px"  v-model="nodeConfigured.nodeLimitMemory" placeholder=""/>Mi
        </FormItem> -->
        <FormItem label="内存：" prop="nodeMemory">
            <Input style="width:150px"  v-model="nodeConfigured.nodeMemory" placeholder="200~1000"/>Mi
        </FormItem>
        <FormItem>
            <Button @click="handleReset('nodeConfigured')" >取消</Button>
            <Button style="margin-left: 8px" type="primary" @click="handleSubmit('nodeConfigured')">确定</Button>
        </FormItem>
    </Form>
    </Modal>
    <!-- <div style="margin-left: 46%;margin-top: 3%;">
        <Button type="primary" @click="handleSubmit('formValidate')">取消</Button>
        <Button @click="handleReset('formValidate')" style="margin-left: 8px">确定</Button>
    </div> -->
  </div>
</template>
<script>
import EditTableMul from '_c/edit-table-mul'
import { nodeList, nodeSave, nodeDelete, nodeUpdate, getNodeInfo, getNetwork } from '@/api/contract'
import { isNumber } from '../../lib/check'
export default {
  components: {
    EditTableMul
  },
  data () {
    const validateipfstName = (rule, value, callback) => {
      let re = /^[a-z0-9]{1,20}$/
      let reg = /^[a-z]/
      if (!reg.test(value.slice(0, 1))) {
        callback(new Error('只能以小写字母开头'))
      } else if (!re.test(value)) {
        callback(new Error('只能输入数字和小写字母'))
      } else {
        callback()
      }
    }
    // cpu
    const validateAccountCpu = (rule, value, callback) => {
      let numValue = Number(value)
      if (!isNumber(value)) {
        callback(new Error('请在区间内输入正整数'))
      } else if (numValue >= 200 && numValue <= 1000) {
        callback()
      } else {
        callback(new Error('最小输入200,最大不能超过cpu限制值'))
      }
    }
    // 内存
    const validateAccountNodeMemory = (rule, value, callback) => {
      let numValue = Number(value)
      if (!isNumber(value)) {
        callback(new Error('请在区间内输入正整数'))
      } else if (numValue >= 200 && numValue <= 1000) {
        callback()
      } else {
        callback(new Error('最小输入200,最大不能超过内存限制值'))
      }
    }
    // 硬盘
    const validateAccountNodeDisk = (rule, value, callback) => {
      let numValue = Number(value)
      if (!isNumber(value)) {
        callback(new Error('请在区间内输入正整数'))
      } else if (numValue <= 1000) {
        callback()
      } else {
        callback(new Error('最大输入1000'))
      }
    }
    // // cpu最大限制值
    // const validatenodeLimiCpu = (rule, value, callback) => {
    //   let numValue = Number(value)
    //   if (!isNumber(value)) {
    //     callback(new Error('请在区间内输入正整数'))
    //   } else if (numValue >= 200 && numValue <= 1000) {
    //     callback()
    //   } else {
    //     callback(new Error('最大输入1000'))
    //   }
    // }
    // // 内存最大限制值
    // const validatenodeLimitMemory = (rule, value, callback) => {
    //   let numValue = Number(value)
    //   if (!isNumber(value)) {
    //     callback(new Error('请在区间内输入正整数'))
    //   } else if (numValue >= 200 && numValue <= 1000) {
    //     callback()
    //   } else {
    //     callback(new Error('最大输入1000'))
    //   }
    // }
    return {
      timer: '',
      zwList: '',
      spinShow: false,

      detailinfo: {},
      nodeConfigured: {
        nodeName: '',
        nodeDisk: '',
        nodeMemory: '',
        nodeCpu: ''
        // nodeLimitMemory: '',
        // nodeLimitCpu: ''
      },
      noderuleValidate: {
        nodeName: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, max: 20, message: '不能超过20位', trigger: 'blur' },
          { trigger: 'blur', validator: validateipfstName }
          // {
          //   type: 'string',
          //   pattern: /^[0-9a-zA-Z]*$/g, /// ^\w+$/
          //   message: '格式应为0-9a-zA-Z',
          //   trigger: 'blur'
          // }
        ],
        nodeCpu: [
          {
            required: true,
            validator: validateAccountCpu,
            trigger: 'blur'
          }
        ],

        nodeDisk: [
          {
            required: true,
            validator: validateAccountNodeDisk,
            trigger: 'blur'
          }
        ],
        nodeMemory: [
          {
            required: true,
            validator: validateAccountNodeMemory,
            trigger: 'blur'
          }
        ]
        // nodeLimitCpu: [
        //   {
        //     required: true,
        //     validator: validatenodeLimiCpu,
        //     trigger: 'blur'
        //   }
        // ],
        // nodeLimitMemory: [
        //   {
        //     required: true,
        //     validator: validatenodeLimitMemory,
        //     trigger: 'blur'
        //   }
        // ]
      },
      alertTitle: '新增节点',
      addModal: false,
      transferKey: 0,
      formValidate: {
        name: '',
        desc: ''
      },
      ruleValidate: {
        name: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ]
      },
      columns: [
        { key: 'nodeName', title: '节点名称' },
        { key: 'nodeAddress', title: 'IP及端口号', tooltip: true },
        { key: 'nodeDisk', title: '硬盘（Gi）' },
        { key: 'nodeCpu', title: 'CPU（m）' },
        { key: 'nodeMemory', title: '内存（Mi）', tooltip: true },
        {
          key: 'status',
          title: '节点状态'
          // render: (h, params) => {
          //   const color = params.row.status === '关闭' ? '#C7C7C7' : '#15AD31'
          //   return h('Tag', {
          //     props: {
          //       type: 'dot',
          //       color: color
          //     },
          //     style: { marginLeft: '-8px' }
          //   }, params.row.status)
          // }
        },
        { key: 'createTime', title: '创建时间', tooltip: true },
        {
          key: 'action',
          title: '操作',
          minWidth: 120,
          render: (h, params) => {
            return h('div', [
              h(
                'Poptip',
                {
                  props: {
                    transfer: true,
                    placement: 'top-end',
                    confirm: true,
                    title: '确认删除吗?',
                    'ok-text': '确认',
                    'cancel-text': '取消'
                  },
                  on: {
                    'on-ok': () => {
                      this.deletedData(params)
                    }
                  }
                },
                [
                  h(
                    'Button', {
                      props: { type: 'text', size: 'small' },
                      style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF', display: params.row.sfyc === true ? 'black' : 'none' }
                    }, '删除'
                  )
                ]
              ),
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    this.editDetails(params)
                  }
                }
              }, '编辑')
            ]

            )
          }
        }
      ],
      tableData: [],
      tablePageParam: {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      },
      noderow: {},
      resous: {}
    }
  },
  methods: {
    // blurcpu (value) {
    //   if (value<this.noderow.nodeDisk) {
    //     this.msgInfo('error', '不能低于最近一次硬盘值', true)
    //   }
    // },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    editDetails (data) {
      this.noderow = data.row
      let row = data.row
      this.alertTitle = '节点修改'
      this.addModal = true
      this.nodeConfigured = {
        nodeName: row.nodeName,
        nodeDisk: row.nodeDisk.split('/')[1],
        nodeMemory: row.nodeMemory.split('/')[1],
        nodeCpu: row.nodeCpu.split('/')[1]
        // nodeLimitCpu: data.row.nodeLimitCpu,
        // nodeLimitMemory: data.row.nodeLimitMemory
      }
      getNodeInfo(this.noderow.nodeId).then(res => {
        this.resous = res.data
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },

    //
    add () {
      this.addModal = true
      this.alertTitle = '新增节点'
      this.$refs['nodeConfigured'].resetFields()
    },
    // 删除
    deletedData (row) {
      // console.log(row)
      nodeDelete(row.row.ipfsId, row.row.nodeId).then(res => {
        if (res.code === '00000') {
          this.detailNodeList()
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
      // this.tableData.splice(index, 1)
    },
    // 配置节点
    handleSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.alertTitle === '新增节点') {
            let nodeadd = {
              ipfsId: this.detailinfo.ipfsId,
              ipfsNodeDTO: this.nodeConfigured
            }
            nodeSave(nodeadd).then(res => {
              if (res.code === '00000') {
                this.addModal = false
                this.spinShow = true
                this.detailNodeList()
                this.$refs[name].resetFields()
              } else {
                this.msgInfo('error', res.message, true)
                // this.$refs[name].resetFields()
              }
            }).catch(error => {
              this.msgInfo('error', error.message, true)
            })
          } else {
            if (this.nodeConfigured.nodeDisk<this.resous.yuNodeDisk) {
              this.msgInfo('error', '硬盘值不可小于硬盘已使用量', true)
            } else {
              let updatanode = {
                ipfsId: this.noderow.ipfsId,
                nodeId: this.noderow.nodeId,
                nodeName: this.nodeConfigured.nodeName,
                nodeCpu: this.nodeConfigured.nodeCpu,
                nodeDisk: this.nodeConfigured.nodeDisk,
                nodeMemory: this.nodeConfigured.nodeMemory,
                yuNodeDisk: this.resous.yuNodeDisk,
                yuNodeCpu: this.resous.yuNodeCpu,
                yuNodeMemory: this.resous.yuNodeMemory
              // nodeLimitMemory: this.nodeConfigured.nodeLimitMemory,
              // nodeLimitCpu: this.nodeConfigured.nodeLimitCpu
              }
              nodeUpdate(updatanode).then(res => {
                if (res.code === '00000') {
                  this.addModal = false
                  this.spinShow = true
                  this.detailNodeList()
                  this.msgInfo('success', res.message, true)
                } else {
                  this.msgInfo('error', res.message, true)
                }
              }).catch(error => {
                this.msgInfo('error', error.message, true)
              })
            }
          }
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },
    handleReset (name) {
      this.$refs[name].resetFields()
      this.addModal = false
    },

    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.detailNodeList()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.detailNodeList()
    },
    // 详情节点列表
    detailNodeList () {
      let detaildata = {
        ipfsId: this.$route.params.ipfsId,
        pageParam: this.tablePageParam
      }

      nodeList(detaildata).then(res => {
        if (res.code === '00000') {
          this.tableData = res.data.records.map((item) => {
            item.nodeAddress = item.nodeAddress ? item.nodeAddress + item.nodeApiPort : ''
            item.nodeDisk = item.nodeDisk ? item.yuNodeDisk + '/' + item.nodeDisk : ''
            item.nodeCpu = item.nodeCpu ? item.yuNodeCpu + '/' + item.nodeCpu : ''
            item.nodeMemory = item.nodeMemory ? item.yuNodeMemory + '/' + item.nodeMemory : ''
            return item
          })

          this.spinShow = false
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    networkStaus () {
      let statusList = {
        ipfsId: this.$route.params.ipfsId
      }
      getNetwork(statusList).then(res => {
        this.zwList = res.data.join('，')
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    cerateTimer () {
      this.timer = setInterval(() => {
        this.networkStaus()
      }, 5000)
    }
  },
  mounted () {
    if (this.$route.params.ipfsId === undefined) {
      this.$router.push({
        name: 'ipfs_network'
      })
    } else {
      this.cerateTimer()
      this.detailNodeList()
      this.detailinfo = this.$route.params
      this.networkStaus()
    }

    console.log(this.$route.params)
  },
  beforeDestroy () {
    clearInterval(this.timer)
    this.timer = null
  }

}
</script>

<style lang="less" scoped>
    .demo-spin-icon-load{
        animation: ani-demo-spin 1s linear infinite;
    }
/deep/.ivu-input-type-textarea{
  height: 200px;
}
.bs {
  float: left;
  width: 6px;
  height: 18px;
  background: #19c3a0;
  opacity: 1;
  border-radius: 3px;
}
span {
  padding-left: 6px;
}
.info-title {
  font-size: 16px;
  font-weight: bold;
  vertical-align: middle;
  height: 18px;
  font-family: "Microsoft YaHei";
  line-height: 18px;
  color: #333333;
  margin: 10px 0 25px 0px;
  // &.addflex {
  //   display: flex;
  //   justify-content: space-between;
  //   // .btns {
  //   //   button {
  //   //     margin-right: 20px;
  //   //   }
  //   // }
  // }
}
</style>
