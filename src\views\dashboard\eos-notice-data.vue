<template>
  <div style="width:100%;height: calc(25.5vh);" class="notice-wrap">
    <!-- <div class="notice" :style="{height: 'calc(' + noticeHeight +')'}"> -->
    <div class="notice" :style="{height: '100%'}">

      <Row style="padding:0 15px;display:flex;justify-content: space-between;margin-bottom: 10px;">
        <div style="display:flex">
          <span class="announce-more"></span>
          <span class="title-1">消息通知</span>
        </div>
        <div class="eosMaor" @click="getMoreX">
          查看更多》
        </div>
      </Row>

      <Tabs type="card" :animated="false" @on-click='tabsFun' v-model="tabsValue" style=" padding-left: 20px;">
        <TabPane label="待办" name='WAIT_DEAL'>
          <div id="noticeScroll" ref="noticeScroll" v-if="announcementData && announcementData.length > 0 ? true : false" class="announce scroll" :style="{maxHeight: getHeight}" style="height: calc(11vh + 10px);">
            <ul v-for="(item) in announcementData" :key="item.noticeId" class="count-to-con">
              <li>
                <div>
                  <span class="circle"></span>
                  <p class="title-3">
                    <span>{{getTimePeriod(item.createTime)}}</span>
                    <span class="publishDate">{{item.createTime}}</span>
                  </p>
                  <p class="title-4" v-html="item.noticeMessage" @click="clickTo1($event, item)"></p>
                </div>
              </li>
            </ul>
            <p style="text-align:center;">
              <span style="font-size:8px;color:#57a3f3;cursor:pointer;" @click="getMore" :style="getHidden">
                更多<img :src="imgUrl" style="cursor:pointer;margin-left:2px;" @click="getMore">
              </span>
            </p>
          </div>
          <div class="data-none" v-else>
            <img :src="imagesurl">
            <p class="title-none" style="">暂无消息</p>
          </div>
        </TabPane>
        <TabPane label="待阅" name='WAIT_READ'>
          <div id="noticeScroll" ref="noticeScroll" v-if="announcementData && announcementData.length > 0 ? true : false" class="announce scroll" :style="{maxHeight: getHeight}" style="height: calc(11vh + 10px);">
            <ul v-for="(item) in announcementData" :key="item.noticeId" class="count-to-con">
              <li>
                <div>
                  <span class="circle"></span>
                  <p class="title-3">
                    <span>{{getTimePeriod(item.createTime)}}</span>
                    <span class="publishDate">{{item.createTime}}</span>
                  </p>
                  <p class="title-4" v-html="item.noticeMessage" @click="clickTo($event, item)"></p>
                </div>
              </li>
            </ul>
            <p style="text-align:center;">
              <span style="font-size:8px;color:#57a3f3;cursor:pointer;" @click="getMore" :style="getHidden">
                更多<img :src="imgUrl" style="cursor:pointer;margin-left:2px;" @click="getMore">
              </span>
            </p>
          </div>
          <div class="data-none" v-else>
            <img :src="imagesurl">
            <p class="title-none" style="">暂无消息</p>
          </div>
        </TabPane>

      </Tabs>

      <!-- <Row v-else>
        <div class="data-none">
          <img :src="imagesurl">
          <p class="title-none" style="">暂无平台公告</p>
        </div>
      </Row>
      <Row v-if="roleId === '1'">
        <Button icon="md-add" class="btn" type="default" @click="publish">创建平台公告</Button>
      </Row> -->
    </div>
    <!-- <div class="notice" style="margin-top:10px;" :style="{height: 'calc(' + noticeHeight +')'}">

    <Row style="padding:10px 0 25px 20px;z-index:999;background:#fff;position:relative;"><span class="announce-more"></span><span class="title-1">我的消息</span></Row>
      <div v-if="noticeData.length === 0 ? false : true" class="announce notice-for" :style="{ top, transition }" @mouseover.stop="closeTime" @mouseout.stop="startTime">
        <ul v-for="(item) in noticeData" :key="item.noticeId + Math.random()" class="count-to-con">
          <li>
            <span class="circle-info"></span>
            <p class="title-3">
              <span>{{getTimePeriod(item.noticeTime)}}</span>
              <span class="publishDate">{{item.noticeTime}}</span>
            </p>
            <p class="title-4"><a>{{item.noticeContent}}</a></p>
          </li>
        </ul>
      </div>
      <Row v-else>
        <div class="data-none">
          <img :src="imagesurl">
          <p class="title-none" style="">暂无消息</p>
        </div>
      </Row>
  </div> -->
    <Modal v-model="delModal" :styles="{top: '180px'}" :closable="false" :z-index="1000" :mask-closable="false">
      <div>
        <Icon type="ios-information-circle" size="20" style="color:#f60;"></Icon>
        <span style="margin-left:5px;font-size:16px;">{{'确定要删除[' + delTitle +']公告吗？'}}</span>
      </div>
      <div slot="footer">
        <Button @click="delCancel">取消</Button>
        <Button type="primary" @click="delConfirm">确定</Button>
      </div>
    </Modal>
    <Modal v-model="publishCancelModal" :styles="{top: '180px'}" :closable="false" :mask-closable="false">
      <div>
        <Icon type="ios-information-circle" size="20" style="color:#f60;"></Icon>
        <span style="margin-left:5px;font-size:16px;">编辑内容未保存，确定退出吗？</span>
      </div>
      <div slot="footer">
        <Button @click="pubCancel">取消</Button>
        <Button type="primary" @click="pubConfirm">确定</Button>
      </div>
    </Modal>
    <Modal v-model="editModal" :mask-closable="false" scrollable :title="editData.editTitle" :width="700" :draggable="false" :z-index="999" sticky class="notice-container">
      <Form ref="editData" :rules="editDataRule" :model="editData">
        <FormItem label="标题：" prop="title">
          <Input v-model="editData.title" class='bt1' placeholder="请输入标题" style="max-width:450px;vertical-align:baseline;" />
          <hr>
        </FormItem>
        <FormItem label="发布方：" prop="publisher">
          <Input v-model="editData.publisher" class='bt1' placeholder="请输入发布方" style="max-width:450px;vertical-align:baseline;" />
          <hr>
        </FormItem>
      </Form>
      <div :class="uploadClass">
        <Upload :format="typeList" :on-format-error="handleFormatError" multiple action="" :max-size="maxsize" :before-upload="handleUpload" :on-exceeded-size="handleMaxSize" style="float:left;">
          <Icon type="md-attach" style="transform:rotate(45deg);color:#57a3f3;" /><span class="click" style="color:#57a3f3;">添加附件</span>
        </Upload>
        <div><span class="size-color" style="">{{'（单文件最大'+ maxsize / 1024 +'M，文件类型只能为png，jpg，jpeg，xls，xlsx，doc，docx，pdf，mov，rmvb，mp4，avi）'}}</span></div>
      </div>
      <div v-show="editData.uploadFiles.length > 0 ? true : false" style="font-size:8px;background-color: #abccf052;padding:5px 0 0 5px;" v-for="(item, i) in editData.uploadFiles" :key="i">
        <div class="upload-file">
          <Icon type="md-list-box" style="color:#57a3f3;margin-top:-5px;" size="18" />
          {{item.fileName}}
          <span style="color:#bdbbbb;margin-left:5px;">({{getFileSize(item.fileSize)}})</span>
          &nbsp;&nbsp;&nbsp;
          <span style="color:#57a3f3;cursor:pointer;font-size:8px;" @click="fileDel(item.fileName)">删除</span>
        </div>
      </div>
      <div style="padding-top:10px;">
        <!-- <tinymce-editor ref="editor" v-model="editData.content" :disabled="disabled" @onClick="onClick" @onBlur="onBlur">
        </tinymce-editor> -->
        <span class="edit-title" style="color:red;float:left;" v-show="contentFlag">内容不能为空</span>
        <!-- <p class="att">(编辑器注意事项：分段才按Enter键进行换行，否则会被编辑器认为是段落而导致两行之间间距很大)</p> -->
      </div>
      <div slot="footer">
        <Button type="text" @click="editCancel">取消</Button>
        <Button type="primary" @click="editOk">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
// import TinymceEditor from '_c/tinymce/tinymce-editor'
import { getAnnouncement, getNotice, deleteAnnouncement, updateAnnouncement, uploadAnnounceFile, downloadAnnounceFile, noticeManage, read } from '@/api/data'
import { localRead } from '@/lib/util'
export default {
  name: 'notice-data',
  // components: {
  //   TinymceEditor
  // },
  props: {
    noticeHeight: {
      type: String,
      default: '45vh + 148px'
    }
  },
  data () {
    return {
      typeList: ['png', 'jpg', 'jpeg', 'xls', 'xlsx', 'doc', 'docx', 'pdf', 'mov', 'rmvb', 'mp4', 'avi'],
      uploadClass: 'edit-upload-file',
      imgUrl: require('@/assets/img/arrow.png'),
      publishCancelModal: false,
      delModal: false,
      showFile: true,
      checkFlag: true,
      editModal: false,
      moreFlag: true,
      contentFlag: false,
      editData: {
        editTitle: '',
        title: '',
        publisher: '',
        content: '',
        announceId: '',
        fileIdList: [],
        uploadFiles: []
      },
      disabled: false,
      iconSize: 30,
      imagesurl: require('@/assets/img/null.png'),
      announcementData: [],
      noticeData: [],
      activeIndex: 0,
      timerval: null,
      timerval2: null,
      timerout: null,
      roleId: localRead('roleId'),
      maxsize: localStorage.getItem('MAX_FILE_SIZE') ? JSON.parse(localStorage.getItem('MAX_FILE_SIZE')) : 2048,
      publishOrEdit: false,
      publishOrDel: false,
      uploadList: [],
      fileList: [],
      count: 0,
      total: 0,
      pages: 0,
      pageParam: {
        pageIndex: 1,
        pageSize: 8
      },
      announceId: 0,
      editDataRule: {
        title: [{ required: true, message: '标题不能为空', trigger: 'blur' },
        { max: 50, message: '不能多于50个字符', trigger: 'blur' }],
        publisher: [{ required: true, message: '发布方不能为空', trigger: 'blur' },
        { max: 30, message: '不能多于30个字符', trigger: 'blur' }]
      },
      delTitle: '',
      tabsValue: localStorage.getItem('roleId') == 1 ? 'WAIT_DEAL' : 'WAIT_READ',
      processStatus: localStorage.getItem('roleId') == 1 ? 'UNDEAL' : 'UNREAD',
      noticeIdList: []
    }
  },
  computed: {
    top () {
      return -this.activeIndex * 50 + 'px'
    },
    transition () {
      return this.activeIndex === 0 ? 'none' : 'top 0.5s'
    },
    getHidden () {
      if (this.count < this.total) {
        return ''
      } else {
        return 'display:none'
      }
    },
    getHeight () {
      if (this.roleId === '1') {
        return '115vh'
      } else {
        return '125vh'
      }
    }
  },
  methods: {
    getMoreX () {
      this.$router.push({
        name: 'user_info',
        params: {
          cur: 3
        }
      })
    },
    clickTo1 (e, item) {
      if (e.target.localName.toLowerCase() === 'span') {
        if (this.routerTo1(item)) {
          this.$emit('closeDrawer')
        }
      }
    },
    // 待办
    routerTo1 (item) {
      // 链账户审批
      if (item.bizType === 'ACCOUNT_AUDIT') {
        this.$router.push({
          name: 'chain-approvel'
        })
        // 合约部署审批
      } else if (item.bizType === 'DEPLOY_AUDIT') {
        if (this.$route.path === '/contract-approvel') {
          this.$router.go(0)
        } else {
          this.$router.push({
            name: 'contract-approvel',
            params: {
              tabs: 'name1'
            }
          })
        }

        // 工单审批
      } else if (item.bizType === 'ORDER_AUDIT') {
        this.$router.push({
          name: 'workorder-approvel'
        })
        // 密码过期
      } else if (item.bizType === 'PASSWORD_EXPIRE') {
        this.updateCur(1)
        this.$router.push({
          name: 'user_info',
          params: {
            cur: 1
          }
        })
      } else if (item.bizType === 'SHARE_AUDIT') {
        // console.log('item:', item)
        this.$router.push({
          name: 'sharecontract-approvel',
          params: {
            tabs: 'name1'
          }
        })
        // 链账户资源管理
      } else if (item.bizType === 'ACCOUNT_UNASSIGNED') {
        // console.log('item:', item)
        this.$router.push({
          name: 'token_admin'
        })
        // 个人中心待办
      } else if (item.bizType === 'CONTRACT_APP_ON' || item.bizType === 'MARKET_AUDIT' || item.bizType === 'MARKET_AUDIT_OFF' || item.bizType === 'MARKET_AUDIT_REON') {
        this.$router.push({
          name: 'shelves_approval'
        })
      } else {
        this.msgInfo('warning', item.bizType + '没找到匹配路由', true)
        return false
      }
      return true
    },
    // 待阅
    routerTo (item) {
      var jsonObj = item.linkParams ? JSON.parse(item.linkParams) : {}
      console.log('item:', jsonObj)

      // 链管理详情页
      if (item.bizType === 'NODE_ABNORMAL') {
        this.$router.push({
          name: 'multilink_details',
          params: {
            eosChainId: jsonObj.chainId,

          }
        })
        //  链账户资源管理
      } else if (item.bizType === 'ACCOUNT_RESULT') {
        this.$router.push({
          name: 'chain_table',
          query: {
            chainId: jsonObj.chainId,
            chainName: jsonObj.chainName,
            chainAccountName: jsonObj.chainAccountName
          }
        })
        // 合约部署审批
      } else if (item.bizType === 'DEPLOY_RESULT') {
        if (this.$route.path === '/contract_table') {
          if (!item.processStatus) {
            this.noticeIdList = []
            this.noticeIdList.push(item.noticeId)
            item.processStatus = true
            this.postRead(this.noticeIdList, true)
          } else {
            this.$router.go(0)
          }
        } else {
          this.$router.push({
            name: 'contract_table',
            params: {
              contractId: jsonObj.contractId
            }
          })
        }

        // 工单审批
      } else if (item.bizType === 'ORDER_RESULT') {
        this.$router.push({
          name: 'workorder_detail',
          params: {
            orderId: jsonObj.orderId
          }
        })
        // 所属租户变更
      } else if (item.bizType === 'TENANT_USER_UPDATE') {
        this.updateCur(2)
        this.$router.push({
          name: 'user_info',
          params: {
            cur: 2
          }
        })
        // 租户成员变动
      } else if (item.bizType === 'OWNER_TENANT_UPDATE') {
        this.$router.push({
          name: 'tenant_details',
          params: {
            tenantId: jsonObj.tenantId
          }
        })
      } else if (item.bizType === 'PASSWORD_RESET' || item.bizType === 'PASSWORD_MODIFY' || item.bizType === 'SHARE_OFF' || item.bizType === 'SHARE_UNBIND' || item.bizType === 'SHARE_DELETE_TENANT') {
        return false
      } else if (item.bizType === 'SHARE_RESULT') {
        this.$router.push({
          name: 'contract_details',
          params: {
            contractId: jsonObj.contractId
          }
        })
      } else if (item.bizType === 'SHARE_RECEIVE') {
        this.updateFlag(false)
        this.$router.push({
          name: 'contract_area',
          params: {
            isContractFlag: false
          }
        })
      } else if (item.bizType === 'SHARE_UPDATE') {
        this.$router.push({
          name: 'sharecontract_details',
          params: {
            shareRecordId: jsonObj.shareRecordId,
            receivedTenantId: item.receivedTenantId
          }
        })
        // 个人中心待阅
      } else if (item.bizType === 'CONTRACT_MARKET_ON' || item.bizType === 'CONTRACT_MARKET_OFF' || item.bizType === 'MARKET_OFF_SUCCESS' || item.bizType === 'MARKET_OFF_FAILED' || item.bizType === 'MARKET_REON_SUCCESS' || item.bizType === 'MARKET_REON_FAILED' || item.bizType === 'CONTRACT_APP_ON' || item.bizType === 'CONTRACT_APP_OFF') {
        this.$router.push({
          name: 'contract_market',
          params: {
            tabs: 'name2'
          }
        })
        // 链账户资源分配待阅
      } else if (item.bizType === 'ACCOUNT_ASSIGNED') {
        this.$router.push({
          name: 'chain_table'
        })
      } else {
        this.msgInfo('warning', item.bizType + '没找到匹配路由', true)
        return false
      }
      return true
    },
    // 待阅
    clickTo (e, item) {
      if (e.target.localName.toLowerCase() === 'span') {
        if (this.routerTo(item)) {
          this.$emit('closeDrawer')
          this.noticeIdList = []
          this.noticeIdList.push(item.noticeId)
          this.postRead(this.noticeIdList)

        }
      }
    },
    postRead (list, flag) {
      read(list).then(res => {
        if (res.code === '00000') {
          if (flag) {
            this.$router.go(0)
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    tabsFun (e) {
      this.tabsValue = e
      this.processStatus = e == 'WAIT_DEAL' ? 'UNDEAL' : 'UNREAD'
      this.announcementData = []
      this.pageParam = {
        pageSize: 8,
        pageIndex: 1
      },
        this.getAnnouncementData()
    },
    handleFormatError (file) {
      this.uploadClass = 'edit-upload-file'
      return false
    },
    unique (arr) {
      const res = new Map()
      return arr.filter((arr) => !res.has(arr.noticeId) && res.set(arr.noticeId, 1))
    },
    closeTime () {
      clearInterval(this.timerval)
      clearInterval(this.timerval2)
      clearTimeout(this.timerout)
      this.timerval = null
      this.timerout = null
      this.activeIndex = 0
    },
    startTime () {
      if (this.noticeData[0]) {
        this.timerval = setInterval(_ => {
          this.noticeData.push(this.noticeData[0])
          this.unique(this.noticeData)
          this.activeIndex += 1
        }, 3000)
        this.timerout = setTimeout(_ => {
          this.timerval2 = setInterval(_ => {
            this.noticeData.splice(0, 1)
            this.activeIndex = 0
          }, 3000)
        }, 500)
      }
    },
    getContentFlag () {
      if (this.editData.content && this.editData.content.length > 0) {
        this.contentFlag = false
      } else {
        this.contentFlag = true
      }
    },
    editDataInit () {
      this.$nextTick(() => {
        this.$refs['editData'].resetFields()
      })
      this.contentFlag = false
    },
    getMore () {
      if (this.pageParam.pageIndex < this.pages) {
        this.pageParam.pageIndex += 1
        const scrollDiv = document.getElementById('noticeScroll')
        scrollDiv.scrollTo({
          top: scrollDiv.scrollHeight,
          behavior: 'smooth'
        })
        this.getAnnouncementData(true)
      }
    },
    getTitleWidth (value) {
      if (value.length * 22 > 520) {
        return 'max-width:' + 520 + 'px'
      } else {
        return `min-width:${value.length * 22}px;width:100%`
      }
    },
    edit (item) {
      this.editDataInit()
      this.editData = {
        editTitle: '编辑公告',
        title: item.title,
        publisher: item.publisher,
        content: item.content,
        announceId: item.announceId,
        fileIdList: [],
        uploadFiles: item.uploadFiles
      }
      this.editModal = true
      this.publishOrEdit = false
    },
    announceDel (item) {
      this.delTitle = item.title
      this.delModal = true
      this.announceId = item.announceId
    },
    delCancel () {
      this.delModal = false
    },
    delConfirm () {
      deleteAnnouncement(this.announceId).then(res => {
        if (res.code === '00000') {
          this.msgInfo('success', res.message, true)
          this.pageParam.pageIndex = 1
          this.getAnnouncementData()
          this.delModal = false
        } else {
          this.msgInfo('warning', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    pubCancel () {
      this.publishCancelModal = false
    },
    pubConfirm () {
      this.publishCancelModal = false
      this.editModal = false
    },
    editOk () {
      this.$refs['editData'].validate((valid) => {
        if (valid) {
          if (this.editData.content && this.editData.content.length > 0) {
            this.contentFlag = false
            if (this.editData.uploadFiles && this.editData.uploadFiles.length > 0) {
              this.editData.uploadFiles.forEach(element => {
                this.editData.fileIdList.push(element.fileId)
              })
            }
            updateAnnouncement(this.editData).then(res => {
              if (res.code === '00000') {
                this.pageParam.pageIndex = 1
                this.getAnnouncementData()
                this.msgInfo('success', res.message, true)
                this.editModal = false
              } else {
                this.msgInfo('error', res.message, true)
                this.editData.fileIdList = []
              }
            }).catch(error => {
              this.msgInfo('error', error.message, true)
              this.editData.fileIdList = []
            })
          } else {
            this.contentFlag = true
          }
        } else {
          if (this.editData.content && this.editData.content.length > 0) {
            this.contentFlag = false
          } else {
            this.contentFlag = true
          }
          this.msgInfo('warning', '标题、发布方或者内容不符合规范，请检查', true)
        }
      })
    },
    editCancel () {
      if (this.publishOrEdit) {
        this.publishCancelModal = true
      } else {
        this.editModal = false
      }
    },
    publish () {
      this.editDataInit()
      this.publishOrEdit = true
      this.editData = {
        editTitle: '发布公告',
        announceId: null,
        title: '',
        publisher: '',
        content: '',
        uploadFiles: [],
        fileIdList: []
      }
      this.editModal = true
    },
    onClick (e, editor) {
      this.getContentFlag()
    },
    onBlur (e, editor) {
      this.getContentFlag()
    },
    initEditor () {
      // this.$refs.editor.clear()
      this.$refs.editor.initEditor()
    },
    handleMaxSize (file) {
      this.uploadClass = 'edit-upload-file'
      return false
    },
    handleUpload (file) {
      this.uploadClass = 'edit-upload-file-before'
      var result = this.editData.uploadFiles.some(item => {
        if (item.fileName === file.name) {
          return true
        }
      })
      if (!result) {
        // var name = file.name.substring(file.name.indexOf('.') + 1)
        // var name = file.name.split('.')
        // var fileType = name[name.length - 1]
        // let typeString = file.type.split('/')[1]
        // console.log('strs:', file, name[name.length - 1], this.typeList.includes(name[name.length - 1]))
        // if (fileType.match(/^.*[A-Z]+.*$/)) {
        //   this.msgInfo('error', `上传的文件类型为${fileType}，包含大写，不符合上传的文件类型`, true)
        //   this.uploadClass = 'edit-upload-file'
        //   return
        // }
        if (file.size / 1024 / 1024 <= this.maxsize / 1024) {
          let typeString = file.type.split('/')[1]
          let flag = false
          this.typeList.forEach(val => {
            if ((typeString && typeString.indexOf(val) !== -1) || file.name.indexOf(`.${val}`) !== -1) {
              flag = true
            }
          })
          if (!flag) {
            this.uploadClass = 'edit-upload-file'
            this.msgInfo('warning', file.name + '格式不正确,只能为jpg、png、jpeg、xls、xlsx、doc、docx、pdf、mov、rmvb、mp4、avi', true)
            return
          }
          var fileObj = {}
          uploadAnnounceFile('ANNOUNCE', file).then(res => {
            if (res.code === '00000') {
              fileObj.fileName = file.name
              fileObj.file = file
              fileObj.fileSize = file.size
              fileObj.fileId = res.data.fileId
              fileObj.fileHash = res.data.fileHash
              var reName = ''
              var flag = this.editData.uploadFiles.some(item => {
                if (item.fileId === res.data.fileId) {
                  reName = item.fileName
                  return true
                }
              })
              if (flag) {
                this.msgInfo('error', `当前上传文件名${file.name}内容与${reName} 相同，请检查`, true)
              } else {
                this.editData.uploadFiles.push(fileObj)
              }
            } else {
              this.msgInfo('error', res.message, true)
            }
            this.uploadClass = 'edit-upload-file'
          }).catch(error => {
            this.uploadClass = 'edit-upload-file'
            this.msgInfo('error', error.message, true)
          })
        } else {
          this.uploadClass = 'edit-upload-file'
          this.msgInfo('warning', '文件' + file.name + '太大,不能超过' + this.maxsize / 1024 + 'M', true)
        }
      } else {
        this.msgInfo('warning', '文件' + file.name + '已上传', true)
        this.uploadClass = 'edit-upload-file'
        return false
      }
    },
    fileDel (fileName) {
      this.editData.uploadFiles = this.editData.uploadFiles.filter(item => item.fileName !== fileName)
    },
    downloadFile (fileId, fileName) {
      downloadAnnounceFile(fileId).then(res => {
        let blob = new Blob([res])
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = fileName
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      }).catch(error => {
        this.msgInfo('error', error, true)
      })
    },
    getDay (value) {
      var dt = new Date(value.replace(/-/g, '/'))
      dt.setMonth(dt.getMonth() + 1)
      dt.setDate(0)
      return dt.getDate()
    },
    getTimePeriod (value) {
      var dateBegin = new Date(value.replace(/-/g, '/'))
      var maxDate = this.getDay(value)
      var dateEnd = new Date()
      var diff = dateEnd - dateBegin
      var meg = ''
      if (diff / 1000 / 60 < 1) {
        meg = Math.floor(diff / 1000) + '秒前'
      } else if (diff / 1000 / 60 >= 1 && diff / 1000 / 3600 < 1) {
        meg = Math.floor(diff / 1000 / 60) + '分钟前'
      } else if (diff / 1000 / 3600 >= 1 && diff / 1000 / (3600 * 24) < 1) {
        meg = Math.floor(diff / 1000 / 3600) + '小时前'
      } else if (diff / 1000 / (3600 * 24) >= 1) {
        if (dateEnd.getFullYear() - dateBegin.getFullYear() >= 1) {
          if (dateEnd.getFullYear() - dateBegin.getFullYear() === 1 && dateEnd.getMonth() < dateBegin.getMonth()) {
            meg = dateEnd.getMonth() + 12 - dateBegin.getMonth() + '个月前'
          } else {
            meg = dateEnd.getFullYear() - dateBegin.getFullYear() + '年前'
          }
        } else if (dateEnd.getMonth() - dateBegin.getMonth() >= 1) {
          if (dateEnd.getMonth() - dateBegin.getMonth() === 1 && dateEnd.getDate() < dateBegin.getDate()) {
            meg = dateEnd.getDate() + maxDate - dateBegin.getDate() + '天前'
          } else {
            meg = dateEnd.getMonth() - dateBegin.getMonth() + '个月前'
          }
        } else {
          meg = dateEnd.getDate() - dateBegin.getDate() + '天前'
        }
      }
      return meg
    },
    getFileSize (value) {
      if (value < 1024) {
        return value + 'B'
      } else if (value / (1024 * 1024) < 1) {
        return (value / 1024).toFixed(2) + 'KB'
      } else {
        return (value / 1024 / 1024).toFixed(2) + 'MB'
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    getAnnouncementData (flag) {
      noticeManage(this.tabsValue, this.pageParam, null, null, null, this.processStatus).then(res => {
        if (res.code === '00000') {
          if (flag) {
            this.announcementData.push.apply(this.announcementData, res.data.records)
          } else {
            this.announcementData = res.data.records
          }
          this.pages = res.data.pages
          this.total = res.data.total
          this.count = this.announcementData.length
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getNoticeData () {
      getNotice().then(res => {
        if (res.code === '00000') {
          this.noticeData = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  mounted () {
    this.getAnnouncementData()
  },
  watch: {
    noticeHeight: {
      handler (newVal, oldVal) {
        this.noticeHeight = newVal
      },
      deep: true,
      immediate: false
    }
  }
}
</script>
<style lang="less">
.notice-container {
  .ivu-poptip-popper {
    z-index: 999;
  }
  .ivu-modal-mask {
    z-index: 1000 !important;
  }
  .ivu-modal-wrap {
    z-index: 1000 !important;
  }
}
</style>
<style lang="less" scoped>
.btn{
margin:5px 10px 0 10px;
width:100%;
height:4vh;
position:relative;
// background:#F2F6FD ;
color:#519FF2;border:1px solid #519FF2
// border: none;
}
  // .transfer {
  //   transform: scale(0.8);
  // }
  .title {
    font-size:16px;
    font-weight:bold;
  }
  .click {
    cursor: pointer;
  }
  a {
    color:#000000;
    cursor: default;}
  a:hover {
    color:#0000ff}
  a:active {
    color:#0000ff}
  .title-1{
    .title;
        font-weight: bold;
    padding-left: 6px;
    font-size: 16px;
    // height:18px;
    // font-size:16px;
    // float: left;
    // // margin-top:17px;
    // padding-left:6px;
    // vertical-align: middle;

  }
  .title-3{
    font-size:12px;
    color:#9B9B9B;
    margin-top:-15px;
    margin-left:13px;
    font-family: 'Microsoft YaHei';
    font-weight: 400;
    .publishDate{
      float: right;
      text-align: right;
      margin-right:5px;
      margin-top:-15px;
    }
  }
  .title-4{
    font-size:14px;
    color:#333333;
    font-family: 'Microsoft YaHei';
    font-weight: 400;
    // padding-top: 10px;
    // padding-bottom: 10px;
    margin-left:15px;
    margin-right:0px;
    word-break:break-word;
    white-space: pre-wrap;
  }
  .notice-wrap{
    height: 100%;
    background-image: #fff;
  }
  .notice{
    // height: calc(45vh + 47px + 41px + 100px);
    margin-left:2px;
    // background-color: #fff;
    border-radius: 3px;
    margin-bottom: 8px;
    padding-top: 16px;
    background-color: #fff;
    #noticeScroll{
      height:auto;
      max-height: 100%;
    }
    &:last-child{
      margin-bottom:0;
    }
    ul,li{ list-style:none;margin-left:-16px;padding-top:5px;}
    li {
    font-size:14px;
    padding:5px 20px 5px 10px;
    }
    li span{
      vertical-align: middle;
      display: table-cell;
    }
    li:hover{
      background-color: #e6eaf5;
      z-index:-1;
      .circle{
        border: 4px solid #21f3be;
      }
      .announce{
        z-index:999;
      }
      .circle-info{
        border: 4px solid #477af1;
      }
    }
    .circle{
      position: relative;
      width: 11px;
      height: 11px;
      border: 3px solid #7ba0f5;
      border-radius: 50%;
      opacity: 0.6;
      background-color: #E0E6F3;
      z-index:999;
      }
    .circle-info{
      .circle;
      border: 3px solid #52C7AA;
    }
  }
  .announce{
    height:80%;
    overflow: hidden;
    //position: relative; //此属性会影响公告样式
    transition: top 0.5s;
    .count-to-con{
      // width: 240px;
      margin-left:24px;
      margin-top:-5px;
      border-left: 1px dashed #E0E6F3;
      z-index:999;
    }
  }
  .notice-for{
    position: relative;
  }
  .announce:hover{overflow-y:auto;}
  .scroll{
    overflow-y:scroll;
    height: calc(15vh + 10px);
    }
 ::-webkit-scrollbar{
    width : 5px;  /*高宽分别对应横竖滚动条的尺寸*/
    min-height: 1px;
  }
 ::-webkit-scrollbar-thumb{
    border-radius   : 10px;
    background-color: rgb(135, 158, 235);
  }
  .data-none{
    position: relative;
    text-align:center;
    vertical-align: middle;
    display: table-cell;
    margin:0 auto;
    width: 30%;
    // padding-top:50px;
    img{
      margin: auto;
      height: 10vh;
      max-width:72%;
      max-height:72%;
    }
    .title-none{
      font-size:8px;
      color:#bdbbbb
    }
  }
  .announce-more{
    margin:3px 0 5px 0;
    width: 6px;
    height: 18px;
    background: #19C3A0;
    opacity: 1;
    border-radius: 3px;
  }
  .ivu-card-shadow{
    box-shadow: none !important;
  }
  /deep/.ivu-card:hover{
    box-shadow: none !important;
    .ivu-card.ivu-card-shadow:hover{
      box-shadow: none !important;
    }
  }
  .an-title{
    font-size: 16px;
    font-family: 'Microsoft YaHei';
    font-weight: bold;
    line-height: 21px;
    opacity: 1;
    padding-top:5px;
    padding-bottom: 10px;
    word-break:break-word;
    white-space: pre-wrap;
  }
  .an-time{
    font-size:14px;
    color:#9B9B9B;
    font-weight: 400;
    line-height: 21px;
    padding-bottom:5px;
    opacity: 1;
  }
  hr{
    height:1px;border:none;border-top: 1px solid #bfbfc0;
    width:100%;
  }
  .edit-title{
    font-size:14px;
    padding-left:10px;
    color:rgb(140, 140, 141)
  }
  .tip-title{
    font-size:10px;
  }
  .edit-author{
    .edit-title;
    padding:10px 0 0 0;
  }
  .edit-upload-file{
    font-size:10px;
    padding-bottom:10px;
  }
  .edit-upload-file-before{
    font-size:10px;
    padding-bottom:45px;
  }
  .size-color{
    color:#bfbfc0;
    padding-left:5px;
    font-size:8px;
    pointer-events: none;
  }
  /deep/.ivu-modal-header-inner{
    font-size:14px;
    font-weight: bold;
  }
  /deep/.ivu-input{
    font-size:13px;
    border:none;
  }
  /deep/.ivu-input:focus{ outline:none; border:none; box-shadow: 0 0 0 1px transparent; }
  /deep/.ivu-form-item-label{font-size:13px;}
  /deep/.an-content{
    padding-top:10px;
    height:110%;
    p:nth-child(n+2){margin-top: -10px; border:0px;}
    ol>li{margin-left:20px;list-style:decimal;}
    ul>li{margin-left:20px;list-style:disc;}
  }
  .an-file{
    background-color: #F7F8FA;
    margin-top:15px;
    font-size:14px;
    max-width:600px;
    padding:5px 0 0 5px;
    .an-attach{
      padding-bottom:10px;
      font-size: 14px;
    }
  }
  .upload-file{padding-bottom:5px;}
  /deep/.ivu-btn-ghost.ivu-btn-dashed{
    color: #57a3f3;
    border-color: #57a3f3;
  }
  /deep/.ivu-avatar:hover{
    background-color:#ffbc31;
    opacity: 0.8;
  }
 .eosMaor {
    font-size: 14px;
    font-family: Microsoft YaHei;
    // font-weight: bold;
      color: cornflowerblue;
      cursor: pointer;
      padding-right: 5px;
    }
</style>
