import axios from '../../index'
const BASEURL = '/cmbaas/portal/fabric/CommonAPI'
// 查询平台概览数据
export function getAllChainStatistic (query) {
  return axios.request({
    url: BASEURL + '?msgType=itcloud%23getAllChainStatistic',
    method: 'get',
    params: query
  })
}
// 查询节点地图
export function getChainNodes (query) {
  return axios.request({
    url: BASEURL + '?msgType=itcloud%23getChainNodes',
    method: 'get',
    params: query
  })
}
