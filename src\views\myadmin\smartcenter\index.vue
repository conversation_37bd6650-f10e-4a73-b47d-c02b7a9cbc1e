<template>
  <!-- 智慧中台-->
  <div>
    <div class="cz_header">
      <Input style="width:15%;margin-right:10px;" placeholder="请输入文件名称" v-model="search_value" @on-enter="getmanagementList" />
      <Button type="primary" icon="ios-search" @click.native="getmanagementList">查询</Button>
      <Button type="primary" ghost icon="md-sync" @click.native="reset">重置</Button>

    </div>
    <div class="cz_table">
      <Table :columns="smartColumns" :data="smartData">
      </Table>
      <Page :total="dataCount" :page-size="tablePageParam.pageSize" show-sizer show-total show-elevator class="paging" @on-change="changepage" :current.sync="tablePageParam.pageIndex" style="text-align: right;margin-top:20px;" @on-page-size-change="pageSizeChange" :pageSizeOpts="pageSizeOpts"></Page>
    </div>
  </div>
</template>
<script>
import { unitZhzt } from '@/api/arrange'
export default {
  data () {
    return {
      pageSizeOpts: [10, 20, 40, 60, 100],
      status: '', // 搜索下拉框值
      dataCount: 0, // 总条数
      search_value: '', // 输入框
      tablePageParam: { pageIndex: 1, pageSize: 10 }, // 分页
      smartColumns: [//   table 表头
        {
          title: '文件名称',
          key: 'fileName',
          tooltip: true
        },
        {
          title: '文件路径',
          key: 'filePath',
          tooltip: true
        },
        {
          title: '文件类型',
          key: 'fileType',
          tooltip: true
        },
        {
          title: '文件大小',
          key: 'fileSize',
          tooltip: true
        },
        {
          title: '文件状态',
          key: 'status'
          // render: (h, params) => {
          //   // const color = params.row.status === '关闭' ? '#C7C7C7' : '#15AD31'
          //   return h('span', {
          //     // props: {
          //     //   // type: 'dot',
          //     //   color: color
          //     // },
          //     style: { marginLeft: '-8px', color: params.row.status === '关闭' ? 'red' : '#15AD31' }
          //   }, params.row.status)
          // }
        },
        {
          title: '创建时间',
          key: 'createTime',
          tooltip: true
        }
      ],
      // 表格数据
      smartData: []
    }
  },
  mounted () {
    this.getmanagementList()
  },

  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },

    // 查询列表接口
    getmanagementList () {
      let params = {
        fileName: this.search_value,
        // ip: this.search_value,
        pageParam: this.tablePageParam // 分页
      }
      unitZhzt(params).then((res) => {
        console.log(res)
        if (res.code === '00000') {
          // let list = res.data.records.map((item) => {
          //   return {
          //     ...item,
          //     useStatus: item.useStatus === '0' ? '未被使用' : '被使用',
          //     node: item.nodeSelectKey + ' ' + '：' + item.nodeSelectValue
          //   }
          // })
          // console.log(list)
          this.smartData = res.data.records
          this.tablePageParam = {
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          this.dataCount = res.data.total
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },

    // 翻页
    changepage (index) {
      this.tablePageParam.pageIndex = index // 当前页
      this.getmanagementList()
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      // 当前展示条数
      this.tablePageParam.pageSize = size
      this.getmanagementList()
    },
    // 重置按钮事件
    reset () {
      this.search_value = ''
      this.status = ''
      this.tablePageParam = { pageIndex: 1, pageSize: 10 }
      this.getmanagementList()
    }
  },
  watch: {
  }

}
</script>

<style lang="less" scoped>
.newnode{
/deep/.ivu-form-item-error-tip{
  width: 135px
}
}
/deep/.ivu-btn-default:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-form-item-content{
  display: flex;
}
.detail_info {
  p {
    padding: 2%;
  }
}
.sloatbtn {
  text-align: center;
}
.cz_header {
  // display: flex;
  margin-top: 10px;
  /deep/ .ivu-select,
  /deep/ .ivu-date-picker {
    width: 15%;
    margin-right: 10px;
  }
}

//
/deep/ .ivu-modal {
  width: 700px;
}
// table
.cz_table {
  margin-top: 2% !important;
}
//

.ivu-btn-primary {
  margin-left: 7px;
}
</style>
