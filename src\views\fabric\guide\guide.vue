<template>
    <div class="guide-content">
        <div class="guide-title">操作引导</div>
        <div class="guide-list flex">
            <div class="guide-item">
                <img src="../../../assets/chainManage_images/overview/guide_icon1.png" alt="">
                <div class="item_tit">1、创建区块链</div>
                <div class="item_text">一键式创建区块链，开启专属区块链服务。</div>
                <div class="item_list">
                    <div>①根据业务需要选择建链模版</div>
                    <div>②一键式部署</div>
                    <div>③链组织与节点管理</div>
                </div>
            </div>
            <div class="guide-item">
                <img src="../../../assets/chainManage_images/overview/guide_icon2.png" alt="">
                <div class="item_tit">2、创建通道</div>
                <div class="item_text">在链上创建通道，将不同业务独立记账。</div>
                <div class="item_list">
                    <div>①为链添加通道</div>
                    <div>②将组织成员加入通道</div>
                    <div>③通道管理</div>
                </div>
            </div>
            <div class="guide-item">
                <img src="../../../assets/chainManage_images/overview/guide_icon3.png" alt="">
                <div class="item_tit">3、部署合约</div>
                <div class="item_text">针对业务开发智能合约，将合约部署至区块链。</div>
                <div class="item_list">
                    <div>①创建并发布合约</div>
                    <div>②在通道中安装合约</div>
                    <div>③在通道中实例化合约</div>
                    <div>④调用已实例化合约</div>
                    <div>⑤合约管理</div>
                </div>
            </div>
            <div class="guide-item">
                <img src="../../../assets/chainManage_images/overview/guide_icon4.png" alt="">
                <div class="item_tit">4、安装应用</div>
                <div class="item_text last_text">区块链应用通过服务接口和区块链无缝对接。</div>
                <div class="item_list">
                    <div>①申请区块链调用权限</div>
                    <div>②部署区块链应用</div>
                    <div>③区块链应用管理</div>
                </div>
            </div>
        </div>
        <div class="btn-wrap">
            <el-button type="primary" class="blue-btn" @click="goStep">创建区块链</el-button>
        </div>
        
    </div>
</template>
<script>
export default {
  methods: {
    goStep() {
      this.$router.push({
              path:'/guide/step'
            })
    }
  }
}
</script>
<style lang="less" scoped>
    .guide-content {
        width:100%;
        height:100%;
       // background: url('../../../assets/chainManage_images/overview/guide_bg.png') no-repeat;
        background-size: cover;
        background-position-y:-56px ;
        overflow: hidden;
    }
    .guide-title {
        font-size:36px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #333333;
        line-height: 40px;
        text-align: center;
        margin:60px 0 59px;
    }
    .guide-list {
        padding:0 ;
    }
    .guide-list>div{
        width: 360px;
        height: 500px;
    }
    .guide-list>div:nth-child(1){
        background: url('../../../assets/chainManage_images/overview/guide_list_bg1.png') no-repeat;
        background-size: cover;
    }
    .guide-list>div:nth-child(2){
        background: url('../../../assets/chainManage_images/overview/guide_list_bg2.png') no-repeat;
        background-size: cover;
    }
    .guide-list>div:nth-child(3){
        background: url('../../../assets/chainManage_images/overview/guide_list_bg3.png') no-repeat;
        background-size: cover;
    }
    .guide-list>div:nth-child(4){
        width:330px;
        background: url('../../../assets/chainManage_images/overview/guide_list_bg4.png') no-repeat;
        background-size: cover;
    }
    .guide-list .guide-item img{
        width: 92px;
        height: 92px;
        display:block;
        margin:26px 0 0 115px;
    }
    .guide-list .guide-item .item_tit{
        // font-size: 24px;
        font-size:22px;
        font-weight: bold;
        color: #333333;
        margin: 63px 0 0 54px;
    }
    .guide-list .guide-item .item_text{
        // font-size: 18px;
        font-size:16px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #666666;
        padding: 0 91px 0 55px;
        margin:22px 0 33px;
    }
    .guide-list .guide-item .last_text {
        // font-size: 18px;
        font-size:16px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #666666;
        padding: 0 55px 0 55px;
        margin:22px 0 33px;
    }
    .guide-list .guide-item .item_list{
        margin-left: 54px;
    }
    .guide-list .guide-item .item_list div{
        // margin-bottom:15px;
        margin-bottom:8px;
        // font-size:16px;
        color:#666;
    }
    .creation-btn{
        width: 192px;
        height: 64px;
        background: #337DFF;
        border-image: linear-gradient(90deg, #1973CC, #55CEC7) 2 2;
        box-shadow: 0px 5px 10px 0px rgba(9, 50, 110, 0.4);
        border-radius: 4px;
        font-size: 22px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 60px;
        margin:59px auto 0;
        text-align: center;
        cursor: pointer;
        overflow: hidden;
    }
    .btn-wrap {
        text-align: center;
        margin:40px auto 0;
    }
</style>
