<template>
  <div class="contract_index">
    <keep-alive>
    <contractadminTable v-if="currentTab==='contractadmin_table'" />
  </keep-alive>
  <router-view v-if="currentTab!=='contractadmin_table'"/>
  </div>
</template>

<script>
import contractadminTable from './contractadmin_table.vue'
export default {
  name: 'contract_index',
  components: {
    contractadminTable
  },
  data () {
    return {
      // excludeArr: ['new_user', 'chain_details', 'result_page']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
