<template>
  <div class="multilink-details">
    <div style="width：auto;margin-left: 10px;">
      <Card style="width: auto">
        <div class="info-title">
          <div class="bs"></div>
          <span>基本信息</span>
        </div>
        <ul>
          <li>区块链名称：{{ arrDetails.chainName }}</li>
          <li>开发架构类型：{{ arrDetails.engineType }}</li>
          <li>审核列表：{{ arrDetails.auditList?arrDetails.auditList.map(val => val.auditValue).join(','):'' }}</li>
          <li>
            状态：<span>{{ arrDetails.status }}</span>
          </li>
          <li class="describeLi">
            详情：
            {{ arrDetails.chainBrief?arrDetails.chainBrief:'无' }}
          </li>
        </ul>
      </Card>
    </div>
    <Card style="margin: 20px 5px 10px 10px">
      <div class="node" style="width：auto;margin:10px 5px 0 10px;">
        <div class="info-title">
          <span class="bs"></span><span>节点</span>
          <Button ghost type="success" @click="add" icon="md-add" style="float: right; margin-top: -5px" :disabled="arrDetails.sfyc">新增节点</Button>
        </div>
        <!-- 节点 -->
        <edit-table-mul :columns="columns" v-model="tableData1" :key="transferKey" style="margin-top: 20px"></edit-table-mul>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[5, 10, 20, 40, 60, 100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align: right; margin: 10px 0" />
        <div class="network">
          <Poptip confirm title="确定启动吗？" @on-ok="startThe" @on-cancel="cancel">
            <Button :loading="loadingStatus" type="primary" :disabled="
            arrDetails.status === '启用' ? true : arrDetails.nodeNum < 4
          ">启动区块链网络</Button>
          </Poptip>
          <Button type="primary" :style="`display:${arrDetails.status!=='启用' ? 'none' : 'inline-block'};`" @click="ConfiGuration">Hyperion配置</Button>
        </div>
      </div>
    </Card>
    <div class="aaa">
      <Card style="margin:20px 5px 30px 10px;">
        <div class="account" style="margin:10px 5px 30px 10px;">
          <div class="info-title">
            <span class="bs"></span><span>链账户</span>
            <Button class="btn" type="success" ghost @click="addAccount" icon="md-add" style="float:right;margin-top:-5px;">新增管理链账户</Button>
          </div>
          <edit-table-mul :columns="accountColums" v-model="accountTableData" :key="transferKey" style="margin-top:20px;"></edit-table-mul>
        </div>
      </Card>
      <Card style="margin:20px 5px 30px 10px;">
        <Tenant :tenantVisibility="arrDetails.tenantVisibility" :chainId="arrDetails.chainId" :chainName="arrDetails.chainName"></Tenant>
      </Card>
    </div>

    <Modal :draggable="true" v-model="modal" width="550" :title="formItem.alertTitle" :z-index="1000" sticky :mask-closable="false">
      <Form ref="formItem" :rules="formItemRule" :model="formItem" :label-width="100">
        <FormItem label="节点名称" prop="nodeName">
          <Input placeholder="请输入节点名称" style="width: 400px; vertical-align: baseline" v-model="formItem.nodeName" :disabled='formItem.disa' />
        </FormItem>
        <FormItem label="硬盘" prop="nodeDisk">
          <Input :active-change="false" :max="1000" :min="500" placeholder="500 ~ 1000" style="width: 120px; vertical-align: baseline" v-model="formItem.nodeDisk">
          </Input>
          Gi
        </FormItem>
        <FormItem label="CPU" prop="nodeCpu">
          <Input :active-change="false" :max="500" :min="200" placeholder="200 ~ 500" style="width: 120px; vertical-align: baseline" v-model="formItem.nodeCpu"></Input>
          <!-- <Input
            placeholder="请输入CPU"
            style="width: 120px; vertical-align: baseline"
            v-model="formItem.nodeCpu"
          /> -->
          m
        </FormItem>
        <FormItem label="内存" prop="nodeMemory">
          <Input :active-change="false" :max="800" :min="500" placeholder="500 ~ 800" style="width: 120px; vertical-align: baseline" v-model="formItem.nodeMemory"></Input>
          <!-- <Input
            placeholder="请输入内存"
            style="width: 120px; vertical-align: baseline"
            v-model="formItem.nodeMemory"
          /> -->
          Mi
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="cancel('formItem')">取消</Button>
        <Button type="primary" @click="ok('formItem')">确定</Button>
      </div>
    </Modal>
    <Modal :draggable="true" v-model="accountModal" width="700" :title="accountItem.alertTitle" :z-index="1000" sticky :mask-closable="false">
      <Form ref="accountItem" :rules="accountItemRule" :model="accountItem" :label-width="150">
        <FormItem label="管理链账户名称：" prop="manageAccountName">
          <Tooltip max-width="200" content="5-12位,仅包含{a-z,1-5,.},且.不能在最前和最后">
            <Icon type="md-help-circle" style="font-size: 16px; margin-left: -18px" />
          </Tooltip>
          <Input placeholder="管理链账户名称" style="width: 250px; vertical-align: baseline" v-model="accountItem.manageAccountName" :disabled="disabledInput" />
        </FormItem>
        <FormItem v-if="ownerKeyShow" label="owner权限私钥：" prop="ownerPrivateKey">
          <Input type="password" placeholder="owner权限私钥" style="width: 500px; vertical-align: baseline" v-model="accountItem.ownerPrivateKey">
          <i class="ri-eye-close-line" slot="suffix" @click="handleOwnerKey"></i>
          </Input>
        </FormItem>
        <FormItem v-else label="owner权限私钥：" prop="ownerPrivateKey">
          <Input type="text" placeholder="owner权限私钥" style="width: 500px; vertical-align: baseline" v-model="accountItem.ownerPrivateKey">
          <i class="ri-eye-line" slot="suffix" @click="handleOwnerKey"></i>
          </Input>
        </FormItem>
        <FormItem v-if="activeKeyShow" label="active权限私钥：" prop="activePrivateKey">
          <Input type="password" placeholder="active权限私钥" style="width: 500px; vertical-align: baseline" v-model="accountItem.activePrivateKey">
          <i class="ri-eye-close-line" slot="suffix" @click="handleActiveKey"></i>
          </Input>
        </FormItem>
        <FormItem v-else label="active权限私钥：" prop="activePrivateKey">
          <Input type="text" placeholder="active权限私钥" style="width: 500px; vertical-align: baseline" v-model="accountItem.activePrivateKey">
          <i class="ri-eye-line" slot="suffix" @click="handleActiveKey"></i>
          </Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="cancelAccount">取消</Button>
        <Button type="primary" @click="okAccount">确定</Button>
      </div>
    </Modal>
    <!-- 赠送 -->
    <Modal :draggable="true" v-model="giving" width="800" title="赠送" sticky :scrollable="true" :mask-closable="false">
      <div style="padding: 10px 0 20px 0">
        <Input placeholder="请输入用户名" class="input-search" style="width: 250px; margin-left: 515px; vertical-align: middle" v-model="givingName" @keyup.enter.native="searchNoTenantName">
        <Icon type="ios-search" slot="suffix" @click="searchNoTenantName" />
        </Input>
      </div>
      <div style="width: 100%">
        <edit-table-mul :columns="historyColumns" v-model="historyData" :key="transferKey"></edit-table-mul>
      </div>
      <Page :total="noSelectPageParam.pagetotal" :current.sync="noSelectPageParam.pageIndex" :page-size="noSelectPageParam.pageSize" @on-change="noSelectPageChange" :page-size-opts="[5, 10, 20, 40, 60, 100]" show-total show-elevator show-sizer @on-page-size-change="noSelectPageSizeChange" style="text-align: right; padding-top: 15px" />
      <div slot="footer">
        <Button type="primary" @click="cancelGivingUser">取消</Button>
        <Button type="primary" @click="givingUser">确定</Button>
      </div>
    </Modal>
    <!-- 赠送节点 -->
    <Modal v-model="givModal" title="修改节点角色" @on-ok="role" @on-cancel="cancel">
      <RadioGroup v-model="animal">
        <Radio label="业务节点"></Radio>
        <Radio label="共识节点"></Radio>
      </RadioGroup>
    </Modal>
    <!-- Hyperion配置 -->
    <Modal v-model="hyperionModal" title="Hyperion配置" footer-hide @on-cancel="hyperionCancel('formInline')">
      <Form ref="formInline" :model="formInline" :rules="ruleInline" inline>
        <FormItem prop="hyperionValue">
          <Input v-model="formInline.hyperionValue" placeholder="请输入url" style="width: 490px" />
        </FormItem>
        <div class="newFromSubmit">
          <Button @click="hyperionCancel('formInline')" style="margin-right: 10px">取消</Button>
          <Button type="primary" @click="hyperionOk('formInline')">确定</Button>
        </div>
      </Form>

    </Modal>
  </div>
</template>

<script>
import {
  getMultiLinkDetailsNet,
  reviseMultiChainNodeNet,
  addMultiChainNodeNet,
  addManageAccountNet,
  deleteManageAccountNet,
  getNodeListNet,
  jion,
  GiviUer,
  GiviUersNode,
  getRole,
  qiStart, getProgress,
  getHyperionSerch,
  getHyperionAdd
} from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import Tenant from './tenant'
import { getKey, changeKey } from './tool'
import { statusList, nodeTypeList } from './typeList'
import { isAccount, isNumber } from '../../../lib/check'
import Vue from 'vue'
export default {
  name: 'network-details',
  components: {
    EditTableMul,
    Tenant
  },
  data () {
    const validateAccount = (rule, value, callback) => {
      if (!isAccount(value)) {
        callback(
          new Error('仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字')
        )
      } else {
        callback()
      }
    }
    const validateAccountCpu = (rule, value, callback) => {
      let numValue = Number(value)
      if (!isNumber(value)) {
        callback(new Error('请在区间内输入正整数'))
      } else if (numValue >= 200 && numValue <= 500) {
        callback()
      } else {
        callback(new Error('最小200,最大500'))
      }
    }
    const validateAccountNodeMemory = (rule, value, callback) => {
      let numValue = Number(value)
      if (!isNumber(value)) {
        callback(new Error('请在区间内输入正整数'))
      } else if (numValue >= 500 && numValue <= 800) {
        callback()
      } else {
        callback(new Error('最小500,最大800'))
      }
    }
    const validateAccountNodeDisk = (rule, value, callback) => {
      let numValue = Number(value)
      if (!isNumber(value)) {
        callback(new Error('请在区间内输入正整数'))
      } else if (numValue >= 500 && numValue <= 1000) {
        callback()
      } else {
        callback(new Error('最小500,最大1000'))
      }
    }
    return {
      formInline: {
        hyperionValue: '' // Hyperion输入框
      },
      ruleInline: {
        hyperionValue: [
          { required: true, message: '请输入url', trigger: 'blur' }
        ]
      },
      hyperionModal: false, // Hyperion配置
      tablePageParam: {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      },
      loadingStatus: false,
      disabledInput: false,
      modal: false,
      giving: false, // 赠送
      accountModal: false,
      noTenantName: '', // 赠送搜索
      givingName: '', // 赠送用户
      userId: '',
      detaInfo: '', // 详情描述
      animal: '',
      editer: [],
      noSelectPageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      }, // 赠送分页
      ownerKeyShow: true,
      activeKeyShow: true,
      transferKey: 0,
      //   table 表头
      historyColumns: [
        {
          title: '选中',
          key: 'checkBox',
          width: 200,
          render: (h, params) => {
            return h('div', [
              h('Checkbox', {
                props: {
                  value: params.row.checkBox
                },
                on: {
                  'on-change': (e) => {
                    this.historyData.forEach((items) => {
                      // 先取消所有对象的勾选，checkBox设置为false
                      this.$set(items, 'checkBox', false)
                    })
                    this.historyData[params.index].checkBox = e // 再将勾选的对象的checkBox设置为true
                    e ? this.givingUserinfo = this.historyData[params.index].userId : this.givingUserinfo = ''
                  }
                }
              })
            ])
          }
        },
        {
          title: '用户名',
          key: 'userLoginId'
        }
      ],
      selectColums: [
        {
          type: 'selection',
          width: 35,
          align: 'center'
        },
        { key: 'tenantName', title: '租户名称' },
        { key: 'tenantBrief', title: '租户描述', minWidth: 300, tooltip: true }
      ],
      notSelectTableData: [],
      chainId: this.$route.params.chainId ? this.$route.params.chainId : '',
      arrDetails: {},
      jionIndex: '',
      nodeIndex: '',
      givingUserinfo: '',
      givModal: false,
      tableData1: [], // 节点
      roleId: '', //
      accountTableData: [], // 链账户
      reviseTableData: [],
      formItem: {
        alertTitle: '节点信息',
        nodeName: '',
        nodeCpu: 0,
        nodeMemory: 0,
        nodeDisk: 0,
        nodeId: '',
        createTime: '',
        chainId: '',
        online: '',
        disa: ''// 禁用
      },
      // 新增管理链账户
      accountItem: {
        alertTitle: '新增管理链账户',
        chainId: '',
        manageAccountName: '',
        ownerPrivateKey: '',
        activePrivateKey: ''
      },
      historyData: [],
      tableColumn: [
        {
          title: '',
          width: 60,
          align: 'center',
          type: 'selection'
        },
        {
          title: '规则实例名称',
          key: 'name'
        },
        {
          title: '规则模板类别',
          key: 'ruleType',
          render: (h, params) => {
            return h('div', [
              h(
                'div',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  }
                },
                Filter.ruleTypeFilter(params.row.ruleTemplate.ruleType)
              )
            ])
          }
        },
        {
          title: '规则模板',
          key: 'templateName',
          render: (h, params) => {
            return h('div', [
              h(
                'div',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  }
                },
                params.row.ruleTemplate.name
              )
            ])
          }
        },
        {
          title: '创建时间',
          key: 'createTime'
        },
        {
          title: '描述',
          key: 'description'
        },
        {
          title: '操作',
          key: 'action',
          minWidth: 250,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'a',
                {
                  style: {
                    display: 'inline-block',
                    height: '32px',
                    padding: '12px 10px'
                  },
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      this.updateModel = true
                    }
                  }
                },
                '修改'
              ),
              h(
                'a',
                {
                  style: {
                    display: 'inline-block',
                    height: '32px',
                    padding: '12px 10px'
                  },
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      this.viewInstance = true
                    }
                  }
                },
                '查看'
              ),
              h(
                'a',
                {
                  style: {
                    display: 'inline-block',
                    height: '32px',
                    padding: '12px 10px'
                  },
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      this.deleteInstance()
                    }
                  }
                },
                '删除'
              ),
              h(
                'a',
                {
                  style: {
                    display: 'inline-block',
                    height: '32px',
                    padding: '12px 10px'
                  },
                  props: {
                    type: 'text',
                    size: 'small'
                  }
                },
                '绑定指标'
              )
            ])
          }
        }
      ],

      // 新增节点规则
      formItemRule: {
        nodeName: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, max: 13, message: '长度不能超过13', trigger: 'blur' },
          {
            type: 'string',
            pattern: /^[0-9a-zA-Z]*$/g, /// ^\w+$/
            message: '格式应为0-9a-zA-Z',
            trigger: 'blur'
          }
        ],
        nodeCpu: [
          {
            required: true,
            validator: validateAccountCpu,
            trigger: 'blur'
          }
        ],

        nodeDisk: [
          {
            required: true,
            validator: validateAccountNodeDisk,
            trigger: 'blur'
          }
        ],
        nodeMemory: [
          {
            required: true,
            validator: validateAccountNodeMemory,
            trigger: 'blur'
          }
        ]

      },
      // 链管理规则
      accountItemRule: {
        manageAccountName: [
          { required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
          { max: 12, message: '不能多于12位', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateAccount }
        ],
        ownerPrivateKey: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            type: 'string',
            pattern: /^[a-zA-Z0-9]{51}$/,
            message: '格式有误,长度必须为51位,a-zA-Z0-9',
            trigger: 'blur'
          }
        ],
        activePrivateKey: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ]
      },
      statusList: [],
      nodeTypeList: [],
      // 节点
      columns: [
        {
          title: '序号',
          type: 'index'
        },
        { key: 'nodeName', title: '节点名称', tooltip: true },
        { key: 'nodeDisk', title: '硬盘' },
        { key: 'nodeCpu', title: 'CPU' },
        { key: 'nodeMemory', title: '内存' },
        { key: 'roleStatus', title: '角色' },
        { key: 'createTime', title: '创建时间', minWidth: 80 },
        {
          key: 'action',
          title: '操作',
          minWidth: 150,
          render: (h, params) => {
            return h('div', [
              // eslint-disable-next-line eqeqeq
              this.tableData1[params.index].joinOutStatus == '0'
                ? h(
                  'Poptip',
                  {
                    props: {
                      confirm: true,
                      transfer: true,
                      type: 'text',
                      size: 'small',
                      title: '确定退出吗?'
                    },
                    style: {
                      marginRight: '8px',
                      color: '#3D73EF',
                      border: this.tableData1[params.index].jdczsfyc
                        ? '1px solid #cccccc'
                        : '1px solid #3D73EF'
                    },
                    on: {
                      'on-ok': () => {
                        if (
                          this.arrDetails.status === '启用' &&
                          this.arrDetails.sfczzt <= 4
                        ) {
                          this.$Message.error(
                            '已启用的区块链网络至少包含4个节点'
                          )
                        } else {
                          // this.jionIndex = params.index
                          this.getjionModal('1', params.index)
                        }
                      },
                      'on-cancel': () => { }
                    }
                  },
                  [
                    h(
                      'Button',
                      {
                        props: {
                          size: 'small',
                          type: 'text',
                          // loading: this.loadingStatus,
                          // loading: true,
                          loading: this.tableData1[params.index].loading,
                          disabled: this.tableData1[params.index].jdczsfyc
                        },
                        style: {
                          color: this.tableData1[params.index].jdczsfyc
                            ? ''
                            : '#3D73EF'
                        },
                        on: {
                          click: () => { }
                        }
                      },
                      '退出'
                    )
                  ]
                )
                : h(
                  'Poptip',
                  {
                    props: {
                      confirm: true,
                      transfer: true,
                      type: 'text',
                      size: 'small',
                      title: '确定加入吗?'
                    },
                    on: {
                      'on-ok': () => {
                        // this.progressBar(params)
                        // this.jionIndex = params.index
                        // console.log(this.jionIndex)
                        this.getjionModal('0', params.index)
                      },
                      'on-cancel': () => { }
                    }
                  },
                  [
                    h(
                      'Button',
                      {
                        props: {
                          size: 'small',
                          type: 'text',
                          loading: this.tableData1[params.index].loading,
                          disabled:
                            this.arrDetails.status === '关闭'
                              ? true
                              : this.tableData1[params.index].jdczsfyc
                        },
                        style: {
                          marginRight: '8px',
                          color:
                            this.arrDetails.status === '关闭'
                              ? true
                              : this.tableData1[params.index].jdczsfyc
                                ? ''
                                : '#3D73EF',
                          border:
                            this.arrDetails.status === '关闭'
                              ? '1px solid #cccccc'
                              : this.tableData1[params.index].jdczsfyc
                                ? '1px solid #cccccc'
                                : this.arrDetails.sfyc
                                  ? '1px solid #cccccc'
                                  : this.tableData1[params.index].joinOutStatus ==
                                    '0'
                                    ? '1px solid #cccccc'
                                    : '1px solid #3D73EF'
                        },
                        on: {
                          click: () => { }
                        }
                      },
                      '加入'
                    )
                  ]
                ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small',
                    disabled: this.tableData1[params.index].jdczsfyc
                  },
                  style: {
                    marginRight: '8px',
                    color: this.tableData1[params.index].jdczsfyc
                      ? ''
                      : '#3D73EF',
                    border: this.tableData1[params.index].jdczsfyc
                      ? '1px solid #cccccc'
                      : '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.showDetails(params.index)
                    }
                  }
                },
                '编辑'
              ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small',
                    disabled: this.tableData1[params.index].jdczsfyc
                      ? true
                      : this.arrDetails.sfyc
                        ? true
                        : this.tableData1[params.index].joinOutStatus == '0'
                  },
                  style: {
                    marginRight: '8px',
                    color: this.tableData1[params.index].jdczsfyc
                      ? ''
                      : this.arrDetails.sfyc
                        ? ''
                        : this.tableData1[params.index].joinOutStatus == '0'
                          ? ''
                          : '#3D73EF',

                    border: this.tableData1[params.index].jdczsfyc
                      ? '1px solid #cccccc'
                      : this.arrDetails.sfyc
                        ? '1px solid #cccccc'
                        : this.tableData1[params.index].joinOutStatus == '0'
                          ? '1px solid #cccccc'
                          : '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.givingInt(params.index)
                    }
                  }
                },
                '赠送'
              ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small',
                    disabled: this.tableData1[params.index].jdczsfyc
                  },
                  style: {
                    marginRight: '8px',
                    color: this.tableData1[params.index].jdczsfyc
                      ? ''
                      : '#3D73EF',
                    border: this.tableData1[params.index].jdczsfyc
                      ? '1px solid #cccccc'
                      : '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.givingNode(params.index)
                      this.roleId = params.index
                    }
                  }
                },
                '角色'
              )
            ])
          }
        }
      ],
      accountColums: [
        { key: 'accountName', title: '管理链账户名称' },
        { key: 'ownerPrivateKey', title: 'owner权限私钥' },
        { key: 'activePrivateKey', title: 'active权限私钥' },
        {
          key: 'action',
          title: '操作',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: { type: 'text', size: 'small' },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.editAccount(params.index)
                    }
                  }
                },
                '编辑'
              ),
              h(
                'Poptip',
                {
                  props: {
                    confirm: true,
                    transfer: true,
                    title:
                      '确定删除管理链账户[' +
                      this.accountTableData[params.index].accountName +
                      ']吗?'
                  },
                  on: {
                    'on-ok': () => {
                      this.editTableIndex = -1
                      // 调用删除方法
                      this.deleteAccount(params.index)
                    },
                    'on-cancel': () => { }
                  }
                },
                [
                  h(
                    'Button',
                    {
                      class: { btnFA5151: true },
                      props: { size: 'small', type: 'text' },
                      style: {
                        marginRight: '8px',
                        color: '#FA5151',
                        border: '1px solid #FA5151'
                      },
                      on: {
                        click: () => { }
                      }
                    },
                    '删除'
                  )
                ]
              )
            ])
          }
        }
      ]
    }
  },
  methods: {
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    // Hyperion配置弹框
    ConfiGuration () {
      getHyperionSerch(this.chainId).then(res => {
        this.formInline.hyperionValue = res.data
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
      this.hyperionModal = true
    },
    hyperionOk (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          // this.hyperionModal = false
          getHyperionAdd(this.chainId, this.formInline.hyperionValue).then(res => {
            if (res.code === '00000') {
              this.msgInfo('success', res.message, true)
              this.hyperionModal = false
              // this.$refs[name].resetFields()
            }
          }).catch((error) => {
            this.msgInfo('error', error.message, true)
          })
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },
    hyperionCancel (name) {
      this.$refs[name].resetFields()
      this.hyperionModal = false
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData(this.arrDetails.chainId)
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData(this.arrDetails.chainId)
    },
    // 赠送分页
    noSelectPageChange (index) {
      this.noSelectPageParam.pageIndex = index
      this.getChainGiving()
      this.givingUserinfo = ''
    },
    noSelectPageSizeChange (index) {
      this.noSelectPageParam.pageSize = index
      this.getChainGiving()
      this.givingUserinfo = ''
    },
    // 加入进度条
    // progressBar (params) {
    //   console.log(params)
    //   getProgress(params.row.nodeId).then(res => {
    //     console.log(res)
    //   })
    // },
    init () {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.formItem = {
        alertTitle: '新增节点',
        chainId: '',
        nodeId: '',
        nodeName: '', // 节点名称
        nodeCpu: '', // cpu
        nodeMemory: '', // 内存
        nodeDisk: '', // 硬盘
        createTime: '', // 创建时间
        online: ''// 是否在线
      }
    },
    // 编辑
    showDetails (index) {
      this.init()
      this.modal = true
      if (`${this.tableData1[index].nodeName}`) {
        this.formItem = {
          alertTitle: '节点修改',
          chainId: `${this.editer[index].chainId}`,
          nodeId: `${this.editer[index].nodeId}`,
          nodeName: `${this.editer[index].nodeName}`, // 节点名称
          nodeMemory: Number(`${this.editer[index].nodeMemory}`), // 内存
          nodeDisk: Number(`${this.editer[index].nodeDisk}`), // 硬盘
          nodeCpu: Number(`${this.editer[index].nodeCpu}`), // cpu
          online: this.editer[index].joinOutStatus == '0' ? '0' : '1', // 是否在线
          disa: this.editer[index].joinOutStatus == '0'
        }
      }
    },
    // 赠送
    givingInt (index) {
      this.init()
      this.givingName = ''
      this.giving = true
      this.getChainGiving()
      this.nodeId = index
      this.givingUserinfo = ''
    },
    // 角色
    givingNode (index) {
      this.givModal = true
      this.animal =
        this.tableData1[index].roleStatus === '共识节点'
          ? '共识节点'
          : '业务节点'
    },
    // 搜索赠送用户
    searchNoTenantName () {
      this.getChainGiving()
    },
    // 取消
    cancelGivingUser () {
      this.giving = false
    },
    // 选中赠送用户
    givingUser () {
      let userNode = {
        userId: this.givingUserinfo,
        chainId: `${this.tableData1[this.nodeId].chainId}`,
        nodeId: `${this.tableData1[this.nodeId].nodeId}`
      }
      if (this.givingUserinfo === '') {
        this.giving = true
        this.$Message.warning('未选中赠送用户')
      } else if (this.arrDetails.jdsfkzs ||
        (this.givingUserinfo == this.arrDetails.belongUserId &&
          this.arrDetails.jdsfkzs == false)) {
        GiviUersNode(userNode).then((res) => {
          if (res.code === '00000') {
            this.msgInfo('success', res.message, true)
            this.giving = false
            this.getInfo(this.chainId)
          } else {
            this.msgInfo('error', res.message, true)
            this.reback()
          }
        }).catch((error) => {
          this.msgInfo('error', error.message, true)
        })
      } else {
        this.giving = true
        this.$Message.error('该链只能赠送同一用户')
      }
    },

    // 赠送用户方法
    getChainGiving () {
      let userName = {
        userLoginId: this.givingName,
        pageParam: this.noSelectPageParam
      }
      GiviUer(userName).then((res) => {
        if (res.code === '00000') {
          this.historyData = res.data.records
          this.noSelectPageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          ++this.transferKey
        } else {
          this.msgInfo('error', res.message, true)
          this.reback()
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getjionModal (num, index) {
      this.online = num
      let jionN = {
        status: num,
        online: num,
        chainId: `${this.tableData1[index].chainId}`,
        nodeId: `${this.tableData1[index].nodeId}`
      }
      // this.loadingStatus = true
      // debugger
      this.tableData1[index].loading = true
      // console.log(this.tableData1)
      // console.log(index)
      // console.log(this.tableData1[this.jionIndex].loading)

      // this.$Spin.show({
      //   render: (h) => {
      //     return h('div', [
      //       h('Icon', {
      //         class: 'demo-load',
      //         style: 'animation: ani-demo-spin 1s linear infinite',
      //         props: {
      //           type: 'ios-loading',
      //           size: 18
      //         }
      //       }),
      //       h('div', 'Loading')
      //     ])
      //   }
      // })
      this.$Loading.start()
      jion(jionN)
        .then((res) => {
          if (res.code === '00000') {
            this.tableData1[index].loading = false // 请求成功关闭动画
            // this.loadingStatus = false
            // this.$Spin.hide()
            this.$Loading.finish()
            this.getTableData(jionN.chainId)
            this.getInfo(this.chainId)
          } else {
            this.tableData1[index].loading = false
            // this.loadingStatus = false
            // this.$Spin.hide()
            this.$Loading.error()
            this.$Message.warning(res.message)
          }
        })
        .catch((error) => {
          this.tableData1[index].loading = false
          // this.loadingStatus = false
          this.$Loading.error()
          this.msgInfo('error', error.message, true)
          // this.$Spin.hide()
        })
    },

    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    tipInfo (res) {
      if (res.code === '00000') {
        this.msgInfo('success', res.message, true)
        this.getInfo(this.chainId)
        this.modal = false
        this.accountModal = false
        this.ownerKeyShow = true
        this.activeKeyShow = true
      } else {
        this.msgInfo('error', res.message, true)
      }
    },
    getTableData (chainId) {
      getNodeListNet(this.tablePageParam, chainId)
        .then((res) => {
          if (res.code === '00000') {
            let roles = {
              0: '共识节点',
              1: '业务节点'
            }
            let tableData1List = res.data.records.map((item, index) => {
              return {
                ...item,
                nodeCpu: item.nodeCpu + 'm',
                nodeDisk: item.nodeDisk + 'Gi',
                nodeMemory: item.nodeMemory + 'Mi',
                roleStatus: roles[item.roleStatus],
                loading: false
              }
            })
            this.editer = res.data.records
            this.tableData1 = tableData1List
            // this.tableData1 = res.data.records.map(item => {
            //   item.loading = false
            //   return item
            // })
            // console.log(this.tableData1)
            this.tablePageParam = {
              pagetotal: res.data.total,
              pageSize: res.data.size,
              pageIndex: res.data.current
            }

            ++this.transferKey
          } else {
            this.msgInfo('error', res.message, true)
            this.reback()
          }
        })
        .catch((error) => {
          this.msgInfo('error', error.message, true)
          this.reback()
        })
    },
    //
    getInfo (chainId) {
      getMultiLinkDetailsNet(chainId)
        .then((res) => {
          if (res.code === '00000') {
            this.arrDetails = res.data
            this.getTableData(this.arrDetails.chainId)
            this.accountTableData = res.data.manageAccountList
            // 深拷贝数组
            this.reviseTableData = JSON.parse(
              JSON.stringify(res.data.manageAccountList)
            )
            this.accountTableData = changeKey(res.data.manageAccountList)
            ++this.transferKey
          } else {
            this.msgInfo('error', res.message, true)
            this.reback()
          }
        })
        .catch((error) => {
          this.msgInfo('error', error.message, true)
          this.reback()
        })
    },
    // editDetails (index) {
    //   this.$nextTick(() => {
    //     this.$refs['formItem'].resetFields()
    //   })
    //   this.modal = true
    //   this.formItem = {
    //     alertTitle: '修改链节点信息',
    //     chainId: this.arrDetails.chainId,
    //     nodeId: `${this.tableData1[index].nodeId}`,
    //     nodeName: `${this.tableData1[index].nodeName}`,
    //     nodeCpu: `${this.tableData1[index].nodeCpu}`,
    //     nodeMemory: `${this.tableData1[index].nodeMemory}`,
    //     nodeDisk: `${this.tableData1[index].nodeDisk}`,
    //    online:this.tableData1[index].joinOutStatus=='0'?'0':'1'

    //   }
    // },
    ok (name) {
      this.$Loading.start()
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.formItem.nodeId) {
            reviseMultiChainNodeNet(this.formItem)
              .then((res) => {
                this.$Loading.finish()
                this.tipInfo(res)
              })
              .catch((error) => {
                this.$Loading.error()
                this.msgInfo('error', error.message, true)
              })
          } else {
            this.formItem.chainId = this.arrDetails.chainId
            addMultiChainNodeNet(this.formItem)
              .then((res) => {
                this.$Loading.finish()
                this.tipInfo(res)
              })
              .catch((error) => {
                this.$Loading.error()
                this.msgInfo('error', error.message, true)
              })
          }
        } else {
          this.$Loading.error()
          this.msgInfo('error', '存在字段校验不符合规范，请检查！', true)
        }
      })
      // this.init()
      // this.$refs.formItem.resetFields()
    },
    // 角色
    role () {
      let roleData = {
        chainId: this.chainId,
        nodeId: this.tableData1[this.roleId].nodeId,
        roleStatus: this.animal == '共识节点' ? '0' : '1'
      }
      this.$Loading.start()
      getRole(roleData).then((res) => {
        if (res.code === '00000') {
          this.givModal = false
          this.$Loading.finish()
          this.msgInfo('success', res.message, true)
          this.getInfo(this.chainId)
        } else {
          this.$Loading.error()
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },

    cancel () {
      this.init()
      this.$Loading.finish()
      this.modal = false
    },
    // 启动按钮状态
    startThe () {
      let startData = {
        chainId: this.chainId,
        status: 'ENABLE'
      }
      this.loadingStatus = true
      this.$Loading.start()
      // this.$Spin.show({
      //   render: (h) => {
      //     return h('div', [
      //       h('Icon', {
      //         class: 'demo-load',
      //         style: 'animation: ani-demo-spin 1s linear infinite',
      //         props: {
      //           type: 'ios-loading',
      //           size: 18
      //         }
      //       }),
      //       h('div', 'Loading')
      //     ])
      //   }
      // })

      qiStart(startData)
        .then((res) => {
          if (res.code === '00000') {
            this.getInfo(this.chainId)
            this.$Loading.finish()
            this.loadingStatus = false
            this.$Message.success(res.message)
            // this.$Spin.hide()
          } else {
            // this.$Spin.hide()
            this.$Loading.error()
            this.loadingStatus = false
            this.$Message.error(res.message)
          }
        })
        .catch((error) => {
          this.$Loading.error()
          this.loadingStatus = false
          this.$Message.error(error.message)
        })
      // // 加载进度条
      // let pro = {
      //   chainId: this.chainId
      // }
      // getProgress(pro).then(res => {
      //   if (res.code === '00000') {
      //     this.$Loading.finish()
      //     this.loadingStatus = false
      //   } else {
      //     this.$Loading.error()
      //     this.loadingStatus = false
      //     this.$Message.error(res.message)
      //   }
      // }).catch(error => {
      //   this.loadingStatus = false
      //   this.$Loading.error()
      //   this.$Message.error(error.message)
      // })
    },
    add () {
      this.init()
      this.modal = true
      // this.getInfo(this.chainId);
    },
    reback () {
      this.$router.push({
        name: 'blockchain_network'
      })
      // this.$emit('handleTabRemove', this.$route.name, event)
    },
    // 新增管理链账户
    addAccount () {
      this.$nextTick(() => {
        this.$refs['accountItem'].resetFields()
      })
      this.accountItem.alertTitle = '新增管理链账户'
      this.disabledInput = false
      this.accountModal = true
    },
    okAccount () {
      this.$refs['accountItem'].validate((valid) => {
        if (valid) {
          this.accountItem.chainId = this.arrDetails.chainId
          addManageAccountNet(this.accountItem)
            .then((res) => {
              this.tipInfo(res)
            })
            .catch((error) => {
              this.msgInfo('error', error.message, true)
            })
        } else {
          this.msgInfo('error', '存在字段校验不符合规范，请检查！', true)
        }
      })
    },
    cancelAccount () {
      this.accountModal = false
      this.ownerKeyShow = true
      this.activeKeyShow = true
    },
    editAccount (index) {
      this.$nextTick(() => {
        this.$refs['accountItem'].resetFields()
      })
      this.accountModal = true
      var list = getKey(
        this.accountTableData[index].accountName,
        this.reviseTableData
      )
      this.accountItem = {
        alertTitle: '修改链账户信息',
        chainId: this.arrDetails.chainId,
        manageAccountName: `${this.accountTableData[index].accountName}`,
        ownerPrivateKey: list.ownerPrivateKey,
        activePrivateKey: list.activePrivateKey
      }
      this.disabledInput = true
    },
    handleOwnerKey () {
      this.ownerKeyShow = !this.ownerKeyShow
    },
    handleActiveKey () {
      this.activeKeyShow = !this.activeKeyShow
    },
    addClass (value) {
      return value === '启用' ? 'status_style_blue' : 'status_style'
    },

    deleteAccount (index) {
      deleteManageAccountNet(
        this.arrDetails.chainId,
        this.accountTableData[index].accountName
      )
        .then((res) => {
          this.tipInfo(res)
        })
        .catch((error) => {
          this.msgInfo('error', error.message, true)
        })
    }
  },
  created () { },
  mounted () {
    this.statusList = statusList
    this.nodeTypeList = nodeTypeList
    if (this.chainId) {
      this.getInfo(this.chainId)
    } else {
      this.reback()
    }
    // this.$Message.config({
    //   top: 250,
    //   duration: 2
    // })
  },
  beforeRouteEnter (to, from, next) {
    // 在渲染该组件的对应路由被 confirm 前调用
    // 不！能！获取组件实例 `this`
    // 因为当钩子执行前，组件实例还没被创建
    if (from.name) {
      next()
    } else {
      next('/blockchain_network')
    }
  }
  // beforeRouteLeave (to, from, next) {
  //   // 导航离开该组件的对应路由时调用
  //   // 可以访问组件实例 `this`
  //   // this.reback()
  //   // this.$emit('handleTabRemove', this.$route.name, event)
  //   // this.tabRemove(this.$route.name, event)
  //   // const state = {
  //   //   tabList: JSON.parse(localRead('tabList') || '[]')
  //   // }

  //   // const getTabListToLocal = state.tabList.filter(item => {
  //   //   return item.name !== 'multilink_details'
  //   // }).map(item => {
  //   //   return {
  //   //     name: item.name,
  //   //     path: item.path,
  //   //     meta: item.meta,
  //   //     params: item.params,
  //   //     query: item.query
  //   //   }
  //   // })

  //   next()
  // }
}
</script>

<style lang="less" scoped>
.newFromSubmit {
  margin-left: 35%;
}
ul,
li {
  padding-left: 7px;
  margin: 0;
  list-style: none;
}
li {
  font-size: 14px;
  margin-bottom: 5px;
  .status_style_blue {
    width: 100px;
    height: 30px;
    padding: 3px 15px;
    color: #ffffff;
    background-color: #2d8cf0;
    box-shadow: #c2bdbd 0 0 5px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
  }
  .status_style {
    width: 100px;
    height: 30px;
    padding: 3px 15px;
    color: #ffffff;
    background-color: #c2bdbd;
    box-shadow: #c2bdbd 0 0 5px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
  }
}
span {
  padding-left: 6px;
}
.info-title {
  font-size: 16px;
  font-weight: bold;
  vertical-align: middle;
  height: 18px;
  font-family: "Microsoft YaHei";
  line-height: 18px;
  color: #333333;
  margin: 10px 0 25px 0px;
}

.bs {
  float: left;
  width: 6px;
  height: 18px;
  background: #19c3a0;
  opacity: 1;
  border-radius: 3px;
}
.network {
  display: flex;
  justify-content: flex-end;
}

// /deep/.ivu-btn-text:hover {
//   background-color: rgba(61, 115, 239, 0.8);
//   color: #fff !important;
// }
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.btnFA5151:hover {
  background-color: #fa5151 !important;
}
/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
.aaa {
  position: relative;
  z-index: -1;
}
</style>
