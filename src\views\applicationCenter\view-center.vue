<template>
  <div class="comp-wrap">
    <div class="title"><span class="bs"></span>视图中心</div>
    <div class="empty" v-if="list.length === 0">
      <img class="imgs" :src="imagesurl" />
      <p class="empty-none">暂无数据</p>
    </div>
    <Row class="card-list" v-else>
      <Col v-for="(item, index) in list" :span="11" :offset="index % 2 ? 2 : 0" :key="item.id" class="card-wrap">
      <a :href="item.routeUrl" target="_blank" style="display: block" @click="handleGo(item.title)">
        <Card>
          <div class="card">
            <div class="card-title">
              <span class="comp-tit">
                <Tooltip :content="item.title" max-width="550" placement="top-start">
                  <!-- {{ item.brief }} -->
                  <div class="textversionTitle">
                    <span>{{ item.title }}</span>
                  </div>

                </Tooltip>
              </span>
              <span class="comp-version">
                <Tooltip :content="item.version" max-width="650" placement="top-start">
                  <!-- {{ item.brief }} -->
                  <span class="textversion">当前版本：{{ item.version }}</span>
                </Tooltip>
              </span>
            </div>
            <div class="paragraph">
              <Tooltip :content="item.brief" max-width="650">
                <!-- {{ item.brief }} -->
                <div class="text">
                  <span>{{ item.brief }}</span>
                </div>
              </Tooltip>
            </div>
            <!-- <div class="tips">
                      <span class="tips-tit">支持框架：</span>
                      <div class="tips-item" v-for="(tips,index) in item.tipsList" :key="index">{{tips}}</div>
                  </div> -->
          </div>
        </Card>
      </a>
      </Col>
    </Row>
    <div class="page-wrap" v-if="list.length > 0">
      <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[6, 8, 10]" show-total show-elevator show-sizer @on-page-size-change="sizeChange" style="text-align: right" />
    </div>
  </div>
</template>

<script>
import { getWebCenter } from '@/api/data.js'
import { getLogsave } from '@/api/contract.js'
export default {
  data () {
    return {
      tablePageParam: {
        pagetotal: 0,
        pageIndex: 1,
        pageSize: 6
      },
      list: [],
      imagesurl: require('@/assets/img/null.png'),

    }
  },
  methods: {
    handleGo (title) {
      sessionStorage.setItem('toName', title);;
      let lastPages=sessionStorage.getItem('fromName')
      let operation = {
        "lastPage":lastPages,
        "currentPage": title,
        "operateType": 'NORMAL_VIEW'
      }
      console.log(operation);
      getLogsave(operation).then((res) => {
        console.log(res);
      })
    },
    /**
     * 每页条数改变
     */
    sizeChange (size) {
      this.tablePageParam.pageSize = size
      this.getList()
    },
    /**
     * 页码改变
     */
    pageChange (pageIndex) {
      this.tablePageParam.pageIndex = pageIndex
      this.getList()
    },
    getList () {
      let pageParam = {
        pageSize: this.tablePageParam.pageSize,
        pageIndex: this.tablePageParam.pageIndex
      }
      getWebCenter({ pageParam })
        .then((res) => {
          console.log(res)
          if (res.code === '00000') {
            this.list = res.data.records
            this.tablePageParam = {
              pagetotal: res.data.total,
              pageIndex: res.data.current,
              pageSize: res.data.size
            }
          } else {
            this.msgInfo('error', res.message)
          }
        })
        .catch((error) => {
          this.msgInfo('error', error.message)
        })
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    }
  },
  created () {
    this.getList()
  }
}
</script>

<style lang="less" scoped>
.comp-wrap {
  padding: 0 40px;
  box-sizing: border-box;
  .title {
    margin: 15px 0;
    font-size: 16px;
    font-weight: bold;
    height: 25px;
    line-height: 25px;
    vertical-align: middle;
  }
  .bs {
    float: left;
    width: 6px;
    height: 16px;
    background: #19c3a0;
    opacity: 1;
    border-radius: 3px;
    margin: 4px 5px 0 0;
  }
  .empty {
    text-align: center;
    margin: 0 auto;
    vertical-align: middle;
    position: relative;
    font-size: 16px;
    .empty-none {
      font-size: 8px;
      color: #d4d3d3;
      margin-top: -5px;
      position: relative;
    }
  }
  .card-list {
    .card-wrap {
      background-color: #fff;
      margin-bottom: 20px;
      transition: all 0.15s linear;
      &:hover {
        box-shadow: 0 1px 6px rgba(0, 0, 0, 20%) !important;
      }
      .card {
        height: 100px;
        color: #2c3e50;
        padding: 15px;
        box-sizing: border-box;
        cursor: pointer;
      }
    }
    .card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .comp-tit {
        width: 300px;
        font-size: 16px;
        font-weight: bold;
      }
      .comp-version {
        width: 120px;
        font-size: 10px;
        color: #bbb;
        padding: 4px 10px 0 10px;
        border: 1px solid #bbb;
        border-radius: 100px;
      }
    }
    .paragraph {
      margin-top: 10px;
      .text {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        word-break: break-all; // 注意这个文字多行很重要
        -webkit-box-orient: vertical;
      }
    }
    .tips {
      display: flex;
      margin-top: 15px;
      .tips-tit {
        margin-right: 5px;
        font-size: 12px;
        color: #999;
      }
      .tips-item {
        margin-right: 15px;
        padding: 2px 10px;
        border-radius: 100px;
        background-color: #def8f8;
        font-size: 12px;
        color: #92d5d9;
      }
    }
  }
}

/deep/.ivu-tooltip-contentr {
  max-height: 400px;
  overflow-y: auto;
}
/deep/.ivu-tooltip-inner {
  max-height: 300px;
  overflow-y: auto;
}
.textversion {
  display: inline-block;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}
.textversionTitle {
  width: 150px; /*要显示文字的宽度*/
  text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
  white-space: nowrap; /*让文字不换行*/
  overflow: hidden; /*超出要隐藏*/
}
</style>
