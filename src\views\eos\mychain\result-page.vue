<template>
  <div class="result">
    <div class="imgstyle" v-if="res === 'fail'">
      <img src="@/assets/img/fail.png">
      <p>提交失败，请重新创建链账户！</p>
    </div>
    <div class="imgstyle" v-else-if="res === 'success'">
      <img src="@/assets/img/success.png">
      <p class="font-b" v-if="$route.query.status === 'APPROVED'">链账户验证通过，无需审批，已发送创建指令！</p>
      <p v-else class="font-b">提交成功，预计<span>两个工作日内</span>完成审核！</p>
    </div>
    <div class="imgstyle" v-else>
      <img src="@/assets/img/null.png">
      <p>当前无结果返回，可与管理员联系！</p>
    </div>
    <div class="imgstyle1">
      <p>租户管理员可通过：管理员权限-->审批管理-->链账户审批处进行链账户审批</p>
    </div>
    <div class="btn">
      <Button @click="handleClick('replace')" type="success" icon="ios-add">新建链账户</Button>
      <Button :style="'fail'==res?'display:block':'display:none'" @click="handleClick('back')" type="error" icon="md-arrow-back">重新提交</Button>
      <Button :style="'success'==res?'display:block':'display:none'" @click="handleClick('push')" type="primary" icon="md-checkmark-circle-outline">返回链账户列表</Button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'result_page',
  props: {
    res: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleClick (type) {
      if (type === 'back') {
        this.$router.back()
      } else if (type === 'push') {
        this.$router.push({
          name: 'chain_table',
          params: {
            reload: true
          }
        })
      } else if (type === 'replace') {
        localStorage.removeItem('newUser')
        this.$router.replace({
          name: 'new_user'
        })
      }
    }
  },
  mounted () {
  }
}
</script>
<style lang="less" scoped>
.result {
  .imgstyle {
    position: relative;
    /* margin: 0 auto; */
    text-align: center;
    margin-top: 12%;
  }
  .imgstyle1 {
    position: relative;
    /* margin: 0 auto; */
    text-align: center;
    margin-top: 2%;
  }
  p {
    text-align: center;
  }
  .font-b {
    font-weight: 600;
    span {
      color: red;
    }
  }
  .btn {
    display: inline-block;
    position: relative;
    left: 50%;
    margin: 6% auto;
    transform: translate(-50%, -50%);
  }
  button {
    float: left;
    margin: 0 10px;
  }
}
</style>
