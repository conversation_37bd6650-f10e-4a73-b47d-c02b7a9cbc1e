<template>
  <div class="contract">
    <Collapse v-model="panelValue" simple name="mainpanel">
      <Panel name="1" style="background:#ECEFFC;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        合约上架信息
        <p slot="content" class="basetext">
          <span>合约包名称：{{this.Interface.contractBagName}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>链类型：{{this.Interface.chainType}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>合约语言：{{this.Interface.contractLanguage==='JS'?'Java Script':this.Interface.contractLanguage}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>适用场景信息：{{this.Interface.applicaSecene}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>版本信息:</span>
          <edit-table-mul :height="200" style="width: 900px;" border :columns="tableTitle" v-model="VersionData" @on-selection-change="getSelectAll()"></edit-table-mul>
        </p>
        <div slot="content" class="apply_for" v-if="this.curType==='申请下架'">
          <h2>申请下架说明</h2>
          <p class="apply_for_detail">下架原因： <span>{{this.$route.params.applyReason}}</span> </p>
        </div>
        <div slot="content" class="apply_for" v-else-if="this.curType==='恢复上架'">
          <h2>恢复上架说明</h2>
          <p class="apply_for_detail">上架原因 ：<span>{{this.$route.params.applyReason}}</span> </p>
        </div>
      </Panel>
      <Panel name="2" style="background:#ECEFFC;display:block;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        审批意见
        <div slot="content">
          <div slot="content" class="basetext">
            <p class="mandatory" style="margin-left:1.1%">审批结果：

              <span style="color: #52C7AA;margin-left:5px;font-size:18px;font-weight:bold;" v-if="this.isConsent">审核通过</span>
              <span style="color:red;margin-left:5px;font-size:18px;font-weight:bold;" v-else>审核不通过</span>
              <img :src=" require(`../../../../assets/img/${this.isConsent ? 'pass':'unpass'}.png`)" alt="">
            </p>

            <!-- <img v-if="this.show"  src="../../../../assets/img/pass.png" alt="">
<img v-else src="../../../../assets/img/unpass.png" alt=""> -->
          </div>
          <div slot="content" class="basetext">
            <p><span>审批说明： {{this.market}}</span></p>
            <!-- <Input v-model="formValidate.describe" placeholder="输入审批说明" type="textarea" :maxlength="200" show-word-limit :autosize="{minRows: 3,maxRows: 5}" style="width: 350px"  /> -->
          </div>
          <p slot="content" class="basetext">
            <Button type="primary" @click="$router.back(-1)" style="margin-right: 10px">返回</Button>

          </p>

        </div>
      </Panel>
    </Collapse>
    <!-- 详情弹框 -->
    <!-- <Modal
        v-model="versionmodal"
        title="版本详情"
        width='900px'
        >
        <div class="versionDetailone">
     <div class="detailModalInfo">
                <h3>运维信息</h3>
              </div>
           <p  class="detailModal">
         合约类型： <span>{{versionDetails.contractTypeDesc}}</span>
        </p>
           <p  class="detailModal">
         TPS预估： <span> {{versionDetails.tps}}</span>
        </p>
        </div>
        <div class="versionDetailtwo">
            <ul class="pending_ui" v-for="item in opsLinkman" :key="item.id">
        <li> 运维联系人：<span  v-if="item.tenantName"><i class="ri-organization-chart"></i>{{item.tenantName}}</span> </li>
           <li> <span v-if="item.name"><i class="ri-user-line"></i>{{item.name}}</span> </li>
           <li><span v-if="item.phone"><i class="ri-smartphone-line"></i>{{item.phone}}</span> </li>
         </ul>
           <ul class="pending_ui">
          <li> 需求联系人：<span v-if="demandSide.tenantName"><i class="ri-organization-chart"></i>{{demandSide.tenantName}}</span> </li>
          <li> <span v-if="demandSide.name"><i class="ri-user-line"></i>{{demandSide.name}}</span> </li>
          <li><span v-if="demandSide.phone"><i class="ri-smartphone-line"></i>{{demandSide.phone}}</span> </li>
        </ul>

         <ul class="pending_ui" v-for="item in callData" :key="item.id">
            <li>调用联系人： <span v-if="item.tenantName"><i class="ri-organization-chart"></i>{{item.tenantName}}</span> </li>
          <li> <span v-if="item.name"><i class="ri-user-line"></i>{{item.name}}</span> </li>
          <li><span v-if="item.phone"><i class="ri-smartphone-line"></i>{{item.phone}}</span> </li>
        </ul>
        </div>
    </Modal> -->
    <!-- 合约链码 -->

    <Modal v-model="chaincode" title="查询合约链码" width='900px'>
      <p style="margin-bottom:20px">上传版本号：{{this.title}}</p>

      <div v-if="isSingleCpp=='0'">
        <Layout>
          <Sider hide-trigger :style="{background: '#fff'}">
            <Menu theme="light" width="auto" :open-names="['1']">
              <Submenu :name="key" v-for="(item,key) in cppsTitle" :key="key">
                <template slot="title">
                  <Icon type="ios-folder"></Icon>
                  {{key}}
                </template>
                <MenuItem :name="items.fileName" v-for="(items,i) in item" :key="i" @click.native="clickCpps(items.content)">{{items.fileName}}</MenuItem>
              </Submenu>
            </Menu>
          </Sider>
          <Content :style="{padding: ' 0 24px', minHeight: '280px', background: '#fff'}">
            <p>
              <textarea class="textarea-style" v-html="cppContent" readonly @scroll="handScrolljs($event, 'abi')"></textarea>
            </p>
          </Content>
        </Layout>

      </div>
      <div v-else>
        <Collapse simple accordion v-if="this.Interface.contractLanguage === 'C++'">
          <Panel :name="transferKey1" :key="transferKey1">
            {{transferKey1}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.cppcentent.fileContent" readonly @scroll="handScroll($event, 'cpp')"></textarea>
            </p>
          </Panel>
          <Panel :name="item" v-for="item in filesHpp" :key='item'>
            {{item}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.hppcentent.fileContent" readonly @scroll="handScroll($event, 'hpp')"></textarea>
            </p>
          </Panel>
        </Collapse>
        <Collapse simple accordion v-else>
          <Panel :name="transferKey1" :key="transferKey1">
            {{transferKey1}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.jscentent.fileContent" readonly @scroll="handScroll($event, 'js')"></textarea>
            </p>
          </Panel>
          <Panel :name="fileName" v-if="fileName">
            {{fileName}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.abicentent.fileContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
            </p>
          </Panel>
        </Collapse>
      </div>
    </Modal>
  </div>
</template>
<script>

import EditTableMul from '_c/edit-table-mul'
import { getMarketInfo, getChaincode } from '@/api/data'
export default {
  components: {
    EditTableMul
  },
  data () {
    return {
      callData: [], // 调用联系人
      Interface: {}, // 合约上架信息
      market: this.$route.params.content,
      // show: this.Interface.status ? '1' : '2',
      // animal: '', // 单选按钮
      panelValue: ['1', '2'],
      chaincode: false, // 查询合约链码弹框
      versionmodal: false, // 版本详情弹框
      VersionTitle: [

        {
          title: '版本号',
          key: 'uploadVersion',
          with: 180
        },
        {
          title: 'cpp文件名',
          key: 'cppFileName'

        },
        {
          title: 'hpp文件名',
          key: 'hppFileNames',
          tooltip: true,
          render: (h, params) => {
            return h('div', params.row.hppFileNames.join(','))
          }
        },
        {
          title: '操作',
          key: 'action',
          width: 180,
          align: 'left',
          render: (h, params) => {
            return h('div', [
              // h(
              //   'Button',
              //   {
              //     props: {
              //       type: 'text',
              //       size: 'small'
              //     },
              //     style: {
              //       marginRight: '8px',
              //       color: '#3D73EF',
              //       border: '1px solid #3D73EF'
              //     },
              //     on: {
              //       click: () => {
              //         this.detailModal(params.index)
              //       }
              //     }
              //   },
              //   '详情'
              // ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      VersionData: [], // 版本信息数组
      versionDetails: [],
      opsLinkman: [], // 运维联系人
      demandSide: {}, // 需求联系人
      fileTpye: [],
      CollContent: { cppcentent: {}, hppcentent: {}, jscentent: {}, abicentent: {} },
      codeData: {},
      filesHpp: [],
      title: '', // 查看文件源码标题
      transferKey1: '',
      // 以下是js新添加
      columnsJs: [
        {
          title: '版本号',
          key: 'uploadVersion',
          tooltip: true
        },
        {
          title: 'JavaScript文件名',
          key: 'jsFileName'
        },
        {
          title: 'abi文件名',
          key: 'abiFileName'
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      tableTitle: [],
      fileName: '',
      cppContent: '请选择要看的源码文件',
      cppsTitle: '',
      isSingleCpp: '',
      isSingleCpp1: '',
    }
  },
  computed: {
    isConsent () {
      return this.$route.params.statusdetail === '审核通过'
    },
    curType () {
      // debugger
      // console.log(this.$route.params.bizType)
      return this.$route.params.bizType
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, top: 300 }) },
    // 点击文件源码
    fileModal (params) {
      this.chaincode = true
      this.title = params.row.uploadVersion
      this.codeData = {
        contractId: params.row.contractId,
        uploadVersion: params.row.uploadVersion
      }
      this.isSingleCpp1 = params.row.isSingleCpp
      if (this.Interface.contractLanguage === 'C++') {
        this.transferKey1 = params.row.cppFileName
        this.filesHpp = params.row.hppFileNames
        this.getCode(params.row.cppFileName, 'cpp')
        if (params.row.hppFileNames && params.row.hppFileNames.length > 0) {
          params.row.hppFileNames.forEach(val => this.getNewCode(val, 'hpp'))
        }
      } else {
        this.transferKey1 = params.row.jsFileName
        this.fileName = params.row.abiFileName
        this.getCode(params.row.jsFileName, 'js')
        this.getNewCode(params.row.abiFileName, 'abi')
      }

      // console.log(params)
    },
    getCode (fileName) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        },
        isSingleCpp: this.isSingleCpp1
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          if (this.Interface.contractLanguage === 'C++') {
            this.CollContent.cppcentent = res.data
            this.cppsTitle = res.data.fileContent
          } else {
            this.CollContent.jscentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    getNewCode (fileName, val) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          if (this.Interface.contractLanguage === 'C++') {
            this.CollContent.hppcentent = res.data
          } else {
            this.CollContent.abicentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 点击折叠面板事件
    // colldata (key) {
    //   if (key[0]) {
    //     this.codeData.fileName = key[0]
    //     getChaincode(this.codeData).then(res => {
    //       if (res.code === '00000') {
    //         this.CollContent = res.data
    //         // console.log(this.CollContent)
    //       } else {
    //         this.msgInfo('error', res.message, true)
    //       }
    //     }).catch((error) => {
    //       this.msgInfo('error', error.message, true)
    //     })
    //   }
    // },
    // 点击详情
    // detailModal (index) {
    //   this.versionmodal = true
    //   getAccountOpsDTOMessage(this.VersionData[index].uploadVersion, this.$route.params.usercontract).then(res => {
    //     this.callData = res.data.caller
    //     this.versionDetails = res.data
    //     this.opsLinkman = [res.data.opsLinkman]
    //     this.demandSide = res.data.demandSide
    //   })
    // },

    // 滚动
    handScroll (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        // console.log('到底了', fileType, this.codeTotalPages[fileType])
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    getSelectAll (list) {
      // console.log(list)
    },

    // 取消事件
    handleReset (name) {
      this.$router.push({
        name: 'contract_table'
      })
    },
    clickCpps (value) {
      this.cppContent = value

    }
  },
  mounted () {
    // console.log(this.$route.params.listId)
    // console.log(this.$route.params.content)
    this.tableTitle = this.$route.params.languageType === 'C++' ? this.VersionTitle : this.columnsJs
    if (this.$route.params.listId && this.$route.params.usercontract) {
      // 请求信息和列表
      getMarketInfo(this.$route.params.usercontract, this.$route.params.listId).then(res => {
        // console.log(res.data)
        this.Interface = res.data

        let versionData = res.data.records.map(item => {
          return {
            ...item,
            hppFileNames: item.hppFileNames ? item.hppFileNames : []
          }
        })
        this.VersionData = versionData
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    } else {
      this.$router.push({
        name: 'shelves_approval'
      })
    }
  }
}
</script>

<style lang="less" scoped>
.detailModalInfo {
  margin-bottom: 2%;
}
.versionDetailone {
  margin: 2%;
  p {
    margin-bottom: 2%;
  }
}
.versionDetailtwo {
  i {
    vertical-align: -0.15em;
  }
  padding: 2%;
  // margin-top: 3%;
  // margin: 2%;
  .pending_ui {
    margin-top: 2%;
  }
}
.apply_for {
  margin-left: 1%;
  .apply_for_detail {
    font-size: 20px;

    margin: 1%;
  }
}
.detailModal {
  span {
    margin-left: 3% !important;
  }
}
.mandatory::before {
  content: "*";
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 14px;
  color: #ed4014;
}
.pending_ui {
  display: flex;

  li:nth-child(2) {
    margin-left: 2%;
  }
  li:nth-child(3) {
    margin-left: 2%;
  }
}
.contract {
  margin: -16px;
  button.btn {
    position: absolute;
    right: 10px;
  }
  .basetext {
    display: flex;
    padding-top: 20px;
    span {
      text-align: left;
      margin: 0 26px;
      line-height: 20px;
      word-break: break-all;
    }
  }
}
// from表单
.shelvesInfo {
  padding: 2%;
  // border: 1px solid red;
  /deep/.ivu-form-item-label {
    width: 110px !important;
  }
  /deep/.ivu-form-item-content {
    margin-left: 110px !important;
  }

  .mandatory {
    /deep/.ivu-form-item-label::before {
      content: "*";
      display: inline-block;
      margin-right: 4px;
      line-height: 1;
      font-family: SimSun;
      font-size: 14px;
      color: #ed4014;
    }
  }
}
.newFromSubmit {
  float: right;
  margin-right: 3%;
}
// /deep/.ivu-modal>.ivu-modal-content>.ivu-modal-body{max-height: 60vh;overflow: auto;}
// /deep/.ivu-upload-drag{background-color: #f8f8f9;}
// /deep/.ivu-btn-text:hover {
//   background-color: rgba(61,115,239,.8);
//   color: #fff!important;
// }
// /deep/.ivu-btn-text:active{
//   background-color: #3D73EF;
// }
/deep/.ivu-card {
  background: #f2f6fd;
}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
/deep/.ivu-collapse[name="mainpanel"] {
  border: none;
}
/deep/.ivu-collapse[name="mainpanel"] > .ivu-collapse-item {
  border: none;
  border-radius: 5px 5px;
  margin-bottom: 10px;
}
/deep/.ivu-scroll-container {
  height: auto;
  overflow-y: auto;
}

//
// 滚动条
.textarea-style {
  width: 820px;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
</style>
