<template>
  <Card>
    <div class="workorder-details">
      <div class="detail-content">
        <h3 class="detail-title">问题信息</h3>
        <p>问题位置：<span>{{userInfo.menuFunction}}</span></p>
        <!-- <p>功能：<span>{{userInfo.menuFunction}}</span></p> -->
        <p>标题：<span>{{userInfo.title}}</span></p>
        <p>描述：<span>{{userInfo.brief}}</span></p>
        <p>调整期望：<span>{{userInfo.expectResult}}</span></p>
        <p class="p-img">附件：
          <img :style="{width:changeWidth}" @click="changeWidthFn" v-if="userInfo.isImg" id="myImage">
          <span v-else @click="downloadFile(userInfo.fileId, userInfo.fileName)">
            <!-- <a :href="userInfo.fileAddr" :download="userInfo.fileName">{{userInfo.fileName}}</a> -->
            <a>{{userInfo.fileName}}</a>
          </span>
        </p>
      </div>
      <Divider />
      <div class="detail-content">
        <h3 class="detail-title">填报人信息</h3>
        <p>填报人：<span>{{userInfo.reportUser.reportUserName}}</span></p>
        <p>所属租户：<span>{{userInfo.tenantName}}</span></p>
        <p>平台角色：<span>{{userInfo.reportUser.reportRoleName}}</span></p>
        <p>填报日期：<span>{{userInfo.reportTime}}</span></p>
        <p>邮箱地址：<span>{{userInfo.reportUser.reportEmail}}</span></p>
        <p>联系电话：<span>{{userInfo.reportUser.reportPhoneNumber}}</span></p>
      </div>
      <Divider />
      <div class="detail-content" v-if="userInfo.reportType ==='FOR_OTHERS'">
        <h3 class="detail-title">申请人信息</h3>
        <p>填报人：<span>{{userInfo.applyUser.applyUserName}}</span></p>
        <p>所属租户：<span>{{userInfo.tenantName}}</span></p>
        <p>平台角色：<span>{{userInfo.applyUser.applyRoleName}}</span></p>
        <p>填报日期：<span>{{userInfo.reportTime}}</span></p>
        <p>邮箱地址：<span>{{userInfo.applyUser.applyEmail}}</span></p>
        <p>联系电话：<span>{{userInfo.applyUser.applyPhoneNumber}}</span></p>
      </div>
      <Divider v-if="userInfo.reportType ==='FOR_OTHERS'" />
      <div class="detail-content">
        <h3 class="detail-title">处理情况</h3>
        <p>处理结果：<span>{{this.statusDict[userInfo.orderStatus]}}</span></p>
        <p>说明：<span>{{userInfo.auditRemark}}</span></p>
        <p>处理日期：<span>{{userInfo.auditTime}}</span></p>
      </div>
    </div>
  </Card>
</template>
<script>
import { getOrderDetail, downloadAnnounceFile } from '@/api/data'
import { mapActions } from 'vuex' // mapMutations,

export default {
  data () {
    const statusDict = {
      UNAPPROVED: '待审核',
      PROCESSED_SUCCESS: '处理成功',
      PROCESSED_FAILED: '处理失败'
    }
    return {
      statusDict,
      changeWidth: '100px',
      userInfo: {
        isImg: false,
        reportUser: {}
      },
      orderId: '',
      imgAdd: ''
    }
  },
  methods: {
    ...mapActions([
      'updateOrderId'
    ]),
    changeWidthFn () {
      if (this.changeWidth === '100px') {
        this.changeWidth = '600px'
      } else {
        this.changeWidth = '100px'
      }
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, duration: 8 }) },
    down (url, name) {
      const adom = document.createElement('a')
      adom.setAttribute('href', url)
      adom.setAttribute('download', name)
      document.body.appendChild(adom)
      adom.click()
      document.body.removeChild(adom)
    },
    downloadFile (fileId, fileName) {
      downloadAnnounceFile(fileId).then(res => {
        var blob = new Blob([res])
        var downloadElement = document.createElement('a')
        var href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = fileName
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      }).catch(error => {
        this.msgInfo('error', error, true)
      })
    },
    getQuery (orderId) {
      if (orderId) {
        getOrderDetail(orderId).then(res => {
          // console.log('getOrderDetail===>', res)
          if (res.code !== '00000') this.msgInfo('warning', res.message, true)
          else {
            this.userInfo = res.data
            this.updateOrderId(orderId)
            if (res.data.isImg) {
              downloadAnnounceFile(res.data.fileId).then(res => {
                console.log(res);
                var blob = new Blob([res])
                this.imgAdd = window.URL.createObjectURL(blob)
                document.getElementById('myImage').src = this.imgAdd;
              }).catch(error => {
                this.msgInfo('error', error, true)
              })
            }
          }
        }).catch(error => {
          this.msgInfo('error', error.message, true)
        })
      }
    }
  },
  watch: {},
  created () {
  },
  computed: {
    watchOrderId () {
      if (this.$store.state.tabNav.orderId.toString().indexOf('new') === -1) {
        // console.log('join', this.$store.state.tabNav.orderId)
        return this.getQuery(this.$store.state.tabNav.orderId)
      }
      return ''
    }
  },
  mounted () {
    this.orderId = this.$route.params.orderId
    this.getQuery(this.orderId)
  },
  unmounted () {
    window.URL.revokeObjectURL(this.imgAdd)
  },
}
</script>
<style lang="less" scoped>
.workorder-details {
  .detail-content {
    .detail-title {
      font-weight: bold;
      font-size: 16px;
      height: 32px;
      color: #000;
    }
    .p-img {
      display: flex;
      span {
        color: #3d73ef;
        opacity: 0.8;
        &:hover {
          opacity: 1;
        }
      }
    }
    p {
      min-height: 26px;
      font-size: 14px;
      font-weight: bold;
      color: #000;
      img {
        width: 80px;
        height: auto;
      }
      span {
        font-weight: 500;
        color: #4d4f5c;
      }
    }
  }
}
.tenantdetails {
  .basetext {
    span {
      text-align: left;
      margin: 0 30px;
      line-height: 30px;
    }
  }
}
</style>
