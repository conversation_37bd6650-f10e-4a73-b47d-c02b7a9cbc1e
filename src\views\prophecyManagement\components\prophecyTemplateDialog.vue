<!--
  aturun
预言机模板弹框
  2021/10/21

-->
<template>
  <el-dialog
      class="dialog_sty prophecy_template_dialog"
      title="预言机模板"
      :visible.sync="Visible"
      width="520px"
      destroy-on-close
      :close-on-click-modal="false"
      :before-close="handleClose"
      @opened="open">
    <div class="dialog_content">
      <el-form :model="form" :rules="rules" ref="newConsumerForm" label-width="100px">
        <el-form-item label="模板名称：">
          <div>{{form.tempName}}</div>
        </el-form-item>
        <el-form-item label="创建时间：">
          <div>{{setDates(form.createTime)}}</div>
        </el-form-item>
        <el-form-item label="业务描述：" style="padding-bottom: 68px">
          <el-input show-word-limit maxlength="200" :rows="4" :disabled="operationState!=1" v-model="form.busDescription" placeholder="请输入" type="textarea"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="submitForm">确 定</el-button>
  </span>
  </el-dialog>
</template>

<script>
import {getFormatDates} from "../../../utils/atuUtils";
import * as api from "../api";

export default {
  name: "prophecyTemplateDialog",
  components: {},
  props:[],
  data() {
    return {
      Visible:false,
      form:{
        "appNum": null,
        "busDescription": "",
        "createTime": "",
        "tempId": "",
        "tempName": ""
      },
      description:null,
      operationState:0,
      rules: {
        name: [
          {required: true, message: '请输入用户名称', trigger: 'blur'},
        ],
      }
    }
  },
  watch: {},
  created() {

  },
  mounted() {
  },
  methods: {
    open(){
      //获取数据
        api.getMachineTempDetails(
            {
              tempId:this.form.tempId
            }
        ).then(res=>{
          if(res.code!=0) return this.$message.warning(res.msg)
          this.form = res.result
        })

    },
    setDates(val){
      return getFormatDates(val,'yyyy-mm-dd MM:mm:ss')
    },
    submitForm() {
      if(this.operationState==0){
        this.Visible=false
      }else {
        api.updateDescription({
          "tempId": this.form.tempId,
          "description": this.form.busDescription||''
        }).then(res=>{
          if(res.code!=0) return this.$message.warning(res.msg)
          this.Visible=false
          this.$message({
            type: 'success',
            message: '修改成功!'
          });
          this.$emit('Refresh')
        })
      }


    },
    resetForm() {
      this.$refs['ss'].resetFields();
    },
    handleClose(done) {
       done();
    }
  },

}
</script>

<style lang="less" scoped>
.prophecy_template_dialog{

    .text_inf{
      //overflow: hidden;
      //-webkit-line-clamp: 2;
      //-webkit-box-orient: vertical;
      //display: -webkit-box;
    }

}
</style>
