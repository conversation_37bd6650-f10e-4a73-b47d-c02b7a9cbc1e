const path = require('path')
const resolve = dir => path.join(__dirname, dir)
const Version = new Date().getTime()
const { defineConfig } = require('@vue/cli-service');
const TerserPlugin = require('terser-webpack-plugin');
const baseUrl = process.env.NODE_ENV === 'production' ? '/' : '/'

module.exports = defineConfig({
  lintOnSave: false,
  css: {
    sourceMap: true
  },
  publicPath: baseUrl,
  chainWebpack: config => {
    config.resolve.alias
      .set('@', resolve('src'))
      .set('_c', resolve('src/components'))
      .set('_fabric', resolve('src/api'));

    if (process.env.NODE_ENV !== 'development') {
      config.output.filename(`./js/[name].[chunkhash:8].${Version}.js`);
      config.output.chunkFilename(`./js/[name].[chunkhash:8].${Version}.js`);
      config.plugin('extract-css').tap(args => [{
        filename: `css/[name].[contenthash:8].${Version}.css`,
        chunkFilename: `css/[name].[contenthash:8].${Version}.css`
      }]);

      // 添加 TerserPlugin 配置
      // config.optimization.minimizer('terser').use(TerserPlugin, [{
      //   terserOptions: {
      //     output: {
      //       comments: false, // 移除所有注释
      //     },
      //   },
      // }]);
      // 使用 tap 方法配置 TerserPlugin
      config.optimization.minimizer('terser').tap((args) => {
        args[0].terserOptions.compress.drop_console = true;
        args[0].terserOptions.compress.drop_debugger = true;
        args[0].terserOptions.compress.pure_funcs = ['console.log'];
        args[0].terserOptions.output = {
          comments: false
        };
        return args;
      });
    }
  },
  productionSourceMap: false,
  devServer: {
    host: '0.0.0.0',
    port: 8080,
    proxy: {
      '/cmbaas/portal/': {
        target: 'http://************:30005', // 胡明军
        changeOrigin: true,
        ws: true,
        timeout: 5 * 60 * 1000,
        pathRewrite: {
          '^/cmbaas/portal/': ''
        }
      },
      '/baasapi': {
        target: 'http://**********:8765',
        changeOrigin: true,
        ws: true,
        timeout: 5 * 60 * 1000,
        pathRewrite: {
          '^/baasapi': '/baasapi'
        }
      }
    }
  }
});

