<template>
  <div class="chaintable">
    <!-- <img src="@/assets/img/null.png"> -->
    <div class="search-form">
      <div class="search-form-content">
        <!-- 第一行：3个输入框 + 操作按钮 -->
        <div class="form-row">
          <div class="form-item">
            <div class="label">链账户名称:</div>
            <Input placeholder="请输入链账户名称" style="width:200px" v-model="chainAccountName" />
          </div>
          <div class="form-item">
            <div class="label">类型:</div>
            <Select v-model="accountType" placeholder="请选择链账户类型" style="width:200px" @on-change="searchList">
              <Option value="ALL">全部</Option>
              <Option value="NORMAL">普通链账户</Option>
              <Option value="CONTRACT">合约链账户</Option>
            </Select>
          </div>
          <div class="form-item">
            <div class="label">所属链:</div>
            <Select v-model="chainId" placeholder="请选择目标链" style="width:200px" filterable @on-change="searchList">
              <Option v-for="item in chainIdList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
              <Option :value="chainId" :label="chainId" :disabled="true" v-if="chainIdPageParam.pageIndex < pages && chainIdList.length>0" style="text-align:center">
                <span @mouseover="handleReachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
              </Option>
              <Option :value="chainId" :label="chainId" :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
                <span style="font-size:8px;">已加载全部</span>
              </Option>
            </Select>
          </div>
          <!-- 第一行右侧操作按钮 -->
          <div class="action-buttons">
            <Button type="primary" @click="searchList" style="margin-right: 8px;">查询</Button>
            <Button @click="reset" style="margin-right: 8px;">重置</Button>
            <div class="expand-section">
              <a class="expand-btn" @click="toggleExpand">{{ isExpanded ? '高级查询' : '高级查询' }}
                <Icon :type="isExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
              </a>
            </div>
          </div>
        </div>

        <!-- 第二行：展开的高级查询字段 -->
        <div v-show="isExpanded" class="form-row">
          <div class="form-item">
            <div class="label">所属租户:</div>
            <Input placeholder="请输入所属租户" style="width:200px" v-model="tenantName" />
          </div>
          <div class="form-item">
            <div class="label">链账户归属公司:</div>
            <Select v-model="accountCompanyId" placeholder="请选择归属公司" style="width:200px" filterable @on-change="searchList">
              <Option v-for="item in selectList" :value="item.id" :key="item.id">{{ item.companyName }}</Option>
            </Select>
          </div>
          <div class="form-item">
            <div class="label">状态:</div>
            <Select v-model="status" placeholder="请选择状态" style="width:200px" @on-change="searchList">
              <Option value="null">全部</Option>
              <Option value="UNAPPROVED">待审核</Option>
              <Option value="APPROVED">审核通过</Option>
              <Option value="REJECT">审核拒绝</Option>
              <Option value="CHAIN_SUCCESS">上链成功</Option>
              <Option value="CHAIN_FAILED">上链失败</Option>
            </Select>
          </div>
        </div>

        <!-- 第三行：创建时间（如果展开） -->
        <div v-show="isExpanded" class="form-row">
          <div class="form-item">
            <div class="label">创建时间:</div>
            <DatePicker type="daterange" v-model="dateRange" format="yyyy-MM-dd" placement="bottom-start" placeholder="选择日期时间范围" style="width:320px" @on-change="searchList"></DatePicker>
          </div>
        </div>

      </div>
    </div>
    <div class="add-btn-row">
      <Button icon="md-add" @click="handleUrl('new_user')" :disabled="hasEditPermission" type="success" ghost>新建链账户</Button>
    </div>
    <edit-table-mul style="margin: 10px 0;" :columns="columns" v-model="tableData" :key="transferKey"></edit-table-mul><!-- @on-edit="handleEdit"  -->
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;" />
    <Modal v-model="modal" title="修改链账户" :draggable="true" :mask-closable="false" @on-cancel="cancel" sticky>
      <Form ref="permission" :rules="permissionRule" :model="modifydata" :label-width="140">
        <FormItem label="链账户名称：" prop="chainName">
          <Input v-model="modifydata.chainName" :disabled="this.arrChain.chianStatus==='审核通过'||this.arrChain.chianStatus==='上链成功'||this.arrChain.chianStatus==='上链失败'?true:false" placeholder="填写链账户名称" />
        </FormItem>
        <FormItem label="链账户归属公司：" prop="accountCompanyId">
          <Select v-model="modifydata.accountCompanyId" placeholder="请选择归属公司" filterable @on-change="searchList">
            <Option v-for="item in selectList" :value="item.id" :key="item.id">{{ item.companyName }}</Option>
          </Select>
        </FormItem>
        <FormItem label="业务描述：" prop="newValue" style="margin-top:8%">
          <!-- <Tooltip max-width="200" content="链账户业务描述不超过50个字" style="margin-left: -8px;">
          <Icon type="md-help-circle" style="font-size:16px;" />
        </Tooltip> -->
          <Input v-model="modifydata.newValue" type="textarea" :maxlength="255" show-word-limit :autosize="{minRows: 3,maxRows: 5}" placeholder="填写合约业务描述" />
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="cancel">取消</Button>
        <Button type="primary" @click="ok">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
// import { msgInfo } from '@/lib/tools'
import { getChainIdList, getChainTableData, addChainApproval, reviseDescription, getChainTableDataAll } from '@/api/data'
import { tenantcompanyList } from '@/api/contract'

// import EditTable from '_c/edit-table'
import EditTableMul from '_c/edit-table-mul'
import { localRead } from '@/lib/util'
export default {
  name: 'chain_table',
  components: {
    // EditTable,
    EditTableMul
  },
  data () {
    return {
      imgUrl: require('@/assets/img/arrow.png'),
      pages: 0,
      modal: false,
      transferKey: 0,
      chainId: this.$route.params.chainId ? this.$route.params.chainId : '',
      chainAccountName: '',
      status: "null",
      tenantName: '',
      accountCompanyId: '',
      dateRange: [],
      accountType: 'ALL',
      inputvalue: '',
      isExpanded: false,
      modifydata: { newValue: '', chainName: '', accountCompanyId: '' },
      arrChain: { index: '', chianStatus: '' },
      chainIdPageParam: { pagetotal: 0, pageSize: 60, pageIndex: 1 },
      tablePageParam: { pagetotal: 0, pageSize: 10, pageIndex: 1 },
      chainIdList: [],
      companyList: [],
      tableData: [],
      columns: [
        { key: 'chainAccountName', title: '链账户名称' },
        { key: 'accountType', title: '类型' },
        { key: 'chainName', title: '所属链' },
        { key: 'tenantName', title: '所属租户' }, // , editable: true
        { key: 'accountCompanyName', title: '链账户归属公司' }, // , editable: true
        { key: 'createTime', title: '创建时间', },

        // { key: 'description', title: '描述', tooltip: true }, // , editable: true
        // { key: 'recentActions', title: '最近操作', tooltip: true },
        {
          key: 'status',
          title: '状态',
          // width: '200px',
          render: (h, params) => {
            const row = params.row
            const color = row.status === '待审核' ? 'primary' : row.status === '审核通过' ? '#2db7f5' : row.status === '审核拒绝' ? 'error' : row.status === '上链成功' ? 'success' : '#515a6e'
            // const text = row.status === 1 ? 'Working' : row.status === 2 ? 'Success' : 'Fail';
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, row.status)
          }
        },
        { // key: 'action',// fixed: 'right',
          title: '操作',
          width: 190,
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: { click: () => { this.showDetails(params.index) } }
              }, '详情'),
              h('Button', {
                props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                style: this.buttonStyle,
                on: {
                  click: () => {
                    // if()
                    this.modal = true
                    this.arrChain.index = params.index
                    this.modifydata.newValue = params.row.description
                    this.modifydata.chainName = params.row.chainAccountName
                    this.modifydata.accountCompanyId = params.row.accountCompanyId
                    this.arrChain.chianStatus = params.row.status
                    this.editChain = params.row.chainId
                  }
                }
              }, '修改'),
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: { click: () => { this.showResource(params.index) } }
              }, '资源'),
              h('Button', {
                // attrs: { id: 'btnFA8C15' },
                class: { 'btnFA8C15': true },
                props: { type: 'text', size: 'small' },
                style: {
                  marginRight: '8px',
                  color: '#FA8C15',
                  border: '1px solid #FA8C15',
                  display: params.row.statusKey === 'REJECT' ? 'black' : 'none'
                },
                on: { click: () => { this.addChainApproval(params.index) } }
              }, '重新审批')
            ])
          }
        }
      ],
      permissionRule: {
        chainName: [
          { required: true, pattern: /^(?![.])(?!\d+$)[a-z1-5.]{5,12}(?<![.])$/, message: '不能少于5位或多于12位,仅包含:{a-z,1-5,.},且.不能在开头或结尾,不能为纯数字', trigger: 'blur' }
        ],
        accountCompanyId: [
          { required: true, message: '不能为空' }
        ],
        newValue: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ]
      },
      userPermission: JSON.parse(localRead('userPermission')),
      editChain: '',
      selectList: ''
    }
  },
  methods: {
    init () {
      this.arrChain = { index: '', newValue: '' }
      this.modifydata = { newValue: '', chainName: '', accountCompanyId: '' }
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    // 格式化开始日期时间为 yyyy-MM-dd 00:00:00 格式
    formatDateTimeStart (date) {
      if (!date) return null;
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day} 00:00:00`;
    },
    // 格式化结束日期时间为 yyyy-MM-dd 23:59:59 格式
    formatDateTimeEnd (date) {
      if (!date) return null;
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day} 23:59:59`;
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    getTableData () {
      // 构建查询参数
      const params = {
        chainId: this.chainId,
        pageParam: this.tablePageParam,
        accountType: this.accountType === 'ALL' ? null : this.accountType,
        chainAccountName: this.chainAccountName,
        status: this.status === 'null' ? null : this.status,
        tenantName: this.tenantName,
        accountCompanyId: this.accountCompanyId,
        createTimeStart: this.dateRange && this.dateRange.length > 0 ? this.formatDateTimeStart(this.dateRange[0]) : null,
        createTimeEnd: this.dateRange && this.dateRange.length > 0 ? this.formatDateTimeEnd(this.dateRange[1]) : null,
        inputvalue: this.inputvalue
      }

      getChainTableDataAll(params).then(res => {
        // console.log('getChainTableData===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.tableData = res.data.records
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          ++this.transferKey
        }
      }).catch(error => {
        console.log('getChainTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    reset () {
      if (this.chainIdList[0] && this.chainIdList[0].chainId) this.chainId = this.chainIdList[0].chainId
      this.accountType = 'ALL'
      this.inputvalue = ''
      this.chainAccountName = ''
      this.status = "null"
      this.tenantName = ''
      this.accountCompanyId = ''
      this.dateRange = []
      this.tablePageParam = { pagetotal: 0, pageSize: 10, pageIndex: 1 }
      this.getTableData()
    },
    ok () {
      this.$refs['permission'].validate((valid) => {
        if (valid) {
          if (this.editChain) {
            reviseDescription(this.modifydata.chainName, this.editChain, this.tableData[this.arrChain.index].chainAccountUuid, this.modifydata.newValue, this.modifydata.accountCompanyId,).then(res => {
              // console.log('reviseDescription===>', res)
              if (res.code !== '00000') {
                if (res.code === '500') {
                  this.msgInfo('error', res.message, true)
                } else {
                  this.msgInfo('warning', res.message, true)
                }
              } else {
                this.modal = false
                this.getTableData()
                this.init()
                this.msgInfo('success', res.message, true)
              }
            }).catch(error => {
              console.log('reviseDescription.error===>', error)
              this.msgInfo('error', error.message, true)
            })
          }
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },
    cancel () { this.modal = false; this.init(); this.$refs['permission'].resetFields() },
    handleUrl (url) { localStorage.removeItem('newUser'); this.$router.push({ name: url }) },
    searchList () {
      // console.log('searchList~~', this.chainId)
      if (this.chainId) this.getTableData()
      else this.msgInfo('warning', '请对目标链进行选择后查询！', true)
    },
    addChainApproval (index) {
      if (this.chainId) {
        addChainApproval(this.chainId, `${this.tableData[index].chainAccountId}`).then(res => {
          // console.log('addChainApproval===>', res)
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {
            this.msgInfo('info', res.message)
            this.getTableData()
          }
        }).catch(error => {
          console.log('addChainApproval.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      } else this.msgInfo('warning', '请对目标链进行选择后审批！', true)
    },
    showDetails (index) {
      this.getChainName(this.chainId)
      // console.log('showDetails~~', index, this.chainId, `${this.tableData[index].chainAccountId}`, `${this.tableData[index].chainAccountUuid}`, `${this.tableData[index].chainAccountName}`)
      this.$router.push({
        name: 'chain_details',
        query: {
          // chainId: this.chainId,
          chainId: `${this.tableData[index].chainId}`,
          chainAccountId: `${this.tableData[index].chainAccountId}`,
          chainAccountUuid: `${this.tableData[index].chainAccountUuid}`,
          chainAccountName: `${this.tableData[index].chainAccountName}`,
          status: `${this.tableData[index].status}`,
          chainName: this.chainName,
          ownerTenantId: `${this.tableData[index].ownerTenantId}`, // 租户id
          tenantName: `${this.tableData[index].tenantName}` // 租户名称

        }
      })
    },

    getChainName (value) {
      for (var item in this.chainIdList) {
        if (this.chainIdList[item].chainId === value) {
          this.chainName = this.chainIdList[item].chainName
        }
      }
    },
    // 资源详情
    showResource (index) {
      this.getChainName(this.chainId)
      // console.log('showDetails~~', index, this.chainId, `${this.tableData[index].chainAccountId}`, `${this.tableData[index].chainAccountUuid}`, `${this.tableData[index].chainAccountName}`)
      this.$router.push({
        name: 'chain_resource_details',
        query: {
          // chainId: this.chainId,
          chainId: `${this.tableData[index].chainId}`,
          chainAccountId: `${this.tableData[index].chainAccountId}`,
          chainAccountUuid: `${this.tableData[index].chainAccountUuid}`,
          chainAccountName: `${this.tableData[index].chainAccountName}`,
          status: `${this.tableData[index].status}`,
          chainName: this.chainName,
          ownerTenantId: `${this.tableData[index].ownerTenantId}`, // 租户id
          tenantName: `${this.tableData[index].tenantName}` // 租户名称

        }
      })
    },
    handleReachBottom () {
      if (this.chainIdPageParam.pageIndex < this.pages) {
        this.chainIdPageParam.pageIndex += 1
        this.getChainList(true)
      }
    },
    getChainList (flag) {
      getChainIdList(this.chainIdPageParam).then(res => {
        // console.log('getChainIdList===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          if (flag) {
            this.chainIdList.push.apply(this.chainIdList, res.data.records)
          } else {
            this.chainIdList = res.data.records
            this.chainIdList.unshift({
              chainId: -1,
              chainName: '全部'
            })

          }
          this.chainIdPageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          this.pages = res.data.pages
          if (this.chainIdList[0] && this.chainIdList.length > 0 && flag === undefined) {
            this.chainId = this.chainIdList[0].chainId
            this.getTableData()
          }
        }
      }).catch(error => {
        console.log('getChainIdList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },

    // 获取公司列表
    getCompanyList () {
      let listcompany = {
        companyName: ''
      }
      tenantcompanyList(listcompany).then(res => {
        if (res.code === '00000') {
          this.selectList = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })

    },

    toggleExpand () {
      this.isExpanded = !this.isExpanded;
    }
  },
  mounted () {
    // 确保默认选中"全部"
    this.$nextTick(() => {
      this.accountType = 'ALL'
    })
    this.getChainList()
    this.getCompanyList()
  },
  activated () {
    if (this.$route.params.reload) {
      this.reset() // 重置
    }
  },
  computed: {
    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
}
</script>

<style lang="less" scoped>
.chaintable {
  .search-form {
    border-radius: 4px;
    margin-bottom: 16px;

    .search-form-content {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .form-row {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .form-item {
      display: flex;
      align-items: center;
      flex: 0 0 auto;
      .label {
        width: 110px;
        text-align: right;
        padding-right: 8px;
        flex-shrink: 0;
      }
    }

    .action-buttons {
      display: flex;
      align-items: center;
      // gap: 8px;
      // flex-shrink: 0;
      // margin-left: auto;
    }

    .expand-section {
      display: flex;
      align-items: center;
      .expand-btn {
        color: #3d73ef;
        cursor: pointer;
        white-space: nowrap;
      }
    }

    .right-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .actions {
        display: flex;
        align-items: center;
      }
    }
  }

  .add-btn-row {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }
}

/deep/.ivu-btn-text:hover {
  // color: #57a3f3;
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.btnFA8C15:hover {
  background-color: rgba(250, 140, 21, 0.8) !important;
}
/deep/.btnFA8C15:active {
  background-color: #fa8c15 !important;
}
/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
</style>
