<template>
  <div>
    <li><span>修改密码</span></li>
    <Form ref="newPwd" :rules="newPwdRule" :model="userInfo" label-position="top" style="padding:20px 0 0 10px;">
      <FormItem v-if="flag" :label="getExpireTime" prop="password">
        <Input type="password" v-model="userInfo.password" style="width:270px;">
        <i class="ri-eye-close-line" slot="suffix" @click="handleShow"></i>
        </Input>
      </FormItem>
      <FormItem v-else :label="getExpireTime" prop="password">
        <Input v-model="userInfo.password" style="width:270px;">
        <i class="ri-eye-line" slot="suffix" @click="handleShow"></i>
        </Input>
      </FormItem>
      <FormItem label="新密码" prop="newPassword" v-if="flag">
        <Input type="password" v-model="userInfo.newPassword" style="width:270px;">
        <i class="ri-eye-close-line" slot="suffix" @click="handleShow"></i>
        </Input>
      </FormItem>
      <FormItem label="新密码" prop="newPassword" v-else>
        <Input v-model="userInfo.newPassword" style="width:270px;">
        <i class="ri-eye-line" slot="suffix" @click="handleShow"></i>
        </Input>
      </FormItem>
      <FormItem label="确认新密码" prop="reNewPassword" v-if="flag">
        <Input type="password" v-model="userInfo.reNewPassword" style="width:270px;">
        <i class="ri-eye-close-line" slot="suffix" @click="handleShow"></i>
        </Input>
      </FormItem>
      <FormItem label="确认新密码" prop="reNewPassword" v-else>
        <Input v-model="userInfo.reNewPassword" style="width:270px;">
        <i class="ri-eye-line" slot="suffix" @click="handleShow"></i>
        </Input>
      </FormItem>
      <FormItem>
        <!-- <p style="font-size:14px;font-weight:400;margin-bottom:20px;">有效期至：{{expireTime}}</p> -->
        <Button type="primary" @click="handlePwdSubmit" style="font-size:14px;" :disabled="getSaveFlag">保存</Button>
        <Button @click="handlePwdCancel" style="margin-left: 8px;font-size:14px;">返回</Button>
      </FormItem>
    </Form>
  </div>
</template>
<script>
import { updatePassword } from '@/api/data'
import { getconfig } from '@/api/contract'
import { encryptedData, decryptData } from '@/lib/encrypt'
import { setToken } from '@/lib/util'
import { isPassword } from '@/lib/check'
export default {
  props: {
    expireTime: {
      type: String,
      default: ''
    }
  },
  data () {
    const validatePassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value === this.userInfo.password) {
        callback(new Error('新密码与旧密码一致,请重置!'))
      } else if (!isPassword(value)) {
        callback(new Error('至少包含一位大小写字母、数字，特殊字符只能包含!#$%且至少一位'))
      } else {
        callback()
      }
    }
    const validateNewPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.userInfo.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      timer: null,
      saveFlag: true,
      flag: true,
      userInfo: {
        password: '',
        newPassword: '',
        reNewPassword: ''
      },
      newPwdRule: {
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, min: 8, message: '密码不能少于8位', trigger: 'blur' },
          { max: 20, message: '密码不能多于20位', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validatePassword }
        ],
        reNewPassword: [
          { required: true, min: 8, message: '密码不能少于8位', trigger: 'blur' },
          { max: 20, message: '密码不能多于20位', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateNewPassword }
        ]
      }
    }
  },
  computed: {

    getSaveFlag () {
      if (this.userInfo.password && this.userInfo.newPassword && this.userInfo.reNewPassword) {
        if (isPassword(this.userInfo.newPassword) && this.userInfo.reNewPassword === this.userInfo.newPassword) {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },
    getExpireTime () {
      if (this.expireTime) {
        return '旧密码（有效期至：' + this.expireTime + ')'
      } else {
        return '旧密码'
      }
    }

  },
  methods: {
    getpublicKey () {
      let name = 'RSA_PUBLIC_KEY'
      getconfig(name).then((res) => {
        this.savepublicKey(decryptData(res.data.value))
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    getphone () {
      let name = 'CONTACT_PHONE'
      getconfig(name).then((res) => {
        this.phone = res.data.value.split(',')
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 保存系统公钥
    savepublicKey (val) {
      this.$store.commit('SAVE_PUBLICKEY', val)

      // console.log("公钥",this.$store.state.publicKey)
    },
    handlePwdSubmit (name) {
      this.$refs['newPwd'].validate((valid) => {
        if (valid) {
          updatePassword(encryptedData(this.userInfo.password, this.$store.state.publicKey), encryptedData(this.userInfo.newPassword, this.$store.state.publicKey)).then((res) => {
            if (res.code === '00000') {
              this.msgInfo('success', res.message + '即将跳转到登陆界面', true)
              this.userInfo = {
                password: '',
                newPassword: '',
                reNewPassword: ''
              }
              this.timer = setTimeout(() => {
                setToken('')
                this.$router.push({
                  name: 'login'
                })
              }, 3 * 1000)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        }
      })
    },
    handlePwdCancel () {
      this.$emit('select', 0)
      this.initUserInfo()
    },
    handleShow () {
      this.flag = !this.flag
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    initPwd () {
      this.$nextTick(() => {
        this.$refs['newPwd'].resetFields()
      })
      this.userInfo = {
        password: '',
        newPassword: '',
        reNewPassword: ''
      }
    }
  },
  mounted () {
    this.getpublicKey()
    this.getphone()
    // this.uploadList = this.$refs.upload.fileList
    // this.$Message.config({
    //   top: 250,
    //   duration: 3
    // })
  },
  destroyed () {
    clearInterval(this.timer)
  }
}
</script>
<style lang="less" scoped>
.click {
  cursor: pointer;
}
ul,
li {
  list-style: none;
  margin-top: 20px;
}
li {
  //.click;
  font-size: 14px;
  padding: 5px 10px;
}
li span {
  vertical-align: middle;
  display: table-cell;
  font-weight: bold;
  font-size: 16px;
  margin-top: 0px;
  cursor: default;
}
.active {
  color: #3f7dff;
  //padding-bottom: 10px;
  border-right: solid #3f7dff 3px;
  height: 28px;
  font-weight: bold;
  z-index: 999;
  background-color: #eef4ff;
}
.link {
  // width: 5px;
  height: 100%;
  border-left: solid #e4e4e6 2px;
  padding-left: 9px;
  margin-left: -2px;
}
/deep/.ivu-form-item-label {
  font-size: 14px !important;
}
/deep/.ivu-modal-header-inner {
  font-size: 14px !important;
  font-weight: bold;
}
/deep/.ivu-input {
  font-size: 14px !important;
}
.ivu-btn {
  font-size: 14px;
}
</style>
