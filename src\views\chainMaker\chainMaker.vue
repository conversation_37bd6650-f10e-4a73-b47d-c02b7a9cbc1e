<template>
<div style="height:100%;">
    <div style="padding:10px 0px 20px 10px;">
      <div style="float:right;">
          <Input
            prefix="ios-search"
              type="text"
              style="width:400px;margin-right:-1px;"
              v-model="searchData"
              placeholder="区块高度/交易哈希"
              @keyup.enter.native="browserBlur(searchData)">
          </Input>
          <Button type="primary" style="padding:2px;width:75px;height:31px;" @click="browserBlur(searchData)"> 搜索</Button>
      </div>
    </div>
    <div v-show="!isShow">
    <div class="title"><div class="bs"></div><div>实时数据</div></div>
      <Row :gutter="18">
        <i-col :xs="12" :md="12" :lg="6">
            <div class="bg-title">
              <img class="imgs" :src="imagesurl1">
              <div class="content" :style="blockHeight.params.unit ? '' : 'padding-top:5px;'">
                <p class="bg-title1">区块高度</p>
                <p ref="init" class="bg-title2">{{blockHeight.count}}</p>
                <div class="tip" :style="getWidth(blockHeight.params.val)" v-if="blockHeight.params.unit">
                  <div class="inner">
                  </div>
                  <div class="inner1" :style="blockHeight.params.val >5 ? 'margin-left:-8px;' : ''">
                    <span>{{blockHeight.params.unit}}</span>
                  </div>
                </div>
              </div>
            </div>
        </i-col>
        <i-col :xs="12" :md="12" :lg="6">
             <div class="bg-title">
               <img class="imgs" :src="imagesurl2">
               <div class="content" :style="tradeCount.params.unit ? '' : 'padding-top:5px;'">
                  <p class="bg-title1">交易数<span v-if="tradeCount.unit">({{tradeCount.unit}})</span></p>
                  <p class="bg-title2">{{tradeCount.count}}</p>
                  <div class="tip" :style="getWidth(tradeCount.params.val)" v-if="tradeCount.params.unit">
                  <div class="inner" style="background-color:#677daa">
                  </div>
                  <div class="inner1">
                    <span>{{tradeCount.params.unit}}</span>
                  </div>
                </div>
               </div>
            </div>
        </i-col>
        <i-col :xs="12" :md="12" :lg="6">
            <div class="bg-title">
               <img class="imgs" :src="imagesurl3">
               <div class="content" :style="orgCount.params.unit ? '' : 'padding-top:5px;'">
                  <p class="bg-title1">组织数<span v-if="orgCount.unit">({{orgCount.unit}})</span></p>
                  <p class="bg-title2">{{orgCount.count}}</p>
                  <div class="tip" :style="getWidth(orgCount.params.val)" v-if="orgCount.params.unit">
                  <div class="inner" style="background-color:#ef5c5e"></div>
                  <div class="inner1" :style="orgCount.params.val >5 ?'margin-left:-8px;' : ''">
                    <span>{{orgCount.params.unit}}</span>
                  </div>
                </div>
               </div>
            </div>
        </i-col>
        <i-col :xs="12" :md="12" :lg="6">
            <div class="bg-title">
               <img class="imgs" :src="imagesurl4">
               <div class="content" :style="contractCount.params.unit ? '' : 'padding-top:5px;'">
                  <p class="bg-title1">合约数<span v-if="contractCount.unit">({{contractCount.unit}})</span></p>
                  <p class="bg-title2">{{contractCount.count}}</p>
                  <div class="tip" :style='getWidth(contractCount.params.val)' v-if="contractCount.params.unit">
                  <div class="inner" style="background-color:#53577c"></div>
                    <div class="inner1">
                      <span>{{contractCount.params.unit}}</span>
                    </div>
                  </div>
               </div>
            </div>
        </i-col>
      </Row>
      <div class="login_header"  >
        <div @click="cur=0" :class="{active:cur===0}" class="login_header_1">
          <img :src="cur === 0 ? logo1s : logo1" style="cursor:pointer;margin-right:10px;vertical-align:middle"/>
          <span>最近区块</span>
        </div>
        <div @click="cur=1" :class="{active:cur===1}" class="login_header_2">
          <img :src="cur === 1 ? logo2s : logo2" style="cursor:pointer;margin-right:10px;vertical-align:middle">
          <span>最近交易</span>
        </div>
      </div>
      <div v-show="cur===0" class="tab-1">
        <edit-table-mul :columns="columns" v-model="blockData"></edit-table-mul>
      </div>
      <div v-show="cur===1" class="tab-1">
        <edit-table-mul :columns="columns2" v-model="tradeData"></edit-table-mul>
      </div>
    </div>
    <div v-show="isShow" class="show-style">
        <img class="imgs" :src="showUrl">
        <p class="msg-style">{{showMsg}}</p>
    </div>
</div>
</template>

<script>
import { chainMakerBrowser, chainMakerBlock, chainMakerTrade } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import { isCmBlockNum, isTrxId } from '@/lib/check'
import { transferData, transferVal } from '@/lib/transformUnit'
// import { mapState } from 'vuex'
export default {
  name: 'chainMaker_index',
  components: {
    EditTableMul
  },
  data () {
    return {
      isShow: false,
      showUrl: require('@/assets/img/null.png'),
      showMsg: '',
      cur: 0,
      searchData: '',
      timer: null,
      imagesurl1: require('@/assets/img/chainmaker/card1.png'),
      imagesurl2: require('@/assets/img/chainmaker/card2.png'),
      imagesurl3: require('@/assets/img/chainmaker/card3.png'),
      imagesurl4: require('@/assets/img/chainmaker/card4.png'),
      logo1: require('@/assets/img/browser/logo1.png'),
      logo1s: require('@/assets/img/browser/logo1s.png'),
      logo2: require('@/assets/img/browser/logo2.png'),
      logo2s: require('@/assets/img/browser/logo2s.png'),
      blockHeight: { count: 0, unit: '', params: { unit: '', val: 0 } },
      orgCount: { count: 0, unit: '', params: { unit: '', val: 0 } },
      tradeCount: { count: 0, unit: '', params: { unit: '', val: 0 } },
      contractCount: { count: 0, unit: '', params: { unit: '', val: 0 } },
      columns: [
        { key: 'blockHeight',
          title: '区块高度',
          render: (h, params) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  marginRight: '5px',
                  color: 'blue',
                  whiteSpace: 'nowrap',
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    this.handleGoBlock(params.row.blockHeight)
                  }
                }
              }, params.row.blockHeight)
            ])
          }
        },
        { key: 'blockHash', title: '区块哈希', minWidth: 300 },
        { key: 'tradeCount', title: '交易数量' },
        { key: 'blockTime', title: '时间' }
      ],
      columns2: [
        { key: 'tradeHash',
          title: '交易哈希',
          minWidth: 300,
          render: (h, params, key) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  marginRight: '5px',
                  color: 'blue',
                  whiteSpace: 'nowrap',
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    this.handleGoTrade(params.row.tradeHash)
                  }
                }
              }, params.row.tradeHash)
            ])
          }
        },
        { key: 'blockHeight',
          title: '区块高度',
          render: (h, params, key) => {
            return h('div', [
              h('a', {
                props: {
                  type: 'primary',
                  size: 'small'
                },
                style: {
                  marginRight: '5px',
                  color: 'blue',
                  whiteSpace: 'nowrap',
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    this.handleGoBlock(params.row.blockHeight)
                  }
                }
              }, params.row.blockHeight)
            ])
          }
        },
        { key: 'tradeTime', title: '交易时间' }
      ],
      blockData: [],
      tradeData: [],
      hisPath: ''
    }
  },
  computed: {
  },
  methods: {
    getWidth (val) {
      return `width: ${0.2 + val}vw`
    },
    browserBlur (val) {
      if (isCmBlockNum(val)) {
        this.$router.push({
          name: 'chainMaker_block',
          query: {
            blockNum: val,
            tag: true
          }
        })
      } else if (isTrxId(val)) {
        this.$router.push({
          name: 'chainMaker_trade',
          query: {
            txId: val,
            tag: true
          }
        })
      } else {
        if (val === '') {
          this.msgInfo('warning', '未输入任何查询信息，请检查！', true)
        } else {
          this.msgInfo('warning', '输入信息有误，请检查！', true)
        }
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    transformData (val1, val2, val3) {
      if (!val3) {
        val2.count = val1
        val2.params.unit = transferData(val1).unit
        val2.params.val = transferData(val1).val
      } else {
        val2.count = transferVal(val1).value
        val2.unit = transferVal(val1).unit
        val2.params.unit = transferData(val2.count).unit
        val2.params.val = transferData(val2.count).val
      }
    },
    // 查询实时数据
    getBrowserData () {
      chainMakerBrowser().then(res => {
        if (res.code === '00000') {
          this.isShow = false
          this.transformData(res.data.blockHeight ? res.data.blockHeight : 0, this.blockHeight, false)
          this.transformData(res.data.orgCount ? res.data.orgCount : 0, this.orgCount, true)
          this.transformData(res.data.tradeCount ? res.data.tradeCount : 0, this.tradeCount, true)
          this.transformData(res.data.contractCount ? res.data.contractCount : 0, this.contractCount, true)
          this.getBlockData()
          this.getTradeData()
        } else {
          this.isShow = true
          this.showMsg = res.message
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.isShow = true
        this.showMsg = error.message
        this.msgInfo('error', error.message, true)
        if (error.code === 'C0005') {
          clearInterval(this.timer)
        }
      })
    },
    // 查询最近区块
    getBlockData () {
      chainMakerBlock().then(res => {
        if (res.code === '00000') {
          this.blockData = res.data ? res.data : []
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
        if (error.code === 'C0005') {
          clearInterval(this.timer)
        }
      })
    },
    // 查询最近交易
    getTradeData () {
      chainMakerTrade().then(res => {
        if (res.code === '00000') {
          this.tradeData = res.data ? res.data : []
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
        if (error.code === 'C0005') {
          clearInterval(this.timer)
        }
      })
    },
    // 区块高度超链接
    handleGoBlock (value) {
      this.$router.push({
        name: 'chainMaker_block',
        query: {
          blockNum: value
        }
      })
    },
    // 交易哈希超链接
    handleGoTrade (value) {
      this.$router.push({
        name: 'chainMaker_trade',
        query: {
          txId: value
        }
      })
    },
    startTimer () {
      if (this.timer) {
        clearInterval(this.timer)
      } else {
        this.timer = setInterval(() => {
          this.getBrowserData()
        }, 10000)
      }
    },
    btnClick () {
      this.$router.push({
        name: 'iframe'
      })
    }
  },
  mounted () {
    this.getBrowserData()
    this.startTimer()
  },
  deactivated () {
    clearInterval(this.timer)
    this.timer = null
  },
  destroyed () {
    clearInterval(this.timer)
    this.timer = null
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      if (from.name === 'chainMaker_block' || from.name === 'chainMaker_trade') {
        vm.hisPath = from.name
      } else {
        vm.hisPath = ''
      }
    })
  },
  watch: {
  }
}
</script>

<style lang="less" scoped>
.active {
  color: #3d73ef;
  padding-bottom: 8px;
  border-bottom: 3px solid #3d73ef;
  cursor: pointer;
}
/deep/.ivu-card-body {
  padding: 0px !important;
}
.circles:hover {
  display: block;
}
.title {
  height: 18px;
  font-weight: bold;
  font-size: 16px;
  font-family: "Microsoft YaHei";
  line-height: 18px;
  color: #333333;
  margin: 30px 0 10px 10px;
  vertical-align: middle;
  .bs {
    float: left;
    width: 6px;
    height: 18px;
    background: #19c3a0;
    opacity: 1;
    border-radius: 3px;
    margin-right: 6px;
  }
}
.font {
  font-family: "D-DIN";
}
.bg-title {
  position: relative;
  color: #ffffff;
  .content {
    text-align: left;
    position: absolute;
    z-index: 2;
    top: 45%;
    left: 25%;
    transform: translate(-25%, -50%);
    .bg-title1 {
      font-size: 0.84vw;
      font-family: "Microsoft YaHei";
      font-weight: 400;
      color: #ffffff;
      text-shadow: -1px -1px 1px rgba(103, 67, 10, 0.25);
      opacity: 0.7;
    }
    .bg-title2 {
      font-size: 1.77vw;
      font-family: "D-DIN";
    }
    .tip {
      opacity: 0.6;
      width: 90px;
      height: 1px;
      border-top: 1px solid #c5c4c4;
      margin-top: -2px;
      .inner {
        background-color: #f37441;
        width: 5px;
        height: 6px;
        border: 1px solid #e2e1e1;
        position: relative;
        left: 10px;
        top: -4px;
        transform: rotate(45deg);
        border-right: 0px;
        border-bottom: 0px;
      }
      .inner1 {
        margin-top: -8px;
        font-size: 8px;
        transform: scale(0.8);
        color: #e2e1e1;
        margin-left: 4px;
      }
    }
  }
}
.imgs {
  height: auto;
  width: auto;
  width: 100%;
}
/deep/.ivu-card {
  transition: none;
}
.login_header {
  height: 72px;
  font-size: 16px;
  background-image: url("../../assets/img/browser/head.png");
  background-repeat: no-repeat;
  background-size: 100% 72px;
}
.login_header_1 {
  margin-right: 30px;
  cursor: pointer;
  font-weight: bold;
  width: 100px;
  display: inline-block;
  margin-left: 30px;
}
.login_header_2 {
  cursor: pointer;
  font-weight: bold;
  padding-top: 15px;
  width: 100px;
  display: inline-block;
}

.tab-1 {
  width: 100%;
  height: 100%;
  background: #ffffff;
  opacity: 1;
  border-radius: 22px 22px 0px 0px;
  border: 1px solid #a5a4bf17;
  margin-top: -20px;
  padding: 20px;
}
/deep/.ivu-table-tip {
  overflow: hidden;
}
.show-style {
  display: table;
  text-align: center;
  vertical-align: middle;
  margin: 20px auto;
  position: relative;
  padding: 8%;
  .msg-style {
    color: #b7b8b9;
    font-size: 12px;
  }
}
.imgs {
  height: auto;
  width: auto;
  width: 100%;
}
/deep/.ivu-input{
  border-radius: 4px 0 0 4px;
}
/deep/.ivu-btn{
   border-radius: 0 4px 4px 0;
}
</style>
