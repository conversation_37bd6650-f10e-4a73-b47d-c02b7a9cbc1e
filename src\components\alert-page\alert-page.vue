<template>
  <Modal
    v-model="modal"
    title="新建权限"
    @on-ok="ok"
    @on-cancel="cancel"
    :mask-closable="false">
    <Table :columns="columns" :data="data"></Table>
    <Button @click="handleAdd" type="">新建权限</Button>
  </Modal>
</template>

<script>
export default {
  name: 'AlertPage',
  props: {
    modal: true
  },
  data () {
    return {
      columns: [
        {
          title: '权限',
          key: 'name'
        },
        {
          title: '父权限',
          key: 'age'
        },
        {
          title: '密钥生成方式',
          key: 'address'
        }
      ],
      data: [
        {
          name: 'Owner',
          age: '/',
          address: 'New York No. 1 Lake Park',
          date: '2016-10-03'
        },
        {
          name: 'Active',
          age: 'Owner(默认)',
          address: 'London No. 1 Lake Park',
          date: '2016-10-01'
        }
      ]
    }
  },
  methods: {
    ok () {
      this.$Message.info('Clicked ok')
    },
    cancel () {
      this.$Message.info('Clicked cancel')
    }
  }
}
</script>
