<template>
  <div class="page">
    <div class="content">
      <div>
        <span class="back"  @click="goback">返回</span>
        <span>{{detail.ChannelName}}</span>
      </div>
      <div class="title">
        <div class="left">
          <!-- <span class="infotext" @click="goDetail">通道信息</span>
          <img :src="arrowIcon" class="icon">
          <span class="infotext"  @click="goChannelMgr">{{detail.ChannelName}}通道详情</span> -->
          <img :src="infoIcon" class="infoIcon">
          <span class="infotext">合约信息</span>
        </div>
      </div>
      <!-- 通道合约信息 -->
      <SpaceLayout top="0" paddingX="0" paddingY="0">
        <div slot="padding">
          <div class="table-wrapper">
            <el-row class="nav-box">
              <el-col :span="3"><div class="">合约名称</div></el-col>
              <el-col :span="6"><div class="">版本</div></el-col>
              <el-col :span="6"><div class="">背书组织</div></el-col>
              <el-col :span="5"><div class="">背书策略</div></el-col>
              <el-col :span="4"><div class="">状态</div></el-col>
            </el-row>
            <div class="none" v-if="chainCodes.length == 0">
              <i class="el-icon-loading" v-if="paddingText == '数据请求中...'"></i>
              <!-- <svg-icon icon-class="table-empty" v-else/> -->
              {{paddingText}}
            </div>
            <div class="nan-item" v-for="(item,chainIndex) in listData" :key="chainIndex">
              <el-row  class="nav-box">
                  <el-col :span="3"><div class=""><span>{{item.Name}}</span></div></el-col>
                  <el-col :span="6"><div class=""><span>{{item.Version}}</span></div></el-col>
                  <el-col :span="6">
                    <div class="">
                      <div v-for="(citem,cindex) in item.Endorse" :key="cindex">
                        <div v-for="(orgs,orgindex) in citem.Orgs" :key="orgindex">
                          <span>
                            {{orgs.Name}}
                          </span>
                        </div>

                      </div>
                    </div>
                  </el-col>
                  <el-col :span="5">
                    <div class="">
                      <div v-for="(citem,ccindex) in item.Endorse" :key="ccindex">
                        <span v-if="citem.Rule == 'all'">
                          全部同意
                        </span>
                        <span v-if="citem.Rule == 'part'">
                          {{'部分同意-' + citem.AgreeCount + '个'}}
                        </span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="4">
                    <div v-if="item.Status == 'unInstalled'" class="tag red">待安装</div>
                    <div v-if="item.Status == 'inited'" class="tag green">可调用</div>
                    <div v-if="item.Status == 'installed'" class="tag blue">待初始化</div>
                  </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </SpaceLayout>
      <Page
        :total="total"
        :current.sync="pageIndex"
        @on-change="handleCurrentChange"
        :page-size="pageSize"
        :page-size-opts="[10,20,40,60,100]"
        show-total show-elevator show-sizer
        @on-page-size-change="handleSizeChange"
        style="text-align:right;"/>
      <!-- <pagination
          @toHandleSizeChange="handleSizeChange"
          @toHandleCurrentChange="handleCurrentChange"
          @toJumpFirstPage="jumpFirstPage"
          @toJumpLastPage="jumpLastPage"
          :fTotal="total"
          :fBtnStartDisabled="btnStartDisabled"
          :fBtnEndDisabled="btnEndDisabled"
          :fPageIndex="pageIndex"
          :fZys="zys"
          >
      </pagination> -->
    </div>
    <!-- <countDown v-if="isShowIcon" :state="countState" :countTime="countTime" :text="countText" @getCountDown="getCountDown"></countDown> -->
  </div>
</template>

<script>
import{getChaincodeEndorseRule}from '@/api/baascore/channelMgr'
import {getChannelChaincodeList} from '@/api/baascore/agreement';
import SpaceLayout from '@/components/SpaceLayout'
import {forbidden} from "@/utils/index.js";
import { mapGetters } from 'vuex'
export default {
  mixins: [forbidden],
  props: {
    detail:{
      type: Object,
      default() {
          return {}
      }
    }
  },
  components: {
    SpaceLayout
  },
  data() {
    return {
      countState:'',
      countTime:2,
      countText:'',
      isShowIcon:false,
      arrowIcon:require('@/assets/chainManage_images/overview/arrow.png'),
      infoIcon: require("@/assets/chainManage_images/overview/infoIcon.png"),
      chainCodes:[], // 合约
      total:0,
      btnStartDisabled: false, //用来判断首页尾页按钮是否禁用
      btnEndDisabled: false, //用来判断首页尾页按钮是否禁用
      zys: 0,
      pageIndex: 1,
      pageSize:10,
      paddingText:'数据请求中...',
      listData:[],
      // detail:{
      //   ChannelName: "",
      //   ServiceId: "",
      // },
    }
  },
  computed: {
    ...mapGetters(['chainItem']),
    getChainCodeStatus(status) {
      return function(status) {
        switch(status) {
          case 'unInstalled':
            return '已发布待安装';
          case 'installed':
            return '已安装待初始化';
          case 'inited':
            return '已初始化';
        }
      }
    },
  },
  watch : {
    // chainItem: {
    //   handler(newvalue, oldvalue) {
    //     if (newvalue.Id != oldvalue.Id) {
    //       this.$router.push({
    //         path:'/chainManage/channelMgr'
    //       })
    //     }
    //   },
    //   deep: true,
    // },
  },
  mounted () {
    //this.detail = JSON.parse(sessionStorage.getItem('detail'))
    document.body.scrollTop = 0;
    document.documentElement.scrollTop = 0;
    this.getChannelChaincodeList()
  },
  methods: {
    getCountDown(type) {
      this.isShowIcon = false
    },
    getListData() {
      // this.zys = Math.ceil(this.total/ this.pageSize); //获取总页数
      // this.pageIndex > this.zys ? this.pageIndex = this.zys : '';
      //  // 调用混入方法判断首页尾页按钮禁用的方法
      // this.forbidden(this.zys, this.pageIndex);
      this.listData = this.chainCodes.slice((this.pageIndex -1) * this.pageSize, this.pageSize * this.pageIndex)
    },
    //合约列表
    getChannelChaincodeList() {
      var ServiceId = this.detail.ServiceId
      var ChannelName = this.detail.ChannelName
      var params = {
        ServiceId,
        ChannelName
      }
      getChannelChaincodeList(params).then(res =>{
        if(res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if(res.data && res.data.ChainCodes) {
            var data = res.data.ChainCodes
            data.forEach(item =>{
              if(item.Status == 'inited') {
                this.chainCodes.push(item)
              }
            })
            this.total = this.chainCodes.length
            if(this.total == 0) {
              this.paddingText = '暂无数据'
            }
            this.zys = Math.ceil(this.total / this.pageSize); //获取总页数
            if(this.chainCodes.length > 0) {
              this.chainCodes.forEach((item,index) =>{
                var ServiceId = this.detail.ServiceId
                var ChannelName = this.detail.ChannelName
                var params = {
                  ServiceId,
                  ChannelName,
                  ChainCodeName:item.Name,
                  Version:item.Version
                }
                getChaincodeEndorseRule(params).then(res =>{
                  if(res.code == 200) {
                    // this.isShowIcon = true
                    // this.countState = 'success'
                    // this.countText = '请求成功！'
                    this.$set(this.chainCodes[index],'Endorse',res.data)
                    this.getListData()
                  }else {
                    this.getListData()
                    // this.isShowIcon = true
                    // this.countState = 'error'
                    // this.countText = '数据获取失败，请重新加载！'
                    this.$message.error("数据获取失败，请重新加载！");
                  }
                })
              })
            }
          }else {
            this.paddingText = '暂无数据'
          }
        }
        if(res.code != 200) {
          this.paddingText = '暂无数据'
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      })
    },
    goback() {
      this.$emit('getisShowChainCodes',false)
    },
    goChannelMgr() {
      this.$emit('getisShowChainCodes',false)
    },
    goDetail() {
      this.$emit('getIsShowDetial',false)
    },
    handleSizeChange(val) {
      this.pageSize  = val
      this.getListData()
    },
    handleCurrentChange(val) {
      this.pageIndex = val
      this.getListData()
    },
    // 首页按钮
    jumpFirstPage(val) {
      this.pageIndex = val;
      this.handleCurrentChange(val);
    },
    // 尾页按钮
    jumpLastPage(val) {
      this.pageIndex = val;
      this.handleCurrentChange(this.pageIndex);
    },
  }
}
</script>

<style rel="stylesheet/less" lang="less" scoped>
@import "../../../../styles/common/modal.less";
.page {
  width: 100%;
  height: 100%;
  //margin-top:-20px;
  .content {
    //padding-bottom: 50px;
    .title {
      margin: 20px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .infotext {
        // font-size: 22px;
        font-size: 14px;
        color: #333333;
        vertical-align: middle;
        cursor: pointer;
      }
      .icon {
        width: 22px;
        height: 22px;
        margin: 0 24px;
      }
      .name {
        // font-size: 20px;
        font-size: 14px;
        font-weight: bold;
        color: #333333;
        cursor: default;
      }
      .left {
        display: flex;
        align-items: center;
        .infoIcon {
          width: 3px;
          height: 14px;
          margin-right:5px;
          vertical-align: middle;
        }
        .infotext {
          // font-size: 22px;
          font-size: 14px;
          color: #333333;
          vertical-align: middle;
        }
      }

    }
    .none {
      text-align: center;
      color: #666;
      // font-size: 18px;
      font-size: 14px;
    }
    .nav-box{
      color: #999999;
      // font-size: 18px;
      font-size: 14px;
      box-sizing: border-box;
      /*margin-top:20px;*/
    }
    .nav-box /deep/ .el-col{
      text-align: center;
      color: #999999;
      // font-size: 18px;
      font-size: 14px;
      padding:0px 0;
      //line-height: 36px;
    }
    .nav-box .el-col div span{
      /*cursor: default;*/
    }
    .nan-item .nav-box:hover{
      -moz-box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6; -webkit-box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6; box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6;
    }
    .nan-item .nav-box{
        // font-size: 16px;
        font-size: 14px;
        box-sizing: border-box;
        background: #fff;
        display: flex;
        align-items: center;
    }
    .nan-item .nav-box /deep/ .el-col {
      color: #666666;
    }
    .nan-item .nav-box .el-col div span.call {
      display: inline-block;
      width: 102px;
      height: 36px;
      border-radius: 4px;
      text-align: center;
      line-height: 36px;
      background: #00ADA2;
      // font-size: 16px;
      font-size: 14px;
      color: #FFFFFF;
    }
    .nan-item .nav-box .el-col div span.text {
      display: block;
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
      /*padding: 0 40px;*/
    }
    .nan-item .nav-box .el-col div span.textLeft {
      /*padding-left: 36px;*/
    }
    .pagination {
      /*margin-top: 16px;*/
      // display: flex;
      // justify-content: flex-end;
      // align-items: center;
    }
  }
  .ivu-page{
    margin-top:10px;
  }

}
</style>
