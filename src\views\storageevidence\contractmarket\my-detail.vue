<template>
  <div class="contract">
    <Collapse v-model="panelValue" simple name="mainpanel">
      <Panel name="1" style="background:#ECEFFC;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        合约上架信息
        <p slot="content" class="basetext">
          <span>合约包名称：{{previewData.contractBagName}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>链类型：{{previewData.chainType}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>合约语言：{{previewData.contractLanguage==='JS'?'Java Script':previewData.contractLanguage}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>适用场景信息：{{previewData.applicaSecene}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>版本信息:</span>
          <edit-table-mul :height="200" style="width: 900px;" border :columns="tableTitle" v-model="VersionData"></edit-table-mul>

        </p>

      </Panel>
      <Panel name="2" style="background:#ECEFFC;display:block;" v-if="!shjlsfzh">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        审批记录
        <div slot="content">
          <div class="record">
            <edit-table-mul style="width: 1000px;" border :columns="RecordTitle" v-model="RecordData"></edit-table-mul>

          </div>

          <p slot="content" class="btn">

          </p>

        </div>
      </Panel>
    </Collapse>

    <Modal v-model="chaincode" title="查询合约链码" width='900px'>
      <p style="margin-bottom:20px">上传版本号：{{this.title}}</p>
      <div v-if="isSingleCpp=='0'">
        <Layout>
          <Sider hide-trigger :style="{background: '#fff'}">
            <Menu theme="light" width="auto" :open-names="['1']">
              <Submenu :name="key" v-for="(item,key) in cppsTitle" :key="key">
                <template slot="title">
                  <Icon type="ios-folder"></Icon>
                  {{key}}
                </template>
                <MenuItem :name="items.fileName" v-for="(items,i) in item" :key="i" @click.native="clickCpps(items.content)">{{items.fileName}}</MenuItem>
              </Submenu>
            </Menu>
          </Sider>
          <Content :style="{padding: ' 0 24px', minHeight: '280px', background: '#fff'}">
            <p>
              <textarea class="textarea-style" v-html="cppContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
            </p>
          </Content>
        </Layout>
      </div>
      <div v-else>
        <Collapse simple accordion v-if="this.previewData.contractLanguage === 'C++'">
          <Panel :name="transferKey1" :key="transferKey1">
            {{transferKey1}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.cppcentent.fileContent" readonly @scroll="handScroll($event, 'cpp')"></textarea>
            </p>
          </Panel>
          <Panel :name="item" v-for="item in filesHpp" :key='item'>
            {{item}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.hppcentent.fileContent" readonly @scroll="handScroll($event, 'hpp')"></textarea>
            </p>
          </Panel>
        </Collapse>
        <Collapse simple accordion v-else>
          <Panel :name="transferKey1" :key="transferKey1">
            {{transferKey1}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.jscentent.fileContent" readonly @scroll="handScroll($event, 'js')"></textarea>
            </p>
          </Panel>
          <Panel :name="fileName" v-if="fileName">
            {{fileName}}
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.abicentent.fileContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
            </p>
          </Panel>
        </Collapse>
      </div>

    </Modal>
    <!-- 详情弹框 -->
    <!-- <Modal
        v-model="versionmodal"
        title="版本详情"
        width='700px'
        >
        <div class="versionDetailone">
   <div  class="detailModalInfo">
          <h3>运维信息</h3>
        </div>
          <p class="detailModal">
           合约名称：<span>{{previewData.contractName}}</span>
        </p>
        <p class="detailModal">
          版本号：<span></span>
        </p>
           <p  class="detailModal">
          合约类型：<span> {{versionDetails.contractTypeDesc}}</span>
        </p>
           <p  class="detailModal">
          TPS预估：<span> {{versionDetails.tps}}</span>
        </p>
        </div>
        <div class="versionDetailtwo">
            <ul class="pending_ui" v-for="item in opsLinkman" :key="item.id">
        <li> 运维联系人：<span  v-if="item.tenantName"><i class="ri-organization-chart"></i>{{item.tenantName}}</span> </li>
           <li> <span v-if="item.name"><i class="ri-user-line"></i>{{item.name}}</span> </li>
           <li><span v-if="item.phone"><i class="ri-smartphone-line"></i>{{item.phone}}</span> </li>
         </ul>
        <ul class="pending_ui">
          <li> 需求联系人：<span v-if="demandSide.tenantName"><i class="ri-organization-chart"></i>{{demandSide.tenantName}}</span> </li>
          <li> <span v-if="demandSide.name"><i class="ri-user-line"></i>{{demandSide.name}}</span> </li>
          <li><span v-if="demandSide.phone"><i class="ri-smartphone-line"></i>{{demandSide.phone}}</span> </li>
        </ul>

         <ul class="pending_ui" v-for="item in callData" :key="item.id">
            <li>调用联系人： <span v-if="item.tenantName"><i class="ri-organization-chart"></i>{{item.tenantName}}</span> </li>
          <li> <span v-if="item.name"><i class="ri-user-line"></i>{{item.name}}</span> </li>
          <li><span v-if="item.phone"><i class="ri-smartphone-line"></i>{{item.phone}}</span> </li>
        </ul>
        </div>

    </Modal> -->
    <Button :size="buttonSize" type="primary" @click="$router.back(-1)" style="margin-left: 50%;width: 100px;margin-top: 10px;">返回</Button>
  </div>
</template>
<script>

import EditTableMul from '_c/edit-table-mul'
import { ContractOwnerPreview, getContractChaincode } from '@/api/data'
export default {
  components: {
    EditTableMul
  },
  data () {
    return {
      callData: [], // 调用联系人
      // animal: '', // 单选按钮
      buttonSize: 'large',
      panelValue: ['1', '2'],
      chaincode: false, // 查询合约链码弹框
      versionmodal: false, // 版本详情弹框
      previewData: [], // 详细信息
      versionDetails: [], // 版本详情信息
      opsLinkman: [], // 运维联系人
      demandSide: {}, // 需求联系人
      transferKey1: '',
      shjlsfzh: '',
      VersionTitle: [
        {
          title: '版本号',
          key: 'uploadVersion',
          with: 180
        },
        {
          title: 'cpp文件名',
          key: 'cppFileName'

        },
        {
          title: 'hpp文件名',
          key: 'hppFileNames',
          tooltip: true,
          render: (h, params) => {
            return h('div', params.row.hppFileNames.join(','))
          }

        },
        {
          title: '操作',
          key: 'action',
          width: 180,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              // h(
              //   'Button',
              //   {
              //     props: {
              //       type: 'text',
              //       size: 'small'
              //     },
              //     style: {
              //       marginRight: '8px',
              //       color: '#3D73EF',
              //       border: '1px solid #3D73EF'
              //     },
              //     on: {
              //       click: () => {
              //         this.detailModal(params.index)
              //       }
              //     }
              //   },
              //   '详情'
              // ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      console.log(params);
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      VersionData: [],
      // 审批意见表格
      RecordTitle: [
        {
          title: '申请类型',
          key: 'applyType',
          with: 180
        },
        {
          title: '审批状态',
          key: 'status',
          with: 180
        },
        {
          title: '审批时间',
          key: 'createTime'

        },
        {
          title: '审批人',
          key: 'auditUserName'

        },
        {
          title: '说明',
          key: 'remark',
          tooltip: true
        }
      ],
      RecordData: [],
      CollContent: { cppcentent: {}, hppcentent: {}, jscentent: {}, abicentent: {} },
      codeData: {},
      filesHpp: [],
      title: '', // 查看文件源码标题
      // 以下是添加js
      columnsJs: [
        {
          title: '版本号',
          key: 'uploadVersion',
          tooltip: true
        },
        {
          title: 'JavaScript文件名',
          key: 'jsFileName'
        },
        {
          title: 'abi文件名',
          key: 'abiFileName'
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      tableTitle: [],
      fileName: '',
      cppContent: '请选择要看的源码文件',
      cppsTitle: '',
      isSingleCpp: ''
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    // 点击文件源码
    fileModal (params) {
      console.log(params);
      this.chaincode = true
      this.title = params.row.uploadVersion
      this.codeData = {
        contractId: params.row.contractId,
        uploadVersion: params.row.uploadVersion,
        fileId: params.row.id
      }
      this.isSingleCpp = params.row.isSingleCpp
      this.cppsTitle = params.row.fileContent
      if (this.previewData.contractLanguage === 'C++' && this.isSingleCpp != '0') {
        this.transferKey1 = params.row.cppFileName
        this.filesHpp = params.row.hppFileNames
        this.getCode(params.row.cppFileName, 'cpp')
        if (params.row.hppFileNames && params.row.hppFileNames.length > 0) {
          params.row.hppFileNames.forEach(val => this.getNewCode(val, 'hpp'))
        }
      } else {
        this.transferKey1 = params.row.jsFileName
        this.fileName = params.row.abiFileName
        this.getCode(params.row.jsFileName, 'js')
        this.getNewCode(params.row.abiFileName, 'abi')
      }
    },
    getCode (fileName) {
      let contractId = this.codeData.contractId
      let uploadVersion = this.codeData.uploadVersion
      let pageParam = {
        pageSize: 1,
        pageIndex: 10
      }
      getContractChaincode(contractId, uploadVersion, fileName, pageParam).then(res => {
        if (res.code === '00000') {
          if (this.previewData.contractLanguage === 'C++') {
            this.CollContent.cppcentent = res.data
          } else {
            this.CollContent.jscentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    getNewCode (fileName, val) {
      let contractId = this.codeData.contractId
      let uploadVersion = this.codeData.uploadVersion
      let pageParam = {
        pageSize: 1,
        pageIndex: 10
      }
      getContractChaincode(contractId, uploadVersion, fileName, pageParam).then(res => {
        if (res.code === '00000') {
          if (this.previewData.contractLanguage === 'C++') {
            this.CollContent.hppcentent = res.data
          } else {
            this.CollContent.abicentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // colldata (key) {
    //   if (key[0]) {
    //     let fileName = key[0]
    //     let contractId = this.codeData.contractId
    //     let uploadVersion = this.codeData.uploadVersion
    //     let pageParam = {
    //       pageSize: 1,
    //       pageIndex: 10
    //     }

    //     getContractChaincode(contractId, uploadVersion, fileName, pageParam).then(res => {
    //       if (res.code === '00000') {
    //         this.CollContent = res.data
    //       } else {
    //         this.msgInfo('error', res.message, true)
    //       }
    //     }).catch((error) => {
    //       this.msgInfo('error', error.message, true)
    //     })
    //   }
    // },
    // 点击详情
    // detailModal (index) {
    //   let uploadVersion = this.VersionData[index].uploadVersion
    //   let contractId = this.VersionData[index].contractId
    //   getAccountOpsDTOMessage(uploadVersion, contractId).then(res => {
    //     this.callData = res.data.caller
    //     this.versionDetails = res.data
    //     this.opsLinkman = [res.data.opsLinkman]
    //     this.demandSide = res.data.demandSide
    //   })
    //   this.versionmodal = true
    // },
    // 滚动
    handScroll (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    getSelectAll (list) {

    },

    // 取消事件
    handleReset (name) {
      this.$router.push({
        name: 'contract_market'
      })
    },

    // 请求
    getTablist () {
      let contractId = this.$route.params.contractId
      let sort = this.$route.params.sort
      this.tableTitle = this.$route.params.languageType === 'C++' ? this.VersionTitle : this.columnsJs
      ContractOwnerPreview(contractId, sort).then(res => {
        if (res.code === '00000') {
          this.previewData = res.data
          let statusdata = {
            '1': '审核通过',
            '2': '审核不通过'
          }
          let applytypeenmu = {
            'on': '申请上架',
            'off': '申请下架 ',
            'recover': '恢复上架'
          }

          if (res.data.auditRecordDOList) {
            this.RecordData = res.data.auditRecordDOList.map(item => {
              return {
                ...item,
                status: statusdata[item.status],
                applyType: applytypeenmu[item.applyType]
              }
            })
          }
          this.shjlsfzh = res.data.isManager

          let versionData = res.data.records.map(item => {
            return {
              ...item,
              hppFileNames: item.hppFileNames ? item.hppFileNames : []
            }
          })
          this.VersionData = versionData
          // this.myshelvesData = myshelvesData,
          //  this.myPageParam = {
          //   pagetotal: res.data.total,
          //   pageSize: res.data.size,
          //   pageIndex: res.data.current
          // }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    clickCpps (value) {
      this.cppContent = value

    }
  },
  mounted () {
    if (this.$route.params.contractId) {
      this.getTablist()
    } else {
      this.$router.push({
        name: 'contract_market'
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.ivu-menu,
.ivu-menu-dark,
.ivu-menu-vertical,
.ivu-menu-opened,
.ivu-menu-submenu-title {
  background: #ffffff !important;
}
/deep/.ivu-menu-submenu-title {
  background: #ffffff !important;
}
/deep/.ivu-menu-vertical.ivu-menu-light:after {
  background: #ffffff;
}
.detailModalInfo {
  margin-bottom: 2%;
}
.versionDetailone {
  margin: 2%;
  p {
    margin-bottom: 2%;
  }
}
.versionDetailtwo {
  i {
    vertical-align: -0.15em;
  }
  padding: 2%;
  // margin-top: 3%;
  // margin: 2%;
  .pending_ui {
    margin-top: 2%;
  }
}
.detailModal {
  span {
    margin-left: 5%;
  }
}
.mandatory::before {
  content: "*";
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 14px;
  color: #ed4014;
}
.pending_ui {
  display: flex;

  li:nth-child(2) {
    margin-left: 2%;
  }
  li:nth-child(3) {
    margin-left: 2%;
  }
}
.contract {
  margin: -16px;
  button.btn {
    position: absolute;
    right: 10px;
  }
  .basetext {
    display: flex;
    padding-top: 20px;
    span {
      text-align: left;
      margin: 0 26px;
      line-height: 20px;
      word-break: break-all;
    }
  }
}
// from表单
.shelvesInfo {
  padding: 2%;
  // border: 1px solid red;
  /deep/.ivu-form-item-label {
    width: 110px !important;
  }
  /deep/.ivu-form-item-content {
    margin-left: 110px !important;
  }

  .mandatory {
    /deep/.ivu-form-item-label::before {
      content: "*";
      display: inline-block;
      margin-right: 4px;
      line-height: 1;
      font-family: SimSun;
      font-size: 14px;
      color: #ed4014;
    }
  }
}
.record {
  padding-top: 25px;
  padding-left: 30px;
}
// /deep/.ivu-modal>.ivu-modal-content>.ivu-modal-body{max-height: 60vh;overflow: auto;}
// /deep/.ivu-upload-drag{background-color: #f8f8f9;}
// /deep/.ivu-btn-text:hover {
//   background-color: rgba(61,115,239,.8);
//   color: #fff!important;
// }
// /deep/.ivu-btn-text:active{
//   background-color: #3D73EF;
// }
/deep/.ivu-card {
  background: #f2f6fd;
}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
/deep/.ivu-collapse[name="mainpanel"] {
  border: none;
}
/deep/.ivu-collapse[name="mainpanel"] > .ivu-collapse-item {
  border: none;
  border-radius: 5px 5px;
  margin-bottom: 10px;
}
/deep/.ivu-scroll-container {
  height: auto;
  overflow-y: auto;
}

//
// 滚动条
.textarea-style {
  width: 820px;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
</style>
