<template>
  <div style="width:700px">
    <Form ref="fromdify" :model="fromdify" :rules="ruleValidate" :label-width="80">
      <FormItem label="模板类型" prop="contractType">
        <Input v-model="fromdify.contractType" placeholder="请填写模板类型" />
      </FormItem>
      <FormItem label="链类型" prop="chainType">
        <Select v-model="fromdify.chainType" placeholder="请选择链类型" disabled>
          <Option v-for="item in cityList" :value="item.enumValue" :key="item.enumKey">{{ item.enumValue }}</Option>
        </Select>
      </FormItem>
      <FormItem label="合约语言" prop="languagetype">
        <Select v-model="fromdify.languagetype" placeholder="请选择合约语言" disabled>
          <Option v-for="item in languageList" :value="item.enumKey" :key="item.enumKey">{{ item.enumValue }}</Option>
        </Select>
      </FormItem>
      <FormItem label="适用场景" prop="scene">
        <Input v-model="fromdify.scene" placeholder="请填写适用场景" />
      </FormItem>
      <FormItem label="描述" prop="description">
        <Input v-model="fromdify.description" placeholder="请填写描述内容" type="textarea" :maxlength="200" show-word-limit :autosize="{minRows: 3,maxRows: 5}" />
      </FormItem>
      <FormItem label="接口描述" prop="interfacedescribe" class="mandatory">
        <Button type="success" ghost @click="addInfo">新增</Button>
        <!-- <Button type="success" ghost>删除</Button> -->
        <edit-table-mul border :columns="modalDescribe" v-model="modaldescribeData"></edit-table-mul>
      </FormItem>

      <FormItem label="模板上传">
        <div v-if="ideShowZ">
          <p style="float-right" v-if="ideShow">
            <Button type="success" ghost @click="editbtn" :disabled="fromdify.languagetype==='JS'||changeModal==='manycpp'">在线编辑</Button>
          </p>
        </div>

        <div style="display: flex;padding-top: 1%;" v-if="fromdify.languagetype=='C++'">
          <!-- <p>cpp文件：</p>
          <RadioGroup v-model="changeModal">
            <Radio disabled label="onecpp">单cpp</Radio>
            <Radio disabled label="manycpp">多cpp</Radio>
          </RadioGroup> -->
        </div>
        <Upload v-if="fromdify.chaincity=='ChainMaker'" action="" type="drag" multiple :format="['rs', 'sol','go','cpp','hpp']" :accept="accepttype" :before-upload="handleUpload" style="display: inline-table; width: 100%; margin-top: 10px">
          <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
          <h4>支持拖拽上传文件</h4>
          <br />
          <p v-if="fromdify.languagetype=='RUST'" style="color: #aaa; font-size: 12px">仅支持RS文件上传，RS文件上传最大10M</p>
          <p v-else-if="fromdify.languagetype=='GO'||fromdify.languagetype=='TINYGO'" style="color: #aaa; font-size: 12px">仅支持GO文件上传，GO文件上传最大10M</p>
          <p v-else-if="fromdify.languagetype=='SOLIDITY'" style="color: #aaa; font-size: 12px">仅支持SOL文件上传，SOL文件上传最大10M</p>
          <p v-else-if="fromdify.languagetype=='C++'&&changeModal=='onecpp'" style="color: #aaa; font-size: 12px">CPP/HPP文件支持多个文件上传,CPP文件只能有一个,<br>HPP文件可以零个、一个或多个,CPP/HPP文件总大小不能超过10M。</p>
          <p v-else-if="fromdify.languagetype=='C++'&&changeModal=='manycpp'" style="color: #aaa; font-size: 12px">请按照“多CPP文件模板（含CMakeList文件模板）”和“文件规范指引”,将cpp、hpp、CMakeLis文件按照文件规范打包上传(支持zip和tar格式)</p>
        </Upload>

        <Upload v-else action="" type="drag" multiple :format="['zip', 'tar']" :before-upload="handleUpload" style="display: inline-table; width: 100%; margin-top: 10px">
          <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
          <h4>支持拖拽上传文件</h4>
          <br />
          <p v-if="fromdify.languagetype=='C++'&&changeModal=='onecpp'" style="color: #aaa; font-size: 12px">
            支持上传cpp文件(必填)、hpp文件(选填)，且cpp文件文件只能有一个,hpp文件可以一个或多个;<br />合约代码中类名需与合约名称一致。总文件大小不能超过50M。
          </p>
          <p v-else-if="fromdify.languagetype=='JS'" style="color: #aaa; font-size: 12px">支持上传JavaScript文件（必填）、abi文件（必填）、并根据下上传的JavaScript文件<br>生成abi文件并下载编辑，再次进行abi文件的上传（注：JavaScript文件与abi文件一一对应）。</p>
          <p v-else-if="fromdify.languagetype=='C++'&&changeModal=='manycpp'" style="color: #aaa; font-size: 12px">请按照“多CPP文件模板（含CMakeList文件模板）”和“文件规范指引”,将cpp、hpp、CMakeLis文件按照文件规范打包上传(支持zip和tar格式)</p>
        </Upload>

        <edit-table-mul :key="transferKey1" :columns="columns" v-model="tableData"></edit-table-mul>
      </FormItem>

      <!-- <FormItem label="模板上传" >
            <p style="float-right">
                <Button type="success" ghost @click="editbtn" :disabled="fromdify.languagetype==='JS'">在线编辑</Button>
            </p>

            <Upload v-if="fromdify.chainType=='ChainMaker'" action="" type="drag" multiple :format="['rs', 'sol','go','cpp','hpp']" :accept="accepttype" :before-upload="handleUpload" style="display: inline-table; width: 100%; margin-top: 10px">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <h4>支持拖拽上传文件</h4>
                <br />
                <p v-if="fromdify.languagetype=='RUST'" style="color: #aaa; font-size: 12px">仅支持RS文件上传，RS文件上传最大10M</p>
                <p v-else-if="fromdify.languagetype=='GO'||fromdify.languagetype=='TINYGO'" style="color: #aaa; font-size: 12px">仅支持GO文件上传，GO文件上传最大10M</p>
                <p v-else-if="fromdify.languagetype=='SOLIDITY'" style="color: #aaa; font-size: 12px">仅支持SOL文件上传，SOL文件上传最大10M</p>
                <p v-else-if="fromdify.languagetype=='C++'" style="color: #aaa; font-size: 12px">CPP/HPP文件支持多个文件上传,CPP文件只能有一个,<br>HPP文件可以零个、一个或多个,CPP/HPP文件总大小不能超过10M。</p>
            </Upload>

            <Upload v-else  action=""  type="drag" multiple :format="['cpp', 'hpp']" :before-upload="handleUpload" :accept="accepttype" style="display: inline-table; width: 100%; margin-top: 10px">
              <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
              <h4>支持拖拽上传文件</h4>
              <br />
              <p style="color: #aaa; font-size: 12px" v-if="fromdify.languagetype=='C++'">
                支持上传cpp文件(必填)、hpp文件(选填)，且cpp文件文件只能有一个,hpp文件可以一个或多个;<br />合约代码中类名需与合约名称一致。总文件大小不能超过50M。
              </p>
              <p v-else-if="fromdify.languagetype=='JS'" style="color: #aaa; font-size: 12px">支持上传JavaScript文件（必填）、abi文件（必填）、并根据下上传的JavaScript文件<br>生成abi文件并下载编辑，再次进行abi文件的上传（注：JavaScript文件与abi文件一一对应）。</p>
            </Upload>
            <edit-table-mul :key="transferKey1" :columns="columns" v-model="tableData"></edit-table-mul>
          </FormItem> -->
      <FormItem>
        <!-- :loading="loading" @click="toLoading" -->
        <Button type="primary" :loading="loadingStatus" @click="handleSubmit('fromdify',1)">{{ loadingStatus ? "上传中" : "提交" }}</Button>
        <Button @click="handleReset('fromdify')" style="margin-left: 8px">取消</Button>
      </FormItem>
    </Form>
    <!-- 接口描述新增、修改弹框 -->
    <Modal v-model="newdescribe" :title="describeTitle" footer-hide>
      <Form ref="newfrom" :rules="newfromValidate" :model="newfrom" :label-width="80">
        <FormItem label="函数名" prop="funcName">
          <Input v-model="newfrom.funcName" placeholder="请填写函数名" />
        </FormItem>
        <FormItem label="参数" prop="parameter">
          <Input v-model="newfrom.parameter" placeholder="请填写参数" />
        </FormItem>
        <FormItem label="简介" prop="description">
          <Input v-model="newfrom.description" placeholder="请填写简介" />
        </FormItem>
      </Form>
      <div class="newdescribe">
        <Button type="primary" @click="describeOk('newfrom')">确定</Button>
        <Button @click="FormInfoCancel" style="margin-left: 8px">取消</Button>
      </div>
    </Modal>

    <!-- 在线编辑确定弹窗 -->
    <Modal v-model="modal2" title="在线编译" @on-ok="editok" @on-cancel="editcancel">
      <p style="padding:20px;height:100px;">请确认上述数据已填写并保存上述数据，同时页面跳转至IDE平台进行在线编辑</p>
    </Modal>
  </div>
</template>

<script>
import { abidownFile, getconfig } from '@/api/contract'
import { PreviewContractType, TemplateDetails, TemplatePreview, getTempateEos, getTempateLanguage, goIDE } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import { isContractName } from '../../../lib/check'
export default {
  components: {
    EditTableMul
  },
  data () {
    const validateContractName = (rule, value, callback) => {
      let reg = /^[_a-zA-Z]/
      if (!reg.test(value.slice(0, 1))) {
        callback(new Error('只能以英文及下划线开头'))
      }
      if (!isContractName(value)) {
        callback(new Error('支持英文和数字，特殊符号只能有英文句号.和英文_'))
      } else {
        callback()
      }
    }
    return {
      ideShow: false,
      changeModal: 'onecpp',
      manyCpp: null, //
      loadingStatus: false,
      cityList: [], // 链类型数组
      languageList: [], // 语言类型数组
      selectlanguage: 'LANGUAGE_TYPE', // 语言类型传参
      selecteos: 'CHAIN_TYPE', // 链类型传参
      fromdify: {
        contractType: '',
        chainType: '', // 链类型
        languagetype: '', // 语言类型
        scene: '',
        description: ''
      },
      // tableData: this.$route.params.content, // 路由传过来的内容
      difyid: this.$route.params.listIddisy, // 路由传过来的id
      cppfile: '', // 存放上传cpp文件的
      hppfile: [], // 存放上传hpp文件
      file: [], // 文件名称
      newdescribe: false, // 接口描述新增、修改弹框
      bool: false, // 用于上传前判断是否有cpp
      // from表单校验
      ruleValidate: {
        contractType: [{ required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
        { max: 60, message: '不能多于60位', trigger: 'blur' },
        { trigger: 'blur', validator: validateContractName }
        ],
        //
        chainType: [
          {
            required: true,
            message: '不能为空',
            trigger: 'change'
          }
        ],
        languagetype: [
          {
            required: true,
            message: '不能为空',
            trigger: 'change'
          }
        ],
        scene: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, max: 60, message: '不能为空且长度不能超过60位', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, max: 200, message: '不能为空且长度不能超过200位', trigger: 'blur' }
        ]

      },
      // 接口描述
      modalDescribe: [

        {
          title: '函数名（name）',
          key: 'funcName',
          width: 150,
          tooltip: true
        },
        {
          title: '参数（inputs）',
          key: 'parameter',
          width: 150,
          tooltip: true
        },
        {
          title: '简介（description）',
          key: 'description',
          tooltip: true
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.descriptEdit(params)
                    }
                  }
                },
                '修改'
              ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.describeRemove(params)
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      // 接口描述内容列表
      modaldescribeData: [
      ],
      // 模板上传数组
      tableData: [],
      // 上传文件
      columns: [
        { key: 'name', title: '文件名', tooltip: true },
        {
          // key: 'action',
          title: '操作',
          // minWidth: 210,
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    marginTop: '5px',
                    color: '#3D73EF',
                    float: 'left',
                    border: '1px solid #3D73EF',
                    display: params.row.name.indexOf('.js') !== -1 ? 'block' : 'none'
                  },
                  on: {
                    click: () => {
                      this.abiDown(params)
                    }
                  }
                },
                'abi文件下载'
              ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.previewRemove(params)
                    }
                  }
                },
                '删除'
              )
            ])
          }
        }
      ],
      transferKey1: 0,
      describeTitle: '修改', // 修改或新增 标题
      // 接口描述的新增/修改表单
      newfrom: {
        funcName: '',
        parameter: '',
        description: '',
        index: null // 用于替换当前修改的行
      },
      newfromValidate: {
        funcName: [
          {
            required: true,
            message: '不能为空',
            trigger: 'blur'
          }
        ]
      },
      // 保存删除id项
      tabledataId: [],
      traversecpp: false, // 用于提交前判断是否有cpp
      size: 50 * 1024 * 1024, // 计算50M
      infoSize: '', // 用来存放上传文件相加总和
      // curSelectList: [] // 当前选中列表
      modal2: false,
      accepttype: '.cpp,.hpp',
      goFile: '',
      rsFile: '',
      solFile: '',
      jsFile: '',
      abiFile: '',
      ideShowZ: false,
    }
  },
  methods: {
    abiDown (data) {
      let name = this.jsFile.name.split('.')
      abidownFile(data.row.storePath ? [] : this.jsFile, data.row.storePath ? data.row.storePath : '').then(res => {
        let blob = new Blob([res])
        let downloadElement = document.createElement('a')
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        downloadElement.download = name[0] + '.abi'
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    // 上传
    handleUpload (file) {
      if (this.fromdify.languagetype === 'C++' && this.changeModal === 'onecpp') { // c++文件
        if (file.name.indexOf('.cpp') !== -1) {
          if (!this.cppfile) {
            this.cppfile = file
            this.tableData.push({ name: this.cppfile.name, size: file.size })
            this.bool = true
          } else {
            alert('只能上传一个cpp文件')
          }
        } else if (file.name.indexOf('.hpp') !== -1) {
          this.hppfile.push(file)
          this.tableData.push({ name: file.name, size: file.size })
          // let transSize = this.transSize(infoSize) // 字节转 M
        } else {
          alert('请上传对应的文件')
        }
      } else if (this.fromdify.languagetype === 'C++' && this.changeModal === 'manycpp') {
        if (file.name.indexOf('zip') !== -1 || file.name.indexOf('tar') !== -1) {
          if (!this.manyCpp) {
            this.manyCpp = file
            this.tableData.push({ name: this.manyCpp.name, size: file.size })
            this.bool = true
          } else {
            alert('只能上传一个zip/tar压缩包')
          }
        } else {
          alert('只允许上传zip/tar压缩包')
        }
      } else if (this.fromdify.languagetype === 'GO' || this.fromdify.languagetype === 'TINYGO') { // go文件
        if (file.name.indexOf('.go') !== -1) {
          if (!this.goFile) {
            this.goFile = file
            this.tableData.push({ name: file.name, size: file.size })
          } else {
            alert('只能上传一个go文件')
          }
        } else {
          alert('请上传对应的文件')
        }
      } else if (this.fromdify.languagetype === 'RUST') { // rust文件
        if (file.name.indexOf('.rs') !== -1) {
          if (!this.rsFile) {
            this.rsFile = file
            this.tableData.push({ name: file.name, size: file.size })
          } else {
            alert('只能上传一个rs文件')
          }
        } else {
          alert('请上传对应的文件')
        }
      } else if (this.fromdify.languagetype === 'SOLIDITY') { // solidity文件
        if (file.name.indexOf('.sol') !== -1) {
          if (!this.solFile) {
            this.solFile = file
            this.tableData.push({ name: file.name, size: file.size })
          } else {
            alert('只能上传一个sol文件')
          }
        } else {
          alert('请上传对应的文件')
        }
      } else if (this.fromdify.languagetype === 'JS') {

        if (file.name.indexOf('.js') !== -1) {
          if (!this.jsFile) {
            this.jsFile = file
            this.tableData.push({ name: this.jsFile.name, size: file.size })
            this.bool = true
          } else {
            alert('只能上传一个js文件')
          }
        } else if (file.name.indexOf('.abi') !== -1) {
          console.log(this.abiFile)
          if (!this.abiFile) {
            this.abiFile = file
            this.tableData.push({ name: this.abiFile.name, size: file.size })
            this.bool = true
          } else {
            alert('只能上传一个abi文件')
          }
        } else {
          alert('请上传对应的文件')
        }
      } else {
        alert('请选择语言类型')
      }
      return false
    },

    // 初始化接口描述新增/修改弹框input框数据
    initFormInfo () {
      this.newfrom.parameter = ''
      this.newfrom.funcName = ''
      this.newfrom.description = ''
      this.newfrom.index = null
    },
    // 接口描述 弹框 (修改、新增) 确定按钮
    describeOk () {
      this.$refs['newfrom'].validate((valid) => {
        // console.log(valid)
        if (valid) { // 检验通过
          this.newdescribe = false
          const { index, ...info } = this.newfrom
          if (this.describeTitle === '修改') {
            // 保存修改信息
            // console.log(info)
            this.modaldescribeData.splice(index, 1, info) // 替换当前项
          } else {
            // 保存新增
            this.modaldescribeData.push(info)
            // console.log(this.data1)
          }
          this.initFormInfo()
          // 关闭弹框
        } else { // 校验失败
          // console.log(this.newdescribe)
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
      // console.log('点击确定按钮')
      // const { index, ...info } = this.newfrom
      // if (this.describeTitle === '修改') {
      //   // 保存修改信息

      //   // console.log(info)
      //   this.modaldescribeData.splice(index, 1, info) // 替换当前项
      // } else {
      //   // 保存新增
      //   this.modaldescribeData.push(info)
      // }
      // this.initFormInfo()
    },
    // 修改信息 打开弹框
    descriptEdit (info) {
      let { row } = info
      this.newdescribe = true
      this.describeTitle = '修改'
      this.newfrom.parameter = row.parameter
      this.newfrom.funcName = row.funcName
      this.newfrom.description = row.description
      this.newfrom.index = info.index
    },
    // 打开新增弹框
    addInfo () {
      this.newdescribe = true
      this.describeTitle = '新增'
    }, // 接口描述列表删除
    describeRemove (info) {
      // console.log(info.row) // 当前行信息
      // debugger
      this.modaldescribeData.splice(info.index, 1)
    },
    // 接口描述 弹框取消清空表单
    FormInfoCancel () {
      this.newdescribe = false
      this.initFormInfo()
    },
    // 模板上传table数组删除
    previewRemove (info) {
      this.tableData.splice(info.index, 1) // tableData中的删除
      this.tabledataId.push(info.row.fileid) // 将删除项的id添加到tabledataId
      this.hppfile.forEach((item, i) => {
        if (item.name === info.row.name) {
          this.hppfile.splice(i, 1)// 从hppfile删除这一项
        }
      })

      //   this.cppfile = this.tableData.some((item, i) => {
      //     if (item.name === info.row.name) {
      //       this.cppfile = ''// 清空cppfile
      //     }
      //   })
      if (info.row.name.indexOf('cpp') !== -1) {
        this.cppfile = ''
      } else if (info.row.name.indexOf('go') !== -1) {
        this.goFile = ''
      } else if (info.row.name.indexOf('rs') !== -1) {
        this.rsFile = ''
      } else if (info.row.name.indexOf('sol') !== -1) {
        this.solFile = ''
      } else if (info.row.name.indexOf('.js') !== -1) {
        this.jsFile = ''
      } else if (info.row.name.indexOf('.abi') !== -1) {
        this.abiFile = ''
      } else if (info.row.name.indexOf('.zip') !== -1 || info.row.name.indexOf('.tar') !== -1) {
        this.manyCpp = ''
      }
      // console.log(this.hppfile,this.cppfile,this.goFile,this.rsFile,this.solFile,"_____")
    },
    // 点击提交请求接口
    handleSubmit (name, type) {
      this.traversecpp = this.tableData.some(item => {
        if (item.name.indexOf('cpp') !== -1) {
          return true
        } else {
          return false
        }
      })

      this.loadingStatus = true
      this.$refs['fromdify'].validate((valid) => {
        if (valid) {
          if (this.modaldescribeData.length > 0) {
            let newList = {
              contractTypeId: this.difyid,
              contractType: this.fromdify.contractType, // 合约类型
              chainType: this.fromdify.chainType, // 链类型
              scene: this.fromdify.scene, // 适用场景
              description: this.fromdify.description, // 描述
              descriptionList: this.modaldescribeData, // 接口描述列表
              deleteIdList: this.tabledataId,
              isSingleCpp: this.changeModal == 'onecpp' ? '1' : this.changeModal == 'manycpp' ? '0' : ''  //判断是单cpp和多cpp
            }
            let upList = {
              cppFile: this.cppfile,
              goFile: this.goFile, // go文件
              rsFile: this.rsFile, // rs文件
              solFile: this.solFile, // sol文件
              manycppFile: this.manyCpp, // 多cpp文件
            }
            let jsList = {
              jsFile: this.jsFile, // js文件
              abiFile: this.abiFile // abi文件
            }

            if (this.fromdify.chainType == 'EOS' || this.fromdify.chainType == 'BOS') {
              if (this.fromdify.languagetype == 'C++' && this.changeModal == 'manycpp') {
                this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                if (this.infoSize > this.size) {
                  this.loadingStatus = false
                  alert('上传文件大于50M,请重新上传！')
                } else {
                  // 修改from和接口描述接口
                  this.newmodel(newList, upList, type)
                }
              } else {
                if (!this.traversecpp && this.tableData.length > 0) {
                  this.loadingStatus = false
                  alert('必须有一个cpp文件')
                } else {
                  this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                  if (this.infoSize > this.size) {
                    this.loadingStatus = false
                    alert('上传文件大于50M,请重新上传！')
                  } else {
                    // 修改from和接口描述接口
                    this.newmodel(newList, upList, type)
                  }
                }
              }

            } else if (this.fromdify.chainType === 'CMEOS') {
              if (this.fromdify.languagetype === 'JS') {
                if (this.jsFile === '' || this.abiFile === '') {
                  if (this.tableData.length > 0) {
                    this.loadingStatus = false
                    alert('JavaScript文件和abi文件是必填项')
                  } else {
                    this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                    if (this.infoSize > this.size) {
                      this.loadingStatus = false
                      alert('上传文件大于50M,请重新上传！')
                    } else {
                      this.newmodel(newList, jsList, type)
                    }
                  }
                } else {
                  this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                  if (this.infoSize > this.size) {
                    this.loadingStatus = false
                    alert('上传文件大于50M,请重新上传！')
                  } else {
                    this.newmodel(newList, jsList, type)
                  }
                }
              } else {
                if (this.fromdify.languagetype == 'C++' && this.changeModal == 'manycpp') {
                  this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                  if (this.infoSize > this.size) {
                    this.loadingStatus = false
                    alert('上传文件大于50M,请重新上传！')
                  } else {
                    this.newmodel(newList, upList, type)
                  }
                } else {
                  if (this.cppfile === '' && this.tableData.length > 0) {
                    this.loadingStatus = false
                    alert('必须有一个cpp文件')
                  } else {
                    this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                    if (this.infoSize > this.size) {
                      this.loadingStatus = false
                      alert('上传文件大于50M,请重新上传！')
                    } else {
                      this.newmodel(newList, upList, type)
                    }
                  }
                }

              }
            } else if (this.fromdify.chainType == 'ChainMaker') {
              console.log(this.fromdify.languagetype);
              if (this.fromdify.languagetype == 'C++' && this.changeModal == 'manycpp') {
                this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                if (this.infoSize > this.size) {
                  this.loadingStatus = false
                  alert('上传文件大于50M,请重新上传！')
                } else {
                  this.newmodel(newList, upList, type)
                }
              } else {

                if (this.fromdify.languagetype == 'C++' && !this.traversecpp && this.tableData.length > 0) {
                  this.loadingStatus = false
                  alert('必须有一个cpp文件')
                } else {
                  this.infoSize = this.tableData.reduce((prev, next) => { return prev + next.size }, 0)// 计算数组总和
                  if (this.infoSize > 10 * 1024 * 1024) {
                    this.loadingStatus = false
                    alert('上传文件大于10M,请重新上传！')
                  } else {
                    this.newmodel(newList, upList, type)
                  }
                }
              }
            }


          } else {
            this.loadingStatus = false
            this.$Message.success('接口描述不能为空')
          }
        } else {
          this.loadingStatus = false
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },
    // 取消按钮
    handleReset (name) {
      this.$router.push({
        name: 'template_table'
      })
      // this.$refs[name].resetFields()
      // this.modal1 = false
    },

    // 合约修改类型from请求
    details (detailid) {
      // console.log(detailid)
      let detailList = {
        contractModelId: this.difyid
      }
      // 获取修改里的信息和模板上传数组接口
      TemplateDetails(detailList).then(res => {
        if (res.data.chainType === 'EOS' || res.data.chainType === 'BOS') {
          this.languageList = [
            { enumKey: 'C++', enumValue: 'C++' }
          ]
        } else if (res.data.chainType === 'CMEOS') {
          this.languageList = [
            { enumKey: 'C++', enumValue: 'C++' },
            { enumKey: 'JS', enumValue: 'Java Script' }
          ]
        } else if (res.data.chainType === 'ChainMaker') {
          this.languageList = [
            { enumKey: 'GO', enumValue: 'GO' },
            { enumKey: 'C++', enumValue: 'C++' },
            { enumKey: 'RUST', enumValue: 'RUST' },
            { enumKey: 'TINYGO', enumValue: 'TINYGO' },
            { enumKey: 'SOLIDITY', enumValue: 'SOLIDITY' }
          ]
        }

        this.fromdify.contractType = res.data.contractType
        this.fromdify.chainType = res.data.chainType// 链类型
        this.fromdify.languagetype = res.data.languageType// 语言类型
        this.fromdify.scene = res.data.scene
        this.fromdify.description = res.data.description
        this.modaldescribeData = res.data.contractTypeDescriptions
        this.changeModal = res.data.isSingleCpp !== '1' ? 'manycpp' : 'onecpp'
        if (this.fromdify.languagetype === 'C++') {
          this.accepttype = '.hpp,.cpp'
          this.bool = false// cpp文件是否为必传
        } else if (this.fromdify.languagetype === 'GO' || this.fromdify.languagetype === 'TINYGO') {
          this.accepttype = '.go'
          this.bool = true
        } else if (this.fromdify.languagetype === 'RUST') {
          this.accepttype = '.rs'
          this.bool = true
        } else if (this.fromdify.languagetype === 'SOLIDITY') {
          this.accepttype = '.sol'
          this.bool = true
        } else if (this.fromdify.languagetype === 'JS') {
          this.accepttype = '.js,.abi'
          this.bool = true
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
      TemplatePreview(this.$route.params.listIddisy).then(res => {
        let previewup = res.data.map(item => {
          return {
            name: item.fileName, fileid: item.id, size: item.fileSize, storePath: item.storePath
          }
        })
        this.tableData = previewup

        this.tableData.map(item => {
          if (item.name.indexOf('cpp') !== -1) {
            this.cppfile = item
          } else if (item.name.indexOf('go') !== -1) {
            this.goFile = item
          } else if (item.name.indexOf('rs') !== -1) {
            this.rsFile = item
          } else if (item.name.indexOf('sol') !== -1) {
            this.solFile = item
            // console.log(file)
          } else if (item.name.indexOf('hpp') !== -1) {
            this.hppfile.push(item)
            // console.log(file)
          } else if (item.name.indexOf('.js') !== -1) {
            this.jsFile = item
          } else if (item.name.indexOf('.abi') !== -1) {
            this.abiFile = item
          } else if (item.name.indexOf('.zip') !== -1 || item.name.indexOf('.tar') !== -1) {
            this.manyCpp = item

          }
        })
        // console.log(this.cppfile,this.goFile,this.rsFile,this.hppfile)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },

    // s6
    newmodel (newList, upList, type) {
      PreviewContractType(newList, upList, this.hppfile).then(res => {
        if (res.code === '00000') {
          setTimeout(() => {
            this.loadingStatus = false
            if (type == 1) {
              this.$router.push({
                name: 'template_table'
              })
              this.$Message.success('修改模板类型成功!')
            } else if (type == 2) {
              this.goide(res.data.contractModelId)
            }
          }, 3000)
        } else {
          setTimeout(() => {
            this.loadingStatus = false
            this.$Message.error(res.message)
          }, 1500)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    goide (id) {
      goIDE(id).then((res) => {
        // console.log(res.data.url)
        this.$router.push({
          name: 'template_table'
        })
        window.open(res.data.url, 'ide')
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    editbtn () {
      this.modal2 = true
    },
    editok () {
      this.handleSubmit('fromdify', 2)
    },
    editcancel () {

    }
  },
  mounted () {
    let name = 'LINE_IDE'
    getconfig(name).then((res) => {
      if (res.data) {
        this.ideShow = res.data.value == 1

      } else {
        this.ideShow = false
      }
    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })
    let nameIde = 'Z_LINE_IDE'
    getconfig(nameIde).then((res) => {
      if (res.data) {
        this.ideShowZ = res.data.value == 1
      } else {
        this.ideShowZ = false
      }
    }).catch((error) => {
      this.msgInfo('error', error.message, true)
    })
    if (!this.$route.params.listIddisy) {
      this.$router.push({
        name: 'template_table'
      })
    } else {
      this.details()
    }
    getTempateEos(this.selecteos).then(res => {
      this.cityList = res.data
    }).catch(error => {
      this.msgInfo('error', error.message, true)
    })
    // getTempateLanguage(this.selectlanguage).then(res => {
    //   this.languageList = res.data
    // })
  }
}
</script>

<style scoped lang="less">
// 接口描述 取消按钮
.newdescribe {
  margin-left: 38%;
}
.mandatory {
  /deep/.ivu-form-item-label::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}
</style>
