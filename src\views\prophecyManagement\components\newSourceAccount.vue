<!--
  at<PERSON><PERSON>
  新建信源账户
  2021/10/21

-->
<template>
  <el-dialog
      class="dialog_sty new_source-dialog"
      :title="title"
      :visible.sync="Visible"
      width="580px"
      :modal="true"
      destroy-on-close
      :close-on-click-modal="false"
      :before-close="handleClose"
      @opened="open">
    <div class="dialog_content" style="max-height: 500px;overflow-y: scroll">
      <div class="top_tabs">
        <div class="tab_item" :class="{'active':formOne.provider.providerType=='EOS'}" @click="setTabActive('EOS')">链</div>
        <div class="tab_item" :class="{'active':formOne.provider.providerType=='HTTP'}" @click="setTabActive('HTTP')">http</div>
        <div class="tab_item" :class="{'active':formOne.provider.providerType=='DB'}" @click="setTabActive('DB')">数据库</div>
      </div>
      <el-form :model="formOne" :rules="rules" ref="newSourceAccountref" label-width="120px">
        <el-form-item label="预言机模板：" prop="provider.tempList" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
          <el-select :disabled="isLook" ref="selectTempListRef" v-model="formOne.provider.tempList" placeholder="请选择" multiple @change="changeTempList">
            <el-option :label="item.tempName" :value="item.tempId" v-for="(item,index) in MachineTempList" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="信源名称："
          @keyup.native="btKeyUp"
          prop="provider.providerName"
          :rules="providerRules">
            <el-tooltip placement="top" v-if="operationState==2&&formOne.provider.providerName.length>12">
                <div slot="content" class="dsfdg" >{{formOne.provider.providerName}}</div>
                <el-input maxlength="60" :disabled="isLook" v-model="formOne.provider.providerName" placeholder="请输入">
                </el-input>
            </el-tooltip>
            <el-input show-word-limit maxlength="60" v-else :disabled="isLook" v-model="formOne.provider.providerName" placeholder="请输入"></el-input>
        </el-form-item>
         <!--        链-->
        <div class="link_class" v-if="formOne.provider.providerType=='EOS'">
          <el-form-item label="链类型：" prop="chain.chainType" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
            <el-select :disabled="isLook" v-model="formOne.chain.chainType" placeholder="请选择">
              <el-option label="EOS(CMBAAS内部跨链交互)" value="EOS"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="链地址：" prop="chain.chainUrl" :rules="[{ required: true, message: '请输入', trigger: 'change' }]">
              <el-tooltip placement="top" v-if="operationState==2&&formOne.chain.chainUrl.length>12">
                  <div slot="content" class="dsfdg" >{{formOne.chain.chainUrl}}</div>
                  <el-input maxlength="100" :disabled="isLook" v-model="formOne.chain.chainUrl" placeholder="例如：http://xxxxxx:xx"></el-input>
              </el-tooltip>
            <el-input show-word-limit maxlength="100" v-else :disabled="isLook" v-model="formOne.chain.chainUrl" placeholder="例如：http://xxxxxx:xx"></el-input>
          </el-form-item>
          <el-form-item label="链账号：" prop="chain.chainAccount" :rules="[{ required: true, message: '请输入', trigger: 'change' }]">
              <el-tooltip placement="top" v-if="operationState==2&&formOne.chain.chainAccount.length>12">
                  <div slot="content" class="dsfdg" >{{formOne.chain.chainAccount}}</div>
                  <el-input maxlength="60" :disabled="isLook" v-model="formOne.chain.chainAccount" placeholder="请输入"></el-input>
              </el-tooltip>
            <el-input show-word-limit maxlength="60" v-else :disabled="isLook" v-model="formOne.chain.chainAccount" placeholder="请输入"></el-input>
          </el-form-item>
<!--          <el-form-item label="链地址：" prop="chain.chainUrl" :rules="[{ required: true, message: '请选择', trigger: 'blur' }]">-->
<!--            <el-select :disabled="isLook" v-model="formOne.chain.chainUrl" placeholder="请选择">-->
<!--              <el-option :label="item.chainName" :value="item.nodeId" v-for="(item,index) in NodeList" :key="index"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="合约：" prop="chain.chainAccount" :rules="[{ required: true, message: '请选择', trigger: 'blur' }]" class="input-or-button">-->
<!--            <el-input :disabled="isLook" v-model="formOne.chain.chainAccount" placeholder="请选择" disabled></el-input>-->
<!--            <el-button type="primary" @click="getSecretKey" :disabled="isLook">选择合约</el-button>-->
<!--          </el-form-item>-->
        </div>
        <!--        http-->
        <div class="http_class" v-if="formOne.provider.providerType=='HTTP'">
          <el-form-item label="URL：" prop="http.httpUrl" :rules="[{ required: true, message: '请输入', trigger: 'change' }]" >
              <el-tooltip placement="top" v-if="operationState==2&&formOne.http.httpUrl.length>12">
                  <div slot="content" class="dsfdg" >{{formOne.http.httpUrl}}</div>
                  <el-input maxlength="100" :disabled="isLook" v-model="formOne.http.httpUrl" placeholder="例如：http://xxxxxx:xx"></el-input>
              </el-tooltip>
            <el-input show-word-limit maxlength="100" v-else :disabled="isLook" v-model="formOne.http.httpUrl" placeholder="例如：http://xxxxxx:xx"></el-input>
          </el-form-item>
          <el-form-item label="账号：" >
              <el-tooltip placement="top" v-if="operationState==2&&formOne.http.httpUser.length>12">
                  <div slot="content" class="dsfdg" >{{formOne.http.httpUser}}</div>
                  <el-input maxlength="60" :disabled="isLook" v-model="formOne.http.httpUser" placeholder="请输入"></el-input>
              </el-tooltip>
            <el-input show-word-limit maxlength="60" v-else :disabled="isLook" v-model="formOne.http.httpUser" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="密码："  class="password_look">
            <el-input show-word-limit show-password maxlength="26" :type="'password'" :disabled="isLook" v-model="formOne.http.httpPass" placeholder="请输入"></el-input>
<!--            <i class="el-icon-view" :class="{'active':httpPasswordShow}" @click="ViewPass()" v-if="operationState!=2"></i>-->
          </el-form-item>
          <el-form-item label="访问类型：" prop="http.httpType" :rules="[{ required: true, message: '请选择', trigger: 'change' }]" >
            <el-select :disabled="isLook" v-model="formOne.http.httpType" placeholder="请选择">
              <el-option label="POST" :value="0"></el-option>
              <el-option label="GET" :value="1"></el-option>
            </el-select>
          </el-form-item>
<!--          <el-form-item label="参数：" prop="http.parameter" :rules="[{ required: true, message: '请输入', trigger: 'blur' }]" >-->
<!--            <el-input maxlength="60" :disabled="isLook" v-model="formOne.http.parameter" placeholder="请输入"></el-input>-->
<!--          </el-form-item>-->
        </div>
        <!--        数据库-->
        <div class="mysql_class" v-if="formOne.provider.providerType=='DB'">
          <el-form-item label="数据库类型：" prop="database.dbType" :rules="[{ required: true, message: '请选择', trigger: 'change' }]" >
            <el-select :disabled="isLook" v-model="formOne.database.dbType" placeholder="请选择">
              <el-option label="mysql" value="mysql"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="链接URL：" prop="database.dbUrl" :rules="[{ required: true, message: '请输入', trigger: 'change' }]" >
              <el-tooltip placement="top" v-if="operationState==2&&formOne.database.dbUrl.length>12">
                  <div slot="content" class="dsfdg" >{{formOne.database.dbUrl}}</div>
                  <el-input maxlength="128" :disabled="isLook" v-model="formOne.database.dbUrl" placeholder="例如：*********************************"></el-input>
              </el-tooltip>
            <el-input show-word-limit maxlength="128" v-else :disabled="isLook" v-model="formOne.database.dbUrl" placeholder="例如：*********************************"></el-input>
          </el-form-item>
          <el-form-item label="数据库用户：" prop="database.dbUser" :rules="[{ required: true, message: '请输入', trigger: 'change' }]" >
              <el-tooltip placement="top" v-if="operationState==2&&formOne.database.dbUser.length>12">
                  <div slot="content" class="dsfdg" >{{formOne.database.dbUser}}</div>
                  <el-input maxlength="60" :disabled="isLook" v-model="formOne.database.dbUser" placeholder="请输入"></el-input>
              </el-tooltip>
            <el-input show-word-limit maxlength="60" :disabled="isLook" v-model="formOne.database.dbUser" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="数据库密码：" class="password_look" prop="database.dbPass" :rules="[{ required: true, message: '请输入', trigger: 'change' }]" >
            <el-input show-word-limit show-password maxlength="26" type="password" :disabled="isLook" v-model="formOne.database.dbPass" placeholder="请输入"></el-input>
<!--            <i class="el-icon-view" :class="{'active':dbPasswordShow}" @click="ViewPass()" v-if="operationState!=2"></i>-->
          </el-form-item>
          <el-form-item label="数据库sql：" prop="database.dbSql" :rules="[{ required: true, message: '请输入', trigger: 'change' }]" >
              <el-tooltip placement="top" v-if="operationState==2&&formOne.database.dbSql.length>12">
                  <div slot="content" class="dsfdg" >{{formOne.database.dbSql}}</div>
                  <el-input maxlength="100" :disabled="isLook" v-model="formOne.database.dbSql" placeholder="例如：select * from xx  xx by id"></el-input>
              </el-tooltip>
            <el-input show-word-limit maxlength="100" v-else :disabled="isLook" v-model="formOne.database.dbSql" placeholder="例如：select * from xx  xx by id"></el-input>
          </el-form-item>
          <el-form-item label="数据库驱动：" prop="database.dbDrive" :rules="[{ required: true, message: '请输入', trigger: 'change' }]" >
            <el-select :disabled="isLook" v-model="formOne.database.dbDrive" placeholder="请选择">
              <el-option label="com.mysql.jdbc.Driver" value="com.mysql.jdbc.Driver"></el-option>
              <el-option label="com.mysql.cj.jdbc.Driver" value="com.mysql.cj.jdbc.Driver"></el-option>
            </el-select>
<!--            <el-input maxlength="60" :disabled="isLook" v-model="formOne.database.dbDrive" placeholder="例如：com.mysql.cj.jdbc.Driver"></el-input>-->
          </el-form-item>

        </div>
<!--        公共-->
        <div class="public_item">
          <el-form-item label="取数模式："  prop="provider.fetchType" :rules="[{ required: true, message: '请选择', trigger: 'change' }]" >
            <el-select @change="fetchTypeChange" :disabled="isLook" v-model="formOne.provider.fetchType" placeholder="请选择">
              <el-option label="定时" :value="0"></el-option>
              <el-option label="请求" :value="1"></el-option>
            </el-select>
          </el-form-item>
          <div class="double_input" v-if="formOne.provider.fetchType==0">
            <el-form-item label="取数周期：" prop="provider.cycleTime" :rules="[{ required: true, message: '请输入', trigger: 'change' }]">
              <el-input-number step-strictly :step="1" :controls="false" :disabled="isLook" v-model="formOne.provider.cycleTime" placeholder="请输入" ></el-input-number>
            </el-form-item>
            <el-form-item class="double_select">
              <el-select :disabled="isLook" v-model="formOne.provider.cycleType" placeholder="请选择">
                <el-option label="天" value="3"></el-option>
                <el-option label="小时" value="2"></el-option>
                <el-option label="分钟" value="1"></el-option>
              </el-select>
            </el-form-item>
          </div>

          <el-form-item label="生效时间：" prop="provider.enableTime" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
            <el-date-picker
                v-model="formOne.provider.enableTime"
                type="datetime"
                :disabled="isLook"
                placeholder="年/月/日">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="失效时间：">
            <el-date-picker
                v-model="formOne.provider.stopTime"
                type="datetime"
                :disabled="isLook"
                placeholder="年/月/日">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="业务描述：">
            <el-input show-word-limit maxlength="200" :rows="4" :disabled="isLook" v-model="formOne.provider.memo" placeholder="请输入" type="textarea"></el-input>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="testRequest" :loading="upLoading" v-if="!isLook">测 试</el-button>
    <el-button @click="Refresh();Visible = false">{{operationState==2?'关 闭':'取 消'}}</el-button>
    <el-button type="primary" @click="submitForm" :loading="upLoading" v-if="!isLook">确 定</el-button>
  </span>
    <contractDialog :nodeId="formOne.chain.chainUrl" ref="contractRef" @setSecretKey="setSecretKey"></contractDialog>
  </el-dialog>

</template>

<script>
import contractDialog from './contractDialog'
import * as api from "../api";
import {getFormatDates} from '../../../utils/atuUtils.js'
import {base} from '../../../utils/base64crypto.js'
import {databaseReq, eosReq} from "../api";

export default {
  name: "newSourceAccount",
  components: {
    contractDialog
  },
  data() {
    return {
      editEnableTime:null,
      dbPasswordShow:false,
      httpPasswordShow:false,
      Visible:false,
      title:null,
      upLoading:false,
      providerRules:[
          {required: true, message: '请输入', trigger: 'blur' },
          {pattern: /[^:/\\?*"<>|;]/g, message: '不能输入 :/\\?*"<>|;'}
      ],
      formOne:{
        "provider": {
          fetchType:null,
          cycleMode:0,
          "createTime": null,
          "cycleTime": 1,
          "cycleType": "3",
          "enableTime": null,
          "memo": "",
          "nodeId": "",
          "oracleTemp": "",
          "providerId": "",
          "providerName": "",
          "providerStatus": 0,
          "providerType": "EOS",
          "serviceId": 0,
          "stopTime": null,
          "tempList": []
        },
        "chain": {
          "chainAccount": "",
          "chainType": "",
          "chainUrl": "",
          "createTime": null,
          "dataCode": "",
          "dataId": "",
          "dataMode": "",
          "memo": "",
          "providerId": "",
          "secretKey": "",
          "statusCode": "",
          "sussCode": ""
        },
        "database": {
          "createTime": "",
          "dataId": "",
          "dataMode": "",
          "dbDrive": "",
          "dbPass": "",
          "dbSql": "",
          "dbType": "",
          "dbUrl": "",
          "dbUser": "",
          "memo": "",
          "providerId": ""
        },
        "http": {
          "createTime": "",
          "dataCode": "",
          "dataId": "",
          "dataMode": "",
          "httpPass": "",
          "httpType": 0,
          "httpUrl": "",
          "httpUser": "",
          "memo": "",
          "parameter": "",
          "providerId": "",
          "statusCode": "",
          "sussCode": ""
        }
      },
      providerId:null,
      operationState:0, //0 1 2
      rules: {
        providerName:[
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
            ],
        chainType:[{ type: 'number', message: '必须为数字值'}],
        cycleTimeDay:[
          { min: 1, max: 10, message: '长度在 1 到 10 个字符', trigger: 'blur' }],
        cycleTimeHH:[
          { min: 1, max: 24, message: '长度在 1 到 24 个字符', trigger: 'blur' }],
        cycleTimeMM:[
          { min: 1, max: 59, message: '长度在 1 到 59 个字符', trigger: 'blur' }],
      },
      MachineTempList:[],
      NodeList:[{
        chainName:'测试地址1',
        nodeId:'http://192.168.0.251:9009'
      }],
      tabActive:'EOS',
      isLook:false

    }
  },
  watch: {
    'formOne.chain.chainType'(newVal){
      if(this.operationState==0){
        newVal?this.$message.success('需向信源管理员确认链账户名称'):null
      }

    },
    'formOne.provider.providerType'(){
      this.httpPasswordShow=false
      this.dbPasswordShow=false
    },
    'formOne.provider.fetchType'(val){
      if(this.operationState==1&&val==0){
        // this.formOne.provider.cycleType = '1'
        // this.formOne.provider.cycleTime = 1
      }
    },
      'formOne.provider.providerName'(val) {
        this.$nextTick(() => {
          this.formOne.provider.providerNam = this.filterInput(val)
        })
      }
  },
  created() {

  },
  methods: {
    changeTempList(){
      if(this.formOne.provider.tempList.length==this.MachineTempList.length){
        let select = this.$refs.selectTempListRef
        select.blur();
      }
    },
    fetchTypeChange(fetchType){
      // console.log(fetchType)
      if(fetchType=='0'){
        this.formOne.provider.cycleType = this.formOne.provider.cycleType||'1'
        this.formOne.provider.cycleTime = this.formOne.provider.cycleTime||1
      }
    },
    filterInput(val) {
      // 这里过滤的是除了英文字母和数字的其他字符
      return val.replace(/[^A-z0-9]/g, '')
    },
    btKeyUp(e) {
      e.target.value = e.target.value.replace(/[:/\\?*"<>|;]/g,"");
    },
    messageDialog(content,confirm,cancel){
      this.$confirm(content, '提示', {
        confirmButtonText: '确认',
        showClose:false,
        showCancelButton:false
      }).then(() => {

      }).catch(() => {

      });
    },
    ViewPass(){
      if(this.operationState==2) return
      if(this.formOne.provider.providerType=='HTTP'){
        this.httpPasswordShow=!this.httpPasswordShow
      }else if(this.formOne.provider.providerType=='DB'){
        this.dbPasswordShow=!this.dbPasswordShow
      }
    },
    testRequest(){
      if(this.formOne.provider.providerType=='EOS'){
        this.EOSTset()
      }else if(this.formOne.provider.providerType=='HTTP'){
        this.HTTPTest()
      }else if(this.formOne.provider.providerType=='DB'){
        this.DBTest()
      }
    },
    EOSTset(){
      api.eosReq(
        {
          "chainUrl": this.formOne.chain.chainUrl,
          "chainAccount": this.formOne.chain.chainAccount,
        }
      ).then(res=>{
        if(res.code!=0) return this.messageDialog('测试失败  '+ res.msg)
        this.messageDialog('测试成功')
      }).catch(err=>{
        this.messageDialog('测试失败  '+err)
      })
    },
    DBTest(){
      let testPassword = null
      testPassword=base.encode(this.formOne.database.dbPass)
      api.databaseReq(
        {
          "dbSql": this.formOne.database.dbSql,
          "dbType": this.formOne.database.dbType,
          "dbUrl": this.formOne.database.dbUrl,
          "dbUser": this.formOne.database.dbUser,
          "dbPass": testPassword,
          "dbDrive": this.formOne.database.dbDrive
        }
      ).then(res=>{
        if(res.code!=0) return this.messageDialog('测试失败  '+ res.msg)
        this.messageDialog('测试成功')
      }).catch(err=>{
        this.messageDialog('测试失败  '+err)
      })
    },
    HTTPTest(){
      api.httpReq(
          {
            "url": this.formOne.http.httpUrl,
            "param": {},
            "reqType": this.formOne.http.httpType
          }
      ).then(res=>{
        if(res.code!=0) return this.messageDialog('测试失败  '+ res.msg)
        this.messageDialog('测试成功')
      }).catch(err=>{
        this.messageDialog('测试失败  '+err)
      })
    },
    setTabActive(val){
      if(this.operationState!=0) return
      this.$refs.newSourceAccountref.clearValidate()
      this.formOne.provider.providerType=val
    },
    tabClick(tab, event){
      console.log(tab, event);
    },
   getSecretKey(){
      if(this.formOne.chain.chainUrl){
        this.$refs.contractRef.Visible=true
      }else {
        this.$message.error('请先选择链地址!')
      }
    },
    setSecretKey(val){
     this.formOne.chain.chainAccount=val
    },
    open(){
      this.Refresh()
      this.getMachineTempList()
      if(this.formOne.provider.providerType=='EOS'){
        this.getNodeList()
      }
      if(this.operationState==0){
        this.title='新建信源'
        this.isLook = false
        this.formOne.provider.cycleType = '1'
        this.formOne.provider.cycleTime = 1
      }else if(this.operationState==1){
        this.title='编辑信源'

        this.isLook = false
        this.getOracleProviderDetails()
      }else {
        this.title='信源管理'
        this.isLook = true
        this.getOracleProviderDetails()
      }
    },
    //获取数据用户列表
    getMachineTempList(){
      api.getMachineTempList(
          {
            "filter": {},
            "order": "",
            "page": 0,
            "rows": 0,
            "sort": "",
            "tempName": ""
          }
      ).then(res=>{
        this.MachineTempList=res.result.rows
      })
    },
    Refresh(){
      this.editEnableTime = null
      this.formOne={
        "provider": {
          fetchType:null,
          cycleMode:0,
          "createTime": null,
          "cycleTime": 1,
          "cycleType": "3",
          "enableTime": null,
          "memo": "",
          "nodeId": "",
          "oracleTemp": "",
          "providerId": "",
          "providerName": "",
          "providerStatus": 0,
          "providerType": "EOS",
          "serviceId": 0,
          "stopTime": null,
          "tempList": []
        },
        "chain": {
          "chainAccount": "",
          "chainType": "",
          "chainUrl": "",
          "createTime": null,
          "dataCode": "",
          "dataId": "",
          "dataMode": "",
          "memo": "",
          "providerId": "",
          "secretKey": "",
          "statusCode": "",
          "sussCode": ""
        },
        "database": {
          "createTime": "",
          "dataId": "",
          "dataMode": "",
          "dbDrive": "",
          "dbPass": "",
          "dbSql": "",
          "dbType": "",
          "dbUrl": "",
          "dbUser": "",
          "memo": "",
          "providerId": ""
        },
        "http": {
          "createTime": "",
          "dataCode": "",
          "dataId": "",
          "dataMode": "",
          "httpPass": "",
          "httpType": 0,
          "httpUrl": "",
          "httpUser": "",
          "memo": "",
          "parameter": "",
          "providerId": "",
          "statusCode": "",
          "sussCode": ""
        }
      }
    },
    //获取列地址列表
    getNodeList(){
      // api.getNodeList(
      //     {
      //       contractName:'',
      //       nodId:'EOS'
      //     }
      // ).then(res=>{
      //   this.NodeList=res.result
      // })
    },
    getOracleProviderDetails(){
      api.getOracleProviderDetails(this.providerId).then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.formOne = res.result
          if(this.formOne.provider.providerType=='HTTP'){
              this.formOne.http.httpPass=base.decode(this.formOne.http.httpPass)
          }else if(this.formOne.provider.providerType=='DB'){
              this.formOne.database.dbPass=base.decode(this.formOne.database.dbPass)
          }
        this.editEnableTime = this.formOne.provider.enableTime
      })
    },
    submitForm() {
      this.$refs['newSourceAccountref'].validate((valid) => {
        if (valid) {
          if(this.operationState==0){
            this.addDataConsumer()
          }else if(this.operationState==1){
            this.updateOracleProvider()
          }else {
            this.Visible=false
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    updateOracleProvider(){
      let creartTime = this.formOne.provider.enableTime
      let endTime = this.formOne.provider.stopTime
      // debugger
      if(creartTime){
        if(Date.parse(creartTime)!=Date.parse(this.editEnableTime)){
          if(Date.parse(creartTime)<=Date.parse(new Date())-80000){
            return this.$message.error('生效时间不能小于等于当前时间,请重新选择')
          }
        }
        this.formOne.provider.enableTime = getFormatDates(creartTime,'yyyy-mm-dd MM:mm:ss')
      }
      if(endTime){
        if(Date.parse(creartTime)>=Date.parse(endTime)){
          return this.$message.error('失效时间不能小于等于生效时间,请重新选择')
        }
        this.formOne.provider.stopTime = getFormatDates(endTime,'yyyy-mm-dd MM:mm:ss')
      }
        if(this.formOne.provider.providerType=='HTTP'){
            this.formOne.http.httpPass=base.encode(this.formOne.http.httpPass)
        }else if(this.formOne.provider.providerType=='DB'){
            this.formOne.database.dbPass=base.encode(this.formOne.database.dbPass)
        }
      if(this.upLoading) return
      this.upLoading = true
      api.updateOracleProvider(this.formOne).then(res=>{
        this.upLoading = false
        if(res.code!=0) {
          if(this.formOne.provider.providerType=='HTTP'){
            this.formOne.http.httpPass=base.decode(this.formOne.http.httpPass)
          }else if(this.formOne.provider.providerType=='DB'){
            this.formOne.database.dbPass=base.decode(this.formOne.database.dbPass)
          }
          this.$message.warning(res.msg)
        }else {
          this.$message.success(res.msg)
          this.Visible=false
          this.Refresh()
          this.$emit('Refresh')
        }

      }).catch(err=>{
        this.upLoading = false
      })
    },
    addDataConsumer(){
      let creartTime = this.formOne.provider.enableTime
      let endTime = this.formOne.provider.stopTime
      // console.log(Date.parse(creartTime),Date.parse(endTime))
      // debugger
      if(creartTime){
        if(Date.parse(creartTime)<=Date.parse(new Date())-80000){
          return this.$message.error('生效时间不能小于等于当前时间,请重新选择')
        }
        this.formOne.provider.enableTime = getFormatDates(creartTime,'yyyy-mm-dd MM:mm:ss')
      }
      if(endTime){
        if(Date.parse(creartTime)>=Date.parse(endTime)){
          return this.$message.error('失效时间不能小于等于生效时间,请重新选择')
        }
        this.formOne.provider.stopTime = getFormatDates(endTime,'yyyy-mm-dd MM:mm:ss')
      }
      if(this.formOne.provider.providerType=='HTTP'){
        this.formOne.http.httpPass=base.encode(this.formOne.http.httpPass)
      }else if(this.formOne.provider.providerType=='DB'){
        this.formOne.database.dbPass=base.encode(this.formOne.database.dbPass)
      }
      if(this.upLoading) return
      this.upLoading = true
      api.addProvider(this.formOne).then(res=>{
        this.upLoading = false
        if(res.code!=0){
          if(this.formOne.provider.providerType=='HTTP'){
            this.formOne.http.httpPass=base.decode(this.formOne.http.httpPass)
          }else if(this.formOne.provider.providerType=='DB'){
            this.formOne.database.dbPass=base.decode(this.formOne.database.dbPass)
          }
          this.$message.warning(res.msg)
        } else {
          this.$message.success(res.msg)
          this.Visible=false
          this.Refresh()
          this.$emit('Refresh')
        }

      }).catch(err=>{
        this.upLoading = false
        this.messageDialog('信源注册失败')
      })
      // this.$confirm('信源一经建立，不可修改', '提示', {
      //   confirmButtonText: '新建',
      //   cancelButtonText: '取消',
      //   showClose:false
      // }).then(() => {
      //   if(this.upLoading) return
      //   this.upLoading = true
      //   api.addProvider(this.formOne).then(res=>{
      //     this.upLoading = false
      //     if(res.code!=0){
      //       if(this.formOne.provider.providerType=='HTTP'){
      //         this.formOne.http.httpPass=base.decode(this.formOne.http.httpPass)
      //       }else if(this.formOne.provider.providerType=='DB'){
      //         this.formOne.database.dbPass=base.decode(this.formOne.database.dbPass)
      //       }
      //       this.$message.warning(res.msg)
      //     } else {
      //       this.$message.success(res.msg)
      //       this.Visible=false
      //       this.Refresh()
      //       this.$emit('Refresh')
      //     }
      //
      //   }).catch(err=>{
      //     this.upLoading = false
      //     this.messageDialog('信源注册失败')
      //   })
      // }).catch(() => {
      //   this.upLoading = false
      // });

    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleClose(done) {
      this.Refresh()
      done();

    }
  },

}
</script>

<style lang="less" scoped>
.new_source-dialog{

}
</style>
