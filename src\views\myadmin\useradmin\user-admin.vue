<template>
  <div class="useradmin">
    <div style="display:flex;justify-content: space-between;">
      <p>
        <Select v-model="tenantId" placeholder="请选择租户" style="width: 200px; margin-right: 10px">
          <Option v-for="item in userListData" :value="item.tenantId" :key="item.tenantId">{{ item.tenantName }}</Option>
        </Select>
        <Select v-model="zoneType" placeholder="请选择业务领域" style="width: 200px; margin-right: 10px" filterable>
          <Option v-for="(item,index) in userchildList" :value="item.value" :key="index">{{ item.label }}</Option>
        </Select>
        <Select v-model="roleValue" placeholder="请选择角色" style="width: 200px; margin-right: 10px" filterable>
          <Option v-for="(item,index) in roleList" :value="item.id" :key="index">{{ item.roleName }}</Option>
        </Select>
        <Input style="width: 200px; vertical-align: baseline" placeholder="可输入账号查询信息" v-model="inputvalue" />
        <Button type="primary" @click="searchList" icon="ios-search" style="margin: 0 10px">查询</Button>
        <Button type="primary" @click="reset" icon="md-sync" ghost>重置</Button>
      </p>

      <p>
        <!-- <span class="zx_user" v-show="userSource">

        </span> -->
        <Button v-show="isAdmin" type="success" @click="onlineUser" ghost>在线用户数</Button>
        <Button v-show="userSource" type="success" @click="createTenant" icon="md-add" :disabled="hasEditPermission" ghost style="margin-left: 10px; float: right">新增用户</Button>
      </p>

    </div>
    <edit-table-mul style="margin: 10px 0" :columns="columns" v-model="tableData" :key="transferKey"></edit-table-mul>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10, 20, 40, 60, 100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align: right" />
    <!-- 新建用户弹框 -->
    <Modal v-model="modal1" :title="arrTenant.alertTitle" :mask-closable='false'>
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate">
        <FormItem prop="platform">
          <Input v-model="formValidate.platform" placeholder="账号" prefix="ios-contact-outline"></Input>
        </FormItem>

        <FormItem prop="username">
          <Input v-model="formValidate.username" placeholder="真实姓名" prefix="ios-person-outline"></Input>
        </FormItem>
        <FormItem prop="name">
          <Input v-model="formValidate.name" placeholder="所属组织" maxlength="32" show-word-limit prefix="ios-link"></Input>
        </FormItem>
        <FormItem prop="phone">
          <Input v-model="formValidate.phone" placeholder="手机号码" prefix="ios-call-outline"></Input>
        </FormItem>
        <FormItem prop="mail">
          <Input v-model="formValidate.mail" placeholder="邮箱地址" prefix="ios-mail-outline"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="cancel">取消</Button>
        <Button type="primary" @click="ok">提交</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  getUserTableData,
  getUserData,
  adduser,
  checkEmail,
  checkPhone,
  checkUserLoginId,
  searchRoleList
} from '@/api/data'
import {
  getUserChildList
} from '@/api/contract'
import EditTableMul from '_c/edit-table-mul'
import {
  isPhoneNumber,
  isEmail,
  isLoginId,
  isRealName
} from '../../../lib/check'
import { localRead } from '@/lib/util'
export default {
  name: 'user_admin',
  components: {
    EditTableMul
  },

  data () {
    // 验证手机号
    const validatePhone = (rule, value, callback) => {
      if (!isPhoneNumber(value)) {
        callback(new Error('手机号格式不支持'))
      } else {
        checkPhone(value)
          .then((res) => {
            if (res.code === '00000') {
              callback()
            } else {
              callback(new Error(res.message))
            }
          })
          .catch((error) => {
            callback(new Error(error.message))
          })
      }
    }
    const validateEmail = (rule, value, callback) => {
      if (!isEmail(value)) {
        callback(new Error('邮箱格式不正确'))
      } else {
        checkEmail(value)
          .then((res) => {
            if (res.code === '00000') {
              callback()
            } else {
              callback(new Error(res.message))
            }
          })
          .catch((error) => {
            // this.msgInfo('error', error.message, true)
            callback(new Error(error.message))
          })
      }
    }
    const validateUserLoginId = (rule, value, callback) => {
      const regex = /^[a-zA-Z0-9@._]+$/;
      if (!/^[a-zA-Z0-9@._]+$/.test(value)) {
        callback(new Error('请输入英文、数字、下划线、@、.'))
      } else {
        checkUserLoginId(value)
          .then((res) => {
            if (res.code === '00000') {
              callback()
            } else {
              callback(new Error(res.message))
            }
          })
          .catch((error) => {
            // this.msgInfo('error', error.message, true)
            callback(new Error(error.message))
          })
      }
    }
    const validateRealName = (rule, value, callback) => {
      if (!isRealName(value)) {
        callback(new Error('只允许中英文、数字， 且不能为纯数字'))
      } else {
        callback()
      }
    }
    return {
      roleValue:'',
      roleList:[],
      modal1: false, // 弹框
      arrTenant: {
        alertTitle: '新增用户'
      }, // 添加
      transferKey: 0,
      inputvalue: '',
      tenantId: '',
      userListData: [],
      tablePageParam: { pagetotal: 0, pageSize: 10, pageIndex: 1 },
      columns: [
        { key: 'userLoginId', title: '账号', tooltip: true },
        { key: 'realName', title: '真实姓名', tooltip: true },
        { key: 'email', title: '邮箱地址', tooltip: true },
        { key: 'phoneNumber', title: '手机号码', tooltip: true },
        { key: 'zoneType', title: '业务领域', tooltip: true },
        { key: 'organization', title: '所属组织', tooltip: true },
        { key: 'tenantName', title: '所属租户', tooltip: true },
        { key: 'roleName', title: '角色', tooltip: true },
        { key: 'status', title: '账号状态', tooltip: true },
        {
          key: 'action',
          title: '操作日志',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: { type: 'text', size: 'small' },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.searchLog(params.index)
                    }
                  }
                },
                '查看'
              )
            ])
          }
        }
      ],
      tableData: [],
      formValidate: {
        name: '', // 用户名
        mail: '', // 邮箱
        phone: '', // 电话
        username: '', // 真实用户名
        platform: '' // 平台用户名
      },
      ruleValidate: {
        name: [
          // {
          //   required: true,
          //   message: "所属组织",
          //   trigger: "blur",
          // },
        ],
        username: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            required: true,
            max: 15,
            message: '长度不能超过15',
            trigger: 'blur'
          },
          { required: true, trigger: 'blur', validator: validateRealName }
        ],
        platform: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            required: true,
            max: 15,
            message: '长度不能超过15',
            trigger: 'blur'
          },
          { required: true, trigger: 'blur', validator: validateUserLoginId }
        ],
        mail: [{ required: true, trigger: 'blur', validator: validateEmail }],
        phone: [
          {
            required: true,
            pattern: /^[0-9]{11}$/,
            message: '手机号应为11位数字',
            trigger: 'blur'
          },
          { required: true, trigger: 'blur', validator: validatePhone }
        ],

      },
      userPermission: JSON.parse(localRead('userPermission')),
      userchildList: [
        {
          value: 'ALL',
          label: '全部'
        },
        {
          value: 'CMBAAS_ZONE_DATA',
          label: '数据概览分区'
        },
        {
          value: 'CMBAAS_ZONE_BROWSER',
          label: '浏览器分区'
        },
        {
          value: 'CMBAAS_ZONE_ACCOUNT',
          label: '链账户分区'
        },
        {
          value: 'CMBAAS_ZONE_CONTRACT',
          label: '智能合约分区'
        },
        {
          value: 'CMBAAS_ZONE_CHAIN',
          label: '链管理分区'
        },
        {
          value: 'CMBAAS_ZONE_SYSTEM',
          label: '系统管理分区'
        },
        {
          value: 'CMBAAS_ZONE_BUSINESS',
          label: '业务审批分区'
        },
        {
          value: 'CMBAAS_ZONE_LOG',
          label: '日志管理分区'
        },
        {
          value: 'CMBAAS_ZONE_SERVER',
          label: '服务中心分区'
        },
        {
          value: 'JSC_ZONE_RUN',
          label: '运行调度分区'
        },
        {
          value: 'YY_ZONE_RUN',
          label: '领域运营分区'
        },
        {
          value: 'YW_ZONE_RUN',
          label: '监控运维分区'
        }
      ],
      zoneType: 'ALL'
    }
  },
  computed: {
    userSource () {
      if (localRead('userSource') == 1) {
        return false
      } else {
        return true
      }
    },
    isAdmin () {
      if (localStorage.getItem('roleId') == 1) {
        return true
      } else {
        return false
      }
    },
    hasEditPermission () {
      console.log(this.userPermission);

      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
  methods: {
    onlineUser () {
      this.$router.push({
        name: 'user_statistics'
      })
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData(this.inputvalue)
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData(this.inputvalue)
    },
    getTableData (inputvalue) {
      // console.log('this.tenantId:', this.tenantId)
      let data = {
        userLoginId: inputvalue,
        tenantId: this.tenantId,
        pageParam: this.tablePageParam,
        zoneType: this.zoneType=='ALL'?'':this.zoneType,
        roleId:this.roleValue
      }
      getUserChildList(data)
        .then((res) => {
          // console.log('getUserTableData===>', res)
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {
            this.tableData = res.data.records
            this.tablePageParam = {
              pagetotal: res.data.total,
              pageSize: res.data.size,
              pageIndex: res.data.current
            }
            ++this.transferKey
          }
        })
        .catch((error) => {
          // console.log('getChainTableData.error===>', error)
          this.msgInfo('error', error.message, true)
        })
    },
    searchList () {
      this.getTableData(this.inputvalue)
    },
    searchLog (index) {
      this.$router.push({
        name: 'user_log',
        params: {
          userLoginId: `${this.tableData[index].userLoginId}`
        }
      })
    },
    getTelnantData () {
      getUserData(0)
        .then((res) => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {
            this.userListData = res.data ? res.data : []
            // console.log('this.userListData:', this.userListData)
            if (this.userListData.length === 1) {
              this.tenantId = this.userListData[0].tenantId
            }
          }
        })
        .catch((error) => {
          // console.log('getChainTableData.error===>', error)
          this.msgInfo('error', error.message, true)
        })
    },
    reset () {
      this.inputvalue = ''
      this.tenantId = ''
      this.zoneType = 'ALL'
      this.roleValue = ''
      this.getTableData(this.inputvalue)
    },
    // 添加的代码
    createTenant () {
      this.init()
      this.modal1 = true
    },
    // 确认回调
    ok () {
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          const adduserdata = {
            realName: this.formValidate.username, // 用户名
            phoneNumber: this.formValidate.phone, // 手机号
            email: this.formValidate.mail, // 邮箱
            organization: this.formValidate.name, // 组织名
            userLoginId: this.formValidate.platform // 平台用户名
          }
          adduser(adduserdata)
            .then((res) => {
              if (res.code !== '00000') {
                if (res.code === '500') {
                  this.msgInfo('error', res.message, true)
                } else {
                  this.msgInfo('warning', res.message, true)
                }
              } else {
                this.msgInfo('success', res.message, true)
                this.modal1 = false
                this.getTableData()
              }
            })
            .catch((error) => {
              this.msgInfo('error', error.message, true)
            })
        } else {
          this.msgInfo('error', '内容不符合规范，请检查', true)
        }
      })
    },
    // 取消回调
    cancel () {
      this.modal1 = false
    },
    init () {
      this.$nextTick(() => {
        this.$refs['formValidate'].resetFields()
      })
      this.arrTenant = {
        alertTitle: '新增用户'
      }
    },
  },
  // watch: {
  //   tableData: {
  //     handler (newVal) {
  //       //
  //     },
  //     deep: true,
  //     immediate: false
  //   }
  // },
  mounted () {
    this.getTelnantData()
    this.getTableData()
    let obj = {
      "roleName": ""
    }
    searchRoleList(obj).then((res) => {
      this.roleList=res.data
    })
  }
}
</script>
<style lang="less" scoped>
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
.zx_user {
  color: #19be6b;
  border: 1px solid;
  height: 33px;
  display: inline-block;
  font-size: 14px;
  line-height: 33px;
  border-radius: 4px;
  padding: 0 15px;
}
</style>
