<template>
  <div class="overview_page">
    <SelectChain @getShowItem='getShowItem'></SelectChain>
    <Overview v-if="index == 0"></Overview>
    <ChannelMgr v-if="index == 1"></ChannelMgr>
    <Agreement v-if="index == 2"></Agreement>
    <Tissue v-if="index == 3"></Tissue>
    <SafetyCheck v-if="index == 4"></SafetyCheck>
    <!-- <AppMgr v-if="index == 4"></AppMgr> -->
    <!-- <SafetyCheck v-if="index == 5"></SafetyCheck> -->
  </div>
</template>

<script>
import SelectChain from '../compontents/selectChain'
import Overview from './overview'
import ChannelMgr from '../channelMgr/index' //通道管理
import Agreement from '../agreement/index' //合约管理
import Tissue from '../tissue/index' //组织节点
import SafetyCheck from '../safetyCheck/SafetyCheck' //组织节点
export default {
  // 组件部分
  components: {
    SelectChain,
    Overview,
    ChannelMgr,
    Agreement,
    Tissue,
    SafetyCheck
  },
  data() {
    return {
      index:0,
    };
  },
  mounted() {
    // var list = JSON.parse(sessionStorage.getItem('breadcrumbList'))
    // if(list.length > 2) {
    //   list = list.slice(0,list.length-1)
    //   this.$store.dispatch("getBreadcrumbList", list)
    // }
  },
  methods: {
    getShowItem(index) {
      this.index = index
    }
  }
};
</script>
<style rel="stylesheet/less" lang="less" scoped>
</style>
