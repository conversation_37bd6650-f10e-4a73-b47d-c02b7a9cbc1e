import { routeHasExist, getRouteById, routeEqual, localSave, localRead } from '@/lib/util'

const state = {
  tabList: JSON.parse(localRead('tabList') || '[]'),
  orderId: '' // 详情列表
}

const getTabListToLocal = tabList => {
  return tabList.map(item => {
    return {
      name: item.name,
      path: item.path,
      meta: item.meta,
      params: item.params,
      query: item.query
    }
  })
}

const mutations = {
  UPDATE_ROUTER (state, route) {
    var routeNameList = [ 'login', 'register', 'chain_details', 'tenant_details', 'contract_details', 'result_page', 'new_user', 'forget_pwd', 'reset_pwd', 'multilink_details', 'browser_block', 'browser_trade', 'browser_chain', 'user_log', 'step', 'exChain', 'prChain', 'cusChain', 'cusPrChain', 'overview', 'chainMaker_block', 'chainMaker_trade', 'contract_area', 'tem_modify', 'contract_shelves' ]
    if (!routeHasExist(state.tabList, route) && !routeNameList.includes(route.name) && route.name.indexOf('detail') === -1 && route.name.indexOf('new') === -1) {
      state.tabList.push(route)
    }
    localSave('tabList', JSON.stringify(getTabListToLocal(state.tabList)))
  },
  REMOVE_TAB (state, index) {
    state.tabList.splice(index, 1)
    localSave('tabList', JSON.stringify(getTabListToLocal(state.tabList)))
  },
  SET_ORDERID (state, params) {
    state.orderId = params
  },
  REMOVE_ALLTAB (state) {
    state.tabList.splice(0, state.tabList.length)
    localStorage.setItem('tabList', JSON.stringify(getTabListToLocal(state.tabList)))
  },
  REMOVE_OTHER (state, params) {
    state.tabList = state.tabList.filter(item => {
      return item.name === params || item.name === 'dashboard'
    })
    localStorage.setItem('tabList', JSON.stringify(getTabListToLocal(state.tabList)))
  }
}

const actions = {
  updateOrderId ({ commit, state }, value) {
    commit('SET_ORDERID', value)
  },
  handleRemove ({ commit }, { id, $route }) {
    return new Promise((resolve) => {
      let route = getRouteById(id)
      let index = state.tabList.findIndex(item => {
        return routeEqual(route, item)
      })
      let len = state.tabList.length
      let nextRoute = {}
      if (index !== -1) {
        if (routeEqual($route, state.tabList[index])) {
          if (index < len - 1) nextRoute = state.tabList[index + 1]
          else nextRoute = state.tabList[index - 1]
        }
      } else {
        nextRoute = {}
      }
      const { name, params, query } = nextRoute || { name: 'dashboard' }
      commit('REMOVE_TAB', index)
      resolve({
        name, params, query
      })
    })
  }
}

export default {
  state,
  mutations,
  actions
}
