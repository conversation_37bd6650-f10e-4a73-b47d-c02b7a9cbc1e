<template>
  <div class="contract">
    <p style="text-align:right;">
      <!-- <b style="float:left;margin: 10px;">智能合约</b> -->
      <Input style="width:280px;vertical-align:baseline;" placeholder="可输入合约名称或应用名称或所属租户查询" v-model="inputvalue" @keyup.enter="searchList" @keyup.enter.native="searchList">
      <Icon type="ios-search" slot="suffix" @click="searchList" />
      </Input>
      <!-- <Button type="primary" @click="searchList" icon="ios-search">查询</Button> -->
      <Button style="margin-left:5px;" icon="md-add" @click="create" :disabled="hasEditPermission" type="success" ghost>新建合约</Button>
    </p>
    <edit-table-mul style="margin: 10px 0;" :key="transferKey" :columns="columns" v-model="tableData"></edit-table-mul>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" placement='top' @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;" />
    <Modal v-model="modal" :title="arrContract.alertTitle" :draggable="true" sticky @on-cancel="cancel" :mask-closable="false" :width="800">
      <div class="result-class" v-if="isTip">
        <!-- <p><Icon :type="tipInfo.icon" size="36" :color="tipInfo.iconColor" /></p> -->
        <p><img :src="tipInfo.imgUrl" alt=""></p>
        <p class="result-title">{{tipInfo.title}}</p>
        <p class="result-content">{{tipInfo.content}}</p>
      </div>
      <div v-else>
        <Form ref="formItem" :rules="formItemRule" :model="arrContract" :label-width="140">
          <!-- 隐藏合约名称字段 -->
          <!-- <FormItem label="合约名称：" prop="contractName"> -->
          <!-- <Tooltip max-width="200" content="合约名称须与合约代码类名一致（或与通过c++ attribute 指定名称一致）长度范围支持5-32位，支持英文和数字，特殊符号只能有英文句号.和英文减号-" style="margin-left: -18px;">
          <Icon type="md-help-circle" style="font-size:16px;" />
        </Tooltip> -->
          <!-- <Input v-bind:disabled="arrContract.titleDisabled" v-model="arrContract.contractName" placeholder="填写合约名称" style="width:520px" />
            <p>合约名称须与“合约代码类名”一致。<a @click="showEg">查看示例></a></p>
          </FormItem> -->
          <FormItem label="应用名称：" prop="contractReadableName">
            <!-- <Tooltip max-width="200" content="中文显示名称不超过15个字" style="margin-left: -18px;">
          <Icon type="md-help-circle" style="font-size:16px;" />
        </Tooltip> -->
            <!-- <Input v-bind:disabled="arrContract.titleDisabled" v-model="arrContract.contractReadableName" placeholder="填写中文简称" style="width:520px" /> -->
            <p>{{arrContract.contractReadableName}}</p>
            <!-- <p>用于平台内展示，如不填写则自动配置为“用户名-编号”格式</p> -->
          </FormItem>
          <FormItem label="项目名称：" prop="projectName" class="mandatory">
            <p>{{arrContract.projectName==='null'?"":arrContract.projectName}}</p>
            <!-- <Input v-bind:disabled="arrContract.titleDisabled" v-model="arrContract.projectName" placeholder="项目简称" style="width:520px" /> -->

          </FormItem>
          <FormItem label="合约语言：" prop="" class="mandatory">
            <p>{{arrContract.languageType==='JS'?'Java Script':arrContract.languageType}}</p>
          </FormItem>
          <FormItem label="应用简介：" prop="brief">
            <Input v-model="arrContract.brief" type="textarea" :maxlength="500" show-word-limit :autosize="{minRows: 3,maxRows: 5}" style="width:540px" placeholder="如：解决交通信息数据一致性问题" />
            <!-- <Input v-model="arrContract.brief" type="textarea" style="width:520px" :maxlength="50" show-word-limit :autosize="{minRows: 3,maxRows: 5}" placeholder="如：解决交通信息数据一致性问题" /> -->
          </FormItem>

        </Form>
      </div>
      <div slot="footer" v-if="isTip">
        <Button type="text" @click="tipCancel(tipInfo.status)">{{tipInfo.cancelText}}</Button>
        <Button type="primary" @click="tipOk(tipInfo.status)">{{tipInfo.confirmText}}</Button>
      </div>
      <div slot="footer" v-else>
        <Button type="text" @click="cancel('formItem')">取消</Button>
        <Button type="primary" @click="ok('formItem')">确定</Button>
      </div>

      <!--  -->
    </Modal>
    <Modal v-model="modalEg" title="合约命名示例" :footer-hide="true" @on-cancel="modalEg=false" class="eg-class">
      <p>智能合约命名规则：合约名称须与合约代码类名一致。</p>
      <p style="font-weight: bold">示例</p>
      <p>合约代码类名为：<a>hello</a>，则合约名称应为：<a>hello</a></p>
      <p style="height:auto;margin-top:10px">
        <img style="width:100%" :src="egImg" alt="">
      </p>
    </Modal>
    <Modal v-model="modalShare" :title="shareItem.title" width="700" @on-cancel="modalShare=false" class="eg-class">
      <Form ref="shareItem" :rules="shareItemRulers" :model="shareItem">
        <FormItem label="选择要共享的合约链账户" prop="contractAccountId">
          <Select v-model="shareItem.contractAccountId" placeholder="请选择" style="width:100%;" label-in-value @on-change="getTenantIdList">
            <Option v-for="item in contractList" :tag="JSON.stringify(item)" :value="`${item.contractAccountId}`" :key="item.contractAccountId">{{item.contractAccountName}}（合约版本：{{item.contractVersion}}，链名称： {{item.chainName}}）</Option>
          </Select>
        </FormItem>
        <SearchTablePage :flag="modalShare" :contractAccountId="shareItem.contractAccountId" :transProps="transProps" @getSelectionData="getSelectionDataFn" />
        <!-- </p> -->
        <!-- <div style="padding: 10px 0 20px 0">
          <span>选择要共享的租户</span>
          <Input placeholder="请输入租户名称" class="input-search" style="width:250px;margin-left:305px;vertical-align:middle;" v-model="noTenantName" @keyup.enter.native="searchNoTenantName">
            <Icon type="ios-search" slot="suffix" @click="searchNoTenantName"/>
          </Input>
        </div>
        <div style="width:100%;">

          <Table
            :columns="selectColums"
            :data="notSelectTableData"
            stripe
            ref="selection"
            :show-header="true"
            @on-select="onSelect"
            @on-select-all="onSelectAll"
            @on-select-cancel="onSelectCancel"
            @on-select-all-cancel="onSelectAllCancel" max-height="300"></Table>
          <Page
            :total="noSelectPageParam.pagetotal"
            :current.sync="noSelectPageParam.pageIndex"
            @on-change="noSelectPageChange"
            :page-size="noSelectPageParam.pageSize"
            :page-size-opts="[5,10,20,40,60,100]"
            show-total show-elevator show-sizer
            @on-page-size-change="noSelectPageSizeChange"
            style="text-align:right;padding-top:15px;"/>
        </div> -->
      </Form>
      <div slot="footer">
        <div v-show="selectRecords.length > 0" style="padding:8px 0 10px 10px;float:left;">
          <span>选择了{{selectRecords.length}}个租户</span>
        </div>
        <Button @click="cancelShare('shareItem')">取消</Button>
        <Button type="primary" @click="okShare('shareItem')">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { getContractPower, getContractTableData, unshareChainAccountList, sharingContract } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import SearchTablePage from '_c/search-table-page'
// import { isContractName } from '../../../lib/check'  // 隐藏合约名称字段
import successImg from '@/assets/img/success.png'
import failImg from '@/assets/img/fail.png'
import egImg from '@/assets/img/eg.png'
import { localRead } from '@/lib/util'
export default {
  name: 'contract_table',
  components: {
    EditTableMul,
    SearchTablePage
  },
  data () {
    // 隐藏合约名称字段
    // const validateContractName = (rule, value, callback) => {
    //   let reg = /^[_a-zA-Z]/
    //   if (!reg.test(value.slice(0, 1))) {
    //     callback(new Error('只能以英文及下划线开头'))
    //   }
    //   if (!isContractName(value)) {
    //     callback(new Error('支持英文和数字，特殊符号只能有英文句号.和英文_'))
    //   } else {
    //     callback()
    //   }
    // }
    const validateContractReadableName = (rule, value, callback) => {
      // let reg = /^[\u4e00-\u9fa5]+$/
      // let regEn = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/
      let reg = /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g
      if (!value) {
        callback(new Error('不能为空'))
      }
      // if (!reg.test(value.slice(0, 1))) {
      //   callback(new Error('必须中文开头'))
      // }
      if (reg.test(value)) {
        callback(new Error('不支持输入表情'))
      } else {
        callback()
      }
    }
    const validateShareTable = (rule, value, callback) => {
      if (value.length > 0) {
        callback()
      } else {
        callback(new Error('共享的租户不能为空！'))
      }
    }
    return {
      preview: false, // 模板预览弹框
      check: false, // 文件hash存证弹框
      single: false, // 选择框
      noTenantName: '',
      transProps: {},
      onSelectData: [],
      selectRecords: [],
      contractList: [],
      selectPageParam: {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      },
      noSelectPageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      modal: false,
      isTip: false,
      modalEg: false, // 示例
      egImg,
      transId: '', // 去详情时的contractId
      tipInfo: {
        status: 'success',
        imgUrl: successImg,
        title: '新建成功',
        content: '可前往详情页编辑合约、上传合约代码',
        cancelText: '返回列表',
        confirmText: '前往详情页'
      },
      chainId: this.$route.params.chainId ? this.$route.params.chainId : '',
      transferKey: 0,
      inputvalue: '',
      arrContract: {
        alertTitle: '新建合约',
        contractId: '',
        contractName: '',
        contractReadableName: '',
        projectName: '',
        brief: '',
        languageType: '',
        titleDisabled: false
      },
      tablePageParam: { pagetotal: 0, pageSize: 10, pageIndex: 1 },
      tableData: [],
      columns: [
        { key: 'contractReadableName', title: '应用名称' },
        { key: 'projectName', title: '项目名称' },
        { key: 'contractName', title: '合约名称' },
        { key: 'brief', title: '应用简介' },
        { key: 'contractSources', title: '合约来源' },
        { key: 'tenantName', title: '所属租户' },
        { // key: 'action',
          title: '操作',
          align: 'left',
          width: 140,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: { click: () => { this.showDetails(params.index) } }
              }, '详情'),
              h('Button', {
                props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                style: this.buttonStyle,
                on: { click: () => { this.editContract(params.index) } }
              }, '修改'),
              // h('Button', {
              //   props: { type: 'text', size: 'small', disabled: !params.row.canShare },
              //   style: { marginRight: '8px', color: !params.row.canShare ? '#c5c8ce' : '#3D73EF', border: !params.row.canShare ? '1px solid #c5c8ce' : '1px solid #3D73EF' },
              //   on: { click: () => { this.shareContract(params.index) } }
              // }, '共享'),
              // // 新增合约上架
              // h('Button', {
              //   props: { type: 'text', size: 'small', disabled: (!params.row.existVersion) },
              //   style: {
              //     marginRight: '8px',
              //     color: `${!params.row.existVersion ? '#c5c8ce' : '#3D73EF'}`,
              //     border: `${!params.row.existVersion ? '1px solid #c5c8ce' : '1px solid #3D73EF'}`,
              //     background: `${!params.row.existVersion ? 'white' : 'none'}`
              //   },
              //   on: { click: () => { this.shelvesContract(params.index) } }
              // }, '上架合约市场')
            ])
          }
        }
      ],
      visibleState: ['VISIBLE', 'INVISIBLE'],

      modalShare: false,
      shareItem: {},
      shareItemRulers: {
        contractAccountId: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        shareTenantIdList: [
          { trigger: 'change', validator: validateShareTable }
        ]
      },
      formItemRule: {
        // 隐藏合约名称字段
        // contractName: [{ required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
        //   { max: 32, message: '不能多于32位', trigger: 'blur' },
        //   { trigger: 'blur', validator: validateContractName }],
        contractReadableName: [
          { required: true, message: '不能为空', trigger: 'blur' }
          // { required: true, max: 15, message: '不能超过15位', trigger: 'blur' },
          // { trigger: 'change', validator: validateContractReadableName },
          // { trigger: 'blur', validator: validateContractReadableName }
        ],
        brief: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { trigger: 'blur', validator: validateContractReadableName }
          // { max: 50, message: '不能超过50位', trigger: 'blur' }
        ]
      },
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  methods: {
    // 新建合约
    create () {
      this.$router.push({
        name: 'contract_new'
      })
    },
    // 上架合约市场
    shelvesContract (index) {
      // console.log(this.tableData[index].contractName)
      // console.log(this.tableData[index])
      // console.log(this.tableData[index].contractId)
      this.$router.push({
        name: 'contract_shelves',
        params: {
          management: this.tableData[index].isManager,
          listId: this.tableData[index].contractId,
          content: this.tableData[index]
          // name: this.tableData[index].contractName
          // params: { contractId: `${this.tableData[index].contractId}` }
        }
      })
    },

    showEg () {
      this.modalEg = true
    },
    init () {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
        this.arrContract = {
          alertTitle: '新建合约',
          contractId: '',
          contractName: '',
          contractReadableName: '',
          languageType: '',
          brief: ''
        }
        // this.$set(this.arrContract, 'contractName', '')
        // this.$set(this.arrContract, 'contractReadableName', '')
        // this.$set(this.arrContract, 'brief', '')
      })
      this.isTip = false
    },
    getContractList (item) {
      unshareChainAccountList(item.contractId).then(res => {
        if (res.code === '00000') {
          this.contractList = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.showFail()
        console.log('unshareChainAccountList.error===>', error)
        // this.msgInfo('error', error.message, true)
      })
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    ok () {
      this.$refs['formItem'].validate((valid) => {
        if (valid) {
          if (this.arrContract.alertTitle === '新建合约') {
            this.$Modal.confirm({
              content: '<p>新建成功后，合约名称不可再修改，确定提交吗？</p>',
              onOk: () => {
                this.okRequest()
              },
              onCancel: () => {
                // this.$Message.info('Clicked cancel')
              }
            })
          } else {
            this.okRequest()
          }
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },

    async okRequest () {
      getContractPower(this.arrContract).then(res => {
        if (res.code !== '00000') {
          this.showFail(res.message)
        } else {
          this.transId = res.data.contractId
          this.showSuccess()
        }
      }).catch(error => {
        this.showFail()
        console.log('getContractPower.error===>', error)
        // this.msgInfo('error', error.message, true)
      })
    },
    // 去往列表
    goList () {
      this.modal = false
      this.getTableData()
      this.isTip = false
      this.init()
    },
    showSuccess () {
      this.isTip = true
      this.tipInfo = {
        status: 'success',
        imgUrl: successImg,
        title: `${this.arrContract.alertTitle === '新建合约' ? '新建' : '修改'}成功`,
        content: '可前往详情页编辑合约、上传合约代码',
        cancelText: '返回列表',
        confirmText: '前往详情页'
      }
    },
    showFail (msg = '系统异常') {
      this.isTip = true
      this.tipInfo = {
        status: 'fail',
        imgUrl: failImg,
        title: `${this.arrContract.alertTitle === '新建合约' ? '新建' : '修改'}失败`,
        content: `由于${msg}原因${this.arrContract.alertTitle === '新建合约' ? '新建' : '修改'}失败请重试`,
        cancelText: '取消',
        confirmText: '重新提交'
      }
    },
    cancel () {
      this.init()
      this.modal = false
    },
    tipCancel (status) {
      if (status === 'fail') {
        this.cancel()
        this.isTip = false
      } else {
        this.goList()
      }
    },
    tipOk (status) {
      if (status === 'fail') {
        this.isTip = false
        // this.okRequest()
      } else {
        this.modal = false
        this.isTip = false
        this.init()
        this.$router.push({
          name: 'contract_details',
          params: { contractId: `${this.transId}` }
        })
      }
    },
    editContract (index) {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.arrContract = {
        alertTitle: '修改合约',
        contractId: `${this.tableData[index].contractId}`,
        // contractName: `${this.tableData[index].contractName}`,
        contractReadableName: `${this.tableData[index].contractReadableName}`,
        projectName: `${this.tableData[index].projectName}`,
        brief: `${this.tableData[index].brief}`,
        languageType: `${this.tableData[index].languageType}`,
        titleDisabled: true
      }
      this.modal = true
    },
    shareContract (index) {
      this.$nextTick(() => {
        this.$refs['shareItem'].resetFields()
      })
      this.noTenantName = ''
      this.shareItem = {
        title: `共享合约${this.tableData[index].contractName === '' ? '' : this.tableData[index].contractName}`
      }
      this.getContractList(this.tableData[index])
      this.modalShare = true
    },
    getTenantIdList (value) {
      if (value) {
        this.transProps = JSON.parse(value.tag)
      }
    },
    cancelShare (name) {
      this.$nextTick(() => {
        this.$refs[name].resetFields()
      })
      this.modalShare = false
    },
    async okShare (name) {
      if (!(this.shareItem.shareTenantList && this.shareItem.shareTenantList.length > 0)) {
        this.msgInfo('warning', '请选择共享租户', true)
        return
      }
      this.$refs[name].validate(async (valid) => {
        if (valid) {
          let result = await this.shareRequest()
          if (result) {
            this.modalShare = false
            this.getTableData()
          }
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },
    async shareRequest () {
      const params = {
        contractId: this.transProps.contractId,
        chainId: this.transProps.chainId,
        contractAccountId: this.transProps.contractAccountId,
        uploadVersion: this.transProps.contractVersion,
        shareTenantList: this.shareItem.shareTenantList
      }
      return sharingContract(params).then(res => {
        if (res.code === '00000') {
          this.$Modal.success({
            content: '<p>' + res.message + '</p>'
          })
          return true
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else {
          this.msgInfo('warning', res.message, true)
          return false
        }
      }).catch(error => {
        this.showFail()
        console.log('sharingContract.error===>', error)
        // this.msgInfo('error', error.message, true)
      })
    },
    getSelectionDataFn (data) {
      this.shareItem.shareTenantList = data.map(val => {
        return {
          tenantId: val.tenantId,
          tenantName: val.tenantName
        }
      })
    },
    getTableData () {
      getContractTableData(this.tablePageParam, this.inputvalue).then(res => {
        // console.log(res.data.records)
        // console.log('getContractTableData===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          let resdata = {
            '1': '合约模板',
            '2': '手动新增'
            // null': '手动新增'
          }
          let tableData = res.data.records.map(item => {
            let contrac = item.contractSources ? resdata[item.contractSources] : '手动新增'
            return {
              ...item,
              contractSources: contrac
            }
          })
          this.tableData = tableData
          // console.log(this.tableData)ai
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        }
        ++this.transferKey
      }).catch(error => {
        // console.log('getContractTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    searchList () { this.getTableData() },
    showDetails (index) {
      // console.log('showDetails~~', index)
      this.modal = false
      this.isTip = false
      this.init()
      this.$router.push({
        name: 'contract_details',
        params: { contractId: `${this.tableData[index].contractId}`, tenantId: `${this.tableData[index].tenantId}`, languageType: `${this.tableData[index].languageType}` }
      })
    }
  },
  mounted () {
    this.getTableData()
    this.modal = this.$route.params.modal ? Boolean(this.$route.params.modal) : false
  },
  deactivated () {
    // this.init()
    this.modal = false
  },
  computed: {
    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
  activated () {
    this.getTableData()

    // if (this.arrContract.contractName || this.arrContract.contractReadableName || this.arrContract.brief) {
    //   this.modal = true
    // } else {
    //   this.modal = false
    // }
    this.modal = this.$route.params.modal ? Boolean(this.$route.params.modal) : false
  }
}
</script>

<style lang="less" scoped>
.mandatory {
  /deep/.ivu-form-item-label::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}
.result-class {
  text-align: center;
  .result-title {
    height: 41px;
    font-size: 24px;
    font-weight: bold;
    line-height: 31px;
    color: #333333;
    opacity: 1;
  }
  .result-content {
    height: 31px;
    font-size: 16px;
    font-weight: 400;
    line-height: 23px;
    color: #9b9b9b;
    opacity: 1;
  }
}
.eg-class {
  p {
    height: 31px;
    line-height: 31px;
  }
}
.contract {
  button.btn {
    position: absolute;
    right: 10px;
  }
  .basetext {
    span {
      text-align: left;
      margin: 0 30px;
      line-height: 30px;
    }
  }
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8) !important;
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
</style>
