<template>
  <div class="content">
    <CreatHeader :isDeploy2="isDeploy2"></CreatHeader>
    <div class="base" v-if="isShowLastStep">
      <div class="title">
        <img :src="infoIcon" class="image">
        <span class="text">基本参数设置</span>
      </div>
      <div class="from">
        <div class="listWrap">
          <div class="list">
            <span class="label baselabel"><span class="required">*</span>区块链名称:</span>
            <el-input v-model="form.ServiceName" maxLength="16" placeholder="4-16位字符，支持中文、英文或数字" @blur="checkServiceName(form.ServiceName)" @focus="clearError"></el-input>
          </div>
          <div class="error" v-show="isShowErrorText">区块链名称格式有误</div>
          <div class="error" v-show="isShowErrorLength">区块链名称长度不小于4个字符</div>
          <div class="error" v-show="isShowNameError">请输入区块链名称</div>
          <div class="error" v-show="isShowReName">区块链名称已存在</div>
        </div>
        <div class="listWrap">
          <div class="list">
            <span class="label baselabel"><span class="required">*</span>区块链版本:</span>
            <!-- <el-select v-model="form.Version" @blur="checkVersion(form.Version)" @change="checkVersion(form.Version)"
            :popper-append-to-body="false" popper-class="select-down">
              <el-option :label="item.Version" :value="item.Version" v-for="(item,index) in versionList" :key="index" ></el-option>
            </el-select> -->
            <el-select v-model="form.Version" disabled @change="checkVersion(form.Version)"  @visible-change="changeVersion($event,form.Version)"
            :popper-append-to-body="false" popper-class="select-down">
              <el-option :label="item.Version" :value="item.Version" v-for="(item,index) in versionList" :key="index" ></el-option>
            </el-select>
            <div class="error" v-show="isShowVersion">请选择区块链版本</div>
          </div>
        </div>
        <div class="list">
          <span class="label baselabel"><span class="required">*</span>安全机制:</span>
          <!-- <el-input v-model="form.CryptoType" readonly></el-input> -->
          <el-select v-model="form.CryptoType" disabled>
            <!-- <el-option label="国密" value="SM2"></el-option> -->
            <el-option label="ECDSA" value="ECDSA"></el-option>
          </el-select>
        </div>
        <div class="listWrap">
          <div class="list">
            <span class="label baselabel"><span class="required">*</span>集群名称:</span>
            <!-- <el-select v-model="clusterObj" value-key="ClusterId" @blur="checkClusterName(clusterObj)" @change="checkClusterName(clusterObj)"
            :popper-append-to-body="false" popper-class="select-down">
              <el-option :label="item.ClusterName" :value="item" v-for="(item,index) in clusterList" :key="index"></el-option>
            </el-select> -->
             <el-select v-model="clusterObj" disabled value-key="ClusterId" @change="checkClusterName(clusterObj)"  @visible-change="changeClusterName($event,clusterObj)"
            :popper-append-to-body="false" popper-class="select-down">
              <el-option :label="item.ClusterName" :value="item" v-for="(item,index) in clusterList" :key="index"></el-option>
            </el-select>
            <div class="error" v-show="isShowClusterName">请选择集群名称</div>
          </div>
        </div>
      </div>
    </div>
    <div class="base" v-if="isShowNextStep">
      <div class="title">
        <img :src="infoIcon" class="image">
        <span class="text">Orderer节点设置</span>
      </div>
      <div class="from">
        <div class="list">
          <span class="label ordername">节点名称</span>
          <span class="text">主机设置</span>
          <span class="required"></span>
        </div>
        <div class="list" v-for="(item,index) in orderList" :key="index">
          <span class="label leftOredr"><span class="required">*</span>{{item.key}}:</span>
          <span class="text">
            <el-select v-model="orderList[index].value" @focus="getK8SNodeList" :popper-append-to-body="false" popper-class="select-down">
              <el-option :label="item.Name" :value="item.Name" v-for="(item,cindex) in orderAndPeer" :key="cindex"></el-option>
            </el-select>
          </span>
        </div>
      </div>
    </div>
    <div class="base" v-if="isShowNextStep">
      <div class="title">
        <img :src="infoIcon" class="image">
        <span class="text">组织节点设置</span>
      </div>
      <div class="from">
        <div v-for="(item,index) in peerNames" :key="index">
          <div class="list org">
            <span class="label orgname">组织{{index+1}}名称:</span>
            <span class="text orgtext">{{item.Name}}</span>
          </div>
          <div class="list">
            <span class="label name">节点名称</span>
            <span class="text">主机设置</span>
          </div>
          <div class="list" v-for="(citem,cindex) in peerNames[index].list" :key="cindex">
            <span class="label">
              <span class="required">*</span>
              <el-popover trigger="hover" placement="top" class="">
                <p>{{citem.key}}</p>
                <div slot="reference" class="name-wrapper">
                  <span slot="reference">{{checkName(citem.key)}}:</span>
                </div>
              </el-popover>
            </span>
            <span class="text">
              <el-select v-model="peerNames[index].list[cindex].value" @focus="getK8SNodeList" @change="peerChange"
              :popper-append-to-body="false" popper-class="select-down">
                <el-option :label="item.Name" :value="item.Name" v-for="(item,optionindex) in orderAndPeer" :key="optionindex"></el-option>
              </el-select>
            </span>
          </div>
          <div class="line" v-show="(index + 1) != peerNames.length"></div>
        </div>
      </div>
    </div>
    <div class="btn-wrap">
      <el-button plain @click="getLastStep">上一步</el-button>
      <el-button type="primary" class="blue-btn" @click="getNextStep" v-if="!isShowNextStep">下一步</el-button>
      <el-button type="primary" class="blue-btn" @click="goDeploy" v-if="isShowNextStep">{{loading?'部署中':'部署'}}</el-button>
    </div>
    <div v-if="loading" class="BoxLoading" v-loading="loading" element-loading-text="部署中"></div>
    <!-- <countDown v-if="isShowIcon" :state="countState" :countTime="countTime" :text="countText" @getCountDown="getCountDown"></countDown> -->
  </div>
</template>

<script>
import {getSupportFabricVersionList,getChainPredefinedInfo,getK8SNodeList,deployNewChain,checkChainDisplayName} from '@/api/baascore/creatChain'
import { getClusterList } from "@/api/baascore/overview";
import CreatHeader from './header'
export default {
  components: {
    CreatHeader
  },
  data() {
    return {
      countState:'',
      countTime:2,
      countText:'',
      isShowIcon:false,
      infoIcon:require('@/assets/chainManage_images/overview/infoIcon.png'),
      form:{
        //level:'等保3级',
        ServiceName:'',
        Version:'',
        CryptoType:'ECDSA',
        DeployTemplate:'KAFKA_CMRI',
      },
      clusterObj:{},
      versionList:[],
      clusterList:[],
      orderAndPeer:[],
      isShowLastStep:true,
      isShowNextStep:false,
      orderList:[],
      peerNames:[],
      deployObj:{},
      isShowErrorText:false,
      isShowErrorLength:false,
      loading:false,
      isShowNameError:false,
      isShowVersion:false,
      isShowClusterName:false,
      isShowReName:false,
      isDeploy2:false,
    };
  },
  computed: {
    checkName(name) {
      return function(name) {
        if(name.indexOf('-') != -1) {
          var strList = name.split('-')
          name = strList[0]
          return name
        }else {
          return name
        }
      }
    },
  },
  mounted() {
    var form = JSON.parse(sessionStorage.getItem('prform'))
    var clusterObj = JSON.parse(sessionStorage.getItem('prCluster'))
    if(form && clusterObj) {
      this.form = form
      this.clusterObj = clusterObj
    }
    sessionStorage.setItem('chainType','KAFKA_CMRI')
    this.getSupportFabricVersionList()
    this.getClusterList()
  },
  methods:{
    changeVersion(e,value) {
      if(!e){
        if(!value) {
          this.isShowVersion = true
        }else {
          this.isShowVersion = false
        }
      }
    },
    changeClusterName(e,name) {
      if(!e){
        var ClusterName = name.ClusterName
        if(!ClusterName) {
          this.isShowClusterName = true
        } else {
          this.isShowClusterName = false
        }
      }
    },
    getCountDown(type) {
      this.isShowIcon = false
    },
    checkClusterName(name) {
      var ClusterName = name.ClusterName
      if(!ClusterName) {
        this.isShowClusterName = true
      } else {
        this.isShowClusterName = false
      }
    },
    checkVersion(value) {
      if(!value) {
        this.isShowVersion = true
      }else {
        this.isShowVersion = false
      }
    },
    clearError() {
      this.isShowReName = false
      this.isShowNameError = false
      this.isShowErrorText = false
      this.isShowErrorLength = false
    },
    checkServiceName(value) {
      return new Promise((resolve) =>{
        var re =  /^[\u4e00-\u9fa5a-zA-Z0-9]{4,16}$/
        if(value == '') {
          this.isShowNameError = true
          this.isShowReName = false
        }else {
          this.isShowNameError = false
        }
        if(value != '' && !re.test(value)) {
          this.isShowErrorText = true
          this.isShowReName = false
        }else {
          this.isShowErrorText = false
        }
        if(re.test(value) && value.length<4) {
          this.isShowErrorLength = true
          this.isShowReName = false
        } else {
          this.isShowErrorLength = false
        }
        if(!this.isShowNameError&&!this.isShowErrorText&&!this.isShowErrorLength) {
          var params = {
            ChainDisplayName:value
          }
          checkChainDisplayName(params).then(res =>{
            if(res.code == 200) {
              this.isShowReName = false
              resolve()
            }else {
              this.isShowReName = true
            }
          })
        }
      })
    },
    getSupportFabricVersionList() {
      getSupportFabricVersionList().then(res =>{
        if(res.code == '200') {
          this.versionList = res.data
          this.form.Version = this.versionList[0].Version
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
        }else{
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      })
    },
    getClusterList() {
      getClusterList().then(res =>{
        if(res.code == '200') {
          this.clusterList = res.data
          this.clusterObj = this.clusterList[0]
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
        }else{
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      })
    },
    peerChange(value) {
      this.$forceUpdate()
    },
    getK8SNodeList() {
      if(this.orderAndPeer.length > 0) {
        return
      }
      var params = {
        ClusterId:this.clusterObj.ClusterId
      }
      getK8SNodeList(params).then(res =>{
        if(res.code == 200) {
          this.orderAndPeer = res.data.Hosts
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
        }else{
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      })
    },
    getLastStep() {
      if(this.isShowLastStep) {
        this.$router.go(-1)
      }
      this.isShowNextStep = false
      this.isShowLastStep = true
      this.orderList = []
    },
    async getNextStep() {
      this.checkClusterName(this.clusterObj)
      this.checkVersion(this.form.Version)
      await this.checkServiceName(this.form.ServiceName)
      if(this.isShowReName || this.isShowErrorText || this.isShowErrorLength || this.isShowVersion || this.isShowClusterName || this.isShowNameError) {
        return
      }
      this.orderList = []
      if(this.form.ServiceName && this.clusterObj.ClusterName && this.form.Version) {
        sessionStorage.setItem('prform',JSON.stringify(this.form))
        sessionStorage.setItem('prCluster',JSON.stringify(this.clusterObj))
        var data = {
          //msgType:'getChainPredefinedInfo',
          //params:{
            ServiceName:'',
            ChainDisplayName:this.form.ServiceName,
            ClusterName:this.clusterObj.ClusterName,
            DeployTemplate:'KAFKA_CMRI',
            OrdererCount:2,
            CryptoType:this.form.CryptoType,
            Version:this.form.Version,
            Orgs:[
              {
                Name:'org1',
                PeerCount:2
              },
              {
                Name:'org2',
                PeerCount:2
              },
            ],
         // }

        }
        getChainPredefinedInfo(data).then(res =>{
          if(res.code == 200) {
            // this.isShowIcon = true
            // this.countState = 'success'
            // this.countText = '请求成功！'
            this.deployObj = res.data
            var obj = res.data.NodeDeploy.commons
            var peerNames = res.data.DeployInfo.PeerOrgs
            var peerObj = res.data.NodeDeploy
            var cobj = {}
            //PEER
            peerNames.forEach(item =>{
              var bkeys = Object.keys(peerObj).sort()
              bkeys.forEach(bitem =>{
                if(item.Name == bitem) {
                  var peerList = []
                  var keys = Object.keys(peerObj[bitem]).sort()
                  keys.forEach(citem =>{
                    cobj = {
                      key:citem,
                      value:peerObj[bitem][citem]
                    }
                    peerList.push(cobj)
                    item.list = peerList
                  })
                }
              })
            })
            this.peerNames = peerNames
            //order
            var orderKey = Object.keys(obj).sort()
             orderKey.forEach(item =>{
              let cobj = {
                key : item,
                value:obj[item]
              }
              this.orderList.push(cobj)
            })
            this.isShowLastStep = false
            this.isShowNextStep = true
            this.isDeploy2 = true
            document.body.scrollTop = 0;
            document.documentElement.scrollTop = 0;
          }else if(res.code == 4102) {
           // this.$message.error(res.message)
            this.isShowReName = true
          }
          else {
            // this.isShowIcon = true
            // this.countState = 'error'
            // this.countText = '数据获取失败，请重新加载！'
            this.$message.error('数据获取失败，请重新加载！')
          }
        })
      }
    },
    goDeploy() {
      this.loading = true
      this.$emit('getChildLoading',true)
      var object = JSON.parse(JSON.stringify(this.deployObj))
      var orderNames = this.orderList
      orderNames.forEach(item =>{
        object.NodeDeploy.commons[item.key] = item.value
      })
      var peerList = this.peerNames
      peerList.forEach(item =>{
        item.list.forEach(citem =>{
          object.NodeDeploy[item.Name][citem.key]  = citem.value
        })
      })
      object.DeployInfo.PeerOrgs.forEach((item) =>{
        delete item.list
      })
      object.ClusterName = this.clusterObj.ClusterName
      var params = Object.assign(object,this.form)
      params.ChainDisplayName = this.form.ServiceName
      params.ServiceName = ''
      // var data = {
      //   msgType:'deployNewChain',
      //   params,
      // }
      deployNewChain(params).then(res =>{
        //this.loading = false
        if(res.code == 200) {
          var ServiceId = res.data
          sessionStorage.setItem('ServiceId',ServiceId)
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '部署成功！'
          //this.$message.success('部署成功！')
          this.$router.push({
            path:'/guide/creatChain/prChain/deployPrChain',
          })
        }else if(res.status == 4102) {
          this.loading = false
          this.$emit('getChildLoading',false)
          this.$message.error("区块链名称已存在，请重新设置。")
        } else {
          this.loading = false
          this.$emit('getChildLoading',false)
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '部署失败，请检查网络！'
          this.$message.error('部署失败，请检查网络！')
        }
      })
      .catch(err => {
              this.loading = false
              this.$emit('getChildLoading',false)
             // this.$message.error('网络异常,请检查网络')
            });
    }
  }
}
</script>

<style lang="less" scoped>
  .content {
    width: 100%;
    //margin-top: 70px;
    .base {
      margin-top: 26px;
      .title {
        .image {
          width: 3px;
          height: 14px;
          vertical-align: middle;
          margin-right: 5px;
        }
        .text {
          // font-size: 20px;
          font-size: 14px;

          font-weight: 400;
          color: #333333;
          vertical-align: middle;
        }
      }
      .from {
        margin: 0px auto;
        padding: 10px 0 20px;
        width:100%;
        background: #FFFFFF;
        // border: 2px solid rgba(228, 227, 227, 0.22);
        // box-shadow: 0px 4px 10px 0px rgba(218, 218, 218, 0.17);
        // border-radius: 4px;
        .listWrap {
          position: relative;
        }
        .error {
          color: #f56c6c;
          position: absolute;
          top: 5px;
          left: 600px;
          // font-size: 17px;
          font-size: 14px;
        }
        .list {
          width: 100%;
          display: flex;
          margin-top: 20px;
          justify-content: flex-start;
          align-items: baseline;
          .label {
            width: 130px;
            font-size: 14px;
            color: #333;
            margin-right: 60px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            &.baselabel{
              width: 120px;
              margin-left: 50px;
              margin-right: 20px;
              justify-content: flex-end;
            }
            &.orgname {
              color:#337DFF;
            }
          }
          .text {
            width: 360px;
            font-size: 14px;
            color: #333333;
            text-align: left;
            margin-right:20px;
            &.orgtext {
              text-align: left;
              color: #337DFF;
            }
          }
          .el-input {
            width: 360px;
           // height: 56px;
            margin-right: 20px;
            // font-size: 17px;
            font-size: 14px;
            /deep/ .el-input__inner {
             // height: 56px;
             height:32px;
             line-height: 32px;
            }
          }
          .el-select {
            width: 360px;
             margin-right: 20px;
             //margin-left: -4px;
            //  font-size: 17px;
            font-size: 14px;

          }
          /deep/ .el-input__inner{
            width: 360px;
           // height: 56px;
            // font-size: 17px;
            font-size: 14px;
            height:32px;
            line-height: 32px;
          }
          /deep/ .el-input__icon{
            line-height: 32px;
          }
          .required {
            color: #FF3A4C;
            font-size: 14px;
            margin-right: 10px;
            position: relative;
            top: 4px;
          }
        }
        .line {
          margin:40px auto;
          width: 95%;
          height: 1Px;
          background: #DFDFDF;
        }
      }
    }
    .btn-wrap {
      background: #fff;
      padding: 50px 0 50px 0;
      margin-left: 190px;
      text-align: center;
      display: flex;
      justify-content: flex-start;
      .last-step {
        width: 210px;
        height: 64px;
        background: #fff;
        border: 3px solid #E7ECEF;
        border-radius: 4px;
        // font-size: 22px;
        font-size: 14px;

        font-weight: 400;
        color: #666;
        line-height: 58px;
        cursor: pointer;
        margin-right: 180px;
      }
      .next-step {
        width: 192px;
        height: 64px;
        background: #337DFF;
        border: 2px solid;
        border-radius: 4px;
        // font-size: 22px;
        font-size: 14px;

        font-weight: 400;
        color: #FFFFFF;
        line-height: 58px;
        cursor: pointer;
      }
    }
  }
.BoxLoading{
  position:fixed;
  top:0;
  right:0;
  bottom:0;
  left:0;
  z-index: 10000
}
.BoxLoading /deep/ .el-loading-mask{
  background: rgba(0, 0, 0, 0.2) !important;
}
.BoxLoading /deep/ .el-loading-spinner .circular{
  width:60px !important;
  height: 60px !important;
}
.BoxLoading /deep/ .el-loading-spinner .path{
  stroke:#fff !important;
}
.BoxLoading /deep/ .el-loading-spinner .el-loading-text{
  color:#fff !important;
  // font-size:24px;
  font-size: 14px;
  font-weight:bold;
}
/deep/ .select-down{
  margin-top:7px !important;
  left:0px !important;
}
</style>
