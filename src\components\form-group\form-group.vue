<template>
  <Form ref="ops" :model="ops" :rules="ruleForm" :label-width="160" style="line-height:0">
    <input type="hidden" id="pastOps" :value="pastOps" />
    <FormItem label="合约类型：" prop="contractType">
      <RadioGroup v-model="ops.contractType" v-for="item in contractTypeArr" :key="item.key+'ops'">
        <Tooltip v-if="item.contractTypeExplain" max-width="200" :content="item.contractTypeExplain" placement="top-start">
          <Radio :key="item.key" :label="item.key" style="margin-right: 20px;" @click="ops.contractTypeDesc = item.value">{{item.value}}</Radio>
        </Tooltip>
        <Radio v-else :key="item.key" :label="item.key" style="margin-right: 20px;" @click="ops.contractTypeDesc = item.vaule">{{item.value}}</Radio>
      </RadioGroup>
    </FormItem>
    <FormItem label="TPS预估：" prop="tps">
      <Tooltip max-width="200" content="TPS预估值应为1-10万以内的正整数。TPS是业务系统调用合约的交易统计，预估该值是为了平台能在合约调用情况出现非预期波动时做出提醒。" style="margin-left: -18px;">
        <Icon type="md-help-circle" style="font-size:16px;" />
      </Tooltip>
      <Input v-model="ops.tps" style="display:inline-table;" type="text" placeholder="请输入" />
    </FormItem>
    <FormItem label="运维联系人：" prop="opsLinkman">
      <RadioGroup v-model="ops.opsLinkman.source" @change.native="init('opsLinkman')">
        <Radio label="SYS_TENANT">平台租户</Radio>
        <Radio label="OTHER">其他</Radio>
      </RadioGroup>
      <Row v-show="showArr.showYW">
        <Col span="6">
        <FormItem>
          <Select v-model="ops.opsLinkman.tenantId" placeholder="请选择租户" @on-change="changeSelectTenant(ops.opsLinkman.tenantId,'opsLinkman')" filterable>
            <Option v-for="item in tenantList" :value="item.tenantId" :key="item.tenantId+'tenantId'">{{ item.tenantName }}</Option>
          </Select>
        </FormItem>
        </Col>
        <Col span="6" style="margin: 0 10px;">
        <FormItem>
          <Select v-model="ops.opsLinkman.userId" placeholder="请选择联系人" filterable>
            <Option v-for="item in userList.opsLinkman" @click.native="changePhone(item.phoneNumber,'opsLinkman')" :value="item.userId" :key="item.userId+'opsLinkman'">{{ item.realName }}</Option>
          </Select>
        </FormItem>
        </Col>
        <Col span="6">
        <FormItem>
          <Input type="text" maxlength="11" placeholder="请输入手机号" v-model="ops.opsLinkman.phone" @blur.native.capture="blurChange('opsLinkman')" />
        </FormItem>
        </Col>
      </Row>
      <Row v-show="!showArr.showYW">
        <Col span="6">
        <FormItem>
          <Input type="text" style="margin-right: 10px;" placeholder="请输入联系人" v-model="ops.opsLinkman.name" @blur.native.capture="blurChange('opsLinkman')" />
        </FormItem>
        </Col>
        <Col span="6" style="margin: 0 10px;">
        <FormItem>
          <Input type="text" maxlength="11" placeholder="请输入手机号" v-model="ops.opsLinkman.phone" @blur.native.capture="blurChange('opsLinkman')" />
        </FormItem>
        </Col>
      </Row>
    </FormItem>
    <FormItem label="需求联系人：" prop="demandSide">
      <RadioGroup v-model="ops.demandSide.source" @change.native="init('demandSide')">
        <Radio label="SYS_TENANT">平台租户</Radio>
        <Radio label="OTHER">其他</Radio>
      </RadioGroup>
      <Row v-show="showArr.showXQ">
        <Col span="6">
        <FormItem>
          <Select v-model="ops.demandSide.tenantId" placeholder="请选择租户" @on-change="changeSelectTenant(ops.demandSide.tenantId,'demandSide')" filterable>
            <Option v-for="item in tenantList" :value="item.tenantId" :key="item.tenantId+'tenantId1'">{{ item.tenantName }}</Option>
          </Select>
        </FormItem>
        </Col>
        <Col span="6" style="margin: 0 10px;">
        <FormItem>
          <Select v-model="ops.demandSide.userId" placeholder="请选择联系人" filterable>
            <Option v-for="item in userList.demandSide" @click.native="changePhone(item.phoneNumber,'demandSide')" :value="item.userId" :key="item.userId+'demandSide'">{{ item.realName }}</Option>
          </Select>
        </FormItem>
        </Col>
        <Col span="6">
        <FormItem>
          <Input type="text" maxlength="11" placeholder="请输入手机号" v-model="ops.demandSide.phone" @blur.native.capture="blurChange('demandSide')" />
        </FormItem>
        </Col>
      </Row>
      <Row v-show="!showArr.showXQ">
        <Col span="6">
        <FormItem>
          <Input type="text" style="margin-right: 10px;" placeholder="请输入联系人" v-model="ops.demandSide.name" @blur.native.capture="blurChange('demandSide')" />
        </FormItem>
        </Col>
        <Col span="6" style="margin: 0 10px;">
        <FormItem>
          <Input type="text" maxlength="11" placeholder="请输入手机号" v-model="ops.demandSide.phone" @blur.native.capture="blurChange('demandSide')" />
        </FormItem>
        </Col>
      </Row>
    </FormItem>
    <FormItem label="调用联系人：">
      <div v-for="(value, key, index) in ops.caller" style="margin-bottom: 10px;" :key="key+'caller'">
        <RadioGroup v-model="ops.caller[key].source" @change.native="changeCallerArr(key)">
          <Radio label="SYS_TENANT">平台租户</Radio>
          <Radio label="OTHER">其他</Radio>
        </RadioGroup>
        <Row v-show="showArr.showDY[key]">
          <Col span="6">
          <FormItem>
            <Select v-model="ops.caller[key].tenantId" placeholder="请选择租户" @on-change="changeSelectTenant(ops.caller[key].tenantId,'caller',key)" filterable>
              <Option v-for="item in tenantList" :value="item.tenantId" :key="item.tenantId">{{ item.tenantName }}</Option>
            </Select>
          </FormItem>
          </Col>
          <Col span="6" style="margin: 0 10px;">
          <FormItem>
            <Select v-model="ops.caller[key].userId" placeholder="请选择联系人" filterable>
              <Option v-for="item in userList.caller[key]" @click.native="changePhone(item.phoneNumber,'caller',key)" :value="item.userId" :key="item.userId">{{ item.realName }}</Option>
            </Select>
          </FormItem>
          </Col>
          <Col span="6">
          <FormItem>
            <Input type="text" maxlength="11" placeholder="请输入手机号" v-model="ops.caller[key].phone" />
          </FormItem>
          </Col>
          <Col span="2" style="margin-left:10px;">
          <Button type="dashed" icon="md-trash" style="padding: 0 6px;" @click="deleteCallerArr(key)"></Button>
          </Col>
        </Row>
        <Row v-show="!showArr.showDY[key]">
          <Col span="6">
          <FormItem>
            <Input type="text" style="margin-right: 10px;" placeholder="请输入联系人" v-model="ops.caller[key].name" />
          </FormItem>
          </Col>
          <Col span="6" style="margin: 0 10px;">
          <FormItem>
            <Input type="text" maxlength="11" placeholder="请输入手机号" v-model="ops.caller[key].phone" />
          </FormItem>
          </Col>
          <Col span="2">
          <Button type="dashed" icon="md-trash" style="padding: 0 6px;" @click="deleteCallerArr(key)"></Button>
          </Col>
        </Row>
      </div>
      <Button type="dashed" long icon="md-add" style="margin: 10px 0" @click="addCallerArr"></Button>
    </FormItem>
  </Form>
</template>

<script>
// import clonedeep from 'clonedeep'
import { getUserData, getAssigned } from '@/api/data'
import { isPhoneNumber, isRealName } from '@/lib/check'
export default {
  name: 'FormGroup',
  props: ['pastOps', 'flag'],
  computed: {
    isFlag () {
      return this.flag
    }
  },
  data () {
    const validateOpsLinkman = (rule, value, callback) => {
      if (value.source === 'SYS_TENANT') {
        if (value.userId && value.tenantId && value.phone) {
          // if (!isPhoneNumber(value.phone)) {
          //   callback(new Error('手机号格式不正确'))
          // }
          callback()
        } else callback(new Error('租户、联系人、手机号不能为空'))
      } else if (value.source === 'OTHER') {
        if (value.name && value.phone) {
          if (!isRealName(value.name)) {
            callback(new Error('联系人只允许中英文和数字,且不能为纯数字,长度不能超过15位'))
          }
          callback()
        } else callback(new Error('联系人、手机号不能为空'))
      }
    }

    return {
      localIsFlag: false,
      transferKey: 0,
      pageParam: { pageSize: 60, pageIndex: 1 },
      showArr: { showYW: true, showXQ: true, showDY: [true] },
      contractTypeArr: [],
      // ops:JSON.parse(this.pastOps),
      ops: Object.assign({}, { 'contractType': '', 'contractTypeDesc': '', 'tps': '', 'opsLinkman': { 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }, 'demandSide': { 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }, 'caller': [{ 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }] }, JSON.parse(this.pastOps)),
      ruleForm: {
        contractType: [{ required: true, message: '合约类型不能为空', trigger: 'change' }],
        tps: [{ required: true, pattern: /^([1-9][0-9]{0,4}|100000)$/, message: '应为1-10万之间的正整数', trigger: 'blur' }],
        opsLinkman: [
          { required: true, validator: validateOpsLinkman, trigger: 'change' }
        ],
        demandSide: [{ required: true, validator: validateOpsLinkman, trigger: 'change' }],
        // phone: [{ style: 'number', pattern: /^[0-9][0-9]*\d{10,11}$/, message: '手机号格式错误', trigger: 'blur' }]
      },
      userList: {
        opsLinkman: [],
        demandSide: [],
        caller: []
      },
      tenantList: []
    }
  },
  watch: {
    'pastOps': {
      handler (newVal, oldVal) {
        this.$nextTick(() => {
          this.ops = JSON.parse(this.pastOps)
        })
      },
      deep: true,
      immediate: false
    },
    'ops.opsLinkman': {
      handler (source) {
        if (!this.isFlag || this.localIsFlag || this.ops.opsLinkman.source === 'OTHER') {
          if (this.ops.opsLinkman.source === 'OTHER') {
            this.showArr.showYW = false
            this.ops.opsLinkman.tenantId = ''
            this.ops.opsLinkman.tenantName = ''
          } else {
            this.showArr.showYW = true
            this.changeSelectTenant(this.ops.opsLinkman.tenantId, 'opsLinkman', undefined, 1)
          }
        } else {
          // console.log('this.ops', this.ops)
        }
      },
      deep: true,
      immediate: true
    },
    'ops.demandSide': {
      handler (source) {
        if (!this.isFlag || this.localIsFlag || this.ops.opsLinkman.source === 'OTHER') {
          if (this.ops.demandSide.source === 'OTHER') {
            this.showArr.showXQ = false
            this.ops.demandSide.tenantId = ''
            this.ops.demandSide.tenantName = ''
          } else {
            this.showArr.showXQ = true
            this.changeSelectTenant(this.ops.demandSide.tenantId, 'demandSide', undefined, 1)
          }
        }
      },
      deep: true,
      immediate: true
    },
    'ops.caller': {
      handler (source) {
        // if (!this.isFlag) {
        this.showArr.showDY = []
        if (this.ops.caller) {
          for (let i = 0; i < this.ops.caller.length; i++) {
            if (this.ops.caller[i].source === 'OTHER') {
              this.showArr.showDY[i] = false
              this.ops.caller[i].tenantId = ''
              this.ops.caller[i].tenantName = ''
            } else if (this.ops.caller[i].source === 'SYS_TENANT') {
              this.showArr.showDY[i] = true
              this.changeSelectTenant(this.ops.caller[i].tenantId, 'caller', i, 1)
            }
          }
        }
        // }
        // console.log('caller===', this.showArr.showDY)
      },
      deep: true,
      immediate: true
    },
    gettenantId: function (newValue, oldValue) {
      this.ops.opsLinkman.tenantId = ''
      this.ops.opsLinkman.userId = ''
      this.ops.opsLinkman.phone = ''
      this.ops.demandSide.tenantId = ''
      this.ops.demandSide.userId = ''
      this.ops.demandSide.phone = ''
      this.ops.caller.map(item => {
        item.tenantId = ''
        item.userId = ''
        item.phone = ''
        return item
      })
      this.getlist()
      // console.log("newValue", newValue);
      // console.log("oldValue", oldValue);
    }
  },
  computed: {
    gettenantId () {
      return this.$store.state.tenantId
    }
  },
  methods: {
    getlist () {
      getUserData(this.$store.state.tenantId ? this.$store.state.tenantId : '0').then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else this.tenantList = res.data
      }).catch(error => {
        console.log('getUrserData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },

    init (type, key) {
      if (this.localIsFlag) {
        if (key !== undefined) {
          this.ops[type][key].userId = ''
          this.ops[type][key].name = ''
          this.ops[type][key].phone = ''
        } else {
          this.ops[type].userId = ''
          this.ops[type].name = ''
          this.ops[type].phone = ''
        }
      } else {
        if (type === 'caller ') { this.localIsFlag = true }
      }
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, duration: 8 }) },
    getOpsFlag () {
      let res = false
      this.$refs['ops'].validate((valid) => {
        if (valid) {
          res = true
        }
      })
      // console.log(this.flag, this.ops.opsLinkman, 'getOpsFlag提交')
      return res
    },
    getOps () {
      let res = false
      this.$refs['ops'].validate((valid) => {
        if (valid) res = this.ops
      })
      return res
    },
    addCallerArr () {
      let callers = { 'id': '', 'source': 'SYS_TENANT', 'tenantId': '', 'tenantName': '', 'userId': '', 'name': '', 'phone': '' }
      if (this.ops.caller === null) this.ops.caller = []
      this.ops.caller.push(callers)
    },
    deleteCallerArr (key) {
      this.ops.caller.splice(key, 1)
      this.userList.caller.splice(key, 1)
      // console.log(this.userList.caller)
    },
    changeCallerArr (key) { this.init('caller', key) },
    changeSelectTenant (tenantId, type, key, init) {
      const arr = ['opsLinkman', 'demandSide']
      if (tenantId && arr.includes(type)) {
        if (this.$refs.ops) this.$refs.ops.validateField(`${type}`)
      }
      if (!init) this.init(type, key)
      if (tenantId && type) {
        getAssigned(tenantId, this.pageParam, '', 1, 1).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
            // this.msgInfo('warning', res.message, true)
          } else {
            if (key !== undefined) {
              // console.log(type, key, res.data.records, 'res.data.records')
              // this.userList[type][key] = res.data.records
              this.$set(this.userList[type], key, res.data.records)
              ++this.transferKey
            } else this.userList[type] = res.data.records
          }
        }).catch(error => {
          // console.log('getAssigned.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }
    },
    changePhone (phone, type, key) {
      // console.log(phone, type, key)
      if (phone && type) {
        if (key !== undefined) this.ops[type][key].phone = phone
        else this.ops[type].phone = phone
        const arr = ['opsLinkman', 'demandSide']
        if (arr.includes(type)) {
          if (this.$refs.ops) this.$refs.ops.validateField(`${type}`)
        }
      }
    },
    blurChange (type) {
      const arr = ['opsLinkman', 'demandSide']
      if (arr.includes(type)) {
        if (this.$refs.ops) this.$refs.ops.validateField(`${type}`)
      }
    }
  },
  mounted () {
    // vuex的实现方式
    this.$store.dispatch('getOptions', 'CONTRACT_TYPE')
      .then((result) => {
        if (Object.prototype.toString.call(result) === '[object Boolean]') {
          this.contractTypeArr = this.$store.state.dict.options.CONTRACT_TYPE
        } else {
          this.contractTypeArr = result
        }
      })
      .catch(error => {
        this.msgInfo('error', error.message, true)
      })
    // getContractType().then(res => {
    //   if (res.code !== '00000') this.msgInfo('warning', res.message, true)
    //   else this.contractTypeArr = res.data
    // }).catch(error => {
    //   console.log('getContractType.error===>', error)
    //   this.msgInfo('error', error.message, true)
    // })
    if (this.tenantList.length === 0) {
      getUserData(this.$store.state.tenantId ? this.$store.state.tenantId : '0').then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else this.tenantList = res.data
      }).catch(error => {
        console.log('getUrserData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    }
    let ops = document.getElementById('pastOps') ? JSON.parse(document.getElementById('pastOps').value) : {}
    if (this.flag && ops.tps) {
      this.ops = ops
      if (this.ops.opsLinkman.tenantId) {
        this.changeSelectTenant(this.ops.opsLinkman.tenantId, 'opsLinkman', undefined, 1)
      }
      if (this.ops.demandSide.tenantId) {
        this.changeSelectTenant(this.ops.demandSide.tenantId, 'demandSide', undefined, 1)
      }
      this.$nextTick(() => {
        if (this.$refs.ops) this.$refs['ops'].validate()
      })
    }
  }
}
</script>

<style lang="less" scoped></style>
