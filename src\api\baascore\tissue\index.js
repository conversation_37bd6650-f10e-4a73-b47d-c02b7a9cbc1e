import axios from '../../index'
const BASEURL = '/cmbaas/portal/fabric/CommonAPI'
// 添加组织
export function addOrgPeerDefaulDeploy (obj) {
  let data = {
    /// chainOrg/prepareForAddOrg
    msgType: 'chainOrg#prepareForAddOrg',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}

// 添加节点
export function prepareForAddPeer (obj) {
  let data = {
    msgType: 'chainOrg#prepareForAddPeer',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
/* // 查询下拉列表
export function getClusterHostList(query) {
    return axios.request({
      url: BASEURL + '/CommonAPI?msgType=getClusterHostList',
      method: 'get',
      params: query
    })
} */

// 组织部署
export function addOrg (obj) {
  let data = {
    msgType: 'chainOrg#addOrg',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}

// 部署公共节点的 修改
export function updateDeployNode (obj) {
  let data = {
    msgType: 'chainOrg#updateDeployNode',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}

// 删除节点
export function deletePeer (obj) {
  return axios.request({
    url: BASEURL + '/chainOrg/deletePeer',
    method: 'post',
    data: obj
  })
}

// 添加节点
export function addPeer (obj) {
  return axios.request({
    url: BASEURL + '/chainOrg/addPeer',
    method: 'post',
    data: obj
  })
}

// 删除链
export function deleteChain (obj) {
  let data = {
    msgType: 'chain#deleteChain',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
// 某条链下所有节点列表
// export function getChainNodeNameList (query) {
//   return axios.request({
//     url: BASEURL + '/chainOrg/getChainNodeNameList',
//     method: 'get',
//     params: query
//   })
// }
// 节点日志查询
export function getFabricNodeLogs (obj) {
  let data = {
    msgType: 'chain#getFabricNodeLogs',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
