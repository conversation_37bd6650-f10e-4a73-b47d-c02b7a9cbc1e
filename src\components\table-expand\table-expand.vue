<style scoped>
  .expand-row{
    margin-bottom: 16px;
  }
</style>
<template>
  <div>
    <Row class="expand-row">
      <Col span="8">
        <span class="expand-key">链名称: </span>
        <span class="expand-value">{{ row.chainName }}</span>
      </Col>
      <Col span="8">
        <span class="expand-key">合约链账户名称: </span>
        <span class="expand-value">{{ row.contractAccountName }}</span>
      </Col>
      <Col span="8">
        <span class="expand-key">部署状态: </span>
        <span class="expand-value">{{ row.deployStatus }}</span>
      </Col>
      <Col span="8">
        <span class="expand-key">部署时间: </span>
        <span class="expand-value">{{ row.deployTime }}</span>
      </Col>
    </Row>
    <Row>
      <Col span="8">
        <span class="expand-key">审核人: </span>
        <span class="expand-value">{{ row.auditorUserName }}</span>
      </Col>
      <Col span="8">
        <span class="expand-key">审核意见: </span>
        <span class="expand-value">{{ row.auditRemark }}</span>
      </Col>
      <Col span="8">
        <span class="expand-key">失败原因: </span>
        <span class="expand-value">{{ row.errorMessage }}</span>
      </Col>
    </Row>
  </div>
</template>
<script>
export default {
  name: 'TableExpand',
  props: {
    row: Object
  }
}
</script>
