<template>
  <div class="prChain-index">
    <PrChain v-if="currentTab==='prChain'" />
    <router-view v-else/>
  </div>
</template>

<script>
import Pr<PERSON>hain from './prChain.vue'
export default {
  name: 'prChain-index',
  components: {
    PrChain
  },
  data () {
    return {
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
