<template>
  <div class="chain">
    <Collapse v-model="panelValue" simple name="mainpanel">
      <Panel name="1" style="background:#ECEFFC; display:block;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        智能合约
        <div slot="content" class="basetext">
          <div slot="left" style="line-height: 30px;">
            <span>合约中文名称：{{ contractArr.contractReadableName }}</span>
            <span><Button @click="showCode" v-if="this.languageType==='C++'">查看源码</Button>
              <Button @click="fileModal" v-else>查看源码</Button></span><br>
            <span>合约类型：{{ contractArr.contractType }}</span><br>
            <span>合约描述：{{ contractArr.contractBrief }}</span><br>
            <span>合约版本：{{ contractArr.uploadVersion }}</span>
            <span><Button @click="showVersion" :disabled="contractArr.lastUploadVersion === null ? true : false">查看上一版本</Button></span><br>
            <span>部署时间：{{ contractArr.deployTime }}</span><br>
          </div>
          <span style="line-height: 30px;">action列表:
            <Tooltip max-width="200" content="只有被绑定的普通链账户才能对智能合约发起交易，指定的普通链账户只有在此配置权限后方可执行该action。">
              <Icon type="md-help-circle" style="font-size:16px;" />
            </Tooltip>
          </span>
          <p>
            <Collapse simple accordion name="contentpanel" @on-change="getLinkList" v-model="actionValue" style="margin-left:10px;">
              <Panel v-for="(item,index) in contractArr.actionList" :name="item" :key="index">
                {{ item }}
                <p slot="content" class="basetext">
                <Table stripe :columns="columns" :data="contractArrList"></Table>
                <Button @click="addBundleAccount(item)" icon="md-add" type="success" ghost>新增绑定链账户</Button>
          </p>
      </Panel>
    </Collapse>
    </p>
  </div>
  </Panel>
  <Panel name="2" style="background:#ECEFFC;">
    <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
    合约链账户
    <p slot="content" class="basetext">
      <span>名称：{{ contractArr.contractAccountName }}</span>
      <span>创建时间：{{ contractArr.accountCreateTime }}</span>
      <span>更新时间：{{ contractArr.accountUpdateTime }}</span><br>
    </p>
    <p slot="content" class="basetext">
      <span>描述：{{ contractArr.accountBrief }}</span>
    </p>
  </Panel>
  <Panel name="3" style="background:#ECEFFC;" class="panel-class">
    <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
    运维信息
    <div slot="content" style="padding-top: 20px;">
      <div :key="transferKey1">
        <p style="margin:0 26px;line-height:30px"> TPS预估：{{ contractArr.tps }} </p>
        <p style="margin:0 26px;line-height:30px"> 运维联系人：
          <span style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ contractArr.opsLinkman.tenantName }}</span>
          <span style="margin-right:10px;"><i class="ri-user-line"></i>{{ contractArr.opsLinkman.name }}</span>
          <span><i class="ri-smartphone-line"></i>{{ contractArr.opsLinkman.phone }}</span>
        </p>
        <p style="margin:0 26px;line-height:30px"> 需求联系人：
          <span style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ contractArr.demandSide.tenantName }}</span>
          <span style="margin-right:10px;"><i class="ri-user-line"></i>{{ contractArr.demandSide.name }}</span>
          <span><i class="ri-smartphone-line"></i>{{ contractArr.demandSide.phone }}</span>
        </p>
        <p style="margin:0 26px;line-height:30px">
          <span style="vertical-align: top;line-height:30px">调用联系人：</span>
        <ul style="display:inline-block;line-height:30px;margin-left:4px;">
          <li v-for="item in contractArr.caller" :key="item.name + Math.random()">
            <span v-if="item.tenantName !== null && item.tenantName" style="margin-right:10px;"><i class="ri-organization-chart"></i>{{ item.tenantName }}</span>
            <span v-if="item.name" style="margin-right:10px;"><i class="ri-user-line"></i>{{ item.name }}</span>
            <span v-if="item.phone"><i class="ri-smartphone-line"></i>{{ item.phone }}</span>
          </li>
        </ul>
        </p>
      </div>
    </div>
  </Panel>
  </Collapse>
  <Modal v-model="isshow.actionModal" title="新增绑定链账户" :draggable="true" sticky :mask-closable="false" style="background-color:#fff" width="550">
    <Form :label-width="140" ref="formItem" :model="formItem" :rules="formItemRule">
      <FormItem label="链账户名称：" prop="normalAccountId">
        <Select v-model="formItem.normalAccountId" placeholder="请选择" style="width:350px;" @on-change="addAccountList">
          <Option v-for="item in accountList" :value="`${item.normalAccountId}`" :key="item.normalAccountId">{{ item.normalAccountName }}</Option>
        </Select>
      </FormItem>
      <p style="font-size:12px;padding:10px 0 10px 140px;"><span>没有合适的链账户，可先</span>
        <router-link to="/new_user">新建链账户</router-link>
      </p>
      <FormItem label="合约功能名称：">
        <Input v-model="actionValue[0]" placeholder="" style="width:350px;" :disabled="true" />
      </FormItem>
      <FormItem label="链账户配置权限：" prop="checkAllGroup">
        <RadioGroup v-model="formItem.checkAllGroup" style="margin-top:-5px;">
          <Radio v-for="item in permissionList" :key="item.id" :label="`${item.permissionId}`"> {{ item.permissionName }} </Radio>
        </RadioGroup>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="cancel('formItem')">取消</Button>
      <Button type="primary" @click="ok('formItem')">确定</Button>
    </div>
  </Modal>
  <Modal v-model="isshow.codeModal" title="查看合约链码" :draggable="true" sticky :mask-closable="false" :footer-hide="true" style="background-color:#fff" :width="720">
    <div v-if="isSingleCpp=='0'">
      <Layout>
        <Sider hide-trigger :style="{background: '#fff'}">
          <Menu theme="light" width="auto" :open-names="['1']">
            <Submenu :name="key" v-for="(item,key) in cppsTitle" :key="key">
              <template slot="title">
                <Icon type="ios-folder"></Icon>
                {{key}}
              </template>
              <MenuItem :name="items.fileName" v-for="(items,i) in item" :key="i" @click.native="clickCpps(items.content)">{{items.fileName}}</MenuItem>
            </Submenu>
          </Menu>
        </Sider>
        <Content :style="{padding: ' 0 24px', minHeight: '280px', background: '#fff'}">
          <p>
            <textarea class="textarea-style" v-html="cppContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
          </p>
        </Content>
      </Layout>
    </div>
    <div style="padding:10px;" v-else>
      <CodeCommon :cppName="contractArr.cppName" :hppNames="contractArr.hppNames" :contractId="contractArr.contractId" :uploadVersion="contractArr.uploadVersion"></CodeCommon>
    </div>

  </Modal>
  <Modal v-model="isshow.versionModal" title="上一版本" :draggable="true" sticky :mask-closable="false" :footer-hide="true" style="background-color:#fff">
    <div style="line-height:30px;padding:0 10px 10px 10px;">
      <p>合约版本号：{{shareDetails.uploadVersion}}</p>
      <p>部署时间：{{shareDetails.deployTime}}</p>
      <p style="line-height: 30px;">action列表:</p>
      <p>
        <Collapse simple accordion name="contentpanel" @on-change="getVersionList" v-model="versionValue">
          <Panel v-for="(item,index) in shareDetails.actionList" :name="item" :key="index">
            {{ item }}
            <p slot="content" class="basetext">
            <Table stripe :columns="versionColumns" :data="versionData"></Table>
      </p>
      </Panel>
      </Collapse>
      </p>
    </div>
  </Modal>
  <Modal v-model="chaincode" title="查询合约链码" width='900px'>
    <div v-if="isSingleCpp=='0'">
      <Layout>
        <Sider hide-trigger :style="{background: '#fff'}">
          <Menu theme="light" width="auto" :open-names="['1']">
            <Submenu :name="key" v-for="(item,key) in cppsTitle" :key="key">
              <template slot="title">
                <Icon type="ios-folder"></Icon>
                {{key}}
              </template>
              <MenuItem :name="items.fileName" v-for="(items,i) in item" :key="i" @click.native="clickCpps(items.content)">{{items.fileName}}</MenuItem>
            </Submenu>
          </Menu>
        </Sider>
        <Content :style="{padding: ' 0 24px', minHeight: '280px', background: '#fff'}">
          <p>
            <textarea class="textarea-style" v-html="cppContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
          </p>
        </Content>
      </Layout>
    </div>
    <div v-else>
      <Collapse simple @on-change="colldata" accordion>
        <Panel :name="contractArr.jsName" :key="contractArr.jsName">
          {{contractArr.jsName}}
          <p slot="content">
            <textarea class="textarea-style" v-html="CollContent.fileContent" readonly @scroll="handScroll($event, 'cpp')"></textarea>
          </p>
        </Panel>
        <Panel :name="contractArr.abiName" v-if="contractArr.abiName">
          {{contractArr.abiName}}
          <p slot="content">
            <textarea class="textarea-style" v-html="CollContent.fileContent" readonly @scroll="handScroll($event, 'js')"></textarea>
          </p>
        </Panel>
      </Collapse>
    </div>

  </Modal>
  </div>
</template>
<script>
import { addChainDeploy, getContractInfoList, getNormalAccountList, getPermissionList, contractUnbundling, contractbundling, getVersionDetails, getShareDetails, getContractChaincode } from '@/api/data'
import CodeCommon from './code-common.vue'

export default {
  name: 'sharecontract_details',
  components: { CodeCommon },
  data () {
    return {
      versionValue: [''],
      shareRecordId: this.$route.params.shareRecordId || '',
      receivedTenantId: this.$route.params.receivedTenantId || 0,
      shareDetails: {},
      timer: null,
      transferKey: 0,
      transferKey1: 0,
      pageParam: { pageSize: 60, pageIndex: 1 },
      panelValue: ['1', '2', '3'],
      formItem: { normalAccountId: '', normalAccountName: '', checkAllGroup: '' },
      isshow: { actionModal: false, codeModal: false, versionModal: false },
      checkobj: { indeterminate: true, checkAll: false, checkAllGroup: '' },
      permission: { permissionName: '', parentPermissionName: '', publicKey: '', privateKey: '', autoGenerateKeyPair: true, writtenToChain: '' },
      password: '',
      actionValue: [''],
      actionValues: '',
      accountList: [],
      caller: [],
      permissionList: [{ permissionId: 1, permissionName: '666s' }, { permissionId: 2, permissionName: '666s' }],
      arrDetails: {},
      contractArr: { opsLinkman: {}, demandSide: {} },
      contractArrList: [],
      columns: [
        { title: '普通链账户', key: 'normalAccountName', tooltip: true },
        { title: '权限', key: 'linkPermission', tooltip: true },
        {
          title: '操作',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    // console.log('解绑：', this.contractArr.chainId, this.contractArr.contractAccountId, this.contractArrList[params.index].normalAccountId, this.actionValue[0])
                    contractUnbundling(this.contractArr.chainId, this.contractArr.contractAccountId, this.contractArrList[params.index].normalAccountId, this.actionValue[0], this.contractArr.shareTenantId).then(res => {
                      if (res.code === '00000') {
                        this.msgInfo('success', res.message)
                        this.timer = setTimeout(() => {
                          this.getLinkList(this.actionValue)
                        }, 2 * 1000)
                      } else if (res.code === '500') {
                        this.msgInfo('error', res.message, true)
                      } else this.msgInfo('warning', res.message)
                    }).catch(error => {
                      this.msgInfo('error', error.message, true)
                    })
                  }
                }
              }, '解绑')
            ])
          }
        }
      ],
      versionColumns: [
        { title: '普通链账户', key: 'normalAccountName', tooltip: true },
        { title: '权限', key: 'linkPermission', tooltip: true }
      ],
      versionData: [],
      formItemRule: {
        normalAccountId: [{ required: true, message: '请选择', trigger: 'change' }],
        checkAllGroup: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      contractSource: 'SHARE_CONTRACT',
      // 以下是js新添加
      chaincode: false,
      jsName: '',
      abiName: '',
      CollContent: [],
      languageType: this.$route.params.languageType || '',
      cppContent: '请选择要看的源码文件',
      cppsTitle: '',
      isSingleCpp: ''
    }
  },
  computed: {
  },
  methods: {
    // 点击文件源码
    fileModal (params) {
      this.chaincode = true
      this.codeData = {
        contractId: this.contractArr.contractId,
        uploadVersion: this.contractArr.uploadVersion,
        fileName: this.contractArr.cppFileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
    },
    colldata (key) {
      if (key[0]) {
        let fileName = key[0]
        let contractId = this.codeData.contractId
        let uploadVersion = this.codeData.uploadVersion
        let pageParam = {
          pageSize: 1,
          pageIndex: 10
        }
        getContractChaincode(contractId, uploadVersion, fileName, pageParam).then(res => {
          if (res.code === '00000') {
            this.CollContent = res.data
          } else {
            this.msgInfo('error', res.message, true)
          }
        }).catch((error) => {
          this.msgInfo('error', error.message, true)
        })
      }
    },
    init () {
      this.permissionList = []
      this.formItem.checkAllGroup = ''
      this.checkobj = { indeterminate: true, checkAll: false, checkAllGroup: '' }
      this.permission = { permissionName: '', parentPermissionName: '', publicKey: '', privateKey: '', autoGenerateKeyPair: true, writtenToChain: '' }
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    addChainDeploy (index) {
      addChainDeploy(this.contractArr.chainId, this.contractArr.contractAccountId).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else this.msgInfo('info', res.message)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    addAccountList (val) {
      // this.$nextTick(() => {
      //   this.$refs['formItem'].resetFields()
      // })
      // console.log('val:', val, this.formItem.normalAccountId, this.actionValue[0])
      this.formItem.checkAllGroup = ''
      if (val) {
        getPermissionList(this.contractArr.chainId, this.contractArr.contractAccountId, this.actionValue[0], this.formItem.normalAccountId).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {
            this.permissionList = res.data
          }
        }).catch(error => {
          this.msgInfo('error', error.message, true)
        })
      }
    },
    ok (val) {
      this.$refs[val].validate((valid) => {
        // console.log('valid', valid)
        if (valid) {
          let formData = {
            'chainAccountId': this.contractArr.contractAccountId,
            'action': this.actionValue[0],
            'normalAccountId': this.formItem.normalAccountId,
            'permissionIdList': [this.formItem.checkAllGroup],
            'contractSource': this.contractSource,
            'shareRecordId': this.contractArr.shareRecordId,
            'receivedTenantId': this.receivedTenantId
          }
          // console.log('FormData:', formData)
          contractbundling(this.contractArr.chainId, formData).then(res => {
            if (res.code !== '00000') {
              if (res.code === '500') {
                this.msgInfo('error', res.message, true)
              } else {
                this.msgInfo('warning', res.message, true)
              }
            } else {
              this.isshow.actionModal = false
              this.msgInfo('success', res.message, true)
              this.timer = setTimeout(() => {
                this.getLinkList(this.actionValue)
              }, 2 * 1000)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        }
      })
    },
    cancel () {
      this.isshow.actionModal = false
      this.init()
    },
    // 新增绑定链账户
    addBundleAccount (name) {
      this.init()
      this.getAccountList(name)
      // console.log('name:', name)
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
    },
    getAccountList (name) {
      if (name) {
        // console.log('getAccountList--name', this.name)
        getNormalAccountList(this.contractArr.chainId, this.contractArr.contractAccountId, name, this.contractSource, this.contractArr.shareRecordId, this.receivedTenantId).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {
            this.isshow.actionModal = true
            this.accountList = res.data
            // if (this.accountList[0]) {
            //   this.formItem.normalAccountId = this.accountList[0].normalAccountId
            //   this.formItem.normalAccountName = this.accountList[0].normalAccountName
            //   this.addAccountList(this.formItem.normalAccountId)
            // }
          }
        }).catch(error => {
          this.msgInfo('error', error.message, true)
        })
      }
    },
    getLinkList (name, flag) {
      // console.log('getLinkList~~', name, name.length, flag)
      if (name[0]) {
        getContractInfoList(this.contractArr.chainId, this.contractArr.contractAccountId, name[0], this.contractSource, this.contractArr.shareRecordId, this.receivedTenantId).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {
            if (flag === 'version') {
              this.versionData = res.data
            } else {
              this.contractArrList = res.data
            }
          }
        }).catch(error => {
          this.msgInfo('error', error.message, true)
        })
      }
    },
    showCode () {
      this.isshow.codeModal = true
    },
    showVersion () {
      this.isshow.versionModal = true
      this.getLastVersionDetails()
    },
    getLastVersionDetails () {
      getVersionDetails(this.contractArr.shareRecordId, this.contractArr.lastUploadVersion).then(res => {
        if (res.code === '00000') {
          this.shareDetails = res.data
        } else this.msgInfo('error', res.message, true)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getShareDetailsData () {
      getShareDetails(this.shareRecordId).then(res => {
        if (res.code === '00000') {
          this.contractArr = res.data
          this.isSingleCpp = res.data.isSingleCpp
          this.cppsTitle = res.data.fileContent
        } else this.msgInfo('error', res.message, true)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getVersionList (name) {
      this.getLinkList(name, 'version')
    },
    clickCpps (value) {
      this.cppContent = value

    },
    // 滚动
    handScroll (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },
  },
  watch: {
    // actionValue: {
    //   handler () {
    //     if (typeof this.actionValue !== 'string') {
    //       this.actionValues = this.actionValue[0]
    //     } else {
    //       this.actionValues = this.actionValue
    //     }
    //   },
    //   deep: true,
    //   immediate: true
    // }
    $route: {
      handler: function (newVal, oldVal) {
        // console.log('watch:', newVal, newVal.params.receivedTenantId, newVal.params.shareRecordId)
        this.receivedTenantId = newVal.params.receivedTenantId
        this.shareRecordId = newVal.params.shareRecordId
        this.getShareDetailsData()
      },
      deep: true,
      immediate: true
    },
    'isshow.versionModal': {
      handler: function (newVal, oldVal) {
        // console.log('watch:', newVal)
        if (oldVal && !newVal) {
          this.versionValue = ['']
        }
      },
      deep: true,
      immediate: true
    },
    clickCpps (value) {
      this.cppContent = value

    }
  },
  mounted () {
    // console.log('receivedTenantId', this.$route.params.languageType)
    // this.getShareDetailsData()
  },
  beforeDestroy () {
    clearInterval(this.timer)
    this.timer = null
  }
}
</script>
<style lang="less" scoped>
/deep/.ivu-menu-submenu-title {
  background: #fff !important;
}
/deep/.ivu-menu,
.ivu-menu-dark,
.ivu-menu-vertical,
.ivu-menu-opened,
.ivu-menu-submenu-title {
  background: #fff !important;
}
.ivu-menu-vertical.ivu-menu-light:after {
  background: #fff;
}
.textarea-style {
  width: 100%;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
.size {
  font-weight: bold;
  font-size: 15px;
}
.chain {
  margin: -16px;
  height: 100%;
  .basetext {
    padding-top: 20px;
    span {
      text-align: left;
      margin: 0 26px;
      line-height: 20px;
      word-break: break-all;
    }
  }
  .demo-split {
    height: 150px;
    line-height: 30px;
    word-break: break-all;
  }
  .title {
    .size;
  }
  .bs {
    text-indent: 10px;
    line-height: 15px;
    border-left: 5px solid #3d73ef;
    margin-bottom: 15px;
  }
}
.panel-class {
  i {
    vertical-align: -0.15em;
  }
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.ivu-card {
  background: #f2f6fd;
}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
/deep/.ivu-collapse[name="mainpanel"] {
  border: none;
}
/deep/.ivu-collapse[name="mainpanel"] > .ivu-collapse-item {
  border: none;
  border-radius: 5px 5px;
  margin-bottom: 10px;
}
</style>
