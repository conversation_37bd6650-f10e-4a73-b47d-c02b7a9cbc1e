<template>
  <div class="useradmin">
    <p style="margin: 10px 10px 15px 0px">
      <Input placeholder="区块链名称" style="width: 180px; vertical-align: baseline" v-model="searchItem.chainName" />
      <Select v-model="searchItem.engineTypeList" multiple placeholder="开发架构类型" style="width: 170px; margin: 0 10px">
        <Option v-for="item in engineTypeList" :value="item.key" :key="item.value">{{ item.key }}</Option>
      </Select>

      <Button type="primary" @click="searchList" icon="ios-search" style="margin: 0 10px">查询</Button>
      <Button type="primary" @click="reset" icon="md-sync" ghost>重置</Button>
      <Button class="btn" type="success" ghost style="width: 150px" @click="add" icon="md-add">新增区块链</Button>
    </p>
    <edit-table-mul :columns="columns" v-model="tableData" :key="transferKey"></edit-table-mul>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10, 20, 40, 60, 100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align: right; margin: 10px 0" />
    <Modal :draggable="true" v-model="modal" width="700" :title="formItem.alertTitle" :z-index="1000" sticky :mask-closable="false">
      <Form ref="formItem" :rules="formItemRule" :model="formItem" :label-width="140">
        <FormItem label="区块链名称" prop="chainName">
          <Input placeholder="请输入" style="width: 500px; vertical-align: baseline" v-model="formItem.chainName" />
        </FormItem>
        <FormItem label="开发架构类型" prop="engineTypeKey">
          <Select v-model="formItem.engineTypeKey" placeholder="选择开发架构类型" style="width: 200px">
            <Option v-for="item in engineTypeList" :value="item.key" :key="item.value">{{ item.key }}</Option>
          </Select>
        </FormItem>
        <FormItem label="审核列表：" prop="needAudited">
          <Tooltip max-width="200" content="可选择租户需要人工审核的操作，为空系统自动审核" style="margin-left: -18px;">
            <Icon type="md-help-circle" style="font-size:16px;" />
          </Tooltip>
          <Select v-model="formItem.needAudited" multiple placeholder="选择审核列表(多选)" style="width:200px;">
            <Option v-for="item in auditList" :value="item.key" :key="item.key">{{ item.value }}</Option>
          </Select>
        </FormItem>
        <FormItem label="描述">
          <Input type="textarea" style="width: 500px" :maxlength="128" show-word-limit v-model="formItem.chainBrief" :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请填写链的主要业务描述" />
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="text" @click="cancel('formItem')">取消</Button>
        <Button type="primary" @click="ok('formItem')" :loading="loading">确定</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { getChainIdListNet, reviseMultiChainNet, addMultiChainNet } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import { searchKey } from './tool'
import { engineTypeList, statusList, auditList, ownershipList } from './typeList'
export default {
  name: 'blockchain_network',
  components: {
    EditTableMul
  },
  data () {
    return {
      modal: false,
      transferKey: 0,
      engineTypeList: [],
      statusList: [],
      auditList: [],
      loading: false,
      ownershipList: [],
      searchItem: {
        chainName: '',
        engineTypeList: [],
        status: ''
      },
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      formItem: {
        alertTitle: '新增链',
        chainId: '',
        chainName: '',
        createTime: '',
        chainBrief: '',
        ownership: '',
        needAudited: [],
        status: '',
        engineTypeKey: '',
        ownershipKey: '',
        needAuditedKey: '',
        statusKey: ''
      },
      formItemRule: {
        chainName: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, max: 13, message: '长度不能超过13', trigger: 'blur' },
          {
            type: 'string',
            pattern: /^[0-9a-zA-Z]*$/g,
            message: '格式应为0-9a-zA-Z',
            trigger: 'blur'
          }
        ],

        // createTime: [
        //   { required: true, message: "不能为空", trigger: "blur" },
        //   {
        //     type: "string",
        //     pattern: /^[a-z0-9]{64}$/,
        //     message: "格式有误,长度必须为64位,a-z0-9",
        //     trigger: "blur",
        //   },
        // ],
        engineTypeKey: [
          { required: true, message: '请选择一项', trigger: 'change' }
        ],
        // ownership: [
        //   { required: true, message: "请选择一项", trigger: "change" },
        // ],
        // needAudited: [
        //   { required: true, message: "请选择一项", trigger: "change" },
        // ],
        chainBrief: [
          {
            required: true,
            message: '请填写链的主要业务描述',
            trigger: 'blur'
          }
        ]
        // status: [{ required: true, message: "请选择一项", trigger: "change" }],
      },
      columns: [
        {
          title: '序号',
          type: 'index',
          width: 100
        },
        { key: 'chainName', title: '区块链名称', tooltip: true },
        { key: 'creatorName', title: '所属人', tooltip: true },
        { key: 'engineTypeKey', title: '开发架构类型' },
        { key: 'chainBrief', title: '描述', tooltip: true },
        { key: 'createTime', title: '创建时间', tooltip: true, width: 180 },

        {
          key: 'status',
          title: '状态',
          render: (h, params) => {
            const color =
              params.row.statusKey === 'ENABLE' ? '#15AD31' : '#C7C7C7'
            return h(
              'Tag',
              {
                props: {
                  type: 'dot',
                  color: color
                },
                style: { marginLeft: '-8px' }
              },
              params.row.status
            )
          }
        },
        // { key: "needAudited", title: "部署审核" },
        {
          key: 'action',
          title: '操作',
          minWidth: 30,
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small',
                    disabled: this.tableData[params.index].sfyc
                  },
                  style: {
                    marginRight: '8px',
                    color: this.tableData[params.index].sfyc ? '' : '#3D73EF',
                    border: this.tableData[params.index].sfyc
                      ? '1px solid #cccccc'
                      : '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.editTenant(params.index)
                    }
                  }
                },
                '编辑'
              ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.showDetails(params.index)
                    }
                  }
                },
                '详情'
              )
            ])
          }
        }
      ],
      tableData: []
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.formItem = {
        alertTitle: '新增链',
        chainName: '',
        createTime: '',
        chainBrief: '',
        engineTypeKey: '',
        ownership: '',
        needAudited: [],
        status: ''
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    tipInfo (res) {
      if (res.code === '00000') {
        this.msgInfo('success', res.message, true)
        this.getTableData()
        this.modal = false
        this.loading = false
      } else {
        // console.log('tipInfo-error:', res.message)
        this.loading = false
        this.msgInfo('error', res.message, true)
      }
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },

    getTableData () {
      getChainIdListNet(this.tablePageParam, this.searchItem)
        .then((res) => {
          // this.tableData = res.data.records
          if (res.code === '00000') {
            this.tableData = this.changeNeedAudited(res.data.records)
            this.tablePageParam = {
              pagetotal: res.data.total,
              pageSize: res.data.size,
              pageIndex: res.data.current
            }
            ++this.transferKey
          } else {
            this.msgInfo('error', res.message, true)
          }
        })
        .catch((error) => {
          this.msgInfo('error', error.message, true)
        })
    },
    // 查询
    searchList () {
      // this.tablePageParam.pageIndex = 1
      this.getTableData()
    },
    changeNeedAudited (list) {
      for (var i = 0; i < list.length; i++) {
        if (list[i].auditList) {
          list[i]['needAudited'] = list[i].auditList.map(val => val.auditValue).join(',') || ''
        } else {
          list[i].needAudited = list[i].needAudited ? '需要' : '不需要'
        }
        // if (list[i].needAudited) {
        //   list[i].needAudited = '需要'
        // } else {
        //   list[i].needAudited = '不需要'
        // }
      }
      return list
    },
    // 重置
    reset () {
      this.searchItem = {
        chainName: '',
        engineTypeList: [],
        status: ''
      }
      this.searchList()
    },
    ok (name) {
      console.log(this.formItem)
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.loading = true
          this.formItem.ownershipKey = searchKey(
            this.formItem.ownership,
            this.ownershipList
          )
          // this.formItem.needAuditedKey = searchKey(
          //   this.formItem.needAudited,
          //   this.auditList
          // )
          this.formItem.auditList = this.formItem.needAudited || []
          // console.log('this.formItem.status:', this.formItem.status)
          if (this.formItem.status) {
            this.formItem.statusKey = searchKey(
              this.formItem.status,
              this.statusList
            )
            reviseMultiChainNet(this.formItem)
              .then((res) => {
                this.tipInfo(res)
              })
              .catch((error) => {
                // console.log('revise-error:', error)
                this.msgInfo('error', error.message, true)
                this.loading = false
              })
          } else {
            addMultiChainNet(this.formItem)
              .then((res) => {
                this.tipInfo(res)
              })
              .catch((error) => {
                // console.log('add-error:', error)
                this.msgInfo('error', error.message, true)
                this.loading = false
              })
          }
        } else {
          this.msgInfo('error', '存在字段校验不符合规范，请检查！', true)
          this.loading = false
        }
      })
      // this.$refs[name].resetFields()
      // this.init()
    },
    cancel (name) {
      this.init()
      this.modal = false
    },

    add (name) {
      this.init()
      this.modal = true
    },
    showDetails (index) {
      this.$router.push({
        name: 'network-details',
        params: {
          chainId: `${this.tableData[index].chainId}`
        }
      })
    },
    editTenant (index) {
      this.init()
      this.modal = true
      // if (`${this.tableData[index].chainName}`) {
      this.formItem = {
        alertTitle: '修改链',
        chainId: `${this.tableData[index].chainId}`,
        chainBrief: `${this.tableData[index].chainBrief}`,
        chainName: `${this.tableData[index].chainName}`,
        engineTypeKey: `${this.tableData[index].engineTypeKey}`,
        createTime: `${this.tableData[index].createTime}`,
        ownership: `${this.tableData[index].ownership}`,
        needAudited: `${this.tableData[index].needAudited}`,
        status: `${this.tableData[index].status}`
      }
      // }
    }
  },
  // watch: {
  //   tableData: {
  //     handler (newVal) {
  //       //
  //     },
  //     deep: true,
  //     immediate: false
  //   }
  // },
  created () {
    this.engineTypeList = engineTypeList
    this.statusList = statusList
    this.auditList = auditList
    this.ownershipList = ownershipList
  },
  mounted () {
    if (this.$route.params.show) {
      this.add()
    }

    this.getTableData()
    this.$store.dispatch('getOptions', 'AUDIT_TYPE')
      .then(result => {
        if (Object.prototype.toString.call(result) === '[object Boolean]') {
          this.auditList = this.auditLists
        } else {
          this.auditList = result
        }
      })
      .catch(err => { this.msgInfo('error', err.message, true) })
    // this.$Message.config({
    //   top: 250,
    //   duration: 2
    // })
  },
  activated () {
    this.getTableData()
  }
}
</script>
<style lang="less" scoped>
span.search-title {
  font-size: 12px;
}
button.btn {
  position: absolute;
  right: 10px;
  margin: 0 10px;
}
// /deep/.ivu-btn-text:hover {
//   background-color: rgba(61, 115, 239, 0.8);
//   color: #fff !important;
// }
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
</style>
