<template>
  <div class="contract-home">
    <keep-alive>
    <div class="contract-home-item">
      <!-- <div class="contract-title">
        <b>合约广场</b>
        <Button  icon="ios-arrow-forward" size="small"   @click="isContractFlag=true">智能合约</Button>
      </div> -->
      <Button class="right-button" icon="ios-arrow-forward" size="small"   @click="changeFlag(true)">智能合约</Button>
      <Tabs :value="storeFlag?'share2':'share1'" @on-click="onClickTab">
        <TabPane label="共享给我的" name="share1">
          <ShareMe />
        </TabPane>
        <TabPane label="我共享的" name="share2">
          <MyShare />
        </TabPane>
      </Tabs>
    </div>
    </keep-alive>
  </div>
</template>

<script>
import ShareMe from './share-me.vue'
import MyShare from './my-share.vue'
import { mapActions } from 'vuex'
export default {
  name: 'contract_area',
  components: {
    //
    ShareMe,
    MyShare
  },
  data () {
    return {
    }
  },
  computed: {
    storeFlag () {
      return this.$store.state.user.contractFlag
    }
  },
  methods: {
    ...mapActions([
      'updateFlag'
    ]),
    onClickTab (name) {
      if (name === 'share2') this.updateFlag(true)
    },
    changeFlag (flag) {
      this.updateFlag(flag)
      this.$router.push({
        name: 'contract_table'
      })
    }
  },
  mounted () {

  }
}
</script>

<style lang="less" scoped>
.contract-home{
  .contract-home-item{
    position: relative;
    .contract-title{
      display: flex;
      align-items: center;
      b{
        margin-right:10px;
      }
    }
  }
 //
 .right-button{
   position: absolute;
   right: 0px;
   top:6px;
   z-index: 10;
 }
}
</style>
<style lang="less">
.share-flex{
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  margin-bottom: 20px;
  .share-card{
    width:calc((100% - 20px)/3);
    margin-right:10px;
    margin-bottom:10px;
    &:nth-of-type(3n){
      margin-right:0;
    }
    h3{
      margin-bottom: 15px;
      height:40px;
    }
    p{
      line-height: 14px;
      min-height:40px;
      display: flex;
      width: 100%;
      align-items: center;
      .label-span{
        display: inline-block;
        min-width: 70px;
      }
      .p-span{
        display: inline-block;
        width:calc(100% - 90px);
        overflow:hidden;
        text-overflow:ellipsis;
        white-space:nowrap;
      }
    }
    .circle{
      border:5px solid;
      border-radius: 5px;
      margin-right: 10px;
    }
    .btn-div{
      span{
        border-top:1px solid #dcdee2;
        width:50%;
        text-align: center;
        display: inline-block;
        line-height: 36px;
        height: 36px;
      }
    }
    .btn-left{
      border-right: 1px solid #dcdee2;
    }
  }
}
.share-flex-none{
  text-align: center;
  min-height:60vh;
  img {
    height:40vh;
    max-width: 80%;
    max-height: 80%;
  }

  .contract-none {
    font-size: 8px;
    color: #d4d3d3;
    margin-top: -5px;
    position: relative;
  }
}

</style>
