<template>
  <div class="contract">
    <Collapse v-model="panelValue" simple name="mainpanel">
      <Panel name="1" style="background:#ECEFFC;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        合约上架信息
        <p slot="content" class="basetext">
          <span>合约包名称：{{this.Interface.contractBagName}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>链类型：{{this.Interface.chainType}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>合约语言：{{this.Interface.contractLanguage==='JS'?'Java Script':this.Interface.contractLanguage}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>适用场景信息：{{this.Interface.applicaSecene}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>版本信息:</span>
          <edit-table-mul :height="200" style="width: 900px;" border :columns="tableTitle" v-model="VersionData" @on-selection-change="getSelectAll()"></edit-table-mul>
        </p>
        <div slot="content" class="apply_for" v-if="this.curType==='MARKET_AUDIT_OFF'">
          <h2>申请下架说明</h2>
          <p class="apply_for_detail">下架原因: <span>{{this.$route.params.applyReason}}</span> </p>
        </div>
        <div slot="content" class="apply_for" v-else-if="this.curType==='MARKET_AUDIT_REON'">
          <h2>恢复上架说明</h2>
          <p class="apply_for_detail">上架原因: <span>{{this.$route.params.applyReason}}</span> </p>
        </div>
        <!-- <div v-else>sssss</div> -->
      </Panel>
      <Panel name="2" style="background:#ECEFFC;display:block;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        审批意见
        <div slot="content">
          <p slot="content" class="basetext">
            <span class="mandatory">是否同意</span>
            <RadioGroup v-model="animal">
              <Radio v-for="item in workList" :label="item.value" :key="item.value">
                <span>{{item.label}}</span>
              </Radio>
              <!-- <Radio label="同意"></Radio>
        <Radio label="不同意"></Radio> -->
            </RadioGroup>
          </p>
          <div slot="content" class="basetext" style="margin-left:1.5%">
            <!-- <span :class="{mandatory:this.isConsent}">审批说明</span>
            <Input v-model="instructions" placeholder="输入审批说明" type="textarea" :maxlength="30" show-word-limit :autosize="{minRows: 3,maxRows: 5}" style="width: 350px" /> -->

<Form ref="formInline" :model="formInline" :rules="ruleInline" inline :class="{mandatory:this.isConsent}">
        <FormItem label="审批说明" prop="instructions" >
            <Input type="textarea" style="width: 300px"  :maxlength="30" show-word-limit :autosize="{minRows: 3,maxRows: 5}"  v-model="formInline.instructions" placeholder="输入审批说明"/>
        </FormItem>
    </Form>

          </div>
          <p slot="content" class="basetext">
            <Button @click="$router.back(-1)" style="margin-right: 10px">返回</Button>
            <Button type="primary" @click="handleSubmit('formInline')" v-prevent-re-click :loading="loadingStatus">{{ loadingStatus ? "审批中" : "提交审批" }}</Button>
            <!-- <Button type="primary" @click="handleSubmit('formValidate')" v-prevent-re-click :loading="loadingStatus">{{ loadingStatus ? "上传中" : "提交" }}</Button> -->

          </p>

          <Modal v-model="chaincode" title="查询合约链码" width='900px'>
            <p style="margin-bottom:20px">上传版本号：{{this.title}}</p>
            <Collapse simple  accordion v-if="this.Interface.contractLanguage === 'C++'">
              <Panel :name="transferKey1" :key="transferKey1">
                {{transferKey1}}
                <p slot="content">
                  <textarea class="textarea-style" v-html="CollContent.cppcentent.fileContent" readonly @scroll="handScroll($event, 'cpp')"></textarea>
                </p>
              </Panel>
              <Panel :name="item" v-for="item in filesHpp" :key='item'>
                {{item}}
                <p slot="content">
                  <textarea class="textarea-style" v-html="CollContent.hppcentent.fileContent" readonly @scroll="handScroll($event, 'hpp')"></textarea>
                </p>
              </Panel>

            </Collapse>
             <Collapse simple  accordion v-else>
              <Panel :name="transferKey1" :key="transferKey1">
                {{transferKey1}}
                <p slot="content">
                  <textarea class="textarea-style" v-html="CollContent.jscentent.fileContent" readonly @scroll="handScroll($event, 'js')"></textarea>
                </p>
              </Panel>
             <Panel :name="fileName" v-if="fileName">
               {{fileName}}
            <p slot="content">
            <textarea class="textarea-style" v-html="CollContent.abicentent.fileContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
            </p>
             </Panel>
            </Collapse>
          </Modal>
          <!-- 详情弹框 -->
          <!-- <Modal v-model="versionmodal" title="版本详情" width='900px'>
            <div class="versionDetailone">
              <div class="detailModalInfo">
                <h3>运维信息</h3>
              </div>
              <p class="detailModal">
                合约类型：<span>{{contractyype}}</span>
              </p>
              <p class="detailModal">
                TPS预估：<span>{{tps}}</span>

              </p>
            </div>
            <div class="versionDetailtwo">
                   <ul class="pending_ui" v-for="item in opsLinkman" :key="item.id">
        <li> 运维联系人：<span  v-if="item.tenantName"><i class="ri-organization-chart"></i>{{item.tenantName}}</span> </li>
           <li> <span v-if="item.name"><i class="ri-user-line"></i>{{item.name}}</span> </li>
           <li><span v-if="item.phone"><i class="ri-smartphone-line"></i>{{item.phone}}</span> </li>
         </ul>
              <ul class="pending_ui">
          <li> 需求联系人：<span v-if="demandSide.tenantName"><i class="ri-organization-chart"></i>{{demandSide.tenantName}}</span> </li>
          <li> <span v-if="demandSide.name"><i class="ri-user-line"></i>{{demandSide.name}}</span> </li>
          <li><span v-if="demandSide.phone"><i class="ri-smartphone-line"></i>{{demandSide.phone}}</span> </li>
        </ul>

         <ul class="pending_ui" v-for="item in callData" :key="item.id">
            <li>调用联系人： <span v-if="item.tenantName"><i class="ri-organization-chart"></i>{{item.tenantName}}</span> </li>
          <li> <span v-if="item.name"><i class="ri-user-line"></i>{{item.name}}</span> </li>
          <li><span v-if="item.phone"><i class="ri-smartphone-line"></i>{{item.phone}}</span> </li>
        </ul>
            </div>

          </Modal> -->
        </div>
      </Panel>
    </Collapse>
  </div>
</template>
<script>
// import { TemContractName } from '../../../lib/check'
import EditTableMul from '_c/edit-table-mul'
import { getMarketInfo, Pendingsubmit, getChaincode } from '@/api/data'
export default {
  components: {
    EditTableMul
  },
  watch: {
    animal: {
      handler (val) {
        if (val === 'APPROVED') {
          this.$refs['formInline'].resetFields()
        }
      }
    }

  },
  data () {
    return {
      callData: [], // 调用联系人
      formInline: {
        instructions: ''

      }, // 审批
      ruleInline: {
        instructions: [
          { required: true, message: '审批说明不能为空', trigger: 'change' }
        ]

      },
      biztype: '',
      tps: '',
      contractyype: '',
      demandSide: {},
      Interface: {}, // 合约上架信息
      sortid: this.$route.params.listId, // 次数
      contractid: this.$route.params.usercontract, // 合约id
      animal: '', // 单选按钮
      panelValue: ['1', '2'],
      // instructions: '', // 审批说明
      chaincode: false, // 查询合约链码弹框
      versionmodal: false, // 版本详情弹框
      workList: [
        { label: '同意', value: 'APPROVED' },
        { label: '不同意', value: 'REJECT' }
      ],
      // form表单
      formValidate: {
        name: '',
        chaincity: 'EOS', // 链类型
        languagetype: 'C++', // 语言类型
        scenario: '',
        describe: ''
      },

      // 版本信息表格
      VersionTitle: [

        {
          title: '版本号',
          key: 'uploadVersion',
          with: 180
        },
        {
          title: 'cpp文件名',
          key: 'cppFileName'

        },
        {
          title: 'hpp文件名',
          key: 'hppFileNames',
          tooltip: true,
          render: (h, params) => {
            return h('div', params.row.hppFileNames.join(','))
          }

        },
        {
          title: '操作',
          key: 'action',
          width: 180,
          align: 'left',
          render: (h, params) => {
            return h('div', [
              // h(
              //   'Button',
              //   {
              //     props: {
              //       type: 'text',
              //       size: 'small'
              //     },
              //     style: {
              //       marginRight: '8px',
              //       color: '#3D73EF',
              //       border: '1px solid #3D73EF'
              //     },
              //     on: {
              //       click: () => {
              //         this.detailModal(params.index)
              //       }
              //     }
              //   },
              //   '详情'
              // ),
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      VersionData: [], // 版本信息数组
      versionDetails: [],
      opsLinkman: [],
      fileTpye: [],
      CollContent: { cppcentent: {}, hppcentent: {}, jscentent: {}, abicentent: {} },
      codeData: {},
      filesHpp: [],
      title: '', // 查看文件源码标题
      transferKey1: '',
      fileName: '',
      // 以下是js新加
      VersionTitleJ: [
        {
          title: '版本号',
          tooltip: true,
          key: 'uploadVersion'

        },
        {
          title: 'JavaScript文件名',
          key: 'jsFileName',
          tooltip: true
        },
        {
          title: 'abi文件名',
          key: 'abiFileName',
          tooltip: true
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      tableTitle: []
    }
  },
  computed: {
    isConsent () {
      return this.animal === 'REJECT'
    },
    curType () {
      // debugger
      // console.log(this.$route.params.bizType)
      return this.$route.params.bizType
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    // 点击文件源码
    fileModal (params) {
      this.chaincode = true
      this.title = params.row.uploadVersion
      this.codeData = {
        contractId: params.row.contractId,
        uploadVersion: params.row.uploadVersion
      }
      if (this.Interface.contractLanguage === 'C++') {
        this.transferKey1 = params.row.cppFileName
        this.filesHpp = params.row.hppFileNames
        this.getCode(params.row.cppFileName, 'cpp')
        if (params.row.hppFileNames && params.row.hppFileNames.length > 0) {
          params.row.hppFileNames.forEach(val => this.getNewCode(val, 'hpp'))
        }
      } else {
        this.transferKey1 = params.row.jsFileName
        this.fileName = params.row.abiFileName
        this.getCode(params.row.jsFileName, 'js')
        this.getNewCode(params.row.abiFileName, 'abi')
      }
    },
    getCode (fileName) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          if (this.Interface.contractLanguage === 'C++') {
            this.CollContent.cppcentent = res.data
          } else {
            this.CollContent.jscentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    getNewCode (fileName, val) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          if (this.Interface.contractLanguage === 'C++') {
            this.CollContent.hppcentent = res.data
          } else {
            this.CollContent.abicentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 点击详情
    // detailModal (index) {
    //   this.versionmodal = true
    //   getAccountOpsDTOMessage(this.VersionData[index].uploadVersion, this.$route.params.usercontract).then(res => {
    //     this.callData = res.data.caller
    //     this.versionDetails = res.data
    //     this.opsLinkman = [res.data.opsLinkman]
    //     this.demandSide = res.data.demandSide
    //     this.tps = res.data.tps
    //     this.contractyype = res.data.contractTypeDesc
    //   })
    // },
    // 点击折叠面板事件
    // colldata (key) {
    //   if (key[0]) {
    //     this.codeData.fileName = key[0]
    //     getChaincode(this.codeData).then(res => {
    //       if (res.code === '00000') {
    //         this.CollContent = res.data
    //       }
    //     }).catch((error) => {
    //       this.msgInfo('error', error.message, true)
    //     })
    //   }
    // },
    // 滚动
    handScroll (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        // console.log('到底了', fileType, this.codeTotalPages[fileType])
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    getSelectAll (list) {

    },
    // 提交审批事件
    handleSubmit () {
      if (this.animal === '') { this.msgInfo('warning', '请选择审批意见', true) } else {
        if (this.animal === 'REJECT') {
          this.$refs['formInline'].validate((valid) => {
            if (valid) {
              this.loadingStatus = true
              let submitList = {
                noticeId: this.$route.params.noticeid,
                contractId: this.contractid, // 合约id
                contractBagName: this.Interface.contractBagName, // 合约包名称
                bizId: this.Interface.id, // 合约包id
                status: this.animal, // 状态
                remark: this.formInline.instructions, // 审核备注
                sort: this.sortid, // 次数,
                applyType: this.curType
              }
              // 提交审批意见接口
              Pendingsubmit(submitList).then(res => {
                if (res.code === 'A1503') {
                  this.loadingStatus = false
                  this.msgInfo('warning', '已存在此合约包名称，请修改重试', true)
                } else {
                  this.loadingStatus = false
                  this.$router.go(0)
                  this.$Message.success('提交审批成功!')
                // this.$router.push({
                //   name: 'shelves_approval'
                // })
                }
              }).catch(error => {
                this.loadingStatus = false
                this.msgInfo('warning', error.message, true)
              })
            }
          })
        } else if (this.animal === 'APPROVED') {
          this.loadingStatus = true
          // this.$refs['formInline'].resetFields()
          let submitList = {
            noticeId: this.$route.params.noticeid,
            contractId: this.contractid, // 合约id
            contractBagName: this.Interface.contractBagName, // 合约包名称
            bizId: this.Interface.id, // 合约包id
            status: this.animal, // 状态
            remark: this.formInline.instructions, // 审核备注
            sort: this.sortid, // 次数,
            applyType: this.curType
          }
          // 提交审批意见接口
          Pendingsubmit(submitList).then(res => {
            if (res.code === 'A1503') {
              this.loadingStatus = false
              this.msgInfo('warning', '已存在此合约包名称，请修改重试', true)
            } else {
              this.loadingStatus = false
              this.$router.go(0)
              this.$Message.success('提交审批成功!')
            // this.$router.push({
            //   name: 'shelves_approval'
            // })
            }
          }).catch(error => {
            this.loadingStatus = false
            this.msgInfo('warning', error.message, true)
          })
        }
      }
    },
    // 取消事件
    handleReset (name) {
      this.$router.push({
        name: 'shelves_approval'
      })
    }
  },
  mounted () {
    // this.biztype = this.$route.params.bizType

    // console.log(this.$route.params.usercontract)
    if (this.$route.params.listId && this.$route.params.usercontract) {
      // 请求信息和列表
      this.tableTitle = this.$route.params.languageType === 'C++' ? this.VersionTitle : this.VersionTitleJ
      getMarketInfo(this.$route.params.usercontract, this.$route.params.listId).then(res => {
        // console.log(res.data)
        this.Interface = res.data

        let versionData = res.data.records.map(item => {
          return {
            ...item,
            hppFileNames: item.hppFileNames ? item.hppFileNames : []
          }
        })
        this.VersionData = versionData
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    } else {
      this.$router.push({
        name: 'shelves_approval'
      })
    }
  }
}
</script>

<style lang="less" scoped>
.detailModalInfo{
margin-bottom: 2%;
}
/deep/.ivu-form-item-label::before{
content: '' !important;
}
/deep/.ivu-form-item-content{
  display: flex !important;
}
.versionDetailone{
  margin: 2%;
  p{
    margin-bottom: 2%;
  }
}
.versionDetailtwo{
  i{
    vertical-align: -0.15em
  }
  padding: 2%;
  // margin-top: 3%;
  // margin: 2%;
  .pending_ui{
    margin-top: 2%;
  }
}
.apply_for {
  margin-left: 1%;
  .apply_for_detail {
    font-size: 20px;

    margin: 1%;
  }
}
.detailModal {
  span {
    margin-left: 3% !important;
  }
}
.mandatory::before {
  content: "*";
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 14px;
  color: #ed4014;
      margin-top: 10px;
}
.pending_ui {
  display: flex;

  li:nth-child(2){
  margin-left: 2%;
}
  li:nth-child(3){
  margin-left: 2%;
}
}
.contract {
  margin: -16px;
  button.btn {
    position: absolute;
    right: 10px;
  }
  .basetext {
    display: flex;
    padding-top: 20px;
    span {
      text-align: left;
      margin: 0 26px;
      line-height: 20px;
      word-break: break-all;
    }
  }
}
// from表单
.shelvesInfo {
  padding: 2%;
  // border: 1px solid red;
  /deep/.ivu-form-item-label {
    width: 110px !important;
  }
  /deep/.ivu-form-item-content {
    margin-left: 110px !important;
  }

  .mandatory {
    /deep/.ivu-form-item-label::before {
      content: "*";
      display: inline-block;
      margin-right: 4px;
      line-height: 1;
      font-family: SimSun;
      font-size: 14px;
      color: #ed4014;
    }
  }
}
.newFromSubmit {
  float: right;
  margin-right: 3%;
}
// /deep/.ivu-modal>.ivu-modal-content>.ivu-modal-body{max-height: 60vh;overflow: auto;}
// /deep/.ivu-upload-drag{background-color: #f8f8f9;}
// /deep/.ivu-btn-text:hover {
//   background-color: rgba(61,115,239,.8);
//   color: #fff!important;
// }
// /deep/.ivu-btn-text:active{
//   background-color: #3D73EF;
// }
/deep/.ivu-card {
  background: #f2f6fd;
}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
/deep/.ivu-collapse[name="mainpanel"] {
  border: none;
}
/deep/.ivu-collapse[name="mainpanel"] > .ivu-collapse-item {
  border: none;
  border-radius: 5px 5px;
  margin-bottom: 10px;
}
/deep/.ivu-scroll-container {
  height: auto;
  overflow-y: auto;
}

//
// 滚动条
.textarea-style {
  width: 820px;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
</style>
