<template>

  <div style="padding-top:20px;">
    <!-- <div class="result-class" v-if="isTip"> -->
    <!-- <p><Icon :type="tipInfo.icon" size="36" :color="tipInfo.iconColor" /></p> -->
    <!-- <p><img :src="tipInfo.imgUrl" alt=""></p>
      <p class="result-title">{{tipInfo.title}}</p>
      <p class="result-content">{{tipInfo.content}}</p>
    </div> -->
    <div>
      <Form ref="formItem" :rules="formItemRule" :model="arrContract" :label-width="140">
        <!--  隐藏合约名称字段 -->
        <!-- <FormItem label="合约名称：" prop="contractName"> -->
        <!-- <Tooltip max-width="200" content="合约名称须与合约代码类名一致（或与通过c++ attribute 指定名称一致）长度范围支持5-32位，支持英文和数字，特殊符号只能有英文句号.和英文减号-" style="margin-left: -18px;">
          <Icon type="md-help-circle" style="font-size:16px;" />
        </Tooltip> -->
        <!-- <Input v-model="arrContract.contractName" placeholder="填写合约名称" style="width: 520px" />
          <p>合约名称须与“合约代码类名”一致。<a @click="showEg">查看示例></a></p> -->
        <!-- </FormItem> -->
        <FormItem label="应用名称：" prop="contractReadableName">
          <!-- <Tooltip max-width="200" content="中文显示名称不超过15个字" style="margin-left: -18px;">
          <Icon type="md-help-circle" style="font-size:16px;" />
        </Tooltip> -->
          <Input v-model="arrContract.contractReadableName" placeholder="请认真填写应用名称，不可修改" style="width: 520px" />
          <!-- <p>用于平台内展示，如不填写则自动配置为“用户名-编号”格式</p> -->
        </FormItem>
        <FormItem label="项目名称：" prop="projectName">
          <Input v-model="arrContract.projectName" placeholder="请认真填写项目名称，不可修改" style="width: 520px" />
        </FormItem>
        <FormItem label="合约语言：" prop="languageType">
          <Select v-model="arrContract.languageType" placeholder="请选择合约语言" style="width:160px;margin:0 5px" clearable>
            <Option value="C++">C++</Option>
            <Option value="JS">Java Script</Option>
          </Select>
        </FormItem>
        <FormItem label="应用简介：" prop="brief">
          <Input v-model="arrContract.brief" type="textarea" :maxlength="500" show-word-limit :autosize="{minRows: 3,maxRows: 5}" style="width: 520px" placeholder="填写应用简介" />
        </FormItem>
        <!-- <FormItem label="合约来源">
          <Checkbox v-model="single">合约模板</Checkbox>
          <span class="orign_style">{{
            single
              ? `当前合约来源为“合约模板”，取消勾选时来源为“手动新增”`
              : `当前合约来源为“手动新增”，勾选时来源为“合约模板"`
          }}</span>
        </FormItem> -->
        <FormItem v-show="single" label="模板选择" prop="choose" class="mandatory">
          <ul class="choose" v-if="Infolist.length">
            <!-- <li v-for="item in Infolist" :class="{
                select_style: item.active,
              }" :key="item.id">
              <p>{{ item.contractType }}</p>
              <span>{{ item.description }}</span>
              <p class="check_btn" @click="() => showDetail(item)">查看详情</p>
            </li> -->
            <li v-for="(item,index) in Infolist" class="select_style" :key="item.id" @click="temType(item,index)">
              <div class="dd" :class='{ active:index===number}'>
                <p class="select_style_title"> <b>{{ item.contractType }}</b> </p>
                <p class="hide">{{ item.languageType==='JS'?'Java Script':item.languageType }}<br> {{ item.description }} </p>
                <!-- <p class="hide"></p> -->
                <span class="check_btn" @click.stop="() => showDetail(item,index)">查看详情</span>
              </div>

            </li>
          </ul>
          <ul v-else>
            <p>暂无数据</p>
          </ul>
        </FormItem>
      </Form>
    </div>

    <div slot="footer" class="footerBtn">
      <template v-if="single">

        <Button @click="backRouter('formItem')">返回</Button>&nbsp;&nbsp;&nbsp;&nbsp;
        <Button type="primary" @click="down">创建并下载合约模板</Button>
      </template>
      <template v-else>

        <Button @click="backRouter('formItem')">取消</Button> &nbsp;&nbsp;&nbsp;&nbsp;
        <Button type="primary" @click="ok('formItem')" :loading="btnState">确定</Button>

      </template>
    </div>
    <!--   文件hash存证弹框  -->
    <Modal v-model="check" width="900px" footer-hide :title="temlateDetail.contractType">
      <div class="management_box">
        <div class="management_top">
          <div class="filesss">
            <Button type="success" @click="previewClick" ghost class="file_btnsss">模板预览</Button>
          </div>
          <Row class="top_center">
            <Col span="4"><b>发布者：</b> <span>{{temlateDetail.userLoginId}}</span></Col>
            <Col span="4"> <b>链类型：</b> <span>{{temlateDetail.chainType}}</span></Col>
            <Col span="4"> <b>方法：</b> <span>{{temlateDetail.methods}}个</span></Col>
            <Col span="4"> <b>大小：</b> <span>{{temlateDetail.size}}</span></Col>
            <Col span="4"> <b>语言：</b> <span>{{temlateDetail.languageType}}</span></Col>
          </Row>
        </div>
        <div class="management_middle">
          <Row class="middle_center">
            <Col span="7"> <b>发布时间：</b> <span>{{temlateDetail.createTime}}</span></Col>
            <Col span="7"> <b>更新时间：</b> <span>{{temlateDetail.updateTime}}</span></Col>
            <Col span="7"> <b>适用场景：</b> <span>{{temlateDetail.scene}}</span></Col>
          </Row>
        </div>
        <div class="describe">
          <div> <b>描述</b> : <span>{{temlateDetail.description}}</span></div>

        </div>
        <div class="cz_table">
          <h4>接口描述</h4>
          <!-- <Table :columns="columns7" :data="data6"></Table> -->
          <edit-table-mul :columns="columns7" v-model="data6" :key="transferKey"></edit-table-mul>
        </div>

        <div class="mananger_footer">
          <Button type="info" @click="selectTem">选择模板</Button>
          <Button @click="back">返回</Button>
        </div>
      </div>
    </Modal>
    <Modal v-model="preview" title="查询合约链码" width="900px" @on-cancel="handleCancel" @on-ok='handleOk'>
      <Collapse simple @on-change="colldata" accordion>
        <Panel :name="item.fileId" v-for="item in fileName" :key='item.id'>
          {{item.fileName.substr(-3,3)}}文件名：{{item.fileName}}
          <p slot="content">
            <textarea class="textarea-style" v-html="CollContent.fileContent" readonly @scroll="handScroll($event, 'hpp')"></textarea>
          </p>
        </Panel>

      </Collapse>
    </Modal>
    <Modal v-model="modalEg" title="合约命名示例" :footer-hide="true" @on-cancel="modalEg=false" class="eg-class">
      <p>智能合约命名规则：合约名称须与合约代码类名一致。</p>
      <p style="font-weight: bold">示例</p>
      <p>合约代码类名为：<a>hello</a>，则合约名称应为：<a>hello</a></p>
      <p style="height:auto;margin-top:10px">
        <img style="width:100%" :src="egImg" alt="">
      </p>
    </Modal>
  </div>
</template>

<script>
import EditTableMul from '_c/edit-table-mul'
import { getnewContract, getTempate, getTempateDetails, getContractPreview, getDown, getCollContract, getColl } from '@/api/data'
import { isContractName } from '../../../lib/check'
import successImg from '@/assets/img/success.png'
import failImg from '@/assets/img/fail.png'
import egImg from '@/assets/img/eg.png'
export default {
  components: {
    EditTableMul
  },
  data () {
    const validateContractName = (rule, value, callback) => {
      let reg = /^[_a-zA-Z]/
      if (!reg.test(value.slice(0, 1))) {
        callback(new Error('只能以英文及下划线开头'))
      }
      if (!isContractName(value)) {
        callback(new Error('支持英文和数字，特殊符号只能有英文句号.和英文_'))
      } else {
        callback()
      }
    }
    const validateContractReadableName = (rule, value, callback) => {
      // let reg = /^[\u4e00-\u9fa5]+$/
      // let regEn = /^[\\u4e00-\\u9fa5a-zA-Z0-9]+$/
      let reg = /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g
      if (!value) {
        callback(new Error('不能为空'))
      }
      // if (!reg.test(value.slice(0, 1))) {
      //   callback(new Error('必须中文开头'))
      // }
      // if (!regEn.test(value) || value.indexOf(' ') !== -1) {
      //   callback(new Error('不能包含特殊字符.'))
      // }
      if (reg.test(value)) {
        callback(new Error('不支持输入表情'))
      } else {
        callback()
      }
    }
    return {
      ckeckeos: 'EOS',
      preview: false, // 模板预览弹框
      check: false, // 文件hash存证弹框
      single: false, // 选择框
      transferKey: 0,
      number: -1,
      number1: '',
      modalEg: false, // 查看示例
      temlateDetail: {}, // 模板详情
      fileName: [], // 文件名
      CollContent: {}, // 折叠面板内容
      fun: '',
      id: '',
      egImg,
      formItemRule: {
        // 隐藏合约名称字段
        // contractName: [
        //   { required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
        //   { max: 32, message: '不能多于32位', trigger: 'blur' },
        //   { trigger: 'blur', validator: validateContractName }
        // ],
        // 应用名称
        contractReadableName: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, min: 3, message: '不能少于3位', trigger: 'blur' },
          { required: true, max: 30, message: '不能超过30位', trigger: 'blur' },
          { trigger: 'blur', validator: validateContractReadableName }
        ],
        // 项目名称
        projectName: [
          { required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
          { max: 32, message: '不能多于32位', trigger: 'blur' },
          { trigger: 'blur', validator: validateContractName }
        ],
        // 应用简介
        brief: [
          { required: true, message: '不能为空', trigger: 'blur' },
          // { max: 50, message: '不能超过50位', trigger: 'blur' },
          { trigger: 'blur', validator: validateContractReadableName }
        ],
        languageType: [
          { required: true, message: '请选择合约语言', trigger: 'change' }
        ]
        // source: [{ required: true, trigger: 'blur' }],
        // choose: [
        //   { required: true, trigger: 'blur', message: '需要选择一个模板' }
        // ]
      },
      arrContract: {
        contractSources: this.single ? '1' : '2',
        // contractName: '',// 隐藏合约名称字段
        contractReadableName: '',
        projectName: '', // 项目名称
        brief: '',
        ownerTenantId: '',
        languageType: ''
      },
      //
      columns7: [
        {
          title: '函数名（name）',
          key: 'funcName'
        },
        {
          title: '参数（inputs）',
          key: 'parameter'
        },
        {
          title: '简介（description）',
          key: 'description'
        }
      ],
      data6: [

      ],
      // 模板列表
      Infolist: [],
      // 当前详情模板数据
      detailInfo: {},
      curSelectList: [], // 当前选中项
      btnState: false // 按钮状态
    }
  },
  methods: {
    // 查看示例
    showEg () {
      this.modalEg = true
    },

    // 模板预览按钮
    previewClick () {
      this.check = false
      this.preview = true
      getContractPreview(this.id).then(res => {
        // console.log(res)
        if (res.code === '00000') {
          this.fileName = res.data
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else {
          this.msgInfo('warning', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询合约链码关闭
    handleCancel () {
      // console.log('弹框关闭')
      this.preview = false
      this.check = true
    },
    handleOk () {
      this.preview = false
      this.check = true
    },
    backRouter (name) {
      this.$refs[name].resetFields()
      this.arrContract.contractSources = ''
      // this.arrContract.contractName = ''// 隐藏合约名称字段
      this.arrContract.contractReadableName = ''
      this.arrContract.brief = ''
      this.arrContract.languageType = ''
      this.single = false
      this.$router.push({
        name: 'contract_table'
      })
    },

    // 折叠面板
    colldata (key) {
      111111111
      // console.log(key)
      if (key[0]) {
        // let Colldata = {
        //   fileId: key[0],
        //   pageParam: {
        //     pageSize: 1,
        //     pageIndex: 10
        //   }
        // }
        // getCollContract(Colldata).then(res => {
        //   if (res.code === '00000') {
        //     this.CollContent = res.data
        //   } else {
        //     this.msgInfo('error', res.message, true)
        //   }
        // }).catch(error => {
        //   this.msgInfo('error', error.message, true)
        // })

        this.fileName.forEach(item => {
          if (item.fileId == key[0]) {
            let Colldata = {
              fileId: key[0],
              fileName: item.fileName,
              pageParam: {
                pageSize: 1,
                pageIndex: 10
              }
            }
            getColl(Colldata).then(res => {
              this.CollContent = res.data
            }).catch((error) => {
              this.msgInfo('error', error.message, true)
            })
          }
        })
      }
    },
    ok () {
      this.$refs['formItem'].validate((valid) => {
        if (valid) {
          let contentC = localStorage.getItem('roleId') == 1 ? '<p>请确认所选租户是否正确？</p>' : '<p>新建成功后，应用名称、项目名称不可再修改，确定提交吗？</p>'
          this.$Modal.confirm({
            content: contentC,
            onOk: () => {
              if (this.arrContract.contractSources === '') {
                this.arrContract.contractSources = '2'
                this.getNew()
              } else {
                this.getNew()
              }
            },
            onCancel: () => {
              // this.$Message.info('Clicked cancel')
            }
          })
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },
    cancel () {
      this.arrContract.contractSources = ''
      // this.arrContract.contractName = ''// 隐藏合约名称字段
      this.arrContract.contractReadableName = ''
      this.arrContract.projectName = ''
      this.arrContract.brief = ''
      this.single = false
      this.$router.push({
        name: 'contract_table'
      })
    },
    // 查看详情
    showDetail (item, index) {
      this.number1 = index
      this.check = true // 打开弹框
      this.detailInfo = item
      this.id = item.id
      getTempateDetails(item.id).then(res => {
        if (res.code === '00000') {
          // console.log(res.data)
          this.temlateDetail = res.data
          this.data6 = res.data.contractTypeDescriptions
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else {
          this.msgInfo('warning', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 点击选中的样式
    temType (item, index) {
      this.number = index
      this.detailInfo.active = true
      this.curSelectList.splice(0, 1, item)
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, top: 300 }) },
    // 点击弹框选择模板按钮
    selectTem () {
      this.number = this.number1
      this.curSelectList.splice(0, 1, this.detailInfo) // 添加当前项
      this.detailInfo.active = true // 当前项添加一个标识来控制选中项样式
      this.check = false // 打开弹框
    },

    // 下载
    down () {
      if (!this.$store.state.tenantId && localStorage.getItem('roleId') == 1) {
        this.msgInfo('error', '创建失败，未选择租户，请选择租户后重新尝试', true)
        return
      }
      this.$refs['formItem'].validate((valid) => {
        if (valid) {
          if (this.curSelectList.length ? this.curSelectList[0].id : '') {
            this.$Modal.confirm({
              content: localStorage.getItem('roleId') == 1 ? '<p>请确认所选租户是否正确？</p>' : '<p>新建成功后，应用名称、项目名称不可再修改，确定提交吗？</p>',
              onOk: () => {
                getDown(this.curSelectList.length ? this.curSelectList[0].id : '', this.arrContract.projectName).then((res) => {
                  let blob = new Blob([res], { type: 'application/zip' })
                  let downloadElement = document.createElement('a')
                  let href = window.URL.createObjectURL(blob)
                  downloadElement.href = href
                  downloadElement.download = this.arrContract.projectName
                  document.body.appendChild(downloadElement)
                  downloadElement.click()
                  document.body.removeChild(downloadElement)
                  window.URL.revokeObjectURL(href)
                  this.arrContract.contractSources = '1'
                  this.getNew()
                }).catch(error => {
                  if (error instanceof Blob) {
                    error.text().then(text => {
                      let msg = JSON.parse(text)
                      this.msgInfo('error', msg.message, true)
                    })

                  }
                })
              },
              onCancel: () => {
                // this.$Message.info('Clicked cancel')
              }
            })
          } else {
            this.msgInfo('warning', '请选择下载模板！', true)
          }
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },
    // 滚动
    handScroll (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        // console.log('到底了', fileType, this.codeTotalPages[fileType])
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    // 选择模板返回
    back () {
      this.check = false
    },
    // 新建合约请求
    getNew () {
      if (!this.$store.state.tenantId && localStorage.getItem('roleId') == 1) {
        this.msgInfo('error', '创建失败，未选择租户，请选择租户后重新尝试', true)
        return
      }
      this.arrContract.ownerTenantId = this.$store.state.tenantId // 新增租户字段id
      getnewContract(this.arrContract).then(res => {
        this.btnState = true
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.btnState = false
            this.msgInfo('info', res.message, true)
          }
        } else {
          this.btnState = false
          this.msgInfo('success', '新建成功！', true)
          this.arrContract.contractSources = ''
          // this.arrContract.contractName = ''// 隐藏合约名称字段
          this.arrContract.projectName = ''
          this.arrContract.contractReadableName = ''
          this.arrContract.brief = ''
          this.arrContract.languageType = ''
          this.single = false
          this.$router.push({
            name: 'contract_table'
          })
        }
      }).catch(error => {
        this.showFail()
        // this.msgInfo('info', error.message, true)
        // console.log('getContractPower.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    showSuccess () {
      this.isTip = true
      this.tipInfo = {
        status: 'success',
        imgUrl: successImg,
        // title: `${this.arrContract.alertTitle === '新建合约' ? '新建' : '修改'}成功`,
        content: '可前往详情页编辑合约、上传合约代码',
        cancelText: '返回列表',
        confirmText: '前往详情页'
      }
    },
    showFail (msg = '系统异常') {
      this.isTip = true
      this.tipInfo = {
        status: 'fail',
        imgUrl: failImg,
        title: `${this.arrContract.alertTitle === '新建合约' ? '新建' : '修改'}失败`,
        content: `由于${msg}原因${this.arrContract.alertTitle === '新建合约' ? '新建' : '修改'}失败请重试`,
        cancelText: '取消',
        confirmText: '重新提交'
      }
    }
  },
  watch: {
    'single': {
      handler (value) {
        if (this.single) {
          getTempate(this.ckeckeos).then(res => {
            // console.log(res.data)
            if (res.code === '00000') {
              this.Infolist = res.data
            } else if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        } else {
          this.number = -1
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
// 滚动条
.textarea-style {
  width: 820px;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}

.choose {
  // display: grid;
  // grid-template-columns: repeat(4, minmax(1px, 1fr));
  display: flex;
  width: 80%;
  height: 250px;
  overflow-y: scroll;
  // display: flex;
  flex-wrap: wrap;
  li {
    width: 230px;
    height: 110px;
    border-radius: 5px;
    box-sizing: border-box;
    position: relative;
    margin: 15px 1% 0 15px;
    .dd {
      border: 1px solid;
      width: 230px;
      border-radius: 5px;
      height: 110px;
      overflow: hidden;
      box-sizing: border-box;
      padding: 0 10px;
      transition: all 0.5s;
      position: absolute;
      top: 0;
    }
    // position: relative;
    .check_btn {
      cursor: pointer;
      color: #2d8cf0;
      font-size: 13px;
    }
    .hide {
      height: 70px;
      display: -webkit-box; /**对象作为伸缩盒子模型展示**/
      -webkit-box-orient: vertical; /**设置或检索伸缩盒子对象的子元素的排列方式**/
      -webkit-line-clamp: 2; /**显示的行数**/
      overflow: hidden; /**隐藏超出的内容**/
    }
  }
  li:hover .dd {
    top: -15px;
    position: absolute;
    height: 140px;
    transition: all 0.5s;
    border: 1px solid rgba(74, 190, 132, 1);
  }
  li:hover {
    border: none;
  }
}

.orign_style {
  color: #2d8cf0;
  margin-left: 10px;
}

.mananger_footer {
  margin-top: 2%;
  display: flex;
  justify-content: flex-end;
  :nth-child(1) {
    margin-right: 1%;
  }
}
.active {
  position: relative;
  width: 81px;
  height: 93px;
  color: #4abe84;
  background-color: #fff;
  box-shadow: 0px 2px 7px 0px rgba(85, 110, 97, 0.35);
  border-radius: 7px;
  border: 1px solid rgba(74, 190, 132, 1);
}
.active:before {
  content: "";
  position: absolute;
  right: 0;
  bottom: 0;
  border: 17px solid #4abe84;
  border-top-color: transparent;
  border-left-color: transparent;
}
.active:after {
  content: "";
  width: 5px;
  height: 12px;
  position: absolute;
  right: 6px;
  bottom: 6px;
  border: 2px solid #fff;
  border-top-color: transparent;
  border-left-color: transparent;
  transform: rotate(45deg);
}
// 查看详情模板样式
.cz_table {
  margin-top: 3%;
  h4 {
    margin-bottom: 1%;
  }
}
.describe {
  margin: 1% 0;
}
.management_middle {
  margin-top: 1%;
}
.filesss {
  height: 32px;
  // overflow: hidden;
  .file_btnsss {
    float: right;
  }
}

.footerBtn {
  box-sizing: border-box;
  padding-left: 14%;
}
.select_style_title {
  font-size: 18px;
}
.mandatory {
  /deep/.ivu-form-item-label::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 14px;
    color: #ed4014;
  }
}
</style>
