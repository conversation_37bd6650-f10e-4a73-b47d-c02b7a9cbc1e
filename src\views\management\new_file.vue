<template>
  <div>
    <div style="width：auto;margin-left: 10px;">
      <Card style="width: auto;">
        <!-- <p class="info-title" style="margin:10px 0 15px 10px;"><span class="bs">基本信息</span></p> -->
        <div class="info-title addflex">
          <div>
            <div class="bs"></div>
            <span>基本信息</span>
          </div>
        </div>
        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="140">
        <FormItem label="IPFS网络名称：" prop="name">
            <Input style="width:400px"  v-model="formValidate.name" placeholder="请输入IPFS网络名称"/>
        </FormItem>
        <FormItem label="描述：" prop="desc">
            <Input style="width:500px;height:110px" v-model="formValidate.desc" maxlength="50" show-word-limit type="textarea" :rows="5" placeholder="请输入主要业务描述"/>
        </FormItem>
          <FormItem label="资源池命名空间：" prop="rason">
          <Select v-model="formValidate.rason" placeholder="请选择资源池命名空间" style="width: 200px">
            <Option v-for="item in rasonList" :value="item.namespaceId" :key="item.namespaceId">{{ item.ipfsNamespace }}</Option>
            <!-- <Option v-for="item in subChainList" :value="item.value" :key="item.value">{{ item.name }}</Option> -->
          </Select>
        </FormItem>
        <!-- <FormItem>
            <Button type="primary" @click="handleSubmit('formValidate')">Submit</Button>
            <Button @click="handleReset('formValidate')" style="margin-left: 8px">Reset</Button>
        </FormItem> -->
    </Form>
      </Card>
    </div>
     <Card style="margin:20px 5px 10px 10px;">
      <div class="node" style="width：auto;margin:10px 5px 0px 10px;">
        <div class="info-title">
          <span class="bs"></span><span>节点</span>
          <Button ghost type="success" @click="add" icon="md-add" style="float:right;margin-top:-5px;">配置节点</Button>
        </div>
        <edit-table-mul :columns="columns" v-model="tableData" :key="transferKey" style="margin-top:20px;"></edit-table-mul>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[5,10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;margin:10px 0;" />
      </div>
    </Card>
    <Modal :draggable="true" v-model="addModal" width="500" :title="alertTitle" :z-index="1000" sticky :mask-closable="false" :footer-hide='true' @on-cancel="handleReset('nodeConfigured')">
        <Form ref="nodeConfigured" :model="nodeConfigured" :rules="noderuleValidate" :label-width="140">
        <FormItem label="节点名称：" prop="nodeName">
            <Input style="width:150px"  v-model="nodeConfigured.nodeName" placeholder="请输入节点名称"/>
        </FormItem>
        <FormItem label="硬盘：" prop="nodeDisk">
            <Input style="width:150px"  v-model="nodeConfigured.nodeDisk" placeholder=""/>Gi
        </FormItem>
         <!-- <FormItem label="CPU最大限制值：" prop="nodeLimitCpu">
            <Input style="width:150px"  v-model="nodeConfigured.nodeLimitCpu" placeholder=""/>m
        </FormItem> -->
        <FormItem label="CPU：" prop="nodeCpu">
            <Input style="width:150px"  v-model="nodeConfigured.nodeCpu" placeholder="200~1000"/>m
        </FormItem>
         <!-- <FormItem label="内存最大限制值：" prop="nodeLimitMemory">
            <Input style="width:150px"  v-model="nodeConfigured.nodeLimitMemory" placeholder=""/>Mi
        </FormItem> -->
        <FormItem label="内存：" prop="nodeMemory">
            <Input style="width:150px"  v-model="nodeConfigured.nodeMemory" placeholder="200~1000"/>Mi
        </FormItem>
        <FormItem>
            <Button @click="handleReset('nodeConfigured')" >取消</Button>
            <Button style="margin-left: 8px" type="primary" @click="handleSubmit('nodeConfigured')">确定</Button>
        </FormItem>
    </Form>
    </Modal>
    <div style="margin-left: 46%;margin-top: 3%;">
       <Button @click="handleipfsReset('formValidate')" >取消</Button>
        <Button type="primary" style="margin-left: 8px" @click="handleipfsSubmit('formValidate')" :loading="this.onloading">确定</Button>

    </div>
  </div>
</template>
<script>
import EditTableMul from '_c/edit-table-mul'
import { addIpfs, namespaceList } from '@/api/contract'
import { isNumber } from '../../lib/check'
export default {
  components: {
    EditTableMul
  },
  data () {
    const validateipfstName = (rule, value, callback) => {
      let re = /^[a-z0-9]{1,20}$/
      let reg = /^[a-z]/
      if (!reg.test(value.slice(0, 1))) {
        callback(new Error('只能以小写字母开头'))
      } else if (!re.test(value)) {
        callback(new Error('只能输入数字和小写字母'))
      } else {
        callback()
      }
    }
    // cpu
    const validateAccountCpu = (rule, value, callback) => {
      let numValue = Number(value)
      if (!isNumber(value)) {
        callback(new Error('请在区间内输入正整数'))
      } else if (numValue >= 200 && numValue <= 1000) {
        callback()
      } else {
        callback(new Error('最小输入200,最大不能超过1000'))
      }
    }
    // 内存
    const validateAccountNodeMemory = (rule, value, callback) => {
      let numValue = Number(value)
      if (!isNumber(value)) {
        callback(new Error('请在区间内输入正整数'))
      } else if (numValue >= 200 && numValue <= 1000) {
        callback()
      } else {
        callback(new Error('最小输入200,最大不能超过1000'))
      }
    }
    // 硬盘
    const validateAccountNodeDisk = (rule, value, callback) => {
      let numValue = Number(value)
      if (!isNumber(value)) {
        callback(new Error('请在区间内输入正整数'))
      } else if (numValue <= 1000) {
        callback()
      } else {
        callback(new Error('最大输入1000'))
      }
    }
    // // cpu最大限制值
    // const validatenodeLimiCpu = (rule, value, callback) => {
    //   let numValue = Number(value)
    //   if (!isNumber(value)) {
    //     callback(new Error('请在区间内输入正整数'))
    //   } else if (numValue >= 200 && numValue <= 1000) {
    //     callback()
    //   } else {
    //     callback(new Error('最大输入1000'))
    //   }
    // }
    // // 内存最大限制值
    // const validatenodeLimitMemory = (rule, value, callback) => {
    //   let numValue = Number(value)
    //   if (!isNumber(value)) {
    //     callback(new Error('请在区间内输入正整数'))
    //   } else if (numValue >= 200 && numValue <= 1000) {
    //     callback()
    //   } else {
    //     callback(new Error('最大输入1000'))
    //   }
    // }
    return {
      onloading: false,
      rasonList: [],
      nodeConfigured: {
        nodeName: '',
        nodeDisk: '',
        nodeMemory: '',
        nodeCpu: '',
        id: 0
        // nodeLimitMemory: '',
        // nodeLimitCpu: ''
      },
      noderuleValidate: {
        nodeName: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, max: 20, message: '不能超过20位', trigger: 'blur' },
          { trigger: 'blur', validator: validateipfstName }
          // {
          //   type: 'string',
          //   pattern: /^[0-9a-zA-Z]*$/g, /// ^\w+$/
          //   message: '格式应为0-9a-zA-Z',
          //   trigger: 'blur'
          // }
        ],
        nodeCpu: [
          {
            required: true,
            validator: validateAccountCpu,
            trigger: 'blur'
          }
        ],

        nodeDisk: [
          {
            required: true,
            validator: validateAccountNodeDisk,
            trigger: 'blur'
          }
        ],
        nodeMemory: [
          {
            required: true,
            validator: validateAccountNodeMemory,
            trigger: 'blur'
          }
        ]
        // nodeLimitCpu: [
        //   {
        //     required: true,
        //     validator: validatenodeLimiCpu,
        //     trigger: 'blur'
        //   }
        // ],
        // nodeLimitMemory: [
        //   {
        //     required: true,
        //     validator: validatenodeLimitMemory,
        //     trigger: 'blur'
        //   }
        // ]
      },
      alertTitle: '配置节点',
      addModal: false,
      transferKey: 0,
      formValidate: {
        name: '',
        desc: '',
        rason: ''
      },
      ruleValidate: {
        name: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, max: 20, message: '不能超过20位', trigger: 'blur' },
          { trigger: 'blur', validator: validateipfstName }

        ],
        rason: [
          { required: true, message: '请选择一项', trigger: 'change', type: 'number' }
        ]
      },
      columns: [
        { key: 'nodeName', title: '节点名称' },
        { key: 'nodeAddress', title: 'IP及端口号' },
        { key: 'nodeDisk', title: '硬盘（Gi）' },
        { key: 'nodeCpu', title: 'CPU（m）' },
        { key: 'nodeMemory', title: '内存（Mi）', tooltip: true },
        {
          key: 'status',
          title: '节点状态'
        },
        { key: 'location', title: '创建时间', tooltip: true },
        {
          key: 'action',
          title: '操作',
          minWidth: 120,
          render: (h, params) => {
            return h('div', [
              h(
                'Poptip',
                {
                  props: {
                    transfer: true,
                    placement: 'top-end',
                    confirm: true,
                    title: '确认删除吗?',
                    'ok-text': '确认',
                    'cancel-text': '取消'
                  },
                  on: {
                    'on-ok': () => {
                      this.deletedData(params.index)
                    }
                  }
                },
                [
                  h(
                    'Button', {
                      props: { type: 'text', size: 'small' },
                      style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' }
                    }, '删除'
                  )
                ]
              ),
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    this.editDetails(params)
                  }
                }
              }, '编辑')
            ]

            )
          }
        }
      ],
      tableData: [],
      tablePageParam: {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      }
    }
  },
  methods: {
    handleipfsReset () {
      this.$router.push({
        name: 'ipfs_network'
      })
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    // ipfs新建提交
    handleipfsSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.onloading = true
          let newipfs = {
            ipfsName: this.formValidate.name,
            ipfsBrief: this.formValidate.desc,
            ipfsNodeList: this.tableData,
            namespaceId: this.formValidate.rason
          }
          addIpfs(newipfs).then(res => {
            // console.log(res)
            if (res.code !== '00000') {
              if (res.code === '500') {
                this.msgInfo('error', res.message, true)
              } else {
                this.msgInfo('warning', res.message, true)
                this.onloading = false
              }
            } else {
              this.msgInfo('success', res.message)
              this.onloading = false
              this.$router.push({
                name: 'ipfs_network'
              })
            }
          }).catch(error => {
            // console.log('addChainDeploy.error===>', error)
            this.msgInfo('error', error.message, true)
          })
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },
    editDetails (data) {
      this.alertTitle = '节点修改'
      this.addModal = true
      this.nodeConfigured = {
        nodeName: data.row.nodeName,
        nodeDisk: data.row.nodeDisk,
        nodeMemory: data.row.nodeMemory,
        nodeCpu: data.row.nodeCpu,
        index: data.index,
        id: data.row.id
      }
    },
    //
    add () {
      this.addModal = true
      this.alertTitle = '配置节点'
    },
    // 删除
    deletedData (index) {
      this.tableData.splice(index, 1)
    },
    // 配置节点
    handleSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          const { index, ...info } = this.nodeConfigured
          if (this.alertTitle === '配置节点') {
            if (this.tableData.length === 0) {
              this.tableData.forEach((item, index) => { item.id = index + 1 })
              this.tableData.push(info)
              this.tablePageParam = {
                pagetotal: this.tablePageParam.pagetotal + 1,
                pageSize: 5,
                pageIndex: 1
              }
              this.$Message.success('配置节点成功!')
              this.addModal = false
              this.$refs[name].resetFields()
            } else {
              let nodeName = this.tableData.some((item) => item.nodeName === info.nodeName)
              if (nodeName) {
                this.msgInfo('error', '节点名称不允许重复', true)
              } else {
                this.tableData.push(info)
                this.tableData.forEach((item, index) => { item.id = index + 1 })
                this.tablePageParam = {
                  pagetotal: this.tablePageParam.pagetotal + 1,
                  pageSize: 5,
                  pageIndex: 1
                }
                this.$Message.success('配置节点成功!')
                this.addModal = false
                this.$refs[name].resetFields()
              }
            }
          } else {
            let filterlist = this.tableData.filter((item) => {
              return item.id !== this.nodeConfigured.id
            })
            let nodeNameedit = filterlist.some((item) => item.nodeName === info.nodeName)
            if (nodeNameedit) {
              this.msgInfo('error', '节点名称不允许重复', true)
            } else {
              this.tableData.splice(index, 1, info)
              this.$Message.success('修改节点成功!')
              this.$refs[name].resetFields()
              this.addModal = false
            }
          }
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
        }
      })
    },
    handleReset (name) {
      this.$refs['nodeConfigured'].resetFields()
      this.addModal = false
    },

    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData(this.arrDetails.chainId)
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData(this.arrDetails.chainId)
    }
  },
  mounted () {
    namespaceList().then(res => {
      if (res.code !== '00000') {
        if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else {
          this.msgInfo('warning', res.message, true)
        }
      } else {
        this.rasonList = res.data
      }
    }).catch(error => {
      // console.log('addChainDeploy.error===>', error)
      this.msgInfo('error', error.message, true)
    })
  }

}
</script>

<style lang="less" scoped>
/deep/.ivu-input-type-textarea{
  height: 200px;
}
.bs {
  float: left;
  width: 6px;
  height: 18px;
  background: #19c3a0;
  opacity: 1;
  border-radius: 3px;
}
span {
  padding-left: 6px;
}
.info-title {
  font-size: 16px;
  font-weight: bold;
  vertical-align: middle;
  height: 18px;
  font-family: "Microsoft YaHei";
  line-height: 18px;
  color: #333333;
  margin: 10px 0 25px 0px;
  // &.addflex {
  //   display: flex;
  //   justify-content: space-between;
  //   // .btns {
  //   //   button {
  //   //     margin-right: 20px;
  //   //   }
  //   // }
  // }
}
</style>
