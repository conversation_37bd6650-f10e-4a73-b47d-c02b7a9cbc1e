import axios from './index'

// 创建链节点
export const addBlock<PERSON>hain = (params) => {
  return axios.request({
    url: '/cmbaas/autodeploy/block/chain/addBlockChain',
    data: params,
    method: 'POST'
  })
}
// 主机列表
export const findCmeosNodeLableList = (params) => {
  return axios.request({
    url: '/cmbaas/autodeploy/nodelable/findCmeosNodeLableList',
    data: params,
    method: 'POST'
  })
}
// 查询日志
export const getLogList = (Id) => {
  return axios.request({
    url: '/cmbaas/autodeploy/log/getLogList?chainId=' + Id,
    method: 'POST'
  })
}
// 初始化节点
export const initnetWork = (chainId) => {
  return axios.request({
    url: '/cmbaas/autodeploy/audit/initnetWork?chainId=' + chainId,
    method: 'GET'
  })
}
// 创建区块链网络
export const createNetwork = (params) => {
  return axios.request({
    url: '/cmbaas/autodeploy/audit/createNetwork',
    data: params,
    method: 'POST'
  })
}
// 新增节点
export const addCmeosNode = (formItem) => {
  console.log(formItem);
  return axios.request({
    url: '/cmbaas/autodeploy/audit/addCmeosNode',
    data: {
      chainId: formItem.chainId,
      nodeName: formItem.nodeNameg,
      nodeAddress: formItem.nodeAddressg,
      nodeApiPort: formItem.nodeApiPort,
      nodeType: formItem.nodeTypeKey,
      nodeP2pPort: formItem.nodeP2pPort,
      location: formItem.location,
      provinceCode: +formItem.provinceCode, // 省code
      cityCode: +formItem.cityCode, // 市code
      provinceName: formItem.provinceName, // 省名称
      cityName: formItem.cityName, // 市名称
      diskDir: '/cmeos' + formItem.diskdir, // 磁盘挂载
      status: 'ENABLE',
      id: formItem.nodeId
    },
    method: 'POST'
  })
}
// 修改节点
export const updateNetwork = (formItem) => {
  return axios.request({
    url: '/cmbaas/autodeploy/audit/updateNetwork',
    data: {
      chainId: formItem.chainId,
      nodeName: formItem.nodeNameg,
      nodeAddress: formItem.nodeAddressg,
      nodeApiPort: formItem.nodeApiPort,
      nodeType: formItem.nodeTypeKey,
      nodeP2pPort: formItem.nodeP2pPort,
      location: formItem.location,
      provinceCode: +formItem.provinceCode, // 省code
      cityCode: +formItem.cityCode, // 市code
      provinceName: formItem.provinceName, // 省名称
      cityName: formItem.cityName, // 市名称
      diskDir: formItem.diskdir, // 磁盘挂载
      status: formItem.statusKey,
      id: formItem.nodeId
    },
    method: 'POST'
  })
}
// 查询ip
export const nodelablegetList = () => {
  return axios.request({
    url: '/cmbaas/autodeploy/nodelable/getList',
    data: {},
    method: 'GET'
  })
}

// 添加主机
export const addCmeosNodelable = (params) => {
  return axios.request({
    url: '/cmbaas/autodeploy/nodelable/addCmeosNodelable',
    data: params,
    method: 'POST'
  })
}
// 修改主机
export const editCmeosNodelable = (params) => {
  return axios.request({
    url: '/cmbaas/autodeploy/nodelable/editCmeosNodelable',
    data: params,
    method: 'POST'
  })
}

// 主机详情
export const findCmeosNodelableById = (Id) => {
  return axios.request({
    url: '/cmbaas/autodeploy/nodelable/findCmeosNodelableById?id=' + Id,
    method: 'GET'
  })
}
// 删除主机
export const deleteById = (params) => {
  return axios.request({
    url: '/cmbaas/autodeploy/nodelable/deleteById',
    data: params,
    method: 'POST'
  })
}

// 智慧中台数据信息
export const unitZhzt = (params) => {
  return axios.request({
    url: '/cmbaas/autodeploy/unit/zhzt',
    data: params,
    method: 'POST'
  })
}
// 重启节点
export const restartCmeosNode = (chainId, data) => {
  return axios.request({
    url: '/cmbaas/autodeploy/audit/restartCmeosNode',
    data: {
      chainId: chainId,
      nodeName: data.nodeName,
      nodeAddress: data.nodeAddress,
      nodeApiPort: data.nodeApiPort,
      nodeType: data.nodeTypeKey,
      nodeP2pPort: data.nodeP2pPort,
      location: data.location,
      diskDir: data.diskDir, // 磁盘挂载
      status: data.statusKey,
      id: data.nodeId
    },
    method: 'POST'
  })
}
// 重启节点
export const deleteCmeosNode = (data) => {
  return axios.request({
    url: '/cmbaas/autodeploy/audit/deleteCmeosNode',
    data: {
      nodeName: data.nodeName,
      id: data.nodeId
    },
    method: 'POST'
  })
}
