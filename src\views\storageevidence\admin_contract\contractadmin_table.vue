<template>
  <div>
    <div>

      <div class="top">
        <div style="line-height:30px">合约名称：</div>
        <Input placeholder="请输入合约名称" style="width: 250px; vertical-align: baseline; margin-right: 10px" v-model="search_value" />
        <div style="line-height:30px">链类型：</div>
        <Select v-model="chain_value" style="width: 200px;margin-right: 10px" placeholder="请选择链类型" @on-change="changechaincity">
          <Option v-for="item in chainList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
        <div style="line-height:30px">合约语言：</div>
        <Select v-model="language_value" style="width: 200px; margin-right: 10px" placeholder="请选择合约语言">
          <Option v-for="item in languageList" :value="item.enumKey" :key="item.enumKey">{{ item.enumValue }}</Option>
        </Select>

        <Button style="margin-left:1%" type="primary" icon="ios-search" @click.native="information">查询</Button>
        <Button style="margin-left:1%" type="primary" ghost icon="md-sync" @click.native="reset">重置</Button>
      </div>
      <Table :columns="tableList" :data="tableData" stripe style="margin-top:1%">
        <template slot-scope="{ row, index }" slot="action">
          <Button type="text" size="small" style="margin-right: 5px; color: #3D73EF;border: 1px solid #3D73EF" @click="details(index)">详情</Button>
          <Button type="text" v-if="row.flag" size="small" style="margin-right: 5px; color: #3D73EF;border: 1px solid #3D73EF" @click="contracttemplate(row)">上架合约模板</Button>
          <Button type="text" v-else size="small" style="margin-right: 5px; color: #cccccc;border: 1px solid #cccccc" @click="contracttemplate(row)" disabled>上架合约模板</Button>
          <Button type="text" size="small" style="margin-right: 5px; color: #3D73EF;border: 1px solid #3D73EF" @click="contractmarket(row)">上架合约市场</Button>
        </template>
      </Table>
      <Page placement='top' :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px" />
    </div>
    <!-- 上架合约模板 -->
    <Putaway ref="child" />
    <PutawayAgora ref="agora" />
  </div>
</template>

<script>
import Putaway from './putaway.vue'
import PutawayAgora from './putaway_agora.vue'
import { getlibraryTable } from '@/api/contract'
export default {
  name: 'contractadmin_table',
  components: {
    Putaway,
    PutawayAgora
  },
  data () {
    return {
      language_value: '', // 合约语言
      languageList: [],
      search_value: '', // 合约名称
      chain_value: '', // 链类型
      flag: false,
      chainList: [
        {
          value: 'EOS',
          label: 'EOS'
        },
        {
          value: 'BOS',
          label: 'BOS'
        },
        {
          value: 'CMEOS',
          label: 'CMEOS'
        },
        {
          value: 'ChainMaker',
          label: 'ChainMaker'
        }
      ],
      tablePageParam: { pageIndex: 1, pageSize: 10, pagetotal: 0 },
      tableList: [
        { key: 'contractName', title: '合约名称', tooltip: true },
        { key: 'chainType', title: '链类型', tooltip: true },
        { key: 'languageType', title: '合约语言', tooltip: true },
        { key: 'tenantName', title: '所属租户', tooltip: true },
        { key: 'chainName', title: '所属链', tooltip: true },
        { key: 'createTime', title: '创建时间', tooltip: true },
        {
          slot: 'action',
          title: '操作',
          align: 'left',
          minWidth: 200
        }
      ],
      tableData: []
    }
  },

  mounted () {
    this.getTableList()
  },

  methods: {
    changechaincity (value) {
      this.language_value = ''
      if (value === 'EOS' || value === 'BOS') {
        this.languageList = [
          { enumKey: 'C++', enumValue: 'C++' }
        ]
      } else if (value === 'CMEOS') {
        this.languageList = [
          { enumKey: 'C++', enumValue: 'C++' },
          { enumKey: 'JS', enumValue: 'Java Script' }
        ]
      } else if (value === 'ChainMaker') {
        // GO、C++、rust、tinygo、solidity
        this.languageList = [
          { enumKey: 'GO', enumValue: 'GO' },
          { enumKey: 'C++', enumValue: 'C++' },
          { enumKey: 'RUST', enumValue: 'RUST' },
          { enumKey: 'TINYGO', enumValue: 'TINYGO' },
          { enumKey: 'SOLIDITY', enumValue: 'SOLIDITY' }
        ]
      } else {
        this.languageList = []
      }
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    // 获取列表
    getTableList () {
      let listdata = {
        languageType: this.language_value,
        contractName: this.search_value,
        chainType: this.chain_value,
        pageParam: this.tablePageParam
      }
      getlibraryTable(listdata).then((res) => {
        if (res.code === '00000') {
          let tabledata = res.data.records.map(item => {
            return {
              ...item,
              languageType: item.languageType === 'JS' ? 'JavaScript' : item.languageType
            }
          })
          this.tableData = tabledata
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index // 当前页
      this.getTableList()
    },
    // 展示每页显示数据
    pageSizeChange (size) {
      this.tablePageParam.pageSize = size
      this.getTableList()
    },
    // 搜索
    information () {
      this.getTableList()
    },
    // 重置
    reset () {
      this.language_value = ''
      this.search_value = ''
      this.chain_value = ''
      this.tablePageParam = { pageIndex: 1, pageSize: 10 }
      this.getTableList()
    },
    // 详情
    details (index) {
      this.$router.push({
        name: 'contractadmin_details',
        params: { name: `${this.tableData[index].chainType}`, contractId: `${this.tableData[index].contractId}`, languagetype: `${this.tableData[index].languageType === 'JavaScript' ? 'JS' : this.tableData[index].languageType}` }
      })
    },
    // 上架合约市场
    contractmarket (row) {
      this.$refs.agora.putaway = true
      this.$refs.agora.gainSound(row)
      this.$refs.agora.init()
    },
    // 上架合约模板
    contracttemplate (row) {
      this.$refs.child.putaway = true
      this.$refs.child.gainSound(row)
      this.$refs.child.init()
    }
  }
}
</script>

<style lang="less" scoped>
.top {
  display: flex;
}
</style>
