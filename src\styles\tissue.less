.node-title{
    font-size:22px;
    color:#333;
    font-family: Microsoft YaHei;
    font-weight: 400;
    margin-top:43px;
}
.node-title::before{
    content: '';
    display:inline-block;
    width: 6px;
    height: 22px;
    background:  #337DFF;
    //border-radius: 2px;
    margin-right:6px;
    transform: translateY(2px);
}
.node-list-nav .nav-box{
    color: #999999;
    font-size: 18px;
    box-sizing: border-box;
}
.node-list-nav .nav-box /deep/ .el-col{
    text-align: center;
    color: #666666;
    font-size: 18px;
    //padding:10px 0 10px;
}
.node-list-nav .nan-item .nav-box:hover{
    border: 2px solid rgba(31, 130, 232, 0.33);
    -moz-box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6; -webkit-box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6; box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6;
}
.node-list-nav .nan-item .nav-box{
    background: #fff;
    margin-top:10px;
    border-radius: 4px;
    overflow:hidden;
    display: flex;
    align-items: center;
}

.node-list-nav .nan-item .status span{
    background:rgba(9, 223, 192, 0.2);
    color:#00ADA2;
    padding:10px 26px;
    border-radius: 4px;
    cursor: default;
}
// .node-list-nav .nan-item .edit span:nth-child(1){
//     background:#00ADA2;
//     color:#fff;
//     cursor: pointer;
// }
// .node-list-nav .nan-item .edit span:nth-child(2){
//     background:#1973CC;
//     color:#fff;
//     cursor: pointer;
// }
.node-list-nav .nan-item .edit span.chcek{
    background:#1973CC;
    color:#fff;
    cursor: pointer;
}
.node-list-nav .nan-item .wait span{
    background:rgba(253, 187, 45, 0.2);
    color:#FCB827;
    cursor: default;
}
.node-list-nav .nan-item .waring span{
    background: rgba(255, 0, 43,0.4);
    color:#fff;
    cursor: default;
}
.node-list-nav .nav-box .el-col div span{
    //padding:10px 30px;
    //border-radius: 4px;
}
// .node-list-nav .nav-box .el-col .operaBtn span:nth-child(1){
//     background:#00ADA2;
//     color:#fff;
//     cursor: pointer;
// }
// .node-list-nav .nav-box .el-col .operaBtn span:nth-child(2){
//     background:#A7BFE8;
//     color:#fff;
//     cursor: pointer;
// }
// .node-list-nav .nav-box .el-col .operaBtn span:nth-child(3){
//     background:#1973CC;
//     color:#fff;
//     cursor: pointer;
// }
.alertBox{
    position:fixed;
    top:0;
    right:0;
    bottom:0;
    left:0;
    background:rgba(0,0,0,0.5);
}
.alertBox .addTissue{
    position:absolute;
    width:700px;
    background:#fff;
    // margin:131px auto;
    overflow:hidden;
    border-radius: 4px;
    left:50%;
    top:50%;
    transform: translate(-50%, -50%);
}
.alertTop{
    height:80px;
    background-size: cover;
    color:#fff;
    padding:30px;
}
.alertTop .tit{
    font-size:20px;
    font-weight: bold;
}
.closeIcon{
    cursor: pointer;
}
// .node-list-nav .nan-item .edit .class1{
//     background:#00ADA2 !important;
//     color:#fff !important;
//     cursor: pointer !important;
// }
.node-list-nav .nav-box .el-col .edit span.class1 {
    background:#00ADA2 !important;
    color:#fff !important;
    cursor: pointer !important;
}
.node-list-nav .nav-box .el-col .edit span.class2 {
    background:#1973CC !important;
    color:#fff !important;
    cursor: pointer !important;
}
// .node-list-nav .nan-item .edit .class2{
//     background:#1973CC !important;
//     color:#fff !important;
//     cursor: pointer !important;
// }
.node-list-nav .nav-box .el-col .operaBtn span.class1{
    background:#00ADA2 !important;
    color:#fff !important;
    cursor: pointer !important;
}
.node-list-nav .nav-box .el-col .operaBtn span.class2{
    background:#A7BFE8 !important;
    color:#fff !important;
    cursor: pointer !important;
}
.node-list-nav .nav-box .el-col .operaBtn span.class3{
    background:#1973CC !important;
    color:#fff !important;
    cursor: pointer !important;
}
