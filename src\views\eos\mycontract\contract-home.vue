<template>
  <div class="contract-home">
    <keep-alive>
      <div class="contract-home-item">
        <!-- <div class="contract-title">
          <b>智能合约</b>
          <Button icon="ios-arrow-forward" size="small" @click="isContractFlag=false">合约广场</Button>
        </div> -->
        <!-- <Button class="right-button" icon="ios-arrow-forward" size="small" @click="changeFlag(false)">合约广场</Button> -->
        <Tabs value="name1">
          <TabPane label="合约列表" name="name1">
            <ContractTable />
          </TabPane>
          <!-- <TabPane label="获取的共享合约" name="name2">
            <GetShare />
          </TabPane> -->
        </Tabs>
      </div>
    </keep-alive>
  </div>
</template>

<script>
import ContractTable from './contract-table.vue'
import GetShare from './get-share.vue'
export default {
  name: 'contract_home',
  components: {
    ContractTable,
    GetShare
  },
  data () {
    return {
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    changeFlag (flag) {
      this.$router.push({
        name: 'contract_area'
      })
    }
  },
  mounted () {

  }
}
</script>

<style lang="less" scoped>
.contract-home {
  .contract-home-item {
    position: relative;
    .contract-title {
      display: flex;
      align-items: center;
      b {
        margin-right: 10px;
      }
    }
  }
  //
  .right-button {
    position: absolute;
    right: 0px;
    top: 6px;
    z-index: 10;
  }
}
</style>
