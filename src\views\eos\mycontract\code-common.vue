<template>
 <Collapse v-model="filepanel" simple accordion :key="transferKey" @on-change="getCodeList">
        <Panel :name="cppName">
          cpp文件名：{{ cppName }}
          <div slot="content">
            <Spin v-if="cppTopLoading">
             <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
             <div>上一页加载中。。。</div>
            </Spin>
            <textarea class="textarea-style" v-if="arrChainCode.cppObj.fileContent" v-html="arrChainCode.cppObj.fileContent" readonly @scroll="handScroll($event, 'cpp', cppName)"></textarea>
            <div v-else class="no-records">该文件无记录</div>
            <Spin v-if="cppBottomLoading">
              <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
              <div>下一页加载中。。。</div>
            </Spin>
          </div>
        </Panel>
        <Panel :name="item" v-for="item in hppNames" :key="item">
          hpp文件名：{{ item }}
          <div slot="content">
            <Spin v-if="hppTopLoading">
             <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
             <div>上一页加载中。。。</div>
             </Spin>
            <textarea class="textarea-style" readonly v-if="arrChainCode.hppObj.fileContent" v-html="arrChainCode.hppObj.fileContent" @scroll="handScroll($event, 'hpp', item)"></textarea>
             <div v-else class="no-records">该文件无记录</div>
             <Spin v-if="hppBottomLoading">
             <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
             <div>下一页加载中。。。</div>
             </Spin>
          </div>
        </Panel>
  </Collapse>
</template>

<script>
import { getContractChaincode } from '@/api/data'
export default {
  props: {
    cppName: {
      type: String,
      default: ''
    },
    hppNames: {
      type: Array,
      default () {
        return []
      }
    },
    contractId: {
      type: Number
    },
    uploadVersion: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      cppBottomLoading: false,
      cppTopLoading: false,
      hppBottomLoading: false,
      hppTopLoading: false,
      loadingMsg: 'Loading',
      transferKey: 0,
      filepanel: ['codeDetails1'],
      arrChainCode: { cppObj: {}, hppObj: {}, abiObj: {} },
      chainCodePageParam: {
        cpp: { pagetotal: 0, pageSize: 1000, pageIndex: 1 },
        hpp: { pagetotal: 0, pageSize: 1000, pageIndex: 1 }
      },
      codeTotalPages: { cpp: 0, hpp: 0 }
    }
  },
  methods: {
    handScroll (e, fileType, name) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        if (parseInt(this.chainCodePageParam[fileType].pageIndex) < parseInt(this.codeTotalPages[fileType])) {
          if (fileType === 'cpp') {
            this.cppBottomLoading = true
          } else {
            this.hppBottomLoading = true
          }
          return new Promise(resolve => {
            this.timer = setTimeout(() => {
              ++this.chainCodePageParam[fileType].pageIndex
              this.getCode(name, fileType, 'bottom')
              resolve()
            }, 50)
          })
        } else {
          clearTimeout(this.timerStamp)
          let that = this
          this.timerStamp = setTimeout(() => {
            let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
            if (height < 3 && height > 0) {
              that.msgInfo('info', '到底了！', true)
            }
          }, 500)
        }
      } else if (e.srcElement.scrollTop === 0) {
        if (parseInt(this.chainCodePageParam[fileType].pageIndex) !== 1) {
          if (fileType === 'cpp') {
            this.cppTopLoading = true
          } else {
            this.hppTopLoading = true
          }
          return new Promise(resolve => {
            this.timer = setTimeout(() => {
              --this.chainCodePageParam[fileType].pageIndex
              this.getCode(name, fileType, 'top')
              resolve()
            }, 50)
          })
        } else {
          this.msgInfo('info', '已到首页！', true)
        }
      }
    },
    getCode (fileName, filetype, val) {
      // this.uploadVersion = 'U_211013_145212_012212'
      // this.contractId = 108
      getContractChaincode(this.contractId, this.uploadVersion, fileName, this.chainCodePageParam[filetype]).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.loadingMsg = res.message
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.arrChainCode[filetype + 'Obj'] = res.data
          ++this.transferKey
          this.codeTotalPages[filetype] = res.data.pages
        }
      }).catch(error => {
        this.loadingMsg = error.message
        this.msgInfo('error', error.message, true)
      })
      if (val === 'bottom') {
        if (filetype === 'cpp') {
          this.cppBottomLoading = false
        } else {
          this.hppBottomLoading = false
        }
      } else if (val === 'top') {
        if (filetype === 'cpp') {
          this.cppTopLoading = false
        } else {
          this.hppTopLoading = false
        }
      }
    },
    getCodeList (name) {
      if (name[0]) {
        this.chainCodePageParam = {
          cpp: { pagetotal: 0, pageSize: 1000, pageIndex: 1 },
          hpp: { pagetotal: 0, pageSize: 1000, pageIndex: 1 }
        }
        let fileType = name[0].substring(name[0].length - 3, name[0].length)
        this.getCode(name[0], fileType, name[0].length)
      }
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) }
  },
  beforeDestroy () {
    clearInterval(this.timer)
    this.timer = null
  }
}
</script>

<style lang="less" scoped>
.textarea-style {
    width:620px;
    height:350px;
    border-color: #ffffff;
    color: #515A6E;
    background-color:#f8f8f9;
    resize:none;
}
textarea {
  overflow-y: scroll;
  padding:10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar{
    width : 10px;  /*高宽分别对应横竖滚动条的尺寸*/
    min-height: 1px;
  }
::-webkit-scrollbar-thumb{
    border-radius   : 10px;
    background-color: rgb(135, 158, 235);
  }
  .no-records{
    text-align: center;
    color: #848485;
  }
</style>
