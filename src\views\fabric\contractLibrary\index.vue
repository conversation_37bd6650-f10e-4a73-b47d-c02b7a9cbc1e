<template>
  <div class="hyk">
    <!-- 合约库头部、 -->
    <div class="info">
      <div class="infoTitle">
        <img :src="infoIcon" class="infoIcon" />
        <span class="infotext">合约信息</span>
      </div>
      <div class="btnList">
        <el-button type="primary" class="green-btn btn-bt" @click="uploading">上传合约</el-button>
      </div>
    </div>
    <!-- 合约库列表内容、 v-if="contractLibrary_btn_uploading"-->
    <!-- <div class="btnList">
      <el-button type="primary" class="blue-btn btn-bt" @click="uploading">上传合约</el-button>
    </div> -->
    <div class="hyk_content table-wrapper">
      <el-row class="nav-box">
      <el-col :span="6"><div class="">名称</div></el-col>
      <el-col :span="6"><div class="">版本</div></el-col>
      <el-col :span="6"><div class="">文件名称</div></el-col>
      <el-col :span="6"><div class="">操作</div></el-col>
    </el-row>
      <template v-if="listData.length > 0">
      <div class="nan-item" v-for="(item, index) in listData" :key="index">
        <el-row class="nav-box">
          <el-col :span="6"
            ><div class="">
              <span>{{ item.Name }}</span>
            </div>
          </el-col>
          <el-col :span="6"
            ><div class="">
              <span>{{ item.Version }}</span>
            </div></el-col
          >
          <el-col :span="6"
            ><div class="">
              <span>{{ item.FileName }}</span>
            </div></el-col
          >
          <el-col :span="6"
            ><div class="">
              <span class="handle-btn" @click="lookCodeFun(item.Name,item.Version)">查看合约代码</span>
              <span class="handle-btn" @click="issue(item)">发布</span>
              <span
                class="handle-btn delete-btn"
                :class="!item.CanOperation ? 'disable' : ''"
                @click="remove(item)"
                >删除</span
              >
            </div></el-col>
        </el-row>
      </div>
    </template>
      <template v-else>
        <div class="none" ref="noThing">
          <i class="el-icon-loading" v-if="paddingText == '数据请求中...'"></i>
          {{paddingText}}
        </div>
      </template>
    </div>
    <div class="pagination" v-if="total.length > 0">
      <Page
      :total="total.length"
      :current.sync="pageIndex"
      @on-change="handleCurrentChange"
      :page-size="pageSize"
      :page-size-opts="[10,20,40,60,100]"
      show-total show-elevator show-sizer
      @on-page-size-change="handleSizeChange"
      style="text-align:right;"/>
    </div>
    <!-- <div class="pagination" v-if="total.length > 0">
      <pagination
        @toHandleSizeChange="handleSizeChange"
        @toHandleCurrentChange="handleCurrentChange"
        @toJumpFirstPage="jumpFirstPage"
        @toJumpLastPage="jumpLastPage"
        :fTotal="total.length"
        :fBtnStartDisabled="btnStartDisabled"
        :fBtnEndDisabled="btnEndDisabled"
        :fPageIndex="pageIndex"
        :fZys="zys"
      >
      </pagination>
    </div> -->
    <!-- 上传合约弹窗 -->
    <transition name="fade">
      <div class="alertBox" v-if="uploadDialogShow">
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">上传智能合约
              <i class="el-icon-cursor el-icon-close fr"  @click="closeDialog"></i>
            </div>
<!--            <div class="closeIcon close_img">-->
<!--              <img :src="closeImg" alt="" />-->
<!--            </div>-->

          </div>
          <div class="alert_box">
            <el-form
              ref="hyForm"
              :model="hyForm"
              :rules="rules"
            >
              <div class="selectBox">
                <div class="evertModule">
                  <el-form-item  label="合约名称：" prop="upName">
                    <el-input
                      v-model="hyForm.upName"
                      maxLength="16"
                      placeholder="请输入"
                      @focus ='focusupName'
                    ></el-input>
                    <span class="userTip" v-if="upNameTip">4-16个小写字母或数字组成</span>
                    <span class="userTip el-form-item__error" v-if="isReName">合约名称已存在</span>
                  </el-form-item>

                </div>
                <div class="evertModule">
                  <el-form-item label="版本号：" prop="upVersion">
                    <el-input
                      v-model="hyForm.upVersion"
                      placeholder="请输入"
                      @focus ='focusUpVersion'
                    ></el-input>
                    <span class="userTip" v-if="upVersionTip">v开头+三段数字，如v1.0.0</span>
                  </el-form-item>
                </div>
                <div class="evertModule upfile">
                  <el-form-item prop="file" label="上传文件：">
                    <el-upload
                      ref="upfile"
                      class="upload-demo"
                      drag
                      v-model="hyForm.file"
                      :on-change="handleChange"
                      :on-remove="handleRemove"
                      action="auto"
                      accept=".go"
                      :file-list="fileList"
                      :auto-upload="false"
                      :class="{isred:firstFile || ycts || firstFileMS || firstFileBack} "
                    >
                    <img src="../../../assets/image/upload.png" class="uploadImg">
                      <div class="el-upload__text">
                        选择或拖拽.go文件到此处  <span class="uploadText"> 点击上传</span><br />
                        每次只可上传一个文件，文件大小不能超过1M <br/>
                        暂不支持使用依赖包的智能合约
                      </div>
                    </el-upload>
                        <div class="fileError" v-if="firstFile">
                      文件格式不正确
                    </div>
                    <div class="fileError" v-if="ycts">
                      安全检测异常，请重新上传！
                      <span class="errYc" @click="ycjc">查看异常原因</span>
                    </div>
                    <div class="fileError" v-if="firstFileMS">
                      文件过大，请重新上传
                    </div>
                     <div class="fileError" v-if="firstFileBack">
                      文件过大，请重新上传
                    </div>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <div class="btn-row upload-btn-wrap">
            <Button type="primary" class="sure-btn" @click="upLoad('hyForm')">上传</Button>
            <Button type="text" class="border-btn" @click="cancel">取消</Button>
          </div>
        </div>
      </div>
    </transition>
    <!-- 发布弹窗 -->
    <transition name="fade">
      <div class="alertBox" v-if="issueDialogShow">
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">发布智能合约
              <i class="el-icon-cursor el-icon-close fr"  @click="closeDialog"></i>
            </div>
          </div>
          <div class="alert_box fbIpt">
            <el-form
              ref="hyForm"
              :rules="FbRules"
              :model="hyForm"
            >
              <div class="selectBox">
                <div class="flex-align-item view">
                  <div class="tit">合约名称：</div>
                  <div class="titValue">{{hyForm.Name}}</div>
                </div>
                <div class="flex-align-item view">
                  <div class="tit">版本号：</div>
                  <div class="titValue">{{hyForm.Version}}</div>
                </div>
<!--                <div class="evertModule">-->
<!--                  <el-form-item class="view" label="合约名称：">-->
<!--                    <el-input-->
<!--                      class="ipt"-->
<!--                      v-model="hyForm.Name"-->
<!--                      disabled="disabled"-->
<!--                    ></el-input>-->
<!--                  </el-form-item>-->
<!--                </div>-->
<!--                <div class="evertModule">-->
<!--                  <el-form-item class="view" label="版本号：">-->
<!--                    <el-input-->
<!--                      class="ipt"-->
<!--                      v-model="hyForm.Version"-->
<!--                      disabled="disabled"-->
<!--                    ></el-input>-->
<!--                  </el-form-item>-->
<!--                </div>-->
                <div class="evertModule">
                  <el-form-item label="发布位置：" prop="value">
                    <el-select
                      @focus="handleFocusTip"
                      v-model="hyForm.value"
                      placeholder="请选择区块链"
                      popper-class="area_popper"
                      @change="$forceUpdate()"
                    >
                      <el-option
                        v-for="(item, idx) in selectOption"
                        :key="idx"
                        :label="item.ChainDisplayName"
                        :value="item.Id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <div class="btn-row">
             <Button class="sure-btn" type="primary" @click="hyFb('hyForm')">发布</Button>
            <Button type="text" class="border-btn" @click="cancel">取消</Button>
          </div>
        </div>
      </div>
    </transition>
    <!-- 删除弹窗 -->
    <transition name="fade">
      <div v-if="deleteDialogShow" class="alertBox confirmBox">
        <div class="addTissue">
          <div class="delText">
            <!-- <i class="el-icon-warning"></i> -->
            <img src="../../../assets/image/el-warning.png" class="iconImage">
            <span class="title">您确定要删除智能合约 {{ hyName }} 吗？</span>
          </div>
          <div class="confirmBottom">
            <Button type="primary" class="sure-btn" @click="confirm">确定</Button>
            <Button class="border-btn" @click="cancel">取消</Button>
          </div>
        </div>
      </div>
    </transition>
    <!-- 查看异常原因 -->
    <transition name="fade">
      <div v-if="ycjcjg" class="alertBox">
        <div class="addTissue">
          <div class="alertTop flex">
            <div class="tit">异常原因</div>
            <div class="closeIcon close_img" @click="closeDialog()"><img :src="closeImg" alt=""></div>
          </div>
          <div class="ycText">
            <p v-for="(item,index) in ycMessage" :key="index">
                {{item.key+':'+item.value}}
            </p>
          </div>
        </div>
      </div>
    </transition>
    <transition v-if="isCode" name="fade">
      <div class="alertBox">
        <div class="addTissue" style="width:1000px;max-width:1000px">
          <div class="alertTop">
            <div class="tit">查看合约代码
              <i class="el-icon-cursor el-icon-close fr" @click="closeInstall"></i>
            </div>
            <!--            <div class="closeIcon close_img" @click="closeInstall('04')">-->
            <!--              <img :src="closeImg" alt="">-->
            <!--            </div>-->
          </div>
          <div class="alert_box">
            <div class="selectBox">
              <p style="white-space: pre-wrap;" class="">{{soundCode}}</p>
            </div>
          </div>

        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import {forbidden,version} from "@/utils/index.js";
import {
  getPlatChaincodeList,
  issuePlatChaincode,
  uploadPlatChaincode,
  deletePlatChaincode,
  getFilterChainList,
  chaincodeFunc,checkChainCodeName
} from "@/api/baascore/contractLibrary";
export default {
  mixins: [forbidden],
  data() {
    // 校验版本号
    const yzVersion = (rule, value, callback) => {
      this.upVersionTip=false;
      if (!value) {
        callback(new Error("请输入版本号"));
      } else {
        if (!version(value)) {
          callback(new Error("版本号格式错误"));
        } else {
          callback();
        }
      }
    };
    return {
      contractLibrary_btn_uploading:false,
      contractLibrary_btn_del:false,
      contractLibrary_btn_issue:false,
      // 成功失败弹窗
      isShowIcon: false,
      countTime: 2,
      countState: "",
      countText: "",
      closeImg: require('@/assets/image/close.png'),
      fileList: [], //上传的文件
      ycMessage: [], //异常信息
      ycts: false, //是否展示异常
      ycjcjg: false, //异常检测结果
      firstFile: false, //判断第一次拖拽
      firstFileMS: false, //判断文件大小格式是否ok
      firstFileBack:false,//后台返回的文件大小判断值
      hasFile: false, //判断文件格式是否go
      btnStartDisabled: false, //用来判断首页尾页按钮是否禁用
      btnEndDisabled: false, //用来判断首页尾页按钮是否禁用
      zys: 0, //判断总页数
      fileName: {}, //文件名称
      hyName: "", //删除合约的文字提示
      rempveData: {}, //点击删除弹框确认按钮需要的数据
      // 弹窗form
      issueDialogShow: false, //发布弹窗
      deleteDialogShow: false, //删除弹窗
      uploadDialogShow: false, //上传弹窗
      // titleMessage: "", //form表头
      hyForm: {}, //form表单
      // dialogBtn: "发布", //弹窗下面的按钮判断
      selectOption: [], //添加组织下拉option
      total: 0,
      paddingText:"数据请求中...",
      pageSize: 10,
      pageIndex: 1,
      infoIcon: require("@/assets/chainManage_images/overview/infoIcon.png"),
      // 智能合约库总条数
      listData: [],
      // 发布验证输入
      FbRules: {
        value: [
          { required: true, message: "请选择发布位置", trigger: "change" }
        ]
      },
      // 上传验证输入
      rules: {
        upName: [
          { required: true, validator: this.validaHyName, trigger: "blur" }
          // { min: 4, max: 16, message: "长度在 4 到 16 个字符", trigger: "blur" }
        ],
        // upName: [
        //   { required: true, message: "请输入合约名称", trigger: "blur" },
        //   { min: 4, max: 16, message: "长度在 4 到 16 个字符", trigger: "blur" }
        // ],
        upVersion: [{ required: true, trigger: "blur", validator: yzVersion }],
        file: [
          {
            required: true,
            message: "请上传文件",
            trigger: "change"
          }
        ],
      },
      upNameTip:true,
      upVersionTip:true,
      isCode:false,//合约代码弹框
      soundCode:'',
      isReName:false,
    };
  },

  // 组件部分
  components: {
  },
  watch: {},
  mounted() {
  },
  created() {
    this.getHyList();
    // this.contractLibrary_btn_uploading = this.elements["contractLibrary:btn_uploading"];
    // this.contractLibrary_btn_del = this.elements["contractLibrary:btn_del"];
    // this.contractLibrary_btn_issue = this.elements["contractLibrary:btn_issue"];
    if(this.$route.query){
      this.uploadDialogShow = this.$route.query.isUpload
    }
  },
  computed: {
    // 过滤名字
    filterName(name) {
      return function(name) {
        if (name.indexOf("/") != -1) {
          name = name.substring(name.lastIndexOf("/") + 1);
          return name;
        } else {
          return name;
        }
      };
    }
  },
  methods: {
    $forceUpdate(){
      this.$forceUpdate();
    },
    //格式化代码
    traversalObject(arrObject) {
      for (const key in arrObject) {
        if (typeof (arrObject[key]) == 'object' || typeof (arrObject[key]) == 'array') {
          this.traversalObject(arrObject[key])
        }else {
          var nerObj = {
          key:key,
          value:arrObject[key]
          }
          this.ycMessage.push(nerObj)
        }

      }
    },
    // 上传文件异常检测
    ycjc() {
      this.uploadDialogShow = false;
      this.ycjcjg = true;
    },
    //效验合约名称 ^(?!\d)[a-zA-Z0-9\u4e00-\u9fa5]+$
    validaHyName(rule, value, callback) {
      this.upNameTip=false;
      //let re = /[a-z0-9]{4,16}/;
      let re = /^[0-9a-z]{4,16}$/;
      if (value == "" || value == undefined) {
        callback(new Error("请输入合约名称"));
      } else if (!re.test(value)) {
        callback(new Error("合约名称格式错误"));
      } else {
        var params = {
          ChainCodeName:value
        }
        checkChainCodeName(params).then(res =>{
          if(res.code == 200) {
            callback();
          }else {
            callback(new Error("合约名称已存在"));
          }
        })

      }
    },
    // 判断文件拖拽的不是.go给个错误提示
    onDrag: function(e) {
      e.stopPropagation();
      e.preventDefault();
    },
    onDrop: function(e) {
      e.stopPropagation();
      e.preventDefault();
      let dt = e.dataTransfer;
      // this.hasFile = false;
      for (var i = 0; i !== dt.files.length; i++) {
        let FileExt = dt.files[i].name.replace(/.+\./, "");
        let flag = ["go"].includes(FileExt);
        let errMsg = document.querySelector(".upfile");
        let upfileErrMsg = errMsg.querySelector(".el-form-item__error");
        if (!flag) {
          if (upfileErrMsg == null) {
            this.firstFile = true;
          } else {
            this.firstFile = false;
            upfileErrMsg.innerHTML = "文件格式不正确";
          }
          this.hyForm.file = "";
          this.fileName = {};
          this.fileList = [];
        } else {
          this.firstFile = false;
        }
        this.hasFile = !flag;
      }
    },
    // 上传文件按钮
    upLoad(formName) {
      if (this.hyForm.file == "") {
        this.firstFile = false;
        let errMsg = document.querySelector(".upfile");
        let upfileErrMsg = errMsg.querySelector(".el-form-item__error");
        if (upfileErrMsg) {
          upfileErrMsg.innerHTML = "请上传文件";
        }
      }
      this.$refs[formName].validate(valid => {
        if (this.hasFile || this.firstFileMS) {
          return;
        }
        if (valid) {
          let msg = {
            // msgType: "uploadPlatChaincode",
            // params: {
              ChainType: "fabric",
              ChainCodeName: this.hyForm.upName,
              Version: this.hyForm.upVersion,
              file: this.fileName
            // }
          };
          let formdata = new FormData();
          formdata.append("msgType", "platChaincode#uploadPlatChaincode");
          formdata.append("ChainType", "fabric");
          formdata.append("ChainCodeName", this.hyForm.upName);
          formdata.append("Version", this.hyForm.upVersion);
          formdata.append("file", this.fileName);
          uploadPlatChaincode(formdata).then(res => {
            if (res.code == 200) {
              // this.isShowIcon = true;
              // this.countText = "上传成功";
              // this.countState = "success";
              this.$message.success('上传成功！')
              this.uploadDialogShow = false;
              this.getHyList();
            } else if (res.code == 4414) {
              this.ycts = true;
              this.ycMessage = [];
              this.traversalObject(res.data)
            }else if(res.code == 4406) {
              this.$message.error(res.message)
            }else if(res.code == 1028) {
              this.firstFileBack = true
            }else if(res.code == 4417) {
              this.isReName = true
            }
            else {
              // this.isShowIcon = true;
              // this.countText = "上传失败，请重试！";
              // this.countState = "error";
              this.$message.error('上传失败，请重试！')
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 合约发布确定按钮
    hyFb(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let msg = {
            // msgType: "issuePlatChaincode",
            // params: {
              ServiceId: this.hyForm.value,
              ChainCodeName: this.hyForm.Name,
              Version: this.hyForm.Version
            // }
          };
          issuePlatChaincode(msg).then(res => {
            if (res.code == 200) {
              // this.isShowIcon = true;
              // this.countText = "发布成功";
              // this.countState = "success";
              this.$message.success('发布成功！')
              this.issueDialogShow = false;
              this.getHyList();
            } else if(res.code == 4413) {
              this.$message.warning(res.message)
              this.issueDialogShow = false;
              this.getHyList();
            }else if(res.code == 4409) {
              this.$message.error(res.message)
            }else if(res.code == 4230) {
              this.$message.error("发布操作失败，请稍后再试！");
            }
            else{
              // this.isShowIcon = true;
              // this.countText = "发布失败，请重试！";
              // this.countState = "error";
              this.$message.error('发布失败，请重试！')
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //  获取发布时下拉数据
    getHyX(item) {
      let msg = {
          ChainCodeName: item.Name,
          Version: item.Version
        };
      getFilterChainList(msg).then(res => {
        if (res.code == 200) {
          if(res.data && res.data.length > 0) {
            this.selectOption = res.data;
            this.issueDialogShow = true;
          }else {
            this.$message.warning('该合约暂无可发布的链');
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    // focus select 清空校验信息
    handleFocusTip(){
      this.$refs["hyForm"].clearValidate();
    },
    // 后台没分页再次做分页
    getListData() {
      // this.zys = Math.ceil(this.total.length / this.pageSize); //获取总页数
      // this.pageIndex > this.zys ? this.pageIndex = this.zys : '';
      // 调用混入方法判断首页尾页按钮禁用的方法
      // this.forbidden(this.zys, this.pageIndex);
      this.listData = this.total.slice(
        (this.pageIndex - 1) * this.pageSize,
        this.pageSize * this.pageIndex
      );
    },
    //获取合约列表
    getHyList() {
      getPlatChaincodeList().then(res => {
        if (res.code == 200) {
          this.total = res.data.ChainCodes || [];
          if (this.total.length == 0) {
            this.paddingText = "暂无数据";
          }
          // 前端再次调用分页
          this.getListData();
        } else {
          this.paddingText = "暂无数据";
          // this.isShowIcon = true;
          // this.countText = "数据获取失败，请重新加载！";
          // this.countState = "error";
          this.$message.error('数据获取失败，请重新加载！')
        }
      });
    },
    // 文件上传
    handleChange(file, fileList) {
      let FileExt = file.name.replace(/.+\./, "");
      let isOKM = file.size / 1024 / 1024 < 1;
      let flag = ["go"].includes(FileExt);
      this.hasFile = !flag;
      if (!flag) {
        this.firstFile = true;
      } else {
        this.firstFile = false;
      }
       if (!isOKM) {
        this.firstFileMS = true;
        this.$refs["hyForm"].clearValidate(["file"]);
      } else {
        this.firstFileMS = false;
      }
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]];
        this.$refs["hyForm"].clearValidate(["file"]);
        this.hyForm.file = this.fileList[0];
        this.fileName = this.fileList[0].raw;
        // this.hyForm.file = fileList;
        // this.fileName = fileList[0].raw;
      }
    },
    // 文件移除
    handleRemove(file, fileList) {
      this.ycts = false;
      this.firstFile = false;
      this.hyForm.file = "";
      this.firstFileMS = false;
      this.firstFileBack = false;
      this.fileName = {};
      this.fileList = [];
      if (fileList.length == 0) {
        this.hasFile = false;
        this.$refs["hyForm"].validateField(["file"]);
      }
      // this.ycts = false;
      // this.firstFile = false;
      // this.hyForm.file = "";
      // this.fileName = {};
    },
    // 点击发布按钮
    issue(item) {
      //获取发布时下拉数据
      this.hyForm.value = ""; //清空上次选中的值
      // 拿到点击的哪行并赋值到form里面
      this.hyForm = item;
      this.getHyX(item);
    },
    // 查看合约代码
    lookCodeFun(name,version) {
      // this.isCall = false
      // this.isCode = true
      this.chaincodeFunc(name,version)
    },
    //取消弹窗+初始化弹窗
    closeInstall() {
        this.isCode = false
    },
    // 合约源码
    chaincodeFunc(name,version) {
      let msg = {
        // msgType: 'chaincodeFunc',
        // params:{
        "ChainCodeName": name,
        //"ServiceId": this.ServiceId, //暂时不传递此字段
        "Version": version
        // }
      }
      chaincodeFunc(msg).then((res) => {
        if(res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if(res.data[0].Body) {
            // this.isCall = false
            this.isCode = true
            this.soundCode = res.data[0].Body
          }
        } else if(res.code == 4403) {
            this.$message.error(res.message)
        }
        else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '查看合约代码失败，请重新查看！'
          this.$message.error('查看合约代码失败，请重新查看!')
          // this.isCall = true
          // this.isCode = false
        }
      })
    },
    // 点击上传合约按钮
    uploading() {
      this.firstFileMS = false;
       this.firstFileBack = false;
      this.ycts = false;
      this.hasFile = false;
      this.hyForm = {
        ChainCodeName: "",
        Version: "",
        file: ""
      };
      this.fileList = [];
      this.uploadDialogShow = true;
      this.upNameTip=true;//显示用户提示信息
      this.upVersionTip=true;
      this.$nextTick(() => {
        //拖拽的监听
        let dropbox = document.querySelector(".upfile");
        dropbox.addEventListener("dragenter", this.onDrag, false);
        dropbox.addEventListener("dragover", this.onDrag, false);
        dropbox.addEventListener("drop", this.onDrop, false);
      });
    },
    // 点击删除按钮
    remove(item) {
      this.deleteDialogShow = true;
      this.rempveData = item;
      this.hyName = this.rempveData.Name;
    },
    // 取消按钮关闭弹窗
    cancel() {
      this.isReName = false
      this.firstFile = this.issueDialogShow = this.deleteDialogShow = this.uploadDialogShow = false; //发布弹窗//删除弹窗//上传弹窗
    },
    // 关闭按钮关闭弹窗
    closeDialog() {
      this.isReName = false
      this.firstFile = this.firstFile = this.issueDialogShow = this.deleteDialogShow = this.uploadDialogShow = this.ycjcjg = false; //发布弹窗//删除弹窗//上传弹窗
    },
    // 删除确认按钮
    confirm() {
      let msg = {
       // msgType: "deletePlatChaincode",
       // params: {
          ChainType: "fabric",
          ChainCodeName: this.rempveData.Name,
          Version: this.rempveData.Version
      //  }
      };
      deletePlatChaincode(msg).then(async res => {
        if (res.code == 200) {
          // this.isShowIcon = true;
          // this.countText = "删除成功！";
          // this.countState = "success";
          this.$message.success('删除成功！')
          // 防止删除的时候在第二页或者其他页第一个问题
          if (this.total.length - 1 == this.pageSize) {
            this.pageIndex -= 1;
          }
          this.deleteDialogShow = false;
          this.getHyList();
        }else if(res.code == 4408) {
          this.$message.error(res.message)
        }else if(res.code == 4409) {
          this.$message.error(res.message)
        }else if(res.code == 4412) {
          this.$message.error(res.message)
        }
        else {
          // this.isShowIcon = true;
          // this.countText = "删除失败，请重试！";
          // this.countState = "error";
          this.$message.error('删除失败，请重试！')
        }
      });
    },
    // 每页数量
    handleSizeChange(val) {
      this.pageSize = val;
      this.getListData();
    },
    // 页码
    handleCurrentChange(val) {
      this.pageIndex = val;
      this.getListData();
    },
    // 首页按钮
    jumpFirstPage(val) {
      this.pageIndex = val;
      this.handleCurrentChange(val);
    },
    // 尾页按钮
    jumpLastPage(val) {
      this.pageIndex = val;
      this.handleCurrentChange(this.pageIndex);
    },
    focusupName(){
      this.upNameTip = true;
      this.isReName = false
      this.$refs["hyForm"].clearValidate(["upName"]);
    },
    focusUpVersion(){
      this.upVersionTip = true;
      this.$refs["hyForm"].clearValidate(["upVersion"]);
    },
  }
};
</script>
<style  rel="stylesheet/less" lang="less" scoped>
  @import "../../../styles/common/modal.less";
.errYc {
  // font-size: 16px;
  font-size: 14px;

  font-weight: 400;
  color: #1973cc;
  cursor: pointer;
}
.upload-btn-wrap .ivu-btn {
  height:auto;
  padding:4px 15px;
}
.ycText {
  // font-size: 20px;
  font-size: 14px;
  color: #333333;
  margin: 59px 80px;
  word-wrap: break-word;
}
.none {
  // font-size: 18px;
  font-size: 14px;
  text-align: center;
  line-height: 65px;
  color: #666666;
  background: #fff;
}
.info {
  width: 100%;
  margin: 0px 0px 10px;
  display: flex;
  justify-content: space-between;
  .infoTitle {
    // font-size: 20px;
    font-size: 14px;
    .infoIcon {
      width: 3px;
      height: 14px;
      margin-right:5px;
      vertical-align: middle;
    }
    .infotext {
      color: #333333;
      vertical-align: middle;
    }
  }
  .add-uploading-btn {
    width: 120px;
    height: 46px;
    // font-size: 18px;
    font-size: 14px;

    font-weight: 400;
    color: #ffffff;
    text-align: center;
    line-height: 46px;
    cursor: pointer;
    background: #337DFF;
    border-radius: 4px;
  }
}

// 合约库内容
.nan-item {
  margin: 5px 0px 15px 0px;
}
.nav-box {
  color: #999999;
  // font-size: 18px;
  font-size: 14px;
  box-sizing: border-box;
  // padding: 15px 0 15px !important;
  //height: 65px;
  //line-height: 65px;
}
.nav-box /deep/ .el-col {
  text-align: center;
  color: #999999;
  // font-size: 18px;
  font-size: 14px;
}
.nav-box .el-col div span {
  /*margin: 0 3px;*/
  /*cursor: default;*/
}
.nav-box .el-col div span.status {
  display: inline-block;
  width: 102px;
  height: 36px;
  line-height: 36px;
  background: #00ada2;
  border-radius: 4px;

  font-weight: 400;
  color: #ffffff;
  cursor: pointer;
}
.nav-box .el-col div span.status.remove {
  background: #a7bfe8;
}
/*.nav-box .el-col div span.status.disable {*/
  /*cursor: default;*/
/*}*/
.nan-item .nav-box:hover {
  // padding: 14px 0 14px !important;
  -moz-box-shadow: 2px 2px 6px #bbd1e6, -2px -2px 6px #bbd1e6;
  -webkit-box-shadow: 2px 2px 6px #bbd1e6, -2px -2px 6px #bbd1e6;
  box-shadow: 2px 2px 6px #bbd1e6, -2px -2px 6px #bbd1e6;
}
.nan-item .nav-box {
  // font-size: 18px;
  font-size: 14px;
  box-sizing: border-box;
  background: #fff;
  display: flex;
  align-items: center;
}
.nan-item .nav-box /deep/ .el-col {
  color: #666666;
}
// 页码
.pagination {
  /*margin: 20px 0px 20px;*/
  // display: flex;
  // justify-content: flex-end;
  // align-items: center;
}

.disable {
  pointer-events: none;
  /*background: #e4e4e4 !important;*/
}


.fileError {
  width: 100%;
  color: #F04134;
  // font-size: 16px;
  font-size: 14px;
  margin-left: 0px !important;
  text-align: left;
  padding-top: 4px;
}
.ivu-page{
  margin-top:10px;
}
/deep/ .select-down{
  margin-top:7px;
  left:0px !important;
}
/deep/ .el-input__icon{
  line-height: 32px;
}
</style>

