<!--
  aturun
  数据信息
  2021/10/21

-->
<template>
  <el-dialog
      class="dialog_sty see_transaction_info"
      title="数据信息"
      :visible.sync="Visible"
      width="700px"
      :close-on-click-modal="false"
      destroy-on-close
  @opened="open">
    <div class="dialog_content">
<!--      <pre>{{ JSON.stringify(textInfo, null, 4)  }}</pre>-->
      <el-table
          :data="tableData"
          style="width: 100%"
          height="500px"
          stripe
      >
        <el-table-column label="中文名称" width="120px">
          <template slot-scope="scope">
            {{fieldName[scope.row.labelEN]}}
          </template>
        </el-table-column>
        <el-table-column prop="labelEN" label="英文字段" width="200px"></el-table-column>
        <el-table-column prop="value" label="值" ></el-table-column>
      </el-table>

    </div>
  </el-dialog>
</template>

<script>
import * as api from "../api";
import {getReqFieldDef, getReqRecDetails} from "../api";

export default {
  name: "seeTransactionInfo",
  components: {},
  props:[],
  data() {
    return {
      tableTitle:{dd:'ff',gg:'ftt'},
      tableData:[],
      Visible:false,
      dataId:null,
      textInfo:{
      },
      text:'',
      fieldName:{},
      rules: {
        name: [
          {required: true, message: '请输入用户名称', trigger: 'blur'},
        ],
      }
    }
  },
  watch: {},
  created() {
this.getFieldDef()
  },
  mounted() {
  },
  methods: {
    open(){
      this.getBusinessRecordDetails()
    },
    getBusinessRecordDetails(){
      api.getReqRecDetails(
          {dataId:this.dataId}
      ).then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.tableData=[]
        this.textInfo=res.result
        for(let item in this.textInfo){
          this.tableData.push({labelEN:item,value:this.textInfo[item]})
        }
      }).catch(err=>{
        this.tableData=[]
      })
    },
    // 下划线转驼峰
    toHump(name) {
     return name.replace(/\_(\w)/g, function(all, letter){
        return letter.toUpperCase();
      });
     },
    getFieldDef(){
      api.getReqFieldDef().then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.fieldName=res.result
        let obj = {}
        for(let item in this.fieldName){
          obj[this.toHump(item)] = this.fieldName[item]
        }
        this.fieldName = obj
      })
    }
  },

}
</script>

<style lang="less" scoped>
.see_transaction_info{
  .dialog_content{
    height:500px;
    //overflow-x: scroll;
  }
}
</style>
