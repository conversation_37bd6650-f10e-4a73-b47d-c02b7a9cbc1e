<template>
  <div class="tenant_index">
    <keep-alive v-if="currentTab==='tenant_admin'">
      <TenantTable />
    </keep-alive>
    <keep-alive :exclude="excludeArr" v-else>
    <router-view/>
    </keep-alive>
  </div>
</template>

<script>
import TenantTable from './tenant-table.vue'
export default {
  name: 'tenant_index',
  components: {
    TenantTable
  },
  data () {
    return {
      excludeArr: ['tenant_details']
    }
  },
  computed: {
    currentTab () {
      // console.log('this.$route.name:', this.$route.name)
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
