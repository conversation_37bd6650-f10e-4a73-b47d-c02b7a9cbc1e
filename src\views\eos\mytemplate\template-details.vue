<template>
  <div>
    <div class="management_box">
      <div class="management_top">

        <div class="file">
          <h5>{{this.content.contractType?this.content.contractType:''}}</h5>
          <div class="width:190px">
            <Button type="success" @click="chainClick" label="large" ghost class="file_btn">模板预览</Button>
            <Icon type="ios-close" size='50' @click="shutdown" />
          </div>

        </div>

      </div>

      <div class="management_middle">
        <Row class="top_center">
          <Col span="4"> 发布者：<span>{{ this.Interface.userLoginId}}</span></Col>
          <Col span="5">链类型：<span>{{this.content.chainType}}</span></Col>
          <Col span="5">方法：<span>{{this.Interface.methods}}个</span></Col>
          <Col span="5">大小：<span>{{this.Interface.size}}</span></Col>
        </Row>
        <Row class="middle_center">
          <Col span="4">合约语言：<span>{{this.Interface.languageType==='JS'?'Java Script':this.Interface.languageType}}</span></Col>
          <Col span="5">发布时间：<span>{{this.content.createTime}}</span></Col>
          <Col span="5">更新时间：<span>{{this.content.updateTime}} </span></Col>
          <Col span="5">适用场景：<span>{{this.content.scene}}</span></Col>
        </Row>
      </div>
      <div class="describe">
        <h5>描述</h5>
        <span>{{this.content.description}}</span>
      </div>

      <div class="cz_table">
        <h5>接口描述</h5>
        <!-- <Table :columns="columns7" :data="data6"></Table> -->
        <edit-table-mul :columns="descriptionList" v-model="descriptionData" :key="transferKey"></edit-table-mul>
      </div>

    </div>
    <!-- 模板预览弹框 -->
    <Modal v-model="chaincode" title="查询合约链码" width='900px' footer-hide>
      <!-- <p style="margin-bottom:20px">上传版本号：sajdhsiu_zsxds_111</p> -->
      <div v-if="isSingleCpp=='0'">
        <Layout>
          <Sider hide-trigger :style="{background: '#fff'}">
            <Menu theme="light" width="auto" :open-names="['1']">
              <Submenu :name="key" v-for="(item,key) in cppsTitle" :key="key">
                <template slot="title">
                  <Icon type="ios-folder"></Icon>
                  {{key}}
                </template>
                <MenuItem :name="items.fileName" v-for="(items,i) in item" :key="i" @click.native="clickCpps(items.content)">{{items.fileName}}</MenuItem>
              </Submenu>
            </Menu>
          </Sider>
          <Content :style="{padding: ' 0 24px', minHeight: '280px', background: '#fff'}">
            <p>
              <textarea class="textarea-style" v-html="cppContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
            </p>
          </Content>
        </Layout>
      </div>
      <div v-else>
        <Collapse simple @on-change="colldata" accordion>
          <Panel :name="item.fileId" v-for="item in fileName" :key='item.id'>
            {{item.fileName.indexOf('.js') !== -1 ?item.fileName.substr(-2,2):item.fileName.substr(-3,3)}}文件名：{{item.fileName}}
            <!-- <p slot="content">{{CollContent.fileContent}}</p> -->
            <p slot="content">
              <textarea class="textarea-style" v-html="CollContent.fileContent" readonly @scroll="handScroll($event, 'hpp')"></textarea>
            </p>
          </Panel>
        </Collapse>
      </div>

    </Modal>
  </div>
</template>

<script>
import { TemplateDetails, getTempatePreview, getColl } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
export default {
  components: {
    EditTableMul
  },
  data () {
    return {
      Interface: {},
      content: this.$route.params.content, // 路由传过来的内容
      detailid: this.$route.params.listId, // 路由传过来的id
      chaincode: false, // 查询合约链码弹框
      transferKey: 0,
      fileName: [], // 模板预览文件名
      CollContent: {}, // 折叠面板内容
      // 接口描述列表
      descriptionList: [
        {
          title: '函数名（name）',
          key: 'funcName'

        },
        {
          title: '参数（inputs）',
          key: 'parameter'
        },
        {
          title: '简介（description）',
          key: 'description'
        }
      ],
      descriptionData: [],
      cppContent: '请选择要看的源码文件',
      cppsTitle: '',
      isSingleCpp: ''

      // // 分页
      // PageParam: {
      //   pageSize: 10,
      //   pageIndex: 1
      // }

    }
  },
  methods: {
    shutdown () {
      this.$router.push({
        name: 'template_table'
      })
    },
    // ok () {
    //   this.$Message.info('Clicked ok')
    // },
    // cancel () {
    //   // this.$Message.info('Clicked cancel')
    // },
    // 模板预览
    chainClick () {
      this.chaincode = true
      getTempatePreview(this.detailid).then(res => {
        if (res.code === '00000') {
          if (res.data) {
            this.fileName = res.data
            if (res.data[0].zipFileContent) {
              this.cppsTitle = res.data[0].zipFileContent

            }
          } else {
            this.chaincode = false
            this.msgInfo('error', '暂无源码', true)
          }

        } else {
          this.chaincode = false

          this.msgInfo('error', res.message, true)
        }

      }).catch(error => {
        this.chaincode = false
        this.msgInfo('error', error.message, true)
      })
      // let Chaininfo = {
      //   // contractId: this.this.detailid,
      //   // pageParam: this.PageParam
      // }
      // QueryChainCode(Chaininfo).then((res) => {

      // })
    },
    colldata (key) {
      // console.log(key[0])
      if (key[0]) {
        this.fileName.forEach(item => {
          if (item.fileId == key[0]) {
            let Colldata = {
              fileId: key[0],
              fileName: item.fileName,
              pageParam: {
                pageSize: 1,
                pageIndex: 10
              }
            }
            getColl(Colldata).then(res => {
              this.CollContent = res.data
            }).catch((error) => {
              this.msgInfo('error', error.message, true)
            })
          }
        })
      }
    },
    // 详情请求
    details (detailid) {
      let detailList = {
        contractModelId: this.detailid
      }
      TemplateDetails(detailList).then(res => {
        this.Interface = res.data

        this.descriptionData = res.data.contractTypeDescriptions
        this.isSingleCpp = res.data.isSingleCpp
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, top: 300 }) },
    // 滚动
    handScroll (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    clickCpps (value) {
      this.cppContent = value

    }
  },
  mounted () {
    if (!this.$route.params.listId) {
      this.$router.push({
        name: 'template_table'
      })
    } else {
      this.details()
    }
    // console.log(this.$route.params.content)
    // console.log(this.detailid)
  }
}
</script>
<style scoped lang="less">
/deep/.ivu-menu,
.ivu-menu-dark,
.ivu-menu-vertical,
.ivu-menu-opened,
.ivu-menu-submenu-title {
  background: #ffffff !important;
}
/deep/.ivu-menu-submenu-title {
  background: #ffffff !important;
}
/deep/.ivu-menu-vertical.ivu-menu-light:after {
  background: #ffffff;
}

.management_top {
  // border-bottom: 1px solid #bfbfbf;
  .file {
    height: 90px;
    h5 {
      font-size: 20px;
      line-height: 50px;
    }
    .file_btn {
      height: 50px;
      width: 104px;
      margin-top: 30%;
    }
    display: flex;
    justify-content: space-between;
  }
}

.management_middle {
  // border-bottom: 1px solid #bfbfbf;
  margin-bottom: 1%;
  .middle_center {
    // margin-bottom: 3%;
    margin-top: 3%;
    margin-left: 3%;
  }
  .top_center {
    display: flex;
    flex-flow: row wrap;
    margin-left: 3%;
    margin-top: 1%;
    margin-bottom: 3%;
    // .ivu-col {
    //   margin-right: 2%;
    // }
  }
}
.describe {
  h5 {
    font-size: 20px;
    height: 41px;
  }
}
.cz_table {
  margin-top: 3%;
  h5 {
    font-size: 20px;
    height: 41px;
    margin-bottom: 1%;
  }
}

// .ivu-collapse>.ivu-collapse-item>.ivu-collapse-header>i {
//     transition: transform .2s ease-in-out;
//     margin-right: 14px;
//     float: right ;
//     line-height: 37px;
// }
// .ivu-collapse-content {
//     color: #515a6e;
//     padding: 0 16px;
//     // background-color: #fff;

//     background: whitesmoke;
// }
//
// 滚动条
.textarea-style {
  width: 100%;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
</style>
