<template>
  <div class="survival_index">
  <keep-alive v-if="currentTab==='survival_direct'">
    <SurvivalDirect  />
  </keep-alive>
  <keep-alive :exclude="excludeArr" v-else>
  <router-view />
  </keep-alive>
  </div>
</template>

<script>
import SurvivalDirect from './survival_direct.vue'
export default {
  name: 'survival_index',
  components: {
    SurvivalDirect
  },
  data () {
    return {
      excludeArr: ['survival_details', 'new_deposit_certificate']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
  },
  mounted () {}
}
</script>
