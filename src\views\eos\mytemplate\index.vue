<template>
  <div class="template_table">
    <keep-alive>
      <templateTable v-if="currentTab==='template_table'" />
    </keep-alive>
    <router-view v-if="currentTab!=='template_table'" />
  </div>
</template>

<script>
import templateTable from './template-table.vue'
export default {
  name: 'template_table',
  components: {
    templateTable
  },
  data () {
    return {
      // excludeArr: ['tem_modify']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () { }
}
</script>
