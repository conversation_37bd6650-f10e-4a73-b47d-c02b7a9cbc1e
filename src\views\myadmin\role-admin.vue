<template>
  <div class="roleadmin">
    <div style="display: flex;justify-content: space-between;">
      <p>
        <Input style="width: 200px; vertical-align: baseline" placeholder="可输入角色名称查询" v-model="roleName" />
        <Button type="primary" @click="getTableData()" icon="ios-search" style="margin: 0 10px">查询</Button>
      </p>
      <p>
        <Button type="success" ghost @click="newRole" icon="md-add" :disabled="hasEditPermission">新建角色</Button>
      </p>
    </div>

    <edit-table-mul style="margin: 10px 0;" :key="transferKey+'edit'" :columns="columns" v-model="tableData" @on-edit="handleEdit"></edit-table-mul>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;" />
    <Modal v-model="modal" title="分配资源" :mask-closable="false">
      <Form ref="arrRoleModify" :rules="arrRoleItemRuleModify" :model="arrRoleModify">
        <FormItem label="父角色名称：" prop="parentName">
          <!-- <p style="line-height:30px;"> -->
          <!-- 父角色名称： -->
          <Select v-model="arrRoleModify.parentName" placeholder="请选择" style="width:490px;" label-in-value @on-change="getParentIdModify">
            <Scroll :on-reach-bottom="handleReachBottom" :distance-to-edge="[8,8]" :height="190" :loading-text="modifyTotal <= modifyParentList.length ? '已全部加载完成' : '加载中。。。。。。'">
              <Option v-for="item in modifyParentList" :tag="item.parentId" v-show="item.parentId !== 3" :value="item.parentName" :key="item.parentId">{{ item.parentName }}</Option>
              <p class="p-finish" v-if="modifyTotal <= modifyParentList.length">已全部加载完成</p>
              <!-- <p class="p-finish" style="font-size:8px;" v-else>
                下拉刷新
                <img style="margin-left:5px;" :src="imgUrl">
                </p> -->
            </Scroll>
          </Select>
        </FormItem>
        <!-- </p> -->
        <FormItem label="资源分配：" prop="resourceIdList">
          <br>
          <div class="scroll-div">
            <Tree v-if="dataTree && dataTree.length>0" ref="tree" :data="dataTree" show-checkbox multiple @on-check-change="checkResourceId"></Tree>
            <div class="data-none" v-else>
              <img :src="imagesurl">
              <p class="title-none" style="">暂无数据</p>
            </div>
          </div>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="cancelModify('arrRoleModify')">取消</Button>
        <Button type="primary" @click="ok" :disabled="hasEditPermission">确定</Button>
      </div>
    </Modal>
    <Modal v-model="modalrole" :title="arrRole.alertTitle" sticky :draggable="true">
      <div slot="close" @click="cancelClose">
        <Icon type="ios-close" />
      </div>
      <Form ref="arrRole" :rules="arrRoleItemRule" :model="arrRole" @submit.native.prevent>
        <div v-if="isOneStep">
          <FormItem label="角色名称：" prop="roleName">
            <Input placeholder="填写角色名称" maxlength="32" show-word-limit style="vertical-align:baseline;" v-model="arrRole.roleName" />
          </FormItem>
          <FormItem label="角色描述：" prop="brief">
            <Input v-model="arrRole.brief" maxlength="64" show-word-limit type="textarea" style="width:490px" :autosize="{minRows: 3,maxRows: 5}" placeholder="填写角色描述" />
          </FormItem>
          <!-- <p style="line-height:30px;"> 角色名称：<Input v-model="arrRole.roleName" placeholder="填写角色名称" style="width:490px"/> </p>
          <p style="line-height:30px;"> 角色描述：<Input v-model="arrRole.brief" type="textarea" style="width:490px" :autosize="{minRows: 3,maxRows: 5}" placeholder="填写角色描述"/></p> -->
        </div>
        <div v-else style="min-height:55vh">
          <FormItem label="父角色名称（新建角色的资源是父角色的子集）：" prop="parentName">
            <!-- <Select v-model="arrRole.parentName" @on-change="getParentId" placeholder="请选择" style="width:490px;height:auto">
              <Scroll :on-reach-bottom="handleReachBottom" :height="190" :loading-text="createTotal <= createParentList.length ? '已全部加载完成' : '加载中。。。。。。'">
                <Option v-for="item in createParentList" v-show="item.roleName !== arrRole.roleName && item.roleId !== 3" :value="item.roleName" :key="item.roleId">{{ item.roleName }}</Option>
                <p class="p-finish" v-if="createTotal <= createParentList.length">已全部加载完成</p>
              </Scroll>
            </Select> -->
            <Scroll-Option :vModel="arrRole.parentName" @change="getParentId" :styles="'width:490px;height:auto'" @renderBottom="handleReachBottom" :list="dealArr(createParentList)" :finishFlag="createTotal <= createParentList.length" />
          </FormItem>
          <FormItem label="资源分配：" prop="resourceIdList">
            <br>
            <!-- <input type="text" :v-model="arrRole.resourceIdList" disabled style="visibility: hidden"/> -->
            <div class="scroll-div">
              <Tree v-if="dataTree && dataTree.length>0" :data="arrRole.parentName ? dataTree:[]" show-checkbox multiple @on-check-change="(value) => this.checkResourceId(value,'new')"></Tree>
              <div class="data-none" v-else>
                <img :src="imagesurl">
                <p class="title-none" style="">{{arrRole.parentName ? '暂无数据' : '请先选择父⻆色'}}</p>
              </div>
            </div>
          </FormItem>
        </div>
      </Form>
      <div slot="footer">
        <Button @click="cancel('arrRole')">{{modelRoleText.cancel}}</Button>
        <Button type="primary" @click="okrole('arrRole')">{{modelRoleText.ok}}</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { getRoleTableData, getRoleAction, getRoleResources, editRoleTableData, checkName, getRoleparentList } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import ScrollOption from '_c/scroll-option'
import { localRead } from '@/lib/util'
export default {
  name: 'role_admin',
  components: {
    EditTableMul,
    ScrollOption
  },
  data () {
    const validateResourceIdList = (rule, value, callback) => {
      if (this.resourceIdList && this.resourceIdList.length > 0) {
        callback()
      } else {
        callback(new Error('不能为空'))
      }
    }
    return {
      roleName: '',
      imgUrl: require('@/assets/img/arrow.png'),
      isOneStep: true,
      modal: false,
      modalrole: false,
      transferKey: 0,
      newValue: '',
      modelRoleText: {
        cancel: '取消',
        ok: '下一步'
      },
      imagesurl: require('@/assets/img/null.png'),
      arrRole: { alertTitle: '新建角色', roleId: '', roleName: '', parentId: '', parentName: '', brief: '' },
      arrRoleModify: {
        roleId: '', parentName: '', resourceIdList: []
      },
      arrRoleItemRuleModify: {
        parentName: [{ required: true, message: '不能为空', trigger: 'change' }],
        resourceIdList: [{ required: true, trigger: 'change', validator: validateResourceIdList }]
      },
      arrRoleItemRule: {
        roleName: [{ required: true, message: '不能为空', trigger: 'blur' }],
        // brief: [{ required: true, message: '不能为空', trigger: 'blur' }],
        parentName: [{ required: true, message: '不能为空', trigger: 'change' }],
        resourceIdList: [{ required: true, trigger: 'change', validator: validateResourceIdList }]
      },
      tablePageParam: { pagetotal: 0, pageSize: 10, pageIndex: 1 },
      columns: [
        { key: 'roleName', title: '角色名称', tooltip: true },
        { key: 'parentName', title: '父角色名称', tooltip: true },
        { key: 'brief', title: '角色描述', tooltip: true },
        { // key: 'action',
          title: '操作',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' }, //, disabled: true
                style: {
                  marginRight: '8px',
                  color: '#3D73EF',
                  border: '1px solid #3D73EF',
                  display: params.row.parentId === 0 ? 'none' : 'black'
                },
                on: {
                  click: () => {
                    const datas = {
                      roleId: params.row.roleId,
                      parentId: params.row.parentId
                    }
                    console.log(datas);
                    getRoleResources(datas).then(res => {
                      if (res.code !== '00000') {
                        this.msgInfo('error', res.message, true)
                      } else {
                        this.arrRoleModify.roleId = params.row.roleId
                        this.dataTree = res.data
                        this.modal = true
                        this.resourceIdList = []
                        this.modifyParentList = []
                        const tablePageParam = { pagetotal: 0, pageSize: 10, pageIndex: 1 }
                        this.getRoleparentList({ roleId: params.row.roleId, pageParam: tablePageParam })
                        this.$nextTick(() => {
                          this.arrRoleModify.parentName = params.row.parentName
                        })
                      }
                    }).catch(error => {
                      this.msgInfo('error', error.message, true)
                    })
                  }
                }
              }, '分配资源'),
              h('Button', {
                props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                style: {
                  marginRight: '8px',
                  color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
                  border: this.hasEditPermission ? '1px solid #dcdee2' : '1px solid #3D73EF',
                  display: params.row.parentId === 0 ? 'none' : 'black',

                },
                on: {
                  click: () => {
                    this.editRole(params.index)
                  }
                }
              }, '修改角色')
            ])
          }
        }
      ],
      tableData: [],
      createParentList: [],
      createTotal: 0,
      modifyParentList: [],
      modifyTotal: 0,
      dataTree: [],
      resourceIdList: [],
      buttonProps: {
        // type: 'default',
        size: 'small'
      },
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  methods: {
    init () {
      this.resourceIdList = []
      this.dataTree = []
      this.arrRole = {
        alertTitle: '新建角色',
        RoleId: '',
        RoleName: '',
        // parentId: '',
        parentName: '',
        brief: ''
      }
      this.isOneStep = true
    },
    // 处理下拉选有过滤条件的，还有改造原数组，给数组元元素增加value和key属性适配组件
    dealArr (Arr) {
      return Arr.filter(item => item.roleId !== 3)
        .map(val => {
          val['value'] = val.roleName
          val['key'] = val.roleId
          return val
        })
    },
    handleReachBottom () {
      return new Promise(resolve => {
        let tablePageParam = { pageSize: 10, pageIndex: 1 }
        let pageIndex = 1
        if (this.arrRole.roleId) {
          pageIndex = this.modifyParentList.length / 10 + 1
          tablePageParam = Object.assign({}, tablePageParam, { pageIndex })
          if (this.modifyParentList.length % 10 !== 0 || Math.ceil(this.modifyTotal / 10) < pageIndex) {
            return resolve()
          } else {
            const params = {
              pageParam: tablePageParam,
              roleId: this.arrRole.roleId
            }
            this.getRoleparentList(params)
          }
        } else {
          pageIndex = this.createParentList.length / 10 + 1
          tablePageParam = Object.assign({}, tablePageParam, { pageIndex })
          if (this.createParentList.length % 10 !== 0 || Math.ceil(this.createTotal / 10) < pageIndex) {
            return resolve()
          } else {
            this.getRoleTableData(tablePageParam)
          }
        }
        resolve()
      })
    },
    // scroll请求（修改）父角色
    getRoleparentList (tablePageParam) {
      getRoleparentList(tablePageParam).then(res => {
        // console.log('getTenantTableData===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          if (res.data.records && res.data.records.length > 0) {
            this.modifyTotal = res.data.total
            this.modifyParentList.push(...res.data.records)
          }
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // scroll请求（新建）父角色
    getRoleTableData (tablePageParam) {
      getRoleTableData(tablePageParam).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          if (res.data.records && res.data.records.length > 0) {
            this.createParentList.push(...res.data.records)
          }
        }
      }).catch(error => {
        console.log('getRoleTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    newRole () {
      this.modalrole = true
      this.init()
      this.modelRoleText = {
        cancel: '取消',
        ok: '下一步'
      }
    },
    cancelClose () {
      if (this.isOneStep === true) {
        this.modalrole = false
      } else {
        this.$Modal.confirm({
          title: '提示',
          content: '<p style="">确定要退出吗，未完成资源分配会导致⻆色新建失败</p>',
          loading: true,
          okText: '确定',
          cancelText: '取消',
          onCancel: () => {
            this.modalrole = true
          },
          onOk: () => {
            this.modalrole = false
            this.$Modal.remove()
          }
        })
      }
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    getTableData () {
      getRoleTableData(this.tablePageParam, this.roleName).then(res => {
        // console.log('getTenantTableData===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.tableData = res.data.records
          this.createTotal = res.data.total
          this.createParentList = res.data.records
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          ++this.transferKey
        }
      }).catch(error => {
        console.log('getRoleTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    checkResourceId (nodekey, flag = 'modify') {
      // console.log(nodekey);
      // console.log(this.$refs.tree.getCheckedAndIndeterminateNodes())
      // let datas = this.$refs.tree.getCheckedAndIndeterminateNodes()
      this.resourceIdList = []
      for (let i = 0; i < nodekey.length; i++) if (nodekey[i].id) this.resourceIdList.push(nodekey[i].id)

      if (flag === 'new') {
        this.arrRole.resourceIdList = this.resourceIdList
        if (this.$refs.arrRole) {
          this.$refs.arrRole.validate()
        }
      } else {
        this.arrRoleModify.resourceIdList = this.resourceIdList
        if (this.$refs.arrRoleModify) {
          this.$refs.arrRoleModify.validate()
        }
      }
    },
    cancelModify (name) {
      this.modal = false
      this.$refs[name].resetFields()
    },
    ok () {
      if (this.resourceIdList.length) {
        this.$refs['arrRoleModify'].validate((valid) => {
          if (valid) {
            this.okModify()
          }
        })
      } else {
        this.msgInfo('warning', '当前未更新资源树，如需修改请重新选中！')
      }
    },
    okModify () {
      const params = {
        parentName: this.arrRoleModify.parentName,
        roleId: this.arrRoleModify.roleId,
        resourceIdList: this.resourceIdList
      }
      console.log(this.resourceIdList);
      editRoleTableData(params).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.modal = false
          this.msgInfo('success', res.message, true)
          this.getTableData()
          this.init()
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    okrole (name) {
      if (this.isOneStep) {
        this.$refs[name].validateField('roleName')
        // 校验角色名称是否存在，再决定是否进行下一步
        if (this.arrRole.roleName) {
          if (!this.arrRole.roleId) {
            this.checkName()
          } else {
            this.okroleFn()
          }
        }
      } else {
        this.$refs[name].validate((valid) => {
          if (valid) {
            this.okroleFn()
          } else {
            if (this.resourceIdList <= 0) {
              this.$Message.error('请选择分配资源!')
            }
          }
        })
      }
    },
    okroleFn () {
      let method = 'POST'
      if (this.arrRole.roleName && this.arrRole.parentName) {
        let arrRoleParams = this.arrRole
        if (this.arrRole.roleId) method = 'PUT'
        else arrRoleParams = Object.assign({}, this.arrRole, { resourceIdList: this.resourceIdList })
        getRoleAction(arrRoleParams, method).then(res => {
          if (res.code !== '00000') {
            if (res.code === '500') {
              this.msgInfo('error', res.message, true)
            } else {
              this.msgInfo('warning', res.message, true)
            }
          } else {
            this.msgInfo('success', res.message, true)
            this.modalrole = false
            this.getTableData()
            this.init()
          }
        }).catch(error => {
          this.msgInfo('error', error.message, true)
        })
      } else {
        this.msgInfo('warning', '内容不能为空！', true)
        setTimeout(() => { this.modalrole = true }, 500)
        return false
      }
    },
    checkName () {
      checkName(this.arrRole.roleName).then(res => {
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          this.isOneStep = false
          this.modelRoleText = {
            cancel: '上一步',
            ok: '完成'
          }
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
      // this.isOneStep = false
      // this.modelRoleText = {
      //   cancel: '上一步',
      //   ok: '完成'
      // }
    },
    getParentIdModify (value) {
      if (value) {
        this.arrRole.parentName = value.value
        const params = {
          parentId: value.tag,
          roleId: this.arrRole.roleId
        }
        this.getTree(params)
      }
    },
    getParentId (value) {
      this.arrRole.parentName = value
      if (value) {
        const arr = this.tableData.filter(val => val.roleName === value)
        const parentId = arr[0].roleId
        const params = {
          parentId
        }
        this.getTree(params)
      }
    },
    getTree (params) {
      getRoleResources(params).then(res => {
        if (res.code !== '00000') {
          this.msgInfo('error', res.message, true)
        } else {
          this.dataTree = res.data
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    cancel (name) {
      this.$refs[name].resetFields()
      if (this.isOneStep) {
        this.init()
        this.modalrole = false
      } else {
        this.isOneStep = true
        this.modelRoleText = {
          cancel: '取消',
          ok: '下一步'
        }
      }
    },
    handleEdit ({ row, index, column, newValue }) { console.log(row, index, column, newValue) },
    editRole (index) {
      this.init()
      this.modalrole = true
      this.isOneStep = true
      this.modelRoleText = {
        cancel: '取消',
        ok: '确定'
      }
      this.arrRole = {
        alertTitle: '修改角色',
        roleId: `${this.tableData[index].roleId}`,
        roleName: `${this.tableData[index].roleName}`,
        // parentId: `${this.tableData[index].parentId}`,
        parentName: `${this.tableData[index].parentName}`,
        brief: `${this.tableData[index].brief}`
      }
      // console.log(JSON.stringify(this.arrRole))
    }
  },
  mounted () {
    this.getTableData()
  },
  computed: {
    userSource () {
      if (localRead('userSource') == 1) {
        return false
      } else {
        return true
      }
    },
    isAdmin () {
      if (localStorage.getItem('roleId') == 1) {
        return true
      } else {
        return false
      }
    },
    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
}
</script>
<style lang="less" scoped>
.scroll-div {
  height: 45vh;
  overflow: auto;
}
.data-none {
  position: relative;
  text-align: center;
  vertical-align: middle;
  display: inline-table;
  margin: 0 auto;
  padding-top: 50px;
  img {
    max-width: 82%;
    max-height: 82%;
  }
  .title-none {
    font-size: 8px;
    color: #bdbbbb;
  }
}
.p-finish {
  text-align: center;
}
.demo-tree-render .ivu-tree-title {
  width: 100%;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
/deep/.ivu-modal {
  top: 10vh;
  .ivu-modal-content > .ivu-modal-body {
    max-height: 68vh;
    overflow: auto;
  }
}
</style>
