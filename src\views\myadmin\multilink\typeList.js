export const engineTypeList = [
  {
    value: 'eos引擎',
    key: 'EOS'
  },
  {
    value: 'bos引擎',
    key: 'BOS'
  },
  {
    value: 'cmeos引擎',
    key: 'CMEOS'
  }
]
export const statusListg = [
  {
    value: '启用',
    key: 'ENABLE'
  },
  {
    value: '关闭',
    key: 'DISABLE'
  },
  {
    value: '弃用',
    key: 'ABANDON'
  }
]
export const statusList = [
  {
    value: '启用',
    key: 'ENABLE'
  },
  {
    value: '已下线',
    key: 'DISABLE'
  }
]
export const auditList = [
  {
    key: 'true',
    value: '需要'
  },
  {
    key: 'false',
    value: '不需要'
  }
]
export const ownershipList = [
  {
    value: '纳管链',
    key: 'MANAGED_CHAIN'
  },
  {
    value: '管控链',
    key: 'CONTROL_CHAIN'
  }
]
export const nodeTypeList = [
  {
    key: 'BLOCK_PRODUCER',
    value: '出块节点'
  },
  {
    key: 'BUSINESS_NODE',
    value: '业务节点'
  },
  {
    key: 'SHARE_NODE',
    value: '分享节点'
  },
  {
    key: 'HISTORICAL_NODE',
    value: '历史节点'
  }
]
export const watchTypeList = [
  {
    key: 'ALL_VISIBLE',
    value: '全部租户可见'
  },
  {
    key: 'PARTIALLY_VISIBLE',
    value: '部分租户可见'
  },
  {
    key: 'ALL_INVISIBLE',
    value: '全部租户不可见'
  }
]

export const expTypeList = [
  {
    key: 0,
    value: '不导出'

  },
  {
    key: 1,
    value: 'hyperion导出'

  },
  {
    key: 2,
    value: '数据管理组件导出'

  }
]
export const deplooyList = [
  {
    key: '0',
    value: '企业版'
  },
  {
    key: '1',
    value: '国密版'
  }
]
