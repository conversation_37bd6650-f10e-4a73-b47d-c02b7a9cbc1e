FROM node:14-buster as builder
WORKDIR /code
COPY . .
RUN npm config set registry "http://10.248.68.143:8081/repository/npm-taobao/" \
   && npm config set sass_binary_site "http://10.248.68.143:8081/repository/node-sass/" \
   && npm install @vue/cli -g \
   && npm install @vue/cli-service-global -g \
   && npm install \
   && npm run build

FROM openresty/openresty:alpine
COPY --from=builder /code/dist /usr/share/nginx/html
COPY nginx.conf /usr/local/openresty/nginx/conf/nginx.conf
