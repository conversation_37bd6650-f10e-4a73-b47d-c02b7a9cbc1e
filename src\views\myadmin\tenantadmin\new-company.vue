<template>
  <div>
    <p style="text-align:right;">
      <Input style="width:auto;vertical-align:baseline;" placeholder="可输入公司名称查询信息" v-model="companyvalue" @keyup.enter.native="companyserch">
      <Icon type="ios-search" slot="suffix" @click="companyserch" />
      </Input>
      <Button style="margin-left:10px;" type="success" :disabled="hasEditPermission" ghost @click="createCompany" icon="md-add">新增公司</Button>
    </p>
    <div style="margin-top:1%">
      <Table stripe :columns="columns1" :data="data1">
        <template slot-scope="{ row, index }" slot="action">
          <div v-if="row.edit">
            <Button type='text' size='small' style="margin-right: 8px;color: #3D73EF;border: 1px solid #3D73EF" @click="cancelrow(row,index)">取消</Button>
            <Button type="text" size="small" style="margin-right: 8px;color: #3D73EF;border: 1px solid #3D73EF" @click="savedata(row)">保存</Button>
          </div>
          <div v-else>
            <Poptip transfer placement="top-end" confirm title="确认删除吗?" @on-ok="showDelete(row)" ok-text="确认" cancel-text="取消">
              <Button type='text' size='small' :style="buttonStyle" :disabled="hasEditPermission">删除</Button>
            </Poptip>

            <Button type="text" size="small" :style="buttonStyle" :disabled="hasEditPermission" @click="editContract(row)">修改</Button>
          </div>

        </template>
      </Table>
      <Page :total="tablePageParam1.pagetotal" :current.sync="tablePageParam1.pageIndex" @on-change="pageChange1" :page-size="tablePageParam1.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange1" style="text-align:right;margin-top:1%" />
    </div>

  </div>
</template>

<script>
import { companyList, addCompany, getserchList, deleteCompany, updateCompany } from '@/api/contract'
import { localRead } from '@/lib/util'
export default {
  data () {
    return {
      cityList: [],
      cityList1: [],
      data1: [],
      columns1: [
        {
          title: '公司名称',
          key: 'companyName',
          render: (h, row) => {
            const info = row.row
            // console.log(info)
            return <div>
              {
                info.edit ? <div>
                  <Input value={info.companyName} maxlength="25" show-word-limit placeholder="请输入公司名称" style="width: 200px" onInput={(e) => { info.companyName = e }} />
                </div> : <div>{info.companyName}</div>
              }

            </div>
            // return row.isEdit ? <Input placeholder="请输入公司名称" style="width: 200px" /> : row.text
          }
        },
        {
          title: '公司归属地',
          key: 'attribution',
          minWidth: 160,
          render: (h, { row }) => {
            // console.log(row)
            // console.log('11111111111111', this)
            return <div>
              {
                row.edit ? <div>
                  <Select style="width:150px" value={row.ownProvinceCode} placeholder='请选择所在省' transfer={this.transfer}
                    // onInput={(value) => {
                    //   console.log(value)
                    // }}
                    on={
                      {
                        'on-change': (info) => {
                          this.change(row, info)
                          // console.log('ProvinceCode', info)
                          row.ownProvinceCode = info
                          // let listdata = {
                          //   pcode: info,
                          //   name: ''
                          // }
                          // getserchList(listdata).then((res) => {
                          //   console.log(res)
                          // })
                          // this.getCityOptions(listdata)
                        },
                        'on-open-change': (row) => {
                          if (row.ownProvinceCode !== null) {
                            this.disabled = false
                          }
                        }
                      }
                    }
                  >
                    {
                      this.cityList.map((item) => {
                        return <Option value={item.code} >{item.name}</Option>
                      })
                    }
                  </Select>
                  <Select style="width:150px;margin-left:2%" vModel={row.ownCityCode} disabled={this.disabled} placeholder='请选择所在市' transfer={this.transfer}
                  // on={
                  //   // {
                  //   //   'on-select': (info) => {
                  //   //     console.log('CityCode', info)
                  //   //     row.ownCityCode = info
                  //   //   }
                  //   // }
                  // }
                  >
                    {
                      row.cityList1.map((item) => {
                        return <Option value={item.code}>{item.name}</Option>
                      })
                    }
                  </Select>
                </div> : <div>{row.ownProvinceName + row.ownCityName}</div>
              }

            </div>
          }
        },
        {
          title: '操作',
          align: 'left',
          slot: 'action'
        }
      ],
      companyModal: false,
      tablePageParam1: { pagetotal: 0, pageSize: 10, pageIndex: 1 },
      companyvalue: '',
      disabled: true,
      Companyrow: '',
      ownProvinceName: '',
      ownCityCode: '',
      transfer: false,
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  computed: {
    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    async change (row, data) {
      let listdata = {
        pcode: data,
        name: ''
      }
      row.ownCityCode = ''
      let res = await getserchList(listdata)
      row.cityList1 = res.data
      row.ownProvinceName = row.cityList1[0].name
      row.ownCityCode = row.cityList1[0].code

      // getserchList(listdata)
      // ((res) => {
      //   row.cityList1 = res.data
      //   // row.ownProvinceName = this.cityList1[0].name
      //   row.ownCityCode = row.cityList1[0].code
      // })
      // this.getCityOptions(listdata)
      // row.ownProvinceName = this.ownProvinceName
      // row.ownCityCode = this.ownCityCode
    },
    // 获取省下来选项列表
    getselectOptions () {
      let listdata = {
        pcode: 100000,
        name: ''
      }
      getserchList(listdata).then((res) => {
        this.cityList = res.data
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 公司列表
    companyTable () {
      let listdata = {
        companyName: this.companyvalue,
        pageParam: this.tablePageParam1
      }
      companyList(listdata).then(res => {
        this.data1 = res.data.records.map(item => {
          return {
            ...item,
            edit: false,
            Modify: 1,
            cityList1: []
          }
        })

        this.tablePageParam1 = {
          pagetotal: res.data.total,
          pageSize: res.data.size,
          pageIndex: res.data.current
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 搜索
    companyserch () {
      this.companyTable()
    },
    pageChange1 (index) {
      this.tablePageParam1.pageIndex = index
      this.companyTable()
    },
    pageSizeChange1 (index) {
      this.tablePageParam1.pageSize = index
      this.companyTable()
    },
    // 取消
    cancelrow (row, index) {
      // console.log(row)
      if (row.Modify === '') {
        this.data1.splice(index, 1)
      } else {
        row.edit = false
        row.companyName = this.Companyrow.companyName
      }
    },
    // 保存
    savedata (row) {
      // console.log(row)
      if (row.companyName && row.ownProvinceCode && row.ownCityCode) {
        if (row.Modify === 2) {
          // 修改
          let updata = {
            id: row.id,
            companyName: row.companyName,
            ownProvinceCode: row.ownProvinceCode,
            ownCityCode: row.ownCityCode
          }
          updateCompany(updata).then(res => {
            if (res.code !== '00000') {
              this.msgInfo('warning', res.message, true)
            } else {
              this.companyTable()
              this.msgInfo('info', res.message)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        } else {
          // 新增
          let adddata = {
            companyName: row.companyName,
            ownProvinceCode: row.ownProvinceCode,
            ownCityCode: row.ownCityCode
          }
          // console.log(adddata)
          addCompany(adddata).then(res => {
            if (res.code !== '00000') {
              this.msgInfo('warning', res.message, true)
            } else {
              this.companyTable()
              this.msgInfo('info', res.message)
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
          })
        }
      } else {
        this.msgInfo('error', '公司名称和公司归属地不能为空', true)
      }
    },
    // 新增公司
    createCompany () {
      // if(this.data1.length<=3){}else{

      // }
      if (this.data1.length === 0 || this.data1.length <= 4) {
        this.transfer = true
        this.data1.unshift({
          companyName: '',
          ownProvinceCode: '', // 省code
          // CityName: '', // 市名称
          ownCityCode: '', // 市code
          edit: true,
          cityList1: [],
          Modify: ''
        })
        this.getselectOptions()
      } else {
        if (this.data1[0].companyName === '') {
          this.msgInfo('error', '每次只能新增一条公司', true)
        } else {
          this.transfer = false
          this.data1.unshift({
            companyName: '',
            ownProvinceCode: '', // 省code
            // CityName: '', // 市名称
            ownCityCode: '', // 市code
            edit: true,
            cityList1: [],
            Modify: ''
          })
          this.getselectOptions()
        }
      }
    },
    // 公司配置删除
    showDelete (row) {
      deleteCompany(row.id).then(res => {
        if (res.code !== '00000') {
          this.msgInfo('warning', res.message, true)
        } else {
          this.companyTable()
          this.msgInfo('info', res.message)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 公司配置修改
    editContract (row) {
      // console.log(row)
      this.Companyrow = JSON.parse(JSON.stringify(row))
      row.edit = true
      row.Modify = 2
      this.disabled = false
      this.getselectOptions()
      let listdata1 = {
        pcode: row.ownProvinceCode,
        name: ''
      }
      getserchList(listdata1).then((res) => {
        row.cityList1 = res.data
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    }
  },
  activated () {
    this.companyserch()
  }
  // mounted () {
  //   this.companyTable()
  // }
}
</script>
