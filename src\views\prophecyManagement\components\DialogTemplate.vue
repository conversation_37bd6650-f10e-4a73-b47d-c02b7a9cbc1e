<!--
  aturun

  2021/10/21

-->
<template>
  <el-dialog
      class="dialog_sty"
      title="新建消费者用户"
      :visible.sync="Visible"
      width="520px"
      :before-close="handleClose"
      :close-on-click-modal="false"
      destroy-on-close>
    <div class="dialog_content">
      <el-form :model="Form" :rules="rules" ref="newConsumerForm" label-width="100px">
        <el-form-item label="用户名称：" prop="name" >
          <el-input v-model="Form.name" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
    <el-button @click="Visible = false">取 消</el-button>
    <el-button type="primary" @click="submitForm">确 定</el-button>
  </span>
  </el-dialog>
</template>

<script>
export default {
  name: "DialogTemplate",
  components: {},
  props:[],
  data() {
    return {
      Visible:true,
      Form:{
        name:''
      },
      rules: {
        name: [
          {required: true, message: '请输入用户名称', trigger: 'blur'},
        ],
      }
    }
  },
  watch: {},
  created() {

  },
  mounted() {
  },
  methods: {
    submitForm() {
      this.$refs['newConsumerForm'].validate((valid) => {
        if (valid) {
          alert('submit!');
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetForm() {
      this.$refs['ss'].resetFields();
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
          .then(_ => {
            done();
          })
          .catch(_ => {});
    }
  },

}
</script>

<style lang="less" scoped>

</style>
