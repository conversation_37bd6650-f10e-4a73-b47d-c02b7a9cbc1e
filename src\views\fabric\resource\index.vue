<template>
  <div class="resource">
    <!-- <div class="resourceTitle">
      <el-form :inline="true">
        <div class="evertModule">
          <el-form-item label="选择集群：">
            <el-select
              v-model="xzjq"
              @change="jqChange($event)"
              placeholder="请选择"
            >
              <el-option
                v-for="item in jqOptions"
                :key="item.ClusterName"
                :label="item.ClusterName"
                :value="item.ClusterId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <span class="xing">*</span>
        </div>
      </el-form>
    </div> -->
    <!-- 集群资源 -->
    <!--<div class="everyModule">
      <div class="info">
        <div class="infoTitle">
          <img :src="infoIcon" class="infoIcon" />
          <span class="infotext">集群资源</span>
        </div>
      </div>
       仪表盘
      <div class="ybpList" v-if="jqYbpList.length > 0">
        <template v-for="(item, index) in jqList">
          <div class="ybpModule" :key="index">
            <div :id="item.id" class="ybpSty"></div>
            <div class="unit">{{ item.unit }}</div>
          </div>
        </template>
      </div>
      <div class="ybpList" v-if="jqYbpList.length == 0">
        <template v-for="(item, index) in jqList">
          <div class="ybpModule" :key="index">
            <i
              class="el-icon-loading"
              v-if="paddingText == '数据请求中...'"
            ></i>
            {{ paddingText }}
          </div>
        </template>
      </div>
    </div>-->
    <!-- 主机资源 -->
    <!-- <div class="everyModule">
      <div class="info">
        <div class="infoTitle">
          <img :src="infoIcon" class="infoIcon" />
          <span class="infotext">主机资源</span>
        </div>
      </div>
      <div class="resourceTitle">
        <el-form :inline="true">
          <div class="evertModule">
            <el-form-item label="选择主机：">
              <el-select v-model="xzzj" @change="zjChange" placeholder="请选择">
                <el-option
                  v-for="item in zjOptions"
                  :key="item.Name"
                  :label="item.Name"
                  :value="item.Name"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <span class="xing">*</span>
          </div>
        </el-form>
      </div>
      <div class="resorceState">
        <span>创建时间：{{ zjTime }}</span>
        <span class="setStyle">主机地址：{{ zjAddress }}</span>
      </div>
      仪表盘
      <div class="ybpList" v-if="zjYbpList.length > 0">
        <template v-for="(item, index) in zjList">
          <div class="ybpModule" :key="index">
            <div :id="item.id" class="ybpSty"></div>
            <div class="unit">{{ item.unit }}</div>
          </div>
        </template>
      </div>
      <div class="ybpList" v-if="zjYbpList.length == 0">
        <template v-for="(item, index) in zjList">
          <div class="ybpModule" :key="index">
            <i
              class="el-icon-loading"
              v-if="zjPaddingText == '数据请求中...'"
            ></i>
            {{ zjPaddingText }}
          </div>
        </template>
      </div>
    </div> -->
    <!-- 链节点资源 -->
    <div class="everyModule">
      <div class="info" ref="everyWid">
        <div class="infoTitle">
          <img :src="infoIcon" class="infoIcon" />
          <span class="infotext">节点性能资源</span>
        </div>
      </div>
      <template>
        <SpaceLayout paddingX="0" paddingY="0" top="10">
          <div slot="padding">
            <div class="resourceTitle">
              <el-form :inline="true">
                <div class="evertModule">
                  <!-- <div class="module">
                    <span class="xing">*</span>
                    <el-form-item label="选择区块链：">
                      <el-select
                        v-model="xzqkl"
                        @change="qklChange"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in qklOptions"
                          :key="item.Name"
                          :label="item.Name"
                          :value="item.Id"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>

                  </div> -->
                  <div class="setIptStyle module">
                    <span class="xing">*</span>
                    <el-form-item label="选择节点：">
                      <el-select
                        v-model="xzjd"
                        @change="jdChange"
                        placeholder="请选择"
                        :popper-append-to-body="false"
                        popper-class="select-down"
                      >
                        <el-option
                          v-for="(item, index) in jdOptions"
                          :key="index"
                          :label="checkName(item.NodeName)"
                          :value="item.NodeName"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>

                  </div>
                </div>
              </el-form>
            </div>
            <div class="resorceState">
              <!-- <span class="address">主机位置：</span>
              <span class="ljAddress">{{ ljAddress }}</span> -->
              <span class="setStyle">角色：</span>
              <span class="lijs">{{ ljJs }}</span>
              <span>组织：{{ ljZz }}</span>
            </div>
          </div>
        </SpaceLayout>
        <!-- 折线图 -->
        <div class="zxList" v-show="getLjdData == true">
          <template v-for="(item, index) in ljList">
            <div class="zxModule" :key="index">
              <div :id="item.id" class="zxSty"></div>
            </div>
          </template>
        </div>
        <div class="none" v-show="getLjdData == false" ref="noThing">
          <i
            class="el-icon-loading"
            v-if="zxtPaddingText == '数据请求中...'"
          ></i>
          <!-- <svg-icon icon-class="table-empty" v-else/> -->
          {{ zxtPaddingText }}
        </div>
      </template>
    </div>
    <!-- 成功失败倒计时 -->
    <!-- <countDown
      v-if="isShowIcon"
      :state="countState"
      :countTime="countTime"
      :text="countText"
      @getCountDown="getCountDown"
    ></countDown> -->
  </div>
</template>
<script>
import {
  getClusterMonitor,
  getHostNodeMonitor,
  getChainNodeNameList,
  getChainPodList
} from "@/api/baascore/ywResource";
import { getChainList, getClusterList, getChainMonitor } from "@/api/baascore/overview";
import { getK8SNodeList } from "@/api/baascore/creatChain";
import echarts from "echarts";
import { zxOption, ybpOption } from "@/utils/zyEcharts";
import { utc2beijing, getCurrentDate, getYMDHMS } from "@/utils/validate";
import { calcCostMixin } from "@/utils/mixin";
import zxt from "@/utils/zxmixin";
import SpaceLayout from '@/components/SpaceLayout'
export default {
  mixins: [calcCostMixin, zxt],
  data() {
    return {
      // 成功失败弹窗
      isShowIcon: false,
      countTime: 2,
      countState: "",
      countText: "",
      timer: null, //进行定义定时器名称：
      ClusterId: "", //集群资源需要的id
      //  集群资源仪表盘
      jqList: [
        {
          id: "cpu",
          unit: ""
        },
        {
          id: "nc",
          unit: ""
        },
        {
          id: "cc",
          unit: ""
        },
        {
          id: "sc",
          unit: ""
        },
        {
          id: "xz",
          unit: ""
        }
      ],
      //  主机资源仪表盘
      zjList: [
        {
          id: "zjCpu",
          unit: ""
        },
        {
          id: "zjNc",
          unit: ""
        },
        {
          id: "zjCc",
          unit: ""
        },
        {
          id: "zjSc",
          unit: ""
        },
        {
          id: "zjXz",
          unit: ""
        }
      ],
      // 链节点资源
      ljList: [
        {
          id: "zxCpu2",
          unit: "(单位：%)"
        },
        {
          id: "zxNc2",
          unit: "(单位：%)"
        },
        {
          id: "zxCc2",
          unit: "(单位：kb/s)"
        },
        {
          id: "zxSc2",
          unit: "(单位：kb/s)"
        }
      ],
      infoIcon: require("@/assets/chainManage_images/overview/infoIcon.png"),
      xzjq: "", //选择集群
      jqYbpList: [], //集群仪表盘的数据
      xzzj: "", //选择主机
      zjTime: "", //创建时间
      zjAddress: "", //主机地址
      zjYbpList: [], //集群仪表盘的数据
      xzqkl: "", //选择区块链
      xzjd: "", //选择节点：
      getQklName: "", //选择区块链name
      getJdlId: "", //选择节点id
      jqOptions: [], //集群下来
      zjOptions: [], //选择主机
      zjNum: 0, //主机个数
      qklOptions: [], //选择区块链
      jdOptions: [], //选择节点：
      getZjAddress: [], //匹配选择节点对应的数据
      getLjAllList: [], //获取链节点资源总数据
      ljAddress: "", //主机位置
      ljJs: "", //角色
      ljZz: "", //组织
      ljCpuList: [], //链节点资源Cpu使用率
      ljXCpulist: [], //链节点资源X轴数据
      tooltipList:[],
      ljNcList: [], //链节点资源内存使用率
      ljXNclist: [], //链节点资源内存X轴数据
      ljCcList: [], //链节点资源存储使用率
      ljXCclist: [], //链节点资源存储X轴数据
      ljUpdateList: [], //链节点资源上传使用率
      ljXUplist: [], //链节点资源上传X轴数据
      ljDownLoadList: [], //链节点资源下载使用率
      ljXDolist: [], //链节点资源下载X轴数据
      ncMax: "", //获取内存使用率单位
      ccMax: "", //获取存储使用量单位
      scMax: "", //获取上传单位
      xzMax: "", //获取下载单位
      noL: false, //没有链的时候
      getLjdData: false, //链节点数据
      jsq: false,
      parentClientWidth: 0,
      paddingText: "数据请求中...",
      zjPaddingText: "数据请求中...",
      zxtPaddingText: "数据请求中..."
    };
  },
  // 组件部分
  components: {
    SpaceLayout
  },
  created() {},
  async mounted() {
    window.addEventListener('resize',this.initEcharts,false)
    await this.getJqOption(); //选择集群
    //this.getClusterMonitor(); // 集群资源
   // await this.getK8SNodeList(); // 选择主机
   //this.jsq = await this.getChainList(); //获取选择区块链：下拉数据
    var chain = JSON.parse(sessionStorage.getItem("chainItem"));
    this.xzqkl = chain.Id;
    this.ClusterId = chain.Cluster;
    this.getQklName = chain.Name;
    this.getChainNodeNameList(this.xzqkl);
  },
  watch: {
    jsq: {
      handler(newName, oldName) {
        if (newName) {
          this.timer = setInterval(() => {
            //this.getJqOption(); //选择集群
            //this.getK8SNodeList(); // 选择主机
           // this.getClusterMonitor(); // 集群资源
          }, 5000);
        }
      },
      deep: true //对象内部属性的监听，关键。
      // immediate: true
    }
  },
  methods: {
    initEcharts() {
      setTimeout(() =>{
        this.parentClientWidth =  this.$refs.everyWid.clientWidth / 2 - (this.$refs.everyWid.clientWidth / 2) * 0.08;
        let setWid = {
          width: this.parentClientWidth
        };
        echarts.init(document.getElementById("zxCpu2")).resize(setWid);
        echarts.init(document.getElementById("zxNc2")).resize(setWid);
        echarts.init(document.getElementById("zxCc2")).resize(setWid);
        echarts.init(document.getElementById("zxSc2")).resize(setWid);
      },300)
    },
    // 倒计时
    getCountDown(type) {
      this.isShowIcon = false;
    },
    // 集群下拉事件
    jqChange(val) {
      this.getK8SNodeList(val); // 选择主机
    },
    // 主机下拉事件
    zjChange(val) {
      this.xzzj = val;
      let getNewData = this.zjOptions.filter(item => item.Name == val);
      this.zjTime = utc2beijing(getNewData[0].CreateTime);
      this.zjAddress = getNewData[0].ExternalIp;
      this.getHostNodeMonitor(this.ClusterId, this.zjAddress); //主机资源仪表盘list
    },
    // 选择区块链
    qklChange(val) {
      this.xzMax = this.scMax = "";
      let obj = {};
      obj = this.qklOptions.find(item => {
        //遍历qklOptions的数据
        return item.Id === val; //筛选出匹配数据
      });
      this.getQklName = obj.Name;
      this.getChainNodeNameList(val); //  区块链所有节点列表
    },
    // 选择节点：
    jdChange(val) {
      this.xzMax = this.scMax = "";
      let changeZjAddress = []; //匹配选择节点对应的数据
      // console.log(this.getLjAllList,this.xzjd)
      this.getLjAllList.map((item, index) => {
        item.NodeInfo.map((i, index) => {
          if (i.Name == this.xzjd) {
            changeZjAddress.push(item);
            this.ljAddress = i.NodeName;
            this.ljJs = i.Type;
            this.ljZz = changeZjAddress[0].GroupName;
          }
        });
      });
      let obj = {};
      obj = this.jdOptions.find(item => {
        //遍历qklOptions的数据
        return item.NodeName === val; //筛选出匹配数据
      });
      this.xzjd = obj.NodeName;
      this.getChainContainerMonitor();
    },
    // 选择集群
    getJqOption() {
      return new Promise((resolve, reject) => {
        getClusterList().then(res => {
          if (res.code == 200) {
            this.jqOptions = res.data;
            this.xzjq = res.data[0].ClusterName;
            this.ClusterId = res.data[0].ClusterId;
            resolve(res.data[0].ClusterId);
          } else {
            // this.isShowIcon = true;
            // this.countText = "数据获取失败，请重新加载！";
            // this.countState = "error";
            this.$message.error('数据获取失败，请重新加载！')
          }
        });
      });
    },
    // 集群资源
    getClusterMonitor() {
      let parms = {
        ClusterId: this.ClusterId
      };
      getClusterMonitor(parms).then(res => {
        if (res.status == 200) {
          this.jqYbpList = []; //清空仪表盘数据
          res.data.Result.map((item, index) => {
            if(item.Metric) {
              let GetName = item.Metric.Job;
              this.filtYbp(GetName, item, this.jqYbpList, index, this.jqList, 0);
            }
          });
          this.$nextTick(() => {
            this.initChart(this.jqYbpList, this.jqList); //集群资源
          });
        } else {
          this.jqYbpList = [];
          this.paddingText = "暂无数据";
          // this.isShowIcon = true;
          // this.countText = "数据获取失败，请重新加载！";
          // this.countState = "error";
          this.$message.error('数据获取失败，请重新加载！')
        }
      });
    },
    // 选择主机
    getK8SNodeList() {
      return new Promise((resolve, reject) => {
        let parms = {
          ClusterId: this.ClusterId
        };
        getK8SNodeList(parms).then(res => {
          if (res.status == 200) {
            this.xzzj = this.xzzj || res.data.Hosts[0].Name;
            this.zjTime =
              this.zjTime || utc2beijing(res.data.Hosts[0].CreateTime);
            this.zjAddress = this.zjAddress || res.data.Hosts[0].ExternalIp;
            this.zjOptions = res.data.Hosts;
            this.zjNum = res.data.Hosts.length;
            this.getHostNodeMonitor(this.ClusterId, this.zjAddress); //主机资源仪表盘list
            resolve(res.data.Hosts);
          } else {
            // this.isShowIcon = true;
            // this.countText = "数据获取失败，请重新加载！";
            // this.countState = "error";
            this.$message.error('数据获取失败，请重新加载！')
          }
        });
      });
    },
    // 主机资源仪表盘数据获取
    getHostNodeMonitor(ClusterId, Ip) {
      let parms = {
        ClusterId: ClusterId,
        NodeIp: Ip
      };
      getHostNodeMonitor(parms).then(res => {
        if (res.status == 200) {
          this.zjYbpList = []; //清空仪表盘数据
          res.data.Result.map((item, index) => {
            let GetName = item.Metric.Job;
            this.filtYbp(GetName, item, this.zjYbpList, index, this.zjList);
          });
          this.$nextTick(() => {
            this.zjChart(this.zjYbpList, this.zjList); //主机资源
          });
        } else {
          this.zjPaddingText = "暂无数据";
          this.$message.error('数据获取失败，请重新加载！')
          // this.isShowIcon = true;
          // this.countText = "数据获取失败，请重新加载！";
          // this.countState = "error";
        }
      });
    },
    //  获取选择区块链：下拉数据
    getChainList() {
      return new Promise((resolve, reject) => {
        getChainList().then(res => {
          if (res.code == 200) {
            if (res.data) {
              this.noL = true;
              this.qklOptions = res.data;
              this.xzqkl = res.data[0].Id;
              this.getQklName = res.data[0].Name;
              this.getChainNodeNameList(this.xzqkl); //  区块链所有节点列表
            } else {
              this.zxtPaddingText = "暂无数据";
              this.noL = false;
            }
            resolve(this.noL);
          } else {
            // this.isShowIcon = true;
            // this.countText = "数据获取失败，请重新加载！";
            // this.countState = "error";
            this.$message.error('数据获取失败，请重新加载！')
          }
        });
      });
    },
    //  所有节点列表
    getChainNodeNameList(id) {
      let parms = {
        ServiceId: id
      };
      getChainNodeNameList(parms).then(res => {
        if (res.code == 200) {
          this.jdOptions = res.data;
          this.getChainPodList(this.xzqkl); //   链节点资源主机位置
          if (res.data[0] && res.data[0].NodeName) {
            this.xzjd = res.data[0].NodeName;
            this.getJdlId = res.data[0].Id;
            this.getChainContainerMonitor(); //链节点资源数据查询
          } else {
            this.zxtPaddingText = "暂无数据";
          }
        } else {
          this.zxtPaddingText = "暂无数据";
          // this.isShowIcon = true;
          // this.countText = "数据获取失败，请重新加载！";
          // this.countState = "error";
          this.$message.error('数据获取失败，请重新加载！')
        }
      });
    },

    // 链节点资源主机位置
    getChainPodList(id) {
      let parms = {
        ServiceId: id
      };
      getChainPodList(parms).then(res => {
        if (res.code == 200) {
          let getZjAddress = []; //匹配选择节点对应的数据
          this.getLjAllList = res.data;
          this.getLjAllList.map((item, index) => {
            item.NodeInfo.map((i, index) => {
              if (i.Name == this.xzjd) {
                getZjAddress.push(item);
                this.ljAddress = i.NodeName;
                this.ljJs = i.Type;
                this.ljZz = getZjAddress[0].GroupName;
              }
            });
          });
        } else {
          // this.isShowIcon = true;
          // this.countText = "数据获取失败，请重新加载！";
          // this.countState = "error";
          this.$message.error('数据获取失败，请重新加载！')
        }
      });
    },
    // 链节点资源数据查询
    getChainContainerMonitor() {
      let parms = {
        ClusterId: this.ClusterId,
        ServiceName: this.getQklName,
        NodeName: this.xzjd,
        StartTime: Date.parse(getCurrentDate(1))
          .toString()
          .substring(0, 10),
        EndTime: Date.parse(getCurrentDate(0))
          .toString()
          .substring(0, 10)
      };
      getChainMonitor(parms).then(res => {
        if (res.code == 200) {
          if (res.data.Result) {
            this.getLjdData = true;
            res.data.Result.forEach((item, index) => {
              let GetTitleName = item.Metric.Job;
              this.filterZx(GetTitleName, item);
            });
            // v-if第一次失败后面加载不出来，
            if (document.getElementById("zxCpu2")) {
              this.initEcharts()
            }

            this.ljdChart2();
          } else {
            this.getLjdData = false;
            this.zxtPaddingText = "暂无数据";
          }
        } else {
          this.getLjdData = false;
          this.zxtPaddingText = "暂无数据";
          // this.isShowIcon = true;
          // this.countText = "数据获取失败，请重新加载！";
          // this.countState = "error";
          this.$message.error('数据获取失败，请重新加载！')
        }
      });
    },
    // 集群资源echarts
    initChart(val, dw) {
      if (document.getElementById("cpu")) {
        let cpuEchart = echarts.init(document.getElementById("cpu"));
        let ncEchart = echarts.init(document.getElementById("nc"));
        let ccEchart = echarts.init(document.getElementById("cc"));
        let scEchart = echarts.init(document.getElementById("sc"));
        let xzEchart = echarts.init(document.getElementById("xz"));
        // 集群资源cpu、内存使用率、存储使用率、上传速率、下载速率
        cpuEchart.setOption(ybpOption);
        ncEchart.setOption(ybpOption);
        ccEchart.setOption(ybpOption);
        scEchart.setOption(ybpOption);
        xzEchart.setOption(ybpOption);
        cpuEchart.setOption({
          series: [
            {
              name: "内层数据刻度",
              data: [
                {
                  value: val[0]
                }
              ]
            }
          ]
        });
        ncEchart.setOption({
          title: {
            text: "内存使用率"
          },
          series: [
            {
              name: "内层数据刻度",
              data: [
                {
                  value: val[1]
                }
              ]
            }
          ]
        });
        ccEchart.setOption({
          title: {
            text: "存储使用率"
          },
          series: [
            {
              name: "内层数据刻度",
              data: [
                {
                  value: val[2]
                }
              ]
            }
          ]
        });
        scEchart.setOption({
          title: {
            text: "上传速率"
          },
          series: [
            {
              name: "内层数据刻度",
              detail: {
                formatter: function(value) {
                  let getDw = "";
                  if (dw[3].unit.indexOf("(单位：b/s)") >= 0) {
                    getDw = "b/s";
                  } else if (dw[3].unit == "(单位：10kb/s)") {
                    value = value * 10 + "kb/s";
                    getDw = "";
                  } else if (dw[3].unit == "(单位：10Mb/s)") {
                    value = value * 10 + "Mb/s";
                    getDw = "";
                  } else {
                    getDw = dw[3].unit.substr(-5, 4);
                  }
                  // if (dw[3].unit == "(单位：10kb/s)") {
                  //   value = value * 10 + "kb/s";
                  //   getDw = "";
                  // }
                  // if (dw[3].unit == "(单位：10Mb/s)") {
                  //   value = value * 10 + "Mb/s";
                  //   getDw = "";
                  // }
                  return value + getDw;
                }
              },
              data: [
                {
                  value: val[3]
                }
              ]
            }
          ]
        });
        xzEchart.setOption({
          title: {
            text: "下载速率"
          },
          series: [
            {
              name: "内层数据刻度",
              detail: {
                formatter: function(value) {
                  let getDw = "";
                  if (dw[4].unit.indexOf("(单位：b/s)") >= 0) {
                    getDw = "b/s";
                  } else if (dw[4].unit == "(单位：10kb/s)") {
                    value = value * 10 + "kb/s";
                    getDw = "";
                  } else if (dw[4].unit == "(单位：10Mb/s)") {
                    value = value * 10 + "Mb/s";
                    getDw = "";
                  } else {
                    getDw = dw[4].unit.substr(-5, 4);
                  }
                  // if (dw[4].unit == "(单位：10kb/s)") {
                  //   value = value * 10 + "kb/s";
                  //   getDw = "";
                  // }
                  // if (dw[4].unit == "(单位：10Mb/s)") {
                  //   value = value * 10 + "Mb/s";
                  //   getDw = "";
                  // }
                  return value + getDw;
                  // return value + dw[4].unit.substr(-5, 4);
                }
              },
              data: [
                {
                  value: val[4]
                }
              ]
            }
          ]
        });
      }
    },
    // 主机资源echarts
    zjChart(getval, dw) {
      // 主机资源
      if (document.getElementById("zjCpu")) {
        let zjCpuEchart = echarts.init(document.getElementById("zjCpu"));
        let zjNcEchart = echarts.init(document.getElementById("zjNc"));
        let zjCcEchart = echarts.init(document.getElementById("zjCc"));
        let zjScEchart = echarts.init(document.getElementById("zjSc"));
        let zjXzEchart = echarts.init(document.getElementById("zjXz"));
        // 集群资源cpu、内存使用率、存储使用率、上传速率、下载速率
        zjCpuEchart.setOption(ybpOption);
        zjNcEchart.setOption(ybpOption);
        zjCcEchart.setOption(ybpOption);
        zjScEchart.setOption(ybpOption);
        zjXzEchart.setOption(ybpOption);
        zjCpuEchart.setOption({
          series: [
            {
              name: "内层数据刻度",
              data: [
                {
                  value: getval[0]
                }
              ]
            }
          ]
        });
        zjNcEchart.setOption({
          title: {
            text: "内存使用率"
          },
          series: [
            {
              name: "内层数据刻度",
              data: [
                {
                  value: getval[1]
                }
              ]
            }
          ]
        });
        zjCcEchart.setOption({
          title: {
            text: "存储使用率"
          },
          series: [
            {
              name: "内层数据刻度",
              data: [
                {
                  value: getval[2]
                }
              ]
            }
          ]
        });
        zjScEchart.setOption({
          title: {
            text: "上传速率"
          },
          series: [
            {
              name: "内层数据刻度",
              detail: {
                formatter: function(value) {
                  let getDw = "";
                  // dw[3].unit.indexOf("(单位：b/s)") >= 0
                  //   ? "b/s"
                  //   : dw[3].unit.substr(-5, 4);
                  if (dw[3].unit.indexOf("(单位：b/s)") >= 0) {
                    getDw = "b/s";
                  } else if (dw[3].unit == "(单位：10kb/s)") {
                    value = value * 10 + "kb/s";
                    getDw = "";
                  } else if (dw[3].unit == "(单位：10Mb/s)") {
                    value = value * 10 + "Mb/s";
                    getDw = "";
                  } else {
                    getDw = dw[3].unit.substr(-5, 4);
                  }
                  return value + getDw;
                }
              },
              data: [
                {
                  value: getval[3]
                }
              ]
            }
          ]
        });
        zjXzEchart.setOption({
          title: {
            text: "下载速率"
          },
          series: [
            {
              name: "内层数据刻度",
              detail: {
                formatter: function(value) {
                  let getDw = "";
                  if (dw[4].unit.indexOf("(单位：b/s)") >= 0) {
                    getDw = "b/s";
                  } else if (dw[4].unit == "(单位：10kb/s)") {
                    value = value * 10 + "kb/s";
                    getDw = "";
                  } else if (dw[4].unit == "(单位：10Mb/s)") {
                    value = value * 10 + "Mb/s";
                    getDw = "";
                  } else {
                    getDw = dw[4].unit.substr(-5, 4);
                  }
                  // if (dw[4].unit == "(单位：10kb/s)") {
                  //   value = value * 10 + "kb/s";
                  //   getDw = "";
                  // }
                  // if (dw[4].unit == "(单位：10Mb/s)") {
                  //   value = value * 10 + "Mb/s";
                  //   getDw = "";
                  // }
                  return value + getDw;
                  // return value + dw[4].unit.substr(-5, 4);
                }
              },
              data: [
                {
                  value: getval[4]
                }
              ]
            }
          ]
        });
      }
    }
  },
  beforeDestroy() {
    this.jsq = false;
    clearInterval(this.timer);
    this.timer = null;
    window.removeEventListener('resize',this.initEcharts)
  },
  destroyed() {
    this.jsq = false;
    clearInterval(this.timer);
    this.timer = null;
    window.removeEventListener('resize',this.initEcharts)
  }
};
</script>
<style  rel="stylesheet/less" lang="less" scoped>
// /deep/.el-select-dropdown__item {
//   font-size: 14px !important;
// }
/deep/ .el-input--medium .el-input__inner {
  // font-size: 18px;
  font-size: 14px;
}
.none {
  // font-size: 18px;
  font-size: 14px;
  text-align: center;
  line-height: 65px;
  color: #666666;
  background: #fff;
}
.resourceTitle {
  .evertModule {
    display: flex;
    align-items: center;
    width: 100%;
  }
}
.everyModule {
  //padding-bottom: 30px;
}
.module {
  display: flex;
  align-items: center;
}
// 仪表盘
.ybpList {
  display: flex;
  justify-content: space-between;
  .ybpModule {
    width: 19%;
    padding: 20px 0px;
    height: 330px;
    background: #ffffff;
    box-shadow: 0px 4px 10px 0px rgba(218, 218, 218, 0.17);
    border-radius: 4px;
    text-align: center;
    line-height: 280px;
    .ybpSty {
      width: 80%;
      height: 260px;
      margin: 0 auto;
    }
    .unit {
      text-align: center;
      // font-size: 20px;
      font-size: 14px;

      font-weight: 400;
      color: #666666;
      line-height: 28px;
    }
  }
}

// 折线图
.zxList {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  //margin-top: 38px;
  .zxModule {
    width: 48.5%;
    height: 290px;
    padding: 20px 20px;
    background: #ffffff;
    box-shadow: 0px 0px 20px 0px rgba(218, 218, 218, 0.6);
    border-radius: 4px;
    margin: 20px 4px 10px;
    position: relative;
    .zxSty {
      width: 100%;
      height: 290px;
    }
    .timeList {
      position: absolute;
      bottom: 18px;
      width: 80%;
      margin-left: 12%;
      display: flex;
      justify-content: space-between;
      font-size: 12px;

      font-weight: 400;
      color: #666666;
    }
  }
}
.info {
  width: 100%;
  margin: 30px 0px 10px;
  display: flex;
  justify-content: space-between;
  .infoTitle {
    // font-size: 22px;
    font-size: 14px;
    .infoIcon {
      width: 3px;
      height: 14px;
      margin-right:5px;
      vertical-align: middle;
    }
    .infotext {
      color: #333333;
      vertical-align: middle;
    }
  }
}
/deep/ .el-form-item {
  margin-bottom: 0px;
}
/deep/ .el-form-item__label {
  // font-size: 18px;
  font-size: 14px;

  font-weight: 400;
  color: #333333;
  margin-top: 4px;
  line-height: 32px;
}
.resourceTitle /deep/ .el-input__inner {
  //   width: 270px;
  height: 32px;
  line-height:32px;
}
.xing {
  // font-size: 17px;
  font-size: 14px;

  font-weight: 400;
  color: #ff3a4c;
  margin: 4px 10px 0px 0px;
}
.resorceState {
  // font-size: 18px;
  font-size: 14px;

  font-weight: 400;
  color: #333333;
  padding: 10px 0px 0px;
  //   line-height: 40px;
  span {
    // font-size: 17px;
    font-size: 14px;
  }
  .address {
    padding-left:36px;
  }
  .ljAddress {
    margin-left:8px;
  }
  .setStyle {
    padding-left:44px;
  }
  .lijs {
    margin-left: 12px;
    padding-right: 240px;
  }
}
/deep/ .select-down{
  margin-top:4px !important;
  left:0px !important;
}
.setIptStyle {
  //padding: 0px 60px;
}
/deep/ .el-scrollbar__bar.is-vertical {
  opacity: 1 !important;//改为0不显示滚动条
}
</style>
