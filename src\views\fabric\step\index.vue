<template>
  <div class="page">
    <SpaceLayout top="20">
      <div slot="padding">
        <div class="header">
          <ol class="steps">
              <li class="step-active active">
                <div class="step-line"></div>
                <div class="step-content">
                    <span class="step-num">1</span>
                    <div class="step-text">选择建链方式</div>
                </div>
              </li>
              <li class="step-active">
                <div class="step-line"></div>
                <div class="step-content">
                    <span class="step-num">2</span>
                    <div> 基本参数配置</div>
                </div>
              </li>
              <li class="step-active">
                <div class="step-line"></div>
                <div class="step-content">
                    <span class="step-num">3</span>
                    <div>节点配置</div>
                </div>
              </li>
              <li class="step-active">
                <div class="step-line"></div>
                <div class="step-content">
                    <span class="step-num">4</span>
                    <div>部署状态检测</div>
                </div>
              </li>
              <li class="step-active">
                <div class="step-content">
                    <span class="step-num">5</span>
                    <div>完成</div>
                </div>
              </li>
          </ol>
        </div>
      </div>
    </SpaceLayout>
    <div class="content">
      <ul class="list">
        <li class="chain" :class="{active:chain=='exChain'}" @click="getChain('exChain')">
          <div class="item-up">
            <div class="title">标准版</div>
            <div class="text">标准配置，适用于Demo开发及测试场景。</div>
          </div>
          <div class="item-down">
            <div class="title">
              <span>共识方式：</span>
              <span class="solo">SOLO</span>
            </div>
            <div class="text">
              <div>网络配置：</div>
              <div class="firstdiv2 div2">1个组织</div>
              <div class="div2">1个CA节点</div>
              <div class="div2">1个Orderer节点</div>
              <div class="div2">4个Peer节点</div>
            </div>
          </div>
        </li>
        <li class="chain chain2" :class="{active:chain=='prChain'}" @click="getChain('prChain')">
          <div class="item-up">
            <div class="title">安全版</div>
            <div class="text">安全生产配置，适用于对安全要求较高的生产场景。</div>
          </div>
          <div class="item-down">
            <div class="title">
              <span>共识方式：</span>
              <span class="kafka">KAFKA</span>
            </div>
            <div class="text">
              <div>网络配置：</div>
              <div class="firstdiv2 div2">2个组织</div>
              <div class="div2">2个CA节点</div>
              <div class="div2">2个Orderer节点</div>
              <div class="div2">4个Peer节点</div>
            </div>
          </div>
        </li>
        <!-- <li class="chain chain3" :class="{active:chain=='cusChain'}" @click="getChain('cusChain')">
          <div class="item-up">
            <div class="title">专业链</div>
            <div class="text">自定义配置，适用于各类生产需求使用。</div>
          </div>
          <div class="item-down">
            <div class="title">
              <span>共识方式：</span>
              <span class="solo">SOLO</span>
              <span class="kafka">KAFKA</span>
            </div>
            <div class="text">
              <div>网络配置：</div>
              <div class="firstdiv2 div2">组织数量自定义</div>
              <div class="div2">节点数量自定义</div>
            </div>
          </div>
        </li> -->
      </ul>
    </div>
    <div class="btn-wrap">
      <!-- last-step next-step-->
      <el-button plain @click="getLastStep">上一步</el-button>
      <el-button type="primary" class="blue-btn" @click="goCreatChain">下一步</el-button>
    </div>
  </div>
</template>

<script>
//import {Message} from 'element-ui'
import SpaceLayout from '@/components/SpaceLayout'
export default {
  components: {
    SpaceLayout
  },
  data() {
    return {
      chain:'',
    };
  },
  methods:{
    getChain(type) {
      this.chain = type
    },
    getLastStep() {
      this.$router.push({
        path:'/instance'
      })
    },
    goCreatChain() {
      var type = ''
      if(this.chain == 'exChain') {
        type = '标准版'
      }
      if(this.chain == 'prChain') {
        type = '安全版'
      }
      if(this.chain == 'cusChain') {
        type = '专业链'
      }

     if(type) {
        sessionStorage.setItem('params',type)
        this.$router.push({
          path:'/guide/creatChain/' + this.chain,
        })
     }else {
       //Message.warning('请选择建链方式')
     }
    }
  }
}
</script>

<style lang="less" scoped>
// @keyframes mymove{
//     0%{
//       transform: scale(1);  /*开始为原始大小*/
//     }
//     100%{
//         transform: scale(1.1);
//     }
// }
.page {
  ul  {
    list-style: none;
    padding: 0;
  }
  .header {
    //width: 80%;
   // margin-top: 68px;
    margin-left: 22%;
     ol.steps::-webkit-scrollbar { /* chrome 隐藏滚动条*/
            display: none;
        }
        ol.steps{
            list-style: none;
            display: flex;
            height: 45px;
        }
        ol.steps li{
            float: left;
            flex: 1;
            position: relative;
           // width:140px;
        }
        ol.steps li .step-line{
          position: absolute;
          left: 30px;
          right: 40px;
        // width: 100%;
          height: 1Px;
          border-bottom: 1.5Px solid #3D73EF;
        }
        ol.steps li:nth-child(2) .step-line {
          left: 20px;
          right: 50px;
        }
        ol.steps li:nth-child(3) .step-line {
          left:10px;
          right:40px;
        }
        ol.steps li:nth-child(4) .step-line {
          left: 20px;
          right:60px;
        }
        ol.steps .step-content{
            position: absolute;
            top:-20px;
            left:-50px ;
            text-align: center;
        }
        ol.steps li.active .step-content{
            position: absolute;
            top:-20px;
            left:-40px ;
            text-align: center;
        }
        ol.steps .step-content div {
          margin-top: 15px;
          // font-size: 17px;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #666666;
        }
        ol.steps .step-content .step-text {
          color: #333333;
        }
        ol.steps li .step-content .step-num{
            display: inline-block;
            height: 40px;
            width: 40px;
            color: #3D73EF;
            background-color: #fff;
            line-height: 38px;
            border-radius: 50%;
            text-align: center;
            border:2px solid #3D73EF;
            // font-size:17px;
            font-size: 14px;
            font-weight: bold;
        }
        ol.steps li.active .step-content .step-num{
            height: 40px;
            width: 40px;
            line-height:38px;
            background-color:#3D73EF;
            color: #fff;
            border:2px solid #3D73EF;
        }
        ol.steps li.step-end{
            width: 120px!important;
            flex: inherit;
        }
        ol.steps li.step-end .step-line{
            display: none;
        }

  }
  .content {
    /*width: 60%;*/
    width: 700px;
    margin:30px auto;
    .list {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .chain {
        width: 300px;
        height: 400px;
        background-image: url('../../../assets/chainManage_images/overview/step1.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        overflow:hidden;
        border-radius: 6px;
        box-sizing: border-box;
        border: 1px solid #E5E5E5;
        &.chain2 {
          background-image: url('../../../assets/chainManage_images/overview/step2.png');
          &.active {
            transition: all 0.3s;
            transform: scale(1.1);
            border: 1Px solid #97B4E8;
            // -moz-box-shadow:2px 3px 12px rgb(187,209,230); -webkit-box-shadow:2px 3px 12px rgb(187,209,230); box-shadow:2px 3px 12px rgb(187,209,230);
            -moz-box-shadow:2px 10px 18px #BBD1E6, -2px 6px 12px #BBD1E6, 3px 1Px 5px #BBD1E6; -webkit-box-shadow:2px 10px 18px #BBD1E6, -2px 6px 12px #BBD1E6, 3px 1Px 5px #BBD1E6; box-shadow:2px 10px 18px #BBD1E6, -2px 6px 12px #BBD1E6, 3px 1Px 5px #BBD1E6;
          }
        }
        &.chain3 {
          background-image: url('../../../assets/chainManage_images/overview/step3.png');
        }
        &:hover {
          box-shadow: 4px 4px 10px #E5E5E5;
        }
        &.active {
          transition: all 0.3s;
          transform: scale(1.1);
          border: 1Px solid #0CBBFD;
          // -moz-box-shadow:2px 3px 12px rgb(187,209,230); -webkit-box-shadow:2px 3px 12px rgb(187,209,230); box-shadow:2px 3px 12px rgb(187,209,230);
          -moz-box-shadow:2px 10px 18px #BBD1E6, -2px 6px 12px #BBD1E6, 3px 1Px 5px #BBD1E6; -webkit-box-shadow:2px 10px 18px #BBD1E6, -2px 6px 12px #BBD1E6, 3px 1Px 5px #BBD1E6; box-shadow:2px 10px 18px #BBD1E6, -2px 6px 12px #BBD1E6, 3px 1Px 5px #BBD1E6;

        }
        .item-up {
          padding: 20px 56px 0 26px;
          color: #FFFFFF;
          font-family: Microsoft YaHei;
          .title {
            font-size:20px;
            font-weight: bold;
          }
          .text {
            margin-top: 12px;
            // font-size: 17px;
            font-size: 14px;
            line-height: 20px;

          }
        }
        .item-down {
          padding: 0px 0px 0 26px;
          margin-top: 50px;
          // font-size: 17px;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #333333;
          .title {
            .solo {
              display: inline-block;
              text-align: center;
              width: 72px;
              height: 34px;
              background:  rgba(0,173,162,0.2);
              border-radius: 4px;
              line-height: 34px;
              color: #00ADA2;
            }
            .kafka {
              display: inline-block;
              text-align: center;
              width: 72px;
              height: 34px;
              background:  rgba(25,115,204,0.2);
              border-radius: 4px;
              line-height: 34px;
              color:#1973CC;
            }
            .bft {
              display: inline-block;
              text-align: center;
              width: 72px;
              height: 34px;
              background:  rgba(225,85,0,0.2);
              border-radius: 4px;
              line-height: 34px;
              color:#E15500;
            }
          }
          .text {
            margin-top: 20px;
          }
          .firstdiv2 {
            margin-top: 20px;
          }
          .div2 {
            // font-size: 17px;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #666666;
            // line-height: 40px;
            line-height: 32px;
          }
        }
      }
    }
  }
  .btn-wrap {
      margin:20px auto 0;
      text-align: center;
      display: flex;
      justify-content: center;
      .last-step {
        width: 210px;
        height: 64px;
        background: #fff;
        border: 3px solid #E7ECEF;
        border-radius: 4px;
        font-size:14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666;
        line-height: 58px;
        cursor: pointer;
        margin-right: 180px;
      }
      .next-step {
        width: 192px;
        height: 64px;
        background: #337DFF;
        border: 2px solid;
        border-radius: 4px;
        font-size:14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 58px;
        cursor: pointer;
      }
    }
}
</style>
