<template>
  <div class="page">
    <div class="content">
      <div>
        <span class="back"  @click="goback">返回</span>
        <span>{{detail.ChannelName}}</span>
      </div>
      <div class="title">
        <div class="left">
          <!-- <span class="infotext" @click="goDetail">通道信息</span>
          <img :src="arrowIcon" class="icon">
          <span class="infotext"  @click="goChannelMgr">{{detail.ChannelName}}通道详情</span> -->
          <img :src="infoIcon" class="infoIcon">
          <span class="infotext">交易信息</span>
        </div>
      </div>
      <!-- 通道交易信息 -->
      <SpaceLayout top="0" paddingX="0" paddingY="0">
        <div slot="padding">
          <div class="table-wrapper">
            <el-row class="nav-box">
              <el-col :span="3"><div class="">交易号</div></el-col>
              <el-col :span="2"><div class="">所属区块</div></el-col>
              <el-col :span="5"><div class="">交易类型</div></el-col>
              <el-col :span="4"><div class="">交易时间</div></el-col>
              <el-col :span="3"><div class="">合约名称</div></el-col>
              <el-col :span="4"><div class="">交易参数</div></el-col>
              <el-col :span="3"><div class="">交易状态</div></el-col>
            </el-row>
            <div class="none" v-if="transList.length == 0">
              <i class="el-icon-loading" v-if="paddingText == '数据请求中...'"></i>
              <!-- <svg-icon icon-class="table-empty" v-else/> -->
              {{paddingText}}
            </div>
            <div class="nan-item" v-for="(item,tranIndex) in transList" :key="tranIndex">
              <el-row  class="nav-box">
                  <el-col :span="3">
                    <div class="">
                      <span class="">
                        <el-popover trigger="hover" placement="top">
                          <p>{{item.TxId}}</p>
                          <div slot="reference" class="name-wrapper">
                            <span class="text textLeft" slot="reference">{{item.TxId}}</span>
                          </div>
                        </el-popover>
                      </span>
                    </div>
                  </el-col>
                  <el-col :span="2"><div class=""><span>{{item.AttachBlockId}}</span></div></el-col>
                  <el-col :span="5"><div class=""><span>{{item.Type}}</span></div></el-col>
                  <el-col :span="4"><div class=""><span>{{dateFormat('YYYY-mm-dd HH:MM:SS',item.TimeStamp)}}</span></div></el-col>
                  <el-col :span="3"><div class=""><span class="">{{item.ChainCodeName}}</span></div> </el-col>
                  <el-col :span="4"><div class=""><span class="baseShow">{{item.Args}}</span></div></el-col>
                  <el-col :span="3">
                    <div v-if="item.Status&&item.Status.length<=14">{{item.Status}}</div>
                    <el-popover trigger="hover" placement="top" v-else>
                      <p>{{item.Status}}</p>
                      <div slot="reference" class="name-wrapper">
                        <div class="statuspopover">{{item.Status}}</div>
                      </div>
                    </el-popover>
                  </el-col>
              </el-row>
            </div>
          </div>
          <!-- <pagination
              @toHandleSizeChange="handleSizeChange"
              @toHandleCurrentChange="handleCurrentChange"
              @toJumpFirstPage="jumpFirstPage"
              @toJumpLastPage="jumpLastPage"
              :fTotal="total"
              :fBtnStartDisabled="btnStartDisabled"
              :fBtnEndDisabled="btnEndDisabled"
              :fPageIndex="pageIndex"
              :fZys="zys"
              >
          </pagination> -->
          <Page
          :total="total"
          :current.sync="pageIndex"
          @on-change="handleCurrentChange"
          :page-size="pageSize"
          :page-size-opts="[10,20,40,60,100]"
          show-total show-elevator show-sizer
          @on-page-size-change="handleSizeChange"
          style="text-align:right;"/>
        </div>
      </SpaceLayout>
    </div>
    <!-- <countDown v-if="isShowIcon" :state="countState" :countTime="countTime" :text="countText" @getCountDown="getCountDown"></countDown> -->
  </div>
</template>

<script>
import{getChannelTransactionListPage,getChannelTransactionDetail}from '@/api/baascore/channelMgr'
import SpaceLayout from '@/components/SpaceLayout'
import {forbidden} from "@/utils/index.js";
import { mapGetters } from 'vuex'
export default {
  mixins: [forbidden],
  props: {
    detail:{
      type: Object,
      default() {
          return {}
      }
    }
  },
  components: {
    SpaceLayout
  },
  data() {
    return {
      countState:'',
      countTime:2,
      countText:'',
      isShowIcon:false,
      arrowIcon:require('@/assets/chainManage_images/overview/arrow.png'),
      infoIcon: require("@/assets/chainManage_images/overview/infoIcon.png"),
      transList:[],//成员信息
      total:0,
      btnStartDisabled: false, //用来判断首页尾页按钮是否禁用
      btnEndDisabled: false, //用来判断首页尾页按钮是否禁用
      zys: 0,
      pageIndex: 1,
      pageSize:10,
      paddingText:'数据请求中...',
      listData:[],
      params:{
        ServiceId : '',
        ChannelName : '',
        PageSize:10,
        Index:1,
      },
      // detail:{
      //   ChannelName: "",
      //   ServiceId: "",
      // },
    }
  },
  computed: {
    ...mapGetters(['chainItem']),
    dateFormat(fmt, date) {
      return function(fmt, date) {
        date = new Date(date)
        let ret;
        const opt = {
            "Y+": date.getFullYear().toString(),        // 年
            "m+": (date.getMonth() + 1).toString(),     // 月
            "d+": date.getDate().toString(),            // 日
            "H+": date.getHours().toString(),           // 时
            "M+": date.getMinutes().toString(),         // 分
            "S+": date.getSeconds().toString()          // 秒
            // 有其他格式化字符需求可以继续添加，必须转化成字符串
        };
        for (let k in opt) {
            ret = new RegExp("(" + k + ")").exec(fmt);
            if (ret) {
              fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))

            };
        };
        return fmt;
      }

    },
  },
  watch : {
    // chainItem: {
    //   handler(newvalue, oldvalue) {
    //     if (newvalue.Id != oldvalue.Id) {
    //       this.$router.push({
    //         path:'/chainManage/channelMgr'
    //       })
    //     }
    //   },
    //   deep: true,
    // },
  },
  mounted () {
   // this.detail = JSON.parse(sessionStorage.getItem('detail'))
    document.body.scrollTop = 0;
    document.documentElement.scrollTop = 0;
    var ServiceId = this.detail.ServiceId
    var ChannelName = this.detail.ChannelName
    this.params.ServiceId = ServiceId
    this.params.ChannelName = ChannelName
    this.getChannelTransactionListPage(this.params)
  },
  methods: {
    getCountDown(type) {
      this.isShowIcon = false
    },
    // getListData() {
    //   this.listData = this.transList.slice((this.pageIndex -1) * this.pageSize, this.pageSize * this.pageIndex)
    // },
    //交易信息
    getChannelTransactionListPage(params) {
      getChannelTransactionListPage(params).then(res =>{
        if(res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if(res.data && res.data.Transactions) {
            this.transList = res.data.Transactions
            this.total = res.data.Count
            if(this.total == 0) {
              this.paddingText = '暂无数据'
            }
            this.zys = Math.ceil(this.total / this.pageSize); //获取总页数
           // this.transList = this.transList.reverse()
            this.transList.forEach((item,index) =>{
              if(item.TxId || item.TxId === 0) {
                var obj = {
                  ServiceId : this.detail.ServiceId,
                  ChannelName : this.detail.ChannelName,
                  TxId:item.TxId
                }
                getChannelTransactionDetail(obj).then(res =>{
                  if(res.code == 200) {
                    // this.isShowIcon = true
                    // this.countState = 'success'
                    // this.countText = '请求成功！'
                    this.$set(this.transList[index],'Type',res.data.Type)
                    this.$set(this.transList[index],'Status',res.data.Status)
                    this.$set(this.transList[index],'ChainCodeName',res.data.ChainCodeName)
                    this.$set(this.transList[index],'Args',res.data.Args)
                  }
                  else {
                    // this.isShowIcon = true
                    // this.countState = 'error'
                    // this.countText = '数据获取失败，请重新加载！'
                    this.$message.error("数据获取失败，请重新加载！");
                  }
                })
              }
            })
          }else {
            this.paddingText = '暂无数据'
          }
        }
        if(res.code != 200) {
          this.paddingText = '暂无数据'
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error("数据获取失败，请重新加载！");
        }
      })
    },
    goback() {
      this.$emit('getisShowTransList',false)
    },
    goChannelMgr() {
     this.$emit('getisShowTransList',false)
    },
    goDetail() {
      this.$emit('getIsShowDetial',false)
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.params.PageSize  = val
      this.zys = Math.ceil(this.total / this.pageSize); //获取总页数
      this.params.Index > this.zys ? this.params.Index = this.zys : '';
      this.getChannelTransactionListPage(this.params)
    },
    handleCurrentChange(val) {
      this.pageIndex = val
      this.params.Index = val
      this.getChannelTransactionListPage(this.params)
    },
    // 首页按钮
    jumpFirstPage(val) {
      this.pageIndex = val;
      this.handleCurrentChange(val);
    },
    // 尾页按钮
    jumpLastPage(val) {
      this.pageIndex = val;
      this.handleCurrentChange(this.pageIndex);
    },
  }
}
</script>

<style rel="stylesheet/less" lang="less" scoped>
@import "../../../../styles/common/modal.less";
.page {
  width: 100%;
  height: 100%;
 // margin-top:-20px;
  .content {
   // padding-bottom: 50px;
    .title {
      margin:20px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .infotext {
        // font-size: 20px;
        font-size: 14px;
        color: #333333;
        vertical-align: middle;
        cursor: pointer;
      }
      .icon {
        width: 22px;
        height: 22px;
        margin: 0 24px;
      }
      .name {
        // font-size: 20px;
        font-size: 14px;
        font-weight: bold;
        color: #333333;
        cursor: default;
      }
      .left {
        display: flex;
        align-items: center;
        .infoIcon {
          width: 3px;
          height: 14px;
          margin-right:5px;
          vertical-align: middle;
        }
        .infotext {
          // font-size: 22px;
          font-size: 14px;
          color: #333333;
          vertical-align: middle;
        }
      }

    }
    .none {
      text-align: center;
      color: #666;
      // font-size: 18px;
      font-size: 14px;
    }
    .nav-box{
      color: #999999;
      // font-size: 18px;
      font-size: 14px;
      box-sizing: border-box;
      /*margin-top:20px;*/
    }
    .nav-box /deep/ .el-col{
      text-align: center;
      color: #999;
      // font-size: 18px;
      font-size: 14px;
      padding:10px 0;
      //line-height: 36px;
    }
    .nav-box .el-col div span{
      /*cursor: default;*/
    }
    .nan-item .nav-box:hover{
      -moz-box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6; -webkit-box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6; box-shadow:2px 2px 6px #BBD1E6, -2px -2px 6px #BBD1E6;
    }
    .nan-item .nav-box{
        // font-size: 16px;
        font-size: 14px;
        box-sizing: border-box;
        background: #fff;
        display: flex;
        align-items: center;
    }
    .nan-item .nav-box /deep/ .el-col {
      color: #666666;
    }
    .nan-item .nav-box .el-col div span.call {
      display: inline-block;
      width: 102px;
      height: 36px;
      border-radius: 4px;
      text-align: center;
      line-height: 36px;
      background: #00ADA2;
      // font-size: 16px;
      font-size: 14px;
      color: #FFFFFF;
    }
    .nan-item .nav-box .el-col div span.text {
      display: block;
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
      /*padding: 0 40px;*/
    }
    .nan-item .nav-box .el-col div span.baseShow {
      display: block;
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
      padding: 0 ;
    }
    .nan-item .nav-box .el-col div span.baseShow:hover {
      word-wrap: break-word;
      word-break: normal;
      white-space: normal;
    }
    .nan-item .nav-box .el-col div span.textLeft {
      /*padding-left: 36px;*/
    }
    .pagination {
      /*margin-top: 16px;*/
      // display: flex;
      // justify-content: flex-end;
      // align-items: center;
    }
  }
  .ivu-page{
    margin-top:10px;
  }

}
</style>
