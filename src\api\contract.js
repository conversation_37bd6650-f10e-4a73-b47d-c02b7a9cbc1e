import axios from './index'

// 下载安全报告接口
export const getDownsecurity = (filename, filepath) => {
  return axios.request({
    url: '/cmbaas/chain/audit/downloadFile?fileName=' + filename + '&filePath=' + filepath,
    method: 'GET',
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json; application/octet-stream'
    }
  })
}
// 合约部署链账户
export const getChainTableDataLian = (chainId, cppName, chainAccountName = '', accountType = '', tenantName = '', status = '', bizType = '', contractId = '', isSingleCpp = '', zipName = '') => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + JSON.parse(chainId) + '/accountList',
    data: {
      chainId,
      accountType,
      chainAccountName,
      tenantName,
      cppName,
      status,
      bizType,
      contractId,
      isSingleCpp,
      zipName
    },
    method: 'POST'
  })
}

// 新增系统配置
export const addconfig = (params) => {
  return axios.request({
    url: '/cmbaas/portal/config',
    data: params,
    method: 'POST'
  })
}

// 配置管理列表
export const managementList = (params) => {
  return axios.request({
    url: '/cmbaas/portal/config/management/list',
    data: params,
    method: 'POST'
  })
}

// 配置管理日志
export const blogList = (params) => {
  return axios.request({
    url: '/cmbaas/portal/config/management/blog',
    data: params,
    method: 'POST'
  })
}
// 删除配置
export const deleteconfig = (Id) => {
  return axios.request({
    url: '/cmbaas/portal/config/delete/' + Id,
    method: 'POST'
  })
}
/** ****************以上是合约部署接口**************** */
export const ExaminationApproval = params => {
  return axios.request({
    url: '/cmbaas/chain/audit/makeup/chain',
    data: params,
    method: 'POST'
  })
}
/** ****************合约审批和链账户审批接口**************** */

// 查询配置项
export const getconfig = (name) => {
  return axios.request({
    url: '/cmbaas/portal/config/getConfig/' + name,
    method: 'GET'
  })
}

/** ************** 合约库管理上架合约模板********************* */

export const getStencils = (getCollContent) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/addContractModel',
    data: getCollContent,
    method: 'POST'
  })
}

// 合约库管理列表
export const getlibraryTable = (list) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/findDeploySuccessList',
    data: list,
    method: 'POST'
  })
}
// 合约详情下载接口
export const getdownloadFile = (contractId, contractName, uploadVersion, treasuryToken) => {
  const data = new FormData()
  data.append('treasuryToken', treasuryToken)
  return axios.request({
    url: '/cmbaas/contract/eos/contract/model/downloadFile/' + contractId + '/' + contractName + '/' + uploadVersion,
    method: 'POST',
    responseType: 'blob',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
    // headers: { 'Content-Type': 'application/json; application/octet-stream' }
  })
}
// 查询智能合约版本信息
export const getShelvesTem = (shelvesId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/model/' + shelvesId,
    method: 'GET'
  })
}
// 上架合约市场
export const getShelvesAgora = (getCollContent) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/market/add',
    data: getCollContent,
    method: 'POST'
  })
}
/** ************** 以上是合约库管理接口********************* */
// 省市查询接口
export const getserchList = (list) => {
  return axios.request({
    url: '/cmbaas/portal/city/list',
    data: list,
    method: 'POST'
  })
}
// 应用管理列表
export const alicationTable = (list) => {
  return axios.request({
    url: '/cmbaas/portal/application/center/getList',
    data: list,
    method: 'POST'
  })
}
/// / 应用管理新增
export const alicationAdd = (adddata) => {
  return axios.request({
    url: '/cmbaas/portal/application/center/add',
    data: adddata,
    method: 'POST'
  })
}
// 应用管理列表修改
export const alicationUpdate = (data) => {
  return axios.request({
    url: '/cmbaas/portal/application/center/update',
    data: data,
    method: 'POST'
  })
}

// 应用管理列表删除
export const alicationDelete = (Id, type) => {
  return axios.request({
    url: '/cmbaas/portal/application/center/delete?applicationId=' + Id + '&appType=' + type,
    method: 'POST'
  })
}
// 新增查询租户列表
export const getTenantList = () => {
  return axios.request({
    url: '/cmbaas/portal/tenant/listAll',
    method: 'GET'
  })
}
// 合约库管理详情
// 查询智能合约详情
export const getnewContractDetails = (contractId) => {
  return axios.request({
    url: '/cmbaas/contract/eos/contract/deploy/' + contractId + '/SUCCESS',
    method: 'GET'
  })
}
/** ************** 以上是应用管理接口********************* */
// 配置资源
export const configurationResources = (data) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + data.chainId + '/account/' + data.chainAccountName + '/resourcesOperator',
    data: {
      applySourceMsg: data.applySourceMsg, //
      applySize: data.applySize, // 申请大小
      applyReason: data.applyReason, // 申请理由
      description: data.description, // 合约描述
      tenantName: data.tenantName, // 租户名称
      ownerTenantId: data.ownerTenantId, // 租户id
      chainAccountId: data.chainAccountId, //
    },
    method: 'POST'
  })
}
// 链账户资源审批列表
export const getAccountResource = (chainAccountName, userIdStr, statusStr, tablePageParam) => {
  return axios.request({
    url: '/cmbaas/chain/audit/getAccountResourceRecord',
    data: {
      chainAccountName: chainAccountName, // 链账户名称
      tenantId: userIdStr, // 租户name
      status: statusStr, //  状态
      pageParam: tablePageParam // 分页
    },
    method: 'POST'
  })
}
// 链账户资源审批详情
export const getResourceDetail = (id) => {
  return axios.request({
    url: '/cmbaas/chain/audit/getResourceDetail/' + id,
    method: 'GET'
  })
}
// 链账户资源审批
export const getResourceRecord = (id, recordStatus, auditRemark, chainId) => {
  return axios.request({
    url: '/cmbaas/chain/audit/resourceRecord',
    data: {
      id,
      recordStatus, //  审批状态
      auditRemark, // 审批说明
      chainId
    },
    method: 'POST'
  })
}
/** ************** 以上是链账户资源审批/配置资源接口********************* */
// 公司列表
export const companyList = (data) => {
  return axios.request({
    url: '/cmbaas/portal/company/list/page',
    data: data,
    method: 'POST'
  })
}
// 新增公司
export const addCompany = (data) => {
  return axios.request({
    url: '/cmbaas/portal/company/add',
    data: data,
    method: 'POST'
  })
}
// 删除公司
export const deleteCompany = (id) => {
  return axios.request({
    url: '/cmbaas/portal/company/delete/' + id,
    method: 'POST'
  })
}
// 公司列表
export const tenantcompanyList = (data) => {
  return axios.request({
    url: '/cmbaas/portal/company/list',
    data: data,
    method: 'POST'
  })
}
// 修改公司
export const updateCompany = (data) => {
  return axios.request({
    url: '/cmbaas/portal/company/update',
    data: data,
    method: 'POST'
  })
}
/** ************** 以上是公司和租户管理新增接口********************* */
// ipfs列表
export const ipfsList = (data) => {
  return axios.request({
    url: '/cmbaas/ipfs/list',
    data: data,
    method: 'POST'
  })
}
// ipfs新建
export const addIpfs = (data) => {
  return axios.request({
    url: '/cmbaas/ipfs/addipfs',
    data: data,
    method: 'POST'
  })
}
// 详情节点列表
export const nodeList = (data) => {
  return axios.request({
    url: '/cmbaas/ipfs/node/list',
    data: data,
    method: 'POST'
  })
}
// 详情节点新增
export const nodeSave = (data) => {
  return axios.request({
    url: '/cmbaas/ipfs/node/save',
    data: data,
    method: 'POST'
  })
}
// 详情节点删除
export const nodeDelete = (ipfsId, nodeId) => {
  return axios.request({
    url: '/cmbaas/ipfs/node/delete/' + ipfsId + '/' + nodeId,
    method: 'POST'
  })
}
// 详情节点编辑
export const nodeUpdate = (data) => {
  return axios.request({
    url: '/cmbaas/ipfs/node/update',
    data: data,
    method: 'PUT'
  })
}
// 查询节点资源
export const getNodeInfo = (nodeId) => {
  return axios.request({
    url: '/cmbaas/ipfs/node/getNodeInfo/' + nodeId,
    method: 'GET'
  })
}
// 查询资源命名空间
export const namespaceList = () => {
  return axios.request({
    url: '/cmbaas/ipfs/namespace/list',
    method: 'GET'
  })
}
// 组网状态
export const getNetwork = (data) => {
  return axios.request({
    url: '/cmbaas/ipfs/node/getNetworkState',
    data: data,
    method: 'POST'
  })
}
/** ************** 以上是ipfs新增接口********************* */
export const uploadimportMainChain = (file) => {
  const data = new FormData()
  data.append('normalAccFile', file.pFile)
  data.append('contractAccFile', file.hFile)
  data.append('contractfile', file.zFile)
  data.append('relationFile', file.guanFile)
  return axios.request({
    url: '/cmbaas/chain/import/importMainChainData',
    data: data,
    method: 'POST'
  })
}
/** ************** 以上是主链纳管新增接口********************* */
// 客服跳转接口
export const customerfrom = (data) => {
  return axios.request({
    url: '/cmbaas/portal/config/getValueByNameAndStatus?name=' + data.name + '&' + 'status=' + data.status,
    data: data,
    method: 'POST'
  })
}
// /** ************** 以上是新增客服跳转接口********************* */
// js文件上传
export const uploadjsFile = (
  contractId,
  jsFile,
  apiFile,
  size,
  securityScanReportFile,
  languageType,
  interfacDoc
) => {
  const data = new FormData()
  data.append('size', size)
  data.append('jsFile', jsFile)
  data.append('abiFile', apiFile)
  data.append('languageType', languageType)
  data.append('interfacDoc', interfacDoc)
  securityScanReportFile.forEach((val) => {
    data.append('securityScanReportFile ', val)
  })
  return axios.request({
    url: '/cmbaas/contract/eos/contract/base/file/' + JSON.parse(contractId),
    data: data,
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 查询目标链
export const getChainid = (chainId) => {
  return axios.request({
    url: '/cmbaas/chain/eos/chain/' + chainId,
    method: 'POST'
  })
}
// 生成abi文件
export const abidownFile = (
  jsFile,
  storePath
) => {
  const data = new FormData()
  data.append('jsFile', jsFile)
  data.append('jsStorePath', storePath)
  return axios.request({
    url: '/cmbaas/contract/eos/contract/getAbiFileByJs',
    data: data,
    method: 'POST',
    responseType: 'blob',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
/** ************** 以上是js新增接口********************* */
export const uploadmanyFile = (
  contractId,
  isSingleCpp,
  compressed,
  interfacDoc,
  size,
  securityScanReportFile,
  languageType
) => {
  const data = new FormData()
  data.append('size', size)
  data.append('isSingleCpp', isSingleCpp)
  data.append('compressed', compressed)
  data.append('interfacDoc', interfacDoc)
  data.append('languageType', languageType)
  securityScanReportFile.forEach((val) => {
    data.append('securityScanReportFile ', val)
  })
  return axios.request({
    url: '/cmbaas/contract/eos/contract/base/file/' + JSON.parse(contractId),
    data: data,
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
/** ************** 以上是js新增接口********************* */
export const getauditdownloadFile = (deployId) => {
  return axios.request({
    url: '/cmbaas/chain/audit/downloadFile/' + deployId,
    method: 'GET',
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json; application/octet-stream'
    }
  })
}
export const getConfigMap = (data) => {
  return axios.request({
    url: '/cmbaas/portal/config/findSysConfigMap',
    data: data,
    method: 'POST'
  })
}
// 文档规范下载

export const getFile = (file) => {
  return axios.request({
    url: '/cmbaas/portal/announcement/file/downloadByType/' + file,
    responseType: 'blob',
    method: 'POST'
  })
}

// 文件管理列表查询

export const getFileList = (data) => {
  return axios.request({
    url: '/cmbaas/portal/project/list',
    data: data,
    method: 'POST'
  })
}
// 新增项目和编辑项目
export const getFileAdd = (data) => {
  return axios.request({
    url: '/cmbaas/portal/project/add',
    data: data,
    method: 'POST'
  })
}
// 文件管理上传
export const fileUploadmanyFile = (
  projectId,
  file,
) => {
  const data = new FormData()
  data.append('file', file)
  return axios.request({
    url: '/cmbaas/portal/project/upload/' + JSON.parse(projectId),
    data: data,
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}


// 文件管理详情

export const getFileListDetail = (data) => {

  return axios.request({
    url: '/cmbaas/portal/project/upload/list/' + data.id,
    data: data,
    method: 'POST'
  })
}

// 文件管理项目删除
export const getFileListDetete = (data) => {

  return axios.request({
    url: '/cmbaas/portal/project/delete',
    data: data,
    method: 'POST'
  })
}

// 文件删除
export const getFileDetete = (data) => {

  return axios.request({
    url: '/cmbaas/portal/project/deleteFile',
    data: data,
    method: 'POST'
  })
}

// 文件下载
export const getFileDown = (hash) => {

  return axios.request({
    url: '/cmbaas/portal/project/file/download/' + hash,
    responseType: 'blob',
    method: 'POST'
  })
}

// 附件管理列表
export const getFileListAnnex = (data) => {
  return axios.request({
    url: '/cmbaas/portal/project/annex/list',
    data: data,
    method: 'POST'
  })
}

//附件编辑
export const getFileAddAnnex = (data) => {
  return axios.request({
    url: '/cmbaas/portal/project/annex/add',
    data: data,
    method: 'POST'
  })
}
// 附件文件上传
export const fileUploadmanyFileAnnex = (
  projectId,
  file,
) => {
  const data = new FormData()
  data.append('file', file)
  return axios.request({
    url: '/cmbaas/portal/project/annex/upload/' + JSON.parse(projectId),
    data: data,
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 合约附件详情

export const getFileListDetailAnnex = (data) => {

  return axios.request({
    url: '/cmbaas/portal/project/annex/upload/list/' + data.id,
    data: data,
    method: 'POST'
  })
}

// 合约附件详情删除
export const getFileDeteteAnnex = (data) => {

  return axios.request({
    url: '/cmbaas/portal/project/annex/deleteFile',
    data: data,
    method: 'POST'
  })
}
// 合约附件详情下载
export const getFileDownAnnex = (hash) => {

  return axios.request({
    url: '/cmbaas/portal/project/annex/file/download/' + hash,
    responseType: 'blob',
    method: 'POST'
  })
}


//帮助文档列表
export const getFileListHelp = (data) => {
  return axios.request({
    url: '/cmbaas/portal/project/list/upload',
    data: data,
    method: 'POST'
  })
}

// 帮助文档下载
export const getFileDownHelp = (hash) => {

  return axios.request({
    url: '/cmbaas/portal/project/currency/file/download/' + hash,
    responseType: 'blob',
    method: 'POST'
  })
}

// 新建权限手动前调用接口
export const getPermission = (params) => {
  const data = new FormData()
  data.append('publicKey', params.publicKey)
  data.append('privateKey', params.privateKey)
  return axios.request({
    url: '/cmbaas/chain/eos/chain/checkPermission',
    data: data,
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 节点编辑
export const getNodeDetails = (chainId, nodeId) => {

  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/' + chainId + '/' + nodeId,
    // data: params,
    method: 'GET',
  })
}

/** ************** 以上是客服、附件功能新增接口********************* */
/** ************** 以上是s14接口********************* */
/** ************** 以上是客服、附件功能新增接口********************* */

// 大屏显示隐藏接口


export const getCheckRole = () => {

  return axios.request({
    url: '/cmbaas/chain/cd/largeScreen/checkRole',
    // responseType: 'blob',
    method: 'POST',
    data: {}
  })
}

// 埋点数据
export const getLogsave = (data) => {
  return axios.request({
    url: '/cmbaas/portal/v2/page/operate/log/save',
    data: data,
    method: 'POST'
  })
}
// 分区用户列表
export const getUserChildList = (data) => {
  return axios.request({
    url: '/cmbaas/portal/user/child/list',
    data: data,
    method: 'POST'
  })
}
// 区块链数据导出
export const getExportList = (data) => {
  return axios.request({
    url: '/cmbaas/chain/eos/multi/chain/export/list',
    responseType: 'blob',
    data: data,
    method: 'POST'
  })
}

