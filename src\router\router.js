import Layout from '@/views/layout.vue'
import dash from '@/assets/selfIcon/dash.png'
import eos from '@/assets/selfIcon/eos.png'
import eos1009 from '@/assets/selfIcon/eos1009.png'
import eos1010 from '@/assets/selfIcon/eos1010.png'
import eos1011 from '@/assets/selfIcon/eos1011.png'
// import eos1012 from '@/assets/selfIcon/eos1012.png'
import mng from '@/assets/selfIcon/mng.png'
import mng1016 from '@/assets/selfIcon/mng1016.png'
import mng1017 from '@/assets/selfIcon/mng1017.png'
import mng1018 from '@/assets/selfIcon/mng1018.png'
import mng1019 from '@/assets/selfIcon/mng1019.png'
import mng1020 from '@/assets/selfIcon/mng1020.png'
import mng1021 from '@/assets/selfIcon/mng1021.png'
import mng1022 from '@/assets/selfIcon/mng1022.png'
import mng1023 from '@/assets/selfIcon/mng1023.png'
import mng1024 from '@/assets/selfIcon/mng1024.png'
import mng1025 from '@/assets/selfIcon/mng1025.png'
import mng1026 from '@/assets/selfIcon/mng1026.png'
import mng1027 from '@/assets/selfIcon/mng1027.png'
import mng1028 from '@/assets/selfIcon/mng1028.png'
import mng1030 from '@/assets/selfIcon/mng1030a.png'
import mng1032 from '@/assets/selfIcon/mng1032.png'

import fab from '@/assets/selfIcon/fab.png'
import fab01 from '@/assets/selfIcon/fab01.png'
import fab02 from '@/assets/selfIcon/fab02.png'
// 黑色
import dashb from '@/assets/selfIcon/dashb.png'
import eosb from '@/assets/selfIcon/eosb.png'
import eos1009b from '@/assets/selfIcon/eos1009b.png'
import eos1010b from '@/assets/selfIcon/eos1010b.png'
import eos1011b from '@/assets/selfIcon/eos1011b.png'
// import eos1012b from '@/assets/selfIcon/eos1012b.png'
import mngb from '@/assets/selfIcon/mngb.png'
import mng1016b from '@/assets/selfIcon/mng1016b.png'
import mng1017b from '@/assets/selfIcon/mng1017b.png'
import mng1018b from '@/assets/selfIcon/mng1018b.png'
import mng1019b from '@/assets/selfIcon/mng1019b.png'
import mng1020b from '@/assets/selfIcon/mng1020b.png'
import mng1021b from '@/assets/selfIcon/mng1021b.png'
import mng1022b from '@/assets/selfIcon/mng1022b.png'
import mng1023b from '@/assets/selfIcon/mng1023b.png'
import mng1024b from '@/assets/selfIcon/mng1024b.png'
import mng1025b from '@/assets/selfIcon/mng1025b.png'
import mng1026b from '@/assets/selfIcon/mng1026b.png'
import mng1027b from '@/assets/selfIcon/mng1027b.png'
import mng1028b from '@/assets/selfIcon/mng1028b.png'
import mng1030b from '@/assets/selfIcon/mng1030b.png'
import mng1032b from '@/assets/selfIcon/mng1032b.png'

import mng1033 from '@/assets/selfIcon/mng1033.png'
import mng1033b from '@/assets/selfIcon/mng1033b.png'
import mng1034 from '@/assets/selfIcon/mng1034.png'
import mng1034b from '@/assets/selfIcon/mng1034b.png'
import mng1035 from '@/assets/selfIcon/mng1035.png'
import mng1035b from '@/assets/selfIcon/mng1035b.png'
import mng1036 from '@/assets/selfIcon/mng1036.png'
import mng1036b from '@/assets/selfIcon/mng1036b.png'

import app01 from '@/assets/selfIcon/app01.png'
import app01b from '@/assets/selfIcon/app01b.png'
// import fabb from '@/assets/selfIcon/fabb.png'
import cm01 from '@/assets/img/chainmaker/cm01.png'
import cm02 from '@/assets/img/chainmaker/cm02.png'
import {
  prophecyManagementRouters
} from './prophecy'
export const routerMap = []

export const routes = [{
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录'
    },
    component: () => import('@/views/login.vue')
  },
  {
    path: '/register',
    name: 'register',
    meta: {
      title: '注册'
    },
    component: () => import('@/views/register.vue')
  },
  {
    path: '/forget_pwd',
    name: 'forget_pwd',
    meta: {
      title: '忘记密码'
    },
    component: () => import('@/views/forget-pwd.vue')
  },
  {
    path: '/reset_pwd',
    name: 'reset_pwd',
    meta: {
      title: '忘记密码'
    },
    component: () => import('@/views/reset-pwd.vue')
  },
  {
    path: '/token_disabled',
    name: 'token_disabled',
    meta: {
      title: 'token失效'
    },
    component: () => import('@/views/token-disabled.vue')
  },
  {
    path: '/Logout',
    name: 'Logout',
    meta: {
      title: '退出登录'
    },
    component: () => import('@/views/Logout.vue')

  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: Layout,
    icon: 'md-home',
    selfIcon: dash,
    selfIconb: dashb,
    meta: {
      title: 'Dashboard'
    },
    children: [{
      path: 'dashboard_index',
      name: 'dashboard',
      icon: 'md-apps',
      selfIcon: dash,
      selfIconb: dashb,
      meta: {
        title: 'Dashboard'
      },
      component: () => import('@/views/dashboard/index.vue')
    }]
  },
  // 以下是新菜单
  // 区块链应用
  {
    path: '/menu_block_chain_manage',
    name: 'menu_block_chain_manage',
    component: Layout,
    icon: 'md-globe',
    selfIcon: eos,
    selfIconb: eosb,
    meta: {
      title: '区块链管理'
    },
    children: [{
        path: '/browser_index',
        name: 'browser_index',
        icon: 'ios-analytics',
        meta: {
          title: '浏览器'
        },
        component: () => import('@/views/eos/browser/index.vue'),
        // redirect: { name: 'browser' }, // 父路由重定向
        children: [
          // {
          //   path: '/browser',
          //   name: 'browser',
          //   icon: 'ios-analytics',
          //   selfIcon: eos1009,
          //   selfIconb: eos1009b,
          //   meta: {
          //     title: 'EOS浏览器'
          //   },
          //   component: () => import('@/views/eos/browser/browser.vue')
          // },
          {
            path: '/browser_block',
            name: 'browser_block',
            icon: 'ios-analytics',
            meta: {
              title: '特定区块'
            },
            hidden: true,
            component: () => import('@/views/eos/browser/browser-block.vue')
          },
          {
            path: '/browser_trade',
            name: 'browser_trade',
            icon: 'logo-buffer',
            meta: {
              title: '特定交易'
            },
            hidden: true,
            component: () => import('@/views/eos/browser/browser-trade.vue')
          },
          {
            path: '/browser_chain',
            name: 'browser_chain',
            icon: 'logo-buffer',
            meta: {
              title: '账户页'
            },
            hidden: true,
            component: () => import('@/views/eos/browser/browser-chain.vue')
          }
        ]
      },
      {
        path: '/chain_table',
        name: 'chain_table',
        icon: 'md-person-add',
        meta: {
          title: '链账户管理'
        },
        component: () => import('@/views/eos/mychain/index.vue'),
        children: [
          // {
          //   path: '/chain_table',
          //   name: 'chain_table',
          //   icon: 'md-person-add',
          //   selfIcon: eos1010,
          //   selfIconb: eos1010b,
          //   meta: {
          //     title: '我的链账户'
          //   },
          //   component: () => import('@/views/eos/mychain/chain-table.vue')
          // },
          {
            path: '/new_user',
            name: 'new_user',
            icon: 'md-add-circle',
            meta: {
              title: '创建链账户'
            },
            hidden: true,
            component: () => import('@/views/eos/mychain/new-user.vue')
          },
          {
            path: '/role/:res',
            name: 'result_page',
            icon: 'md-ribbon',
            meta: {
              title: '处理结果'
            },
            hidden: true,
            component: () => import('@/views/eos/mychain/result-page.vue'),
            props: true
          },
          {
            path: '/chain_details',
            name: 'chain_details',
            icon: 'md-pricetag',
            meta: {
              title: '链账户详情'
            },
            hidden: true,
            component: () => import('@/views/eos/mychain/chain-details.vue')
          },
          {
            path: '/chain_resource_details',
            name: 'chain_resource_details',
            icon: 'md-pricetag',
            meta: {
              title: '资源详情'
            },
            hidden: true,
            component: () => import('@/views/eos/mychain/chain-resource-details.vue')
          }
        ]
      },
      {
        path: '/contract_table',
        name: 'contract_table',
        icon: 'md-bookmarks',
        meta: {
          title: '智能合约'
        },
        component: () => import('@/views/eos/mycontract/index.vue'),
        // redirect: { name: 'contract_table' }, // 父路由重定向
        children: [{
            path: '/contract_area',
            name: 'contract_area',
            icon: 'md-bookmarks',
            meta: {
              title: '合约广场'
            },
            hidden: true,
            component: () => import('@/views/eos/mycontract/contract-area.vue')
          },
          {
            path: '/contract_details/:contractId/:tenantId',
            name: 'contract_details',
            icon: 'md-pricetag',
            meta: {
              title: '合约详情'
            },
            hidden: true,
            component: () => import('@/views/eos/mycontract/contract-details.vue')
          },
          {
            path: '/contract_analysis',
            name: 'contract_analysis',
            icon: '',
            meta: {
              title: '智能合约数据'
            },
            hidden: true,
            component: () => import('@/views/eos/mycontract/contract-analysis.vue')
          },
          {
            path: '/sharecontract_details/:shareRecordId/:receivedTenantId',
            name: 'sharecontract_details',
            icon: 'md-pricetag',
            meta: {
              title: '共享合约详情'
            },
            hidden: true,
            component: () => import('@/views/eos/mycontract/sharecontract-details.vue')
          },
          {
            path: '/contract_new',
            name: 'contract_new',
            icon: 'md-bookmarks',
            meta: {
              title: '新建合约详情'
            },
            hidden: true,
            component: () => import('@/views/eos/mycontract/contract-new.vue')
          },
          {
            path: '/contract_shelves/:listId',
            name: 'contract_shelves',
            icon: 'md-bookmarks',
            meta: {
              title: '上架合约市场'
            },
            hidden: true,
            component: () => import('@/views/eos/mycontract/contract-shelves.vue')
          }
        ]
      },
      {
        path: '/multilink_admin',
        name: 'multilink_admin',
        icon: 'md-aperture',
        meta: {
          title: '链管理'
        },
        component: () => import('@/views/myadmin/multilink/index.vue'),
        children: [
          // {
          //   path: '/multilink_admin',
          //   name: 'multilink_admin',
          //   selfIcon: mng1022,
          //   selfIconb: mng1022b,
          //   meta: {
          //     title: '链管理'
          //   },
          //   component: () => import('@/views/myadmin/multilink/admin.vue')
          // },
          {
            path: '/multilink_details/:eosChainId',
            name: 'multilink_details',
            selfIcon: mng1022,
            selfIconb: mng1022b,
            meta: {
              title: '链详情'
            },
            hidden: true,
            component: () => import('@/views/myadmin/multilink/details.vue')
          }
        ]
      },
      {
        path: '/token_admin',
        name: 'token_admin',
        icon: 'logo-usd',
        meta: {
          title: '链账户资源管理'
        },
        component: () => import('@/views/myadmin/token-admin.vue')
      },
    ]
  },
  // 系统管理
  {
    path: '/menu_system_manage',
    name: 'menu_system_manage',
    component: Layout,
    icon: 'md-globe',
    selfIcon: mng1020,
    selfIconb: mng1020b,
    meta: {
      title: '系统管理'
    },
    children: [{
        path: '/application_admin',
        name: 'application_admin',
        icon: 'logo-usd',
        meta: {
          title: '服务管理'
        },
        component: () => import('@/views/myadmin/applicationmenu/index.vue')
      },
      {
        path: '/config_admin',
        name: 'config_admin',
        icon: 'logo-usd',
        meta: {
          title: '配置管理'
        },
        component: () => import('@/views/myadmin/settingmenu/index.vue')
      },
      {
        path: '/file_manage',
        name: 'file_manage',
        icon: 'md-globe',
        meta: {
          title: '文件管理'
        },
        component: () => import('@/views/myadmin/filemanage/index.vue'),
        children: [{
            path: '/file_details/:id',
            name: 'file_details',
            icon: 'md-globe',
            selfIcon: fab,
            meta: {
              title: '项目详情'
            },
            hidden: true,
            component: () => import('@/views/myadmin/filemanage/detail.vue')
          },
          {
            path: '/file_annex_details/:id',
            name: 'file_annex_details',
            icon: 'md-globe',
            selfIcon: fab,
            meta: {
              title: '项目详情'
            },
            hidden: true,
            component: () => import('@/views/myadmin/filemanage/attachment_detail.vue')
          }
        ]
      },
    ]
  },
  // 组织权限管理
  {
    path: '/menu_group_auth_manage',
    name: 'menu_group_auth_manage',
    component: Layout,
    icon: 'md-person',
    selfIcon: mng1025,
    selfIconb: mng1025b,
    meta: {
      title: '组织权限管理'
    },
    children: [{
        path: '/tenant_admin',
        name: 'tenant_admin',
        icon: 'md-contacts',
        meta: {
          title: '租户管理'
        },
        component: () => import('@/views/myadmin/tenantadmin/index.vue'),
        children: [{
            path: '/tenant_details/:tenantId',
            name: 'tenant_details',
            icon: 'md-pricetag',
            meta: {
              title: '租户详情'
            },
            hidden: true,
            component: () => import('@/views/myadmin/tenantadmin/tenant-details.vue')
          },
          {
            path: '/new_company',
            name: 'new_company',
            icon: 'md-pricetag',
            meta: {
              title: '新建公司'
            },
            component: () => import('@/views/myadmin/tenantadmin/new-company.vue')
          }
        ]
      },
      {
        path: '/user_admin',
        name: 'user_admin',
        icon: 'md-people',
        meta: {
          title: '用户管理'
        },
        component: () => import('@/views/myadmin/useradmin/index.vue'),
        children: [{
            path: '/user_log/:userLoginId',
            name: 'user_log',
            icon: 'ios-list-box-outline',
            meta: {
              title: '用户日志'
            },
            hidden: true,
            component: () => import('@/views/myadmin/useradmin/user-log.vue')
          },
          {
            path: '/user_statistics',
            name: 'user_statistics',
            icon: 'md-home',
            hidden: true, // 不在侧边栏显示
            meta: {
              title: '用户统计'
            },
            component: () => import('@/views/userstatistics/statistics_index.vue')
          }
        ]
      },
      {
        path: '/role_admin',
        name: 'role_admin',
        icon: 'md-contact',
        meta: {
          title: '角色管理'
        },
        component: () => import('@/views/myadmin/role-admin.vue')
      },
      {
        path: '/menu_admin',
        name: 'menu_admin',
        icon: 'md-clipboard',
        meta: {
          title: '菜单管理'
        },
        component: () => import('@/views/myadmin/menu-admin.vue')
      },
    ]
  },
  // 业务审批管理
  {
    path: '/menu_business_approve_manage',
    name: 'menu_business_approve_manage',
    component: Layout,
    icon: 'md-globe',
    selfIcon: mng1021,
    selfIconb: mng1021b,
    meta: {
      title: '业务审批管理'
    },
    children: [{
        path: '/contract-approvel',
        name: 'contract-approvel',
        meta: {
          title: '合约审批'
        },
        component: () => import('@/views/myadmin/approvel/contract-approvel.vue')
      },
      {
        path: '/chain-approvel',
        name: 'chain-approvel',
        meta: {
          title: '链账户审批'
        },
        component: () => import('@/views/myadmin/approvel/chain-approvel.vue')
      },
      {
        path: '/workorder-approvel',
        name: 'workorder-approvel',
        meta: {
          title: '工单审批'
        },
        component: () => import('@/views/myadmin/approvel/workorder-approvel.vue')
      },
      {
        path: '/chain-resource',
        name: 'chain-resource',
        meta: {
          title: '链账户资源审批'
        },
        component: () => import('@/views/myadmin/approvel/chain-resource.vue')
      }

    ]
  },
  // 日志管理
  {
    path: '/menu_log_manage',
    name: 'menu_log_manage',
    component: Layout,
    icon: 'md-globe',
    selfIcon: mng1027,
    selfIconb: mng1027b,
    meta: {
      title: '日志管理'
    },
    children:[
      {
        path: '/survival_log',
        name: 'survival_log',
        icon: 'md-person',
        meta: {
          title: '用户行为日志'
        },
        component: () => import('@/views/storageevidence/survival_log.vue')
      },
      {
        path: '/system_log',
        name: 'system_log',
        icon: 'md-person',
        meta: {
          title: '应用行为日志'

        },
        component: () => import('@/views/storageevidence/system_log.vue')
      },
    ]
  },
  // 服务中心
  {
    path: '/menu_server_manage',
    name: 'menu_server_manage',
    component: Layout,
    icon: 'md-home',
    selfIcon: app01,
    selfIconb: app01b,
    meta: {
      title: '服务中心'
    },
    children: [
      {
      path: '/menu_server_manage',
      name: 'menu_server_manage',
      icon: 'md-apps',
      meta: {
        title: '服务中心'
      },
      component: () => import('@/views/applicationCenter/component-center.vue')
    }]
  },
  // 视图中心
  {
    path: '/menu_view_manage',
    name: 'menu_view_manage',
    component: Layout,
    icon: 'md-home',
    selfIcon: eos1010,
    selfIconb: eos1010b,
    meta: {
      title: '视图中心'
    },
    children: [
      {
      path: '/menu_view_manage',
      name: 'menu_view_manage',
      icon: 'md-apps',
      meta: {
        title: '视图中心'
      },
      component: () => import('@/views/applicationCenter/view-center.vue')
    }]
  },
  // 以下菜单不展示侧边栏
  // 个人中心
  {
    path: '/userinfo',
    name: 'user_info',
    component: Layout,
    icon: 'md-person',
    selfIcon: mng1024,
    selfIconb: mng1024b,
    hidden: true,
    meta: {
      title: '个人中心'
    },
    children: [{
      path: '/user_index',
      name: 'user_index',
      icon: 'md-apps',
      meta: {
        title: '个人中心'
      },
      component: () => import('@/views/userinfo/index.vue'),
      // component: () => import('@/views/userinfo/user-info.vue'),
      children: [{
        path: '/userinfo_index',
        name: 'user_info',
        icon: 'md-apps',
        meta: {
          title: '个人中心'
        },
        hidden: true,
        component: () => import('@/views/userinfo/user-info.vue')
      },
      {
        path: '/new_workorder',
        name: 'new_workorder',
        meta: {
          title: '新建工单'
        },
        hidden: true,
        component: () => import('@/views/userinfo/new-workorder.vue')
      },
      {
        path: '/workorder_detail/:orderId',
        name: 'workorder_detail',
        meta: {
          title: '工单详情'
        },
        hidden: true,
        component: () => import('@/views/userinfo/workorder-detail.vue')
      }
      ]
    }]
  },
  // 关于关于CMBaaS
  {
    path: '/about_index',
    name: 'about_index',
    icon: 'md-star',
    selfIcon: mng1033,
    selfIconb: mng1033b,
    meta: {
      title: '关于CMBaaS'
    },
    component: Layout,
    hidden: true, // 不在侧边栏显示
    children: [
      {
        path: '/about_index',
        name: 'about_index',
        icon: 'md-contacts',
        selfIcon: mng1034,
        selfIconb: mng1034b,
        meta: {
          title: '关于我们'
        },
        component: () => import('@/views/about/index.vue')
      }
    ]
  },
  {
    path: '*',
    component: () => import('@/views/error_404.vue')
  },
  // 预言机路由
  ...prophecyManagementRouters
]