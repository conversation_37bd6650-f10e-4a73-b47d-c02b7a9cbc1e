import * as CryptoJS from 'crypto-js';

/**
 * 加密工具类
 */
class Crypto {
  /**
   * 生成密钥字节数组, 原始密钥字符串不足128位, 补填0.
   * @param {string} key
   * @returns {string}
   */
   fillKey(key)  {
    const filledKey = Buffer.alloc(128 / 8);
    const keys = Buffer.from(key);
    if (keys.length < filledKey.length) {
      for (let i = 0; i < filledKey.length; i++) {
        if (keys[i]) {
          filledKey[i] = keys[i];
        }else {
          filledKey[i] = 0;
        }
      }
      return filledKey;
    }
    return keys;
  }


  /**
   * AES加密
   * @param {string} content
   * @param {string} key
   * @returns {string}
   */
  aesEncrypt(content, key){
    let encodeKey = CryptoJS.enc.Utf8.parse(this.fillKey(key));
    let srcs = CryptoJS.enc.Utf8.parse(content);
    let encrypted = CryptoJS.AES.encrypt(srcs, encodeKey, {mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7});
    return encrypted.toString();
  }


  /**
   * AES解密
   * @param {string} content
   * @param {string} key
   * @returns {string}
   */
  aesDecrypt(content, key) {
    content = decodeURI(atob(content))
    let encodeKey = CryptoJS.enc.Utf8.parse(this.fillKey(key));
    let decrypt = CryptoJS.AES.decrypt(content, encodeKey, {mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7});
    return CryptoJS.enc.Utf8.stringify(decrypt).toString();
  }
}


export const crypto = new Crypto();
