<!--
    用户操作手册
-->
<template>

  <!-- <div class="operation-manual">
    <div class="operation-title">用户操作手册</div>
    <div class="operation-subtitle">
      这里汇总了CMBaaS控制台的功能说明和操作指南
    </div>
    <div class="btns">
      <Button class="btn" type="primary" :loading="manualLoading" @click="downloadManual" icon="ios-cloud-download-outline">下载手册</Button>
      <Upload class="btn" action="" :before-upload="uploadNewManual" :format="typeListManual" :max-size="maxsize">
        <Button :loading="newManualLoading" v-show="roleId === '1'" icon="ios-cloud-upload-outline">上传新手册</Button>
      </Upload>
    </div>
    <div class="operation-check" v-show="roleId === '1'">
      <div class="operation-title">调查问卷</div>
      <Upload class="btn" action="" :before-upload="uploadQuestionnaire" :format="typeListQuestion" :max-size="maxsize">
        <Button class="btn" type="primary" :loading="questionnaireLoading" icon="ios-cloud-upload-outline">上传调查问卷图</Button>
      </Upload>
    </div> -->

  <!-- 修改 -->
  <div class="comp-wrap">
    <div class="title"><span class="bs"></span>帮助文档</div>
    <div class="empty" v-if="list.length === 0">
      <img class="imgs" :src="imagesurl" />
      <p class="empty-none">暂无数据</p>
    </div>
    <Row class="card-list" v-else>
      <Col v-for="(item, index) in list" :span="11" :offset="index % 2 ? 2 : 0" :key="item.id" class="card-wrap">
      <a :href="item.routeUrl" target="_blank" style="display: block">
        <Card>
          <div class="card">
            <div class="card-title">
              <span class="comp-tit">
                <Tooltip :content="item.projectName" max-width="550" placement="top-start">
                  <div class="textversionTitle">
                    {{ item.projectName }}
                  </div>

                </Tooltip>
              </span>
              <!-- <span class="comp-version">
                <Tooltip :content="item.version" max-width="650" placement="top-start">
                  <span class="textversion">当前版本：{{ item.version }}</span>
                </Tooltip>
              </span> -->
            </div>
            <div class="paragraph">
              <Tooltip :content="item.projectBrief" max-width="650">
                <!-- {{ item.brief }} -->
                <div class="text">
                  {{ item.projectBrief }}
                </div>
              </Tooltip>
            </div>
            <div class="down">
              <Button type="primary" :disabled='!item.fileHash?true:false' :style="!item.fileHash?'margin-right:5px;color:#cccccc;border:1px solid #cccccc': 'margin-right:5px;color:#ffffff;border:1px solid #ffffff'" @click="fileDown(item)" style="margin-left: 8px">下载</Button>
            </div>
            <!-- <div class="tips">
              <span class="tips-tit">支持框架：</span>
              <div class="tips-item" v-for="(tips,index) in item.tipsList" :key="index">{{tips}}</div>
            </div> -->
          </div>
        </Card>
      </a>
      </Col>
    </Row>
    <div class="page-wrap" v-if="list.length > 0">
      <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[6, 8, 10]" show-total show-elevator show-sizer @on-page-size-change="sizeChange" style="text-align: right" :transfer='true' />
    </div>
  </div>

  </div>
</template>

<script>
import { localRead } from '@/lib/util'
import { fileTest } from '@/lib/fileTest'
import { uploadAnnounceFile, downloadAnnounceFile } from '@/api/data'
import { getFileListHelp, getFileDownHelp } from '@/api/contract'
export default {
  data () {
    return {
      typeListManual: ['pdf'],
      typeListQuestion: ['jpg', 'jpeg', 'png'],
      maxsize: localStorage.getItem('MAX_FILE_SIZE') ? JSON.parse(localStorage.getItem('MAX_FILE_SIZE')) : 2048,
      manualLoading: false,
      newManualLoading: false,
      questionnaireLoading: false,
      roleId: localRead('roleId'),
      manualId: localRead('manualId'),
      manualName: localRead('manualName'),
      tablePageParam: {
        pagetotal: 0,
        pageIndex: 1,
        pageSize: 6
      },
      list: [],
      imagesurl: require('@/assets/img/null.png')
    }
  },
  methods: {
    handleFormatError (file) {
      return false
    },
    downloadManual () {
      this.manualLoading = true
      // console.log('manualId-file:', localRead('manualId') || '', this.manualId, this.manualId === 'null', this.manualName)
      if (this.manualId === 'null') {
        // console.log('manualId-file:')
        this.msgInfo('warning', '暂无手册可下载！', true)
        this.manualLoading = false
        return
      }
      downloadAnnounceFile(this.manualId).then(res => {
        var blob = new Blob([res])
        var downloadElement = document.createElement('a')
        var href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        // console.log('res.headers:', res, res.headers)
        // const filename = window.decodeURI(res.headers['Content-Disposition'].split('=')[1])
        // console.log('filename:', filename)
        downloadElement.download = this.manualName === 'null' ? 'CMBaaS用户操作手册' : this.manualName
        document.body.appendChild(downloadElement)
        downloadElement.click()
        document.body.removeChild(downloadElement)
        window.URL.revokeObjectURL(href)
        this.manualLoading = false
      }).catch(error => {
        this.manualLoading = false
        this.msgInfo('error', error, true)
      })
    },
    uploadNewManual (file) {
      let obj = fileTest(file, this.maxsize, this.typeListManual)
      // console.log('file:', file, this.maxsize, this.typeListManual, obj)
      if (!obj.flag) {
        this.msgInfo('warning', obj.msg, true)
        return
      }
      this.newManualLoading = true
      uploadAnnounceFile('OPERATION_MANUAL', file).then(res => {
        if (res.code === '00000') {
          this.newManualLoading = false
          this.msgInfo('success', res.message, true)
        } else {
          this.newManualLoading = false
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.newManualLoading = false
        this.msgInfo('error', error.message, true)
      })
    },
    uploadQuestionnaire (file) {
      let obj = fileTest(file, this.maxsize, this.typeListQuestion)
      // console.log('file:', file, this.maxsize, this.typeListQuestion, obj)
      if (!obj.flag) {
        this.msgInfo('warning', obj.msg, true)
        return
      }
      this.questionnaireLoading = true
      uploadAnnounceFile('SURVEY_QUESTION', file).then(res => {
        if (res.code === '00000') {
          this.questionnaireLoading = false
          this.msgInfo('success', res.message, true)
        } else {
          this.msgInfo('error', res.message, true)
          this.questionnaireLoading = false
        }
      }).catch(error => {
        this.questionnaireLoading = false
        this.msgInfo('error', error.message, true)
      })
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    getList () {
      let data = {
        noFile: 1,
        fileType: 1,
        pageParam: {
          pageSize: this.tablePageParam.pageSize,
          pageIndex: this.tablePageParam.pageIndex
        }
      }

      getFileListHelp(data)
        .then((res) => {
          if (res.code === '00000') {
            this.list = res.data.records
            this.tablePageParam = {
              pagetotal: res.data.total,
              pageIndex: res.data.current,
              pageSize: res.data.size
            }
          } else {
            this.msgInfo('error', res.message)
          }
        })
        .catch((error) => {
          this.msgInfo('error', error.message)
        })
    },

    fileDown (hash) {
      if (hash.fileHash) {
        getFileDownHelp(hash.fileHash).then(res => {
          var blob = new Blob([res])
          var downloadElement = document.createElement('a')
          var href = window.URL.createObjectURL(blob)
          downloadElement.href = href

          downloadElement.download = hash.fileName ? hash.fileName : ''
          document.body.appendChild(downloadElement)
          downloadElement.click()
          document.body.removeChild(downloadElement)
          window.URL.revokeObjectURL(href)
        }).catch(error => {
          this.msgInfo('error', error, true)
        })
      }
    },
    /**
 * 每页条数改变
 */
    sizeChange (size) {
      this.tablePageParam.pageSize = size
      this.getList()
    },
    /**
     * 页码改变
     */
    pageChange (pageIndex) {
      this.tablePageParam.pageIndex = pageIndex
      this.getList()
    },
  },
  mounted () {
    this.getList()
  }
}
</script>

<style lang="less" scoped>
.operation-manual {
  padding: 20px;
  .operation-title {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .operation-subtitle {
    font-size: 16px;
    font-weight: bold;
  }
  .btns {
    margin-top: 25px;
    .btn:nth-child(2) {
      margin-left: 25px;
      color: #777;
      display: inline-block;
    }
    .btn:nth-child(3) {
      margin-left: 25px;
      display: inline-block;
    }
  }
  .operation-check {
    margin-top: 30px;
    .operation-title {
      margin: 20px 0 20px 0;
    }
  }
}

//
.comp-wrap {
  padding: 0 40px;
  box-sizing: border-box;
  .title {
    margin: 15px 0;
    font-size: 16px;
    font-weight: bold;
    height: 25px;
    line-height: 25px;
    vertical-align: middle;
  }
  .bs {
    float: left;
    width: 6px;
    height: 16px;
    background: #19c3a0;
    opacity: 1;
    border-radius: 3px;
    margin: 4px 5px 0 0;
  }
  .empty {
    text-align: center;
    margin: 0 auto;
    vertical-align: middle;
    position: relative;
    font-size: 16px;
    .empty-none {
      font-size: 8px;
      color: #d4d3d3;
      margin-top: -5px;
      position: relative;
    }
  }
  .card-list {
    .card-wrap {
      background-color: #fff;
      margin-bottom: 20px;
      transition: all 0.15s linear;
      &:hover {
        box-shadow: 0 1px 6px rgba(0, 0, 0, 20%) !important;
      }
      .card {
        // height: 130px;
        color: #2c3e50;
        padding: 15px;
        box-sizing: border-box;
        cursor: pointer;
      }
    }
    .card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .comp-tit {
        width: 300px;
        font-size: 16px;
        font-weight: bold;
      }
      .comp-version {
        width: 120px;
        font-size: 10px;
        color: #bbb;
        padding: 4px 10px 0 10px;
        border: 1px solid #bbb;
        border-radius: 100px;
      }
    }
    .paragraph {
      margin-top: 10px;
      height: 50px;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;

      .text {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        word-wrap: break-word;
        word-break: break-all; // 注意这个文字多行很重要
        -webkit-box-orient: vertical;
      }
    }
    .tips {
      display: flex;
      margin-top: 15px;
      .tips-tit {
        margin-right: 5px;
        font-size: 12px;
        color: #999;
      }
      .tips-item {
        margin-right: 15px;
        padding: 2px 10px;
        border-radius: 100px;
        background-color: #def8f8;
        font-size: 12px;
        color: #92d5d9;
      }
    }
  }
}

/deep/.ivu-tooltip-contentr {
  max-height: 400px;
  overflow-y: auto;
}
/deep/.ivu-tooltip-inner {
  max-height: 300px;
  overflow-y: auto;
}
.textversion {
  display: inline-block;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}
.textversionTitle {
  width: 150px; /*要显示文字的宽度*/
  text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
  white-space: nowrap; /*让文字不换行*/
  overflow: hidden; /*超出要隐藏*/
}
.down {
  text-align: right;
}
</style>