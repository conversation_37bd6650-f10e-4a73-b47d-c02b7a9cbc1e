import axios from '../../index'
const BASEURL = '/cmbaas/portal/fabric/CommonAPI' // htttp://10.2.53.192:8765
// 获取版本号
export function getSupportFabricVersionList (query) {
  return axios.request({
    url: BASEURL + '?msgType=chain%23getSupportFabricVersionList&ServiceType=fabric',
    method: 'get',
    params: query
  })
}
// 获取组织节点默认名称 getChainPredefinedInfo
export function getChainPredefinedInfo (obj) {
  let data = {
    msgType: 'chain#getChainPredefinedInfo',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
// getK8SNodeList 查询集群下的主机列表
export function getK8SNodeList (query) {
  return axios.request({
    url: BASEURL + '?msgType=chain%23getK8SNodeList',
    method: 'get',
    params: query
  })
}
// 一键式部署区块链msgType=deployNewChain
export function deployNewChain (obj) {
  let data = {
    msgType: 'chain#deployNewChain',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
// 查询部署进度
export function getDeployChainProgress (query) {
  return axios.request({
    url: BASEURL + '?msgType=chain%23getDeployChainProgress',
    method: 'get',
    timeout: 2 * 60 * 1000,
    params: query

  })
}
// 检查链名是否已存在
export function checkChainDisplayName (obj) {
  let data = {
    msgType: 'chain#checkChainDisplayName',
    params: obj
  }
  return axios.request({
    url: BASEURL,
    method: 'post',
    data: data
  })
}
