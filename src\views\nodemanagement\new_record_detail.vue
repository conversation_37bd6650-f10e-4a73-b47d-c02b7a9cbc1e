<template>
  <div>
    <hr />
    <div class="new_center">
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80">
        <FormItem label="交易类型" prop="radio">
          <RadioGroup v-model="disabledGroup">
            <Radio label="文件" @click.native="click_file(0)"></Radio>
            <Radio label="内容" @click.native="click_center(1)"></Radio>
          </RadioGroup>
        </FormItem>
        <!--  文件-->
        <div v-if="upload_tab == '0'" class="new_file">
          <Upload :before-upload="handleUpload" action="Url" :max-size="2 * 1024">
            <Button icon="ios-cloud-upload-outline">文件最大2M</Button>
          </Upload>

          <div v-if="file !== null">Upload&nbsp;file: {{ file.name }}</div>
          <div class="new_btn">
            <Button type="primary" @click="upload_file('formValidate')" :loading="loadingStatus">{{ loadingStatus ? "上传中" : "提交" }}</Button>
            <Button type="primary" @click="upload">返回</Button>
          </div>
        </div>

        <!-- 内容 -->
        <div class="user_log" v-if="upload_tab == '1'">
          <FormItem label="" prop="textarea">
            <Input id="a" v-model="formValidate.textarea" type="textarea" :rows="6" placeholder="请输入内容" />
          </FormItem>
          <FormItem>
            <div class="new_btn">
              <Button type="primary" :loading="loadingStatus" @click.native="upload_value('formValidate')">提交</Button>
              <Button type="primary" @click.native="upload">返回</Button>
            </div>
          </FormItem>
        </div>
      </Form>
    </div>
  </div>
</template>

<script>
import { TransactionFile, TransactionContent } from '@/api/data'
export default {
  name: 'new_recor',
  data () {
    return {
      upload_tab: '0',
      file: '',
      loadingStatus: false,
      Url: '',
      disabledSingle: true,
      disabledGroup: '文件',
      count: '',
      userLoginId: '', // 用户名称
      detailid: '',
      // 存证名称 存证类型
      formValidate: {
        name: '',
        check: '',
        textarea: '' // 内容存证
      },

      ruleValidate: {
        name: [
          {
            required: true,
            message: '这个名称不能为空',
            trigger: 'blur'
          }
        ],
        textarea: [
          {
            required: true,
            message: '这个名称不能为空',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    //  上传文件
    click_file (e) {
      this.upload_tab = e
    },
    click_center (e) {
      this.upload_tab = e
    },
    // upload 返回
    upload () {
      this.$router.push({ path: '/node_management' })
    },
    handleUpload (file) {
      this.file = file
      if (file.size > 2097152) {
        this.$Message.warning('文件最大2M,请重新上传！')
        this.file = ''
      } else if (file.size === 0) {
        this.$Message.warning('文件内容不能为空,请重新上传！')
        this.file = ''
      }
      return false
    },

    // 上传文件
    upload_file (name) {
      const { content } = this.$route.params
      this.detailid = content.id
      if (this.file) {
        this.$refs[name].validate((valid) => {
          if (valid) {
            this.loadingStatus = true
            TransactionFile(this.file, this.userLoginId, this.detailid)
              .then((res) => {
                if (res.code === 'B0001') {
                  setTimeout(() => {
                    this.loadingStatus = false
                    this.$Message.warning(res.message)
                  }, 1500)
                } else if (res.code === '00000') {
                  setTimeout(() => {
                    this.loadingStatus = false
                    this.$Message.success(res.message)
                    this.$router.push({ path: '/node_management' })
                  }, 1500)
                } else {
                  setTimeout(() => {
                    this.loadingStatus = false
                    this.$Message.error(res.message)
                  }, 1500)
                }
              })
              .catch((error) => {
                setTimeout(() => {
                  this.loadingStatus = false
                  this.$Message.error(error.message)
                }, 1500)
              })
          } else {
            this.$Message.error('请完善提交信息!')
          }
        })
      } else {
        this.$Message.error('请完善提交信息!')
      }
    },

    // 上传内容
    upload_value (name) {
      const { content } = this.$route.params
      this.detailid = content.id

      this.$refs[name].validate((valid) => {
        if (valid) {
          this.loadingStatus = true
          TransactionContent(
            this.formValidate.name,
            this.formValidate.textarea,
            this.userLoginId,
            this.detailid
          )
            .then((res) => {
              if (res.code === 'B0001') {
                setTimeout(() => {
                  this.loadingStatus = false
                  this.$Message.warning(res.message)
                }, 1500)
              } else if (res.code === '00000') {
                setTimeout(() => {
                  this.loadingStatus = false
                  this.$Message.success(res.message)
                  this.$router.push({ path: '/node_management' })
                }, 1500)
              } else {
                setTimeout(() => {
                  this.loadingStatus = false
                  this.$Message.error(res.message)
                }, 1500)
              }
            })
            .catch((error) => {
              setTimeout(() => {
                this.loadingStatus = false
                this.$Message.error(error.message)
              }, 1500)
            })
        } else {
          this.$Message.error('请正确填写全部信息!')
        }
      })
    }
  },

  created () {
    this.userLoginId = localStorage.getItem('userLoginId')
  }
}
</script>

<style scoped>
.new_center {
  width: 25%;
  margin: 50px;
}
.ivu-radio-group {
  font-size: 14px;
  vertical-align: middle;
  display: flex;
  width: 319px;
  justify-content: space-around;
}
.ivu-btn-default {
  width: 384px;
  height: 180px;
}
.new_btn {
  display: flex;
  width: 220px;
  margin-top: 40px;
  justify-content: space-evenly;
}
#a {
  height: 140px;
}
.new_file {
  margin-left: 20px;
}
</style>
