<template>
  <div class="contract">
    <Collapse v-model="panelValue" simple name="mainpanel">
      <Panel name="1" style="background:#ECEFFC;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        合约信息
        <p slot="content" class="basetext">
          <span>合约名称：{{this.basisinfo.contractName}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>应用名称：{{this.basisinfo.contractReadableName}}</span>
        </p>
        <p slot="content" class="basetext">
          <span>应用简介：{{this.basisinfo.brief}}</span>
        </p>

      </Panel>
      <Panel name="2" style="background:#ECEFFC;display:block;">
        <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
        合约上架信息
        <div slot="content">
          <div class="shelvesInfo">
            <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80" @submit.native.prevent>
              <FormItem label="合约包名称" prop="name">
                <Input v-model="formValidate.name" placeholder="请输入合约包名称" style="width: 300px" />
              </FormItem>
              <FormItem label="链类型" prop="chaincity">
                <P>{{this.chainTypeName}}</P>
                <!-- <Select v-model="formValidate.chaincity" placeholder="请选择链类型" style="width:200px" @on-change="changechaincity">
          <Option v-for="item in selectList" :value="item.enumValue" :key="item.enumKey">{{item.enumValue}}</Option>
        </Select> -->
              </FormItem>
              <FormItem label="合约语言" prop="languagetype">
                <p>{{this.basisinfo.languageType==='JS'?'Java Script':this.basisinfo.languageType}}</p>
                <!-- <Select v-model="formValidate.languagetype" placeholder="请选择语言类型" style="width:150px">
          <Option v-for="item in languageList" :value="item.enumValue" :key="item.enumKey">{{item.enumValue}}</Option>
        </Select> -->
              </FormItem>
              <FormItem label="适用场景信息" prop="describe" class="descr">
                <Input v-model="formValidate.describe" placeholder="输入合约包适用场景信息" type="textarea" :maxlength="60" show-word-limit :autosize="{minRows: 3,maxRows: 5}" style="width: 350px;height:110px;" />
              </FormItem>
              <FormItem label="版本信息" prop="versioninfo" class="mandatory">
                <div>
                  <edit-table-mul border ref="selection" :columns="tableTitle" v-model="VersionData" @on-selection-change="getSelectAll"></edit-table-mul>
                </div>
              </FormItem>

              <div class="newFromSubmit">
                <Button @click="handleReset('formValidate')" style="margin-right: 10px">取消</Button>
                <Button type="primary" @click="handleSubmit('formValidate')" v-prevent-re-click :loading="loadingStatus">{{ loadingStatus ? "审批中" : "提交审批" }}</Button>
                <!-- <Button type="primary" @click="handleSubmit('formValidate')" v-prevent-re-click :loading="loadingStatus">{{ loadingStatus ? "上传中" : "提交" }}</Button> -->
              </div>

            </Form>
          </div>

          <Modal v-model="chaincode" title="查询合约链码" width='900px'>
            <p style="margin-bottom:20px">上传版本号：{{this.title}}</p>

            <div v-if="isSingleCpp=='1'">
              <Collapse simple accordion v-if="this.basisinfo.languageType === 'C++'">
                <Panel :name="transferKey1" :key="transferKey1">
                  {{transferKey1}}
                  <p slot="content">
                    <textarea class="textarea-style" v-html="CollContent.cppcentent.fileContent" readonly @scroll="handScroll($event, 'cpp')"></textarea>
                  </p>
                </Panel>
                <Panel :name="item" v-for="item in filesHpp" :key='item'>
                  {{item}}
                  <p slot="content">
                    <textarea class="textarea-style" v-html="CollContent.hppcentent.fileContent" readonly @scroll="handScroll($event, 'hpp')"></textarea>
                  </p>
                </Panel>

              </Collapse>
              <Collapse simple accordion v-else>
                <Panel :name="transferKey1" :key="transferKey1">
                  {{transferKey1}}
                  <p slot="content">
                    <textarea class="textarea-style" v-html="CollContent.jscentent.fileContent" readonly @scroll="handScroll($event, 'js')"></textarea>
                  </p>
                </Panel>
                <Panel :name="fileName" v-if="fileName">
                  {{fileName}}
                  <p slot="content">
                    <textarea class="textarea-style" v-html="CollContent.abicentent.fileContent" readonly @scroll="handScroll($event, 'abi')"></textarea>
                  </p>
                </Panel>

              </Collapse>
            </div>
            <div v-else>
              <Layout>
                <Sider hide-trigger :style="{background: '#fff'}">
                  <Menu theme="light" width="auto" :open-names="['1']">
                    <Submenu :name="key" v-for="(item,key) in cppsTitle" :key="key">
                      <template slot="title">
                        <Icon type="ios-folder"></Icon>
                        {{key}}
                      </template>
                      <MenuItem :name="items.fileName" v-for="(items,i) in item" :key="i" @click.native="clickCpps(items.content)">{{items.fileName}}</MenuItem>
                    </Submenu>
                  </Menu>
                </Sider>
                <Content :style="{padding: ' 0 24px', minHeight: '280px', background: '#fff'}">
                  <p>
                    <textarea class="textarea-style" v-html="cppContent" readonly @scroll="handScrolljs($event, 'abi')"></textarea>
                  </p>
                </Content>
              </Layout>
            </div>
          </Modal>
        </div>
      </Panel>
    </Collapse>
  </div>
</template>
<script>
import { isContractName } from '../../../lib/check'
import { getTempateEos, getTempateLanguage, getShelvesInfo, getShelves, getChaincode } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
export default {
  components: {
    EditTableMul
  },
  data () {
    const validateContractName = (rule, value, callback) => {
      let reg = /^[_a-zA-Z]/

      if (!reg.test(value.slice(0, 1))) {
        callback(new Error('只能以英文及下划线开头'))
      }
      if (!isContractName(value)) {
        callback(new Error('支持英文和数字，特殊符号只能有英文句号.和英文_'))
      } else {
        callback()
      }
    }
    return {
      loadingStatus: false,
      selectList: [], // 链类型数组
      languageList: [], // 语言类型数组
      selecteos: 'CHAIN_TYPE', // 链类型传参
      selectlanguage: 'LANGUAGE_TYPE', // 语言类型传参
      panelValue: ['1', '2'], // 页面折叠面板
      chaincode: false, // 查询合约链码弹框
      shelvesid: this.$route.params.listId, // 路由传过来的id
      AllList: [], // 选中的列表
      basisinfo: {}, // 渲染基础信息
      // form表单
      formValidate: {
        name: '', // 合约包名称
        chaincity: '', // 链类型
        languagetype: '', // 语言类型
        describe: '' // 场景信息
      },
      // from表单校验
      ruleValidate: {

        name: [
          { required: true, min: 5, message: '不能少于5位', trigger: 'blur' },
          { max: 20, message: '不能多于20位', trigger: 'blur' },
          { trigger: 'blur', validator: validateContractName }

        ]
        //
        // chaincity: [
        //   {
        //     required: true,
        //     message: '不能为空',
        //     trigger: 'blur'
        //   }
        // ],
        // languagetype: [
        //   {
        //     required: true,
        //     message: '不能为空',
        //     trigger: 'blur'
        //   }
        // ]

      },
      VersionTitle: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '版本号',
          key: 'uploadVersion'

        },
        {
          title: 'cpp文件名',
          key: 'cppFileName',
          tooltip: true
        },
        {
          title: 'hpp文件名',
          key: 'hppFileNames',
          tooltip: true,
          render: (h, params) => {
            return h('div', params.row.hppFileNames.join(','))
          }
        },
        {
          title: '上架状态',
          key: 'contractMarketStatus',
          tooltip: true
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      VersionData: [],
      fileTpye: [],
      CollContent: { cppcentent: {}, hppcentent: {}, jscentent: {}, abicentent: {} },
      codeData: {},
      filesHpp: [],
      transferKey1: '',
      title: '', // 查看文件源码标题
      // 以下是添加关于js
      columnsJs: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '版本号',
          key: 'uploadVersion',
          tooltip: true
        },
        {
          title: 'JavaScript文件名',
          key: 'jsFileName'
        },
        {
          title: 'abi文件名',
          key: 'abiFileName'
        },
        {
          title: '上架状态',
          key: 'contractMarketStatus',
          tooltip: true
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '8px',
                    color: '#3D73EF',
                    border: '1px solid #3D73EF'
                  },
                  on: {
                    click: () => {
                      this.fileModal(params)
                    }
                  }
                },
                '文件源码'
              )

            ])
          }
        }
      ],
      tableTitle: [],
      fileName: '',
      chainTypeName: '',
      isSingleCpp: '',//判断是否是多cpp
      cppContent: '请选择要看的源码文件',
      cppsTitle: '',
    }
  },
  methods: {
    changechaincity (value) {
      this.formValidate.languagetype = ''
      // this.cleardata()
      if (value === 'EOS' || value === 'CMEOS') {
        this.languageList = [
          { enumKey: 'C++', enumValue: 'C++' }
        ]
      } else if (value === 'ChainMaker') {
        // GO、C++、rust、tinygo、solidity
        this.languageList = [
          { enumKey: 'GO', enumValue: 'GO' },
          { enumKey: 'C++', enumValue: 'C++' },
          { enumKey: 'RUST', enumValue: 'RUST' },
          { enumKey: 'TINYGO', enumValue: 'TINYGO' },
          { enumKey: 'SOLIDITY', enumValue: 'SOLIDITY' }
        ]
      } else {
        this.languageList = []
      }
    },
    empty () {
      this.formValidate.name = ''
      this.formValidate.chaincity = ''
      this.formValidate.languagetype = ''
      this.formValidate.describe = ''
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, top: 300 }) },

    // 点击文件源码
    fileModal (params) {

      this.chaincode = true
      this.title = params.row.uploadVersion
      this.isSingleCpp = params.row.isSingleCpp == '0' ? '0' : '1'
      this.cppsTitle = params.row.fileContent
      if (this.basisinfo.languageType === 'C++') {
        this.transferKey1 = params.row.cppFileName
        this.filesHpp = params.row.hppFileNames
        this.fileName = ''
        this.codeData = {
          contractId: params.row.contractId,
          uploadVersion: params.row.uploadVersion
        }
        this.getCode(params.row.cppFileName, 'cpp')
        if (params.row.hppFileNames && params.row.hppFileNames.length > 0) {
          params.row.hppFileNames.forEach(val => this.getNewCode(val, 'hpp'))
        }
      } else {
        this.transferKey1 = params.row.jsFileName
        this.fileName = params.row.abiFileName
        this.codeData = {
          contractId: params.row.contractId,
          uploadVersion: params.row.uploadVersion
        }
        this.getCode(params.row.jsFileName, 'js')
        this.getNewCode(params.row.abiFileName, 'abi')
      }
    },
    getCode (fileName) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          if (this.basisinfo.languageType === 'C++') {
            this.CollContent.cppcentent = res.data
          } else {
            this.CollContent.jscentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    getNewCode (fileName, val) {
      let data = {
        contractId: this.codeData.contractId,
        uploadVersion: this.codeData.uploadVersion,
        fileName: fileName,
        pageParam: {
          'pagetotal': 0,
          'pageSize': 30,
          'pageIndex': 1
        }
      }
      getChaincode(data).then(res => {
        if (res.code === '00000') {
          if (this.basisinfo.languageType === 'C++') {
            this.CollContent.hppcentent = res.data
          } else {
            this.CollContent.abicentent = res.data
          }
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 点击折叠面板事件
    // colldata (key) {
    //   if (key[0]) {
    //     this.codeData.fileName = key[0]
    //     getChaincode(this.codeData).then(res => {
    //       if (res.code === '00000') {
    //         this.CollContent = res.data
    //       }
    //     }).catch((error) => {
    //       this.msgInfo('error', error.message, true)
    //     })
    //   }
    // },
    // 滚动
    handScroll (e) {
      if (e.srcElement.scrollTop + e.srcElement.offsetHeight >= e.srcElement.scrollHeight) {
        clearTimeout(this.timerStamp)
        let that = this
        this.timerStamp = setTimeout(() => {
          let height = e.srcElement.scrollTop + e.srcElement.offsetHeight - e.srcElement.scrollHeight // if (height >= 1 && height < 2) {
          if (height < 3 && height > 0) {
            that.msgInfo('info', '到底了！', true)
          }
        }, 500)
      } else if (e.srcElement.scrollTop === 0) {
        this.msgInfo('info', '已到首页！', true)
      }
    },
    // 版本信息选中事件
    getSelectAll (list) {
      this.AllList = list.map(item => item.uploadVersion)
      // this.AllList.push(list)
    },
    // 提交审批事件
    handleSubmit () {
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          if (this.AllList.length > 0) {
            this.loadingStatus = true
            let fromList = {
              contractName: this.basisinfo.contractName,
              contractId: this.$route.params.listId, // 合约id
              contractBagName: this.formValidate.name, // 合约包名称
              chainType: this.chainTypeName, // 链类型
              contractLanguage: this.basisinfo.languageType, // 合约语言
              applicaSecene: this.formValidate.describe, // 适用场景
              selectVersion: this.AllList // 版本信息
            }
            getShelves(fromList).then(res => {
              if (res.code === 'A1503') {
                this.msgInfo('warning', '已存在此合约包名称，请修改重试', true)
                this.loadingStatus = false
              } else if (res.code === 'A1505') {
                this.msgInfo('warning', '该合约下存在待审批的上架记录，不允许再次上架', true)
                this.loadingStatus = false
              } else {
                this.loadingStatus = false
                this.$router.push({
                  name: 'contract_table'
                })
                // this.$Message.success('已提交合约上架信息，需等待平台管理员审批!')
                this.$Message.success(this.$route.params.management === true ? '已提交合约上架信息' : '已提交合约上架信息，需等待平台管理员审批!')
                this.empty()
              }
            }).catch((error) => {
              this.msgInfo('error', error.message, true)
              this.loadingStatus = false
            })
          } else {
            this.msgInfo('warning', '请勾选版本信息！', true)
            this.loadingStatus = false
          }
        } else {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
          this.loadingStatus = false
        }
      })
    },
    // 取消事件
    handleReset (name) {
      this.$refs[name].resetFields()
      this.$router.push({
        name: 'contract_table'
      })
      this.empty()
    },
    clickCpps (value) {
      this.cppContent = value

    }

  },
  // mounted () {
  //   if (!this.$route.params.listId) {
  //     this.$router.push({
  //       name: 'contract_table'
  //     })
  //   } else {
  //     this.basisinfo = this.$route.params.content
  //     this.tableTitle = this.basisinfo.languageType === 'C++' ? this.VersionTitle : this.columnsJs
  //   }
  // },
  activated () {
    if (this.$route.params.content) {
      this.basisinfo = this.$route.params.content
      this.tableTitle = this.basisinfo.languageType === 'C++' ? this.VersionTitle : this.columnsJs
      if (this.$route.params.listId) {
        getShelvesInfo(this.$route.params.listId).then(res => {
          if (res.code === '00000') {
            this.chainTypeName = res.data[0].chainType
            let statusdata = {
              1: '已上架',
              2: '未上架'
            }
            let versionData = res.data.map(item => {
              return {
                ...item,
                hppFileNames: item.hppFileNames ? item.hppFileNames : [],
                contractMarketStatus: statusdata[item.contractMarketStatus]
              }
            })
            this.VersionData = versionData
          }
        }).catch((error) => {
          this.msgInfo('error', error.message, true)
        })
      } else {
        this.$router.push({
          name: 'contract_table'
        })
      }

      getTempateEos(this.selecteos).then(res => {
        this.selectList = res.data
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })
    } else {
      this.$router.go(-1)
    }


  }

}
</script>

<style lang="less" scoped>
/deep/.ivu-menu-submenu-title {
  background: #fff !important;
}
/deep/.ivu-menu,
.ivu-menu-dark,
.ivu-menu-vertical,
.ivu-menu-opened,
.ivu-menu-submenu-title {
  background: #fff !important;
}
.ivu-menu-vertical.ivu-menu-light:after {
  background: #fff;
}
.descr {
  /deep/textarea {
    height: 125px !important;
  }
}
.contract {
  margin: -16px;
  button.btn {
    position: absolute;
    right: 10px;
  }
  .basetext {
    padding-top: 20px;
    span {
      text-align: left;
      margin: 0 26px;
      line-height: 20px;
      word-break: break-all;
    }
  }
}
// from表单
.shelvesInfo {
  padding: 2%;
  // border: 1px solid red;
  /deep/.ivu-form-item-label {
    width: 110px !important;
  }
  /deep/.ivu-form-item-content {
    margin-left: 110px !important;
  }

  .mandatory {
    /deep/.ivu-form-item-label::before {
      content: "*";
      display: inline-block;
      margin-right: 4px;
      line-height: 1;
      font-family: SimSun;
      font-size: 14px;
      color: #ed4014;
    }
  }
}
.newFromSubmit {
  float: right;
  margin-right: 3%;
}
// /deep/.ivu-modal>.ivu-modal-content>.ivu-modal-body{max-height: 60vh;overflow: auto;}
// /deep/.ivu-upload-drag{background-color: #f8f8f9;}
// /deep/.ivu-btn-text:hover {
//   background-color: rgba(61,115,239,.8);
//   color: #fff!important;
// }
// /deep/.ivu-btn-text:active{
//   background-color: #3D73EF;
// }
/deep/.ivu-card {
  background: #f2f6fd;
}
/deep/.ivu-card.ivu-card-shadow,
.ivu-card.ivu-card-shadow:hover {
  box-shadow: none;
}
/deep/.ivu-icon-ios-arrow-forward {
  float: right;
  padding: 12px;
}
/deep/.ivu-collapse[name="mainpanel"] {
  border: none;
}
/deep/.ivu-collapse[name="mainpanel"] > .ivu-collapse-item {
  border: none;
  border-radius: 5px 5px;
  margin-bottom: 10px;
}
/deep/.ivu-scroll-container {
  height: auto;
  overflow-y: auto;
}

//
// 滚动条
.textarea-style {
  width: 100%;
  height: 350px;
  border-color: #ffffff;
  color: #515a6e;
  background-color: #f8f8f9;
  resize: none;
}
textarea {
  overflow-y: scroll;
  padding: 10px;
  cursor: default;
  outline: none;
}
::-webkit-scrollbar {
  width: 10px; /*高宽分别对应横竖滚动条的尺寸*/
  min-height: 1px;
}
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgb(135, 158, 235);
}
</style>
