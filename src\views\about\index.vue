<template>
  <div class="about_index">
    <keep-alive>
    <AboutHome v-if="currentTab==='about_index'" />
  </keep-alive>
  <router-view v-if="currentTab!=='about_index'"/>
  </div>
</template>

<script>
import AboutHome from './about-home.vue'
export default {
  name: 'about_index',
  components: {
    AboutHome
  },
  data () {
    return {
      // excludeArr: ['tem_modify']
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
