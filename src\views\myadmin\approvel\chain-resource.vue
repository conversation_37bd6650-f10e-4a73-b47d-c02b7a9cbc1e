<template>
  <!-- 链账户资源审批 -->
  <div class="chainapprovel">
    <Tabs :value="name" @on-click="clickTab">
      <TabPane label="待审批" name="name1">
        <p style="margin:10px 10px 15px 0px;">
          <Input class='bt1 width-input' placeholder="请输入链账户名称" style="vertical-align:baseline;" v-model="chainAccountName" />
          <Select class='bt1 width-input' v-model="tenantIdList" placeholder="请选择租户" multiple :max-tag-count="2">
            <Option v-for="item in userListData" :value="item.tenantId" :key="item.tenantId">{{ item.tenantName }}</Option>
          </Select>
          <Button class='bt1' icon="ios-search" type="primary" @click="searchList">查询</Button>
          <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting">重置</Button>
        </p>
        <edit-table-mul :columns="columns" v-model="tableData"></edit-table-mul>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px" />
      </TabPane>
      <TabPane label="已审批" name="name2">
        <p style="margin:10px 10px 15px 0px;">
          <Input class='bt1 width-input' placeholder="请输入链账户名称" style="vertical-align:baseline;" v-model="chainAccountName2" />
          <Select class='bt1 width-input' v-model="tenantIdList2" placeholder="请选择租户" multiple :max-tag-count="2">
            <Option v-for="item in userListData" :value="item.tenantId" :key="item.tenantId">{{ item.tenantName }}</Option>
          </Select>
          <Button class='bt1' icon="ios-search" type="primary" @click="searchList2">查询</Button>
          <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting2">重置</Button>
        </p>
        <Table :columns="columns2" :data="tableData2">
          <template slot-scope="{ row, index }" slot="action">
            <Button type="text" size="small" style="marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF" @click="detailbtn(row)">详情</Button>
          </template>
        </Table>
        <Page :total="tablePageParam2.pagetotal" :current.sync="tablePageParam2.pageIndex" @on-change="pageChange2" :page-size="tablePageParam2.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange2" style="text-align:right;line-height:40px" />
      </TabPane>
    </Tabs>
    <!-- 待审批 -->
    <Modal v-model="modalAppro" :title="appModal" width="750px" :draggable=true sticky :mask-closable="false">
      <Card dis-hover style="margin-bottom:2%">
        <p class="title bs"> 链账户资源信息 </p>
        <Table stripe :columns="columnsResource" :data="dataResource"></Table>
      </Card>
      <Card dis-hover>
        <p class="title bs"> 链账户信息 </p>
        <span style="line-height:30px">链名称：{{ arrDetails.chainName }}</span><br>
        <span style="line-height:30px">租户：{{ arrDetails.tenantName }}</span><br>
        <span style="line-height:30px">链账户名称：{{ arrDetails.chainAccountName }}</span><br>
        <span style="line-height:30px">链账户类型：普通链账户</span><br>
        <span style="word-break:break-all;white-space: pre-wrap;">合约描述：{{ arrDetails.description }}</span><br>
        <span style="line-height:30px">提交时间：{{ arrDetails.createTime }}</span><br>
        <span style="line-height:30px">RAM资源申请量：{{ arrDetails.applySourceMsg }}</span><br>
        <span style="line-height:30px">申请理由：{{ arrDetails.applyReason }}</span><br>

      </Card>
      <div slot="footer">
        <Card shadow :bordered="false">
          <p class="title" style="text-align:left;">审批意见</p>
          <i-Form :model="formItem" :rules="formItemRule" :label-width="80" ref="formItem" style="text-align:left;">
            <FormItem style="margin:1px;padding:1px;" label="是否同意" prop="approStatus">
              <RadioGroup v-model="formItem.approStatus">
                <Radio label="APPROVED">同意</Radio>
                <Radio label="REJECT">不同意</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem style="padding:20px 0 10px 0;" label="审批说明" prop="auditRemark" v-show="formItem.approStatus==='REJECT'">
              <Input v-model="formItem.auditRemark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="说明" :maxlength="30" show-word-limit></Input>
            </FormItem>
            <FormItem style="padding:20px 0 10px 0;" label="审批说明" v-show="formItem.approStatus!=='REJECT'">
              <Input v-model="formItem.auditRemark" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder="说明（选填）" :maxlength="30" show-word-limit></Input>
            </FormItem>
          </i-Form>
          <Button type="primary" @click="ok('formItem')" :loading="loadingStatus">{{ loadingStatus ? "审批中" : "提交" }}</Button>
          <Button type="default" @click="cancelApp('formItem')">取消</Button>
        </Card>
      </div>
    </Modal>
    <!-- 已审批 -->
    <Modal v-model="modalDetail" :title="appModal" width="750px" :draggable=true :mask-closable="false" sticky>
      <Card dis-hover style="margin-bottom:2%">
        <p class="title bs"> 链账户资源信息 </p>
        <Table stripe :columns="columnsResource" :data="dataResource"></Table>
      </Card>
      <Card dis-hover>
        <p class="title bs"> 链账户信息 </p>
        <span style="line-height:30px">链名称：{{ arrDetails.chainName }}</span><br>
        <span style="line-height:30px">租户：{{ arrDetails.tenantName }}</span><br>
        <span style="line-height:30px">链账户名称：{{ arrDetails.chainAccountName }}</span><br>
        <span style="line-height:30px">链账户类型：普通链账户</span><br>
        <span style="word-break:break-all;white-space: pre-wrap;">合约描述：{{ arrDetails.description }}</span><br>
        <span style="line-height:30px">提交时间：{{ arrDetails.createTime }}</span><br>
        <span style="line-height:30px">审批时间：{{ arrDetails.auditTime }}</span><br>
        <span style="line-height:30px">RAM资源申请量：{{ arrDetails.applySourceMsg }}</span><br>
        <span style="line-height:30px">申请理由：{{ arrDetails.applyReason }}</span><br>
      </Card>
      <div slot="footer" class="bg1" :bordered="false" v-show="deployStatus !== '审核拒绝'">
        <p class="title" style="text-align:left;">审批结果</p>
        <div class="divS">
          <p style="text-align:left; margin-left:5px; margin-top:10px;">结果:<span class="resultS">{{ deployStatus}}</span></p>
          <p style="text-align:left; margin-left:5px; margin-top:5px;">说明:<span>{{ arrDetails.auditRemark }}</span></p>
        </div>
        <Button type="default" @click="cancelDet('formItem')">返回</Button>
      </div>
      <div slot="footer" class="bg2" :bordered="false" v-show="deployStatus === '审核拒绝'">
        <p class="title" style="text-align:left;">审批结果</p>
        <div class="divS">
          <p style="text-align:left; margin-left:5px; margin-top:10px;">结果:<span class="resultF">{{ deployStatus }}</span></p>
          <p style="text-align:left; margin-left:5px; margin-top:5px;">说明:<span>{{ arrDetails.auditRemark }}</span></p>
        </div>
        <Button type="default" @click="cancelDet('formItem')">返回</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { getUserData } from '@/api/data'
import { getAccountResource, getResourceRecord, getResourceDetail } from '@/api/contract'
import EditTableMul from '_c/edit-table-mul'
import { localRead } from '@/lib/util'
let unitConversion = {
  Byte (value) { return value / 1 },
  KB (value) { return value / 1024 },
  MB (value) { return value / 1024 / 1024 },
  GB (value) { return value / 1024 / 1024 / 1024 }
}
export default {
  name: 'chain-approvel',
  components: {
    EditTableMul
  },
  data () {
    return {
      loadingStatus: false,
      name: this.$route.params.tabs || 'name1',
      ops: {},
      // nullData: false,
      modalAppro: false,
      modalDetail: false,
      chainAccountName: '',
      chainAccountName2: '',
      userIdStr: '',
      userIdStr2: '',
      formItem: {
        auditRemark: '',
        approStatus: ''
      },
      formItemRule: {
        approStatus: [{ required: true, message: '请选择是否同意！', trigger: 'change' }],
        auditRemark: [{ required: true, message: '请填写审批说明！', trigger: 'blur' }]
      },
      appModal: '链账户资源详情',
      arrDetails: {},
      statusList: ['UNAPPROVED'],
      statusStr: '',
      tenantIdList: [],
      userListData: [],
      statusList2: [],
      statusStr2: '',
      tenantIdList2: [],
      bizId: 0,
      chainId: '',
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        { key: 'platformAccountName', title: '平台用户', tooltip: true },
        { key: 'chainAccountName', title: '链账户名称', tooltip: true },
        { key: 'tenantName', title: '租户', tooltip: true },
        // { key: 'chainName', title: '链名称', tooltip: true },
        {
          key: 'recordStatus',
          title: '状态',
          minWidth: 120,
          tooltip: true,
          render: (h, params) => {
            const row = params.row
            const color = row.recordStatus === '待审核' ? 'primary' : '#515a6e'
            // const text = row.status === 1 ? 'Working' : row.status === 2 ? 'Success' : 'Fail';
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, row.recordStatus)
          }
        },
        { key: 'createTime', title: '提交时间', tooltip: true },
        {
          key: 'action',
          title: '操作',
          // fixed: 'right',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small', disabled: this.hasEditPermission },
                style: this.buttonStyle,
                on: {
                  click: () => {
                    this.ApproDetails(params.index)
                  }
                }
              }, '审批')

            ])
          }
        }
      ],

      tableData: [],
      tablePageParam2: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      columns2: [
        { key: 'platformAccountName', title: '平台账户', tooltip: true },
        { key: 'chainAccountName', title: '链账户名称', tooltip: true },
        { key: 'tenantName', title: '租户', tooltip: true },
        { key: 'chainName', title: '链名称', tooltip: true },
        {
          key: 'recordStatus',
          title: '状态',
          tooltip: true,
          minWidth: 120,
          render: (h, params) => {
            const row = params.row
            const color = row.recordStatus === '审核通过' ? 'success' : row.recordStatus === '审核拒绝' ? 'error' : 'error'
            // const text = row.status === 1 ? 'Working' : row.status === 2 ? 'Success' : 'Fail';

            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, row.recordStatus)
          }
        },
        // { key: 'auditName', title: '审批人', tooltip: true },
        { key: 'auditTime', title: '审批时间', tooltip: true },
        {
          slot: 'action',
          title: '操作',
          // fixed: 'right',
          align: 'left',
          minWidth: 120
        }
      ],
      tableData2: [],
      deployStatus: '',
      whyDetail: {},
      // 链账户资源信息列表
      columnsResource: [
        {
          title: '单位',
          key: '',
          render: (h, { row }) => {
            // console.log('11111111111111', this)
            return <div>
              {
                <div>
                  <Select style="width:100px" value={row.changname} transfer
                    on={
                      {
                        'on-change': (info) => {
                          // this.change(info, row)
                          console.log('change', row)
                          this.dataResource = [{ ramQuota: unitConversion[info](this.dataResource1[0].ramQuota), ramUsage: unitConversion[info](this.dataResource1[0].ramUsage), odd: unitConversion[info](this.dataResource1[0].odd), proportion: row.proportion, changname: info }]
                        }
                      }
                    }
                  >
                    {
                      this.unitList.map((item) => {
                        return <Option value={item.value} >{item.lable}</Option>
                      })
                    }
                  </Select>
                </div>
              }

            </div>
          }

        },
        { title: 'RAM总量', key: 'ramQuota', tooltip: true },
        { title: 'RAM已用量', key: 'ramUsage', tooltip: true },
        { title: 'RAM剩余量', key: 'odd', tooltip: true },
        { title: 'RAM使用占比', key: 'proportion', tooltip: true }
      ],
      dataResource: [],
      dataResource1: [],
      unitList: [{ value: 'MB', lable: 'MB' }, { value: 'KB', lable: 'KB' }, { value: 'GB', lable: 'GB' }, { value: 'Byte', lable: 'Byte' }],
      userPermission: JSON.parse(localRead('userPermission')),
    }
  },
  computed: {


    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }

    },
  },
  methods: {
    clickTab (name) {
      if (name === 'name1') {
        this.resetting()
      }
    },
    init () {
      this.$nextTick(() => {
        this.$refs['formItem'].resetFields()
      })
      this.formItem = {
        auditRemark: '',
        approStatus: ''
      }
      this.statusList2 = []
      this.tenantIdList2 = []
      this.chainAccountName2 = ''
      this.tenantIdList = []
      this.chainAccountName = ''
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },
    pageChange2 (index) {
      this.tablePageParam2.pageIndex = index
      this.getTableData2()
    },
    pageSizeChange2 (index) {
      this.tablePageParam2.pageSize = index
      this.getTableData2()
    },
    // 详情
    detailbtn (row) {
      // console.log(row)
      this.deployStatus = row.recordStatus
      this.bizId = row.bizId
      this.modalDetail = true
      getResourceDetail(row.id).then(res => {
        if (res.code === '00000') {
          this.modalDetail = true
          this.arrDetails = res.data
          this.dataResource1 = res.data.ramQuota ? [{ ramQuota: this.arrDetails.ramQuota, ramUsage: this.arrDetails.ramUsage, odd: this.arrDetails.odd, proportion: this.arrDetails.proportion, changname: 'MB' }] : []
          this.dataResource = res.data.ramQuota ? [{ ramQuota: unitConversion['MB'](this.arrDetails.ramQuota), ramUsage: unitConversion['MB'](this.arrDetails.ramUsage), odd: unitConversion['MB'](this.arrDetails.odd), proportion: this.arrDetails.proportion, changname: 'MB' }] : []
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else this.msgInfo('info', res.message)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 待审批列表
    getTableData () {
      // 查询待审核状态的数据
      this.statusStr = 'UNAPPROVED'

      this.userIdStr = ''
      for (var j = 0; j < this.tenantIdList.length; j++) {
        if (j < this.tenantIdList.length - 1) {
          this.userIdStr += this.tenantIdList[j] + ','
        } else {
          this.userIdStr += this.tenantIdList[j]
        }
      }
      getAccountResource(this.chainAccountName, this.userIdStr, this.statusStr, this.tablePageParam).then(res => {
        if (res.code === '00000') {
          // console.log('getTableData===>', res)
          let recostate = {
            'UNAPPROVED': '待审核'
          }
          let lists = res.data.records.map(item => {
            return {
              ...item,
              recordStatus: recostate[item.recordStatus]
            }
          })
          this.tableData = lists
          // this.tableData = res.data.records
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        // console.log('getTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    // 已审批列表
    getTableData2 () {
      // status数组拼装成“，”连接的字符串statusStr,已审核的所有数据
      this.statusStr2 = ''
      if (this.statusList2.length === 0) {
        this.statusStr2 = 'APPROVED,REJECT'
      } else {
        for (var i = 0; i < this.statusList2.length; i++) {
          if (i < this.statusList2.length - 1) {
            this.statusStr2 += this.statusList2[i] + ','
          } else {
            this.statusStr2 += this.statusList2[i]
          }
        }
      }

      this.userIdStr2 = ''
      for (var j = 0; j < this.tenantIdList2.length; j++) {
        if (j < this.tenantIdList2.length - 1) {
          this.userIdStr2 += this.tenantIdList2[j] + ','
        } else {
          this.userIdStr2 += this.tenantIdList2[j]
        }
      }

      getAccountResource(this.chainAccountName2, this.userIdStr2, this.statusStr2, this.tablePageParam2).then(res => {
        // console.log('getTableData===>', res)
        if (res.code === '00000') {
          let recostate = {
            // 'UNAPPROVED': '待审核',
            'APPROVED': '审核通过',
            'REJECT': '审核拒绝'
          }
          let lists = res.data.records.map(item => {
            return {
              ...item,
              recordStatus: recostate[item.recordStatus]
            }
          })
          this.tableData2 = lists
          this.tablePageParam2 = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        // console.log('getTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    // cratetimer (item) {
    //   // let TIME_COUNT = time
    //   // if (!this.timer) {
    //   // thicount = item.retrySurplusTime
    //   // item.show = false
    //   let timer = setInterval(() => {
    //     if (item.retrySurplusTime > 0) {
    //       item.retrySurplusTime--
    //       item.show = true
    //     } else {
    //       item.show = false
    //       // this.getTableData2()
    //       clearInterval(timer)
    //       // this.timer = null
    //     }
    //   }, 1000)
    //   // }
    // },
    // 查询按钮
    searchList () { this.getTableData() },
    searchList2 () { this.getTableData2() },

    // 查询租户下拉框
    searchUserList () {
      getUserData(0).then(res => {
        this.userListData = res.data
      }).catch(error => {
        // console.log('getUrserData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    // 审批弹窗
    ApproDetails (index) {
      this.modalAppro = true
      // console.log('进入弹窗函数')
      // 状态和审批描述置空(X掉弹窗后，置空上一次的操作。)
      this.formItem.approStatus = ''
      this.formItem.auditRemark = ''

      this.chainId = this.tableData[index].chainId // 链id
      this.bizId = this.tableData[index].id
      getResourceDetail(this.tableData[index].id).then(res => {
        if (res.code === '00000') {
          this.modalAppro = true
          this.arrDetails = res.data
          this.dataResource1 = res.data.ramQuota ? [{ ramQuota: this.arrDetails.ramQuota, ramUsage: this.arrDetails.ramUsage, odd: this.arrDetails.odd, proportion: this.arrDetails.proportion, changname: 'MB' }] : []
          this.dataResource = res.data.ramQuota ? [{ ramQuota: unitConversion['MB'](this.arrDetails.ramQuota), ramUsage: unitConversion['MB'](this.arrDetails.ramUsage), odd: unitConversion['MB'](this.arrDetails.odd), proportion: this.arrDetails.proportion, changname: 'MB' }] : []
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else this.msgInfo('info', res.message)
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    ok (name) {
      this.$refs['formItem'].validate((valid) => {
        this.loadingStatus = true
        // if (valid) {
        if (this.formItem.approStatus === '') {
          this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
          this.loadingStatus = false
        } else {
          if (this.formItem.approStatus === 'REJECT' && this.formItem.auditRemark === '') {
            this.msgInfo('warning', '存在校验字段不通过，请检查！', true)
            this.loadingStatus = false
          } else {
            getResourceRecord(this.bizId, this.formItem.approStatus, this.formItem.auditRemark, this.chainId).then(res => {
              // console.log('addChainApproval===>', res)
              // this.msgInfo('info', res.message)
              this.tipInfo(res)
            }).catch(error => {
              this.loadingStatus = false
              // console.log('contractApprovel.error===>', error)
              this.msgInfo('error', error.message, true)
            })
          }
        }
      })
    },
    tipInfo (res) {
      if (res.code === '00000') {
        this.msgInfo('success', res.message, true)
        this.getTableData()
        this.getTableData2()
        this.modalAppro = false
        this.formItem.approStatus = ''
        this.formItem.auditRemark = ''
        this.loadingStatus = false
      } else {
        this.loadingStatus = false
        // console.log('tipInfo-error:', res.message)
        this.msgInfo('error', res.message, true)
      }
    },
    // 待审批取消
    cancelApp (name) {
      // this.init()
      this.modalAppro = false
      // 用户操作清空，状态和审批描述置空
      this.formItem.approStatus = ''
      this.formItem.auditRemark = ''
    },
    // 已审批取消
    cancelDet (name) {
      // this.init()
      this.modalDetail = false
    },
    // 重置
    resetting () {
      this.tenantIdList = []
      this.statusList = ['UNAPPROVED']
      this.chainAccountName = ''
      this.getTableData()
    },
    resetting2 () {
      this.tenantIdList2 = []
      this.statusList2 = []
      this.chainAccountName2 = ''
      this.getTableData2()
    }

  },

  watch: {
    tableData: {
      handler (newVal) {
        //
      },
      deep: true,
      immediate: false
    }
  },
  mounted () {
    this.getTableData()
    this.getTableData2()
    this.searchUserList()
  }

}
</script>

<style lang="less" scoped>

.chainapprovel {
  /deep/.ivu-tabs {
    min-height: calc(100vh - 208px);
  }
  .width-input{
    width:15vw;
    min-width:200px;
  }
}
input {
  margin: 0 0 10px;
}
button.btn {
  position: absolute;
  right: 10px;
  margin: 0 10px;
}
.bt1 {
  margin-right: 10px;
}
.search-title {
  font-size: 12px;
}

.title {
  font-weight: bold;
  font-size: 16px;
}
.bs {
  text-indent: 10px;
  line-height: 15px;
  border-left: 5px solid #3d73ef;
  margin-bottom: 15px;
}
.bg1 {
  position: relative;
  background-repeat: no-repeat;
  background: top right no-repeat;
  background-image: url("../../../assets/img/pass.png");
}
.bg2 {
  //以下是右下角图片设置
  position: relative;
  background-repeat: no-repeat;
  background: top right no-repeat;
  width: 100%;
  height: 140px;
  background-image: url("../../../assets/img/unpass.png");
}
.resultS {
  margin-top: 30px;
  font-weight: bold;
  font-size: 18px;
  color: #52c7aa;
  margin-left: 5px;
}

.resultF {
  margin-top: 20px;
  font-weight: bold;
  font-size: 18px;
  color: #ef7d68;
  margin-left: 5px;
}
.divS {
  //margin-top:10px;
  //margin-left: 30px;
  width: 500px;
  height: 70px;
  background-color: rgb(255, 255, 255);
}
/deep/.ivu-modal-footer {
  /* border-top: 1px solid #e8eaec; */
  /* padding: 12px 18px 12px 18px; */
  text-align: right;
  //background-color: #F5F6FA;
}
/deep/.ivu-modal > .ivu-modal-content > .ivu-modal-body {
  max-height: 45vh;
  overflow: auto;
}
/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
</style>
