<template>
  <div class="agreement-box">
    <!-- <SelectChain></Select<PERSON>hain> -->
    <div class="flex flex-align-item">
      <div class="node-title flex-align-item">合约信息</div>

    </div>
    <div class="nodeBox">
      <SpaceLayout top="0" paddingX="0" paddingY="0">
        <div slot="padding">
          <el-form class="demo-form-inline sel-size left">
            <div class="form-item channel-name">
              <span class="xing">*</span>
              <el-form-item label="选择通道：">
                <el-select v-model="aisleValue" @change="getOptionVal()" placeholder="请选择通道" :popper-append-to-body="false" popper-class="select-down">
                  <el-option v-for="msg in optionData" :key="msg.ChannelName" :label="msg.ChannelName" :value="msg.ChannelName"></el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-form>
          <div class="node-list-nav table-wrapper">
            <el-row class="nav-box">
              <el-col :span="4">
                <div class="">合约名称</div>
              </el-col>
              <el-col :span="4">
                <div class="">版本</div>
              </el-col>
              <el-col :span="5">
                <div class="">通道</div>
              </el-col>
              <el-col :span="5">
                <div class="">状态</div>
              </el-col>
              <el-col :span="6">
                <div class="">操作</div>
              </el-col>
            </el-row>
            <template v-if="listData.length > 0">
              <div class="nan-item" v-for="(msg, idx) in listData" :key="idx">
                <el-row class="nav-box">
                  <el-col :span="4">{{msg.Name}}</el-col>
                  <el-col :span="4">{{msg.Version}}</el-col>
                  <el-col :span="5">{{aisleValue}}</el-col>
                  <el-col :span="5">
                    <div v-if="!initName">
                      <div v-if="msg.Status == 'unInstalled'" class="tag gray">待安装</div>
                      <div v-if="msg.Status == 'inited'" class="tag green">可调用</div>
                      <div v-if="msg.Status == 'installed'" class="tag blue">待初始化</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="flex-align-item btn-group">
                      <!--                                    <div class="handle-btn" @click="installClick('01',msg)" :class="{disable:initName}">安装</div>-->
                      <div class="handle-btn disable" v-if="initName">安装</div>
                      <div class="handle-btn" v-else @click="installClick('01',msg)">安装</div>
                      <div v-if="msg.Status == 'installed'" @click="installClick('02', msg)" class="handle-btn" :class="{disable:msg.Status !== 'installed'|| initName }">初始化</div>
                      <div v-if="msg.Status !== 'installed'" class="handle-btn" :class="{disable:msg.Status !== 'installed'}">初始化</div>

                      <div v-if="msg.Status == 'inited'" @click="installClick('03', msg, idx)" class="handle-btn" :class="{disable:msg.Status !== 'inited'|| initName}">调用合约</div>
                      <div v-if="msg.Status !== 'inited'" class="handle-btn" :class="{disable:msg.Status !== 'inited'}">调用合约</div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </template>
            <div class="none" v-else>
              <i class="el-icon-loading" v-if="paddingText == '数据请求中...'"></i>
              <!-- <svg-icon icon-class="table-empty" v-else/> -->
              {{ paddingText }}
            </div>
          </div>

          <div class="pagination" v-show="listData.length > 0">
            <Page :total="total" :current.sync="pageIndex" @on-change="handleCurrentChange" :page-size="pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="handleSizeChange" style="text-align:right;" />
            <!-- <pagination
                  @toHandleSizeChange="handleSizeChange"
                  @toHandleCurrentChange="handleCurrentChange"
                  @toJumpFirstPage="jumpFirstPage"
                  @toJumpLastPage="jumpLastPage"
                  :fTotal="total"
                  :fBtnStartDisabled="btnStartDisabled"
                  :fBtnEndDisabled="btnEndDisabled"
                  :fPageIndex="pageIndex"
                  :fZys="zys"
                >
                </pagination> -->
          </div>

        </div>
      </SpaceLayout>
    </div>
    <!-- 安装合约弹框 -->
    <transition name="fade">
      <div class="alertBox" v-if="isInstall">
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">安装合约<i class="el-icon-cursor el-icon-close fr" @click="closeInstall('01')"></i></div>
          </div>
          <div class="alert_box fbIpt">
            <el-form ref="orgsForm" :model="orgsForm" :rules="orgsRules">
              <div class="selectBox">
                <div class="flex-align-item view">
                  <div class="tit">合约名称：</div>
                  <div class="titValue">{{contractItemData.Name}}</div>
                </div>
                <div class="flex-align-item view">
                  <div class="tit">通道名称：</div>
                  <div class="titValue">{{aisleValue}}</div>
                </div>
                <!--                <div class="evertModule">-->
                <!--                  <el-form-item class="view" label="合约名称：" prop="ChannelName">-->
                <!--                    <el-input-->
                <!--                      class="ipt"-->
                <!--                      v-model="contractItemData.Name"-->
                <!--                      disabled="disabled"-->
                <!--                    ></el-input>-->
                <!--                  </el-form-item>-->
                <!--                </div>-->
                <!--                <div class="evertModule">-->
                <!--                  <el-form-item class="view" label="通道名称：">-->
                <!--                    <el-input-->
                <!--                      class="ipt"-->
                <!--                      v-model="aisleValue"-->
                <!--                      disabled="disabled"-->
                <!--                    ></el-input>-->
                <!--                  </el-form-item>-->
                <!--                </div>-->
                <div class="evertModule">
                  <el-form-item label="组织节点：" prop="Orgs">
                    <el-table :data="tableData" :border="false" @selection-change="changeFun" max-height='210'>
                      <el-table-column type="selection" :width="40"></el-table-column>
                      <el-table-column v-for="(v, index) in tHead" :key="index" :prop="v.value" :label="v.title">
                        <template slot-scope="scope">
                          <div v-if="index == 0">{{scope.row[v.field]}}</div>
                          <div v-else>
                            <!-- <el-popover trigger="hover" placement="top">
                                <p>{{scope.row[v.field]}}</p>
                                <div slot="reference" class="name-wrapper">
                                <span slot="reference">{{checkName(scope.row[v.field])}}</span>
                                </div>
                            </el-popover> -->
                            <div slot="reference" class="name-wrapper">
                              <span slot="reference">{{checkName(scope.row[v.field])}}</span>
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <div class="btn-row">
            <Button class="sure-btn" type="primary" @click="addInstall">安装</Button>
            <Button class="border-btn" @click="closeInstall('01')">取消</Button>
          </div>
        </div>
      </div>
    </transition>
    <transition v-if="isInit" name="fade">
      <div class="alertBox">
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">初始化合约<i class="el-icon-cursor el-icon-close fr" @click="closeInstall('02')"></i></div>
          </div>
          <div class="alert_box fbIpt" ref="init">
            <el-form ref="orgsForm" :model="orgsForm" :rules="orgsRules">
              <div class="selectBox">
                <div class="flex-align-item view">
                  <div class="tit">合约名称：</div>
                  <div class="titValue">{{contractItemData.Name}}</div>
                </div>
                <div class="flex-align-item view">
                  <div class="tit">通道名称：</div>
                  <div class="titValue">{{aisleValue}}</div>
                </div>
                <!--                <div class="evertModule">-->
                <!--                  <el-form-item class="view" label="合约名称：" prop="ChannelName">-->
                <!--                    <el-input-->
                <!--                      class="ipt"-->
                <!--                      v-model="contractItemData.Name"-->
                <!--                      disabled="disabled"-->
                <!--                    ></el-input>-->
                <!--                  </el-form-item>-->
                <!--                </div>-->
                <!--                <div class="evertModule">-->
                <!--                  <el-form-item class="view" label="通道名称：" prop="ChannelName">-->
                <!--                    <el-input-->
                <!--                      class="ipt"-->
                <!--                      v-model="aisleValue"-->
                <!--                      disabled="disabled"-->
                <!--                    ></el-input>-->
                <!--                  </el-form-item>-->
                <!--                </div>-->

                <div class="evertModule">
                  <el-form-item label="背书组织：" prop="Orgs">
                    <el-table :data="InitData" :border="false" style="" max-height='210' ref="multipleTable" @selection-change="changeFun" @select="select" @select-all="selectAll">
                      <el-table-column type="selection" :width="40"></el-table-column>
                      <el-table-column v-for="(v, index) in InitHead" :key="index" :prop="v.value" :label="v.title">
                        <template slot-scope="scope">
                          <div class="tisName">{{scope.row[v.field]}}</div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <div class="hint" v-if="isBeishu"><span>{{beishuText}}</span></div>
                  </el-form-item>
                </div>
                <div class="evertModule">
                  <el-form-item label="生效条件：" prop="OrgsNum">
                    <span style="width:6%;display:inline-block" class="tip-size">需</span>
                    <el-input style="width:69%" type="text" v-model="number" placeholder="请输入" @input="number=number.replace(/[^1-9]/g,'')" @blur="inputFunc" @focus="clearNumber" />
                    <span style="width:25%;display:inline-block;text-align:right" class="tip-size">个组织同意</span>
                    <!-- <div class="userTipSpecial">需输入大于0且不超过通道内组织个数的整数</div> -->
                    <div class="hint OrgsNum" v-if="isNumber"><span>{{numberText}}</span></div>
                  </el-form-item>
                  <!--                  <div class="tit">生效条件：</div>-->

                  <!--                  <div class="start starts">*</div>-->

                </div>
                <div class="evertModule input">
                  <div v-for="(item,index) in argumentList" :key="index" class="inputwrap">
                    <el-form-item :label="'参数'+(index + 1)+'设置：'">
                      <el-input type="text" v-model="item.text" @input="validArgument(item.text,index)" @blur="validArgument(item.text,index)" @focus="claerArgument(index)" placeholder="请输入" class="addinput" />
                      <div class="add" @click="addArgumentList" v-if="index == argumentList.length - 1">
                        <img src="../../../assets/image/add.png">
                      </div>
                      <div class="delete" @click="deleteArgumentList" v-if="index > 0 && index == argumentList.length - 1">
                        <img src="../../../assets/image/delete.png">
                      </div>
                      <div class="hint inputhint" v-if="argumentList[index].isshowError"><span>请输入1-30位字符，支持中文、英文、数字、特殊字符</span></div>
                      <!-- <el-input type="text" v-model="argument" @blur="validArgument" @focus="claerArgument" placeholder="请输入"/>
                      <div class="userTipSpecial">参数间用空格分开，示例："参数1" "参数2" ...（选填，参数是否填写与合约有关）</div> -->
                      <!-- <div class="hint" v-if="isArgument"><span >输入格式有误</span></div> -->
                    </el-form-item>
                  </div>
                  <!-- <div class="userTipSpecial">选填，参数是否填写与合约有关。参数输入规则：支持中文、英文、数字及特殊符号，1-30位字符</div> -->
                </div>
              </div>
            </el-form>

            <!--            <div class="flex-align-item box-item">-->
            <!--              <div class="tit">通道名称：</div>-->
            <!--              <div class="titValue">{{aisleValue}}</div>-->
            <!--            </div>-->

          </div>
          <div class="btn-row">
            <Button class="sure-btn" type="primary" @click="addInit">初始化</Button>
            <Button class="border-btn" @click="closeInstall('02')">取消</Button>
          </div>
        </div>
      </div>
    </transition>
    <transition>
      <div class="alertBox" v-if="isCall" name="fade">
        <!--                    <div class="alertBox" v-if="true" name="fade">-->
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">调用合约
              <i class="el-icon-cursor el-icon-close fr" @click="closeInstall('03')"></i>
            </div>
          </div>
          <div class="alert_box fbIpt" ref="invoke">
            <el-form ref="orgsForm" :model="orgsForm" :rules="orgsRules">
              <div class="selectBox">
                <div class="flex-align-item view">
                  <div class="tit">合约名称：</div>
                  <div class="titValue">{{contractItemData.Name}}</div>
                </div>
                <div class="flex-align-item view">
                  <div class="tit">背书组织：</div>
                  <div class="titValue">{{endorsed}}</div>
                </div>
                <div class="flex-align-item view">
                  <div class="tit">背书状态：</div>
                  <div class="titValue">{{endorsedStatus}}</div>
                </div>
                <!-- <div class="evertModule">
                  &lt;!&ndash;                        <div class="tit">合约名称：</div>&ndash;&gt;
                  &lt;!&ndash;                        <div class="titValue">{{aisleValue}}</div>&ndash;&gt;
                  <el-form-item class="view" label="合约名称：" prop="">
                    <el-input
                      class="ipt"
                      v-model="aisleValue"
                      disabled="disabled"
                    ></el-input>
                  </el-form-item>
                </div>
                <div class="evertModule">
                  &lt;!&ndash;                        <div class="tit">背书组织：</div>&ndash;&gt;
                  &lt;!&ndash;                        <div class="titValue">{{endorsed}}</div>&ndash;&gt;
                  <el-form-item class="view" label="背书组织：" prop="">
                    <el-input
                      class="ipt"
                      v-model="endorsed"
                      disabled="disabled"
                    ></el-input>
                  </el-form-item>
                </div>
                <div class="evertModule">
                  &lt;!&ndash;                        <div class="tit">背书状态：</div>&ndash;&gt;
                  &lt;!&ndash;                        <div class="titValue">{{endorsedStatus}}</div>&ndash;&gt;
                  <el-form-item class="view" label="背书状态：" prop="">
                    <el-input
                      class="ipt"
                      v-model="endorsedStatus"
                      disabled="disabled"
                    ></el-input>
                  </el-form-item>
                </div>-->
                <div class="evertModule">
                  <!--                        <div class="tit tit-top">组织节点：</div>-->
                  <el-form-item prop="Orgs" label="组织节点：">
                    <el-table :data="callData" :border="false" @selection-change="changeFun" @select="selects" @select-all="selectAlls" max-height='210'>
                      <el-table-column type="selection" :width="40"></el-table-column>
                      <el-table-column v-for="(v, index) in callThead" :key="index" :prop="v.value" :label="v.title">
                        <template slot-scope="scope">
                          <div v-if="index == 0">{{scope.row[v.field]}}</div>
                          <div v-else>
                            <!-- <el-popover trigger="hover" placement="top">
                                <p>{{scope.row[v.field]}}</p>
                                <div slot="reference" class="name-wrapper">
                                <span slot="reference">{{checkName(scope.row[v.field])}}</span>
                                </div>
                            </el-popover> -->
                            <div slot="reference" class="name-wrapper">
                              <span slot="reference">{{checkName(scope.row[v.field])}}</span>
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>
                    <div class="hint" v-if="iscallT"><span>请选择组织节点</span></div>
                  </el-form-item>

                  <!--                        <div class="start">*</div>-->
                </div>
                <div class="evertModule">
                  <el-form-item prop="Orgs" label="调用方法：">
                    <el-select @change="getOptionVals()" v-model="funValue" placeholder="请选择调用方法域" popper-class="area_popper">
                      <el-option label="invoke" value="invoke"></el-option>
                      <el-option label="query" value="query"></el-option>
                    </el-select>
                    <div class="hint" v-if="iscallD"><span>请选择调用方法</span></div>
                  </el-form-item>
                  <!--                        <div class="tit">调用方法：</div>-->
                  <!--                        <div class="trem trem_width">-->
                  <!--                          <div class="inputVal">-->
                  <!--                            <el-form class="demo-form-inline">-->
                  <!--                              <el-form-item>-->
                  <!--                                <el-select @change="getOptionVals()" v-model="funValue" placeholder="请选择调用方法域">-->
                  <!--                                  <el-option label="invoke" value="invoke"></el-option>-->
                  <!--                                  <el-option label="query" value="query"></el-option>-->
                  <!--                                </el-select>-->
                  <!--                              </el-form-item>-->
                  <!--                            </el-form>-->
                  <!--                          </div>-->

                  <!--                        </div>-->
                  <!--                        <div class="start starts">*</div>-->

                </div>
                <div class="evertModule input">
                  <div v-for="(item,index) in funargumentList" :key="index" class="inputwrap">
                    <el-form-item :label="'参数'+(index + 1)+'设置：'">
                      <el-input type="text" v-model="item.text" @input="validFunargument(item.text,index)" @focus="clearFunargument(index)" @blur="validFunargument(item.text,index)" placeholder="请输入" class="addinput" />
                      <div class="lockCode" @click="lookCodeFun" v-if="index == 0">查看合约代码</div>
                      <div class="add" @click="addFunargumentList" v-if="index == funargumentList.length - 1">
                        <img src="../../../assets/image/add.png">
                      </div>
                      <div class="delete" @click="deleteFunargumentList" v-if="index > 0 && index == funargumentList.length - 1">
                        <img src="../../../assets/image/delete.png">
                      </div>
                      <div class="hint inputhint" v-if="funargumentList[index].isshowError"><span>请输入1-30位字符，支持中文、英文、数字、特殊字符</span></div>
                    </el-form-item>
                  </div>
                  <!-- <div class="userTipSpecial">选填。参数输入规则：支持中文、英文、数字及特殊符号，1-30位字符</div> -->
                </div>
                <!-- <div class="evertModule">
                  <el-form-item prop="" label="参数设置：">
                    <el-input type="text" v-model="funargument" @blur="validFunargument" @focus="clearFunargument" placeholder="请输入"/>
                    <div class="lockCode" @click="lookCodeFun">查看合约代码</div>
                    <div class="userTipSpecial">方法名、参数间用空格分开，示例："方法名" "参数1" "参数2" ...（方法名选填）</div>
                    <div class="hint" v-if="iscallC"><span >输入格式有误</span></div>
                  </el-form-item>
                </div> -->
              </div>
            </el-form>

          </div>
          <div class="btn-row invoke-btn-wrap">
            <Button class="sure-btn" type="primary" @click="invoke()">调用</Button>
            <Button class="border-btn" @click="closeInstall('03')">取消</Button>
          </div>
        </div>
      </div>
    </transition>
    <transition v-if="isCode" name="fade">
      <div class="alertBox">
        <div class="addTissue" style="width:1000px;max-width:1000px;    position: relative;">
          <div class="alertTop">
            <div class="tit">查看合约代码
              <i class="el-icon-cursor el-icon-close fr" @click="closeInstall('04')"></i>
            </div>
            <!--            <div class="closeIcon close_img" @click="closeInstall('04')">-->
            <!--              <img :src="closeImg" alt="">-->
            <!--            </div>-->
          </div>
          <div class="alert_box">
            <div class="selectBox log">
              <p style="white-space: pre-wrap;">{{soundCode}}</p>
            </div>
          </div>

        </div>
      </div>
    </transition>
    <transition v-if="issuccess" name="fade">
      <div class="alertBox">
        <div class="addTissue">
          <div class="alertTop">
            <div class="tit">调用结果
              <i class="el-icon-cursor el-icon-close fr" @click="closeInstall('05')"></i>
            </div>
          </div>
          <div class="contract_box">
            <div class="soundCode">
              <p v-for="(item,index) in successData" :key="index">
                {{item.key+':'+item.value}}
              </p>
            </div>
          </div>
        </div>
      </div>
    </transition>
    <div v-if="loading" class="BoxLoading" v-loading="loading" :element-loading-text="text">
    </div>
    <!-- <countDown v-if="isShowIcon" :state="countState" :countTime="countTime" :text="countText" @getCountDown="getCountDown"></countDown> -->
  </div>
</template>
<script>
import {
  getChannelList,
  getChannelChaincodeList,
  getNoChaincodePeerList,
  InstallChainCode,
  initChaincode,
  callChaincode,
  chaincodeFunc,
} from '@/api/baascore/agreement'
import { getChaincodeEndorseRule } from '@/api/baascore/channelMgr'
import { base64_encode, forbidden } from "@/utils/index.js";
import { calcCostMixin } from '@/utils/mixin'
import SelectChain from '../compontents/selectChain'
import SpaceLayout from '@/components/SpaceLayout'

export default {
  //inject: ['reload'],
  mixins: [forbidden, calcCostMixin],
  components: {
    SelectChain,
    SpaceLayout
  },
  data () {
    return {
      countState: '',
      countTime: 2,
      countText: '',
      isShowIcon: false,
      closeImg: require('@/assets/image/close.png'),
      text: "初始化中",
      loading: false,
      chainInfo: '', // 一键建链的数据
      aisleValue: '', //选择通道
      optionData: [], // 通道下拉数据
      contractData: [], //合约列表数据
      contractItemData: '', //选中某一条的合约数据
      isInstall: false, // 是否展示安装合约弹窗
      tableData: [], //表格数据
      tHead: [
        // 表格的头部数据
        { field: 'name', title: '组织名称' },
        { field: 'value', title: '节点名称' },
      ],
      checkData: [], // 安装合约选中的组织节点
      isHint: false, // 是否展示安装合约提示信息
      isInit: false, // 是否展示初始化弹窗
      InitHead: [
        // 初始化合约表头
        { field: 'Name', title: '组织名称' }
      ],
      InitData: [], // 初始化数据
      isBeishu: false, // 是否展示背书组织
      beishuText: '请选择背书组织',
      number: '', // 生效条件
      numberText: '请输入生效条件',
      isNumber: false, // 是否展示生效提交提示
      isArgument: false, // 是否展示参数提示
      isArgText: '请输入参数设置',
      argument: '', //参数
      isCall: false, // 是否展示调用合约弹窗
      // 调用合约
      endorsed: '', //背书组织
      endorsedStatus: '', //背书状态
      callData: [], //调用合约表格数据
      callThead: [    //表格头部
        { field: 'name', title: '组织名称' },
        { field: 'value', title: '节点名称' },
      ],
      iscallT: false, // 请选择组织节点
      iscallD: false, // 请选择调用方法
      funValue: 'invoke', //调用方法
      funargument: '', //参数设置
      iscallC: false, // 请输入参数设置
      iscallCt: '请输入参数设置',
      isCode: false, // 查看合约代码
      soundCode: '', //源码
      total: 0,
      btnStartDisabled: false, //用来判断首页尾页按钮是否禁用
      btnEndDisabled: false, //用来判断首页尾页按钮是否禁用
      zys: 0,
      pageIndex: 1,
      pageSize: 10,
      listData: [],
      issuccess: false,
      successData: [],
      orgsRules: {
        Orgs: [{ required: true, message: '请添加组织节点', trigger: "blur" },],
        OrgsNum: [{ required: true, message: '', trigger: "none" },],
      },
      orgsForm: {
        Orgs: [],
      },
      paddingText: '数据请求中...',
      initName: false,//第一次不带合约名称调列表
      argumentList: [
        {
          text: '',
          isshowError: false,
        }
      ],
      funargumentList: [
        {
          text: '',
          isshowError: false,
        }
      ],
    }
  },
  computed: {
  },
  watch: {
    // chainItem: {
    // handler(newvalue,oldvalue) {
    //     this.chainInfo =  JSON.parse(sessionStorage.getItem('chainItem'))
    //     this.ServiceId = this.chainInfo.Id
    //     this.promiseFun(this.chainInfo.Id)
    // },
    // deep:true
    // }
  },
  updated () {
  },
  mounted () {
    this.chainInfo = JSON.parse(sessionStorage.getItem('chainItem'))
    this.ServiceId = this.chainInfo.Id
    this.promiseFun(this.chainInfo.Id)
  },
  methods: {
    getCountDown (type) {
      this.isShowIcon = false
    },
    handleSizeChange (val) {
      this.pageSize = val
      this.getListData()
    },
    handleCurrentChange (val) {
      this.pageIndex = val
      this.getListData()
    },
    // 首页按钮
    jumpFirstPage (val) {
      this.pageIndex = val;
      this.handleCurrentChange(val);
    },
    // 尾页按钮
    jumpLastPage (val) {
      this.pageIndex = val;
      this.handleCurrentChange(this.pageIndex);
    },
    getListData () {
      // this.zys = Math.ceil(this.total / this.pageSize); //获取总页数
      // this.pageIndex > this.zys ? this.pageIndex = this.zys : '';
      // this.forbidden(this.zys, this.pageIndex);
      this.listData = this.contractData.slice((this.pageIndex - 1) * this.pageSize, this.pageSize * this.pageIndex);
    },
    // promis
    promiseFun (id) {
      this.getChannelList(id).then(res => {
        if (res.Channels) {
          let name = res.Channels[0].ChannelName
          this.getChannelChaincodeList(id, name)
        } else {
          this.getChannelChaincodeList(id)
        }
      }).catch(() => {
        // this.getChannelChaincodeList(id)
        this.paddingText = '暂无数据'
      })
    },
    //1、获取通道列表
    getChannelList (id) {
      return new Promise((resolve, reject) => {
        var query = {
          ServiceId: id
        }
        getChannelList(query).then(res => {
          if (res.code == 200) {
            // this.isShowIcon = true
            // this.countState = 'success'
            // this.countText = '请求成功！'
            if (res.data.Channels) {
              this.optionData = res.data.Channels
              this.aisleValue = res.data.Channels[0].ChannelName
              //resolve(res.data)
            } else {
              this.aisleValue = ''
              this.optionData = []
              // this.paddingText = '暂无数据'
            }
            resolve(res.data)
          } else {
            //this.paddingText = '暂无数据'
            // this.isShowIcon = true
            // this.countState = 'error'
            // this.countText = '数据获取失败，请重新加载！'
            reject(res.data)
            this.$message.error('数据获取失败，请重新加载！')
          }
        })
      })
    },

    //2、取某个通道上的智能合约列表
    getChannelChaincodeList (id, name) {
      return new Promise((resolve, reject) => {
        var query = {};
        if (name) {
          this.initName = false;
          query = {
            ServiceId: id,
            ChannelName: name
          }
        }
        else {
          this.initName = true;
          query = {
            ServiceId: id
          }
        }
        getChannelChaincodeList(query).then(res => {
          if (res.code == 200) {
            // this.isShowIcon = true
            // this.countState = 'success'
            // this.countText = '请求成功！'
            if (res.data.ChainCodes && res.data.ChainCodes.length > 0) {
              this.contractData = res.data.ChainCodes;
              this.total = res.data.ChainCodes.length
              if (this.total == 0) {
                this.paddingText = '暂无数据'
              }
              this.getListData()
            } else {
              this.contractData = []
              this.listData = []
              this.paddingText = '暂无数据'
            }
          } else {
            this.paddingText = '暂无数据'
            // this.isShowIcon = true
            // this.countState = 'error'
            // this.countText = '数据获取失败，请重新加载！'
            this.$message.error('数据获取失败，请重新加载！')
          }
        })
      })
    },
    //2、取某个通道上的智能合约列表
    getChannelChaincodeLists (id, name) {
      return new Promise((resolve, reject) => {
        var query = {
          ServiceId: id,
          ChannelName: name
        }
        getChannelChaincodeList(query).then(res => {
          if (res.code == 200) {
            if (res.data.ChainCodes && res.data.ChainCodes.length > 0) {
              resolve(res.data)
            }
          } else {
            this.$message.error('数据获取失败，请重新加载！')
          }
        })
      })
    },

    // 3、选择通道选中请求
    getOptionVal () {
      this.getChannelChaincodeList(this.ServiceId, this.aisleValue)
    },

    // 列表安装按钮、初始化按钮
    installClick (code, msg, idx) {
      // 01 安装  02 初始化 03 调用合约
      if (code == '01') {
        this.getNoChaincodePeerList(this.ServiceId, this.aisleValue, msg.Name,)
        this.contractItemData = msg
        // this.isInstall = true
      } else if (code == '02') {
        this.contractItemData = msg
        let msgs = msg
        // 过滤orgs数据
        let filterData = []
        if (msgs.Orgs && msgs.Orgs.length > 0) {
          msgs.Orgs.forEach(msg => {
            filterData.push(msg)
          })
        }
        this.InitData = filterData
        this.isInit = true
      } else if (code == '03') {
        //this.getChannelChaincodeLists(this.ServiceId, this.aisleValue).then(res => {
        //let msg = res.ChainCodes[idx]
        if (msg.Orgs && msg.Orgs.length > 0) {
          let filterData = []
          msg.Orgs.forEach((item) => {
            for (var i in item.Peers) {
              let obj = {}
              obj.name = item.Name
              obj.value = item.Peers[i]
              filterData.push(obj)
            }
          })
          this.callData = filterData
        }
        this.getChaincodeEndorseRule(this.ServiceId, msg.Name, msg.Version, this.aisleValue,)
        //})
        this.contractItemData = msg
        /*let msgs = msg
        // 过滤orgs数据
        let filterData = []
        if(msgs.Orgs && msgs.Orgs.length > 0) {
            msgs.Orgs.forEach(item => {
                filterData.push(item.Name)
            })
        }
        this.endorsed = filterData.join('，')*/
      }
    },
    //取消弹窗+初始化弹窗
    closeInstall (code) {
      if (code == '01') {
        this.$refs.orgsForm.resetFields();
        this.isInstall = false
        this.isHint = false
        this.checkData = []
      } else if (code == '02') {
        this.isInit = false
        this.isBeishu = false
        this.isArgument = false
        this.isNumber = false
        this.number = ''
        this.argument = ''
        this.checkData = []
        this.argumentList = [{ text: '', isshowError: false, }]
      } else if (code == '03') {
        this.isCall = false
        this.endorsedStatus = ''
        this.endorsed = ''
        this.funValue = 'invoke'
        this.funargument = ''
        this.iscallC = false
        this.iscallT = false
        this.iscallD = false
        this.checkData = []
        this.funargumentList = [{ text: '', isshowError: false, }]
      } else if (code == '04') {
        this.isCode = false
        this.isCall = true
      } else if (code == '05') {
        this.issuccess = false
      }

    },
    // 获取复选框选中的数据
    changeFun (val) {
      this.checkData = val
      this.orgsForm.Orgs = val
      if (val && this.number) {
        var length = val.length
        if (this.number > length) {
          this.isNumber = true
          this.numberText = '输入数字不能超过背书组织个数'
        }
        else {
          this.isNumber = false
          this.numberText = ''
        }
      }
      this.$refs["orgsForm"].clearValidate(["Orgs"]);
    },

    select (selection, row) {
      if (selection.length > 4) {
        this.beishuText = '最多可选4个组织'
        this.isBeishu = true
      } else {
        this.isBeishu = false
      }
    },
    selectAll (selection) {
      if (selection.length > 4) {
        this.beishuText = '最多可选4个组织'
        this.isBeishu = true
      } else {
        this.isBeishu = false
      }
    },

    // 4、（合约安装）查询某合约没有被安装的节点列表
    getNoChaincodePeerList (id, name, codeName) {
      var query = {
        ServiceId: id,
        ChannelName: name,
        ChainCodeName: codeName
      }
      getNoChaincodePeerList(query).then(res => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          let msg = res.data
          let isAllPeers = false
          if (msg.Orgs.length > 0) {
            msg.Orgs.forEach(item => {
              if (item.Peers) {
                isAllPeers = true
              }
            })
            //// 过滤orgs数据
            if (isAllPeers) {
              this.isInstall = true
              let filterData = []
              msg.Orgs.forEach((item) => {
                for (var i in item.Peers) {
                  let obj = {}
                  obj.name = item.Name
                  obj.value = item.Peers[i]
                  filterData.push(obj)
                }
              })
              this.tableData = filterData
            } else {
              // this.isShowIcon = true;
              // this.countText = "通道中已无可安装合约的节点，请添加成员节点或在其他通道中安装合约。";
              // this.countState = "success";
              this.$message.warning('通道中已无可安装合约的节点，请添加成员节点或在其他通道中安装合约。');
            }
          } else {
            // this.isShowIcon = true;
            // this.countText = "通道中已无可安装合约的节点，请添加成员节点或在其他通道中安装合约。";
            // this.countState = "success";
            this.$message.warning('通道中已无可安装合约的节点，请添加成员节点或在其他通道中安装合约。');
          }

        } else if (res.code == 4403) {
          this.$message.error(res.message)
        }
        else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '数据获取失败，请重新加载！'
          this.$message.error('数据获取失败，请重新加载！')
        }
      })
    },

    // 5、安装合约
    addInstall () {
      this.$refs.orgsForm.validate((valid) => {
        if (valid) {
          if (this.checkData.length > 0) {
            // this.isHint = false
            // 过滤组合选中的数据
            let filterArr = []
            let tempArr = [];
            for (let i = 0; i < this.checkData.length; i++) {
              if (tempArr.indexOf(this.checkData[i].name) === -1) {
                filterArr.push({
                  Name: this.checkData[i].name,
                  Peers: [this.checkData[i].value]
                });
                tempArr.push(this.checkData[i].name);
              } else {
                for (let j = 0; j < filterArr.length; j++) {
                  if (filterArr[j].Name == this.checkData[i].name) {
                    filterArr[j].Peers.push(this.checkData[i].value);
                    break;
                  }
                }
              }
            }
            this.InstallChainCode(this.contractItemData.Name, this.contractItemData.Version, filterArr)
          }
        } else {
          return false
        }
      })


    },
    // 5、安装合约
    InstallChainCode (codeName, Version, arr) {
      let msg = {
        // msgType: 'installChaincode',
        // params:{
        "ServiceId": this.ServiceId,
        "ChannelName": this.aisleValue,
        "ChainCodeName": codeName,
        "Version": Version,
        "Orgs": arr
        // }
      }
      this.loading = true;
      this.text = '安装中';
      InstallChainCode(msg).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '合约可信检测正常，安装成功！'
          this.$message.success('安装成功！')
          this.closeInstall('01')
          // this.reload()
          this.getChannelChaincodeList(this.ServiceId, this.aisleValue)
        } else if (res.code == 4401) {
          this.$message.error(res.message)
        } else if (res.code == 4404) {
          this.$message.error(res.message)
        } else if (res.code == 4405) {
          this.$message.error(res.message)
        } else if (res.code == 4411) {
          this.$message.error(res.message)
        } else if (res.code == 4230) {
          this.$message.error('合约安装操作失败，请稍后再试！')
        }
        else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '合约安装失败，请重新安装！'
          this.$message.error('合约安装失败，请重新安装!')
        }
      })
    },
    //
    addArgumentList () {
      this.argumentList.push({ item: '', isshowError: false })
      this.$refs.init.scrollTop = this.$refs.init.scrollHeight
    },
    addFunargumentList () {
      this.funargumentList.push({ item: '', isshowError: false })
      this.$refs.invoke.scrollTop = this.$refs.invoke.scrollHeight

    },
    deleteArgumentList () {
      this.argumentList.pop()
      if (this.argumentList.length == 1 && this.argumentList[0].text.length < 30) {
        this.argumentList[0].isshowError = false
      }
    },
    deleteFunargumentList () {
      this.funargumentList.pop()
      if (this.funargumentList.length == 1 && this.funargumentList[0].text.length < 30) {
        this.funargumentList[0].isshowError = false
      }
    },
    validArgument (value, index) {
      //var regex = /^("[\u4E00-\u9FA5\w\s]*"\s)*"[\u4E00-\u9FA5\w\s]*"{1}$/g;
      if (value) {
        if (value.length > 30) {
          this.argumentList[index].isshowError = true
        } else {
          this.argumentList[index].isshowError = false
        }
      }
      if (this.argumentList.length > 1) {
        this.argumentList.forEach((citem, cindex) => {
          if (citem.text == '' || citem.text == undefined || citem.text == null || citem.text.length > 30) {
            this.argumentList[cindex].isshowError = true
          } else {
            this.argumentList[cindex].isshowError = false
          }
        })
      }
    },
    claerArgument (index) {
      this.argumentList[index].isshowError = false
    },
    // 6、初始化合约
    addInit () {
      if (this.checkData.length == 0) {
        this.beishuText = '请选择背书组织'
        this.isBeishu = true
      } else {
        if (this.checkData.length > 4) {
          this.beishuText = '背书组织最多勾选4个组织'
          this.isBeishu = true
        }
      }
      if (!this.number) {
        this.numberText = '请输入生效条件'
        this.isNumber = true
      }
      this.isArgument = false
      this.argument = []
      // var regex=/\[|\]/g
      //var regex = /^\["[\u4E00-\u9FA5\w]*"(,"[\u4E00-\u9FA5\w].*")?\]$/g;
      this.validArgument()
      this.argumentList.forEach(item => {
        if (item.isshowError) {
          this.isArgument = true
        }
        if (!item.isshowError) {
          if (item.text == undefined) {
            item.text = ''
          }
          this.argument.push(base64_encode(item.text))
        }
      })
      if (this.argument.length == 1 && this.argument[0] == '') {
        this.argument = []
      }
      if (this.isBeishu || this.isNumber || this.isArgument) {
        return
      }
      this.initChaincode()
    },

    // 6、初始化合约请求
    initChaincode () {
      this.text = '初始化中'
      this.loading = true;
      let msg = {
        //msgType: 'initChaincode',
        //params:{
        "ServiceId": this.ServiceId,
        "ChannelName": this.aisleValue,
        "EndorsePolicy": [
          {
            "Rule": this.checkData.length == this.number ? 'all' : 'part',
            // "Rule": this.InitData.length == this.checkData.length ? 'all' : this.checkData.length == this.number ? 'all': 'part',
            "AgreeCount": parseInt(this.number),
            "Orgs": this.checkData,
          }
        ],
        "ChainCodeName": this.contractItemData.Name,
        "Version": this.contractItemData.Version,
        //"Args": this.argument ? JSON.parse(this.argument) : ''
        "Args": this.argument
        //}
      }
      initChaincode(msg).then((res) => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '合约初始化成功！'
          this.$message.success('合约初始化成功！')
          this.closeInstall('02')
          this.getChannelChaincodeList(this.ServiceId, this.aisleValue)
          this.loading = false;
        } else if (res.code == 4403 || 4404 || 4410 || 1026) {
          this.$message.error(res.message)
          this.loading = false;
        } else if (res.code == 4230) {
          this.$message.error('合约初始化操作失败，请稍后再试！')
          this.loading = false;
        }
        else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '合约初始化失败，请重新初始化！'
          this.$message.error('合约初始化失败，请重新初始化!')
          this.loading = false;
        }
      }).catch(() => {
        //this.$message.error('网络异常,请检查网络')
        this.loading = false;
      })
    },

    // 7、调用合约弹窗获取背书状态和组织节点
    getChaincodeEndorseRule (id, codeName, version, name) {
      var query = {
        ServiceId: id,
        ChainCodeName: codeName,
        Version: version,
        ChannelName: name
      }
      getChaincodeEndorseRule(query).then(res => {

        if (res.code == 200) {

          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          this.endorsedStatus = res.data[0].Rule == 'all' ? '全部生效' : '部分生效'
          // 过滤orgs数据
          let msg = res.data[0]
          // if(msg.Orgs && msg.Orgs.length > 0 && msg.Orgs[0].Peers) {
          //     let filterData = []
          //     msg.Orgs.forEach((item) => {
          //         for(var i in item.Peers){
          //             let obj = {}
          //             obj.name = item.Name
          //             obj.value = item.Peers[i]
          //             filterData.push(obj)
          //         }
          //     })
          //     this.callData = filterData
          // }
          if (msg.Orgs && msg.Orgs.length > 0) {
            let filterData = []
            msg.Orgs.forEach((item) => {
              filterData.push(item.Name)
            })
            this.endorsed = filterData.join('，')
          }
          this.isCall = true
        } else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = res.message
          this.$message.error(res.message)
        }
      })
    },
    selects (selection, row) {
      if (selection.length > 0) {
        this.iscallT = false
      } else {
        this.iscallT = true
      }
    },
    selectAlls (selection) {
      if (selection.length > 0) {
        this.iscallT = false
      } else {
        this.iscallT = true
      }
    },
    getOptionVals () {
      if (this.funValue) {
        this.iscallD = false
      }
    },
    //效验参数
    validFunargument (value, index) {
      if (value) {
        if (value.length > 30) {
          this.funargumentList[index].isshowError = true
        } else {
          this.funargumentList[index].isshowError = false
        }
      }
      if (this.funargumentList.length > 1) {
        this.funargumentList.forEach((citem, cindex) => {
          if (citem.text == '' || citem.text == undefined || citem.text == null || citem.text.length > 30) {
            this.funargumentList[cindex].isshowError = true
          } else {
            this.funargumentList[cindex].isshowError = false
          }
        })
      }
      // var regex = /^("[\u4E00-\u9FA5\w\s]*"\s)*"[\u4E00-\u9FA5\w\s]*"{1}$/g;
      // if(this.funargument){
      //     if(!regex.test(this.funargument)) {
      //       // if()
      //         this.iscallC = true
      //         return
      //     } else {
      //         this.iscallC = false
      //     }
      //   }else{
      //       this.iscallC = false
      //   }
    },
    clearFunargument (index) {
      //this.iscallC = false
      this.funargumentList[index].isshowError = false
    },
    // 7、调用合约~调用按钮
    invoke () {
      if (this.checkData.length == 0) {
        this.iscallT = true
      }
      if (!this.funValue) {
        this.iscallD = true
      }
      this.validFunargument()
      // var regex = /^\["[\u4E00-\u9FA5\w]*"(,"[\u4E00-\u9FA5\w].*")?\]$/g;
      // var regex = /^("[\u4E00-\u9FA5\w\s]*"\s)*"[\u4E00-\u9FA5\w\s]*"{1}$/g;
      // if(this.funargument){
      //   if(!regex.test(this.funargument)) {
      //     // if()
      //       this.iscallC = true
      //       return
      //   } else {
      //       this.iscallC = false
      //   }
      // }else{
      //     this.iscallC = false
      // }
      if (this.checkData.length > 0 && this.funValue) {
        this.isHint = false
        // 过滤组合选中的数据
        let filterArr = []
        let tempArr = [];
        for (let i = 0; i < this.checkData.length; i++) {
          if (tempArr.indexOf(this.checkData[i].name) === -1) {
            filterArr.push({
              Name: this.checkData[i].name,
              Peers: [this.checkData[i].value]
            });
            tempArr.push(this.checkData[i].name);
          } else {
            for (let j = 0; j < filterArr.length; j++) {
              if (filterArr[j].Name == this.checkData[i].name) {
                filterArr[j].Peers.push(this.checkData[i].value);
                break;
              }
            }
          }
        }
        this.iscallC = false
        this.funargument = []
        // var regex=/\[|\]/g
        //var regex = /^\["[\u4E00-\u9FA5\w]*"(,"[\u4E00-\u9FA5\w].*")?\]$/g;
        this.funargumentList.forEach(item => {
          if (item.isshowError) {
            this.iscallC = true
          }
          if (!item.isshowError) {
            if (item.text == undefined) {
              item.text = ''
            }
            this.funargument.push(base64_encode(item.text))
          }
        })
        if (this.funargument.length == 1 && this.funargument[0] == '') {
          this.funargument = []
        }
        if (this.iscallC) {
          return
        }
        this.callChaincode(filterArr)
      }

    },
    //格式话数组
    regArray (str) {
      // var re1 = new RegExp(/\[/)
      // var re2 = new RegExp(/\]/)
      // var re3 = new RegExp(/"/g)
      // var a = str.replace(re1,'').replace(re2,'').replace(re3,'')
      // var arr = a.split(',')
      var b = str.substr(1)
      var c = b.substr(0, b.length - 1)
      var arr = c.split('" "')
      return arr
    },
    // 7、调用合约请求
    callChaincode (arr) {
      this.text = '调用合约中'
      this.loading = true;
      let msg = {
        // msgType: 'callChaincode',
        // params:{
        "ChainCodeName": this.contractItemData.Name,
        // "Args": this.funargument ? JSON.parse(this.funargument) : '',
        "Args": this.funargument,
        "ServiceId": this.ServiceId,
        "Function": this.funValue,
        "ChannelName": this.aisleValue,
        "Orgs": arr
        // }
      }
      callChaincode(msg).then((res) => {
        if (res.code == 200) {
          // this.$message({
          //     message: '合约调用成功!',
          //     type: 'success'
          // });
          // this.closeInstall('03')
          // this.reload()
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '合约调用成功！'
          var obj = res.data
          var arr = []
          if (obj) {
            for (var i in obj) {
              var a = {
                key: i,
                value: obj[i]
              }
              arr.push(a)
            }
          }
          this.successData = arr

          this.issuccess = true
          // this.getChannelChaincodeList(this.ServiceId, this.aisleValue)
          this.loading = false;
        } else if (res.code == 4411 || 1001) {
          this.$message.error(res.message)
          this.loading = false;
        }
        else {
          this.$message.error('合约调用失败，请重新调用!')
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '合约调用失败，请重新调用！'
          this.loading = false;
        }

      }).catch(() => {
        //this.$message.error('网络异常,请检查网络')
        this.loading = false;
      })
    },

    // 8查看合约代码
    lookCodeFun () {
      // this.isCall = false
      // this.isCode = true
      this.chaincodeFunc()
    },

    // 合约源码
    chaincodeFunc () {
      let msg = {
        // msgType: 'chaincodeFunc',
        // params:{
        "ChainCodeName": this.contractItemData.Name,
        //"ServiceId": this.ServiceId, //暂时不传递此字段
        "Version": this.contractItemData.Version
        // }
      }
      chaincodeFunc(msg).then((res) => {
        if (res.code == 200) {
          // this.isShowIcon = true
          // this.countState = 'success'
          // this.countText = '请求成功！'
          if (res.data[0].Body) {
            // this.isCall = false
            this.isCode = true
            this.soundCode = res.data[0].Body
          }
        } else if (res.code == 4403) {
          this.$message.error(res.message)
        }
        else {
          // this.isShowIcon = true
          // this.countState = 'error'
          // this.countText = '查看合约代码失败，请重新查看！'
          this.$message.error('查看合约代码失败，请重新查看!')
          // this.isCall = true
          // this.isCode = false
        }
      })
    },
    clearNumber () {
      this.isNumber = false
    },
    // 实时监听
    inputFunc () {
      if (this.number) {
        if (this.number > this.checkData.length) {
          this.isNumber = true
          this.numberText = '输入数字不能超过背书组织个数'
        } else {
          this.isNumber = false
          this.numberText = ''
        }
      } else {
        this.isNumber = true
        this.numberText = '请输入生效条件'
      }

    }
  }
}
</script>
<style lang="less" scoped>
@import "../../../styles/common/modal.less";
@import "../../../styles/common/select.less";
.alert_box {
  .selectBox {
    padding-bottom: 38px !important;
  }
}
//标题导航~后期可以合并一下
.node-title {
  // font-size: 22px;
  font-size: 14px;
  color: #333;
  font-family: Microsoft YaHei;
  font-weight: 400;
  margin-top: 20px;
}

.node-title::before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 14px;
  margin-top: 3px;
  background: #337dff;
  margin-right: 6px;
  transform: translateY(2px);
}

/*// 重置下拉框*/
  /*.agreement-box .demo-form-inline /deep/ .el-form-item{*/
/*    display:flex;*/
/*    align-items: center;*/
/*    margin-bottom: 0;*/
/*}*/
/*.agreement-box /deep/ .el-form-item__label{*/
/*    font-size: 20px;*/
/*    font-weight: 400;*/
/*}*/
/*.agreement-box /deep/ .el-input--medium .el-input__inner{*/
/*    height: 46px;*/
/*}*/
.sel-size /deep/ .el-select .el-input__inner {
  width: 280px;
  height: 32px;
}
.form-item {
  display: flex;
  align-items: center;
  .el-form-item {
    flex: 1;
  }
}
.xing {
  font-size: 14px;
  color: #ff3a4c;
  font-weight: 400;
  margin: -4px 8px 0px 0px;
}
/deep/ .channel-name .el-form-item__label {
  font-weight: 400;
}

// el-row样式
.nodeBox {
  margin-top: 10px;
  /deep/ .el-form-item {
    margin-bottom: 10px;
  }
}

// 状态颜色区分
// 已初始化
.initText {
  color: #f2994a;
}

//已安装
.installText {
  color: #00ada2;
}
// 状态颜色区分
// 已初始化
.initText {
  color: #f2994a;
}
//已安装
.installText {
  color: #337dff;
}

// 操作按钮样式
/*.btn-group {*/
/*justify-content: center;*/
/*}*/
/*.btn-group div{*/
/*width: 102px;*/
/*padding:10px 0;*/
/*color:#fff;*/
/*border-radius: 4px;*/
/*margin-left: 8px;*/
/*cursor: pointer;*/
/*}*/
.installBtn {
  background: #00ada2;
}
.initBtn {
  background: #f7941d;
}
.callBtn {
  background: #1973cc;
}
.initBtnHui {
  background: #e4e4e4;
  cursor: default !important;
  // color:#666666;
}

/*.contract_box .box-item{*/
/*    margin-top: 40px;*/
/*}*/
/*.contract_box .tit{*/
/*    // margin-left: 76px;*/
/*    // margin-right: 42px;*/
/*    width:100PX;*/
/*    font-size: 18px;*/
/*    // font-weight: 700;*/
/*    color: #333333;*/
/*}*/
/*.contract_box .tit.tits-top {*/
/*    width:100PX;*/
/*    font-size: 18px;*/
/*    color: #333333;*/

/* }*/
/*.contract_box .titValue{*/
/*    font-size: 18px;*/
/*    font-family: Microsoft YaHei;*/
/*}*/
/*.contract_box .table_box{*/
/*    width: 420px;*/
/*    max-height: 320px;*/
/*    // overflow-y: auto;*/
/*    overflow: hidden;*/
/*    border: 2px solid #E7ECEF;*/
/*    border-radius: 4px;*/
/*}*/
/*.contract_box .table_box .tisName{*/
/*    text-align: center;*/
/*}*/
/*错误提示*/
.el-form-item {
  position: relative;
}
.hint {
  position: absolute;
  left: 0px;
  bottom: -26px;
  // font-size: 17px;
  font-size: 14px;
  color: #f04134;
  /*margin-top: 3px;*/
  /*margin-left: 100PX;*/
  // height: 20px;
}
.hint.inputhint {
  bottom: 0px;
  white-space: pre;
}
.hint.OrgsNum {
  left: 0%;
}
.start {
  // font-size: 40px;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #ff3a4c;
  // margin-left:20px;
  display: flex;
  align-items: center;
  position: relative;
  left: 20px;
}

// 初始化合约
.trem div {
  // font-size: 18px;
  font-size: 14px;
  color: #333333;
}

.contract_box {
  padding: 0 50px;
}

.contract_box .tremBox {
  display: flex;
  // align-items: center;
  margin-top: 10px;
}

// .contract_box .tremBox .tit{
//     margin-top:5px;
// }
.contract_box .trem_width {
  width: 420px;
}

.flexs {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.trem .flexs /deep/ .el-input--medium .el-input__inner {
  width: 200px;
  // height: 36px;
  border: 1px solid #e7ecef;
  border-radius: 4px;
}
.trem .flexs /deep/ .el-input {
  width: 200px;
  margin: 0 10px;
}
.flex_ {
  flex: 1;
}

.inputVal /deep/ .el-input {
  width: 420px;
  height: 56px;
}

.inputVal /deep/ .el-input--medium .el-input__inner {
  width: 420px;
  height: 56px;
  // font-size: 18px !important;
  font-size: 14px;
}

.trem .sample {
  color: #666666;
  // font-size: 18px;
  font-size: 14px;
  margin-top: 10px;
  width: 420px;
}
.lockCode {
  width: auto !important;
  text-align: center;
  position: absolute;
  right: -120px;
  top: -5px;
  line-height: 45px;
  color: @primary;
  cursor: pointer;
  // font-size:17px;
  font-size: 14px;
}

// 源码
.soundCode {
  padding: 62px 86px;
  // font-size: 18px;
  font-size: 14px;
  color: #333333;
  line-height: 40px;
  word-break: break-all;
}
// 分页
.pagination {
  // display: flex;
  // justify-content: flex-end;
  // align-items: center;
}
/deep/ .el-table::before {
  height: 0px;
}

/deep/ .el-table--scrollable-y .el-table__body-wrapper {
  overflow-y: auto;
}

/*/deep/ .el-table .el-table__body .cell div {*/
/*  text-align: left;*/
/*  // padding-left:7px;*/
/*  font-family: Microsoft YaHei;*/
/*}*/

/*/deep/ .el-table td div {*/
/*  font-size: 18px;*/
/*  // color: #333333;*/
/*}*/

.node-list-nav .nav-box /deep/ .el-col {
  color: #999;
}

.node-list-nav .nan-item .nav-box /deep/ .el-col {
  color: #666;
}

//样式重构
.node-list-nav .nan-item .nav-box {
  margin-top: 20px;
}

/*.btn-group div{*/
/*padding:0 0;*/
/*height: 36px;*/
/*line-height: 36px;*/
/*}*/

.el-table /deep/ th.gutter {
  background: #f2f7fa !important;
}

/deep/ .el-table-column--selection .cell {
  text-align: left !important;
}

// .el-select-dropdown__item.selected{
//     font-size: 18px !important;
// }
.sel-size /deep/ .el-input__inner {
  // font-size: 18px;
  font-size: 14px;
}

.sel-center /deep/ .el-table .cell div {
  text-align: center;
  padding-left: 0;
}

.starts {
  top: 5px;
}

.zzNode /deep/ .el-form-item {
  margin: 0;
}

.tit-top {
  margin-top: 13px;
}

.tits-top {
  margin-top: 16px;
}

.zwdata {
  color: #666666;
  // font-size: 18px;
  font-size: 14px;
  background: #fff;
  text-align: center;
  height: 56px;
  margin-top: 20px;
}
.zwdata /deep/ .el-col-24 {
  line-height: 56px;
}

/deep/ .el-form-item__error {
  // font-size: 16px;
  font-size: 14px;
}

.BoxLoading {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10000;
}

.BoxLoading /deep/ .el-loading-mask {
  background: rgba(0, 0, 0, 0.2) !important;
}

.BoxLoading /deep/ .el-loading-spinner .circular {
  width: 60px !important;
  height: 60px !important;
}

.BoxLoading /deep/ .el-loading-spinner .path {
  stroke: #fff !important;
}

.BoxLoading /deep/ .el-loading-spinner .el-loading-text {
  color: #fff !important;
  // font-size: 24px;
  font-size: 14px;
  font-weight: bold;
}

.inputValFlex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.inputValFlex /deep/ .el-input {
  width: 300px;
}

.inputValFlex /deep/ .el-input--medium .el-input__inner {
  width: 280px;
}

.grid-content {
  text-align: center;
}
.evertModule.input {
  flex-wrap: wrap;
  .userTipSpecial {
    margin-left: 30px;
  }
}
.inputwrap /deep/ .el-form-item__content {
  display: flex;
}
.addinput {
  width: 100%;
  margin-bottom: 30px;
}
.add,
.delete {
  width: 24px;
  height: 24px;
  border: 1px solid #dcdfe6;
  position: absolute;
  right: -30px;
  top: 4px;
  // margin-top: 6px;
  // margin-left: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  img {
    width: 14px;
    height: 14px;
  }
}
.delete {
  right: -60px;
}
.none {
  text-align: center;
  height: 56px;
  background: #fff;
  line-height: 56px;
  // font-size: 18px;
  font-size: 14px;
}
.ivu-page {
  margin-top: 10px;
}
/deep/ .select-down {
  margin-top: 4px !important;
  left: 0px !important;
}
/deep/ .el-input__icon {
  line-height: 32px;
}
/deep/ .el-icon-arrow-up {
  line-height: 32px;
}
.btn-row:not(.invoke-btn-wrap) .ivu-btn {
  height: auto;
  padding: 4px 15px;
}
</style>
