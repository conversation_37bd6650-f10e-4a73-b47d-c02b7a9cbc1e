<template>
  <Select :v-model="vModel" @on-change="onchange" :placeholder="placeholder" :style="styles">
    <Scroll :on-reach-bottom="handleReachBottom" :distance-to-edge="[8,8]" :height="190" :loading-text="finishFlag ? '已全部加载完成' : '加载中。。。。。。'">
      <Option v-for="item in list" :value="item.value" :key="item.key">{{ item.value }}</Option>
      <p class="p-finish" v-if="finishFlag">已全部加载完成</p>
      <!-- <p class="p-finish" style="font-size:8px;" v-else>下拉刷新
        <img style="margin-left:5px;" :src="imgUrl">
      </p> -->
    </Scroll>
  </Select>
</template>
<script>
export default {
  name: 'ScrollOption',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    finishFlag: {
      type: <PERSON><PERSON><PERSON>,
      default: () => false
    },
    styles: {
      type: String,
      default: () => ''
    },
    vModel: {
      type: String,
      default: () => ''
    },
    placeholder: {
      type: String,
      default: () => '请选择'
    }
  },
  data () {
    return {
      imgUrl: require('@/assets/img/arrow.png')
    }
  },
  methods: {
    handleReachBottom () {
      this.$emit('renderBottom', this.list)
    },
    onchange (value) {
      this.$emit('change', value)
    }
  }
}
</script>
<style lang="less" scoped>
.p-finish{
  text-align: center;
}
</style>
