<template>
  <div class="trade">
      <div style="padding:10px 0px 40px 10px;">
      <div style="float:right;">
          <Input
            prefix="ios-search"
              type="text"
              style="width:400px;margin-right:-1px;"
              v-model="searchData"
              placeholder="区块高度/交易哈希"
              @keyup.enter.native="browserBlur(searchData)">
          </Input>
          <Button type="primary" style="padding:2px;width:75px;height:31px;" @click="browserBlur(searchData)"> 搜索</Button>
      </div>
      </div>
      <div v-show="!isShow">
      <Row style="padding-top:10px;">
        <Col span="24">
          <Card class="back">
            <div class="title"><div class="bs"></div><div>交易信息</div></div>
            <p class="title-2">交易哈希:<span class="title-3">{{ txId }}</span></p>
            <p class="title-2">区块高度:<span class="title-1" style="color:#3D73EF;">{{ blockNum }}</span></p>
            <p class="title-2">时间:<span class="title-1">{{ timestamp }}</span></p>
          </Card>
        </Col>
      </Row>
      <Card style="margin-top:20px;">
        <div class="title"><div class="bs"></div><div>操作</div></div>
        <edit-table-mul :columns="columns" v-model="actionDetailData" :key="transferKey" style="margin:2px;"></edit-table-mul>
        <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;padding-top:10px;"/>
      </Card>
    </div>
    <div v-show="isShow" class="show-style">
        <img class="imgs" :src="showUrl">
        <p class="msg-style">{{showMsg}}</p>
    </div>
  </div>
</template>

<script>
import EditTableMul from '_c/edit-table-mul'
import JsonViewer from 'vue-json-viewer'
import { chainMakerTradeInfo } from '@/api/data'
import { isCmBlockNum, isTrxId } from '@/lib/check'
// import { mapActions } from 'vuex'
export default {
  name: 'chainMaker_trade',
  components: {
    EditTableMul,
    JsonViewer
  },
  data () {
    return {
      copyable: { copyText: '复制', copiedText: '已复制' },
      searchData: '',
      hisPath: 'chainMaker_index',
      transferKey: 0,
      txId: this.$route.query.txId || '',
      blockNum: 0,
      timestamp: '',
      columns: [
        {
          title: '交易时间',
          key: 'timestamp',
          minWidth: 80
        },
        {
          title: '执行智能合约',
          key: 'contractName'
        },
        {
          title: '合约方法',
          key: 'method'
        },
        {
          title: '交易结果',
          key: 'result',
          minWidth: 200,
          render: (h, params) => {
            if (params.row.result) {
              return h('div', [
                h(JsonViewer, {
                  props: {
                    value: params.row.result,
                    copyable: this.copyable,
                    expandDepth: 0
                  },
                  style: { background: 'transparent' }
                })
              ])
            }
          }
          // tooltip: true,
          // tooltipmaxwidth: 400
        }
      ],
      tablePageParam: {
        pagetotal: 0,
        pageSize: 10,
        pageIndex: 1
      },
      actionDetailData: [],
      isShow: false,
      showUrl: require('@/assets/img/null.png'),
      showMsg: ''
    }
  },
  computed: {
  },
  methods: {
    browserBlur (val) {
      if (isCmBlockNum(val)) {
        this.$router.push({
          name: 'chainMaker_block',
          query: {
            blockNum: val,
            tag: true
          }
        })
      } else if (isTrxId(val)) {
        // this.txId = val
        this.getTradeData(val)
      } else {
        if (val === '') {
          this.msgInfo('warning', '未输入任何查询信息，请检查！', true)
          this.showMsg = '未输入任何查询信息，请检查！'
        } else {
          this.msgInfo('warning', '输入信息有误，请检查！', true)
          this.showMsg = '输入信息有误，请检查！'
        }
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTradeData(this.txId)
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTradeData(this.txId)
    },
    getTradeData (value) {
      chainMakerTradeInfo(value, this.tablePageParam.pageIndex, this.tablePageParam.pageSize).then(res => {
        if (res.code === '00000') {
          this.isShow = false
          this.actionDetailData = res.data.methods ? res.data.methods : []
          this.txId = res.data.txId ? res.data.txId : '无记录'
          this.blockNum = res.data.blockHeight ? res.data.blockHeight : 0
          this.timestamp = res.data.timestamp ? res.data.timestamp : '无记录'
          this.tablePageParam.pagetotal = res.data.total
          ++this.transferKey
        } else {
          this.isShow = true
          this.msgInfo('error', res.message, true)
          this.showMsg = '查询交易ID[' + value + ']，' + res.message
        }
      }).catch(error => {
        this.isShow = true
        this.showMsg = '查询交易ID[' + value + ']，' + error.message
        this.msgInfo('error', error.message, true)
      })
    },
    reback () {
      this.$router.push({
        name: 'chainMaker_index'
      })
    }
  },
  mounted () {
    if (this.$route.query.txId) {
      if (this.$route.query.tag) {
        this.searchData = this.$route.query.txId
      }
      this.getTradeData(this.$route.query.txId)
    } else {
      this.reback()
    }
  },
  beforeRouteEnter (to, from, next) {
    if (from.name) {
      next(vm => {
        vm.hisPath = from.name
      })
    } else {
      next('/chainMaker_index')
    }
  }
}
</script>

<style lang="less" scoped>
.size{
  font-weight: bold;
  font-size:16px;
}
.trade{
  height:100%;
  .title{
    margin:5px 0 15px 10px;
    .size;
    height:18px;;
    font-family: 'Microsoft YaHei';
    line-height: 18px;
    color: #333333;
    vertical-align: middle;
  }
  .bs{
    float:left;
    width: 6px;
    height: 18px;
    background: #19C3A0;
    opacity: 1;
    border-radius: 3px;
    margin-right:6px
   }
  .title-2{
      font-size:14px;
       margin:10px 0 10px 10px;
       font-weight:300;
       color:#9B9B9B;
     }
  .title-3{
      margin-left:10px;
      font-size:1.5vw;
      font-weight:bold;
      color:#11173D;
      font-family: 'Microsoft YaHei';
      vertical-align: middle;
      }
  .title-1{
      font-size:14px;
      margin-left:10px;
      font-weight:300;
      color:#333333;
  }
  .back{
    background-image:url('../../assets/img/browser/trade.png');
    background-repeat:no-repeat;
    background-size:100% 100%;
  }
  .show-style{
  display: table;
  text-align: center;
  vertical-align: middle;
  margin:0 auto;
  position: relative;
  padding:8%;
  .msg-style{
    color:#b7b8b9;
    font-size:12px;
  }
}
}
/deep/.ivu-btn{
    border-radius: 0 4px 4px 0;
  }
/deep/.ivu-input{
     border-radius: 4px 0 0 4px;
  }
</style>
