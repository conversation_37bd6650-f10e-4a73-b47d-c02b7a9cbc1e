<!--
 交易记录
   Aturun
-->
<template>
    <div class="transaction_record">
      <div class="content-top">
        <div class="content-top-lift-title">消费者信息</div>
        <div class="content-top-right">
<!--              <el-select v-model="entityIn.consumerName" placeholder="请选择消费者用户" class="consumer_user-select">-->
<!--                <el-option label="区域一" value="shanghai"></el-option>-->
<!--                <el-option label="区域二" value="beijing"></el-option>-->
<!--              </el-select>-->
          <div class="top_text" style="width: 60px;">信源名称:</div>
              <el-select v-model="entityIn.providerId" clearable placeholder="请选择" @change="getBusinessRecordList(true)">
                <el-option :label="item.providerName" :value="item.providerId" v-for="(item,index) in OracleProviderList" :key="index"></el-option>
              </el-select>
          <div class="top_text" style="width: 100px;">数据生成时间:</div>
          <el-date-picker
              v-model="entityIn.startTime"
              type="datetime"
              placeholder="选择日期">
          </el-date-picker>
          <div class="top_text">到</div>
          <el-date-picker
              v-model="entityIn.endTime"
              type="datetime"
              placeholder="选择日期">
          </el-date-picker>
          <el-button type="primary" icon="el-icon-search" @click="getBusinessRecordList(true)">查 询</el-button>
        </div>

      </div>
      <div class="content-body">
        <el-table
            :data="tableData"
            style="width: 100%"
            height="520px"
            stripe>
          <el-table-column
              label="预言机名称"
              width="280">
            <template slot-scope="scope">
              {{scope.row.oracleName||'无订阅'}}
            </template>
          </el-table-column>
          <el-table-column
              prop="tempId"
              label="预言机模板"
              width="200">
          </el-table-column>
          <el-table-column
              prop="providerName"
              label="信源名称"
              width="200">
          </el-table-column>
          <el-table-column
              label="数据产生时间"
              width="220"
          >
            <template slot-scope="scope">
              {{setDates(scope.row.createTime)}}
            </template>
          </el-table-column>

          <el-table-column label="数据查看" width="280">
            <template slot-scope="scope">
              <el-button
                  size="mini"
                  @click="handleLook(scope.$index, scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="block table_pag">
          <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="entityIn.rows"
              background
              layout="total, prev, pager, next, sizes, jumper"
              :total="total">
          </el-pagination>
        </div>
      </div>
      <seeTransactionInfo ref="seeTransactionInfoRef"></seeTransactionInfo>
    </div>
</template>

<script>
import seeTransactionInfo from './components/seeTransactionInfo'
import * as api from "./api";
import {getFormatDates} from "../../utils/atuUtils";
export default {
  components:{
    seeTransactionInfo
  },
  data(){
    return {
      form:{
        region:''
      },
      currentPage: 1,
      input:'',
      tableData: [],
      total:null,
      entityIn:{
        "dataName": "",
        "endTime": "",
        "filter": {},
        "order": "",
        "page": 1,
        "rows": 10,
        "sort": "",
        "startTime": "",
        "tempId": "",
        "transStatus": 0,
        "providerId":""
      },
      OracleProviderEntityIn:{
        "endTime": "",
        "filter": {},
        "order": "",
        "page": 0,
        "providerName": "",
        "rows": 0,
        "sort": "",
        "startTime": ""
      },
      OracleProviderList:[]
    }
  },
  created(){
    this.getBusinessRecordList()
    this.getOracleProviderList()
  },
  mounted(){

  },
  methods: {
    //获取信源列表
    getOracleProviderList(){
      api.getOracleProviderList(
          this.OracleProviderEntityIn
      ).then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.OracleProviderList=res.result.rows

      })
    },
    setDates(val){
      return getFormatDates(val,'yyyy-mm-dd MM:mm:ss')
    },
    //获取数据用户列表
    getBusinessRecordList(search=false){
      if(this.entityIn.startTime&&this.entityIn.endTime){
        if(new Date(this.entityIn.startTime).getTime()>=new Date(this.entityIn.endTime).getTime()){
          return this.$message.warning('结束时间不能小于开始时间！')
        }
      }
      if(this.entityIn.startTime){
        this.entityIn.startTime = this.setDates(this.entityIn.startTime)
      }
      if(this.entityIn.endTime){
        this.entityIn.endTime = this.setDates(this.entityIn.endTime)
      }
      if(search){
        this.entityIn.page = 1
        this.entityIn.rows = 10
        this.currentPage = 1
        console.log(this.entityIn)
      }

      api.getBusinessRecordList(
          this.entityIn
      ).then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.tableData=res.result.rows
        this.total=res.result.total
      })
    },
    handleLook(index, row) {
      this.$refs.seeTransactionInfoRef.transId=row.transId
      this.$refs.seeTransactionInfoRef.Visible=true
    },
    handleSizeChange(val) {
      this.entityIn.rows =val
      this.getBusinessRecordList()
    },
    handleCurrentChange(val) {
      this.entityIn.page = val
      this.currentPage = val
      this.getBusinessRecordList()
    }
  },
}
</script>

<style lang="less" scoped>
.transaction_record{
  margin: 16px 14px;
  background: #ffffff;
  height: 706px;
  .content-top{
    //display: flex;
    //justify-content: space-between;
    .content-top-lift-title{
      padding-left: 30px;
      font-size: 18px;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }
    .content-top-right{
      display: flex;
      padding: 16px 20px 0 20px;
      min-width: 1000px;
      .top-right-input{
        margin-right: 12px;
      }
      .top-right-button{
        .el-button{

        }
      }
    }
    .content-top-lift{
      padding: 25px 0 0 23px;
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      color: #333333;
      opacity: 1;
    }
  }
  .content-body{
    margin: 11px 17px 0 16px;
  }
  .table_pag{
    margin: 12px 16px 0 0;
    display: flex;
    justify-content: flex-end;
  }
}
</style>



