<template>
  <div>
     <Modal
      v-model="detailModal"
      title="合约共享详情"
      :draggable="true"
      sticky
      :mask-closable="false"
      style="background-color:#fff"
      :footer-hide="true"
      :width="720"
      >
        <div style="padding:10px 10px 40px 10px;">
           <p>请选择要查看的合约链账户</p>
           <Select placeholder="请选择要查看的合约链账户" v-model="shareChainAccount" clearable style="width:100%;padding:10px 0 20px 0;" @on-change="changeShareAccountName(shareChainAccount)">
             <Option v-for="item in shareAccountList" :value="item.contractAccountId" :key="item.contractAccountId">{{ `${item.contractAccountName + '（合约版本：' + item.uploadVersion + '，链名称：' + item.chainName + '）'}`}}</Option>
           </Select>
           <div style="padding:10px 0 5px 0;">
            <div style="float:left;">
              共享租户
            </div>
            <div style="float:right;">
              <Input placeholder="请输入租户名称" style="width:300px;" v-model="query" suffix="ios-search" @keyup.enter.native="search">
                <Icon type="ios-search" slot="suffix" @click="search"/>
              </Input>
            </div>
           </div>
        </div>
        <EditTableMul :key="transferKey" :columns="tenantColumns" v-model="tenantTableData"></EditTableMul>
        <Page :total="sharePageParam.pagetotal" :current.sync="sharePageParam.pageIndex" @on-change="sharePageChange" :page-size="sharePageParam.pageSize" :page-size-opts="[5,10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="sharePageSizeChange" style="text-align:right;padding-top:10px;"/>
     </Modal>
     <Modal
      v-model="shareModal"
      :title="contractName"
      :draggable="true"
      sticky
      :mask-closable="false"
      style="background-color:#fff"
      :width="620">
        <div style="padding:10px 10px 40px 10px;">
          <Form ref="formItem1" label-position="top" :model="formItem1">
          <FormItem label="请选择要共享的合约链账户" prop="chainAccountId">
           <Select placeholder="请选择要共享的合约链账户" v-model="formItem1.chainAccountId" clearable style="width:100%;padding:10px 0 20px 0;" @on-change="changeAccountName(formItem1.chainAccountId)">
             <Option v-for="item in accountList" :value="item.contractAccountId" :key="item.contractAccountId">{{ `${item.contractAccountName + '（合约版本：' + item.contractVersion + '，链名称：' + item.chainName + '）'}`}}</Option>
           </Select>
           </FormItem>
          </Form>
           <div style="padding:10px 0 5px 0;">
            <div style="float:left;">
              请选择要共享的租户
            </div>
            <div style="float:right;">
              <Input placeholder="请输入租户名称" style="width:300px;" v-model="queryNoShare" suffix="ios-search" @keyup.enter.native="searchNoshare">
                <Icon type="ios-search" slot="suffix" @click="searchNoshare"/>
              </Input>
            </div>
           </div>
        </div>
        <div style="padding:10px;">
          <Table :columns="selectColums" :data="notSelectTableData" stripe :key="transferKey" ref="selection" :show-header="true" @on-select="onSelect" @on-select-all="onSelectAll" @on-select-cancel="onSelectCancel" @on-select-all-cancel="onSelectAllCancel" max-height="300"></Table>
          <Page :total="noSelectPageParam.pagetotal" :current.sync="noSelectPageParam.pageIndex" @on-change="noSelectPageChange" :page-size="noSelectPageParam.pageSize" :page-size-opts="[5,10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="noSelectPageSizeChange" style="text-align:right;padding-top:10px;"/>
        </div>
        <div slot="footer">
          <div v-show="selectRecords.length > 0" style="padding:8px 0 10px 10px;float:left;">
            <span>选择了{{selectRecords.length}}个租户</span>
          </div>
        <Button type="text"  @click="cancel('formItem')">取消</Button>
        <Button type="primary" @click="ok('formItem')">确定</Button>
      </div>
     </Modal>
  </div>
</template>

<script>
import EditTableMul from '_c/edit-table-mul'
import { unshareChainAccountList, shareAccountContractList, findSharedContractTenantList, shareableTenantList, sharingContract } from '@/api/data'
export default {
  name: 'modalCommon',
  components: {
    EditTableMul
  },
  data () {
    return {
      msgModal: true,
      contractId: 0,
      contractName: '',
      detailModal: false,
      shareModal: false,
      shareChainAccount: 0,
      shareAccountList: [],
      formItem1: {
        chainAccountId: 0
      },
      selectItem: {},
      shareSelectItem: {},
      accountList: [],
      query: '',
      queryNoShare: '',
      transferKey: 0,
      tenantColumns: [
        { key: 'tenantName', title: '租户名称', tooltip: true },
        { key: 'tenantBrief', title: '租户描述', tooltip: true },
        { key: 'createTime', title: '配置时间', tooltip: true }
      ],
      tenantTableData: [],
      selectColums: [
        {
          type: 'selection',
          width: 35,
          align: 'center'
        },
        { key: 'tenantName', title: '租户名称', minWidth: 100 },
        { key: 'tenantBrief', title: '租户描述', minWidth: 300, tooltip: true }
      ],
      notSelectTableData: [],
      noSelectPageParam: {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      },
      sharePageParam: {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      },
      formItemRule: {
        chainAccountId: [{ required: true, message: '不能为空', trigger: 'change' }]
      },
      selectRecords: []
    }
  },
  methods: {
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content }) },
    search () {
      this.getSharedContractTenantList(this.shareSelectItem)
    },
    searchNoshare (queryNoShare) {
      this.getShareableTenantList(this.selectItem, 'search')
    },
    sharePageChange (index) {
      this.sharePageParam.pageIndex = index
      this.getSharedContractTenantList(this.shareSelectItem)
    },
    sharePageSizeChange (index) {
      this.sharePageParam.pageSize = index
      this.getSharedContractTenantList(this.shareSelectItem)
    },
    noSelectPageChange (index) {
      this.noSelectPageParam.pageIndex = index
      this.getShareableTenantList(this.selectItem)
    },
    noSelectPageSizeChange (index) {
      this.noSelectPageParam.pageSize = index
      this.getShareableTenantList(this.selectItem)
    },
    openDetail (contractName, contractId) {
      // console.log('val', contractName, contractId)
      this.detailModal = true
      this.contractName = '共享合约' + contractName
      this.contractId = contractId
      this.getShareContractList(contractId)
    },
    openShare (contractName, contractId) {
      // console.log('openShare:', this.shareModal, contractId)
      this.shareModal = true
      this.contractName = '共享合约' + contractName
      this.contractId = contractId
      this.getUnshareContractList(contractId)
    },
    init () {
      this.selectRecords = []
      this.formItem1.chainAccountId = 0
      this.notSelectTableData = []
      this.accountList = []
      this.noSelectPageParam = {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      }
      this.selectItem = {}
      this.queryNoShare = ''
    },
    detailInit () {
      this.shareChainAccount = 0
      this.sharePageParam = {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      }
      this.shareAccountList = []
      this.tenantTableData = []
      this.shareSelectItem = {}
      this.query = ''
    },
    handleSelectAll (status) {
      this.$refs.selection.selectAll(status)
      // console.log('handleSelectAll', status)
    },
    onSelectAll (selection) {
      // console.log('onSelectAll:', selection, this.selectRecords)
      selection.forEach(item => {
        this.selectRecords.push(item)
      })
      let obj = {}
      this.selectRecords = this.selectRecords.reduce((cur, next) => {
        (obj[next.tenantId] ? '' : obj[next.tenantId] = true) && cur.push(next)
        return cur
      }, [])
      /// console.log('onSelectAll--this.selectRecords:', this.selectRecords)
    },
    onSelectAllCancel (selection) {
      // console.log('onSelectAllCancel:', selection)
      this.notSelectTableData.forEach(item => {
        this.selectRecords.forEach((e, index) => {
          if (item.tenantId === e.tenantId) {
            this.selectRecords.splice(index, 1)
          }
        })
      })
    },
    onSelect (selection, row) {
      // console.log('onSelect--row:', selection, row)
      this.selectRecords.push(row)
      this.notSelectTableData.forEach(item => {
        if (item.tenantId === row.tenantId) {
          item['_checked'] = true
        }
      })
    },
    onSelectCancel (selection, row) {
      // console.log('onSelectCancel:', selection, row)
      this.selectRecords.forEach((item, index) => {
        if (row.tenantId === item.tenantId) {
          this.selectRecords.splice(index, 1)
        }
      })
      // console.log('this.selectRecords:', this.selectRecords)
    },
    getUnshareContractList (contractId) {
      unshareChainAccountList(contractId).then(res => {
        if (res.code === '00000') {
          this.accountList = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        // console.log('unshareChainAccountList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询已共享的合约的链账户列表
    getShareContractList (contractId) {
      let params = {}
      params.contractId = contractId
      shareAccountContractList(params).then(res => {
        if (res.code === '00000') {
          this.shareAccountList = res.data
          if (this.shareAccountList[0]) {
            this.shareChainAccount = this.shareAccountList[0].contractAccountId
            this.changeShareAccountName(this.shareChainAccount)
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        // console.log('shareAccountContractList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    // // 查询共享合约的可见租户列表
    getSharedContractTenantList (item) {
      // console.log('item:', item)
      if (JSON.stringify(item) === '{}') {
        this.msgInfo('error', '请先选择要查看的合约链账户', true)
        return
      }
      let params = {}
      params.shareRecordId = item.shareId
      params.pageParam = this.sharePageParam
      params.tenantName = this.query
      findSharedContractTenantList(params).then(res => {
        if (res.code === '00000') {
          this.tenantTableData = res.data.records
          this.sharePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        // console.log('shareAccountContractList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询合约的可共享租户列表
    getShareableTenantList (item, flag) {
      if (JSON.stringify(item) === '{}') {
        this.msgInfo('error', '请先选择要共享的合约链账户', true)
        return
      }
      let params = {}
      params.chainId = item.chainId
      params.contractAccountId = item.contractAccountId
      params.uploadVersion = item.contractVersion
      params.pageParam = this.noSelectPageParam
      params.tenantName = this.queryNoShare
      shareableTenantList(params).then(res => {
        if (res.code === '00000') {
          this.notSelectTableData = res.data.records
          this.handleTableChecked(this.notSelectTableData)
          this.noSelectPageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        // console.log('shareAccountContractList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    changeAccountName (id) {
      // console.log('id:', id)
      if (id) {
        this.selectItem = this.findId(id, this.accountList)
        this.noSelectPageParam = {
          pagetotal: 0,
          pageSize: 5,
          pageIndex: 1
        }
        this.getShareableTenantList(this.selectItem)
      }
    },
    findId (id, data) {
      let item = {}
      for (let i in data) {
        if (data[i].contractAccountId === id) {
          item = data[i]
        }
      }
      return item
    },
    // 发起共享合约
    postSharingContract () {
      let params = {}
      params.contractId = this.contractId
      params.chainId = this.selectItem.chainId
      params.contractAccountId = this.selectItem.contractAccountId
      params.shareTenantList = this.transferTenant(this.selectRecords)
      params.uploadVersion = this.selectItem.contractVersion
      sharingContract(params).then(res => {
        if (res.code === '00000') {
          this.$Modal.success({
            content: res.message
          })
          this.shareModal = false
          // 需要刷新详情
          this.$emit('getDetails')
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        // console.log('shareAccountContractList.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    ok () {
      if (this.selectRecords.length > 0) {
        this.postSharingContract()
      } else {
        this.msgInfo('warning', '请选择要共享的租户', true)
      }
    },
    cancel () {
      this.shareModal = false
    },
    handleTableChecked (datas) {
      // console.log('datas:', datas)
      this.selectRecords.forEach(item => { // 判断本页数据状态
        datas.forEach(e => {
          if (item.tenantId === e.tenantId) {
            e['_checked'] = true
          }
        })
      })
    },
    transferTenant (data) {
      let obj = []
      data.forEach(item => {
        let params = {}
        params.tenantId = item.tenantId
        params.tenantName = item.tenantName
        obj.push(params)
      })
      return obj
    },
    changeShareAccountName (val) {
      // console.log('val===', val, this.findId(val, this.shareAccountList))
      if (val) {
        this.shareSelectItem = this.findId(val, this.shareAccountList)
        this.getSharedContractTenantList(this.shareSelectItem)
      }
    }
  },
  mounted () {
    // console.log('this.detailModal:', this.detailModal)
  },
  watch: {
    shareModal: {
      handler (newVal, oldVal) {
        if (!newVal) {
          this.init()
        }
      },
      deep: true,
      immediate: false
    },
    detailModal: {
      handler (newVal, oldVal) {
        if (!newVal) {
          this.detailInit()
        }
      },
      deep: true,
      immediate: false
    }
  }
}
</script>

<style lang="less" scoped>

</style>
