<template>
  <div class="home"> </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'home',
  components: { },
  data () {
    return {
      //
    }
  },
  props: { },
  computed: {
    ...mapState({
      rules: state => state.user.rules
    })
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      // console.log(vm)
    })
  },
  beforeRouteLeave (to, from, next) {
    // const leave = confirm('您确定要离开吗？')
    // if (leave) next()
    // else next(false)
    next()
  },
  methods: {
    ...mapActions([
      'logout'
    ]),
    handleLogout () {
      this.logout()
      this.$router.push({
        name: 'login'
      })
    }
  }
}
</script>
<style lang="less" scoped>

</style>
