import axios from 'axios'
import { baseURL } from '@/config'
import { localRead, generateSessionId } from '@/lib/util'
// import store from '@/store'
class HttpRequest {
  constructor() {
    this.queue = {}
  }
  getInsideConfig () {
    if (baseURL && baseURL !== '/') { localStorage.setItem('baseURL', baseURL) }
    // console.log(baseURL, localStorage.getItem('baseURL'))
    return {
      baseURL: localStorage.getItem('baseURL') || baseURL,
      headers: {}
    }
  }
  distroy (url) {
    delete this.queue[url]
    if (!Object.keys(this.queue).length) {
      // Spin.hide()
    }
  }
  interceptors (instance, url) {
    instance.interceptors.request.use(config => {
      // 添加全局的loading...
      if (!Object.keys(this.queue).length) {
        // Spin.show()
      }
      this.queue[url] = true
      // config.headers['Authorization'] = getToken()
      config.headers['Authorization'] = 'Bearer ' + localRead('token')
      config.headers['Sessionid'] = ''
      config.headers['X-TRACE-ID'] = generateSessionId()

      // config.headers['Token'] = '123456'
      // sessionStorage.setItem('modalAbout', true)
      return config
    }, error => {
      return Promise.reject(error)
    })
    instance.interceptors.response.use(res => {
      this.distroy(url)
      const { data } = res
      sessionStorage.setItem('sessionid', res.headers.sessionid)
      // store.dispatch('modalAbout', false)// 初始化数据
      return data
    }, error => {
      this.distroy(url)
      console.log(error);
      // if (error.response.data.code === "A0314") {
      // store.dispatch('modalAbout', true)// 初始化数据
      // }
      return Promise.reject(error.response.data)
    })
  }
  request (options) {
    const instance = axios.create()
    options = Object.assign(this.getInsideConfig(), options)
    this.interceptors(instance, options.url)
    return instance(options)
  }
}
export default HttpRequest
