<template>
  <div>
    <div class="register">
      <h1>
        <img :src="logoUrl" /><br />
        <!-- <b>CMBaaS</b> -->
        <span style="font-size: 18px">中移动CMBaaS —— 同心共筑，"链"接未来</span>
      </h1>
      <card style="width: 620px; padding-left: 5px">
        <div style="margin-left: 8px">
          <div style="margin: 5px 0 25px 0">
            <span style="font-size: 20px; font-weight: bold">注册</span>
          </div>
          <Form ref="formData" :model="formData" :rules="formDataRule" inline>
            <FormItem prop="userLoginId">
              <Input prefix="ios-contact" placeholder="账号" style="width: 260px;margin-right:30px;" v-model="formData.userLoginId" />
            </FormItem>
            <!-- </Form>
       <Form ref="formData" :model="formData" :rules="formDataRule" inline> -->
            <FormItem prop="realName">
              <Input prefix="ios-person" placeholder="真实姓名" style="width: 260px" v-model="formData.realName" />
            </FormItem>
            <FormItem prop="organization">
              <Input prefix="ios-link" placeholder="所属组织" style="width: 260px;margin-right:30px;" v-model="formData.organization" />
            </FormItem>
            <FormItem prop="phoneNumber">
              <Input prefix="ios-call" placeholder="手机号码" style="width: 260px" v-model="formData.phoneNumber" />
            </FormItem>

            <!-- </Form>
      <Form ref="formData" :model="formData" :rules="formDataRule" inline> -->
            <FormItem prop="email">
              <Input prefix="md-mail" placeholder="邮箱地址" style="width: 260px;margin-right:30px;" v-model="formData.email">
              </Input>
            </FormItem>
            <FormItem prop="emailCode">
              <div style="display: flex; align-items: center">
                <Input prefix="md-checkmark-circle" type="text" style="width: 167px" v-model="formData.emailCode" placeholder="请输入验证码">
                </Input>
                <Button type="primary" style="
                    margin-left: 8px;
                    padding: 2px;
                    width: 86px;
                    height: 30px;
                  " :disabled="!!cooling" @click="getCaptcha">
                  {{
                    cooling ? "重新发送(" + cooling + ")" : " 发送验证码 "
                  }}</Button>
              </div>
            </FormItem>
            <!-- </Form>
      <Form ref="formData" :model="formData" :rules="formDataRule" inline> -->
            <FormItem v-if="flag" prop="password">
              <Input type="password" prefix="ios-key" placeholder="密码" style="width: 260px;margin-right:30px;" v-model="formData.password">
              <i class="ri-eye-close-line" slot="suffix" @click="handlePassword"></i>
              </Input>
            </FormItem>
            <FormItem v-else prop="password">
              <Input type="text" prefix="ios-key" placeholder="密码" style="width: 260px;margin-right:30px;" v-model="formData.password">
              <i class="ri-eye-line" slot="suffix" @click="handlePassword"></i>
              </Input>
            </FormItem>
            <FormItem v-if="flag" prop="rePassword">
              <Input type="password" prefix="ios-key-outline" placeholder="确认密码" style="width: 260px;" v-model="formData.rePassword">
              <i class="ri-eye-close-line" slot="suffix" @click="handlePassword"></i>
              </Input>
            </FormItem>
            <FormItem v-else prop="rePassword">
              <Input type="text" prefix="ios-key-outline" placeholder="确认密码" style="width: 260px;" v-model="formData.rePassword">
              <i class="ri-eye-line" slot="suffix" @click="handlePassword"></i>
              </Input>
            </FormItem>
          </Form>
          <Form>
            <FormItem style="margin-top: 20px">
              <Button type="primary" @click="handleSubmit" style="width: 560px">注册</Button>
              <p style="padding: 10px 12px 0 0; text-align: right">
                <router-link to="/login">已有账号？马上登录</router-link>
              </p>
            </FormItem>
          </Form>
        </div>
      </card>
    </div>
    <canvas class="cash" id="" :style="
        'background:url(' + images + ') no-repeat;background-size:100% 100%;'
      "></canvas>
    <div class="footer-copyright">
      <p style="margin-left:30%">{{ versionData.version  }}</p>
      <span>{{ versionData.copyright }}</span>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
// import { starsNest } from '@/lib/starts'
import { newDate } from '../../static/config.json'
import {
  isPhoneNumber,
  isEmail,
  isLoginId,
  isRealName,
  isPassword
} from '../lib/check'
import {
  checkEmail,
  checkPhone,
  checkUserLoginId,
  requestVerificationCode,
  checkVerificationCode
} from '@/api/data'
import { encryptedData, decryptData } from '@/lib/encrypt'
import { getconfig } from '@/api/contract'

export default {
  name: 'register',
  data () {
    const validatePhone = (rule, value, callback) => {
      if (!isPhoneNumber(value)) {
        callback(new Error('手机号格式不支持'))
      } else {
        checkPhone(value)
          .then((res) => {
            if (res.code === '00000') {
              callback()
            } else {
              callback(new Error(res.message))
            }
          })
          .catch((error) => {
            callback(new Error(error.message))
          })
      }
    }
    const validateEmail = (rule, value, callback) => {
      if (!isEmail(value)) {
        callback(new Error('邮箱格式不正确'))
      } else {
        // checkEmail(value)
        //   .then((res) => {
        //     if (res.code === '00000') {
        //       callback()
        //     } else {
        //       callback(new Error(res.message))
        //     }
        //   })
        //   .catch((error) => {
        //     // this.msgInfo('error', error.message, true)
        //     callback(new Error(error.message))
        //   })
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.formData.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    const validateUserLoginId = (rule, value, callback) => {
      if (!isLoginId(value)) {
        callback(new Error('请输入英文、数字、下划线'))
      } else {
        checkUserLoginId(value)
          .then((res) => {
            if (res.code === '00000') {
              callback()
            } else {
              callback(new Error(res.message))
            }
          })
          .catch((error) => {
            // this.msgInfo('error', error.message, true)
            callback(new Error(error.message))
          })
      }
    }
    const validateEmailCode = (rule, value, callback) => {
      if (!/^\d{6}$/.test(value)) {
        callback(new Error('请输入6位数字'))
      } else {
        callback()
      }
    }
    const validateRealName = (rule, value, callback) => {
      if (!isRealName(value)) {
        callback(new Error('只允许中英文、数字， 且不能为纯数字'))
      } else {
        callback()
      }
    }
    const validatePasswordRule = (rule, value, callback) => {
      if (!isPassword(value)) {
        callback(
          new Error(
            '至少包含一位大小写字母、数字，特殊字符只能包含!#$%且至少一位'
          )
        )
      } else {
        callback()
      }
    }
    return {
      flag: true,
      reflag: true,
      cooling: 0,
      captcha: '',
      // emailPrefix: '@chinamobile.com',
      formData: {
        organization: '',
        email: '',
        realName: '',
        emailCode: '',
        userLoginId: '',
        password: '',
        phoneNumber: '',
        rePassword: ''
      },
      formDataRule: {
        realName: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            required: true,
            max: 15,
            message: '长度不能超过15',
            trigger: 'blur'
          },
          { required: true, trigger: 'blur', validator: validateRealName }
        ],
        email: [{ required: true, trigger: 'blur', validator: validateEmail }],
        password: [
          {
            required: true,
            min: 8,
            message: '密码不能少于8位',
            trigger: 'blur'
          },
          { max: 20, message: '密码不能多于20位', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validatePasswordRule }
        ],
        rePassword: [
          {
            required: true,
            min: 8,
            message: '密码不能少于8位',
            trigger: 'blur'
          },
          { max: 20, message: '密码不能多于20位', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validatePassword }
        ],
        phoneNumber: [
          {
            required: true,
            pattern: /^[0-9]{11}$/,
            message: '手机号应为11位数字',
            trigger: 'blur'
          },
          { required: true, trigger: 'blur', validator: validatePhone }
        ],
        userLoginId: [
          { required: true, message: '不能为空', trigger: 'blur' },
          {
            required: true,
            max: 15,
            message: '长度不能超过15',
            trigger: 'blur'
          },
          { required: true, trigger: 'blur', validator: validateUserLoginId }
        ],
        emailCode: [
          { required: true, message: '不能为空', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: validateEmailCode }
        ]
      },
      images: require('@/assets/img/bg1.png'),
      newDate: '',
      versionData: '',
      logoUrl: ''
    }
  },
  created () {
    let name = 'CONNECT_4A_OPEN'
    getconfig(name).then((res) => {
      console.log(res)
      localStorage.setItem('CONNECT_4A_OPEN', res.data.value)
    })
  },
  mounted () {
    this.versionData = JSON.parse(sessionStorage.getItem('versionData'))
    this.newDate = newDate
    // starsNest()
    this.$nextTick(() => {
      this.$refs['formData'].resetFields()
    })
    this.init()
    // this.$Message.config({
    //   top: 270,
    //   duration: 3
    // })
    this.getpublicKey()
    const storedVersionData = JSON.parse(sessionStorage.getItem('versionData')) || {};
    this.versionData = {
      version: storedVersionData.version || '产品版本：1.5.0',
      copyright: storedVersionData.copyright || 'Copyright © 2022 中移信息技术有限公司',

    };
    this.logoUrl = storedVersionData.logoUrl || '/data/iconLogin.png';
  },
  methods: {
    ...mapActions(['register', 'checkUserLoginId']),
    getpublicKey () {
      let name = 'RSA_PUBLIC_KEY'
      getconfig(name).then((res) => {
        this.savepublicKey(decryptData(res.data.value))
      })
    },
    // 保存系统公钥
    savepublicKey (val) {
      this.$store.commit('SAVE_PUBLICKEY', val)

      // console.log("公钥",this.$store.state.publicKey)
    },
    init () {
      this.formData = {
        organization: '',
        email: '',
        realName: '',
        emailCode: '',
        userLoginId: '',
        password: '',
        phoneNumber: '',
        rePassword: ''
      }
    },
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    handleSubmit () {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          //  console.log(this.formData.password,"1111")
          let params = {
            organization: this.formData.organization,
            realName: this.formData.realName,
            userLoginId: this.formData.userLoginId,
            password: encryptedData(
              this.formData.password,
              this.$store.state.publicKey
            ),
            phoneNumber: this.formData.phoneNumber,
            email: this.formData.email
          }
          //   console.log(params.password,"222222")
          if (this.formData.emailCode) {
            checkVerificationCode(
              null,
              this.formData.email,
              null,
              this.formData.emailCode,
              'USER_REGISTRATION'
            )
              .then((res) => {
                if (res.code === '00000') {
                  // this.msgInfo('success', res.message, true)
                  this.register(params)
                    .then((res) => {
                      if (res.code === '00000') {
                        this.msgInfo(
                          'success',
                          '用户名[' + this.formData.userLoginId + ']注册成功',
                          true
                        )
                        this.$router.push({
                          name: 'login'
                        })
                      } else {
                        this.msgInfo(
                          'error',
                          '用户名[' +
                          this.formData.userLoginId +
                          ']注册失败,原因：' +
                          res.message +
                          '请重试！！',
                          true
                        )
                      }
                    })
                    .catch((error) => {
                      console.log(error)
                      alert(error.message)
                    })
                } else {
                  this.msgInfo('warning', res.message, true)
                }
              })
              .catch((error) => {
                this.msgInfo('error', error.message, true)
              })
          } else {
            this.msgInfo('error', '请输入验证码', true)
          }
        } else {
          this.msgInfo('error', '有字段校验不通过,请检查', true)
        }
      })
    },
    handlePassword () {
      this.flag = !this.flag
    },
    handleRePassword () {
      this.reflag = !this.reflag
    },
    getCaptcha () {
      if (this.formData.email) {
        requestVerificationCode(
          this.formData.userLoginId,
          this.formData.email,
          null,
          'USER_REGISTRATION'
        )
          .then((res) => {
            if (res.code === '00000') {
              this.msgInfo('success', res.message, true)
              this.cooling = 60
              let coolingInter = setInterval(() => {
                if (this.cooling > 0) {
                  this.cooling--
                } else {
                  clearInterval(coolingInter)
                  coolingInter = null
                }
              }, 1000)
            } else {
              this.msgInfo('error', res.message, true)
            }
          })
          .catch((error) => {
            this.msgInfo('error', error.message, true)
          })
      } else {
        this.msgInfo('error', '请先输入邮箱验证', true)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.cash {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000c17;
}
div.register {
  display: grid;
  position: absolute;
  left: 50%;
  top: 46%;
  transform: translate(-50%, -50%);
  z-index: 100;
  opacity: 0.8;
  h1 {
    margin: 10px;
    b {
      text-align: center;
      margin: 0 10px;
      color: #fff;
    }
    span {
      font-size: small;
      color: #ccc;
    }
  }
  input {
    //margin-bottom: 10px;
    opacity: 0.9;
  }
  /deep/.ivu-card-body {
    padding: 10px 16px 0 16px;
  }
}
</style>
