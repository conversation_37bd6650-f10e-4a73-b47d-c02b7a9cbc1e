<!--
 异常日志
   Aturun
-->
<template>
    <div class="exception_log">
      <div class="content-top">
        <div class="content-top-lift-title">
          异常日志
        </div>
        <div class="content-top-right">
          <div class="top_text" style="width: 80px;">预言机名称:</div>
          <el-select v-model="entityIn.oracleId" clearable placeholder="请选择" @change="getExceptionLogList(true)">
            <el-option :label="item.oracleName" :value="item.oracleId" v-for="(item,index) in MachineTempList" :key="index"></el-option>
          </el-select>
          <div class="top_text" style="width: 100px;">数据生成时间:</div>
          <el-date-picker
              v-model="entityIn.startTime"
              type="datetime"
              placeholder="选择日期">
          </el-date-picker>
          <div class="top_text">到</div>
          <el-date-picker
              v-model="entityIn.endTime"
              type="datetime"
              placeholder="选择日期">
          </el-date-picker>
          <el-button type="primary" icon="el-icon-search"  @click="getExceptionLogList(true)">查 询</el-button>
        </div>

      </div>
      <div class="content-body">
        <el-table
            :data="tableData"
            style="width: 100%"
            height="520px"
            stripe
        >
          <el-table-column
              prop="oracleName"
              width="180"
              label="预言机名称">
            <template slot-scope="scope">
              {{scope.row.oracleName||'无订阅'}}
            </template>
          </el-table-column>
          <el-table-column
              width="180"
              prop="consumerName"
              label="消费者用户名称">
          </el-table-column>
          <el-table-column
              prop="providerName"
              label="信源名称"
              width="180">
          </el-table-column>
          <el-table-column
              label="数据产生时间"
              width="280">
            <template slot-scope="scope">
              {{setDates(scope.row.createTime)}}
            </template>
          </el-table-column>
          <el-table-column label="数据查看"  width="280">
            <template slot-scope="scope">
              <el-button
                  size="mini"
                  @click="handleLook(scope.$index, scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="block table_pag">
          <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="entityIn.rows"
              background
              layout="total, prev, pager, next, sizes, jumper"
              :total="total">
          </el-pagination>
        </div>
      </div>
      <seeExceptionLog ref="seeExceptionLogRef"></seeExceptionLog>
    </div>
</template>

<script>
import seeExceptionLog from './components/seeExceptionLog'
import * as api from "./api";
import {getFormatDates} from "../../utils/atuUtils";
export default {
  components:{
    seeExceptionLog
  },
  data(){
    return {
      value1:null,
      form:{
        region:''
      },
      MachineTempList:[],
      currentPage: 1,
      input:'',
      tableData: [],
      total:null,
      entityIn:{
        "endTime": "",
        "filter": {},
        "order": "",
        "page": 1,
        "providerName": "",
        "rows": 10,
        "sort": "",
        "startTime": "",
        oracleName:"",
        oracleId:""
      },
    }
  },
  created(){
    this.getExceptionLogList()
    // 预言机名称列表
    this.getOracleNameList()
    // console.log(new Date().getTime())
  },
  mounted(){

  },
  methods: {
    //获取数据用户列表
    getOracleNameList(){
      api.getOracleNameList().then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.MachineTempList=res.result
      })
    },
    setDates(val){
      return getFormatDates(val,'yyyy-mm-dd MM:mm:ss')
    },
    //获取数据用户列表
    getExceptionLogList(search=false){
      if(this.entityIn.startTime&&this.entityIn.endTime){
        if(new Date(this.entityIn.startTime).getTime()>=new Date(this.entityIn.endTime).getTime()){
          return this.$message.warning('结束时间不能小于开始时间！')
        }
      }
      if(this.entityIn.startTime){
        this.entityIn.startTime = this.setDates(this.entityIn.startTime)
      }
      if(this.entityIn.endTime){
        this.entityIn.endTime = this.setDates(this.entityIn.endTime)
      }
      if(search){
        this.entityIn.page=1
        this.currentPage = 1
      }
      api.getExceptionLogList(
          this.entityIn
      ).then(res=>{
        if(res.code!=0) return this.$message.warning(res.msg)
        this.tableData=res.result.rows
        this.total=res.result.total
      })
    },
    handleLook(index, row) {
      this.$refs.seeExceptionLogRef.form=row
      this.$refs.seeExceptionLogRef.Visible=true
    },
    handleSizeChange(val) {
      this.entityIn.rows =val
      this.getExceptionLogList()
    },
    handleCurrentChange(val) {
      this.entityIn.page = val
      this.currentPage =val
      this.getExceptionLogList()
    }
  },
}
</script>

<style lang="less" scoped>
.exception_log{
  margin: 16px 14px;
  background: #ffffff;
  height: 706px;
  .content-top{
    //display: flex;
    //justify-content: space-between;
    .content-top-lift-title{
      padding-left: 30px;
      font-size: 18px;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
    }
    .content-top-right{
      display: flex;
      padding: 20px 20px 0 20px;
      min-width: 1000px;
      .top-right-input{
        margin-right: 12px;
      }
      .top-right-button{
        .el-button{

        }
      }
    }
    .content-top-lift{
      padding: 20px 0 0 23px;
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      color: #333333;
      opacity: 1;
    }
  }
  .content-body{
    margin: 11px 17px 0 16px;
  }
  .table_pag{
    margin: 12px 16px 0 0;
    display: flex;
    justify-content: flex-end;
  }
}
</style>



