<template>
  <div class="page">
    <!-- <div class="changeChain">
      <span class="title">创建区块链</span>
      <span class="arrow el-icon-arrow-right"></span>
      <span class="name">{{chainName}}</span>
    </div>
      <div class="header">
       <ol class="steps">
          <li class="step-active active">
            <div class="step-line active-line"></div>
            <div class="step-content">
                <span class="step-num">1</span>
                <div class="step-text">选择建链方式</div>
            </div>
          </li>
          <li class="step-active active">
            <div class="step-line"></div>
            <div class="step-content">
                <span class="step-num">2</span>
                <div class="step-text">完善链配置</div>
            </div>
          </li>
          <li class="step-active">
            <div class="step-line"></div>
            <div class="step-content">
                <span class="step-num">3</span>
                <div>部署状态检测</div>
            </div>
          </li>
          <li class="step-active">
            <div class="step-content">
                <span class="step-num">4</span>
                <div>完成</div>
            </div>
          </li>
       </ol>
    </div> -->
    <div>
      <router-view></router-view>
    </div>
    <!-- <ExChain v-if="chainName == '体验链'"  @getChildLoading="getChildLoading"></ExChain>
    <PrChain v-if="chainName == '标准链'"  @getChildLoading="getChildLoading"></PrChain>
    <CusChain v-if="chainName == '专业链'"  @getChildLoading="getChildLoading" ></CusChain> -->
    
  </div>
</template>

<script>
import ExChain from './compontents/exChain'
import PrChain from './compontents/prChain'
import CusChain from './compontents/cusChain'
export default {
  components:{
    ExChain,
    PrChain,
    CusChain
  },
  data() {
    return {
      chainName:'',
      infoIcon:require('@/assets/chainManage_images/overview/infoIcon.png'),
      loading:false,
    };
  },
  mounted() {
    this.chainName = sessionStorage.getItem('params')
  },
  methods:{
    getChildLoading(type) {
      this.loading = type
    }
  }
}
</script>

<style lang="less" scoped>
.page {
  width: 100%;
  min-height: 100%;
  //padding:40px 142px 0 ;
  //background: #F2F7FA;
  ul  {
    list-style: none;
  }
  .changeChain {
    .title {
      // font-size: 24px;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
    }
    .arrow {
      color: #666666;
      // font-size: 28px;
      font-size: 14px;
    }
    .name {
      // font-size: 24px;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
    }
  }
  .header {
    width: 80%;
    margin-top: 68px;
    margin-left: 20%;
     ol.steps::-webkit-scrollbar { /* chrome 隐藏滚动条*/
            display: none;
        }
        ol.steps{
            list-style: none;
            display: flex;
            height: 80px;
        }
        ol.steps li{
            float: left;
            flex: 1;
            position: relative;
           // width:140px;
        }
        ol.steps li .step-line{
          width: 100%;
          height: 1Px;
          border: 1Px solid #DFDFDF;
        }
        ol.steps li  .active-line {
          width: 100%;
          height: 1Px;
          border: 1Px solid#2290FF;
        }
        ol.steps .step-content{
            position: absolute;
            top:-20px;
            left:-40px ;
            text-align: center;
        }
        ol.steps li.active .step-content{
            position: absolute;
            top:-24px;
            left:-40px ;
            text-align: center;
        }
        ol.steps .step-content div {
          margin-top: 15px;
          // font-size: 20px;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #666666;
        }
        ol.steps .step-content .step-text {
          color: #333333;
        }
        ol.steps li .step-content .step-num{
            display: inline-block;
            height: 50px;
            width: 50px;
            color: #666666;
            background-color: #DFDFDF;
            line-height: 46px;
            border-radius: 50%;
            text-align: center;
            border:2px solid rgba(224,224,224,1);
            // font-size:20px;
            font-size: 14px;
            font-weight: bold;
        }
        ol.steps li.active .step-content .step-num{
            height: 56px;
            width: 56px;
            background-color:#2290FF;
            line-height:50px;
            color: #fff;
            border:2px solid #2290FF;
        }
        ol.steps li.step-end{
            width: 120px!important;
            flex: inherit;
        }
        ol.steps li.step-end .step-line{
            display: none;
        }

  }
}
</style>