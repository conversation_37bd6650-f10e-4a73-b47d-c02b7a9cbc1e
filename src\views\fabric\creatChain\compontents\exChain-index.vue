<template>
  <div class="exChain-index">
    <ExChain v-if="currentTab==='exChain'" />
    <router-view v-else/>
  </div>
</template>

<script>
import ExChain from './exChain.vue'
export default {
  name: 'exChain-index',
  components: {
    ExChain
  },
  data () {
    return {
    }
  },
  computed: {
    currentTab () {
      return this.$route.name
    }
  },
  methods: {
    //
  },
  mounted () {}
}
</script>
