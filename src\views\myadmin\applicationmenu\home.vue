<template>
  <div class="home">
    <keep-alive>
      <div class="home-item">
        <!-- <div class="contract-title">
          <b>智能合约</b>
          <Button icon="ios-arrow-forward" size="small" @click="isContractFlag=false">合约广场</Button>
        </div> -->
        <!-- <Button class="right-button" icon="ios-arrow-forward" size="small" @click="changeFlag(false)">合约广场</Button> -->
        <Tabs :value="name" @on-click="tabsFun">
          <TabPane label="服务中心" name="name1">
            <ServiceCenter />
          </TabPane>
          <TabPane label="视图中心" name="name2">
            <ViewCenter ref='serviceCallRanking' />
          </TabPane>
        </Tabs>
      </div>
    </keep-alive>
  </div>
</template>

<script>
import ServiceCenter from './fuwu'
import ViewCenter from './shitu'
export default {
  name: 'contract_home',
  components: {
    ServiceCenter,
    ViewCenter
  },
  data () {
    return {
      name: this.$route.params.tabs || 'name1',
      sonData: ''
    }
  },
  computed: {
    currentTab () {
      return this.$route.params
    }
  },
  methods: {
    tabsFun (e) {
      console.log(e);
      if (e === 'name2') {
        this.$refs.serviceCallRanking.tabsFun(e)
      }
    }
    // changeFlag (flag) {
    //   this.$router.push({
    //     name: 'contract_area'
    //   })
    // }
  },
  mounted () {
    // console.log(this.$route.params)
  }
}
</script>

<style lang="less" scoped>
.home {
  .home-item {
    position: relative;
    // .contract-title{
    //   display: flex;
    //   align-items: center;
    //   b{
    //     margin-right:10px;
    //   }
    // }
  }
  //
  .right-button {
    position: absolute;
    right: 16px;
    top: 4px;
    z-index: 10;
  }
}
</style>
