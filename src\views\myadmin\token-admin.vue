<template>
  <div class="tokentable">
    <!-- <Row>
      <Col span="16">
      <Select v-model="chainId" placeholder="选择目标链" style="width:160px;" filterable>
        <Option v-for="item in chainIdList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
      </Select>
      <Input placeholder="可输入租户名称查询信息" style="width:auto;vertical-align:baseline;margin:0 5px" v-model="arrSearch.tenantValute" />
      <Input placeholder="可输入链账户名称查询信息" style="width:auto;vertical-align:baseline;" v-model="arrSearch.chainAccountName" />
      <Button type="primary" icon="ios-search" @click="searchList" style="margin:0 5px;">查询</Button>
      <Button type="primary" @click="reset" icon="md-sync" ghost>重置</Button>
      </Col>
      <Col span="8"> <Button type="primary" icon="md-add" @click="searchList" ghost>批量导入</Button>
      <DatePicker type="daterange" confirm placement="bottom-end" placeholder="Select date" style="width: 115px;margin:0 5px;"></DatePicker>
      <Button type="primary" @click="reset" placement="right">结果下载
        <Icon type="md-arrow-down"></Icon>
      </Button>
      </Col>
    </Row> -->
    <!-- <div v-if="bg" style="background-color: #ccc;width: 100%;position:absolute;height: 100%;opacity: 0.5;z-index: 1;">
    </div> -->
    <div class="search-form-wrapper">
      <div class="search-form">
        <div class="search-form-content">
          <!-- 第一行：3个输入框 + 操作按钮 -->
          <div class="form-row">
            <div class="form-item">
              <div class="label">链账户名称:</div>
              <Input placeholder="请输入链账户名称" style="width:200px" v-model="chainAccountName" />
            </div>
            <div class="form-item">
              <div class="label">类型:</div>
              <Select v-model="accountType" placeholder="请选择链账户类型" style="width:200px" @on-change="searchList">
                <Option value="ALL">全部</Option>
                <Option value="NORMAL">普通链账户</Option>
                <Option value="CONTRACT">合约链账户</Option>
              </Select>
            </div>
            <div class="form-item">
              <div class="label">所属链:</div>
              <Select v-model="chainId" placeholder="请选择目标链" style="width:200px" filterable @on-change="searchList">
                <Option v-for="item in chainIdList" :value="item.chainId" :key="item.chainId">{{ item.chainName }}</Option>
              </Select>
            </div>
            <!-- 第一行操作按钮 -->
            <div class="action-buttons">
              <Button type="primary" icon="ios-search" @click="searchList" style="margin-right: 8px;">查询</Button>
              <Button type="primary" @click="reset" icon="md-sync" ghost style="margin-right: 8px;">重置</Button>
              <div class="expand-section">
                <a class="expand-btn" @click="toggleExpand">{{ isExpanded ? '高级查询' : '高级查询' }}
                  <Icon :type="isExpanded ? 'ios-arrow-up' : 'ios-arrow-down'" />
                </a>
              </div>
            </div>
          </div>

          <!-- 第二行：展开的高级查询字段 -->
          <div v-show="isExpanded" class="form-row">
            <div class="form-item">
              <div class="label">所属租户:</div>
              <Input placeholder="请输入所属租户" style="width:200px" v-model="tenantName" />
            </div>
            <div class="form-item">
              <div class="label">链账户归属公司:</div>
              <Select v-model="accountCompanyId" placeholder="请选择归属公司" style="width:200px" filterable @on-change="searchList">
                <Option v-for="item in selectList" :value="item.id" :key="item.id">{{ item.companyName }}</Option>
              </Select>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style="display:flex; justify-content: flex-end; margin-bottom: 16px;" v-if="showDownBtn">
      <Upload action="" :accept="'.xls'" :format="['xls']" :before-upload="handleBeforeUpload" v-if='this.$store.state.tenantId' :disabled='loadingUp'>
        <Button type="success" icon="md-add" :disabled="hasEditPermission" ghost :loading='loadingUp'>批量导入</Button>
      </Upload>
      <Button type="success" icon="md-add" ghost :loading='loadingUp' :disabled="hasEditPermission" v-else @click="handleBeforeUploadBtn">批量导入</Button>
      <DatePicker v-model="pickerTime" :options="disabledDate" @on-change='getPickerTime' format="yyyy-MM-dd" type="date" confirm placement="bottom-end" placeholder="选择日期" style="width: 115px;margin:0 5px;"></DatePicker>
      <Button type="success" @click="downFile" :disabled="hasEditPermission" placement="right" ghost>结果下载
        <Icon type="md-arrow-down"></Icon>
      </Button>
    </div>
    <edit-table-mul style="margin: 10px 0;" :columns="columns" v-model="tableData" :key="transferKey"></edit-table-mul>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;" />
    <Modal v-model="modal" title="资源配置" fullscreen :mask-closable="false" @on-ok="init" @on-cancel="init">
      <Steps :current="arrManage.current" style="padding: 20px 60px 30px;position: relative;">
        <Step title="配置时" content=""></Step>
        <Step title="配置中" content=""></Step>
        <Step title="刷新结果" content=""></Step>
      </Steps>
      <div style="margin:0 20px 30px;display:flex" :key="transferKey1" v-if="isUpgradeContract=='0'">
        <p><b style="line-height:30px">提供Token的管理账户</b><br />
          <Select v-model="arrManage.manageAccountName" placeholder="选择目标链对应的管理账户" style="width:200px;" @on-change="getChainResources(1);arrManage.current=0">
            <Option v-for="item in arrManage.manageAccounts" :value="item.name" :key="item.name">{{ item.name }}</Option>
          </Select>
          Token余额：{{arrManage.manageCoreToken}}
        </p>
        <p style="margin-top:33px;width:50%;margin-left: 2%;" v-if="this.dataType==='普通链账户'&&this.netyes==='是'"><span>是否使用内存：{{this.netyes}}</span> <span style="margin-left: 3%;">使用内存量：{{this.netdata}}</span></p>
      </div>
      <b style="line-height:30px;margin-left: 20px">{{arrManage.chainAccountName}}：</b>
      <a style="float: right;padding: 10px 20px;font-size:14px" type="button" @click="getChainResources(1);getChainResources();arrManage.current=2">
        <Icon type="md-sync" />刷新
      </a>
      <Card style="width:98%;margin:0 auto;" :key="transferKey1+'card'">
        <p slot="title">
          <b style="width: 56%;display: inline-block;">资源用量/总额</b>
          <Divider type="vertical" />
          <b style="">配置</b>
        </p>
        <ul style="display: block;list-style: none;">
          <li style="display:inline;">
            RAM用量：
            <div style="display:inline; position: relative;">
              <Progress style="width:50%;" stroke-color="#1890ff" :percent="arrManage.ramobj.ramnum>100?100:arrManage.ramobj.ramnum" :stroke-width="20" :status="arrManage.ramobj.ramstatus" hide-info></Progress>
              <span style="position: absolute;top:3px;right:3px;color:#000;font-size:12px">{{arrManage.ramobj.ramnum}}%</span>
            </div>
            <Input type="number" style="width:25%;margin: 10px 10px 10px 20px;display: inline-table" v-model="arrManage.ramobj.ramvalue" placeholder="请输入数值">
            <span slot="append" @click="btn_select" style="width:45px;cursor:pointer">{{this.btn_title}}</span>
            </Input>
            <ButtonGroup size="large">
              <Button type="primary" icon="ios-add" @click="add" :disabled="hasEditPermission">存入</Button>
              <Button icon="md-remove" @click="minus" :disabled="hasEditPermission">收回</Button>
            </ButtonGroup>
          </li>
          <br /><span style="position:relative;left: 80px;top: 0;">{{arrManage.ramobj.ramshow}}</span>
          <Divider />
          <li style="display:inline;">
            NET用量：
            <div style="display:inline; position: relative;">
              <Progress style="width:50%;" stroke-color="#f0b90b" :percent="arrManage.netobj.netnum?arrManage.netobj.netnum>100?100:arrManage.netobj.netnum:0" :stroke-width="20" :status="arrManage.netobj.netstatus" hide-info />
              <span style="position: absolute;top:3px;right:3px;color:#000;font-size:12px">{{arrManage.netobj.netnum}}%</span>
            </div>
            <Input type="number" style="width:25%;margin: 10px 10px 10px 20px;display: inline-table" v-model="arrManage.netobj.netvalue" placeholder="请输入数值">
            <span slot="append">SYS</span>
            </Input>
            <ButtonGroup size="large">
              <Button type="primary" icon="ios-code-download" @click="addResources('net')" :disabled="hasEditPermission">抵押</Button>
              <Button icon="md-remove" @click="minusResources('net')" :disabled="hasEditPermission">收回</Button>
            </ButtonGroup>
          </li>
          <br /><span style="position:relative;left: 80px;top: 0;">{{arrManage.netobj.netshow}}</span>
          <Divider />
          <li style="display:inline;">
            CPU用量：
            <div style="display:inline; position: relative;">
              <Progress style="width:50%;" stroke-color="#34c9ca" :percent="arrManage.netobj.netnum?arrManage.cpuobj.cpunum>100?100:arrManage.cpuobj.cpunum:0" :stroke-width="20" :status="arrManage.cpuobj.cpustatus" hide-info />
              <span style="position: absolute;top:3px;right:3px;color:#000;font-size:12px">{{arrManage.cpuobj.cpunum}}%</span>
            </div>

            <Input type="number" style="width:25%;margin: 10px 10px 10px 20px;display: inline-table;" v-model="arrManage.cpuobj.cpuvalue" placeholder="请输入数值">
            <span slot="append">SYS</span>
            </Input>
            <ButtonGroup size="large">
              <Button type="primary" icon="ios-code-download" @click="addResources('cpu')" :disabled="hasEditPermission">抵押</Button>
              <Button icon="md-remove" @click="minusResources('cpu')" :disabled="hasEditPermission">收回</Button>
            </ButtonGroup>
          </li>
          <br /><span style="position:relative;left: 80px;top: 0;">{{arrManage.cpuobj.cpushow}}</span>
          <Divider />
          <li v-if="isUpgradeContract=='0'">
            Token余额：{{arrManage.CoreToken}}
            <ButtonGroup size="large" style="width:30vh">
              <Button @click="tokenMinus" :disabled="hasEditPermission">一键收回</Button>
            </ButtonGroup>
          </li>
        </ul>
      </Card>
      <Collapse v-model="panelValue" simple name="mainpanel" style="margin-top:20px">
        <Panel name="1" style="background:#ECEFFC;">
          <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
          资源剩余
          <p slot="content" class="basetext" style="display: flex;">

            <span class="btn_title">日期</span>
            <DatePicker format="yyyy-MM-dd" type="daterange" :editable='false' placeholder="开始日期~结束日期" style="width: 220px" @on-change="timeout_click" v-model="time"></DatePicker>
            <Button type="primary" class="btn_search" icon="ios-search" @click.native="input_residue">查询</Button>
            <Button type="primary" ghost icon="md-sync" class="btn_search" @click.native="reset1">重置</Button>
          </p>
          <p slot="content" class="basetext">
          <Table stripe :columns="columnsResource" :data="dataResource">
            <template slot-scope="{ row, index }" slot="action">
              <Button type="text" size="small" style="marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF;" @click="resourcemodal(row)">资源申请</Button>
            </template>
          </Table>
          <Page :total="PageParam.pagetotal" :current.sync="PageParam.pageIndex" @on-change="pageChange1" :page-size="PageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange1" style="text-align:right;" />
          </p>
        </Panel>
        <Panel name="2" style="background:#ECEFFC;">
          <b style="width: 6px;height: 16px;background: rgb(87, 163, 243);display: inline-block;vertical-align: middle;margin: 2px 8px 4px;border-radius: 4px;"></b>
          资源变更日志
          <p slot="content" class="basetext" style="display: flex;">
            <span class="btn_title">操作时间</span>
            <DatePicker format="yyyy-MM-dd" type="daterange" :editable='false' placeholder="开始日期~结束日期" style="width: 220px" @on-change="timeout_click2" v-model="time2"></DatePicker>
            <span class="btn_title">操作类型</span>

            <Select v-model="operationType" style="width: 180px" placeholder="请选择操作类型">
              <Option v-for="(item,index) in cityList1" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
            <Button type="primary" class="btn_search" icon="ios-search" @click.native="input_change">查询</Button>
            <Button type="primary" ghost icon="md-sync" class="btn_search" @click.native="reset2">重置</Button>
          </p>
          <p slot="content" class="basetext">
          <Table stripe :columns="columnsContract" :data="columnsContractArr">
            <template slot-scope="{ row, index }" slot="action">
              <Button type="text" size="small" style="marginRight: 8px; color: #3D73EF; border:1px solid #3D73EF;" @click="resourcemodal(row)">资源申请</Button>
            </template>
          </Table>
          <Page :total="PageParam2.pagetotal" :current.sync="PageParam2.pageIndex" @on-change="pageChange2" :page-size="PageParam2.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange2" style="text-align:right;" />
          </p>
        </Panel>
      </Collapse>
    </Modal>
    <!-- 上传成功/失败 -->
    <Modal :draggable="true" v-model="continueModal" width="550" title="" :z-index="1000" sticky :mask-closable="false" :closable="false" :footer-hide='true'>
      <!-- 成功 -->
      <!-- <div v-if="showhide">
        <p style="text-align:center;">
          <Icon type="md-checkmark-circle" size="60" style="color:#1296DB" />
        </p>
        <p style="color:#888A8C;font-size: 20px;text-align: center;margin-top: 5%;">文件上传成功</p>
        <div slot="footer" style="text-align: center;margin-top: 8%;">
          <Button type="primary" ghost @click="successCancel" style="width: 80px;">取消</Button>
          <Button type="primary" style="margin-left: 15%;" @click="continueUp">继续上传文件</Button>
        </div>
      </div> -->
      <!-- 失败 -->
      <div>
        <p style="text-align:center;">
          <Icon type="md-close-circle" size="50" style="text-align: center;color:red" />
        </p>
        <p style="color:#888A8C;font-size: 20px;text-align: center;margin-bottom:2%">文件上传失败</p>
        <ul class="choose" v-if="shibai">
          <li v-for="item in failResult" :key="item.failRow">
            <p>（{{item.indexkey}}）错误位置：{{item.failRow}}行 </p>
            <p style="margin-left: 17.5%;"> 错误原因：{{item.failReason}}</p>
          </li>
        </ul>
        <p v-else style="color:#3399ff;font-size: 15px;text-align:center">{{shibaiinfo}}</p>
        <div slot="footer" style="text-align: center;margin-top: 5%;">
          <Button type="primary" @click="continueModal=false">确定</Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import { getChainIdList, getChainTableData, getManageAccounts, getChainAccountResources, addBuyRam, reclaimRam, reclaimToken, stakeResources, unstakeResources, getChainTableDataAll, } from '@/api/data'
import EditTableMul from '_c/edit-table-mul'
import { getResidue, getChange, getImportNortalChainData, getExportMainChainData } from '@/api/dashborad'
import { getconfig, tenantcompanyList } from '@/api/contract'
import { localRead } from '@/lib/util'
let unitConversion = {
  Byte (value) { return value * 1 },
  KB (value) { return value * 1024 },
  MB (value) { return value * 1024 * 1024 },
  GB (value) { return value * 1024 * 1024 * 1024 }
}
// 使用全局变量存储窗口引用，避免Vue响应式系统处理窗口对象
let globalTargetWin = null;
export default {
  name: 'token_admin',
  components: {
    EditTableMul
  },
  data () {
    return {
      panelValue: ['1', '2', '3', '4', '5'],
      btn_title: 'MB',
      modal: false,
      transferKey: 0,
      transferKey1: 0,
      chainId: this.$route.params.chainId ? this.$route.params.chainId : '',
      chainAccountName: '',
      status: null,
      tenantName: '',
      accountCompanyId: '',
      dateRange: [],
      accountType: 'ALL',
      inputvalue: '',
      arrSearch: { index: '', tenantValute: '', chainAccountName: '' },
      chainIdPageParam: { pagetotal: 0, pageSize: 60, pageIndex: 1 },
      tablePageParam: { pagetotal: 0, pageSize: 10, pageIndex: 1 },
      arrManage: {
        current: 0,
        chainAccountId: '',
        chainAccountName: '',
        manageAccountName: '',
        manageAccounts: [
          // {'name': 'eosio'}, {'name': 'manager1'}, {'name': 'manager2'}, {'name': 'ceshi.233'}
        ],
        CoreToken: '',
        manageCoreToken: '',
        chainAccountResources: {
          // 'chainId': 1, 'chainAccountName': 'hello2', 'coreTokenLiquidBalance': '0.0000 SYS', 'coreTokenSymbol': 'SYS', 'coreTokenPrecision': 4, 'ramQuota': 50145, 'ramUsage': 3236, 'netWeightTotal': '0.2000 SYS', 'netWeightSelfDelegated': null, 'netBandwidthUsed': 257, 'netBandwidthMax': *************, 'cpuWeightTotal': '0.2000 SYS', 'cpuWeightSelfDelegated': null, 'cpuBandwidthUsed': 2441, 'cpuBandwidthMax': ************
        },
        ramobj: {
          ramstatus: 'normal',
          ramshow: '',
          ramvalue: 0,
          ramnum: 0
        },
        netobj: {
          netstatus: 'active',
          netshow: '',
          netvalue: 0,
          netnum: 0
        },
        cpuobj: {
          cpustatus: 'normal',
          cpushow: '',
          cpuvalue: 0,
          cpunum: 0
        }
      },
      chainIdList: [],
      tableData: [],
      dataType: '',
      netyes: '',
      getchainId: '',
      columns: [
        { key: 'chainAccountName', title: '链账户名称' },
        { key: 'accountType', title: '类型' },
        { key: 'chainName', title: '所属链' },
        { key: 'tenantName', title: '所属租户' },
        { key: 'accountCompanyName', title: '链账户归属公司' },
        { key: 'description', title: '链账户描述' }, // , editable: true
        { key: 'useMemory', title: '是否使用内存' },
        { key: 'useMemoryAmount', title: '使用内存量' },
        { // key: 'action',
          title: '操作',
          align: 'left',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {

                    this.arrManage.chainAccountId = params.row.chainAccountId
                    this.arrManage.chainAccountName = params.row.chainAccountName
                    this.arrManage.description = params.row.description
                    this.dataType = params.row.accountType
                    this.netyes = params.row.useMemory
                    this.netdata = params.row.useMemoryAmount
                    this.modal = true
                    this.getchainId = params.row.chainId
                    this.isUpgradeContract = params.row.isUpgradeContract

                    this.time = ''
                    this.chain_time = []
                    this.PageParam = { pageIndex: 1, pageSize: 10 }
                    this.time2 = ''
                    this.chain_time2 = []
                    this.operationType = ''
                    this.PageParam2 = { pageIndex: 1, pageSize: 10 }

                    getManageAccounts(params.row.chainId).then(res => {
                      // console.log('getManageAccounts===>', res)
                      if (res.code === '00000' && res.data.length) {
                        let obj = []
                        for (let i = 0; i < res.data.length; i++) obj.push({ 'name': res.data[i] })
                        this.arrManage.manageAccounts = obj
                        this.arrManage.manageAccountName = obj[0].name
                        this.getChainResources(1)
                        this.getChainResources()
                      } else {
                        this.msgInfo('error', res.message, true)
                      }
                    }).catch(error => {
                      console.log('getManageAccounts.error===>', error)
                      this.msgInfo('error', error.message, true)
                    })
                    this.getTablist()
                    this.getChangeList()
                  }
                }
              }, '资源配置')
            ])
          }
        }
      ],
      isUpgradeContract: '',
      PageParam: { pageSize: 10, pageIndex: 1, pagetotal: 0 },
      PageParam2: { pageSize: 10, pageIndex: 1, pagetotal: 0 },
      // 上链时间
      chain_time: '',
      chain_time2: '',
      time: '',
      time2: '',
      cityList1: [
        {
          value: 0,
          label: '链账户资源申请'
        },
        {
          value: 1,
          label: '管理员审批通过'
        },
        {
          value: 2,
          label: '管理员审批不通过'
        },
        {
          value: 3,
          label: '管理员手动分配'
        },
      ],
      operationType: '',
      columnsResource: [

        { title: '链账户名称', key: 'chainAccountName', tooltip: true },
        { title: '链名称', key: 'chainName', tooltip: true },
        { title: 'RAM(Byte)', key: 'residueRam', tooltip: true },
        { title: 'NET(Byte)', key: 'residueNet', tooltip: true },
        { title: 'CPU(Us)', key: 'residueCpu', tooltip: true },
        { title: '日期', key: 'time', tooltip: true },
      ],
      dataResource: [],
      columnsContract: [
        { title: '链账户名称', key: 'chainAccountName', tooltip: true },
        { title: '链名称', key: 'chainName', tooltip: true },
        { title: '操作对象', key: 'resourceType', tooltip: true },
        { title: '资源变化量', key: 'resourceSize', width: 320 },
        { title: '操作类型', key: 'sourceType', width: 320 },
        { title: '操作时间', key: 'updateTime', tooltip: true },
      ],
      columnsContractArr: [],
      chainNameFile: '',
      pickerTime: '',
      continueModal: false, // 文件成功
      failResult: [],
      shibai: false,
      shibaiinfo: '',
      disabledDate: {
        disabledDate (current) {
          // 获取当前日期
          const today = new Date();

          // 将当前日期设置为今天的最后一毫秒
          today.setHours(23, 59, 59, 999);

          // 禁用今天之后的日期
          return current && current.valueOf() > today;
        },
      },
      showDownBtn: false, //
      loadingUp: false,
      targetWin: '',
      funcName: '',
      bg: false,
      jin: false,
      userPermission: JSON.parse(localRead('userPermission')),
      isExpanded: false,
      selectList: []
    }
  },
  computed: {

    hasEditPermission () {
      // 建议根据实际业务需求定义权限组合
      return this.userPermission.isBusinessAdmin || this.userPermission.isOperationsAdmin
    },
    buttonStyle () {
      return {
        marginRight: '5px',
        color: this.hasEditPermission ? '#dcdee2' : '#3D73EF',
        border: `1px solid ${this.hasEditPermission ? '#dcdee2' : '#3D73EF'}`
      }
    }
  },
  methods: {
    // 调用金库
    showDiv () {
      this.bg = true
    },
    closeDiv () {
      this.bg = false
    },
    showBank (func) {
      this.funcName = func;
      var iWidth = 700; //模态窗口宽度
      var iHeight = 450;//模态窗口高度
      var iTop = (window.screen.height - iHeight - 100) / 2;
      var iLeft = (window.screen.width - iWidth) / 2;
      var winOption = 'height=' + iHeight + ',innerHeight=' + iHeight + ',width=' + iWidth + ',innerWidth=' + iWidth + ',top=' + iTop + ',left=' + iLeft + ',toolbar=no,menubar=no,scrollbars=no,resizeable=no,location=no,status=no';

      var obj = new Object();
      obj.operCode = localStorage.getItem("accountEncrypt");
      obj.mainLoginName = "";
      obj.subLoginName = localStorage.getItem("userNameEncrypt")
      obj.appCode = "JTNGCMBAAS";
      obj.sessionId = sessionStorage.getItem("session");
      obj.serverIp = window.location.host.split(":")[0]
      obj.serverPort = window.location.port;
      obj.checkSessionUrl = "/";
      obj.svcNum = "";
      obj.operContent = "";

      // var returnValue;
      //增加浏览器的判断，ie走if原有逻辑，非ie走else逻辑,通过遮罩层实现。
      var a1 = navigator.userAgent;
      var yesIE = a1.search(/Trident/i);
      if (window.ActiveXObject || window.attachEvent || yesIE > 0) { //IE
        // var returnValue = window.showModalDialog("b.html?id=" + new Date(), obj, "dialogHeight:" + iHeight + "px; dialogWidth:" + iWidth + "px; toolbar:no; menubar:no;  titlebar:no; scrollbars:yes; resizable:no; location:no; status:no;left:" + iLeft + "px;top:" + iTop + "px;");
        var me = new Object();
        // me.data = returnValue;
        this.receiveMsg(me);
      } else {  //非IE
        this.showDiv();//显示遮罩层
        this.openWindowWithPostRequest(iWidth, iHeight, iTop, iLeft, winOption, obj);
        if (window.addEventListener) {
          //为window注册message事件并绑定监听函数
          window.addEventListener('message', this.receiveMsg, false);
        } else {
          window.attachEvent('message', this.receiveMsg);
        }
      }
    },
    openWindowWithPostRequest (iWidth, iHeight, iTop, iLeft, winOption, obj) {
      console.log(obj);
      var winName = "sWindow";
      var winURL = "http://api.it4a.cmit.cmcc:7081/uac/web3/jsp/goldbank/goldbank3!goldBankIframeAction.action";//应用侧对应后台服务action
      var form = document.createElement("form");
      form.setAttribute("method", "post");

      form.setAttribute("action", winURL);
      form.setAttribute("target", winName);
      for (var i in obj) {
        if (obj.hasOwnProperty(i)) {
          var input = document.createElement('input');
          input.type = 'hidden';
          input.name = i;
          input.value = obj[i];
          form.appendChild(input);
        }
      }
      document.body.appendChild(form);
      //打开地址，刚开始时，打开一个不存在的地址，这样才有返回值
      globalTargetWin = window.open("", winName, winOption);
      form.target = winName;
      form.submit();
      document.body.removeChild(form);
      if (window.focus) {
        globalTargetWin.focus();
      }
    },
    //接收返回值后处理函数
    receiveMsg (e) {
      // returnValue = e.data;
      console.log(e, 'e');
      if (e.data && typeof e.data === 'string') {
        var dataStatus = e.data.split("#")
        const statusMessageMap = {
          '-3': { message: '金库应急开启中，允许业务继续访问', allowAccess: true },
          '-2': { message: '金库场景或元业务未开启，允许业务继续访问', allowAccess: true },
          '-1': { message: '直接关闭窗口，未申请审批，不允许业务继续访问', allowAccess: false },
          '1': { message: '审批通过，允许业务继续访问', allowAccess: true },
          '0': { message: '审批不通过，不允许业务继续访问', allowAccess: false },
          '2': { message: '超时，允许业务继续访问', allowAccess: true },
          '3': { message: '超时，不允许业务继续访问', allowAccess: false },
          '4': { message: '出现错误或异常（包括数据异常），不允许业务继续访问', allowAccess: false },
          '5': { message: '未配置策略，允许业务继续访问', allowAccess: true },
          '6': { message: '未配置策略，不允许继续访问', allowAccess: false }
        };

        const status = dataStatus[0];
        const statusInfo = statusMessageMap[status];
        console.log(statusInfo, 'statusInfo');
        if (statusInfo) {
          if (statusInfo.allowAccess) {
            // 允许业务继续访问，继续请求接口
            // 这里调用你继续请求接口的函数
            getExportMainChainData(this.pickerTime, e.data).then(res => {
              let reader = new FileReader();
              reader.readAsText(res);
              reader.onload = () => {
                try {
                  let jsonData = JSON.parse(reader.result);
                  if (jsonData.code === '00000') {
                    // this.showBank()
                    this.msgInfo('warning', jsonData.message, true)
                  } else {
                    this.msgInfo('error', jsonData.message, true)
                  }
                } catch (e) {
                  let blob = new Blob([res])
                  let downloadElement = document.createElement('a')
                  let href = window.URL.createObjectURL(blob)
                  downloadElement.href = href
                  downloadElement.download = this.pickerTime ? `普通链账户批量创建结果${this.pickerTime}.xls` : '普通链账户批量创建结果.xls'
                  document.body.appendChild(downloadElement)
                  downloadElement.click()
                  document.body.removeChild(downloadElement)
                  window.URL.revokeObjectURL(href)

                }

              };
            }).catch(error => {
              console.log(error);
              this.msgInfo('error', error.message, true)
            })
          } else {
            // 不允许业务继续访问，弹出提示框
            this.msgInfo('warning', statusInfo.message, true);
            // this.closeDiv();
          }
        }
      } else {
        console.error('e.data 不是字符串类型或为空', e.data);
        this.msgInfo('error', '金库返回数据格式错误', true);
      }

      // alert("returnValue1111111===" + e.data);

      /**
         *
         *在这里处理业务，执行回调函数
         */
      // console.log(returnValue);
      // if (returnValue != 'undefined' && returnValue != '') {
      //   eval(funcName);
      // }
      if (globalTargetWin != null) {
        globalTargetWin.close();
        // this.closeDiv();//关闭遮罩层
      }
    },


    // //回调测试函数
    test1 () {
      alert("test1");
    },
    // 结束
    getPickerTime (e) {
      this.pickerTime = e
    },
    //下载
    downFile (e, treasuryToken = '') {
      getExportMainChainData(this.pickerTime, treasuryToken).then(res => {
        let reader = new FileReader();
        reader.readAsText(res);
        reader.onload = () => {
          try {
            let jsonData = JSON.parse(reader.result);
            if (jsonData.code === 'A0314') {
              this.showBank()
            } else {
              this.msgInfo('error', jsonData.message, true)

            }
          } catch (e) {
            console.log(e);
            let blob = new Blob([res])
            let downloadElement = document.createElement('a')
            let href = window.URL.createObjectURL(blob)
            downloadElement.href = href
            downloadElement.download = this.pickerTime ? `普通链账户批量创建结果${this.pickerTime}.xls` : '普通链账户批量创建结果.xls'
            document.body.appendChild(downloadElement)
            downloadElement.click()
            document.body.removeChild(downloadElement)
            window.URL.revokeObjectURL(href)

          }

        };


      }).catch(error => {
        console.log(error);
        this.msgInfo('error', error.message, true)
      })
    },
    // 获取公司列表
    getCompanyList () {
      let listcompany = {
        companyName: ''
      }
      tenantcompanyList(listcompany).then(res => {
        if (res.code === '00000') {
          this.selectList = res.data
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch((error) => {
        this.msgInfo('error', error.message, true)
      })

    },
    toggleExpand () {
      this.isExpanded = !this.isExpanded;
    },
    // if (this.$store.state.tenantId) {
    //   let files = {
    //     tenantId: this.$store.state.tenantId,
    //     file: this.chainNameFile
    //   }
    //   getImportNortalChainData(files).then(res => {
    //     if (res.code === '00000') {
    //       this.msgInfo('success', '上传成功', true)
    //     } else {
    //       this.msgInfo('warning', res.message, true)
    //     }
    //   }).catch(error => {
    //     this.msgInfo('error', error.message, true)
    //   })
    // } else {
    //   this.msgInfo('warning', '您当前还未选择租户', true)
    // }
    handleBeforeUploadBtn () {
      this.msgInfo('warning', '您当前还未选择租户', true)
    },
    // 批量上传
    handleBeforeUpload (file) {
      let filename = file.name.replace(/.+\./, '').toLowerCase()
      if (filename == 'xls') {
        this.chainNameFile = file
        let files = {
          tenantId: this.$store.state.tenantId,
          file: this.chainNameFile
        }
        this.msgInfo('warning', '导入数据校验中，如数据量较大会耗时较久，请勿离开此页面', true)
        this.loadingUp = true
        setTimeout(() => {
          getImportNortalChainData(files).then(res => {
            if (res.data.status == 'success') {
              this.msgInfo('success', '数据导入成功，普通链账户批量创建中，请稍后下载结果进行查看', true)
              this.loadingUp = false
            } else {
              if (res.data.message === null) {
                this.continueModal = true
                this.shibai = true
                res.data.failResult.forEach((item, index) => {
                  item.indexkey = index + 1
                })
                this.failResult = res.data.failResult
                this.loadingUp = false
              } else {
                this.shibai = false
                this.continueModal = true
                this.shibaiinfo = res.data.message
                this.loadingUp = false

              }
            }
          }).catch(error => {
            this.msgInfo('error', error.message, true)
            this.loadingUp = false
          })
        }, 4000)





      } else {
        this.msgInfo('warning', '请上传xls文件', true)
      }
      return false
    },


    // 输入框值
    input_residue () {

      this.getTablist() // 获取表格列表
    },
    input_change () {

      this.getChangeList() // 获取表格列表
    },
    // 重置
    reset1 () {
      this.time = ''
      this.chain_time = []
      this.PageParam = { pageIndex: 1, pageSize: 10 }

      this.getTablist()
    },
    timeout_click (e) {
      this.chain_time = e
    },
    // 重置
    reset2 () {
      this.time2 = ''
      this.chain_time2 = []
      this.operationType = ''
      this.PageParam2 = { pageIndex: 1, pageSize: 10 }
      this.getChangeList()
    },
    timeout_click2 (e) {
      this.chain_time2 = e
    },
    getTablist () {
      let data = {
        chainAccountId: this.arrManage.chainAccountId,
        beginTime: this.chain_time[0] || '',
        endTime: this.chain_time[1] || '',
        pageParam: this.PageParam,
        sourceType: ''
      }
      getResidue(data).then(res => {
        if (res.code === '00000') {
          this.dataResource = res.data.records
          this.PageParam.pagetotal = res.data.total
        } else {
          this.msgInfo('warning', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    getChangeList () {
      let data = {
        chainAccountId: this.arrManage.chainAccountId,
        beginTime: this.chain_time2[0] || '',
        endTime: this.chain_time2[1] || '',
        pageParam: this.PageParam2,
        sourceType: this.operationType
      }
      getChange(data).then(res => {
        if (res.code === '00000') {
          let dataType = {
            0: '链账户资源申请',
            1: '管理员审批通过',
            2: '管理员审批不通过',
            3: '管理员手动分配'
          }
          let data = res.data.records.map(item => {
            return {
              ...item,
              sourceType: dataType[item.sourceType]
            }


          })
          this.columnsContractArr = data
          this.PageParam2.pagetotal = res.data.total
        } else {
          this.msgInfo('warning', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    btn_select () {
      switch (this.btn_title) {
        case 'KB': this.btn_title = 'Byte'
          break
        case 'MB': this.btn_title = 'GB'
          break
        case 'GB': this.btn_title = 'KB'
          break
        case 'Byte': this.btn_title = 'MB'
          break
      }
    },
    init () {
      this.arrManage = {
        current: 0,
        chainAccountId: '',
        chainAccountName: '',
        manageAccountName: '',
        CoreToken: '',
        manageCoreToken: '',
        manageAccounts: [],
        chainAccountResources: {},
        ramobj: {
          ramstatus: 'normal',
          ramshow: '',
          ramvalue: 0,
          ramnum: 0
        },
        netobj: {
          netstatus: 'active',
          netshow: '',
          netvalue: 0,
          netnum: 0
        },
        cpuobj: {
          cpustatus: 'normal',
          cpushow: '',
          cpuvalue: 0,
          cpunum: 0
        }
      }
      this.getTableData()
    },
    msgInfo (type, content, closable = false) { this.$Message[type]({ background: true, closable: closable, content: content, top: 400 }) },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getTableData()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getTableData()
    },

    pageChange1 (index) {
      this.PageParam.pageIndex = index
      this.getTablist()
    },
    pageSizeChange1 (index) {
      this.PageParam.pageSize = index
      this.getTablist()
    },

    pageChange2 (index) {
      this.PageParam2.pageIndex = index
      this.getChangeList()
    },
    pageSizeChange2 (index) {
      this.PageParam2.pageSize = index
      this.getChangeList()
    },
    getTableData () {
      // 构建查询参数
      const params = {
        chainId: this.chainId,
        pageParam: this.tablePageParam,
        accountType: this.accountType === 'ALL' ? null : this.accountType,
        chainAccountName: this.chainAccountName,
        status: this.status,
        tenantName: this.tenantName,
        accountCompanyId: this.accountCompanyId,
        createTimeStart: this.dateRange && this.dateRange.length > 0 ? this.formatDateTime(this.dateRange[0]) : null,
        createTimeEnd: this.dateRange && this.dateRange.length > 0 ? this.formatDateTime(this.dateRange[1]) : null,
        inputvalue: this.inputvalue
      }
      getChainTableDataAll(params).then(res => {
        // console.log('getChainTableData===>', res)
        if (res.code !== '00000') {
          if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else {
            this.msgInfo('warning', res.message, true)
          }
        } else {
          let netdata = { y: '是', n: '否' }
          this.tableData = res.data.records.map(item => {
            return {
              ...item,
              useMemory: netdata[item.useMemory]
            }
          })
          this.tableData.map((item) => {
            if (item.topOrder === 1) {
              item.cellClassName = { chainName: 'demo-table-info-cell-chainName' }
            }
            return item
          })
          this.tableData.map((item) => {
            item.useMemoryAmount = item.useMemoryAmount ? item.useMemoryAmount + 'Byte' : ''
            return item
          })
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          ++this.transferKey
        }
      }).catch(error => {
        console.log('getChainTableData.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    },
    reset () {
      if (this.chainIdList[0] && this.chainIdList[0].chainId) {
        this.chainId = this.chainIdList[0].chainId
      } else {
        this.chainId = ''
      }
      this.arrSearch.tenantValute = ''
      this.chainAccountName = ''
      this.tenantName = ''
      this.accountCompanyId = ''
      this.accountType = 'ALL'
      this.dateRange = []
      this.inputvalue = ''
      this.tablePageParam = { pagetotal: 0, pageSize: 10, pageIndex: 1 }
      this.getTableData()
    },
    getChainResources (type) {
      let AccountName = this.arrManage.chainAccountName
      if (type === 1) AccountName = this.arrManage.manageAccountName
      if (AccountName !== undefined) {
        // alert(AccountName + '=====' + type)
        getChainAccountResources(this.getchainId, AccountName).then(res => {
          // console.log('getChainAccountResources===>', res)
          if (res.code === '00000') {
            this.arrManage.ramobj.ramstatus = 'normal'
            this.arrManage.netobj.netstatus = 'normal'
            this.arrManage.cpuobj.cpustatus = 'normal'
            this.arrManage.chainAccountResources = res.data
            if (type === 1) {
              this.arrManage.manageCoreToken = res.data.coreTokenLiquidBalance
              // alert(this.arrManage.manageCoreToken + '=====' + type)
            } else {
              this.arrManage.CoreToken = res.data.coreTokenLiquidBalance
              this.arrManage.ramobj.ramnum = Math.ceil(res.data.ramUsage / res.data.ramQuota * 100)
              if (res.data.ramQuota > 1e7) this.arrManage.ramobj.ramshow = res.data.ramUsage + ' Byte / ' + Math.ceil(res.data.ramQuota / 1e6) + ' MB'
              else if (res.data.ramQuota) this.arrManage.ramobj.ramshow = res.data.ramUsage + ' / ' + res.data.ramQuota + ' Byte'

              this.arrManage.netobj.netnum = res.data.netBandwidthMax ? Math.ceil(res.data.netBandwidthUsed / res.data.netBandwidthMax * 100) : 100
              if (res.data.netBandwidthMax > 1e7) this.arrManage.netobj.netshow = res.data.netBandwidthUsed + ' Byte / ' + Math.ceil(res.data.netBandwidthMax / 1e6) + ' MB'
              else if (res.data.netBandwidthMax || res.data.netBandwidthMax == 0) this.arrManage.netobj.netshow = res.data.netBandwidthUsed + ' / ' + res.data.netBandwidthMax + ' Byte'
              this.arrManage.netobj.netshow += '(折算：' + res.data.netWeightTotal + ')'
              // if (res.data.netBandwidthMax == 0) {
              //   this.arrManage.netobj.netshow = '(折算：' + res.data.netWeightTotal + ')'
              // } else {
              //   this.arrManage.netobj.netshow += '(折算：' + res.data.netWeightTotal + ')'

              // }

              this.arrManage.cpuobj.cpunum = Math.ceil(res.data.cpuBandwidthUsed / res.data.cpuBandwidthMax * 100)
              if (res.data.cpuBandwidthMax > 1e7) this.arrManage.cpuobj.cpushow = res.data.cpuBandwidthUsed + ' μs / ' + Math.ceil(res.data.cpuBandwidthMax / 1e6) + ' S'
              else if (res.data.cpuBandwidthMax || res.data.cpuBandwidthMax == 0) this.arrManage.cpuobj.cpushow = res.data.cpuBandwidthUsed + ' / ' + res.data.cpuBandwidthMax + ' μs'
              this.arrManage.cpuobj.cpushow += '(折算：' + res.data.cpuWeightTotal + ')'
            }
            // console.log(this.arrManage)
          } else if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else this.msgInfo('warning', res.message, true)
          ++this.transferKey1
        }).catch(error => {
          console.log('getChainAccountResources.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }
    },
    add () {
      if (this.arrManage.ramobj.ramvalue > 0) {
        addBuyRam(this.getchainId, this.arrManage.manageAccountName, this.arrManage.chainAccountName, unitConversion[this.btn_title](this.arrManage.ramobj.ramvalue), this.arrManage.chainAccountId).then(res => {
          // console.log('addBuyRam===>', res)
          if (res.code === '00000') {
            this.arrManage.current = 1
            this.arrManage.ramobj.ramstatus = 'active'
            this.arrManage.ramobj.ramvalue = 0
            this.getChangeList()
            alert(res.message + ',已为您提交申请等待5秒后刷新查看是否成功存入！', 'success')
          } else if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else this.msgInfo('warning', res.message, true)
        }).catch(error => {
          console.log('addBuyRam.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      } else {
        this.msgInfo('warning', '存入RAM字节数须为正整数', true)
      }



    },
    minus () {
      if (this.arrManage.ramobj.ramvalue > 0) {
        reclaimRam(this.getchainId, this.arrManage.chainAccountId, unitConversion[this.btn_title](this.arrManage.ramobj.ramvalue)).then(res => {
          // console.log('reclaimRam===>', res)
          if (res.code === '00000') {
            this.arrManage.current = 1
            this.arrManage.ramobj.ramstatus = 'active'
            this.arrManage.ramobj.ramvalue = 0
            alert(res.message + ',已为您提交申请等待5秒后刷新查看是否成功收回！', 'success')
          } else if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else this.msgInfo('warning', res.message, true)
        }).catch(error => {
          console.log('reclaimRam.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      } else {
        this.msgInfo('warning', '收回RAM字节数须为正整数', true)
      }

    },
    searchList () {
      if (this.chainId) this.getTableData()
      else this.msgInfo('warning', '请对目标链进行选择后查询！', true)
    },
    addResources (row) {
      // if (row === 'net') this.arrManage.cpuobj.cpuvalue = 0
      // else if (row === 'cpu') this.arrManage.netobj.netvalue = 0
      if (row === 'net' && this.arrManage.netobj.netvalue <= 0) {
        this.msgInfo('warning', '抵押NET字节数须为正整数', true)
      } else if (row === 'cpu' && this.arrManage.cpuobj.cpuvalue <= 0) {
        this.msgInfo('warning', '抵押CPU字节数须为正整数', true)
      } else {
        stakeResources(this.getchainId, this.arrManage.chainAccountName, this.arrManage.manageAccountName, this.arrManage.netobj.netvalue, this.arrManage.cpuobj.cpuvalue, this.arrManage.chainAccountId).then(res => {
          // console.log('stakeResources===>', res)
          if (res.code === '00000') {
            this.arrManage.current = 1
            if (row === 'net') {
              this.arrManage.netobj.netstatus = 'active'
              this.arrManage.netobj.netvalue = 0
            } else if (row === 'cpu') {
              this.arrManage.cpuobj.cpustatus = 'active'
              this.arrManage.cpuobj.cpuvalue = 0
            }
            alert(res.message + ',已为您提交申请等待5秒后刷新查看是否成功抵押！', 'success')
          } else if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else this.msgInfo('warning', res.message, true)
        }).catch(error => {
          console.log('stakeResources.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }

    },
    minusResources (row) {
      if (row === 'net' && this.arrManage.netobj.netvalue <= 0) {
        this.msgInfo('warning', '收回NET字节数须为正整数', true)
      } else if (row === 'cpu' && this.arrManage.cpuobj.cpuvalue <= 0) {
        this.msgInfo('warning', '收回CPU字节数须为正整数', true)
      } else {
        // if (row === 'net') this.arrManage.cpuobj.cpuvalue = 0
        // else if (row === 'cpu') this.arrManage.netobj.netvalue = 0
        unstakeResources(this.getchainId, this.arrManage.chainAccountName, this.arrManage.manageAccountName, this.arrManage.netobj.netvalue, this.arrManage.cpuobj.cpuvalue, this.arrManage.chainAccountId).then(res => {
          // console.log('unstakeResources===>', res)
          if (res.code === '00000') {
            this.arrManage.current = 1
            if (row === 'net') {
              this.arrManage.netobj.netstatus = 'active'
              this.arrManage.netobj.netvalue = 0
            } else if (row === 'cpu') {
              this.arrManage.cpuobj.cpustatus = 'active'
              this.arrManage.cpuobj.cpuvalue = 0
            }
            alert(res.message + ',已为您提交申请等待5秒后刷新查看是否成功收回！', 'success')
          } else if (res.code === '500') {
            this.msgInfo('error', res.message, true)
          } else this.msgInfo('warning', res.message, true)
        }).catch(error => {
          console.log('unstakeResources.error===>', error)
          this.msgInfo('error', error.message, true)
        })
      }
    },
    tokenMinus () {
      reclaimToken(this.getchainId, this.arrManage.chainAccountId, this.arrManage.manageAccountName).then(res => {
        // console.log('reclaimToken===>', res)
        if (res.code === '00000') {
          this.arrManage.current = 1
          alert(res.message + ',已为您提交申请等待5秒后刷新查看是否成功收回！', 'success')
        } else if (res.code === '500') {
          this.msgInfo('error', res.message, true)
        } else this.msgInfo('warning', res.message, true)
      }).catch(error => {
        console.log('reclaimToken.error===>', error)
        this.msgInfo('error', error.message, true)
      })
    }
  },
  beforeDestroy () {
    window.removeEventListener('message', this.receiveMsg, false);

    // 确保关闭全局窗口引用
    if (globalTargetWin) {
      try {
        globalTargetWin.close();
      } catch (e) {
        console.error('关闭窗口失败', e);
      }
      globalTargetWin = null;
    }

    clearInterval(this.timer);
    this.timer = null;
  },
  mounted () {
    let name = 'CMBAAS_CREACCS'
    getconfig(name).then((res) => {
      if (res.code === '00000') {
        this.showDownBtn = res.data.value == '1' ? true : false
      } else {
        this.showDownBtn = false
      }


    }).catch((error) => {
      this.showDownBtn = false
      this.msgInfo('error', error.message, true)
    })

    getChainIdList(this.chainIdPageParam).then(res => {
      // console.log('getChainIdList===>', res)
      //       var a=[{id:1,name:'2'}]
      // var c=[{id:2,name:'2'}]
      // var b=a.push(c)
      var a = { chainId: -1, chainName: '全部' }
      this.chainIdList = [a, ...res.data.records]
      this.chainIdPageParam = {
        pagetotal: res.data.total,
        pageSize: res.data.size,
        pageIndex: res.data.current
      }
      if (this.chainIdList[0].chainId) {
        this.chainId = this.chainIdList[0].chainId
        this.getTableData()
      }
    }).catch(error => {
      console.log('getChainIdList.error===>', error)
      this.msgInfo('error', error.message, true)
    })
    this.getCompanyList()
  }
}
</script>

<style lang="less" scoped>
/deep/.ivu-table .demo-table-info-cell-chainName {
  background: url("../../assets/img/jiaobiao.png") no-repeat;
  color: black;
}
.tokentable {
  .ivu-card-head {
    background-color: #f2f2f2;
  }
}
.tokentableHeader {
  display: flex;
  justify-content: space-between;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}

.basetext {
  padding-top: 20px;
  position: relative;
  span {
    text-align: left;
    margin: 0 26px;
    line-height: 20px;
    word-break: break-all;
  }
}
.btn_title {
  // width: 80px;
  height: 33px !important;
  text-align: center !important;
  line-height: 33px !important;
  display: inline-block !important;
}
.btn_search {
  margin-left: 10px;
}
/deep/.ivu-collapse[name="mainpanel"] {
  border: none;
}
/deep/.ivu-collapse[name="mainpanel"] > .ivu-collapse-item {
  border: none;
  border-radius: 5px 5px;
  margin-bottom: 10px;
}
.choose {
  margin-top: 2%;
  display: flex;
  width: 90%;
  height: 200px;
  overflow-y: scroll;
  margin: 0 auto;
  border-radius: 10px;
  // display: flex;
  flex-wrap: wrap;
  background: #f5f5ff;
  border: 1px solid #d9d9da;
  li {
    width: 100%;
    height: 60px;
    margin-top: 3%;
    p {
      margin-left: 9%;
      // text-align: center;
      color: #3399ff;
      font-size: 15px;
    }
  }
}

.search-form {
  border-radius: 4px;
  margin-bottom: 16px;

  .search-form-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .form-row {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .form-item {
    display: flex;
    align-items: center;
    flex: 0 0 auto;

    .label {
      width: 110px;
      text-align: right;
      padding-right: 8px;
      font-size: 14px;
      flex-shrink: 0;
    }
    .label1 {
      width: 110px;
      text-align: right;
      padding-right: 6px;
      font-size: 14px;
      flex-shrink: 0;
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    // gap: 8px;
    // flex-shrink: 0;
    // margin-left: auto;
  }

  .button-group {
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

.expand-section {
  margin-left: 8px;
  display: flex;
  align-items: center;
  .expand-btn {
    color: #3d73ef;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
  }
}

// 响应式布局
@media (max-width: 1400px) {
  .search-form {
    .form-item {
      .label {
        width: 80px;
      }
    }
  }
}

@media (max-width: 1200px) {
  .search-form {
    .search-form-content {
      gap: 4px;
    }
    .form-item {
      margin-bottom: 6px;
      margin-right: 6px;
      .label {
        width: 70px;
        font-size: 13px;
        padding-right: 4px;
      }
    }
  }
}
</style>
