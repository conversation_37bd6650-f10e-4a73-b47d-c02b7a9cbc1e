<template>
  <div class="comp-wrap">
    <!-- <div class="title"><span class="bs"></span>用户资源管理</div>
    <div style="display: flex;">
      <p>平台资源总量（次）：{{totaldata.scalePlatformTotal}}</p>&nbsp;&nbsp;
      <p>当前平台资源剩余（次）：{{totaldata.scaleTotal}}</p>&nbsp;&nbsp;
      <p>已用分配资源（次）：{{totaldata.scaleUseTotal}}</p>
    </div> -->
    <!-- <Button style="width:200px;margin-top:10px;" type="primary" @click="allocatebtn">分配资源</Button> -->
    <!--  -->
    <!-- <div class="title"><span class="bs"></span>资源分配记录</div> -->
    <p style="margin-bottom:30px">

      <!-- <Select class='bt1 width-input' style="vertical-align:baseline;width:230px" v-model="datainput" filterable :remote-method="remoteMethod1s" :loading="loading1" ref="stationSelect" @on-query-change="clearInput" placeholder="请输入服务名称">
        <Option v-for="(item, index) in serverList" :value="item.value" :label="item.label" :key="index">{{item.label}}</Option>
      </Select> -->
      <Input class='bt1 width-input' placeholder="请输入服务名称" @on-enter="searchList" style="vertical-align:baseline;width:230px" v-model.trim="datainput" @keyup.native="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm,'')" />
      <span class="btn_title">统计时间</span>
      <DatePicker format="yyyy-MM-dd" type="daterange" placement="bottom-end" :editable='false' placeholder="开始日期~结束日期" style="width: 220px" @on-change="timeout_click" v-model="time"></DatePicker>
      <Button class='bt1' icon="ios-search" type="primary" @click="searchList">查询</Button>
      <Button class='bt1' icon="md-sync" ghost type="primary" @click="resetting">重置</Button>
    </p>
    <edit-table-mul :columns="columns" v-model="tableData"></edit-table-mul>
    <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[10,20,40,60,100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align:right;line-height:40px" />
    <!-- 分配资源 -->
    <!-- <Modal v-model="modaluser" :title="userTitle" width="600" :draggable="true" :mask-closable="false" @on-cancel='usercancel' sticky>
      <Form ref="formItemRule" :rules="formItemRule" :model="userTenant" :label-width="200" @submit.native.prevent>
        <FormItem label="服务名称：" prop="username">
          <Select v-model="userTenant.username" ref="stationSelect" placeholder="请选择需要分配的服务" style="width:240px" :loading="loading" @keyup.native="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm,'')" :loading-text="modifyParentList.length===0?'无匹配数据':'加载中'">
            <Option v-for="item in modifyParentList" :value="item.id" :key="item.id">{{ item.name }}</Option>
            <Option :value="userTenant.username+''" :disabled="true" v-if="modifyTotal>modifyParentList.length" style="text-align:center">
              <span @mouseover="handleReachBottom" style="font-size:8px;">更多<img :src="imgUrl" style="margin-left:5px;"></span>
            </Option>
            <Option :value="userTenant.username+''" :disabled="true" v-else style="text-align:center;cursor:not-allowed;">
              <span style="font-size:8px;">已加载全部</span>
            </Option>
          </Select>
        </FormItem>

        <FormItem label="分配资源：" prop="allocateresources">
          <Input style="width:230px" v-model="userTenant.allocateresources" placeholder="请填写分配资源次数" :maxlength="50" @keyup.native="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm,'')" /> 次
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="default" @click="usercancel">取消</Button>
        <Button type="primary" @click="userok('formItemRule')" :loading='userLoading'>确定</Button>
      </div>
    </Modal> -->
  </div>
</template>
<script>
import { getServerList, getScaleTransactionResourceShare, getUserResourceList, newdataPush } from '@/api/balance'
import EditTableMul from '_c/edit-table-mul'
import { isNumber, rmoney } from "@/lib/check"
export default {
  components: {
    EditTableMul
  },
  data () {

    const validateValue = (rule, value, callback) => {
      if (value != ' ') {
        let numValue = Number(value.trim())
        if (!isNumber(numValue)) {
          callback(new Error('请输入大于0的正整数'))
        } else if (numValue <= Number(rmoney(this.totaldata.scaleTotal))) {
          if (numValue == 0) {
            callback(new Error('请输入大于0的正整数'))
          } else {
            callback()

          }
        } else {
          callback(new Error('当前剩余资源不足'))
        }
      } else {
        callback(new Error('请输入大于0的正整数'))
      }


    }

    return {
      imgUrl: require('@/assets/img/arrow.png'),
      modifyTotal: 0,
      modifyParentList: [],
      modaluser: false,
      userTitle: '分配资源',
      userTenant: { username: '', allocateresources: '' },
      formItemRule: {
        username: [{ required: true, message: '请选择数据名称', }],
        allocateresources: [{ required: true, message: '请填写分配资源次数', trigger: 'change', }, { trigger: 'change', validator: validateValue }]
      },
      selectList: [{ id: 1, companyName: '1111' }, { id: 2, companyName: '222' }],
      datainput: '',
      columns: [
        { key: 'scalePlatformResourceName', title: '服务名称', tooltip: true },
        { key: 'resourceUseTotal', title: '资源消耗（次）', tooltip: true },
        { key: 'status', title: '状态', tooltip: true },
        { key: 'startTime', title: '开始时间', tooltip: true },
        { key: 'endTime', title: '结束时间', tooltip: true }
      ],
      tableData: [],
      tablePageParam: {
        pageSize: 10,
        pageIndex: 1,
        pagetotal: 0
      },
      totaldata: {},
      dataname: {
        name: '',
        pageParam: {
          pageIndex: 1,
          pageSize: 100
        }
      },
      chainIdPageParam: { pagetotal: 0, pageSize: 60, pageIndex: 1 },
      loading: false,
      userLoading: false,
      datainput: '',
      loading1: false,
      serverList: [],
      timer: null,
      time: '',
      chain_time: '',
    }
  },
  methods: {

    //清空
    clearInput (e) {
      if (!e) {
        this.serverList = []
        this.datainput = ''
      }
    },
    remoteMethod1s (query) {
      clearTimeout(this.timer);
      console.log(this.datainput);
      if (query !== '') {
        this.loading1 = true;
        let data = {
          selectType: 2,
          pageParam: {
            pageIndex: 1,
            pageSize: 10
          }
        }
        this.timer = setTimeout(() => {
          newdataPush(data).then(res => {
            this.loading1 = false;
            const list = res.data.records.map(item => {
              return {
                value: item.id,
                label: item.name
              };
            });
            this.serverList = list.filter(item => item.label.indexOf(query) > -1);
            // this.$refs["stationSelect"].query = query  //内容选中后选择框显示（解决鼠标移出后选择框清空问题）
          }).catch(error => {
            // this.msgInfo('error', error.message, true)
          })
        }, 500);

      } else {
        this.serverList = [];
      }
    },
    // 提示
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    // 分页
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.listTable()
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.listTable()
    },
    searchList () {
      this.tablePageParam = {
        pageSize: 10,
        pageIndex: 1,
      }
      this.listTable()
    },
    resetting () {
      this.datainput = ''
      this.chain_time = []
      this.time = ''
      this.tablePageParam = {
        pageSize: 10,
        pageIndex: 1,
        pagetotal: 0,
      }
      this.listTable()
    },
    userok (formItemRule) {
      this.$refs[formItemRule].validate((valid) => {
        if (valid) {

          this.userLoading = true
          let userResource = {
            scalePlatformResourceId: this.userTenant.username,
            resources: this.userTenant.allocateresources,
          }
          getUserResourceList(userResource).then(res => {
            if (res.code === '00000') {
              this.userLoading = false
              this.msgInfo('success', res.message, true)
              this.modaluser = false;
              this.listTable()
              this.totalresources()
            } else {
              this.userLoading = false
              this.msgInfo('error', res.message, true)
            }
          }).catch(error => {
            this.userLoading = false
            this.msgInfo('error', error.message, true)
          })

        }
      })

    },
    usercancel () {
      this.modaluser = false
      this.$refs.formItemRule.resetFields()
      this.source.cancel('取消请求')
    },
    allocatebtn () {
      this.modaluser = true
      this.userLoading = false
      this.$refs.formItemRule.resetFields()
      this.modifyParentList = []
      this.dataname = {
        pageParam: {
          pageIndex: 1,
          pageSize: 100
        }
      },
        this.datanamelist()
    },
    listTable () {
      let datauser = {
        scalePlatformResourceName: this.datainput,
        pageParam: {
          pageIndex: this.tablePageParam.pageIndex,
          pageSize: this.tablePageParam.pageSize,

        },
        startTime: this.chain_time[0] ? this.chain_time[0] + ' 00:00:00' : '',
        endTime: this.chain_time[1] ? this.chain_time[1] + ' 23:59:59' : '',
      }
      getServerList(datauser).then(res => {
        if (res.code === '00000') {
          let resdata = {
            0: '关闭',
            1: '启用',

          }
          let data = res.data.records.map(item => {
            return {
              ...item,
              status: item.status ? resdata[item.status] : resdata[item.status]
            }
          })
          this.tableData = data
          this.tablePageParam.pagetotal = res.data.total
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    // 查询资源数
    totalresources () {
      getScaleTransactionResourceShare().then(res => {
        this.totaldata = res.data
      })
    },
    // 数据服务名称
    datanamelist () {
      newdataPush(this.dataname).then(res => {
        if (res.code === '00000') {
          if (res.data.records && res.data.records.length > 0) {
            this.modifyParentList.push(...res.data.records)
            this.loading = false;
            this.modifyTotal = res.data.total
          }

        } else {
          this.msgInfo('error', res.message, true)
        }

      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    handleReachBottom () {
      return new Promise(resolve => {
        let tablePageParam = { pageSize: 100, pageIndex: 1 }
        let pageIndex = 1
        // if (this.arrRole.roleId) {
        pageIndex = this.modifyParentList.length / 10 + 1
        tablePageParam = Object.assign({}, tablePageParam, { pageIndex })
        if (this.modifyParentList.length % 10 !== 0 || Math.ceil(this.modifyTotal / 10) < pageIndex) {
          return resolve()
        } else {
          this.dataname.pageParam.pageIndex = tablePageParam.pageIndex
          this.dataname.pageParam.pageSize = tablePageParam.pageSize
          this.datanamelist()
        }

        resolve()
      })
    },


    remotedMethod (value) {


    },
    timeout_click (e) {
      this.chain_time = e
    },

  },
  mounted () {
    this.totalresources()
    this.listTable()
  }
}
</script>
<style lang="less">
.p-finish {
  text-align: center;
}
.comp-wrap {
  padding: 0 40px;
  box-sizing: border-box;
  .title {
    margin: 15px 0;
    font-size: 16px;
    font-weight: bold;
    height: 25px;
    line-height: 25px;
    vertical-align: middle;
  }
  .bs {
    float: left;
    width: 6px;
    height: 16px;
    background: #348eff;
    opacity: 1;
    border-radius: 3px;
    margin: 4px 5px 0 0;
  }
  .bt1 {
    margin-right: 10px;
    margin-left: 10px;
  }
  .btn_title {
    width: 80px;
    height: 33px;
    text-align: center;
    line-height: 33px;
    display: inline-block;
  }
}
</style>
