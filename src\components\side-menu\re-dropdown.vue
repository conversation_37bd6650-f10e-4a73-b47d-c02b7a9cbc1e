<template>
  <Dropdown @on-click="handleClick" placement="right-start" class="right-container">
    <span class="drop-menu-span" :style="titleStyle">
      <div v-show="parent.selfIcon && parent.meta.title !== '审批管理' && (parent.children[0] && !parent.children[0].hidden)"  class="menu-img-container">
        <img :style="`width:${parent.width ? parent.width :'20px' }`" class="menu-img" :src="parent.meta.title == '审批管理' ? parent.selfIconb : parent.selfIcon" alt="">
      </div>
      <!-- <Icon v-else :type="parent.icon" :color="iconColor" :size="20"></Icon> -->
      <span v-if="showTitle">{{ parent.meta.title }}</span>
    </span>
    <DropdownMenu slot="list">
      <template v-for="item in parent.children.filter(val => !val.hidden)">
        <re-dropdown v-if="item.children && !item.children[0].hidden" :key="`drop_${item.name}`" :parent="item"></re-dropdown>
        <DropdownItem v-else :key="`drop_${item.name}`" :name="item.name">
          <!-- <div v-if="item.selfIcon" class="menu-img-container"> <img class="menu-img" :src="item.selfIconb" alt=""></div> -->
          <!-- <Icon v-else :type="item.icon" color="#111945" :size="20"></Icon> -->
          {{ item.meta.title }}
        </DropdownItem>
      </template>
    </DropdownMenu>
  </Dropdown>
</template>

<script>
export default {
  name: 'ReDropdown',
  props: {
    parent: {
      type: Object,
      default: () => ({})
    },
    iconColor: {
      type: String,
      default: '#111945'
    },
    showTitle: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    titleStyle () {
      return {
        textAlign: this.showTitle ? 'left' : 'center',
        paddingLeft: this.showTitle ? '16px' : ''
      }
    }
  },
  methods: {
    handleClick (name) {
      if (!this.showTitle) this.$emit('on-select', name)
    }
  }
}
</script>

<style lang="less">
.right-container{
  .ivu-select-dropdown{
  }
}

</style>
