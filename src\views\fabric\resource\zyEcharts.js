import echarts from "echarts";
// 绘制仪表盘
let colors = ["#FB7C57", "#F2C94C", "#63FAB7", "#60D1FA"];
let fontColor = "#42E5FB";
let data = [70, 2];
let fontSize = 12;
export const ybpOption = {
    title: {
        text: 'CPU使用率',
        left: 'left',
        textStyle: {
            //文字颜色
            color: '#666666',
            //字体风格,'normal','italic','oblique'
            fontStyle: 'normal',
            //字体粗细 'normal','bold','bolder','lighter',100 | 200 | 300 | 400...
            fontWeight: 'normal',
            //字体系列
            fontFamily: 'Microsoft YaHei',
            //字体大小
            fontSize: 14
        }
    },
    series: [
        {
            name: '最内层线',
            type: 'gauge',
            radius: '87%',
            center: ['50%', '66%'],
            min: 0,
            max: 100,
            startAngle: 220,
            endAngle: -40,
            axisLine: {
                show: true,
                lineStyle: {
                    opacity: 0,
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    opacity: 0
                }
            },
            axisLabel: {
                show: false,
                color: "#666666",
                distance: -18,
                fontSize: fontSize,
            },
            axisTick: {
                length: 8,
                lineStyle: {
                    color: fontColor,
                    width: 1,
                    type: 'solid'
                }
            },
            detail: {
                show: true,
                offsetCenter: [80, '258%'],
                fontSize: 15,
                color: '#fff'
            },

            // title: {
            //     show: false,
            //     offsetCenter: [50, '258%'],
            //     textStyle: {
            //         color: fontColor,
            //     }
            // },
            // pointer: {
            //     show: false
            // }
        },
        {
            name: '内层数据刻度',
            type: 'gauge',
            radius: '100%',
            min: 0,
            max: 100,
            startAngle: 220,		// 仪表盘起始角度,默认 225。圆心 正右手侧为0度，正上方为90度，正左手侧为180度。
            endAngle: -40,
            center: ['50%', '66%'],
            axisLine: {
                lineStyle: {
                    width: 8,
                    color: [
                        [0.1, new echarts.graphic.LinearGradient(
                            0, 1, 0, 0,
                            [{
                                offset: 0,
                                color: colors[0]
                            }, {
                                offset: 1,
                                color: colors[1]
                            }]
                        )],
                        [0.2, new echarts.graphic.LinearGradient(
                            0, 1, 0, 0,
                            [{
                                offset: 0,
                                color: colors[1]
                            }, {
                                offset: 1,
                                color: colors[2]
                            }]
                        )],
                        [0.4, new echarts.graphic.LinearGradient(
                            0, 1, 0, 0,
                            [{
                                offset: 0,
                                color: colors[2]
                            }, {
                                offset: 1,
                                color: colors[3]
                            }]
                        )],
                        [0.6, new echarts.graphic.LinearGradient(
                            0, 1, 0, 0,
                            [{
                                offset: 0,
                                color: colors[3]
                            }, {
                                offset: 1,
                                color: colors[3]
                            }]
                        )],

                        [0.75, new echarts.graphic.LinearGradient(
                            0, 1, 0, 0,
                            [{
                                offset: 0,
                                color: colors[2]
                            }, {
                                offset: 1,
                                color: colors[3]
                            }]
                        )],
                        [0.9, new echarts.graphic.LinearGradient(
                            0, 1, 0, 0,
                            [{
                                offset: 0,
                                color: colors[1]
                            }, {
                                offset: 1,
                                color: colors[2]
                            }]
                        )],
                        [1, new echarts.graphic.LinearGradient(
                            0, 1, 0, 0,
                            [{
                                offset: 0,
                                color: colors[0]
                            }, {
                                offset: 1,
                                color: colors[1]
                            }]
                        )]
                    ],
                }
            },
            splitLine: {
                show: false,
            },
            pointer: {				// 仪表盘指针。
                show: true,				// 是否显示指针,默认 true。
                length: "50%",			// 指针长度，可以是绝对数值，也可以是相对于半径的百分比,默认 80%。
                width: 3,
            },

            axisTick: {
                show: false,
                lineStyle: {
                    width: 1,
                    color: '#ffffff'
                }
            },
            axisLabel: {
                show: true,
                color: "#666666",
                distance: -6,
                fontSize: fontSize,
            },
            detail: {
                show: true,
                offsetCenter: ['0', '50%'],
                color: '#333',
                formatter: function (value) {
                    return value + "%";
                },
                fontSize: 14
            },
            // markPoint: {
            //     symbol: 'circle',
            //     symbolSize: 10,
            //     data: [
            //         //跟你的仪表盘的中心位置对应上，颜色可以和画板底色一样
            //         { x: 'center', y: 'center', itemStyle: { color: '#FF9933' } }
            //     ],
            //     itemStyle: {
            //         normal: {
            //             label: {
            //                 show: true,
            //                 color: '#FFF',//气泡中字体颜色
            //                 fontSize: 10
            //             }
            //         }
            //     },
            // },
            itemStyle: {
                normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{

                        offset: 0,
                        color: '#021132'
                    }, {
                        offset: 1,
                        color: '#3488DB'
                    }])
                }
            },
            data: [{
                value: 0
            }],
            // silent: false,
            // title: {
            //     offsetCenter: [0, '75%'],//设置在线率位置
            //     color: fontColor,
            // }
        },
    ]
}
export const zxOption = {
    tooltip: {
        show: true,
        trigger: 'axis',
        formatter: params => {
            let data = "<span>" + params[0].name + ":" + "</span></br> " + "<span>" + params[0].value.toFixed(2); +"</span>"
            return data;
        }
    },
    title: {
        text: 'CPU使用率',
        align: 'left',
        textStyle: {
            fontWeight: 'normal', //标题颜色
            color: '#666666',
            fontSize: 14,
        },
    },
    grid: {
        left: '12%',
        right: '5%',
        bottom: '25%',
        top: '20%',
    },
    xAxis: {
        type: 'category',
        boundaryGap: true,
        data: [],
        // data: ["00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00", "24:00"],
        axisTick: {
            show: false
        },
        axisLine: {
            lineStyle: {
                color: '#EDEDED',
                width: 1,//这里是为了突出显示加上的
            }
        },
        axisLabel: {
            show: true,
            interval:9,//代表显示所有x轴标签显示
            textStyle: {
                color: '#666',
                fontSize: 12      //更改坐标轴文字大小
            }
        }
    },
    yAxis: {
        type: 'value',
        // 坐标刻度线
        axisTick: {
            show: false
        },
        axisLine: {
            lineStyle: {
                color: '#EDEDED',
                width: 1,//这里是为了突出显示加上的
            }
        },
        splitLine: {
            show: true,
            lineStyle: {
                color: ['#F7F7F7'],
                width: 1,
                type: 'solid'
            }
        },
        axisLabel: {
            show: true,
            textStyle: {
                color: '#666',
                fontSize: 12      //更改坐标轴文字大小
            },
            formatter: '{value}%'
        }
    },
    series: [{
        // name: "one",
        data: [],
        type: 'line',
        symbol: 'circle',     //折点设定为实心点
        symbolSize: 5,   //设定实心点的大小
        itemStyle: {
            normal: {
                color: "#4383EC",
                lineStyle: {  //线的颜色
                    color: '#4383EC'
                },
            },
        },
    },
    {
        // name: 'two',
        data: [],
        type: 'line',
        symbol: 'circle',     //折点设定为实心点
        symbolSize: 5,   //设定实心点的大小
        itemStyle: {
            normal: {
                color: "#F2994A",
                lineStyle: {  //线的颜色
                    color: '#F2994A'
                },
            },
        },
    }
    ]
}
