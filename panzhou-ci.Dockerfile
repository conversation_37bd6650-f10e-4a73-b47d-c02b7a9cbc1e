FROM artifactory.dep.devops.cmit.cloud:20101/tools/base-images/node:lts-alpine AS builder
WORKDIR /code
ARG PROJECT_PATH
COPY ./${PROJECT_PATH} .
RUN npm config set registry "http://artifactory.dep.devops.cmit.cloud:20100/artifactory/api/npm/npm/" \
  && npm install @vue/cli -g \
  && npm install @vue/cli-service-global -g \
  && npm install \
  && npm run build

# FROM artifactory.dep.devops.cmit.cloud:20101/cmbaas/openresty:alpine
# FROM artifactory.dep.devops.cmit.cloud:20101/cmbaas_cloud/service/portal-ui:1010
FROM artifactory.dep.devops.cmit.cloud:20101/cmbaas_dev_cloud_1/portal-ui:1113
COPY --from=builder /code/dist /usr/share/nginx/html
COPY --from=builder /code/nginx.conf /usr/local/openresty/nginx/conf/nginx.conf
