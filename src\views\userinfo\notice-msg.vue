<template>
  <div style="padding-top:20px;">
    <Row>
      <Col span="10">
      <div class="login_header">
        <div @click="changeTabs(tas=1)" :class="{active:tas===1}" class="login_header_1">
          <span>待办</span>
        </div>
        <div @click="changeTabs(tas=2)" :class="{active:tas===2}" class="login_header_2">
          <span>待阅</span>
        </div>
        <div @click="changeTabs(tas=3)" :class="{active:tas===3}" class="login_header_3" v-show="this.roleId === '1' || this.roleId === '2'">
          <span>我发送的</span>
        </div>
      </div>
      </Col>
      <Col span="14">
      <div class="search-wrap">
        <Input class="search-input" suffix="ios-search" placeholder="请输入通知关键词" v-model="queryKey" @keyup.enter.native="searchHandle">
        <Icon type="ios-search" slot="suffix" @click="searchHandle" style="cursor: pointer" />
        </Input>
        <Button style="margin-left:10px;" type="primary" icon="md-add" @click="clickSend" v-show="this.roleId === '1' || this.roleId === '2'">发送通知</Button>
      </div>
      </Col>
    </Row>
    <div v-show="tas===1" class="tab-1">
      <edit-table-mul :columns="columns1" v-model="waitDealData" :key="transferKey"></edit-table-mul>
    </div>
    <div v-show="tas===2" class="tab-1">
      <edit-table-mul :columns="columns2" v-model="waitReadData" :key="transferKey"></edit-table-mul>
    </div>
    <div v-show="tas===3" class="tab-1">
      <edit-table-mul :columns="columns3" v-model="mySendData" :key="transferKey"></edit-table-mul>
    </div>
    <div class="page-wrap">
      <Page :total="tablePageParam.pagetotal" :current.sync="tablePageParam.pageIndex" @on-change="pageChange" :page-size="tablePageParam.pageSize" :page-size-opts="[5, 10, 20, 40, 60, 100]" show-total show-elevator show-sizer @on-page-size-change="pageSizeChange" style="text-align: right;" />
    </div>
    <Modal v-model="receiverVisible" title="通知接收人" footer-hide width="600">
      <Table :columns="receiverColumns" :data="receiverData" :height="getHeight" stripe></Table>
    </Modal>
    <send-notification ref="notification" :tas="tas" :roleId="roleId" @getNoticeManageData="getNoticeManageData" />
  </div>
</template>
<script>
import EditTableMul from '_c/edit-table-mul'
import { noticeManage, getReceiverData, read } from '@/api/data'
import SendNotification from './send-notification.vue'
import { localRead } from '@/lib/util'
import { mapState, mapActions } from 'vuex'
export default {
  components: {
    EditTableMul,
    SendNotification
  },
  data () {
    return {
      pendstatus: 'WAIT_DEAL',
      readstatus: 'WAIT_READ',
      transferKey: 0,
      queryKey: '', // 查询关键词
      receiverVisible: false,
      visible: false,
      noticeType: 'WAIT_DEAL',
      onlyShow: 0,
      bizTypew: 'CUSTOM',
      tablePageParam: { pagetotal: 0, pageSize: 5, pageIndex: 1 },
      tablePageParam1: { pageSize: 10, pageIndex: 1 },
      tas: 1,
      waitDealData: [],
      waitReadData: [],
      mySendData: [],
      roleId: localRead('roleId') || '0',
      columns1: [
        {
          key: 'noticeMessage',
          title: '通知内容',
          minWidth: 350,
          render: (h, params) => {
            return h('div', {
              domProps: {
                innerHTML: params.row.noticeMessage
              },
              on: {
                click: ($event) => {
                  if ($event.target.localName.toLowerCase() === 'span') {
                    this.routerDealTo(params.row)
                  }
                }
              }
            }
            )
          }
        },
        { key: 'createTime', title: '通知时间', minWidth: 70 },
        {
          key: 'processStatus',
          title: '状态',
          minWidth: 40,
          render: (h, params) => {
            const color = params.row.processStatus === 'UNDEAL' ? '#15AD31' : '#C7C7C7'
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, params.row.processStatusShow)
          }
        }
      ],
      columns2: [
        {
          key: 'noticeMessage',
          title: '通知内容',
          minWidth: 350,
          render: (h, params) => {
            return h('div', {
              domProps: {
                innerHTML: params.row.noticeMessage
              },
              on: {
                click: ($event) => {
                  if ($event.target.localName.toLowerCase() === 'span') {
                    if (this.routerReadTo(params.row)) {
                      if (params.row.processStatus === 'UNREAD') {
                        this.noticeIdList = []
                        this.noticeIdList.push(params.row.noticeId)
                        this.postRead(this.noticeIdList)
                        // this.getNoticeManageData(this.noticeType, this.onlyShow, this.queryKey, this.bizType)
                      }
                    }
                  }
                }
              }
            }
            )
          }
        },
        { key: 'createTime', title: '通知时间', minWidth: 70 },
        {
          key: 'processStatus',
          title: '状态',
          minWidth: 40,
          render: (h, params) => {
            const color = params.row.processStatus === 'UNREAD' ? '#15AD31' : '#C7C7C7'
            return h('Tag', {
              props: {
                type: 'dot',
                color: color
              },
              style: { marginLeft: '-8px' }
            }, params.row.processStatusShow)
          }
        }
      ],
      columns3: [
        { key: 'noticeMessage', title: '通知内容', minWidth: 400 },
        { key: 'createTime', title: '通知时间', minWidth: 80 },
        {
          key: 'action',
          title: '接收人',
          minWidth: 30,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'text', size: 'small' },
                style: { marginRight: '8px', color: '#3D73EF', border: '1px solid #3D73EF' },
                on: {
                  click: () => {
                    this.showDetails(params.row.noticeId)
                  }
                }
              }, '查看')
            ])
          }
        }
      ],
      receiverColumns: [
        {
          title: '租户',
          key: 'tenantName',
          width: '150'
        },
        {
          title: '用户',
          key: 'userList',
          render: (h, params) => {
            let user = ''
            for (let i in params.row.userList) {
              user = user + params.row.userList[i].userLoginId
              if (i < params.row.userList.length - 1) {
                user = user + '、'
              }
            }
            return h('div', {
              style: { wordBreak: 'break-word' }
            }, user)
          }
        }
      ],
      receiverData: []
    }
  },
  computed: {
    ...mapState({
      tabs: state => state.user.tas
    }),
    getHeight: function (value) {
      if (this.receiverData.length === 0) {
        return 90
      } else {
        return 380
      }
    }
  },
  methods: {
    ...mapActions([
      'updateTas'
    ]),
    msgInfo (type, content, closable = false) {
      this.$Message[type]({
        background: true,
        closable: closable,
        content: content
      })
    },
    searchHandle () {
      this.getNoticeManageData(this.noticeType, this.onlyShow, this.queryKey, this.bizType)
    },
    getNoticeManageData (noticeType, onlyShow, queryKey, bizType) {
      // console.log(onlyShow, queryKey, bizType)
      // let a = {
      //   pageParam: this.tablePageParam1,
      //   noticeType: this.pendstatus
      // }
      // serchPendingList(a).then(res => {
      //   this.waitDealData = res.data.records
      // })
      // let b = {
      //   pageParam: this.tablePageParam1,
      //   noticeType: this.readstatus
      // }
      // serchReadingList(b).then(res => {
      //   this.waitReadData = res.data.records
      // })
      noticeManage(noticeType, this.tablePageParam, onlyShow, queryKey, bizType).then(res => {
        if (res.code === '00000') {
          if (bizType === '' || bizType === null) {
            if (noticeType === 'WAIT_DEAL') {
              this.waitDealData = res.data.records
            } else if (noticeType === 'WAIT_READ') {
              this.waitReadData = res.data.records
            }
          } else {
            if (noticeType === 'WAIT_READ') {
              this.mySendData = res.data.records
            }
          }
          this.pages = res.data.pages
          this.tablePageParam = {
            pagetotal: res.data.total,
            pageSize: res.data.size,
            pageIndex: res.data.current
          }
          ++this.transferKey
        } else {
          this.approvalList = []
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    pageChange (index) {
      this.tablePageParam.pageIndex = index
      this.getNoticeManageData(this.noticeType, this.onlyShow, this.queryKey, this.bizType)
    },
    pageSizeChange (index) {
      this.tablePageParam.pageSize = index
      this.getNoticeManageData(this.noticeType, this.onlyShow, this.queryKey, this.bizType)
    },
    init () {
      this.tablePageParam = {
        pagetotal: 0,
        pageSize: 5,
        pageIndex: 1
      }
    },
    changeTabs (val) {
      this.init()
      if (val === 1) {
        this.getNoticeManageData(this.noticeType = 'WAIT_DEAL', this.onlyShow = 0, this.queryKey = '', this.bizType = null)
      } else if (val === 2) {
        this.getNoticeManageData(this.noticeType = 'WAIT_READ', this.onlyShow = 0, this.queryKey = '', this.bizType = null)
      } else if (val === 3) {
        this.getNoticeManageData(this.noticeType = 'WAIT_READ', this.onlyShow = 0, this.queryKey = '', this.bizType = 'CUSTOM')
      }
    },
    routerDealTo (item) {
      // 链账户审批
      if (item.bizType === 'ACCOUNT_AUDIT') {
        this.$router.push({
          name: 'chain-approvel',
          params: {
            tabs: item.processStatus === 'DEAL' ? 'name2' : 'name1'
          }
        })
        // 链账户资源管理
      } else if (item.bizType === 'ACCOUNT_UNASSIGNED') {
        // console.log('item:', item)
        this.$router.push({
          name: 'token_admin'
        })
        // 合约部署审批
      } else if (item.bizType === 'DEPLOY_AUDIT') {
        this.$router.push({
          name: 'contract-approvel',
          params: {
            tabs: item.processStatus === 'DEAL' ? 'name2' : 'name1'
          }
        })
        // 工单审批
      } else if (item.bizType === 'ORDER_AUDIT') {
        this.$router.push({
          name: 'workorder-approvel',
          params: {
            tabs: item.processStatus === 'DEAL' ? 'name2' : 'name1'
          }
        })
        // 密码过期
      } else if (item.bizType === 'PASSWORD_EXPIRE') {
        this.$emit('selectPage', 1)
        // this.$router.push({
        //   name: 'user_info',
        //   params: {
        //     cur: 1
        //   }
        // })
      } else if (item.bizType === 'SHARE_AUDIT') {
        // console.log('item:', item)
        this.$router.push({
          name: 'sharecontract-approvel',
          params: {
            tabs: 'name1'
          }
        })
      } else if (item.processStatus === 'UNDEAL' || item.processStatus === 'DEAL') {
        this.$router.push({
          name: 'shelves_approval',
          params: {
            tabs: 'name1'
          }
        })
        // 个人中心待办
      } else {
        this.msgInfo('warning', item.bizType + '没找到匹配路由', true)
        return false
      }
      return true
    },
    routerReadTo (item) {

      var jsonObj = item.linkParams ? JSON.parse(item.linkParams) : {}
      // 链管理详情页

      //  链账户资源管理
      if (item.bizType === 'ACCOUNT_RESULT') {
        this.$router.push({
          name: 'chain_details',
          query: {
            chainId: jsonObj.chainId,
            chainName: jsonObj.chainName,
            chainAccountName: jsonObj.chainAccountName
          }
        })
        //  链账户资源管理
      } else if (item.bizType === 'ACCOUNT_ASSIGNED') {
        // console.log('item:', item)
        this.$router.push({
          name: 'chain_table'
        })
        // 合约部署审批
      } else if (item.bizType === 'DEPLOY_RESULT') {
        this.$router.push({
          name: 'contract_table',
          params: {
            contractId: jsonObj.contractId
          }
        })
        // 工单审批
      } else if (item.bizType === 'ORDER_RESULT') {
        this.$router.push({
          name: 'workorder_detail',
          params: {
            orderId: jsonObj.orderId
          }
        })
        // 所属租户变更
      } else if (item.bizType === 'TENANT_USER_UPDATE') {
        this.$emit('selectPage', 2)
        // this.$router.push({
        //   name: 'user_info',
        //   params: {
        //     cur: 2
        //   }
        // })
        // 租户成员变动
      } else if (item.bizType === 'OWNER_TENANT_UPDATE') {
        this.$router.push({
          name: 'tenant_details',
          params: {
            tenantId: jsonObj.tenantId
          }
        })
      } else if (item.bizType === 'PASSWORD_RESET' || item.bizType === 'PASSWORD_MODIFY' || item.bizType === 'SHARE_OFF' || item.bizType === 'SHARE_UNBIND' || item.bizType === 'SHARE_DELETE_TENANT') {
        return false
      } else if (item.bizType === 'SHARE_RECEIVE') {
        this.$router.push({
          name: 'contract_area',
          params: {
            isContractFlag: false
          }
        })
      } else if (item.bizType === 'SHARE_UPDATE') {
        this.$router.push({
          name: 'sharecontract_details',
          params: {
            shareRecordId: jsonObj.shareRecordId,
            receivedTenantId: item.receivedTenantId
          }
        })
      } else if (item.bizType === 'SHARE_RESULT') {
        this.$router.push({
          name: 'contract_table',
          params: {
            contractId: jsonObj.contractId
          }
        })
      } else if (item.processStatus === 'UNREAD' || item.processStatus === 'READ') {
        this.$router.push({
          name: 'contract_market',
          params: {
            tabs: 'name2'
          }
        })
      } else if (item.bizType === 'CONTRACT_MARKET_ON' || item.bizType === 'CONTRACT_MARKET_OFF' || item.bizType === 'MARKET_OFF_SUCCESS' || item.bizType === 'MARKET_OFF_FAILED' || item.bizType === 'MARKET_REON_SUCCESS' || item.bizType === 'MARKET_REON_FAILED' || item.bizType === 'CONTRACT_APP_ON' || item.bizType === 'CONTRACT_APP_OFF') {
        this.$router.push({
          name: 'contract_market'
        })
      } else {
        this.msgInfo('warning', item.bizType + '没找到匹配路由', true)
        return false
      }
      return true
    },
    postRead (list) {
      read(list).then(res => {
        if (res.code === '00000') {
        } else {
          this.msgInfo('error', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    showDetails (noticeId) {
      this.receiverVisible = true
      this.getReceiverDataList(noticeId)
    },
    getReceiverDataList (noticeId) {
      getReceiverData(noticeId).then(res => {
        if (res.code === '00000') {
          this.receiverData = res.data ? res.data : []
        } else {
          this.msgInfo('warning', res.message, true)
        }
      }).catch(error => {
        this.msgInfo('error', error.message, true)
      })
    },
    clickSend () {
      this.$refs.notification.visible = true
      this.$refs.notification.init()
    }
  },
  mounted () {
    this.tas = this.tabs || 1
    this.changeTabs(this.tas)
  },
  destroyed () {
    this.updateTas(this.tas)
  }

}
</script>
<style lang="less" scoped>
.login_header {
  font-size: 16px;
  float: left;
}
.login_header_1 {
  margin-right: 20px;
  cursor: pointer;
  width: 40px;
  display: inline-block;
  margin-left: 30px;
}
.login_header_2 {
  cursor: pointer;
  width: 40px;
  display: inline-block;
  margin-right: 20px;
}
.login_header_3 {
  cursor: pointer;
  width: 70px;
  display: inline-block;
}

.tab-1 {
  width: 100%;
  height: 100%;
  background: #ffffff;
  opacity: 1;
  padding: 20px;
}
/deep/.ivu-table-tip {
  overflow: hidden;
}
.active {
  color: #3d73ef;
  padding-bottom: 6px;
  border-bottom: 2px solid #3d73ef;
  cursor: pointer;
}

.search-wrap {
  float: right;
  padding-right: 20px;
  .search-input {
    width: 300px !important;
  }
}
/deep/.ivu-tag-dot {
  border: 1px none #e8eaec !important;
  background: transparent !important;
}
/deep/.ivu-tag {
  font-size: inherit !important;
}
.page-wrap {
  margin: 0 10px 10px 0;
}
/deep/.ivu-btn-text:hover {
  background-color: rgba(61, 115, 239, 0.8);
  color: #fff !important;
}
/deep/.ivu-btn-text:active {
  background-color: #3d73ef;
}
</style>
